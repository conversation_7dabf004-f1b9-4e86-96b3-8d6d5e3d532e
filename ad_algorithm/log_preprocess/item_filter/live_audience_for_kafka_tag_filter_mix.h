#pragma once

#include <string>
#include <unordered_set>
#include <vector>
#include "teams/ad/ad_proto/kuaishou/ad/ad_log.pb.h"
#include "teams/ad/ad_algorithm/log_preprocess/item_filter_base.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_algorithm/log_preprocess/ad_model_kconf_util.h"

// 1. is_item_live
// item_live_filter_fix
// campaign_type_exclude_filter:16
// un_universe_filter
// un_detail_filter
// item_label_info_filter:feed_impression

// 2. ins_filter
// interactive_form_filter:1+2
// slide_inspire_filter
// inspire_live_nearby_inner_subpage_sample_filter:85

// 3. get_neg_sample
// direct_live_ins_else_neg_filter:xx
namespace ks {
namespace ad_algorithm {

class LiveAudienceForKafkaTagFilterMix : public ItemFilterBase {
 public:
  explicit LiveAudienceForKafkaTagFilterMix(const std::string &);

  virtual bool operator()(const kuaishou::ad::algorithm::AdJointLabeledLog &log,
                          const kuaishou::ad::algorithm::Item &item) const;

  bool is_item_live(const kuaishou::ad::algorithm::AdJointLabeledLog& log,
                          const kuaishou::ad::algorithm::Item &item) const;

  bool get_neg_sample(const kuaishou::ad::algorithm::AdJointLabeledLog& log,
                          const kuaishou::ad::algorithm::Item& item) const;

  bool ins_filter(const kuaishou::ad::algorithm::AdJointLabeledLog& log) const;

  bool front_end_data_filter(const kuaishou::ad::algorithm::AdJointLabeledLog& log) const;

 private:
  std::vector<std::string> sample_;
  double neg_sample_rate = 1.0;
};

LiveAudienceForKafkaTagFilterMix::LiveAudienceForKafkaTagFilterMix(const std::string &conf) {
  if (conf.size() > 0) {
    neg_sample_rate = std::stod(conf.c_str());
  }
}

bool LiveAudienceForKafkaTagFilterMix::operator()(const kuaishou::ad::algorithm::AdJointLabeledLog &log,
                                   const kuaishou::ad::algorithm::Item &item) const {
  return is_item_live(log, item) && get_neg_sample(log, item) && ins_filter(log) &&
  front_end_data_filter(log);
}

bool LiveAudienceForKafkaTagFilterMix::front_end_data_filter(const
  kuaishou::ad::algorithm::AdJointLabeledLog& log) const {
  // 实现功能：label_match_type_filter:2
  if (log.label_match_type() == 2) {
    return true;
  }
  return false;
}

bool LiveAudienceForKafkaTagFilterMix::get_neg_sample(const kuaishou::ad::algorithm::AdJointLabeledLog& log,
                          const kuaishou::ad::algorithm::Item& item) const {
  // 实现功能：direct_live_ins_else_neg_filter:xx

  // 判断 是否是激励 激励直接返回 不参与负采样
  const auto inspire_live_subpage_conf = AdModelKconfUtil::inspireLiveSubpageConf();
  auto sub_page_id = log.context().sub_page_id();
  auto inspire_live_subpage_iter = inspire_live_subpage_conf->find(sub_page_id);
  if (inspire_live_subpage_iter != inspire_live_subpage_conf->end() ||
  (sub_page_id == 11002001 || sub_page_id == 10003001 || sub_page_id == 100011085 ||
  sub_page_id == 100011292 || sub_page_id == 100011503 || sub_page_id == 100012061)) {
    return true;
  }

  int interactive_form = -1;
  for (auto & attr : log.context().info_common_attr()) {
    if (attr.name_value() == ::kuaishou::ad::ContextInfoCommonAttr_Name_INTERACTIVE_FORM) {
      interactive_form = attr.int_value();
      break;
    }
  }

  double random_number = ad_base::AdRandom::GetDouble();
  const auto & label_infos = item.label_info().label_infos();
  if (interactive_form == 1) {
    if (label_infos.find(68) != label_infos.end()) {
      return true;
    }
    if (random_number > neg_sample_rate) {
      return false;
    }
  }
  if (interactive_form == 2 || interactive_form == 3) {
    if (label_infos.find(20000001) != label_infos.end() ||
        label_infos.find(20000020) != label_infos.end() || label_infos.find(20000002) != label_infos.end()) {
      return true;
    }
    if (random_number > neg_sample_rate) {
      return false;
    }
  }
  return true;
}

bool LiveAudienceForKafkaTagFilterMix::ins_filter(const
  kuaishou::ad::algorithm::AdJointLabeledLog& log) const {
  // 实现功能：inspire_live_nearby_inner_subpage_sample_filter:85 &&
  //          slide_inspire_filter && interactive_form_filter:1+2

  int interactive_form = -1;
  for (auto & attr : log.context().info_common_attr()) {
    if (attr.name_value() == ::kuaishou::ad::ContextInfoCommonAttr_Name_INTERACTIVE_FORM) {
      interactive_form = attr.int_value();
      break;
    }
  }
  if ((interactive_form != 1) && (interactive_form != 2)) {
    return false;
  }

  const auto inspire_live_subpage_conf = AdModelKconfUtil::inspireLiveSubpageConf();

  bool slide_form = false;

  if (interactive_form == 2) {
    slide_form = true;
  }

  if (log.has_context()) {
    auto sub_page_id = log.context().sub_page_id();
    auto inspire_live_subpage_iter = inspire_live_subpage_conf->find(sub_page_id);
    if (inspire_live_subpage_iter != inspire_live_subpage_conf->end() ||
    (sub_page_id == 11002001 || sub_page_id == 10003001 || sub_page_id == 100011085 ||
    sub_page_id == 100011292 || sub_page_id == 100011503 || sub_page_id == 100012061)
    ) {
      if ( ad_base::AdRandom::GetInt(0, 99) < 85 ) {
        return false;
      }
      if (slide_form) {
        return false;
      }
    }
  }

  return true;
}

bool LiveAudienceForKafkaTagFilterMix::is_item_live(const kuaishou::ad::algorithm::AdJointLabeledLog& log,
                          const kuaishou::ad::algorithm::Item& item) const {
    // 实现功能: feed_impression && un_detail_filter && un_universe_filter
    //          && campaign_type_exclude_filter:16 && item_live_filter_fix

    const auto & label_infos = item.label_info().label_infos();

    if (label_infos.find(20000004) == label_infos.end()) {
      return false;
    }

    // 快享流量过滤
    if (log.context().page_id() == 10001) {
      return false;
    }

    // 联盟流量过滤
    if (!log.has_context()) {
      return false;
    }
    int32 log_medium = -1;
    for (int i = 0; i < log.context().info_common_attr_size(); ++i) {
      const auto& comm_info_attr = log.context().info_common_attr(i);
      if (comm_info_attr.name_value() == 4) {
        log_medium = comm_info_attr.int_value();
        break;
      }
    }
    if ((log_medium == 2) || (log_medium == 4)) {
      return false;
    }

    // campaign type 16 过滤
    if (item.has_ad_dsp_info() && item.ad_dsp_info().has_campaign() &&
        item.ad_dsp_info().campaign().has_base()) {
      uint64_t campaign_type = item.ad_dsp_info().campaign().base().type();
      if (campaign_type == 16) {
        return false;
      }
    }

    // 直投流量选择
    if (item.type() == kuaishou::ad::algorithm::FANS_TOP_LIVE) {
      return true;
    }

    if (item.type() == kuaishou::ad::algorithm::ItemType::AD_DSP &&
                    item.has_ad_dsp_info() && item.ad_dsp_info().has_campaign() &&
      item.ad_dsp_info().campaign().has_base() && item.ad_dsp_info().has_creative()) {
      auto& campaign = item.ad_dsp_info().campaign().base();
      auto& create = item.ad_dsp_info().creative().base();
      if ((create.live_creative_type() == 1) && (campaign.type() == 21 ||
            campaign.type() == 22 || campaign.type() == 23)) {
        return true;
      }
    }

    if (item.has_ad_dsp_info() && item.ad_dsp_info().has_campaign()
      && item.ad_dsp_info().has_creative()) {
      auto& campaign = item.ad_dsp_info().campaign().base();
      auto& creative = item.ad_dsp_info().creative().base();
      if ((campaign.type() == 14 && creative.live_creative_type() == 1) ||
          (campaign.type() == 16 && creative.live_creative_type() == 3)) {
        return true;
      }
    }
    return false;
}



REGISTER_PLUGIN(ItemFilterBase, LiveAudienceForKafkaTagFilterMix, "live_audience_for_kafka_tag_filter_mix");

}  // namespace ad_algorithm
}  // namespace ks

