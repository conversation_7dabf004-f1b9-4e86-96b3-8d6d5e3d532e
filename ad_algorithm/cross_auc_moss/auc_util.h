#pragma once

#include <math.h>
#include <string>
#include <sstream>
#include <memory>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <iostream>
#include <fstream>
#include <cstdio>
#include <limits>
#include <thread>
#include "ks/util/json.h"
#include "ks/serving_util/dynamic_config.h"
#include "base/common/gflags.h"
#include "base/common/base.h"
#include "base/common/logging.h"
#include "base/strings/string_split.h"
#include "base/time/timestamp.h"
#include "base/encoding/base64.h"
#include "base/encoding/line_escape.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/ad_base/src/kafka/kafka_client.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "infra/falcon_counter/src/falcon/counter.h"

namespace ks {
namespace ad_algorithm {

const auto weight_file = "data.txt";

class PredictData {
 public:
  float rate;
  int label;
  int index;
};

struct AucData {
 public:
  size_t p_cnt = 0;
  size_t n_cnt = 0;
};

class RealData {
 public:
  explicit RealData(int max_auc_bucket_size);

  void AddToAucData(float rate, int label);
  void RemoveFromAucData(float rate, int label);
  void Put(float rate, int label);
  void SetConfig(std::string world_name,
                 std::string exp_name,
                 std::string abtest_sub_group,
                 std::string abtest_traffic,
                 std::string cmd_title);

  float GetReal();
  float GetPred();
  float GetAuc();
  float GetLoss();
  float GetRmse();
  float GetMae();
  float GetSmape();
  float GetNormalizedGini();
  float GetRegressionAuc(float positive_weight);
  float GetRegressionAuc01();
  float GetRegressionAuc11();

  // from last auc, reset cnt
  int64 cnt_reset = 0;
  int64 max_auc_bucket_size = 0;
  int compute_step = 0;
  int sample_per_minute_limit = 0;

  int sample_count = 0;
  base::Time last_count_time;
  bool is_small_data = false;

  std::unique_ptr<AucData[]> auc_data;

  inline uint64_t GetMinCount() {
    return min_count;
  }

  inline uint64_t GetMaxDataSize() {
    return max_data_size;
  }

  inline uint64_t GetCount() {
    return cnt;
  }

  inline uint64_t GetUpdateTime() {
    return update_time;
  }

 private:
  std::unique_ptr<PredictData[]> data;
  std::unique_ptr<PredictData[]> aux_data;
  uint64_t min_count;
  uint64_t max_data_size;
  size_t pos;
  uint64_t cnt;
  uint64_t p_cnt;
  double pred_sum;
  uint64 update_time;
  double loss_one_day;
  double eps;

  std::string world_name;
  std::string exp_name;
  std::string abtest_sub_group;
  std::string abtest_traffic;
  std::string cmd_title;
};

class AucManager {
 public:
  void AddSample(const std::string & cmd,
                 double pred,
                 int label,
                 const std::string& world_name,
                 const std::string& exp_name,
                 const std::string& abtest_sub_group,
                 const std::string& abtest_traffic,
                 const std::string& cmd_title,
                 int max_auc_bucket_size,
                 bool is_regression_model,
                 float positive_weight,
                 ks::ad_base::AdKafkaProducer* predict_producer);

 private:
  std::mutex data_map_mutex;
  std::unordered_map<std::string, std::shared_ptr<RealData>> data_map;
};

}  // namespace ad_algorithm
}  // namespace ks
