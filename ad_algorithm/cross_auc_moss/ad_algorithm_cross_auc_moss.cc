#include <sys/stat.h>
#include <unistd.h>
#include <iostream>
#include <fstream>
#include <string>
#include <random>
#include <cmath>
#include <set>
#include <unordered_map>
#include <unordered_set>
#include <mutex>
#include <vector>
#include <memory>
#include "teams/ad/ad_base/src/container/concurrent_queue.h"
#include "teams/ad/ad_base/src/common/common.h"
#include "zk_client/zk_client.h"
#include "zmq.hpp"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "base/common/base.h"
#include "base/common/logging.h"
#include "base/time/timestamp.h"
#include "base/strings/string_split.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_number_conversions.h"
#include "base/encoding/base64.h"
#include "base/encoding/line_escape.h"
#include "ks/serving_util/dynamic_config.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/dw/ad_log_for_algo.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.pb.h"
#include "teams/ad/picasso/sdk/picasso_client/picasso_client.h"
#include "third_party/tbb/include/tbb/concurrent_priority_queue.h"
#include "teams/ad/ad_proto/kuaishou/ad/picasso/picasso_update.pb.h"
#include "teams/ad/ad_base/src/math/random/random.h"

#include "teams/ad/ad_algorithm/log_preprocess/log_preprocess.h"
#include "teams/ad/ad_algorithm/log_preprocess/label_extractor_def.h"
#include "teams/ad/ad_algorithm/log_preprocess/log_filter_def.h"
#include "teams/ad/ad_algorithm/log_preprocess/item_filter_def.h"
#include "teams/ad/ad_algorithm/log_preprocess/debug_info_def.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.grpc.pb.h"

#include "teams/ad/ad_algorithm/log_preprocess/ad_model_kconf_util.h"
#include "ks/zookeeper/config_key.h"
#include "ks/zookeeper/zk_node_config.h"
#include "serving_base/zookeeper/zk_event_handler.h"
#include "teams/ad/ad_base/src/zk/zk_config.h"

#include "ks/base/abtest/session_context_factory.h"
#include "ks/base/abtest/experiment_info.h"
#include "ks/base/abtest/user_mapping_info.h"
#include "perfutil/perfutil.h"
#include "ks/base/abtest/metrics/abtest_metric.h"

#include "grpc++/grpc++.h"

#include "teams/ad/ad_algorithm/model_online/model_util.h"

#include "teams/ad/ad_algorithm/cross_auc_moss/moss_client.h"
#include "teams/ad/ad_algorithm/cross_auc_moss/auc_util.h"

using namespace ks::ad_algorithm;        // NOLINT
using namespace ks::infra;               // NOLINT
using namespace kuaishou::ad::algorithm;       // NOLINT

using ks::ad_base::OptionsFromMilli;
using ks::ad_base::kconf::KconfNode;
using ks::ad_base::kconf::KconfJson;
using ks::ad_base::AdRandom;

static const ks::AbtestBiz kBiz = ks::AbtestBiz::AD_DSP;
bool enable_adx_biz_type = false;
bool enable_fanstop_biz_type = false;

DEFINE_int32(num_threads, 5, "num_threads");
DEFINE_string(topic, "" , "topic");
DEFINE_string(group_id, "" , "group_id");
DEFINE_int32(msg_obsolete_bound_second, 600, "msg_obsolete_bound_second");

const int SLEEP_TIME = 100;
const auto base1 = "base1";
const auto unbias = "unbias";
const auto unbias_topic = "ad_joint_labeled_log_abtest_base";
const auto unbias_topic_1 = "ad_backend_data_flow_abtest_base";
const int DEFAULT_AUC_BUCKET = 100000;

/*
 * kconf url: https://kconf.corp.kuaishou.com/#/ad/algorithm/AdCrossAucConfDetail
 * cross auc kconf示例:
 *                                                                      
 * {
 *   "conversion_shuffle_data": [
 *     {
 *         "world_name": "world_ad_algorithm_8",
 *         "exp_name": "dsp_conv_zd_bn_large_v3",
 *         "cmd_title": "app_conversion_rate_cmd",
 *         "item_filter": "type_filter:AD_DSP,conversion_feed_filter",
 *         "label_extracot": "event_label_extractor:EVENT_CONVERSION@1",
 *         "max_auc_bucket_size": 1000000,
 *         "groups": {
 *         }
 *     }
 *   ]
 * }
 *
 */

class GroupInfo {
 public:
  std::string name;
  std::string cmd;
  std::string cmd_key;
};

class ExpInfo {
 public:
  std::string cmd_title;
  std::string item_filter;
  std::string label_extractor;
  std::unique_ptr<LogPreprocess> processor;
  std::map<std::string, GroupInfo> groups;
  int max_auc_bucket_size;
  std::string base_name = base1;
  bool is_regression_model;
  float positive_weight;
};

typedef std::map<std::string, std::vector<ExpInfo>> WorldInfo;
typedef std::map<std::string, WorldInfo> AllWorldExp;

std::unordered_set<std::string> get_world_list(const AllWorldExp& all_world_expr) {
  std::unordered_set<std::string> res;

  for (auto it = all_world_expr.begin(); it != all_world_expr.end(); it++) {
    res.insert(it->first);
  }

  return res;
}

void print_world_exp(const AllWorldExp& all_world_exp) {
  for (auto iter = all_world_exp.begin(); iter != all_world_exp.end(); iter++) {
    const WorldInfo& world_info = iter->second;
    LOG(INFO) << "world: " << iter->first;
    for (auto iter_exp = world_info.begin(); iter_exp != world_info.end(); iter_exp++) {
      for (const ExpInfo & exp_info : iter_exp->second) {
        LOG(INFO) << "exp: " << iter_exp->first;
        for (auto iter_group = exp_info.groups.begin(); iter_group != exp_info.groups.end(); iter_group++) {
          LOG(INFO) << "world: " << iter->first
                    << ", exp: " << iter_exp->first
                    << ", cmd_title: " << exp_info.cmd_title
                    << ", item_filter: " << exp_info.item_filter
                    << ", label_extractor: " << exp_info.label_extractor
                    << ", name: " << iter_group->second.name
                    << ", cmd: " << iter_group->second.cmd
                    << ", cmd_key: " << iter_group->second.cmd_key
                    << ", base_name: " << exp_info.base_name
                    << ", is_regression_model: " << exp_info.is_regression_model
                    << ", positive_weight: " << exp_info.positive_weight;
        }
      }
    }
  }
}


AllWorldExp get_all_world_exp(base::Json* kconf_config) {
  AllWorldExp all_world_exp;

  enable_adx_biz_type = false;
  enable_fanstop_biz_type = false;
  for (int i = 0; i < kconf_config->size(); i++) {
    auto one_exp = kconf_config->Get(i);
    std::string world_name = one_exp->GetString("world_name");
    std::string exp_name = one_exp->GetString("exp_name");
    std::string cmd_title = one_exp->GetString("cmd_title");
    std::string item_filter = one_exp->GetString("item_filter", "");
    std::string label_extractor = one_exp->GetString("label_extractor", "");
    std::string base_name = one_exp->GetString("base_name", base1);
    bool is_regression_model = one_exp->GetBoolean("is_regression_model", false);
    float positive_weight = one_exp->GetFloat("positive_weight", 1.0);
    int max_auc_bucket_size = DEFAULT_AUC_BUCKET;
    LOG(INFO) << "one_exp, world_name: " << one_exp->GetString("world_name")
              << ", exp_name: " << one_exp->GetString("exp_name")
              << ", max_auc_bucket_size: " << max_auc_bucket_size
              << ", is_regression_model: " << is_regression_model
              << ", positive_weight: " << positive_weight;

    if (all_world_exp.find(world_name) == all_world_exp.end()) {
      all_world_exp[world_name] = WorldInfo();
    }

    ExpInfo exp_info;
    exp_info.cmd_title = cmd_title;
    exp_info.item_filter = item_filter;
    exp_info.label_extractor = label_extractor;
    exp_info.processor =
      std::move(std::make_unique<ks::ad_algorithm::LogPreprocess>("", "", item_filter, label_extractor));
    exp_info.max_auc_bucket_size = max_auc_bucket_size;
    exp_info.base_name = base_name;
    exp_info.is_regression_model = is_regression_model;
    exp_info.positive_weight = positive_weight;

    base::Json* groups = one_exp->Get("groups");

    for (auto iter = groups->object_begin(); iter != groups->object_end(); iter++) {
      std::string v = groups->GetString(iter->first);
      GroupInfo group_info;
      group_info.name = iter->first;
      group_info.cmd = v;
      group_info.cmd_key = group_info.cmd.substr(0, group_info.cmd.find(":"));

      exp_info.groups[group_info.name] = std::move(group_info);
    }

    if (exp_info.groups.size() > 0) {
      all_world_exp[world_name][exp_name].push_back(std::move(exp_info));
    }
    if (world_name == "world_adx_algorithm") {
      enable_adx_biz_type = true;
    } else if (world_name.find("fans_top") != std::string::npos ||
               world_name.find("fanstop") != std::string::npos) {
      enable_fanstop_biz_type = true;
    }
  }

  print_world_exp(all_world_exp);

  return all_world_exp;
}

void store_predict_detail(ks::ad_algorithm::AdLogWrapper* p_adlog,
                          int pos,
                          const std::string & cmd,
                          double pred,
                          int label,
                          const std::string& world_name,
                          const std::string& exp_name,
                          const std::string& abtest_sub_group,
                          const std::string& abtest_traffic,
                          const std::string& cmd_title,
                          ks::ad_base::AdKafkaProducer* producer) {
  auto& item = p_adlog->Get().item(pos);
  const auto& llsid = p_adlog->llsid();
  const auto& user_id = p_adlog->user_info().id();
  const auto& item_id = item.id();
  uint64_t industry_id = 0;
  std::string product_name;
  int64_t account_id = 0;
  if (item.has_ad_dsp_info() && item.ad_dsp_info().has_creative()) {
    industry_id = item.ad_dsp_info().creative().base().new_industry_id();
  }
  if (item.ad_dsp_info().has_advertiser_base()) {
    product_name = item.ad_dsp_info().advertiser_base().product_name();
  }
  if (item.ad_dsp_info().has_unit() && item.ad_dsp_info().unit().has_base()) {
    account_id = item.ad_dsp_info().unit().base().account_id();
  }

  std::ostringstream oss;
  oss << world_name << "\1"
      << exp_name << "\1"
      << abtest_sub_group << "\1"
      << abtest_traffic << "\1"
      << cmd << "\1"
      << pred << "\1"
      << label << "\1"
      << industry_id << "\1"
      << llsid << "\1"
      << user_id << "\1"
      << item_id << "\1"
      << cmd_title << "\1"
      << product_name << "\1"
      << account_id << "\1"
      << item.creative_tag();

  // LOG(INFO) << oss.str();
  producer->Produce(oss.str());
}

void handle_predict(MossClient* moss_client,
                    AucManager* auc_manager,
                    ks::ad_algorithm::AdLogWrapper* p_adlog,
                    tensorflow::Tensor* p_labels_tensor,
                    const std::string& reco_user_info,
                    const std::string& cmd,
                    const std::string& world_name,
                    const std::string& exp_name,
                    const std::string& group_name,
                    const std::string& base1,
                    const std::string& cmd_title,
                    int max_auc_bucket_size,
                    bool is_regression_model,
                    float positive_weight,
                    ks::ad_base::AdKafkaProducer* producer,
                    ks::ad_base::AdKafkaProducer* predict_producer,
                    int64_t config_version) {
  UniversePredictResponse response =
    moss_client->get_predict_result(p_adlog, reco_user_info, cmd, config_version);
  std::string tmp_str = "fail";
  if (response.predict_result_size() == p_adlog->Get().item_size()) {
    tmp_str = "success";
  }
  ks::infra::PerfUtil::CountLogStash(1,
                                    "klearn.cross_auc",
                                    "count_kess_" + tmp_str,
                                    world_name,
                                    exp_name,
                                    group_name,
                                    base1);

  for (int k = 0; k < response.predict_result_size(); ++k) {
    int64 label = get_one_label_value(*p_labels_tensor, k);
    auc_manager->AddSample(cmd,
                          response.predict_result(k).value(0),
                          label,
                          world_name,
                          exp_name,
                          group_name,
                          base1,
                          cmd_title,
                          max_auc_bucket_size,
                          is_regression_model,
                          positive_weight,
                          predict_producer);

    store_predict_detail(p_adlog,
                        k,
                        cmd,
                        response.predict_result(k).value(0),
                        label,
                        world_name,
                        exp_name,
                        group_name,
                        base1,
                        cmd_title,
                        producer);
  }
}

int main(int argc, char* argv[]) {
  base::InitApp(&argc, &argv, "ad algorithm cross auc");

  std::mutex mutex;
  const std::string topic = FLAGS_topic;
  const std::string group_id = FLAGS_group_id;
  int msg_obsolete_bound_second = FLAGS_msg_obsolete_bound_second;

  const std::string consumer_user_param =  std::string("auto.offset.reset=latest;") +
    std::string("fetch.error.backoff.ms=0;queued.max.messages.kbytes=5120;auto.offset.reset=latest");
  ks::ad_algorithm::AdKafkaConsumerAlgo *data_consumer = new ks::ad_algorithm::AdKafkaConsumerAlgo();
  int ret = data_consumer->InitConsumer(topic, group_id, consumer_user_param);
  if (ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
    LOG(ERROR)<< "init data consumer failed. ";
    return -1;
  }

  const std::string producer_user_params = "compression.codec=lz4";
  auto * producers = new ks::ad_base::AdKafkaProducer[FLAGS_num_threads];
  auto * predict_producers = new ks::ad_base::AdKafkaProducer[FLAGS_num_threads];
  for (int i = 0; i < FLAGS_num_threads; i++) {
    producers[i].Init("ad_cross_auc_predict_moss_detail", producer_user_params);
    predict_producers[i].Init("ad_cross_auc_predict", producer_user_params);
  }

  auto process_func = [&](size_t thread_id){
    base::Time last_update_time = base::Time::Now();

    bool is_config_changed = false;
    double sample_rate = 1.0;
    size_t last_version = 0;
    std::hash<std::string> hash_fn;
    MossClient moss_client;
    AucManager auc_manager;

    KconfNode<KconfJson> cross_auc_config_node_("ad.algorithm.AdCrossAucConfDetail");
    base::Json* kconf_config{nullptr};
    auto kconf_data = cross_auc_config_node_.Get()->data;
    kconf_config = kconf_data->Get(topic);
    if (kconf_config == nullptr) {
      LOG(FATAL) << "cannot find topic: " << topic << " in AdCrossAucConfDetail";
    }
    sample_rate = kconf_config->GetFloat("sample_rate", 0.5);

    AllWorldExp all_world_exp = get_all_world_exp(kconf_config->Get("exp_arr"));
    std::unordered_set<std::string> world_list = get_world_list(all_world_exp);

    while (true) {
      base::Time now = base::Time::Now();
      base::TimeDelta delta = now - last_update_time;
      if (delta.InSeconds() > 600) {
        is_config_changed = false;

        auto kconf_data = cross_auc_config_node_.Get()->data;
        kconf_config = kconf_data->Get(topic);
        if (kconf_config != nullptr) {
          size_t new_version = hash_fn(base::JsonToString(kconf_config->get()));
          if (new_version != last_version) {
            sample_rate = kconf_config->GetFloat("sample_rate", 0.5);
            all_world_exp = get_all_world_exp(kconf_config->Get("exp_arr"));
            LOG(INFO) << "version changed, last_version: " << last_version
                      << ", new_version: " << new_version
                      << ", sample_rate: " << sample_rate;
            last_version = new_version;
            is_config_changed = true;
          }
        }

        last_update_time = now;
      }


      ks::ad_algorithm::AdLogWrapper adlog;

      // std::string line;
      // string bline;
      // mutex.lock();
      // if (!std::getline(std::cin, line)) {
        // LOG(INFO) << "read line done";
        // mutex.unlock();
        // break;
      // }
      // mutex.unlock();
      // if (!base::Base64Decode(line, &bline) || !adlog.parse_from(bline)) {
        // LOG(ERROR) << "parser base64 or adlog failed";
        // continue;
      // }

      // falcon::Inc("cross_auc_distribute_consum_before_sample", 1);
      // if (ad_base::AdRandom::GetDouble() > FLAGS_sample_rate)
      //   continue;
      // falcon::Inc("cross_auc_distribute_consumer_0", 1);

      std::string message;
      int64_t message_ts;
      int retc = data_consumer->ConsumeWithTs(&message, &message_ts);
      if (retc != ks::ad_base::AdKafkaStatus::SUCCESS) {
        std::this_thread::sleep_for(std::chrono::milliseconds(SLEEP_TIME));
        continue;
      }

      if (is_message_obsolete(message_ts, msg_obsolete_bound_second)) {
        std::string time_result;
        base::Time ts = base::Time::FromInternalValue(message_ts);
        ts.ToStringInSeconds(&time_result);
        LOG_EVERY_N(INFO, 10000) << "message obselete, skip, message time, ts: "
                                 << message_ts
                                 << ", time: " << time_result;
        continue;
      }

      if (sample_rate < 1.0) {
        double r = AdRandom::GetDouble();
        if (r > sample_rate) {
          continue;
        }
      }

      if (!adlog.parse_from(message.data(), message.size())) {
       LOG(ERROR) << "parse adlog error!!!!";
       continue;
      }

      int item_size = adlog.item_size();
      if (item_size <= 0) {
        LOG(ERROR) << "adlog has no item !!!!";
        continue;
      }

      uint64_t user_id = 0;
      if (adlog.Get().has_user_info()) {
        user_id = adlog.Get().user_info().id();
      }
      std::string device_id;
      if (adlog.Get().has_user_info() && adlog.Get().user_info().has_device_info()) {
        device_id = adlog.Get().user_info().device_info().id();
      }

      // ks::ExperimentInfo adx_abtest_info;
      // ks::ExperimentInfo fanstop_abtest_info;
      // ks::SessionContextFactory::CreateUserMappingInfo(ks::AbtestBiz::AD_DSP)
      //   ->LookUpUserExperimentInfo(user_id, &abtest_info, device_id);
      // if (enable_adx_biz_type) {
      //   ks::SessionContextFactory::CreateUserMappingInfo(ks::AbtestBiz::AD_ADX)
      //     ->LookUpUserExperimentInfo(user_id, &adx_abtest_info, device_id);
      // }
      // if (enable_fanstop_biz_type) {
      //   ks::SessionContextFactory::CreateUserMappingInfo(ks::AbtestBiz::FANS_TOP)
      //     ->LookUpUserExperimentInfo(user_id, &fanstop_abtest_info, device_id);
      // }
      //
      //
      // 替换为支持 ab 2.0 的接口: GetExperimentInfo。
      // 接口见: ks/base/abtest/metrics/abtest_metric.h
      ks::ExperimentInfo abtest_info = ks::abtest::GetExperimentInfo(user_id, device_id, world_list);

      for (auto it = all_world_exp.begin(); it != all_world_exp.end(); it++) {
        const std::string world_name(it->first);
        // if (world_name == "world_adx_algorithm") {
        //   if (adx_abtest_info.find(world_name) == adx_abtest_info.end()) {
        //     continue;
        //   }
        //   real_exp_name = adx_abtest_info[world_name].experimentId;
        //   real_group = adx_abtest_info[world_name].groupId;
        // } else if (world_name.find("fans_top") != std::string::npos ||
        //            world_name.find("fanstop") != std::string::npos) {
        //   if (fanstop_abtest_info.find(world_name) == fanstop_abtest_info.end()) {
        //     continue;
        //   }
        //   real_exp_name = fanstop_abtest_info[world_name].experimentId;
        //   real_group = fanstop_abtest_info[world_name].groupId;
        // } else if (abtest_info.find(world_name) == abtest_info.end()) {
        //   continue;
        // } else {
        //   real_exp_name = abtest_info[world_name].experimentId;
        //   real_group = abtest_info[world_name].groupId;
        // }
        auto it_ab = abtest_info.find(world_name);
        if (it_ab == abtest_info.end()) {
          LOG_EVERY_N(INFO, 100000) << "cannot find expr info in abtest, world_name: " << world_name
                                    << ",user_id:" << user_id << ",device_id:" << device_id;
          continue;
        }

        std::string real_exp_name = it_ab->second.experimentId;
        std::string real_group = it_ab->second.groupId;

        ks::infra::PerfUtil::CountLogStash(1,
                                          "klearn.cross_auc",
                                          "total",
                                          world_name,
                                          real_exp_name,
                                          real_group);
        if (topic == unbias_topic || topic == unbias_topic_1) {
          WorldInfo & world_info = it->second;
          for (auto iter = world_info.begin(); iter != world_info.end(); iter++) {
            std::string exp_name = iter->first;
            for (const ExpInfo & exp_info : iter->second) {
              tensorflow::Tensor labels_tensor;
              if (!exp_info.processor->preprocess_unchange(&adlog, &labels_tensor)) {
                LOG_EVERY_N(ERROR, 10000) << "extract label failed !" << std::endl;
                continue;
              }

              if (adlog.item_size() != labels_tensor.shape().dim_size(0)) {
                  LOG_EVERY_N(INFO, 10) << "labels_tensor.shape().dim_size(0): "
                                    << labels_tensor.shape().dim_size(0)
                                    << " not equal to item_size: " << adlog.item_size();
                continue;
              }

              ks::infra::PerfUtil::CountLogStash(1,
                                                "klearn.cross_auc",
                                                "count_after_label_extractor",
                                                world_name,
                                                exp_name,
                                                real_group);

              for (auto iter_group = exp_info.groups.begin();
                  iter_group != exp_info.groups.end(); iter_group++) {
                auto& group_name = iter_group->first;
                auto& group_info = iter_group->second;
                handle_predict(&moss_client,
                            &auc_manager,
                            &adlog,
                            &labels_tensor,
                            adlog.Get().serialized_reco_user_info(),
                            group_info.cmd,
                            world_name,
                            exp_name,
                            group_info.name,
                            unbias,
                            exp_info.cmd_title,
                            exp_info.max_auc_bucket_size,
                            exp_info.is_regression_model,
                            exp_info.positive_weight,
                            &producers[thread_id],
                            &predict_producers[thread_id],
                            last_version);
              }
            }
          }
          continue;
        }

        if (it->second.find(real_exp_name) == it->second.end()) {
          continue;
        }
        for (const ExpInfo & exp_info : it->second.find(real_exp_name)->second) {
          if (exp_info.groups.find(real_group) == exp_info.groups.end()) {
            continue;
          }

          tensorflow::Tensor labels_tensor;
          if (!exp_info.processor->preprocess_unchange(&adlog, &labels_tensor)) {
            LOG_EVERY_N(ERROR, 10000) << "extract label failed !" << std::endl;
            continue;
          }

          if (adlog.item_size() != labels_tensor.shape().dim_size(0)) {
            LOG_EVERY_N(INFO, 10) << "labels_tensor.shape().dim_size(0): "
                                  << labels_tensor.shape().dim_size(0)
                                  << " not equal to item_size: " << adlog.item_size();
            continue;
          }

          ks::infra::PerfUtil::CountLogStash(1,
                                            "klearn.cross_auc",
                                            "count_after_label_extractor",
                                            world_name,
                                            real_exp_name,
                                            real_group);

          std::string base1 = exp_info.base_name;
          if (exp_info.groups.find(base1) == exp_info.groups.end()) {
            continue;
          }

          const std::string& base_cmd = exp_info.groups.find(base1)->second.cmd;
          if (real_group == base1) {
            for (auto iter_group = exp_info.groups.begin();
                iter_group != exp_info.groups.end(); iter_group++) {
              auto& group_name = iter_group->first;
              auto& group_info = iter_group->second;
              handle_predict(&moss_client,
                            &auc_manager,
                            &adlog,
                            &labels_tensor,
                            adlog.Get().serialized_reco_user_info(),
                            group_info.cmd,
                            world_name,
                            real_exp_name,
                            group_info.name,
                            base1,
                            exp_info.cmd_title,
                            exp_info.max_auc_bucket_size,
                            exp_info.is_regression_model,
                            exp_info.positive_weight,
                            &producers[thread_id],
                            &predict_producers[thread_id],
                            last_version);
            }
          } else {
            const GroupInfo& group_info = exp_info.groups.find(real_group)->second;;
            handle_predict(&moss_client,
                          &auc_manager,
                          &adlog,
                          &labels_tensor,
                          adlog.Get().serialized_reco_user_info(),
                          group_info.cmd,
                          world_name,
                          real_exp_name,
                          group_info.name,
                          group_info.name,
                          exp_info.cmd_title,
                          exp_info.max_auc_bucket_size,
                          exp_info.is_regression_model,
                          exp_info.positive_weight,
                          &producers[thread_id],
                          &predict_producers[thread_id],
                          last_version);

            handle_predict(&moss_client,
                          &auc_manager,
                          &adlog,
                          &labels_tensor,
                          adlog.Get().serialized_reco_user_info(),
                          base_cmd,
                          world_name,
                          real_exp_name,
                          base1,
                          group_info.name,
                          exp_info.cmd_title,
                          exp_info.max_auc_bucket_size,
                          exp_info.is_regression_model,
                          exp_info.positive_weight,
                          &producers[thread_id],
                          &predict_producers[thread_id],
                          last_version);
          }
        }
      }
    }
  };

  std::thread *process_threads = new std::thread[FLAGS_num_threads];
  int N = 1;

  for (size_t i = 0; i < FLAGS_num_threads; ++i) {
    process_threads[i] = std::thread(process_func, i);
    LOG_EVERY_N(INFO, 100000) << "+ thread process_func";
  }

  for (size_t i = 0; i < FLAGS_num_threads; ++i) {
    process_threads[i].join();
    LOG_EVERY_N(INFO, 100000) << "thread process_func join";
  }

  return 0;
}
