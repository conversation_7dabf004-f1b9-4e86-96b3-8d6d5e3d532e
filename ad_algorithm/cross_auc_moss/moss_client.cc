#include <string>
#include <vector>
#include <memory>
#include <utility>
#include <random>
#include <cinttypes>
#include <iostream>
#include <fstream>
#include "ks/base/abtest/session_context.h"
#include "base/common/logging.h"
#include "base/encoding/base64.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "base/strings/string_number_conversions.h"
#include "third_party/gflags/gflags/gflags.h"

#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "framework/application.h"
#include "third_party/libcurl/include/curl/curl.h"

#include "teams/ad/ad_algorithm/log_preprocess/ad_model_kconf_util.h"
#include "teams/ad/ad_algorithm/cross_auc_moss/moss_client.h"
#include "teams/ad/ad_nn/debug/util.h"

namespace ks {
namespace ad_algorithm {

namespace framework = ::ks::framework;
namespace rpc = ::ks::kess::rpc;
using ks::ad_base::OptionsFromMilli;

using Application = framework::Application;

using kuaishou::ad::algorithm::UniversePredictService;
using kuaishou::ad::algorithm::UniversePredictRequest;
using kuaishou::ad::algorithm::AdJointLabeledLog;

std::shared_ptr<PredictStub> MossClient::get_kess_client(const std::string& kess_name,
                                                                          int64_t config_version) {
  if (config_version != last_version) {
    valid_kess.clear();
    clients.clear();
    last_version = config_version;
  }

  if (valid_kess.find(kess_name) != valid_kess.end()) {
    if (valid_kess.find(kess_name)->second == true) {
      return clients.find(kess_name)->second;
    } else {
      return nullptr;
    }
  }

  auto tmp_client = ::ks::ad_base::AdKessClient::ClientOfKey<kuaishou::ad::algorithm::kess::UniversePredictService>(kess_name).second;

  if (tmp_client == nullptr) {
    LOG(INFO) << "config_version: " << config_version << ", kess client nullptr: " << kess_name;
    valid_kess[kess_name] = false;
    return nullptr;
  }

  valid_kess[kess_name] = true;
  clients[kess_name] = tmp_client;

  LOG(INFO) <<  "config_version: " << config_version << ", kess client success: " << kess_name;
  return clients[kess_name];
}

UniversePredictResponse MossClient::get_predict_result(ks::ad_algorithm::AdLogWrapper* p_adlog,
                                                       const string& reco_user_info,
                                                       const std::string& cmd,
                                                       bool is_config_changed) {
  UniversePredictResponse response;
  UniversePredictRequest request;

  std::string cmd1 = base::StringReplace(base::StringReplace(cmd, "/", "_", true), ":", "_", true);
  std::string kess_name = std::string("grpc") + cmd1 + "_auc_test_predict_server";
  auto client = get_kess_client(kess_name, is_config_changed);
  if (client == nullptr) {
    return response;
  }

  const auto& adlog = p_adlog->Get();
  if (adlog.item_size() <= 0) {
    return response;
  }
  request.set_llsid(adlog.llsid());
  request.set_user_id(adlog.user_info().id());
  request.set_predict_type(::kuaishou::ad::algorithm::AD_JOINT_LABELED_LOG);
  request.set_is_debug_req(false);
  request.add_cmd(cmd);
  // request.add_cmd("/ad/dsp/cvr:universe_ctr_xf_v3");
  request.set_user_id(adlog.user_info().id());
  request.set_item_type(adlog.item(0).type());
  request.set_serialized_reco_user_info(reco_user_info);
  for (int i = 0; i < adlog.item_size(); i++) {
    request.add_item_id(adlog.item(i).id());
    // add callback type
    if (adlog.item(i).ad_dsp_info().has_call_back_type()) {
      request.add_callback_event(adlog.item(i).ad_dsp_info().call_back_type().callback_event(0));
    }
  }

  auto debug_raw_data = request.add_debug_raw_data();
  debug_raw_data->set_pay_load_type(kuaishou::ad::debug::PayLoadType::PLT_AD_JOINT_LABELED_LOG);
  auto mutable_bytes = debug_raw_data->mutable_serialized_bytes();
  if (!adlog.SerializeToString(mutable_bytes)) {
    LOG_EVERY_N(ERROR, 100) << "Serialize to string failed";
    return response;
  }

  rpc::grpc::Options options;
  options.SetTimeout(std::chrono::milliseconds(200));
  // std::string tmp_kess_name = "grpc_ad_dsp_cvr_universe_ctr_xf_v3_auc_test_predict_server";
  auto status = client->SelectOne()->Predict(options, request, &response);

  if (!status.ok() || response.predict_result_size() <= 0
      || response.status() != kuaishou::ad::algorithm::STATUS_OK) {
    LOG_EVERY_N(ERROR, 1000) << "fail!!! "
      << " error_code(): " << status.error_code()
      << ", error_message: " << status.error_message()
      << ", status.ok(): " << status.ok()
      << ", item_size: " << adlog.item_size()
      << ", size: " << response.predict_result_size()
      << ", status: " << response.status();
  } else {
    LOG_EVERY_N(INFO, 10000) << "success!!! "
      << " error_code(): " << status.error_code()
      << ", error_message: " << status.error_message()
      << ", status.ok(): " << status.ok()
      << ", item_size: " << adlog.item_size()
      << ", size: " << response.predict_result_size()
      << ", status: " << response.status()
      << ", result: " << response.predict_result(0).value(0);
  }

  return response;
}

size_t write_callback(char *contents, size_t size, size_t nmemb, void *userp) {
  ((std::string*)userp)->append(reinterpret_cast<char*>(contents), size * nmemb);
  return size * nmemb;
}

void old_http(base::Json* kconf_config) {
  std::string url_prefix = "http://sdpg.abtest-api.internal/rest/i/abtest/parameter/get_by_experiment?";

  std::string url = "";
  std::string content;
  CURL *curl;
  CURLcode res;
  struct curl_slist *headers = NULL;
  headers = curl_slist_append(headers, "Accept: application/json");
  headers = curl_slist_append(headers, "Content-Type: application/json");
  headers = curl_slist_append(headers, "charset: utf-8");
  curl = curl_easy_init();

  if (!curl) {
    LOG(FATAL) << "curl error";
  }

  curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
  curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
  curl_easy_setopt(curl, CURLOPT_HTTPGET, 1);
  curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_callback);
  curl_easy_setopt(curl, CURLOPT_WRITEDATA, &content);
  res = curl_easy_perform(curl);

  LOG(INFO) << "content: " << content;
  if (CURLE_OK != res) {
    LOG(FATAL) << "curl fail: " << res << ", content: " << content;
  }

  base::Json content_json(base::StringToJson(content));
  curl_slist_free_all(headers);
}

}  // namespace ad_algorithm
}  // namespace ks
