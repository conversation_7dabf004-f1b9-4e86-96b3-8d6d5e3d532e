cc_binary(name = "ad_algorithm_cross_auc_moss",
  srcs = ["ad_algorithm_cross_auc_moss.cc", "auc_util.cc", "moss_client.cc"],
  cppflags = [
            "-D GOOGLE_LOGGING=1",
            "-I third_party/flatbuffers/include/",
            "-D DMLC_USE_GLOG",
            "-D USE_HDFS=1",
            "-D DMLC_USE_HDFS=1",
            # "-std=c++17"
              ],
  ldflags=[],
  deps = [
              "//teams/ad/ad_algorithm/pb_adaptor/BUILD:pb_adaptor",
              "//teams/ad/ad_algorithm/model_online/BUILD:model_util",
              "//ks/util/BUILD:util",
              "//ks/serving_util/BUILD:serving_util",
              "//teams/ad/ad_base/src/util/BUILD:util",
              '//teams/ad/ad_base/src/kess/BUILD:kess_client',
              "//teams/ad/ad_algorithm/feature/BUILD:fast_ad_log",
              "//base/strings/BUILD:strings",
              "//base/common/BUILD:base",
              "//base/thread/BUILD:thread",
              "//base/encoding/BUILD:encoding",
              "//base/file/BUILD:file",
              "//base/hash_function/BUILD:hash_function",
              "//teams/ad/ad_algorithm/log_preprocess/BUILD:log_preprocess",
              "//base/encoding/BUILD:encoding",
              "//teams/ad/ad_proto/kuaishou/BUILD:algo",
              "//teams/ad/ad_proto/kuaishou/BUILD:ad",
              "//teams/ad/ad_proto/kuaishou/BUILD:ad_embedding_retr",
              "//infra/redis_proxy_client/BUILD:redis_client",
              "//third_party/zeromq/BUILD:zeromq",
              "//third_party/libcurl/BUILD:libcurl",
              "//third_party/flatbuffers/BUILD:flatbuffers",
               "//ks/cofea/BUILD:proto",
               "//ks/reco_proto/feature_pipe/BUILD:proto",
               "//teams/reco-arch/colossus/BUILD:proto",
               "//serving_base/retrieval/BUILD:retrieval",
	           "//ks/ksib_reco/ksib_proto/proto/BUILD:kwai_proto",
               "//ks/base/abtest/BUILD:common",
               "//teams/ad/ad_algorithm/pb_adaptor/BUILD:adlog_pb_related",
           ],
 )
