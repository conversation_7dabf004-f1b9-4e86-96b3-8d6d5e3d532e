#pragma once

#include <string>
#include <vector>
#include <memory>
#include <map>
#include "third_party/gflags/gflags/gflags.h"

#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_algorithm/feature/fast/ad_log_wrapper.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.grpc.pb.h"

namespace ks {
namespace ad_algorithm {

using kuaishou::ad::algorithm::UniversePredictResponse;
using PredictStub = ks::ad_base::KessClientAdapter<kuaishou::ad::algorithm::kess::UniversePredictService>;

class MossClient {
 public:
  std::shared_ptr<PredictStub> get_kess_client(const std::string& kess_name,
                                                                int64_t config_version);
  UniversePredictResponse get_predict_result(ks::ad_algorithm::AdLogWrapper* p_adlog,
                                             const std::string& reco_user_info,
                                             const std::string& cmd,
                                             bool is_config_changed);

 private:
  std::map<std::string, std::shared_ptr<PredictStub>> clients;
  std::map<std::string, bool> valid_kess;
  int64_t last_version = 0;
};

void old_http(base::Json* kconf_config);

}  // namespace ad_algorithm
}  // namespace ks
