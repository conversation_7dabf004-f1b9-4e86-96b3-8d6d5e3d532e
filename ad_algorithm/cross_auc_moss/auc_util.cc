#include <math.h>
#include <string>
#include <memory>
#include <sstream>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <iostream>
#include <fstream>
#include <cstdio>
#include <limits>
#include <thread>
#include <utility>
#include <algorithm>
#include "ks/util/json.h"
#include "ks/serving_util/dynamic_config.h"
#include "base/common/gflags.h"
#include "base/common/base.h"
#include "base/common/logging.h"
#include "base/strings/string_split.h"
#include "base/time/timestamp.h"
#include "base/encoding/base64.h"
#include "base/encoding/line_escape.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/ad_base/src/kafka/kafka_client.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "perfutil/perfutil.h"

#include "teams/ad/ad_algorithm/cross_auc_moss/auc_util.h"
namespace ks {
namespace ad_algorithm {

DEFINE_int32(max_data_size, 500000, "max_data_size");
DEFINE_int32(write_time_gap_s, 1, "write time gap");
DEFINE_int32(min_count, 50000, "max count");
DEFINE_int32(sample_per_minute_limit, 5, "sample_per_minute_limit");

RealData::RealData(int max_auc_bucket_size) {
  pos = 0;
  cnt = 0;
  p_cnt = 0;
  pred_sum = 0.0;

  std::unique_ptr<AucData[]> tmp_auc_data(new AucData[max_auc_bucket_size]);
  auc_data = std::move(tmp_auc_data);
  this->max_auc_bucket_size = max_auc_bucket_size;

  compute_step = std::max((int)(max_auc_bucket_size * 0.001), 1);

  update_time = base::GetTimestamp() / 1000;
  // set min_count
  min_count = FLAGS_min_count;
  // set max_data_size
  max_data_size = FLAGS_max_data_size;

  std::unique_ptr<PredictData[]> tmp_data(new PredictData[max_data_size]);
  data = std::move(tmp_data);

  for (size_t i = 0; i < max_data_size; ++i) {
    data[i].index = i;
  }

  std::unique_ptr<PredictData[]> tmp_aux_data(new PredictData[max_data_size]);
  aux_data = std::move(tmp_aux_data);

  cnt_reset = 0;

  eps = 1e-8;

  sample_count = 0;
  last_count_time = base::Time::Now();

  sample_per_minute_limit = FLAGS_sample_per_minute_limit;
}

void RealData::AddToAucData(float rate, int label) {
  size_t index = static_cast<size_t>(round(rate * max_auc_bucket_size));
  if (index > (max_auc_bucket_size - 1)) {
    index = max_auc_bucket_size - 1;
  }
  if (label > 0) {
    auc_data[index].p_cnt += 1;
  } else {
    auc_data[index].n_cnt += 1;
  }
}

void RealData::RemoveFromAucData(float rate, int label) {
  size_t index = static_cast<size_t>(round(rate * max_auc_bucket_size));
  if (index > (max_auc_bucket_size - 1)) {
    index = max_auc_bucket_size - 1;
  }
  if (label > 0) {
    auc_data[index].p_cnt -= 1;
  } else {
    auc_data[index].n_cnt -= 1;
  }
}

void RealData::Put(float rate, int label) {
  cnt += 1;
  if (cnt > max_data_size) {
    RemoveFromAucData(data[pos].rate, data[pos].label);
    p_cnt -= data[pos].label;
    pred_sum = MAX(0.0, pred_sum -data[pos].rate);
  }
  p_cnt += label;
  pred_sum += rate;  //所有样本的预估值
  data[pos].rate = rate;
  data[pos].label = label;
  AddToAucData(rate, label);
  pos = (pos + 1) % max_data_size;
  update_time = base::GetTimestamp() / 1000;
}

void RealData::SetConfig(std::string world_name,
                         std::string exp_name,
                         std::string abtest_sub_group,
                         std::string abtest_traffic,
                         std::string cmd_title) {
  this->world_name = world_name;
  this->exp_name = exp_name;
  this->abtest_sub_group = abtest_sub_group;
  this->abtest_traffic = abtest_traffic;
  this->cmd_title = cmd_title;
}

float RealData::GetReal() {
  int total = GetCount();
  if (total > max_data_size) {
    total = max_data_size;
  }
  // int n_cnt = MAX(1, total - p_cnt);
  return static_cast<double>(p_cnt) / static_cast<double>(MAX(1, total));
}

float RealData::GetPred() {
  int total = GetCount();
  if (total > max_data_size) {
    total = max_data_size;
  }
  return static_cast<double>(pred_sum) / static_cast<double>(MAX(1, total));
}

float RealData::GetAuc() {
  float up = 0.0;
  float p = 0.0;
  float n = 0.0;
  for (uint32_t i = 0; i < max_auc_bucket_size; ++i) {
    up += auc_data[i].p_cnt * n + auc_data[i].p_cnt * auc_data[i].n_cnt * 0.5;
    p += auc_data[i].p_cnt;
    n += auc_data[i].n_cnt;
  }

  float auc = 0.0;
  if (p > eps) {
    auc = up * 1.0 / p / n;
  }

  LOG_EVERY_N(INFO, 100) << "exp_name: " << exp_name
                         << ", cmd_title: " << cmd_title
                         << ", abtest_sub_group: " << abtest_sub_group
                         << ", abtest_traffic: " << abtest_traffic
                         << ", up: " << up
                         << ", p: " << p
                         << ", n: " << n
                         << ", auc: " << auc;
  return auc;
}

float RealData::GetLoss() {
  float loss = 0.0;
  int tmp_cnt = std::min(cnt, max_data_size);
  for (int i = 0; i < tmp_cnt; ++i) {
    // loss = -[ylog(y') + (1-y)log(1-y')]
    int label = data[i].label;
    float rate = data[i].rate;
    loss += -(label * log(rate+1e-8) + (1-label) * log(1-rate + 1e-8));
  }
  return loss*1.0 / tmp_cnt;
}

float RealData::GetRmse() {
  // loss = sqrt(sum((y-y1)^2)/n)
  double loss = 0.0;
  int tmp_cnt = std::min(cnt, max_data_size);
  for (int i = 0; i < tmp_cnt; ++i) {
    int label = data[i].label;
    double rate = data[i].rate;
    loss += pow((label - rate), 2);
  }
  return sqrt(loss*1.0 / tmp_cnt);
}

float RealData::GetMae() {
  // loss = sum(abs(y - y')) / n
  double loss = 0.0;
  int tmp_cnt = std::min(cnt, max_data_size);
  for (int i = 0; i < tmp_cnt; ++i) {
    int label = data[i].label;
    double rate = data[i].rate;
    loss += fabs(rate - label * 1.0);
  }
  return 1.0 * loss / tmp_cnt;
}

float RealData::GetSmape() {
  // loss = 2.0 * sum(abs((y - y') / (y + y'))) / n
  double loss = 0.0;
  int tmp_cnt = std::min(cnt, max_data_size);
  for (int i = 0; i < tmp_cnt; ++i) {
    int label = data[i].label;
    double rate = data[i].rate;
    double numerator = fabs(rate - label * 1.0);
    double denominator = fabs(rate + label * 1.0) + 1e-8;
    loss +=  2.0 * numerator / denominator;
  }
  return 1.0 * loss / tmp_cnt;
}

float RealData::GetNormalizedGini() {
  int tmp_cnt = std::min(cnt, max_data_size);
  std::sort(data.get(), data.get() + tmp_cnt,
            [](const PredictData& a, const PredictData& b) {return a.label > b.label;});
  double sum = 0;
  double max_gini = 0;
  for (size_t i = 0; i < tmp_cnt; ++i) {
    sum += data[i].label;
    max_gini += sum;
  }
  std::sort(data.get(), data.get() + tmp_cnt,
            [](const PredictData& a, const PredictData& b) {return a.rate > b.rate;});
  sum = 0;
  double pred_gini = 0;
  for (size_t i = 0; i < tmp_cnt; ++i) {
    sum += data[i].label;
    pred_gini += sum;
  }
  std::sort(data.get(), data.get() + tmp_cnt,
            [](const PredictData& a, const PredictData& b) {return a.index < b.index;});
  return 1.0 * pred_gini / (max_gini + 1e-8);
}
float RealData::GetRegressionAuc01() {
  int tmp_cnt = std::min(cnt, max_data_size);
  std::sort(data.get(), data.get() + tmp_cnt,
              [](const PredictData& a, const PredictData& b) {return a.rate < b.rate;});
  int total = 0;
  int right = 0;
  int k = 0;
  int neg_num = 0;
  for (size_t i = 0; i < tmp_cnt; i++) {
    if (data[i].label == 0) {
      neg_num++;
      continue;
    }
    right += neg_num;
  }
  total = neg_num * (tmp_cnt - neg_num);
  if (total == 0) {
    return 0.0;
  }
  return right * 1.0 / total;
}
float RealData::GetRegressionAuc11() {
  int tmp_cnt = std::min(cnt, max_data_size);
  std::unordered_map<int, std::vector<float>> dict;
  int pos_cnt = 0;
  for (size_t i = 0; i < tmp_cnt; i++) {
    if (data[i].label > 0) {
      pos_cnt++;
      auto key = (int)(data[i].label);
      if (dict.find(key) == dict.end()) {
        dict[key] = std::vector<float>{data[i].rate};
      } else {
        std::vector<float> tmp = dict[key];
        tmp.push_back(data[i].rate);
        dict[key] = tmp;
      }
    }
  }
  std::unordered_map<int, std::vector<float>>::iterator iter;
  std::vector<int> label_list;
  for (iter = dict.begin(); iter != dict.end(); iter++) {
    std::vector<float> pred_list = iter->second;
    std::sort(pred_list.begin(), pred_list.end(), [](const float& a, const float& b) {return a < b;});
    label_list.push_back(iter->first);
  }
  std::sort(label_list.begin(), label_list.end(), [](const int& a, const int& b) {return a < b;});
  int pos_total = 0;
  int pos_right = 0;
  int last_acc = 0;
  for (int i = 0; i < label_list.size(); i++) {
    pos_total += dict[label_list[i]].size() * last_acc;
    last_acc += dict[label_list[i]].size();
    for (int s = 0; s < i; s++) {
      int k = 0;
      for (int j = 0; j < dict[label_list[i]].size(); j++) {
        int t = k;
        while (t < dict[label_list[s]].size()) {
          if (dict[label_list[s]][t] >= dict[label_list[i]][j]) {
            break;
          }
          t += 1;
        }
        pos_right += t;
        k = t;
      }
    }
  }
  if (pos_total == 0) {
    return 0.0;
  }
  return pos_right * 1.0/ pos_total;
}

float RealData::GetRegressionAuc(float positive_weight) {
  int tmp_cnt = std::min(cnt, max_data_size);
  std::sort(data.get(), data.get() + tmp_cnt,
              [](const PredictData& a, const PredictData& b) {return a.rate < b.rate;});
    std::function<std::pair<double, double>(int, int)> cal_pos_pair =
     [&](int start, int end) -> std::pair<double, double> {
        if (end - start <= 1) {
            return std::pair<double, double>(0.0, 0.0);
        }
        std::pair<double, double> result;
        int mid = (start + end) >> 1;
        std::pair<double, double> left_result = cal_pos_pair(start, mid);
        std::pair<double, double> right_result = cal_pos_pair(mid, end);
        result.first = left_result.first + right_result.first;
        result.second = left_result.second + right_result.second;

        int index_l = start;
        int index_r = mid;
        int left_last_value = -1;
        float left_last_value_weight = 0.0;
        float left_cumsum_sample_weight = 0.0;
        for (size_t i = start; i < end; ++i) {
            if (index_l < mid && index_r < end) {
                if (data[index_l].label <= data[index_r].label) {
                    float sample_weight = data[index_l].label > 0 ? positive_weight : 1;
                    aux_data[i] = data[index_l];
                    if (left_last_value == data[index_l].label) {
                        left_last_value_weight += sample_weight;
                    } else {
                        left_last_value = data[index_l].label;
                        left_last_value_weight = sample_weight;
                    }
                    left_cumsum_sample_weight += sample_weight;
                    index_l++;
                } else {
                    float sample_weight = data[index_r].label > 0 ? positive_weight : 1;
                    result.first += left_cumsum_sample_weight * sample_weight;
                    if (data[index_r].label == left_last_value) {
                        result.first -= left_last_value_weight * sample_weight;
                        result.second += left_last_value_weight * sample_weight;
                    }
                    aux_data[i] = data[index_r++];
                }
            } else if (index_l < mid) {
                aux_data[i] = data[index_l++];
            } else {
                float sample_weight = data[index_r].label > 0 ? positive_weight : 1;
                result.first += left_cumsum_sample_weight * sample_weight;
                if (data[index_r].label == left_last_value) {
                    result.first -= left_last_value_weight * sample_weight;
                    result.second += left_last_value_weight * sample_weight;
                }
                aux_data[i] = data[index_r++];
            }
        }
        for (size_t i = start; i < end; ++i) {
            data[i] = aux_data[i];
        }
        return result;
    };

    std::pair<double, double> pos_pair = cal_pos_pair(0, tmp_cnt);
    int pos_cnt = 0;
    for (size_t i = 0; i < tmp_cnt; ++i) {
        if (data[i].label > 0) {
            pos_cnt += 1;
        }
    }
    int neg_cnt = tmp_cnt - pos_cnt;
    double total_pair = 0.5 * (pos_cnt * (pos_cnt - 1) * positive_weight * positive_weight
                               + neg_cnt * (neg_cnt - 1)
                               + pos_cnt * neg_cnt * positive_weight * 2) - pos_pair.second;
    std::sort(data.get(), data.get() + tmp_cnt,
              [](const PredictData& a, const PredictData& b) {return a.index < b.index;});
    if (total_pair <= 0) {
      return 0.0;
    }
    return 1.0 * pos_pair.first / total_pair;
}

void AucManager::AddSample(const std::string & cmd,
                           double pred,
                           int label,
                           const std::string& world_name,
                           const std::string& exp_name,
                           const std::string& abtest_sub_group,
                           const std::string& abtest_traffic,
                           const std::string& cmd_title,
                           int max_auc_bucket_size,
                           bool is_regression_model,
                           float positive_weight,
                           ks::ad_base::AdKafkaProducer* predict_producer) {
  data_map_mutex.lock();

  std::shared_ptr<RealData> real_data_ptr = nullptr;
  std::string key = world_name + "_" + exp_name + "_" + cmd_title + "_" +
                    abtest_sub_group + "_" + abtest_traffic + "_" + cmd;
  auto it = data_map.find(key);
  if (it != data_map.end()) {
    real_data_ptr = it->second;

    if (max_auc_bucket_size > real_data_ptr->max_auc_bucket_size) {
      std::unique_ptr<AucData[]> tmp_auc_data(new AucData[max_auc_bucket_size]);
      for (int i = 0; i < real_data_ptr->max_auc_bucket_size; i++) {
        tmp_auc_data[i] = real_data_ptr->auc_data[i];
      }
      real_data_ptr->auc_data = std::move(tmp_auc_data);

      real_data_ptr->max_auc_bucket_size = max_auc_bucket_size;
    }
  } else {
    auto pit = data_map.emplace(key, new RealData(max_auc_bucket_size));
    real_data_ptr = (pit.first)->second;
    real_data_ptr->SetConfig(world_name, exp_name, abtest_sub_group, abtest_traffic, cmd_title);
  }
  real_data_ptr->Put(pred, label);

  real_data_ptr->sample_count += 1;
  base::Time now = base::Time::Now();
  if ((now - real_data_ptr->last_count_time).InMinutes() >= 10) {
    if (real_data_ptr->sample_count < real_data_ptr->sample_per_minute_limit * 10) {
      real_data_ptr->is_small_data = true;
    }
    real_data_ptr->sample_count = 0;
    real_data_ptr->last_count_time = base::Time::Now();
  }
  if (real_data_ptr->is_small_data) {
    std::ostringstream oss;
    oss << world_name << ","
      << exp_name << ","
      << abtest_traffic << ","
      << "cmd_title:" << cmd_title << ","
      << abtest_sub_group << ","
      << 0 << ","
      << 0 << ","
      << label << ","
      << pred;
    if (is_regression_model) {
      oss << "," << is_regression_model
          << "," << positive_weight;
    }
    std::string send_str = oss.str();

    LOG_EVERY_N(INFO, 100) << send_str;
    std::string str_base64;
    if (base::Base64Encode(send_str, &str_base64)) {
      predict_producer->Produce(str_base64);

      ks::infra::PerfUtil::CountLogStash(1,
                                         "klearn.cross_auc",
                                         "small_data",
                                         world_name,
                                         exp_name,
                                         abtest_sub_group,
                                         abtest_traffic,
                                         cmd_title);
    }
  } else {
    if (real_data_ptr->cnt_reset > 0 &&
        real_data_ptr->cnt_reset < real_data_ptr->max_auc_bucket_size &&
        real_data_ptr->cnt_reset % real_data_ptr->compute_step == 0) {
      auto auc = real_data_ptr->GetAuc();
      auto loss = real_data_ptr->GetLoss();
      auto real_mean = real_data_ptr->GetReal();
      auto prob_mean = real_data_ptr->GetPred();
      auto rmse = real_data_ptr->GetRmse();
      ks::infra::PerfUtil::IntervalLogStash((int64_t)(auc * 1000000),
                                            "klearn.cross_auc",
                                            "auc",
                                            world_name,
                                            exp_name,
                                            abtest_sub_group,
                                            abtest_traffic,
                                            cmd_title);
      ks::infra::PerfUtil::IntervalLogStash((int64_t)(loss * 1000000),
                                            "klearn.cross_auc",
                                            "loss",
                                            world_name,
                                            exp_name,
                                            abtest_sub_group,
                                            abtest_traffic,
                                            cmd_title);
      ks::infra::PerfUtil::IntervalLogStash((int64_t)(real_mean * 1000000),
                                            "klearn.cross_auc",
                                            "real_mean",
                                            world_name,
                                            exp_name,
                                            "",
                                            abtest_traffic,
                                            cmd_title);
      ks::infra::PerfUtil::IntervalLogStash((int64_t)(prob_mean * 1000000),
                                            "klearn.cross_auc",
                                            "prob_mean",
                                            world_name,
                                            exp_name,
                                            abtest_sub_group,
                                            abtest_traffic,
                                            cmd_title);
      ks::infra::PerfUtil::IntervalLogStash((int64_t)(rmse * 1000000),
                                            "klearn.cross_auc",
                                            "rmse",
                                            world_name,
                                            exp_name,
                                            abtest_sub_group,
                                            abtest_traffic,
                                            cmd_title);


      if (is_regression_model) {
        auto mae = real_data_ptr->GetMae();
        auto smape = real_data_ptr->GetSmape();
        auto normalized_gini = real_data_ptr->GetNormalizedGini();
        auto regression_auc = real_data_ptr->GetRegressionAuc(positive_weight);
        auto auc_01 = real_data_ptr->GetRegressionAuc01();
        auto auc_11 = real_data_ptr->GetRegressionAuc11();

        ks::infra::PerfUtil::IntervalLogStash((int64_t)(mae* 1000000),
                                              "klearn.cross_auc",
                                              "mae",
                                              world_name,
                                              exp_name,
                                              abtest_sub_group,
                                              abtest_traffic,
                                              cmd_title);
        ks::infra::PerfUtil::IntervalLogStash((int64_t)(smape* 1000000),
                                              "klearn.cross_auc",
                                              "smape",
                                              world_name,
                                              exp_name,
                                              abtest_sub_group,
                                              abtest_traffic,
                                              cmd_title);
        ks::infra::PerfUtil::IntervalLogStash((int64_t)(normalized_gini * 1000000),
                                              "klearn.cross_auc",
                                              "normalized_gini",
                                              world_name,
                                              exp_name,
                                              abtest_sub_group,
                                              abtest_traffic,
                                              cmd_title);
        ks::infra::PerfUtil::IntervalLogStash((int64_t)(regression_auc * 1000000),
                                              "klearn.cross_auc",
                                              "regression_auc",
                                              world_name,
                                              exp_name,
                                              abtest_sub_group,
                                              abtest_traffic,
                                              cmd_title);
        ks::infra::PerfUtil::IntervalLogStash((int64_t)(auc_01 * 1000000),
                                              "klearn.cross_auc",
                                              "auc01",
                                              world_name,
                                              exp_name,
                                              abtest_sub_group,
                                              abtest_traffic,
                                              cmd_title);
        ks::infra::PerfUtil::IntervalLogStash((int64_t)(auc_11 * 1000000),
                                              "klearn.cross_auc",
                                              "auc11",
                                              world_name,
                                              exp_name,
                                              abtest_sub_group,
                                              abtest_traffic,
                                              cmd_title);
      }
    }

    if (real_data_ptr->cnt_reset >= real_data_ptr->max_auc_bucket_size) {
      real_data_ptr->cnt_reset = 0;
    }

    real_data_ptr->cnt_reset += 1;
  }

  data_map_mutex.unlock();
}

}  // namespace ad_algorithm
}  // namespace ks
