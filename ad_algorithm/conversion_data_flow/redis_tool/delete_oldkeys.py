#!/usr/bin/env python
#coding=utf-8

import sys
import storage
import datetime
from redis import RedisError

class RedisAdBackendDataFlow:
    def __init__(self):
        self.db = storage.get_kcc_redis_resource(biz='adBackendDataFlow',biz_def='infra')

    def get(self, key):
        return self.db.get(key)

    def delete(self, key):
        if len(key.strip()) == 0 or key.endswith('*'):
            print('can not del ' + key)
            return -1
        return self.db.delete(key)

if __name__ == '__main__':
    if len(sys.argv) != 4:
        print('usage:\npython delete_oldkeys.py start_timestamp end_timestamp part')
        sys.exit()
    start_timestamp = int(sys.argv[1])
    end_timestamp = int(sys.argv[2])
    part = int(sys.argv[3])

    start_dt = datetime.datetime.fromtimestamp(start_timestamp).strftime('%Y-%m-%d %H:%M:%S')
    end_dt = datetime.datetime.fromtimestamp(end_timestamp).strftime('%Y-%m-%d %H:%M:%S')
    now_dt = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    print('now is %s' % now_dt)
    print('del keys from %s to %s' % (start_dt, end_dt))
    access = input('continue y/n?')
    if access == 'y':
        db = RedisAdBackendDataFlow()
        print('start del')
        for t in range(start_timestamp, end_timestamp + 1):
            for p in range(0, part + 1):
                key = 'bcd#%s#%s' % (t, p)
                print('del key %s %s' % (key, db.delete(key)))
                pass
            pass
        print('finish del')
        pass
    pass



