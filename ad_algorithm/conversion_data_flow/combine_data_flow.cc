#include "teams/ad/ad_base/src/container/concurrent_queue.h"
#include "teams/ad/ad_base/src/kafka/kafka_client.h"
#include "teams/ad/ad_base/src/common/common.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "base/common/gflags.h"
#include "base/common/base.h"
#include "base/common/logging.h"
#include "base/time/timestamp.h"
#include "base/strings/string_split.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_number_conversions.h"
#include "base/encoding/base64.h"
#include "base/encoding/line_escape.h"
#include "ks/serving_util/dynamic_config.h"
#include "teams/ad/picasso/client/picasso_client.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.pb.h"
#include "teams/ad/picasso/sdk/picasso_client/picasso_client.h"
#include "third_party/tbb/include/tbb/concurrent_hash_map.h"
#include "teams/ad/ad_proto/kuaishou/ad/picasso/picasso_update.pb.h"
#include "teams/ad/picasso/sdk/picasso_client/picasso_client.h"
#include "teams/ad/ad_algorithm/log_preprocess/ad_model_kconf_util.h"


#include <iostream>
#include <fstream>
#include <string>
#include <random>
using namespace std;
using namespace ks::ad_picasso;
using namespace ks::ad_algorithm;
using namespace kuaishou::ad::algorithm;

using namespace tbb::interface5;


const string DSP_TABLE_NAME = "adDspImpressionFull"; //信息日志表
const string ACCOUNT_ACTION_TYPE_PREFIX= "account_action_type"; //账号转化类型
const uint64 SLEEP_TIME = 10;  // in ms
const uint64 KAFKA_TIME_OUT = 10; // in ms

DEFINE_int32(num_server_log_threads,10,"num_thread_label");
DEFINE_int32(num_impression_log_threads,1,"num_thread_example");
DEFINE_int32(buffer_size, 20480, "buffer_size");

class ShuffleBuffer {
 public:
  ShuffleBuffer(size_t size, ks::ad_base::AdKafkaProducer * producer) {
    size_ = size;
    producer_ = producer;
    log_buffer_ = new AdJointLabeledLog *[size];
    for (size_t i = 0; i < size_; ++i) {
      log_buffer_[i] = nullptr;
    }
  }
  void push(AdJointLabeledLog * ad_joint_labeled_log, int index) {
    // 模型实时训练日志流开关
    if (!SupportAdModelTrainLog()){
      falcon::Inc("stop_write_ad_model_train_log_cnt");
      // LOG(INFO)<<"SupportAdModelTrainLog off";
      return;
    }
    auto log_to_put =  ad_joint_labeled_log;
    if (index >= 0 && index < size_) {
      mtx_.lock();
      auto temp = log_to_put;
      log_to_put = log_buffer_[index];
      log_buffer_[index] = temp;
      mtx_.unlock();
    } 
    if (log_to_put != nullptr) {
      std::string send_val;
      log_to_put->SerializeToString(&send_val);
      falcon::Inc("write_to_kafka_cnt");
      producer_->Produce(send_val);
      delete log_to_put;
    }
  }
  ~ShuffleBuffer() {
    for(size_t i = 0; i < size_; ++i) {
      std::string send_val;
      log_buffer_[i]->SerializeToString(&send_val);
      producer_->Produce(send_val);
      delete log_buffer_[i];
      log_buffer_[i] = nullptr;
    }
    delete [] log_buffer_;
  }
 private:
  AdJointLabeledLog ** log_buffer_;
  ks::ad_base::AdKafkaProducer * producer_;
  int size_;
  std::mutex mtx_;
};

int main(int argc, char* argv[]) {
  base::InitApp(&argc, &argv, "转化日志流");
  ks::ad_base::AdKessClient::Instance().SetCaller("data_flow_full");
  auto picasso = ks::ad_picasso::sdk::PicassoClient::GetInstance();
  ks::ad_picasso::sdk::PicassoOption opt;
  opt.client_tag = "data_flow_full";  //配置tag参数,对于每一个服务是唯一的,限制访问特定的表
  opt.self_kess_server_name = "data_flow_full";
  opt.use_hash = true;
  if (!picasso->Init(opt)) {
    return -1;
  }
  // 读piccasso
  auto picassoBGet = [&](const std::string& table, const std::string& key, std::string& value, int retry = 3) {
    auto ret = ks::ad_picasso::sdk::STATUS_OK;
    do {
      ret = picasso->BGet(table, key, value);
    } while (--retry>0 && ret != ks::ad_picasso::sdk::STATUS_OK);
    return ret;
  };
  
  auto mq_env = ks::DynamicJsonConfig::GetConfig()->Get("dsp_log_parser_config");
  if (!mq_env) {
    return -1;
  } 
  int buffer_size = FLAGS_buffer_size;
  int num_server_log_threads = FLAGS_num_server_log_threads;
  int num_impression_log_threads = FLAGS_num_impression_log_threads;
  
  std::mt19937 rng;
  rng.seed(std::random_device()());
  std::uniform_int_distribution<int> dist_int(0, buffer_size);
  const std::string server_log_group_id = mq_env->GetString("server_log_group_id","server_log_combine");
  const std::string item_impression_log_group_id  = mq_env->GetString("item_impression_log_group_id",
                                                                      "item_impression_log_combine");
  const std::string consumer_user_param = mq_env->GetString(
      "consumer_user_param",
      "fetch.error.backoff.ms=0;queued.max.messages.kbytes=5120");

  ks::ad_base::AdKafkaConsumer server_log_consumer;
  auto consumer_config = ks::DynamicJsonConfig::GetConfig()->Get("mq_consumer_config_0");
  if (!consumer_config) {
    LOG(FATAL) << "get mq_consumer_config_0 error";
  }

  std::string topic = consumer_config->GetString("topic");
  if (topic.size() == 0) {
    LOG(FATAL) << "cannot get mq_consumer_config_0.topic";
  }
  int ret = server_log_consumer.InitConsumer(topic, server_log_group_id, consumer_user_param);
  if (ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
    LOG(ERROR)<< "init example consumer failed. ";
    return 1;
  }
  ks::ad_base::AdKafkaConsumer item_impression_consumer;
  consumer_config = ks::DynamicJsonConfig::GetConfig()->Get("mq_consumer_config_1");
  if (!consumer_config) {
    LOG(FATAL) << "get mq_consumer_config_1 error";
  }
  topic = consumer_config->GetString("topic");
  if (topic.size() == 0) {
    LOG(FATAL) << "cannot get mq_consumer_config_1.topic";
  }
  ret = item_impression_consumer.InitConsumer(topic, item_impression_log_group_id, consumer_user_param);
  if (ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
    LOG(ERROR)<< "init label consumer failed. id.";
    return 1;
  }
  const std::string user_params = "compression.codec=lz4";
  ks::ad_base::AdKafkaProducer producer;
  producer.Init("server_show_conversion_labeled_log", user_params);
  ShuffleBuffer shuffle_buffer(buffer_size, &producer);
  concurrent_hash_map<uint64,kuaishou::ad::algorithm::CallBackType> callback_type_map;
  auto server_log_consumer_func = [&]() {
    std::string message;
    while(true) {
      int retc = server_log_consumer.Consume(message);
      if (retc != ks::ad_base::AdKafkaStatus::SUCCESS) {
        std::this_thread::sleep_for(std::chrono::milliseconds(SLEEP_TIME));
        continue;
      }
      kuaishou::ad::algorithm::AdJointLabeledLog ad_joint_labeled_log;
      if (!ad_joint_labeled_log.ParseFromArray(message.data(),message.size())) {
        continue;
      }
      if (ad_joint_labeled_log.item_size() <= 0) {
        continue;
      }
      auto llsid = ad_joint_labeled_log.llsid();
      for (size_t i = 0; i <  ad_joint_labeled_log.item_size(); ++i) {
        auto item  = ad_joint_labeled_log.mutable_item(i);;
        if (item->type() != ItemType::AD_DSP || !item->has_ad_dsp_info()) {
          continue;
        }
        auto account_id = item->ad_dsp_info().creative().base().account_id();
        concurrent_hash_map<uint64,kuaishou::ad::algorithm::CallBackType>::const_accessor ca;
        if (callback_type_map.find(ca,account_id)) {
          item -> mutable_ad_dsp_info() -> mutable_call_back_type() -> CopyFrom(ca->second);
        } else {
          std::string call_back_key = ACCOUNT_ACTION_TYPE_PREFIX +
                                    "#act#accountid#" + std::to_string(account_id);
          kuaishou::ad::algorithm::CallBackType call_back_type;
          std::string value = "-1";
          auto ret = picassoBGet(DSP_TABLE_NAME, call_back_key, value);
          if (ret == ks::ad_picasso::sdk::STATUS_OK && value != "-1") {
            call_back_type.ParseFromArray(value.data(), value.size());
            item -> mutable_ad_dsp_info() -> mutable_call_back_type() -> CopyFrom(call_back_type);
            concurrent_hash_map<uint64,kuaishou::ad::algorithm::CallBackType>::accessor a;
            callback_type_map.insert(a,account_id);
            a->second.CopyFrom(call_back_type);
            a.release();
          }
        }
        auto point = new kuaishou::ad::algorithm::AdJointLabeledLog(ad_joint_labeled_log);
        point->clear_item();
        point->add_item()->CopyFrom(*item);
        shuffle_buffer.push(point, dist_int(rng));
      }
    }
  };
  auto item_impression_consumer_func = [&]() {
    std::string message;
    while(true) {
      int retc = item_impression_consumer.Consume(message);
      if (retc != ks::ad_base::AdKafkaStatus::SUCCESS) {
        std::this_thread::sleep_for(std::chrono::milliseconds(SLEEP_TIME));
        continue;
      }
      kuaishou::ad::algorithm::AdJointLabeledLog ad_joint_labeled_log;
      if (!ad_joint_labeled_log.ParseFromArray(message.data(),message.size())) {
        continue;
      }
      if (ad_joint_labeled_log.item_size() <= 0) {
        continue;
      }
      auto llsid = ad_joint_labeled_log.llsid();
      auto item  = ad_joint_labeled_log.mutable_item(0);;
      if (item->type() != ItemType::AD_DSP || !item->has_ad_dsp_info()) {
        continue;
      }
      auto account_id = item->ad_dsp_info().creative().base().account_id();
      
      concurrent_hash_map<uint64,kuaishou::ad::algorithm::CallBackType>::accessor a;
      callback_type_map.insert(a,account_id);
      a->second.CopyFrom(item -> mutable_ad_dsp_info() -> call_back_type());
      a.release();
      auto point = new kuaishou::ad::algorithm::AdJointLabeledLog(ad_joint_labeled_log);
      shuffle_buffer.push(point, dist_int(rng));
    }
  };
  std::thread *server_log_threads = new std::thread[FLAGS_num_server_log_threads];
  for (size_t i = 0; i < FLAGS_num_server_log_threads; ++i) {
    server_log_threads[i] = std::thread(server_log_consumer_func);
  }

  std::thread *impression_log_threads = new std::thread[FLAGS_num_impression_log_threads];
  for (size_t i = 0; i < FLAGS_num_impression_log_threads; ++i) {
    impression_log_threads[i] = std::thread(item_impression_consumer_func);
  }

  for(size_t i = 0; i < FLAGS_num_server_log_threads; ++i){
    server_log_threads[i].join();
  }
  for(size_t i = 0; i < FLAGS_num_impression_log_threads; ++i){
    impression_log_threads[i].join();
  }
  return 0;
}
