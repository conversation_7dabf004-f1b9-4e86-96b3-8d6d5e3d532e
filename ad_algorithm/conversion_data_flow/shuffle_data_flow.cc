#include "teams/ad/ad_base/src/container/concurrent_queue.h"
#include "teams/ad/ad_base/src/kafka/kafka_client.h"
#include "teams/ad/ad_base/src/common/common.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "base/common/gflags.h"
#include "base/common/base.h"
#include "base/common/logging.h"
#include "base/time/timestamp.h"
#include "base/strings/string_split.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_number_conversions.h"
#include "base/encoding/base64.h"
#include "base/encoding/line_escape.h"
#include "ks/serving_util/dynamic_config.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.pb.h"
#include "teams/ad/ad_base/src/clock_cache/clock_cache.h"

#include <iostream>
#include <fstream>
#include <string>
#include <random>

using namespace std;
using namespace kuaishou::ad::algorithm;
using namespace ks::ad_base;

const uint64 SLEEP_TIME = 10;  // in ms
const uint64 KAFKA_TIME_OUT = 10; // in ms



DEFINE_int32(num_threads, 1, "num_threads");
DEFINE_int32(shuffle_buffer_size, 20480, "shuffle_buffer_size");
DEFINE_int32(data_buffer_size, 30, "data_buffer_size");
DEFINE_int32(capacity, 2000000, "capacity");



class ShuffleBuffer {
 public:
  ShuffleBuffer() {
  }
  void init(ks::ad_base::AdKafkaProducer * producer) {
    size_ = FLAGS_shuffle_buffer_size;
    log_buffer_ = new AdJointLabeledLog *[size_];
    for (size_t i = 0; i < size_; ++i) {
      log_buffer_[i] = nullptr;
    }
    producer_ = producer;
  }
  void push(AdJointLabeledLog * ad_joint_labeled_log, int index) {
    auto log_to_put =  ad_joint_labeled_log;
    if (index >= 0 && index < size_) {
      mtx_.lock();
      auto temp = log_to_put;
      log_to_put = log_buffer_[index];
      log_buffer_[index] = temp;
      mtx_.unlock();
    } 
    if (log_to_put != nullptr) {
      std::string send_val;
      log_to_put->SerializeToString(&send_val);
      falcon::Inc("write_to_kafka_cnt");
      producer_->Produce(send_val);
      delete log_to_put;
    }
  }
  ~ShuffleBuffer() {
    for(size_t i = 0; i < size_; ++i) {
      std::string send_val;
      log_buffer_[i]->SerializeToString(&send_val);
      producer_->Produce(send_val);
      delete log_buffer_[i];
      log_buffer_[i] = nullptr;
    }
    delete [] log_buffer_;
    delete producer_;
  }
 private:
  AdJointLabeledLog ** log_buffer_;
  ks::ad_base::AdKafkaProducer * producer_;
  int size_;
  std::mutex mtx_;
};

ShuffleBuffer shuffle_buffer;
std::uniform_int_distribution<int> dist_int(0, FLAGS_shuffle_buffer_size);
std::uniform_int_distribution<int> dist_int_data(0, FLAGS_data_buffer_size);

class DataBuffer {
 public:
  DataBuffer() {
    rng_.seed(std::random_device()());
  }
  void push(AdJointLabeledLog * ad_joint_labeled_log) {
    auto log_to_put =  ad_joint_labeled_log;
    int index = dist_int_data(rng_);
    if (index >= 0 && index < FLAGS_data_buffer_size) {
      auto temp = log_to_put;
      log_to_put = log_buffer_[index];
      log_buffer_[index] = temp;
    } 
    if (log_to_put != nullptr) {
      shuffle_buffer.push(log_to_put, dist_int(rng_));
    }
  }
  void init() {
    log_buffer_ = new AdJointLabeledLog *[FLAGS_data_buffer_size];
    for (size_t i = 0; i < FLAGS_data_buffer_size; ++i) {
      log_buffer_[i] = nullptr;
    }
    index_ = 0;
  }
  ~DataBuffer() {
    for (size_t i = 0; i < FLAGS_data_buffer_size; ++i) {
      shuffle_buffer.push(log_buffer_[i], dist_int(rng_));
      log_buffer_[i] = nullptr;
    }
    delete [] log_buffer_;
  }
 private:
  AdJointLabeledLog ** log_buffer_;
  int index_;
  std::mt19937 rng_;
 
};

int main(int argc, char* argv[]) {
  base::InitApp(&argc, &argv, "creative 维度shuffle日志流");
  auto mq_env = ks::DynamicJsonConfig::GetConfig()->Get("dsp_log_parser_config");
  if (!mq_env) {
    return -1;
  }
  ks::ad_base::AdKafkaProducer * producer = new ks::ad_base::AdKafkaProducer();
  producer->Init("mq_producer_config_0");
  shuffle_buffer.init(producer);
  const std::string conversion_data_shuffle_group_id = mq_env->GetString("conversion_data_shuffler",
                                                                         "conversion_data_shuffler");
  ks::ad_base::AdKafkaConsumer conversion_data_consumer;
  int ret = conversion_data_consumer.Init("mq_consumer_config_0", conversion_data_shuffle_group_id);
  if (ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
    LOG(ERROR)<< "init example consumer failed. ";
    return 1;
  }
  ClockCache<uint64, DataBuffer> data_clock_cache(FLAGS_capacity);
  int num_threads = FLAGS_num_threads;
  auto shuffle_func = [&]() {
    std::string message;
    auto init_value = [&](DataBuffer* data_buffer) {
       data_buffer ->init();
    };
    auto update_value = [&](DataBuffer* data_buffer, AdJointLabeledLog * ad_joint_labeled_log) {
      data_buffer->push(ad_joint_labeled_log);
    };
    while(true) {
      int retc = conversion_data_consumer.Consume(message);
      if (retc != ks::ad_base::AdKafkaStatus::SUCCESS) {
        std::this_thread::sleep_for(std::chrono::milliseconds(SLEEP_TIME));
        continue;
      }
      kuaishou::ad::algorithm::AdJointLabeledLog ad_joint_labeled_log;
      if (!ad_joint_labeled_log.ParseFromArray(message.data(),message.size())) {
        continue;
      }
      if (ad_joint_labeled_log.item_size() <= 0) {
        continue;
      }
      auto llsid = ad_joint_labeled_log.llsid();
      for (size_t i = 0; i <  ad_joint_labeled_log.item_size(); ++i) {
        auto item  = ad_joint_labeled_log.mutable_item(i);;
        if (item->type() != ItemType::AD_DSP || !item->has_ad_dsp_info()) {
          continue;
        }
        auto point = new kuaishou::ad::algorithm::AdJointLabeledLog(ad_joint_labeled_log);
        point->clear_item();
        point->add_item()->CopyFrom(*item);
        auto creative_id = item->ad_dsp_info().creative().base().id();
        data_clock_cache.Put(creative_id, init_value,std::bind(update_value, std::placeholders::_1,point));
      }
    }
  };
  std::thread *consumer_threads = new std::thread[FLAGS_num_threads];
  for (size_t i = 0; i < FLAGS_num_threads; ++i) {
    consumer_threads[i] = std::thread(shuffle_func);
  }

  for(size_t i = 0; i < FLAGS_num_threads; ++i){
    consumer_threads[i].join();
  }
  return 0;
}
