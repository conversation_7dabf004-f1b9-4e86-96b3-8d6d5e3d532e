cc_binary(name = "conversion_data_flow",
          srcs = ["conversion_data_flow.cc"],
          cppflags = [
                       "-I third_party/protobuf_v3/src/",
                       "-D GOOGLE_LOGGING=1",
                      ],
          ldflags=[
                    "-D _GLIBCXX_USE_CXX11_ABI=0",
                    "-pthread -Lthird_party/prebuilt/lib",
                    "-L./build_tools/gcc-5.3/lib64",
                    "-Wl,--whole-archive -Wl,--no-whole-archive",
                    "-static-libstdc++ -static-libgcc -mavx -mavx2 -mfma",
                    "-Wl,-no-as-needed -Wl,-z,relro,-z,now -pass-exit-codes -Wl,--gc-sections",
                    "-ldl -lrt  -lm -pthread",
                    '-Wl,-rpath=/usr/lib/jvm/java-1.7.0-openjdk-1.7.0.131.x86_64/bin/../jre/lib/amd64/server',
                    '-Wl,-rpath=/usr/lib/jvm/java-1.7.0-openjdk-1.7.0.151.x86_64/bin/../jre/lib/amd64/server',
                    '-Wl,-rpath=/usr/lib/jvm/java-1.7.0-openjdk-1.7.0.181.x86_64/bin/../jre/lib/amd64/server',
                   ],
                  deps = [
                    "//base/strings/BUILD:strings",
                    "//base/hash_function/BUILD:hash_function",
                    "//base/encoding/BUILD:encoding",
                    "//teams/ad/ad_proto/kuaishou/BUILD:algo",
                    "//teams/ad/ad_proto/kuaishou/BUILD:ad",
                    "//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_fast_emit_flow_delay__proto",
                    "//ks/util/BUILD:util",
                    "//teams/ad/ad_base/src/kafka/BUILD:kafka",
                    "//infra/falcon_counter/BUILD:falcon_counter",
                    "//teams/ad/ad_proto/kuaishou/BUILD:picasso_proto",
                    "//teams/ad/picasso/BUILD:picasso_sdk",
                    "//third_party/zstd/BUILD:zstd",
                    "//third_party/cityhash/BUILD:cityhash",
                    '//teams/ad/ad_base/src/kess/BUILD:kess_client'
                  ],
             )

