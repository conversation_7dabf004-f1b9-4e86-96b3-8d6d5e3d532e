#include "teams/ad/ad_base/src/container/concurrent_queue.h"
#include "teams/ad/ad_base/src/kafka/kafka_client.h"
#include "teams/ad/ad_base/src/common/common.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "base/common/gflags.h"
#include "base/common/base.h"
#include "base/common/logging.h"
#include "base/time/timestamp.h"
#include "base/strings/string_split.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_number_conversions.h"
#include "base/encoding/base64.h"
#include "base/encoding/line_escape.h"
#include "ks/serving_util/dynamic_config.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/dw/ad_log_for_algo.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.pb.h"
#include "third_party/tbb/include/tbb/concurrent_priority_queue.h"
#include "teams/ad/ad_proto/kuaishou/ad/picasso/picasso_update.pb.h"
#include "teams/ad/picasso/sdk/picasso_client/picasso_client.h"
#include "teams/ad/ad_algorithm/log_preprocess/ad_model_kconf_util.h"
#include "teams/ad/ad_algorithm/deep_data_flow/ad_backend_label_match_kconf_util.h"
#include "teams/ad/ad_algorithm/deep_data_flow/city_hash_util.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/fast_emit_flow_delay.pb.h"
#include "serving_base/utility/system_util.h"
#include "third_party/zstd/lib/zstd.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"

#include <iostream>
#include <fstream>
#include <string>
#include <random>
using namespace std;
using namespace ks::ad_picasso;
using namespace ks::ad_algorithm;
using namespace kuaishou::ad::algorithm;
using namespace tbb::interface5;


const uint64 MIN_TIME_DELAY_THRESH = 1 * 60 * 1000;  // in ms
const uint64 MAX_TIME_AHEAD_THRESH = 1 * 60 * 1000;  // in ms
const uint64 SLEEP_TIME = 10;  // in ms
const uint64 KAFKA_TIME_OUT = 10; // in ms

const string DSP_TABLE_NAME = "adAlgoConversionJoin"; //信息日志表
const string DETAIL_TABLE_NAME = "adAlgoLabelJoin"; //快享日志表
const string TMP_TABLE_NAME = "adAlgoMatch"; //临时分流日志表
const string DSP_TABLE_NEW_NAME = "adDspImpressionFull";
const string DSP_TABLE_MIGRATE_TMP_NAME = "adDspImpressionFullNewTemp"; // 迁移临时表
const string DSP_TABLE_MIGRATE_NAME = "adDspImpressionFullNew"; // 迁移后的新表名
const string HDD_TABLE = "adAlgoMatchDSPHdd";// HDD table name

const string ACCOUNT_ACTION_TYPE_PREFIX= "account_action_type"; //账号转化类型
const string DSP_CLICK_LOG_PREFIX = "label_log_new_test"; //信息流前缀
const string DETAIL_IMPRESSION_PREFIX = "label_log_detail"; //快享前缀
const string IMPRESSION_LOG_PREFIX = "impression_log_all"; // 新的曝光日志流前缀
const string CONVERSION_NO_LOG_PREFIX = "conversion_no_log_type"; //转化日志
const string ITEM_CLICK_LOG_PREFIX = "item_click_log"; //转化日志

DEFINE_int32(num_threads_label,5,"num_thread_label"); //消费ad log full的线程数，写入优先队列
DEFINE_int32(num_threads_example,5,"num_thread_example"); //消费ad joint labeled log的线程数写入优先队列
DEFINE_int32(num_data_threads_label, 3, ""); // 处理label的线程数
DEFINE_int32(num_data_threads_example, 1, ""); // 处理ad joint labeled log的线程数
DEFINE_int32(buffer_size, 2048, "buffer_size"); // shuffle buffer size
DEFINE_int32(example_queue_size, 500000, ""); //ad joint labeled log队列的大小
DEFINE_int32(label_queue_size, 40000, ""); //label队列的大小
DEFINE_string(delivery_info, "EVENT_ECOM_DELIVERY,EVENT_ECOM_REFUSE_SIGN,EVENT_ECOM_SIGN",
              "delivery_info call_back");  // 回传物流信息
  /*
 * 1. 基本逻辑比较简单：把item impression的ad joint labeled log 写入picasso，同时写入kafka，转化数据来了，
 * 就读取set callback_event (注意这个把callback type和callback label换掉，主要是不再proto修改两份)，
 * 然后写入到kafka
 * 2.为了避免picasso容量的问题，ad joint labeled log 写入picasso的时候，先看快享和老流程是否写入，如果已经写入了
 * ，就不写了，为了达到这个效果，需要写入log的慢于快享和老流程，所以加了一个优先队列当缓存。
 * 3.老流程判断是否回传，是用了creative 维度，现在改成account维度
 * 4.新加了写tmp表逻辑，原由：adDspImpressionFull快满了 分流到adAlgoLabelJoin
 * 分流
 *  step1:adIsConversionDataWriteTempTable 打开，adIsConversionDataReadTempTable 打开，adConversionDataWriteTempTableRate 30，adConversionDataReadTempTableRate 30
 * 回滚
 *  step1:adIsConversionDataWriteTempTable 打开，adIsConversionDataWriteTempTable2 打开，adIsConversionDataReadTempTable 打开，adConversionDataWriteTempTableRate 30，adConversionDataReadTempTableRate 30
 *  step2:5天后
 *  step3:adIsConversionDataWriteTempTable2 关闭，adIsConversionDataReadTempTable 关闭，adIsConversionDataReadTempTable 关闭
 *
 *
 * ======================
 * 简化逻辑，只写picasso
 * ======================
 */

int main(int argc, char* argv[]) {
  base::InitApp(&argc, &argv, "转化日志流");
  
  // service name 增加 "ad_backend" 前缀，适配 Picasso 侧统一管理流量
  ks::kess::rpc::Perf::SetCaller(
      {"ad",
      "ad_backend_conversion_data_flow",
      "ad_backend_conversion_data_flow_service",
      serving_base::GetHostName(),
      "0"});

  auto picasso = ks::ad_picasso::sdk::PicassoClient::GetInstance();
  ks::ad_picasso::sdk::PicassoOption opt;
  opt.client_tag = "data_flow_full";  //配置tag参数,对于每一个服务是唯一的,限制访问特定的表
  opt.self_kess_server_name = "data_flow_full";
  opt.use_hash = true;
  if (!picasso->Init(opt)) {
    return -1;
  }
  hash<string> hash_fn;

  // 读piccasso
  auto picassoBGet = [&](const std::string& table, const std::string& key, std::string& value, int retry = 3) {
    auto ret = ks::ad_picasso::sdk::STATUS_OK;
    int l_retry  = retry;
    falcon::Inc(("picasso_get_"+table+"_num").c_str(), 1);
    do {
      ret = picasso->BGet(table, key, value);
    } while (--l_retry > 0 && ret != ks::ad_picasso::sdk::STATUS_OK);
    if(ret != ks::ad_picasso::sdk::STATUS_OK){
      falcon::Inc(("picasso_get_"+table+"_error_num").c_str(), 1);
      LOG(INFO)<<"miss picasso status : "<<ret<<" , table : "<<table<<" , key : "<<key;
    }
    return ret;
  };

  // 先读tmp piccasso，再读base
  auto picassoBGet_tmp = [&](const std::string& table, const std::string& tmp_table, const std::string& key, std::string& value, int retry = 3) {
    if(IsConversionDataReadTempTable()){
      falcon::Inc("in_read_tmp_table", 1);
      uint32 id = hash_fn(key) % 100;

      if(id < TempConversionHDDRate()){
        falcon::Inc("read_hdd_table", 1);
        auto ret = picassoBGet(HDD_TABLE, "tmp#"+key, value, retry);
        if(ret != ks::ad_picasso::sdk::STATUS_OK){
          falcon::Inc("read_hdd_table_err", 1);
        } else {
          falcon::Inc("read_hdd_table_success", 1);
        }
      }

      if(id < ConversionDataReadTempTableRate()){
        falcon::Inc("read_tmp_table", 1);
        // 属于分流流量
        // 先读tmp 再度base
        auto ret = picassoBGet(tmp_table, "tmp#"+key, value, retry);
        if(ret != ks::ad_picasso::sdk::STATUS_OK){
          falcon::Inc("read_tmp_table_err", 1);
          return picassoBGet(table, key, value, retry);
        }
        return ret;
      }
    }
    return picassoBGet(table, key, value, retry);
  };

  ks::ad_base::AdKafkaProducer picasso_producer;
  // picasso_producer.Init("mq_picasso_producer_config");
  picasso_producer.Init("ad_picasso_log_update_stream", "compression.type=lz4");
  //写picasso
  auto send2picasso = [&](const std::string& table, const std::string& key, const std::string& value,
          const int64_t& timeout, bool is_drop = true) {
    uint32 id = hash_fn(key) % 100;
    falcon::Inc("write_data_ssd", 1);
    if (is_drop && id < AdModelKconfUtil::adConversionDataFlowSSDDropRate()) {
      falcon::Inc("drop_data_ssd", 1);
      return false;
    }
    kuaishou::ad::picasso::UpdateReq update_req;
    update_req.set_cmd(kuaishou::ad::picasso::BSET);
    update_req.set_table(table);
    update_req.set_key(key);
    update_req.mutable_bset_req()->set_val(value);
    update_req.mutable_bset_req()->set_ttl(timeout);
    std::string send_val;
    update_req.SerializeToString(&send_val);
    falcon::Inc(("send2picasso_"+table+"_num").c_str(), 1);
    falcon::Inc(("send2picasso_"+table+"_timeout_"+std::to_string(timeout)).c_str(), 1);
    picasso_producer.Produce(send_val);
    return true;
  };

  ks::ad_base::AdKafkaProducer picasso_producer_hdd;
  // picasso_producer_hdd.Init("mq_picasso_producer_config_hdd");
  picasso_producer_hdd.Init("ad_picasso_log_update_hdd_stream", "compression.type=lz4");
  //写picasso
  auto send2picasso_hdd = [&](const std::string& table, const std::string& key, const std::string& value,
          const int64_t& timeout, bool is_drop = true) {
    uint32 id = hash_fn(key) % 100;
    falcon::Inc("write_data_hdd", 1);
    if (is_drop && id < AdModelKconfUtil::adConversionDataFlowHDDDropRate()) {
      falcon::Inc("drop_data_hdd", 1);
      return false;
    }
    kuaishou::ad::picasso::UpdateReq update_req;
    update_req.set_cmd(kuaishou::ad::picasso::BSET);
    update_req.set_table(table);
    update_req.set_key(key);
    update_req.mutable_bset_req()->set_val(value);
    update_req.mutable_bset_req()->set_ttl(timeout);
    std::string send_val;
    update_req.SerializeToString(&send_val);
    falcon::Inc(("send2picasso_hdd_"+table+"_num").c_str(), 1);
    falcon::Inc(("send2picasso_hdd_"+table+"_timeout_"+std::to_string(timeout)).c_str(), 1);
    picasso_producer_hdd.Produce(send_val);
    return true;
  };
  //写两个表
  auto send2picasso_tmp = [&](const std::string& table, const std::string& tmp_table , const std::string& key, std::string& value,
          const int64_t& timeout, string &table_name) {
    falcon::Stat("value_size", value.size());
    if(IsConversionDataWriteTempTable()){
      falcon::Inc("in_write_tmp_table", 1);
      // uint32 id = hash_fn(key) % 100;
      // hash 逻辑从 std::hash 切换为 third_party/cityhash
      uint32 id = 0;
      if (AdBackendLabelMatchKconfUtil::adBackendImpPicassoHashUnifySwitchForWrite()) {
        id = CityHash64(key.c_str(), key.size()) % 100;
        LOG_EVERY_N(INFO, 10000) << "[Debug hash] key:" << key << ", hash result of CityHash:" << CityHash64(key.c_str(), key.size()) << ", mod result:" << id;
        ks::infra::PerfUtil::CountLogStash(1, "ad.conversion_data_flow", "hash_modify", "new");
      } else {
        id = hash_fn(key) % 100;
        LOG_EVERY_N(INFO, 10000) << "[Debug hash] key:" << key << ", hash result of std hash:" << hash_fn(key) << ", mod result:" << id;
        ks::infra::PerfUtil::CountLogStash(1, "ad.conversion_data_flow", "hash_modify", "old");
      }

      if(id < AdModelKconfUtil::adConversionDataFlowWriteZSTDRate()){
        falcon::Inc("write_picasso_compress", 1);
        size_t dstCapacity = ZSTD_compressBound(value.size()) * 1.2;
        char* dst = new char[dstCapacity];
        auto dstSize = ZSTD_compress(dst, dstCapacity,  value.c_str(), value.size(), 1);
        if (!ZSTD_isError(dstSize)) {
          value.assign(dst, dstSize);
          falcon::Inc("write_picasso_compress_success", 1);
          falcon::Stat("value_compress_size", value.size());
        } else {
          falcon::Inc("write_picasso_compress_error", 1);
        }
        delete dst;
      }

      if(id < TempConversionHDDRate()){
        falcon::Inc("write_hdd_table", 1);
        // 属于分流流量
        auto ret = send2picasso_hdd(HDD_TABLE, "tmp#"+key, value, ConversionDataPicassHDDTimeOut());
        table_name = table_name + HDD_TABLE + ";";
      }

      if(id < ConversionDataWriteTempTableRate()){
        falcon::Inc("write_tmp_table", 1);
        // 属于分流流量
        auto ret = send2picasso(tmp_table, "tmp#"+key, value, timeout);
        table_name = table_name + tmp_table + ";";
        if(IsConversionDataWriteTempTable2()){
          falcon::Inc("write_all_table", 1);
          ret = send2picasso(table, key, value, timeout);
          table_name = table_name + table + ";";
        }
        return ret;
      }
    }
    auto ret = send2picasso(table, key, value, timeout);
    table_name = table_name + table + ";";
    return ret;
  };

  auto mq_env = ks::DynamicJsonConfig::GetConfig()->Get("dsp_log_parser_config");
  if (!mq_env) {
    return -1;
  }
  std::string type = FLAGS_delivery_info;
  std::vector<string> type_delivery;
  std::unordered_set<int> delivery_types;
  base::SplitString(type, std::string(",", 1), &type_delivery);
  kuaishou::ad::AdCallbackLog::EventType type_ = kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN;
  for (const auto & str : type_delivery) {
    if (kuaishou::ad::AdCallbackLog::EventType_Parse(str, &type_)) {
      delivery_types.insert(type_);
    } else {
      LOG(ERROR) << "Parse sample type: " << str << " failed.";
    }
  }


  std::mt19937 rng;
  rng.seed(std::random_device()());
  const std::string example_group_id = mq_env->GetString("example_group_id","dsp_log_full_parser");
  const std::string example_consumer_user_param = "auto.offset.reset=latest;fetch.error.backoff.ms=0;linger.ms=100;kuaishou.set.offset.ms.ago=1";
  auto example_consumer_config = ks::DynamicJsonConfig::GetConfig()->Get("mq_consumer_config_0");
  if (!example_consumer_config) {
    LOG(FATAL) << "gete mq_consumer_config_0 error";
  }
  const std::string example_topic = example_consumer_config->GetString("topic");
  if (example_topic.size() == 0) {
    LOG(FATAL) << "cannot get mq_consumer_config_0.topic";
  }


  ks::ad_base::AdKafkaConsumer example_consumers;
  int ret = example_consumers.InitConsumer(example_topic, example_group_id, example_consumer_user_param);
  if (ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
    LOG(ERROR)<< "init example consumer failed. ";
    return 1;
  }

  const std::string label_group_id = mq_env->GetString("label_group_id","dsp_log_full_parser");
  const std::string label_consumer_user_param = "auto.offset.reset=latest;fetch.error.backoff.ms=0;linger.ms=100;kuaishou.set.offset.ms.ago=1";
  auto label_consumer_config = ks::DynamicJsonConfig::GetConfig()->Get("mq_consumer_config_1");
  if (!label_consumer_config) {
    LOG(FATAL) << "gete mq_consumer_config_1 error";
  }
  const std::string label_topic = label_consumer_config->GetString("topic");
  if (label_topic.size() == 0) {
    LOG(FATAL) << "cannot get mq_consumer_config_1.topic";
  }
  ks::ad_base::AdKafkaConsumer label_consumer;
  ret = label_consumer.InitConsumer(label_topic, label_group_id, label_consumer_user_param);
  if (ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
    LOG(ERROR)<< "init label consumer failed. id.";
    return 1;
  }

  const std::string user_params = "compression.codec=lz4";
  auto json = ks::DynamicJsonConfig::GetConfig()->Get("mq_producer_config_0");
  if (nullptr == json) {
    LOG(ERROR) << "invalid json config key. key == mq_producer_config_0";
    return -1;
  }
  std::string producer_topic = json->GetString("topic", "");
  ks::ad_base::AdKafkaProducer producer;
  producer.Init(producer_topic, user_params);

  // 打点
  // producer ad_fast_emit_trace_log
  // ks::ad_base::AdKafkaProducer producer_tracelog;
  // const string producer_user_param3 = "compression.codec=lz4";
  // string producer_topic3 = "ad_fast_emit_trace_log";
  // ret = producer_tracelog.Init(producer_topic3, producer_user_param3);
  // if (ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
  //   LOG(FATAL) << "init ad_fast_emit_trace_log producer failed. ";
  //   return -1;
  // }

  // create log
  // auto createDelayModelTrainDataTraceLog = [&](string &debug_info_base64,
  //     AdJointLabeledLog &ad_joint_labeled_log,
  //     string &table) {
  //   string debug_info;
  //   if (ad_joint_labeled_log.item_size() < 1) {
  //     return;
  //   }
  //   auto llsid = ad_joint_labeled_log.llsid();
  //   auto creative_id = ad_joint_labeled_log.item(0).ad_dsp_info().creative().base().id();
  //   auto unit_id = ad_joint_labeled_log.item(0).ad_dsp_info().unit().base().id();
  //   auto account_id = ad_joint_labeled_log.item(0).ad_dsp_info().campaign().base().account_id();
  //   auto ocpc_action_type = ad_joint_labeled_log.item(0).ad_dsp_info().unit().base().ocpc_action_type();

  //   kuaishou::ad::algorithm::FastEmitWritePicassoTraceLog fast_emit_write_picasso_trace_log;
  //   fast_emit_write_picasso_trace_log.set_time(ad_joint_labeled_log.time());
  //   fast_emit_write_picasso_trace_log.set_llsid(llsid);
  //   fast_emit_write_picasso_trace_log.set_creative_id(creative_id);
  //   fast_emit_write_picasso_trace_log.set_unit_id(unit_id);
  //   fast_emit_write_picasso_trace_log.set_account_id(account_id);
  //   fast_emit_write_picasso_trace_log.set_ocpc_action_type((kuaishou::ad::AdActionType)ocpc_action_type);
  //   fast_emit_write_picasso_trace_log.set_table(table);
  //   fast_emit_write_picasso_trace_log.SerializeToString(&debug_info);
  //   base::Base64Encode(debug_info, &debug_info_base64);
  //   return;
  // };
  // write log
  // auto produceTraceLog = [&](kuaishou::ad::algorithm::FastEmitTraceLogType log_type,
  //                      string &debug_info_base64) {
  //   string trace_log;
  //   string trace_log_base64;
  //   kuaishou::ad::algorithm::FastEmitTraceLog fast_emit_trace_log;
  //   fast_emit_trace_log.set_time(base::GetTimestamp() / 1000000);
  //   fast_emit_trace_log.set_log_type(log_type);
  //   fast_emit_trace_log.set_log(debug_info_base64);
  //   fast_emit_trace_log.SerializeToString(&trace_log);
  //   base::Base64Encode(trace_log, &trace_log_base64);
  //   producer_tracelog.Produce(trace_log_base64);
  //   //LOG_EVERY_N(ERROR, 1) << trace_log_base64;
  //   return;
  // };
  // 打点结束

  //读item impression的ad joint labeled log match数据写入队列
  auto example_data_func = [&]() {
    std::string message;
    while(true) {
      int retc = example_consumers.Consume(message);
      if (retc != ks::ad_base::AdKafkaStatus::SUCCESS) {
        std::this_thread::sleep_for(std::chrono::milliseconds(SLEEP_TIME));
        continue;
      }
      if(!IsConversionDataFlowConsumerExpData()){
        continue;
      }
      //============================这段就是为了检验日志的完整性===========================
      kuaishou::ad::algorithm::AdJointLabeledLog ad_joint_labeled_log;
      if (!ad_joint_labeled_log.ParseFromArray(message.data(),message.size())) {
        LOG(ERROR)<<"parse ad_joint_labeled_log error!!!!";
        falcon::Inc("parser_call_back_type_error");
        continue;
      }
      if (ad_joint_labeled_log.item_size() <= 0) {
        LOG(ERROR)<<"ad_joint_labeled_log has no item !!!! llsid : " << ad_joint_labeled_log.llsid();
        falcon::Inc("ad_joint_labeled_log_no_item");
        continue;
      }
      auto llsid = ad_joint_labeled_log.llsid();
      auto item = ad_joint_labeled_log.mutable_item(0);
      if (!item->has_ad_dsp_info()) {
        LOG(ERROR)<<"item type error or has no dsp info!";
        falcon::Inc("item_type_dsp_info_error");
        continue;
      }
      int hash_code = llsid % 100;
      if (hash_code < ConversionDataFlowDropExpDataRate()){
        falcon::Inc("filter_by_ConversionDataFlowDropExpDataRate");
        continue;
      } /*else {
        falcon::Inc(("ConversionDataFlowDropExpDataRate_"+std::to_string(hash_code)+"/"+std::to_string(ConversionDataFlowDropExpDataRate())).c_str());
      }*/
      // ===================================================================================

      //if (item->label_info().item_click()) {
      //  continue;
      //}
      //item->mutable_label_info()->set_item_impression(true);
      //if (!item->label_info().item_impression()) {
      //  LOG(ERROR)<<"not item impression!!!";
      //  falcon::Inc("item_impression_label_error");
       // continue;
      //}
      
      // ================= 日志不能太超前 =====================
      uint64 now_time =  base::GetTimestamp() / 1000;
      if (ad_joint_labeled_log.time() > now_time &&
         (ad_joint_labeled_log.time() - now_time) > MAX_TIME_AHEAD_THRESH) {
        falcon::Inc("example_ahead_drop");
        continue;//no need
      }//避免异常log，导致无限等待
      if (ad_joint_labeled_log.item_size() < 1) {
        LOG(ERROR)<<"ad_joint_labeled_log has no item !!!! llsid : " << ad_joint_labeled_log.llsid();
        falcon::Inc("ad_joint_labeled_log_no_item");
        continue;
      }

      // kconf配置
      int64 default_timeout = ConversionDataPicassTimeOut();
      int64 itemclick_timeout = ConversionDataItemClickPicassTimeOut();
      int64 dianshang_timeout = ConversionDataDianshangPicassTimeOut();
      int64 kuaixiang_timeout = ConversionDataKuaixiangPicassTimeOut();

      uint64 creative_id = item->ad_dsp_info().creative().base().id();
      // key: 上传过行为类型的cid (无上传记录的样本丢弃）
      // train_key: 缓存一段时间的样本，供转换数据label_match使用，优先llsid
      std::string key = "" ;
      std::string value = "-1";
      std::string table = "";
      bool is_dianshang = false;
      //===========================设置回传类型=======================================
      auto account_id = item->ad_dsp_info().creative().base().account_id();
      std::string call_back_key = ACCOUNT_ACTION_TYPE_PREFIX + "#act#accountid#" + std::to_string(account_id);
      kuaishou::ad::algorithm::CallBackType call_back_type;
      // 切换picasso表
      //auto ret = picassoBGet(DSP_TABLE_NEW_NAME, call_back_key, value);
      auto ret = picassoBGet(TMP_TABLE_NAME, call_back_key, value);
      if (ret == ks::ad_picasso::sdk::STATUS_OK && value != "-1") {
        if (!call_back_type.ParseFromArray(value.data(), value.size())) {
          falcon::Inc("parser_call_back_type_error");
          LOG(ERROR) << "parse call_back_type error !!!!";
        } else {
          for (auto action_type : call_back_type.callback_event()) {
            if (delivery_types.find(action_type) != delivery_types.end()) {
              is_dianshang = true;
              break;
            }
          }
        }
      }

      // 只保留userInfo中的部分内容，在深度labelmatch中和从rodis里读取的userInfo做merge。
      ::kuaishou::ad::algorithm::UserInfo new_userInfo;
      auto& userInfo = *ad_joint_labeled_log.mutable_user_info();
      auto user_id = userInfo.id();
      auto build_time = userInfo.build_time();
      auto ad_user_info = userInfo.mutable_ad_user_info();
      auto device_info = userInfo.mutable_device_info();
      auto user_real_time_action = userInfo.mutable_user_real_time_action();
      auto fanstop_action_list_with_tag = userInfo.mutable_fanstop_action_list_with_tag();
      
      // 用户实时行为特征列表
      auto attr_white_list = AdBackendLabelMatchKconfUtil::userRealtimeActionNameValue();
      int attr_count = ad_joint_labeled_log.user_info().common_info_attr_size();
      for (int i = 0; i< attr_count; i++) {
        auto& attr = ad_joint_labeled_log.user_info().common_info_attr(i);
        // 根据white list过滤出需要保留的attr
        if (attr_white_list->find(attr.name_value()) != attr_white_list->end()) {
          new_userInfo.add_common_info_attr()->CopyFrom(attr);
        }
      }
      
      new_userInfo.set_id(user_id);
      new_userInfo.set_build_time(build_time);
      new_userInfo.mutable_ad_user_info()->Swap(ad_user_info);
      new_userInfo.mutable_device_info()->Swap(device_info);
      new_userInfo.mutable_user_real_time_action()->Swap(user_real_time_action);
      new_userInfo.mutable_fanstop_action_list_with_tag()->Swap(fanstop_action_list_with_tag);
      // 用裁剪之后的userInfo替换adLog中的userInfo
      ad_joint_labeled_log.mutable_user_info()->Swap(&new_userInfo);

      // 经过上面的swap，此时new_userInfo中的值是原来的老userInfo
      LOG_EVERY_N(INFO, 10000) << "change adLog userInfo, newUserInfo: " 
      << ad_joint_labeled_log.user_info().id() << ","<< ad_joint_labeled_log.user_info().build_time() << "," << ad_joint_labeled_log.user_info().ad_user_info().id()<< "," 
      << ad_joint_labeled_log.user_info().device_info().id() << ","
      << ad_joint_labeled_log.user_info().common_info_attr_size() << "," << ad_joint_labeled_log.user_info().user_real_time_action().build_time()
      << "," << ad_joint_labeled_log.user_info().level()
      << ", change adLog userInfo, oldUserInfo: " << new_userInfo.id() <<","<< new_userInfo.build_time() << "," << new_userInfo.ad_user_info().id()<< "," 
      << new_userInfo.device_info().id() << ","
      << new_userInfo.common_info_attr_size() << ","<< new_userInfo.user_real_time_action().build_time() 
      << "," << new_userInfo.level();
      

      bool exist = false;
      string debug_info_base64 = "";
      string write_table = "";
      // if (!item->label_info().item_click()) {
        if (is_dianshang) {  // 回传过物流信息的 item 全都写到新的 picasso 里，并且过期时间设为 7 天
          key = IMPRESSION_LOG_PREFIX + "#" + std::to_string(llsid) + "#"+ std::to_string(creative_id);
          falcon::Inc("ad_dsp_log_full_write_picasso");
          ad_joint_labeled_log.SerializeToString(&value);
          // 切换 picasso 表
          //send2picasso_tmp(DSP_TABLE_NEW_NAME, TMP_TABLE_NAME, key, value, dianshang_timeout, write_table);
          bool write_succeed = send2picasso_tmp(DSP_TABLE_MIGRATE_NAME, TMP_TABLE_NAME,
              key, value, dianshang_timeout, write_table);
          // 增加打点统计负样本数据量
          ks::infra::PerfUtil::CountLogStash(1, "ad_backend_data_flow", "impression2picasso", std::to_string(write_succeed));
          // createDelayModelTrainDataTraceLog(debug_info_base64, ad_joint_labeled_log, write_table);
          // produceTraceLog(kuaishou::ad::algorithm::FastEmitTraceLogType::WRITE_PICASSO, debug_info_base64);
          exist = true;
        }
        if (!exist) {  //  写入 picasso
          key = IMPRESSION_LOG_PREFIX + "#" + std::to_string(llsid) + "#"+ std::to_string(creative_id);
          falcon::Inc("ad_dsp_log_full_write_picasso");
          ad_joint_labeled_log.SerializeToString(&value);
          bool write_succeed = false;
          if (ad_joint_labeled_log.context().page_id() == 10001){
            falcon::Inc("ad_dsp_log_full_write_kuaixiang_cnt");
            // 切换 picasso 表
            //send2picasso_tmp(DSP_TABLE_NEW_NAME, TMP_TABLE_NAME, key, value, kuaixiang_timeout, write_table);
            write_succeed = send2picasso_tmp(DSP_TABLE_MIGRATE_NAME, TMP_TABLE_NAME,
                key, value, kuaixiang_timeout, write_table);
            // createDelayModelTrainDataTraceLog(debug_info_base64, ad_joint_labeled_log, write_table);
            // produceTraceLog(kuaishou::ad::algorithm::FastEmitTraceLogType::WRITE_PICASSO, debug_info_base64);
          } else {
            falcon::Inc("ad_dsp_log_full_write_defalut_cnt");
            // 切换 picasso 表
            //send2picasso_tmp(DSP_TABLE_NEW_NAME, TMP_TABLE_NAME, key, value, default_timeout, write_table);
            write_succeed = send2picasso_tmp(DSP_TABLE_MIGRATE_NAME, TMP_TABLE_NAME,
                key, value, default_timeout, write_table);
            // createDelayModelTrainDataTraceLog(debug_info_base64, ad_joint_labeled_log, write_table);
            // produceTraceLog(kuaishou::ad::algorithm::FastEmitTraceLogType::WRITE_PICASSO, debug_info_base64);
          }
          // 增加打点统计负样本数据量
          ks::infra::PerfUtil::CountLogStash(1, "ad_backend_data_flow", "impression2picasso", std::to_string(write_succeed));
        }
      // } else { //二跳日志单独处理
      //   key = ITEM_CLICK_LOG_PREFIX + "#" + std::to_string(llsid) + "#"+ std::to_string(creative_id);
      //   falcon::Inc("ad_item_click_log_write_picasso");
      //   ad_joint_labeled_log.SerializeToString(&value);
      //   if (is_dianshang) {
      //     // 切换 picasso 表
      //     //send2picasso_tmp(DSP_TABLE_NEW_NAME, TMP_TABLE_NAME, key, value, dianshang_timeout, write_table);
      //     send2picasso_tmp(DSP_TABLE_MIGRATE_NAME, TMP_TABLE_NAME,
      //         key, value, dianshang_timeout, write_table);
      //   } else {
      //     // 切换 picasso 表
      //     //send2picasso_tmp(DSP_TABLE_NEW_NAME, TMP_TABLE_NAME, key, value, itemclick_timeout, write_table);
      //     send2picasso_tmp(DSP_TABLE_MIGRATE_NAME, TMP_TABLE_NAME,
      //         key, value, itemclick_timeout, write_table);
      //   }
      // }

      /*
      //写入kafka
      falcon::Inc("item_impression_cnt");
      for (auto c : call_back_type.callback_event()) {
        ad_joint_labeled_log.mutable_item(0)->mutable_label_info()->set_callback_event((kuaishou::ad::AdCallbackLog_EventType)c);

        string send_val;
        ad_joint_labeled_log.SerializeToString(&send_val);
        producer.Produce(send_val);
      }


      //写入队列
      while (example_queue.size() >= example_queue_size) {
        falcon::Inc("example_queue_filled");
        std::this_thread::sleep_for(std::chrono::milliseconds(SLEEP_TIME));
      }
      try {
        auto point = new kuaishou::ad::algorithm::AdJointLabeledLog(ad_joint_labeled_log);
        example_queue.push(point);
        falcon::Inc("example_push_cnt");
      } catch (...) {
        LOG(ERROR) << "example queue push error";
        falcon::Inc("example_queue_exception");
        std::this_thread::sleep_for(std::chrono::milliseconds(SLEEP_TIME));
      }*/
    }
  };
  // label数据进入队列
  auto label_data_func = [&]() {
    std::string message;
    while (true) {
      int ret = label_consumer.Consume(message);
      if(ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
        continue;
      }
      if(!IsConversionDataFlowConsumerCallbackData()){
        continue;
      }
      kuaishou::ad::dw::AdLogForAlgo ad_log_for_algo;
      if (!ad_log_for_algo.ParseFromArray(message.data(), message.size())) {
        LOG(ERROR) << "Failed to parse sample info. len=" << message.size();
        continue;
      }
      if (ad_log_for_algo.e_ad_item_impression() > 0 && ad_log_for_algo.source_type() == "DSP") {
          falcon::Inc("ad_log_for_algo_item_impression");
      }
      //去重
      if (!ad_log_for_algo.is_from_callback_api() && ad_log_for_algo.e_event_form_submit() == 0) {
        continue;
      }
      if ( ad_log_for_algo.e_event_form_submit() > 0 ) {
        ad_log_for_algo.set_action_type("EVENT_FORM_SUBMIT");
      }
      auto llsid = ad_log_for_algo.llsid();
      auto creative_id = ad_log_for_algo.creative_id();
      auto account_id = ad_log_for_algo.account_id();
      auto action_type_str = ad_log_for_algo.action_type();

      // action_type & derive_event_type_list 一并遍历处理
      std::vector<string> action_and_derived_event_types;
      action_and_derived_event_types.push_back(action_type_str);
      if (ad_log_for_algo.derive_event_type_list_size() > 0) {
        for (const auto &type : ad_log_for_algo.derive_event_type_list()) {
          action_and_derived_event_types.push_back(type);
        }
      }
      // 解析当前已有的所有回传类型（从原位置提到循环外执行）
      std::string call_back_key = ACCOUNT_ACTION_TYPE_PREFIX + "#act#accountid#" + std::to_string(account_id);
      std::string value = "-1";
      kuaishou::ad::algorithm::CallBackType call_back_type;
      // 切换picasso表
      //ret = picassoBGet(DSP_TABLE_NEW_NAME, call_back_key, value);
      ret = picassoBGet(TMP_TABLE_NAME, call_back_key, value);
      if (ret == ks::ad_picasso::sdk::STATUS_OK && value != "-1") {
        if (!call_back_type.ParseFromArray(value.data(), value.size())) {
          LOG(ERROR)<<"label_data_func:parse call_back_type error!";
          falcon::Inc("label_data_func_parser_call_back_type_error");
        }
      }
      if (ret != ks::ad_picasso::sdk::STATUS_OK && ret != ks::ad_picasso::sdk::STATUS_NOT_FOUND) {
        falcon::Inc("ad_callbacktype_get_failed", 1);
        // Bug 修复：如果查询 Picasso 异常但不处理，会导致当天行为冲掉该账户的所有历史回传记录
        ks::infra::PerfUtil::CountLogStash(1, "ad.conversion_data_flow", "get_callback_fail", absl::StrCat(ret));
        LOG_EVERY_N(INFO, 100) << "Get callback types from picasso failed, account_id: "
            << account_id << ", llsid: " << llsid << ", creative_id: " << creative_id;
        continue;
      }
      ks::infra::PerfUtil::CountLogStash(1, "ad.conversion_data_flow", "get_callback_ok", absl::StrCat(ret));
      // 遍历处理每个 action_type/derived_event_type
      for (const auto &event_type_str : action_and_derived_event_types) {
        //=============================设置account的回传类型========================================
        kuaishou::ad::AdCallbackLog::EventType  action_type = kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN;
        kuaishou::ad::AdCallbackLog::EventType_Parse(event_type_str, &action_type);
        if (action_type == kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN) {
          LOG(ERROR) << "parse "<<event_type_str<<" error! maybe proto is old!";
          continue;
        }
        falcon::Inc(("action_type_" + event_type_str).c_str());
        /*
        std::string call_back_key = ACCOUNT_ACTION_TYPE_PREFIX + "#act#accountid#" + std::to_string(account_id);
        std::string value = "-1";
        kuaishou::ad::algorithm::CallBackType call_back_type;
        // 切换picasso表
        //ret = picassoBGet(DSP_TABLE_NEW_NAME, call_back_key, value);
        ret = picassoBGet(TMP_TABLE_NAME, call_back_key, value);
        if (ret == ks::ad_picasso::sdk::STATUS_OK && value != "-1") {
          if (!call_back_type.ParseFromArray(value.data(), value.size())) {
            LOG(ERROR)<<"label_data_func:parse call_back_type error!";
            falcon::Inc("label_data_func_parser_call_back_type_error");
          }
        } else {
          falcon::Inc("ad_callbacktype_get_failed", 1);
        }
        */
        bool exist_flag = false;
        for (int i = 0; i < call_back_type.callback_event_size(); i++) {
          if (action_type == call_back_type.callback_event(i)) {
            exist_flag = true;
            break;
          }
        }
        if (!exist_flag) {
          call_back_type.add_callback_event(action_type);
          call_back_type.SerializeToString(&value);
          // account的callback时间设置成90天
          int64_t d_timeout = 60 * 60 * 24 * 90;
          falcon::Inc("ada_callbacktype", 1);
          // 切换Picasso表
          //send2picasso(DSP_TABLE_NEW_NAME, call_back_key, value, d_timeout, false);
          send2picasso(TMP_TABLE_NAME, call_back_key, value, d_timeout, false);
        }
      }
      
      //==========================================================================

      /*
      // kconf配置
      int64 default_timeout = ConversionDataPicassTimeOut();
      int64 itemclick_timeout = ConversionDataItemClickPicassTimeOut();
      int64 dianshang_timeout = ConversionDataDianshangPicassTimeOut();
      int64 kuaixiang_timeout = ConversionDataKuaixiangPicassTimeOut();

      auto llsid_str = std::to_string(ad_log_for_algo->llsid());
      auto creative_id = ad_log_for_algo->creative_id();
      auto account_id = ad_log_for_algo->account_id();
      auto action_type_str = ad_log_for_algo->action_type();
      kuaishou::ad::AdCallbackLog::EventType action_type = kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN;
      kuaishou::ad::AdCallbackLog::EventType_Parse(action_type_str, &action_type);
      uint64 now_time = base::GetTimestamp() /1000;
      uint64 log_process_timestamp = ad_log_for_algo->event_server_timestamp();
      uint64 purchase_amount = ad_log_for_algo->callback_purchase_amount();

      //LOG_EVERY_N(INFO,10)<<"now_time: "<<now_time<<"log_process_timestamp: "<<log_process_timestamp;
      // 超前的日志认为是无效的，防止无限等待
      if (log_process_timestamp > now_time
          && log_process_timestamp - now_time > MAX_TIME_AHEAD_THRESH) {
        falcon::Inc("label_ahead_drop");
        continue;
      }
      bool exist = false;
      string write_table = "";
      std::string key;
      std::string value;
      std::reverse(llsid_str.begin(), llsid_str.end());

      // 从新的picasso中读
      if (!exist) {
        key = IMPRESSION_LOG_PREFIX + "#" + std::to_string(ad_log_for_algo->llsid()) + "#"+ std::to_string(creative_id);
        auto ret = picassoBGet_tmp(DSP_TABLE_NEW_NAME, TMP_TABLE_NAME, key, value, 5);
        falcon::Inc("ad_impression_label_read_cnt", 1);
        if (ret == ks::ad_picasso::sdk::STATUS_OK) {
          falcon::Inc("ad_impression_label_match_cnt", 1);
          exist = true;
        }
      }

      // 读到，修改call_back_event
      if (exist) {
        AdJointLabeledLog * ad_joint_labeled_log =  new AdJointLabeledLog();
        ad_joint_labeled_log->ParseFromArray(value.data(),value.size());
        if (ad_joint_labeled_log->item_size() < 1) {
          LOG(ERROR)<<"ad_joint_labeled_log has no item !!!! llsid : " << ad_joint_labeled_log->llsid();
          falcon::Inc("ad_joint_labeled_log_no_item");
          delete ad_joint_labeled_log;
        } else {
          auto item = ad_joint_labeled_log->mutable_item(0);
          auto account_id = item->ad_dsp_info().creative().base().account_id();
          std::string call_back_key = ACCOUNT_ACTION_TYPE_PREFIX +
                                      "#act#accountid#" + std::to_string(account_id);
          kuaishou::ad::algorithm::CallBackType call_back_type;
          auto ret = picassoBGet(DSP_TABLE_NEW_NAME, call_back_key, value);
          if (ret == ks::ad_picasso::sdk::STATUS_OK && value != "-1") {
            if (!call_back_type.ParseFromArray(value.data(), value.size())) {  // 解析失败处理
              falcon::Inc("parser_call_back_type_error");
              LOG(ERROR) << "parse call_back_type error !!!!";
            }
          }
          item->mutable_label_info()->set_callback_event(action_type);
          item->mutable_label_info()->set_purchase_amount(purchase_amount);
          item -> mutable_ad_dsp_info() -> mutable_call_back_type() -> CopyFrom(call_back_type);
          item->set_delayed_time(log_process_timestamp - ad_joint_labeled_log->time());
          falcon::Inc(("match_cnt_" + action_type_str).c_str());
          if (ad_joint_labeled_log->context().page_id() == 10001){
            falcon::Inc(("match_kuaixiang_cnt_" + action_type_str).c_str());
          }
          // 从老的picasso 表读出的数据未必item impression 为true
          item -> mutable_label_info() -> set_item_impression(true);
          producer.Produce(ad_joint_labeled_log);
          key = ITEM_CLICK_LOG_PREFIX + "#" + std::to_string(ad_log_for_algo->llsid()) + "#"+ std::to_string(creative_id);
          ret = picassoBGet_tmp(DSP_TABLE_NEW_NAME, TMP_TABLE_NAME, key, value, 5);
          if (ret == ks::ad_picasso::sdk::STATUS_OK) {
            falcon::Inc("ad_item_click_label_match_cnt", 1);
            AdJointLabeledLog * ad_joint_labeled_log_click =  new AdJointLabeledLog();
            ad_joint_labeled_log_click->ParseFromArray(value.data(),value.size());
            if (ad_joint_labeled_log_click->item_size() < 1) { 
              LOG(ERROR)<<"ad_joint_labeled_log has no item !!!! llsid : " << ad_joint_labeled_log->llsid();
              falcon::Inc("ad_joint_labeled_log_no_item");
              delete ad_joint_labeled_log_click;
            } else {
              item = ad_joint_labeled_log_click->mutable_item(0);
              item->mutable_label_info()->set_callback_event(action_type);
              item->mutable_label_info()->set_purchase_amount(purchase_amount);
              item -> mutable_ad_dsp_info() -> mutable_call_back_type() -> CopyFrom(call_back_type);
              item->set_delayed_time(log_process_timestamp - ad_joint_labeled_log_click->time());
              falcon::Inc(("item_click_match_cnt_" + action_type_str).c_str());
              producer.Produce(ad_joint_labeled_log_click);
            }
          }
        }
      } else { //没有读到的，写到picasso
        key = CONVERSION_NO_LOG_PREFIX + "#" + std::to_string(ad_log_for_algo->llsid())  + "#" + std::to_string(creative_id);
        falcon::Inc(("recall_action_type_put_" + action_type_str).c_str());
        std::string value = "-1";
        kuaishou::ad::algorithm::CallBackType call_back_type;
        ret = picassoBGet_tmp(DSP_TABLE_NEW_NAME, TMP_TABLE_NAME, key, value);
        if (ret == ks::ad_picasso::sdk::STATUS_OK && value != "-1") {
          if (call_back_type.ParseFromArray(value.data(), value.size())) {
            bool exist_flag = false;
            for (int i = 0; i < call_back_type.callback_event_size(); i++) {
              if (action_type == call_back_type.callback_event(i)) {
                exist_flag = true;
                break;
              }
            }
            if (!exist_flag) {
              call_back_type.add_callback_event(action_type);
              call_back_type.SerializeToString(&value);
              if (delivery_types.find(action_type) != delivery_types.end()) {
                // 如果回传的是物流信息，过期时间设为 7 天
                send2picasso_tmp(DSP_TABLE_NEW_NAME, TMP_TABLE_NAME, key, value, dianshang_timeout, write_table);
              } else if (ad_log_for_algo->page_id() == 10001){
                send2picasso_tmp(DSP_TABLE_NEW_NAME, TMP_TABLE_NAME, key, value, kuaixiang_timeout, write_table);
              } else {
                send2picasso_tmp(DSP_TABLE_NEW_NAME, TMP_TABLE_NAME, key, value, default_timeout, write_table);
              }
            }
          }
        }

        LOG(INFO)<<"llsid:"<<ad_log_for_algo->llsid()<<" creative_id:"<<creative_id << "time:"
                 <<ad_log_for_algo->event_server_timestamp() << " page_id:"<< ad_log_for_algo->page_id()
                 <<" sub_page_id:"<< ad_log_for_algo->sub_page_id()
                 <<" delivery_time" << ad_log_for_algo->delivery_timestamp() << " miss";
        // ad_log_for_algo->SerializeToString(&value);
        //send2picasso(key,value);
        if(ad_log_for_algo->page_id() == 10001){
          falcon::Inc(("miss_kuaixiang_cnt_" + action_type_str).c_str());
        }
        falcon::Inc(("miss_cnt_" + action_type_str).c_str());
      }
      delete ad_log_for_algo;
      */
    }
  };
  std::thread *example_data_threads = new std::thread[FLAGS_num_data_threads_example];
  for (size_t i = 0; i < FLAGS_num_data_threads_example; ++i) {
    example_data_threads[i] = std::thread(example_data_func);
  }

  // 停止 account 历史回传记录线程
  // std::thread *label_data_threads = new std::thread[FLAGS_num_data_threads_label];
  // for (size_t i = 0; i < FLAGS_num_data_threads_label; ++i) {
  //   label_data_threads[i] = std::thread(label_data_func);
  // }

  for (size_t i = 0; i < FLAGS_num_data_threads_example; ++i) {
    example_data_threads[i].join();
  }

  // 停止 account 历史回传记录线程
  // for (size_t i = 0; i < FLAGS_num_threads_label; ++i) {
  //   label_data_threads[i].join();
  // }
  return 0;
}
