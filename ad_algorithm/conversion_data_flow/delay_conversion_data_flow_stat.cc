#include <math.h>
#include <string>
#include <sstream>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <iostream>
#include <fstream> 
#include <cstdio>
#include <limits>
#include <thread>

#include "ks/util/json.h"
#include "ks/serving_util/dynamic_config.h"
#include "base/common/gflags.h"
#include "base/common/base.h"
#include "base/common/logging.h"
#include "base/time/timestamp.h"
#include "base/encoding/base64.h"
#include "base/encoding/line_escape.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/ad_base/src/kafka/kafka_client.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.pb.h"

DEFINE_int32(max_stat_num, 20000, "max_stat_num");
DEFINE_int32(min_stat_num, 300, "min_stat_num");
DEFINE_int32(write_time_gap_s, 10, "write time gap");
DEFINE_int32(num_threads, 10, "num thread");

using namespace std;
using namespace kuaishou::ad::algorithm;

class StatLabelData {
 private:
  uint64 last_time = 0;
  int label_cnt = 0;
  int pos = 0;
  bool* label_buff;
  int label_buff_true_cnt = 0;
 public:
  StatLabelData(){
    label_buff = new bool[FLAGS_max_stat_num];
  }

  ~StatLabelData(){
    delete[] label_buff;
  }

  void AddLabel(bool label){
    if(label_cnt < FLAGS_max_stat_num){
      label_buff[pos] = label;
      label_cnt++;
    } else {
      if(label_buff[pos]){
        label_buff_true_cnt--;
      }
      label_buff[pos] = label;
    }

    if(label){
      label_buff_true_cnt++;
    }
    pos = (pos + 1) % FLAGS_max_stat_num; 
    last_time = base::GetTimestamp() / 1000;
  }

  double GetRate(){
    if(label_cnt < FLAGS_min_stat_num){
      return -1.0;
    }
    return (label_buff_true_cnt + 0.0) / label_cnt;
  }

  int GetCount(){
    return label_cnt;
  }

  uint64 GetLastTime(){
    return last_time;
  }

  void ParseFromString(){
    //TODO
  }

  void ToString(){
    //TODO
  }
};

class LogProcessor {
 public:
  LogProcessor() {
    const std::string consumer_user_param = "auto.offset.reset=latest;fetch.error.backoff.ms=0;queued.max.messages.kbytes=51200;linger.ms=100;kuaishou.set.offset.ms.ago=1";
    auto consumer_config = ks::DynamicJsonConfig::GetConfig()->Get("mq_consumer_config_0");
    if (!consumer_config) {
      LOG(FATAL) << "gete mq_consumer_config_0 error";
    }

    const std::string topic = consumer_config->GetString("topic");
    if (topic.size() == 0) {
      LOG(FATAL) << "cannot get mq_consumer_config_0.topic";
    }
    consumer_.InitConsumer(topic, "conversion_shuffle_data_label_stat", consumer_user_param);

    const std::string user_params = "compression.codec=lz4";
    auto json = ks::DynamicJsonConfig::GetConfig()->Get("mq_producer_config_0");
    if (nullptr == json) {
      LOG(ERROR) << "invalid json config key. key == mq_producer_config_0";
    }
    std::string producer_topic = json->GetString("topic", ""); 
    producer_.Init(producer_topic, user_params);
  }
  void Consume() {
    std::string message;
    int ret = consumer_.Consume(message);
    if (ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
      return;
    }
    AdJointLabeledLog ad_joint_labeled_log;
    if (!ad_joint_labeled_log.ParseFromArray(message.data(), message.size())) {
      LOG(ERROR) << "Failed to parse sample info. len=" << message.size();
      return;
    }
    auto item = ad_joint_labeled_log.mutable_item(0);
    if (item->ad_dsp_info().call_back_type().callback_event()[0] != kuaishou::ad::AdCallbackLog::EVENT_CONVERSION) {
      return;
    }
    int64 unit_id = item->ad_dsp_info().creative().base().unit_id();
    int label = item -> mutable_label_info()->callback_event();
    bool is_kx = false;
    bool is_un = false;
    bool is_kuaishou = false;
    bool is_nebula = false;
    //defalut 101 102 尾号代表几跳激活 1就是1跳激活  2就是 2跳激活
    // 快享判断 111 112
    if (ad_joint_labeled_log.context().page_id() == 10001){
      is_kx = true;
    }

    // 联盟判断 121 122
    for (const kuaishou::ad::ContextInfoCommonAttr& contextAttr : ad_joint_labeled_log.context().info_common_attr()) {
      if (contextAttr.name_value() == kuaishou::ad::ContextInfoCommonAttr_Name_MEDIUM_ATTRIBUTE) {
        if(contextAttr.int_value() == 2 || contextAttr.int_value() == 4){
          is_un = true;
          break;
        }
      }
    }

    // 主板判断 11 12
    if (ad_joint_labeled_log.context().app_id() == "kuaishou"){
      is_kuaishou = true;
    }

    // 极速版 21 22
    if (ad_joint_labeled_log.context().app_id() == "kuaishou_nebula"){
      is_nebula = true;
    }


    if(label == 1){
      AddSample(unit_id, true);
      AddSample(1, true);
      AddSample(2, true);
      if(is_kx){
        if(is_kuaishou){
          AddSample(111, true);
          AddSample(112, true);
        } else {
          AddSample(113, true);
          AddSample(114, true);
        }
      } else if (is_un){
        AddSample(121, true);
        AddSample(122, true);
      } else {
        AddSample(101, true);
        AddSample(102, true);
      }

      if(is_kuaishou){
        AddSample(11, true);
        AddSample(12, true);
      } else if (is_nebula){
        AddSample(21, true);
        AddSample(22, true);
      }

    } else if(label == 0){
      AddSample(unit_id, false);
      AddSample(1, false);
      if(is_kx){
        if (is_kuaishou){
          AddSample(111, false);
        } else {
          AddSample(113, false);
        }
      } else if (is_un){
        AddSample(121, false);
      } else {
        AddSample(101, false);
      }

      if(is_kuaishou){
        AddSample(11, false);
      } else if (is_nebula){
        AddSample(21, false);
      }
      if (item -> label_info().item_click()) {
        AddSample(2, false);
        if(is_kx){
          if (is_kuaishou){
            AddSample(112, false);
          } else {
            AddSample(114, false);
          }
        } else if (is_un){
          AddSample(122, false);
        } else {
          AddSample(102, false);
        }

        if(is_kuaishou){
          AddSample(12, false);
        } else if (is_nebula){
          AddSample(22, false);
        }
      }
    }
  }
  
  void AddSample(int64 unit_id, bool label) {
    StatLabelData * stat_label_data_ptr = nullptr;
    data_map_mutex_.lock();
    auto it = data_map_.find(unit_id);
    if (it != data_map_.end()) {
      stat_label_data_ptr = it->second;
    } else {
      auto pit = data_map_.emplace(unit_id, new StatLabelData());
      stat_label_data_ptr = (pit.first)->second;
    }
    stat_label_data_ptr->AddLabel(label);
    data_map_mutex_.unlock();
  }
  void Produce() {
    data_map_mutex_.lock();
    ks::Json result(ks::StringToJson("{}"));
    for (auto it = data_map_.begin(); it != data_map_.end(); ++it) {
      double rate = it->second->GetRate();
      if(rate > 0){
        result.set("tag", "sample");
        result.set("unit", it->first);
        result.set("value", rate);
        result.set("timestamp", base::GetTimestamp() / 1000);
        result.set("cnt", 1);
        std::string msg = ks::JsonToString(result.get());
        producer_.Produce(msg);
        LOG_EVERY_N(INFO, 100) << msg;
      }
    }
    data_map_mutex_.unlock();
  }

 private:
  std::mutex data_map_mutex_;
  std::unordered_map<int64, StatLabelData*> data_map_;
  ks::ad_base::AdKafkaProducer producer_;
  ks::ad_base::AdKafkaConsumer consumer_;
};

int main(int argc, char** argv) {
  base::InitApp(&argc, &argv, "stat train data");
  LogProcessor log_processor;
  auto worker = [&]() {
    while (true) {
      log_processor.Consume();
    }
  };

  auto timer_func = [&]() {
    while (true) {
      std::this_thread::sleep_for(std::chrono::seconds(FLAGS_write_time_gap_s));
      log_processor.Produce();
    }
  };

  std::thread *threads = new std::thread[FLAGS_num_threads];
  for (size_t i = 0; i < FLAGS_num_threads; ++i) {
    threads[i] = std::thread(worker);
  }
  std::thread thread(timer_func);

  for (size_t i = 0; i < FLAGS_num_threads; ++i) {
    threads[i].join();
  }
  return 0;
}
