#include "teams/ad/ad_base/src/container/concurrent_queue.h"
#include "teams/ad/ad_base/src/kafka/kafka_client.h"
#include "teams/ad/ad_base/src/common/common.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "base/common/gflags.h"
#include "base/common/base.h"
#include "base/common/logging.h"
#include "base/time/timestamp.h"
#include "base/strings/string_split.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_number_conversions.h"
#include "base/encoding/base64.h"
#include "base/encoding/line_escape.h"
#include "ks/serving_util/dynamic_config.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/dw/ad_log_for_algo.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.pb.h"
#include "teams/ad/picasso/sdk/picasso_client/picasso_client.h"
#include "third_party/tbb/include/tbb/concurrent_priority_queue.h"
#include "teams/ad/ad_proto/kuaishou/ad/picasso/picasso_update.pb.h"
#include "teams/ad/ad_algorithm/log_preprocess/ad_model_kconf_util.h"
#include "infra/redis_proxy_client/src/redis_proxy_client/redis_proxy_client.h"


#include <iostream>
#include <fstream>
#include <string>
#include <random>
#include <cmath>
#include <set>
using namespace std;
using namespace ks::ad_algorithm;
using namespace ks::infra;
using namespace kuaishou::ad::algorithm;
using namespace tbb::interface5;


const uint64 SLEEP_TIME = 10;  // in ms
const uint64 KAFKA_TIME_OUT = 10; // in ms

const string DSP_TABLE_NAME = "adAlgoConversionJoin"; //信息日志表
const string DETAIL_TABLE_NAME = "adAlgoLabelJoin"; //快享日志表
const string TMP_TABLE_NAME = "adAlgoMatch"; //临时表
const string DSP_TABLE_NEW_NAME = "adDspImpressionFull";

const string ACCOUNT_ACTION_TYPE_PREFIX= "account_action_type"; //账号转化类型
const string DSP_CLICK_LOG_PREFIX = "label_log_new_test"; //信息流前缀
const string DETAIL_IMPRESSION_PREFIX = "label_log_detail"; //快享前缀
const string IMPRESSION_LOG_PREFIX = "impression_log_all"; // 新的曝光日志流前缀
const string CONVERSION_NO_LOG_PREFIX = "conversion_no_log_type"; //转化日志
const string ITEM_CLICK_LOG_PREFIX = "item_click_log"; //转化日志

const string RedisDelayPrefix = "delay";
const string RedisExtPrefix = "ext";
const string RedisPredictUnitPrefix = "convtime_unit_";
const string RedisPredictAccountPrefix = "convtime_account_";
const string RedisPredictUnitKXPrefix = "convtime_unit_kx_";
const string RedisPredictAccountKXPrefix = "convtime_account_kx_";
const string RedisPredictUnitUNPrefix = "convtime_unit_un_";
const string RedisPredictAccountUNPrefix = "convtime_account_un_";
const string RedisExtValueASplitKey = "\1";
const string RedisExtValueBSplitKey = "\2";
const string RedisExtValueCSplitKey = "\3";

DEFINE_int32(num_threads_predict,5,"num_threads_predict");//从kafka读入，predict delay时间，写入redis
DEFINE_int32(num_threads_delay, 5, "num_threads_delay");// delay 写kafka
DEFINE_int32(buffer_size, 2048, "buffer_size"); // shuffle buffer size

DEFINE_uint64(min_delay_ms, 600000, "min_delay_ms");// 最小delay 10 * 60 * 1000
DEFINE_uint64(min_kx_delay_ms, 600000, "min_kx_delay_ms");// 最小delay 10 * 60 * 1000
DEFINE_uint64(min_un_delay_ms, 600000, "min_un_delay_ms");// 最小delay 10 * 60 * 1000
DEFINE_uint64(max_kx_delay_ms, ********, "max_kx_delay_ms");// 最大delay 24 * 60 * 60 * 1000
DEFINE_uint64(max_un_delay_ms, ********, "max_un_delay_ms");// 最大delay 24 * 60 * 60 * 1000
DEFINE_uint64(max_delay_ms, ********, "max_delay_ms");// 最大delay 24 * 60 * 60 * 1000
DEFINE_uint64(default_delay_ms, 3600000, "default_delay_ms");// 默认delay 60 * 60 * 1000
DEFINE_uint64(default_kx_delay_ms, 3600000, "default_kx_delay_ms");// 默认delay 60 * 60 * 1000
DEFINE_uint64(default_un_delay_ms, 3600000, "default_un_delay_ms");// 默认delay 60 * 60 * 1000
DEFINE_uint64(backup_second, 21600, "backup_second");// 默认备份秒数 6 * 60 * 60
DEFINE_uint64(delay_partitions, 10, "delay_partitions");// 默认每秒partitions
DEFINE_uint64(kx_use_default_all, 1, "kx_use_default_all");
DEFINE_uint64(un_use_default_all, 1, "un_use_default_all");

/*
 * 
 * 分为两部分：
 * 1、从kafka读取jointlog，预测其delay时间，把预测时间写入redis
 * 2、从redis读取delay时间到的key，从picasso中取出写入kafka
 *
 */

bool safe_stoul(const string& str, uint64 &res, uint64 default_val = 0){
  try{
    res = stoul(str);
  } catch(...){
    res = default_val;
    return false;
  }
  return true;
}

bool safe_stoi(const string& str, int &res, int default_val = 0){
  try{
    res = stoi(str);
  } catch(...){
    res = default_val;
    return false;
  }
  return true;
}

//输出的时候日志shuffle一下，为了给online learning使用。
class ShuffleBuffer {
 public:
  ShuffleBuffer(size_t size, ks::ad_base::AdKafkaProducer * producer) {
    size_ = size;
    producer_ = producer;
    log_buffer_ = new AdJointLabeledLog *[size];
    for (size_t i = 0; i < size_; ++i) {
      log_buffer_[i] = nullptr;
    }
    rng.seed(random_device()());
    dist_int = new uniform_int_distribution<int>(0, size - 1);
  }
  void push(AdJointLabeledLog * ad_joint_labeled_log) {
    auto log_to_put =  ad_joint_labeled_log;
    int hashid = ad_joint_labeled_log->llsid() % 100;
    if(hashid < ConversionShuffleDataProduceDropRate()){
      delete ad_joint_labeled_log;
      return;
    }

    int index = (*dist_int)(rng);
    if (index >= 0 && index < size_) {
      mtx_.lock();
      auto temp = log_to_put;
      log_to_put = log_buffer_[index];
      log_buffer_[index] = temp;
      mtx_.unlock();
    } 
    if (log_to_put != nullptr) {
      string send_val;
      log_to_put->SerializeToString(&send_val);
      falcon::Inc("write_to_kafka_cnt");
      producer_->Produce(send_val);
      delete log_to_put;
    }
  }
  ~ShuffleBuffer() {
    for(size_t i = 0; i < size_; ++i) {
      string send_val;
      log_buffer_[i]->SerializeToString(&send_val);
      producer_->Produce(send_val);
      delete log_buffer_[i];
      log_buffer_[i] = nullptr;
    }
    delete [] log_buffer_;
    delete dist_int;
  }
 private:
  AdJointLabeledLog ** log_buffer_;
  ks::ad_base::AdKafkaProducer * producer_;
  int size_;
  mutex mtx_;
  mt19937 rng;
  uniform_int_distribution<int> *dist_int;
};

class AdCallbackExtData{
 public:
  uint64 delayed_time = 0;
  kuaishou::ad::AdCallbackLog::EventType action_type;
  uint64 purchase_amount = 0;
 public:
  bool ParseFromString(string &str){
    vector<string> items;
    base::SplitStringUsingSubstr(str, RedisExtValueCSplitKey, &items);
    if(items.size() != 3){
      return false;
    }
    safe_stoul(items[0], delayed_time, 0);
    action_type = static_cast<kuaishou::ad::AdCallbackLog::EventType>(stoi(items[1]));
    safe_stoul(items[2], purchase_amount, 0);
    return true;
  }

  bool ToString(string *value){
    value->clear();
    value->append(to_string(delayed_time));
    value->append(RedisExtValueCSplitKey);
    value->append(to_string(action_type));
    value->append(RedisExtValueCSplitKey);
    value->append(to_string(purchase_amount));
    return true;
  }

  bool Fill(::kuaishou::ad::algorithm::Item* item){
    action_type = item->mutable_label_info()->callback_event();
    // 0 = EVENT_UNKNOWN
    // 1 = EVENT_CONVERSION
    if(action_type != 0 && action_type != 1){
      //LOG(INFO) << "faild fill , action_type = " << action_type;
      return false;
    }
    //LOG(INFO) << "success fill , action_type = " << action_type;
    purchase_amount = item->mutable_label_info()->purchase_amount();
    delayed_time = item->delayed_time();
    return true;
  }

  bool IsCallback(){
    return action_type > 0;
  }
};

// picasso没存的info
class RedisExtData{
 public:
  // label
  uint64 item_click = 0;
  uint64 item_impression = 0;
  uint64 predict_delay_time = 0;
  vector<AdCallbackExtData> callback_ext_datas;
 public:
  bool ParseFromString(string &str){
    vector<string> items;
    base::SplitStringUsingSubstr(str, RedisExtValueASplitKey, &items);
    if(items.size() != 4){
      return false;
    }
    safe_stoul(items[0], item_click, 0);
    safe_stoul(items[1], item_impression, 0);
    safe_stoul(items[2], predict_delay_time, 0);

    vector<string> callback_ext_data_str_list;
    base::SplitStringUsingSubstr(items[3], RedisExtValueBSplitKey, &callback_ext_data_str_list);
    for(string callback_ext_data_str : callback_ext_data_str_list){
      AdCallbackExtData callback_ext_data;
      if(callback_ext_data.ParseFromString(callback_ext_data_str)){
        callback_ext_datas.push_back(callback_ext_data);
      }
    }
    return true;
  }

  bool IsCallback(){
    bool hasCallback = false;
    for(AdCallbackExtData callback_ext_data : callback_ext_datas){
      if(callback_ext_data.IsCallback()){
        hasCallback = true;
        //LOG(INFO) << "has callback : " << callback_ext_data.action_type;
        break;
      }
    }
    return hasCallback;
  }

  bool ToString(string *value){
    value->clear();
    value->append(to_string(item_click));
    value->append(RedisExtValueASplitKey);
    value->append(to_string(item_impression));
    value->append(RedisExtValueASplitKey);
    value->append(to_string(predict_delay_time));
    value->append(RedisExtValueASplitKey);
    if (callback_ext_datas.size() == 0){
      value->append(" ");
    } else if (callback_ext_datas.size() == 1){
      string callback_ext_data_str;
      callback_ext_datas[0].ToString(&callback_ext_data_str);
      value->append(callback_ext_data_str);
    } else {
      bool hasCallback = false;
      for(AdCallbackExtData callback_ext_data : callback_ext_datas){
        if(callback_ext_data.IsCallback()){
          hasCallback = true;
          //LOG(INFO) << "has callback : " << callback_ext_data.action_type;
          break;
        }
      }
      if(hasCallback){
        // 只留一个callback
        for(AdCallbackExtData callback_ext_data : callback_ext_datas){
          if(!callback_ext_data.IsCallback()){
            continue;
          }
          string callback_ext_data_str;
          callback_ext_data.ToString(&callback_ext_data_str);
          value->append(callback_ext_data_str);
          break;
        }
      } else {
        // 只留一个非callback
        for(AdCallbackExtData callback_ext_data : callback_ext_datas){
          if(callback_ext_data.IsCallback()){
            continue;
          }
          string callback_ext_data_str;
          callback_ext_data.ToString(&callback_ext_data_str);
          value->append(callback_ext_data_str);
          break;
        }
      }
    }
    return true;
  }

  void Fill(::kuaishou::ad::algorithm::Item* item){
    if(item->label_info().item_click()){
      item_click = 1;
    }
    if(item->label_info().item_impression()){
      item_impression = 1;
    }
    AdCallbackExtData callback_ext_data;
    if(callback_ext_data.Fill(item)){
      callback_ext_datas.push_back(callback_ext_data);
    }
  }

  void Update(RedisExtData& other){
    if(other.item_click == 1){
      item_click = 1;
    }
    if(other.item_impression == 1){
      item_impression = 1;
    }
    if(other.predict_delay_time > 0){
      predict_delay_time = other.predict_delay_time;
    }
    for(AdCallbackExtData callback_ext_data : other.callback_ext_datas){
      if(callback_ext_data.IsCallback() && IsCallback()){
        falcon::Inc("ad_log_in_callback_exist_cnt", 1);
      }
      callback_ext_datas.push_back(callback_ext_data);
    }
  }

  bool IsNull(){
    return callback_ext_datas.size() < 1;
  }

  void Fill(::kuaishou::ad::algorithm::Item* item, uint64 predict_delay_time_ext){
    if(item->label_info().item_click()){
      item_click = 1;
    }
    if(item->label_info().item_impression()){
      item_impression = 1;
    }
    if(predict_delay_time == 0){
      predict_delay_time = predict_delay_time_ext;
    }
    AdCallbackExtData callback_ext_data;
    if(callback_ext_data.Fill(item)){
      if(callback_ext_data.IsCallback() && IsCallback()){
        falcon::Inc("ad_log_in_callback_exist_cnt", 1);
      }
      callback_ext_datas.push_back(callback_ext_data);
    }
  }
};

int main(int argc, char* argv[]) {
  base::InitApp(&argc, &argv, "延时转化日志流");

  hash<string> hash_fn;
  // picasso
  ks::ad_picasso::sdk::PicassoClient *picasso = ks::ad_picasso::sdk::PicassoClient::GetInstance();

  ks::ad_picasso::sdk::PicassoOption opt;
  opt.client_tag = "data_flow_full"; 
  opt.self_kess_server_name = "data_flow_full";
  opt.use_hash = true;
  if (!picasso->Init(opt)) {
    LOG(ERROR)<< "init picasso failed. ";
    return -1;
  }

  // kafka
  auto mq_env = ks::DynamicJsonConfig::GetConfig()->Get("dsp_log_parser_config");
  if (!mq_env) {
    return -1;
  }
  const string data_group_id = mq_env->GetString("data_group_id", "dsp_log_delay_flow");
  ks::ad_base::AdKafkaConsumer *data_consumer = new ks::ad_base::AdKafkaConsumer();
  const std::string consumer_user_param = "auto.offset.reset=latest;fetch.error.backoff.ms=0;queued.max.messages.kbytes=51200;linger.ms=100;kuaishou.set.offset.ms.ago=1";
  auto consumer_config = ks::DynamicJsonConfig::GetConfig()->Get("mq_consumer_config_0");
  if (!consumer_config) {
    LOG(FATAL) << "gete mq_consumer_config_0 error";
  }

  const std::string topic = consumer_config->GetString("topic");
  if (topic.size() == 0) {
    LOG(FATAL) << "cannot get mq_consumer_config_0.topic";
  }
  int ret = data_consumer->InitConsumer(topic, data_group_id, consumer_user_param);
  if (ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
    LOG(ERROR)<< "init data consumer failed. ";
    return -1;
  }

  const std::string user_params = "compression.codec=lz4";
  auto json = ks::DynamicJsonConfig::GetConfig()->Get("mq_producer_config_0");
  if (nullptr == json) {
    LOG(ERROR) << "invalid json config key. key == mq_producer_config_0";
    return -1;
  }
  std::string producer_topic = json->GetString("topic", "");
  ks::ad_base::AdKafkaProducer *producer = new ks::ad_base::AdKafkaProducer();
  producer->Init(producer_topic, user_params);

  // shuffle_buffer
  int buffer_size = FLAGS_buffer_size;
  ShuffleBuffer *shuffle_buffer = new ShuffleBuffer(buffer_size, producer);

  // redis
  auto redis_config = ks::DynamicJsonConfig::GetConfig()->Get("redis_config");
  string redis_zk_config_path = redis_config->GetString("zk_config_path", "config.zk.cluster.zw:2181:/ks2/redis/adConvOnlinePredict/_ZW");
  int redis_default_timeout_ms = redis_config->GetInt("default_timeout_ms", 1000 * 3600 * 24);
  int redis_max_connection_per_proxy = redis_config->GetInt("redis_max_connection_per_proxy", 5);
  ::ks::infra::RedisClient *redis_client = ks::infra::RedisProxyClient::GetRedisClientByZk(redis_zk_config_path, redis_max_connection_per_proxy, redis_default_timeout_ms);

  // db func
  auto picassoBGet = [&](const string& table, const string& key, string& value, int retry = 3) {
    value = "-1";
    auto ret = ks::ad_picasso::sdk::STATUS_TIMEOUT;
    int l_retry  = retry;
    do {
      ret = picasso->BGet(table, key, value);
    } while (--l_retry > 0 && ret != ks::ad_picasso::sdk::STATUS_OK && value != "-1");
    if(ret != ks::ad_picasso::sdk::STATUS_OK){
      LOG(INFO)<<"miss picasso status : "<<ret<<" , table : "<<table<<" , key : "<<key;
    }
    return ret == ks::ad_picasso::sdk::STATUS_OK && value != "-1";
  };

  auto picassoBGet_tmp = [&](const string& table, const string& tmp_table, const string& key, string& value, int retry = 3) {
    if(IsConversionDataReadTempTable()){
      uint32 id = hash_fn(key) % 100;
      falcon::Inc("in_read_tmp_table", 1);
      if(id < ConversionDataReadTempTableRate()){
        falcon::Inc("read_tmp_table", 1);
        bool ret = picassoBGet(tmp_table, "tmp#"+key, value, retry);
        if(!ret){
          falcon::Inc("read_tmp_table_err", 1);
          return picassoBGet(table, key, value, retry);
        }
        return ret;
      }
    }
    return picassoBGet(table, key, value, retry);
  };
  auto redisExist = [&](const string& key) {
    return redis_client->Exist(key) == RedisErrorCode::KS_INF_REDIS_NO_ERROR;
  };

  auto redisExpire = [&](const string& key, int timeout_ms, int retry = 1) {
    RedisErrorCode err_code = RedisErrorCode::KS_INF_REDIS_NO_ERROR;
    int l_retry  = retry;
    do {
      err_code = redis_client->Expire(key, timeout_ms / 1000);
    } while (--l_retry > 0 && err_code != RedisErrorCode::KS_INF_REDIS_NO_ERROR);
    return err_code == RedisErrorCode::KS_INF_REDIS_NO_ERROR;
  };

  auto redisSet = [&](const string& key, string& value, int timeout_ms, int retry = 3) {
    RedisErrorCode err_code = RedisErrorCode::KS_INF_REDIS_NO_ERROR;
    int l_retry  = retry;
    do {
      //err_code = redis_client->Set(key, value, timeout_ms);
      //err_code = redis_client->Expire(key, timeout_ms / 1000);
      err_code = redis_client->SetEx(key, value, timeout_ms / 1000);

    } while (--l_retry > 0 && err_code != RedisErrorCode::KS_INF_REDIS_NO_ERROR);
    return err_code == RedisErrorCode::KS_INF_REDIS_NO_ERROR;
  };

  auto redisGet = [&](const string& key, std::string *value, int retry = 3) {
    RedisErrorCode err_code = RedisErrorCode::KS_INF_REDIS_NO_ERROR;
    int l_retry  = retry;
    do {
      err_code = redis_client->Get(key, value);
    } while (--l_retry > 0 && err_code != RedisErrorCode::KS_INF_REDIS_NO_ERROR);
    return err_code == RedisErrorCode::KS_INF_REDIS_NO_ERROR;
  };

  auto redisListPushLeft = [&](const string& key, string& value, int timeout_ms, int retry = 1) {
    RedisErrorCode err_code = RedisErrorCode::KS_INF_REDIS_NO_ERROR;
    int l_retry  = retry;
    int64_t list_length;
    do {
      err_code = redis_client->ListPushLeft(key, value, &list_length, timeout_ms);
      err_code = redis_client->Expire(key, timeout_ms / 1000);
    } while (--l_retry > 0 && err_code != RedisErrorCode::KS_INF_REDIS_NO_ERROR);
    return err_code == RedisErrorCode::KS_INF_REDIS_NO_ERROR;
  };

  auto redisListPopLeft = [&](const string& key, string *value, int retry = 1) {
    RedisErrorCode err_code = RedisErrorCode::KS_INF_REDIS_NO_ERROR;
    int l_retry  = retry;
    do {
      err_code = redis_client->ListPopLeft(key, value);
    } while (--l_retry > 0 && err_code != RedisErrorCode::KS_INF_REDIS_NO_ERROR);
    return err_code == RedisErrorCode::KS_INF_REDIS_NO_ERROR;
  };

  // 预测 并写入redis
  auto predict_func = [&](){
    mt19937 rng;
    rng.seed(random_device()());
    uniform_int_distribution<int> dist_int = uniform_int_distribution<int>(0, FLAGS_delay_partitions - 1);

    while(true){
      //// step 1. kafka读数据，判断是否数据异常
      uint64_t start_consumer_timestamp = base::GetTimestamp();
      string message;
      int retc = data_consumer->Consume(message);
      uint64_t end_consumer_timestamp = base::GetTimestamp();
      falcon::Stat("consumer_timestamp", end_consumer_timestamp - start_consumer_timestamp);
      if (retc != ks::ad_base::AdKafkaStatus::SUCCESS) {
        this_thread::sleep_for(chrono::milliseconds(SLEEP_TIME));
        continue;
      }
      falcon::Inc("ad_log_in_kafka_cnt", 1);
      kuaishou::ad::algorithm::AdJointLabeledLog ad_joint_labeled_log;
      if (!ad_joint_labeled_log.ParseFromArray(message.data(),message.size())) {
        LOG(ERROR)<<"parse ad_joint_labeled_log error!!!!";
        falcon::Inc("parser_call_back_type_error");
        continue;
      }
      if (ad_joint_labeled_log.item_size() <= 0) {
        LOG(ERROR)<<"ad_joint_labeled_log has no item !!!!";
        falcon::Inc("ad_joint_labeled_log_no_item");
        continue;
      }
      auto llsid = ad_joint_labeled_log.llsid();
      auto item  = ad_joint_labeled_log.mutable_item(0);;
      if (item->type() != ItemType::AD_DSP || !item->has_ad_dsp_info()) {
        LOG(ERROR)<<"item type error or has no dsp info!";
        falcon::Inc("item_type_dsp_info_error");
        continue;
      }
      uint64 account_id = item->ad_dsp_info().creative().base().account_id();
      uint64 unit_id = item->ad_dsp_info().creative().base().unit_id();
      uint64 creative_id = item->ad_dsp_info().creative().base().id();
      bool is_kx = false;
      bool is_un = false;
      // 判断类型
      // 快享判断
      if (ad_joint_labeled_log.context().page_id() == 10001){
        is_kx = true;
      }

      // 联盟判断
      for (const kuaishou::ad::ContextInfoCommonAttr& contextAttr : ad_joint_labeled_log.context().info_common_attr()) {
        if (contextAttr.name_value() == kuaishou::ad::ContextInfoCommonAttr_Name_MEDIUM_ATTRIBUTE) {
          if(contextAttr.int_value() == 2 || contextAttr.int_value() == 4){
            is_un = true;
            break;
          }
        }
      }

      if (is_kx) {
        falcon::Inc("ad_log_in_kx_cnt");
      } else if (is_un) {
        falcon::Inc("ad_log_in_un_cnt");
      } else {
        falcon::Inc("ad_log_in_cnt");
      }

      //// step 2. 构建一个新的callback info
      // callback info 的key
      string key = RedisExtPrefix + "#" +  to_string(llsid) + "#" + to_string(creative_id);
      string value = "";

      // step 3. 读取redis 
      // 更新 redis callback
      RedisExtData redis_ext_data;
      RedisExtData redis_ext_data_exist;

      // 填充
      redis_ext_data.Fill(item);
      if(redis_ext_data.IsNull()){
        // TODO,这里只保留0 和 1，后面如果要处理所有callback需要修改。
        continue;
      }

      // 是否存在redis
      if(redisExist(key) && redisGet(key, &value)){
        // 存在就把redis信息拿出来，更新
        redis_ext_data_exist.ParseFromString(value);
        redis_ext_data.Update(redis_ext_data_exist);
        redis_ext_data.ToString(&value);
        if (is_kx){
          redisSet(key, value, FLAGS_max_kx_delay_ms + FLAGS_backup_second * 1000); // redis life = 1.5天
        } else if (is_un){
          redisSet(key, value, FLAGS_max_un_delay_ms + FLAGS_backup_second * 1000); // redis life = 1.5天
        } else {
          redisSet(key, value, FLAGS_max_delay_ms + FLAGS_backup_second * 1000); // redis life = 1.5天
        }
        falcon::Inc("ad_log_in_update_cnt", 1);
      } else {
        // key不存在 就predict写runtime

        // 获取预测时间的key
        string predict_key = "";
        string predict_value = "";
        uint64 predict_delay_time = FLAGS_default_delay_ms;
        bool is_predict = false;
        
        // predict delay time
        if (is_kx){
          predict_delay_time = FLAGS_default_kx_delay_ms;
          // 快享
          if (!is_predict){
            predict_key = RedisPredictUnitKXPrefix + to_string(unit_id);
            falcon::Inc("ad_log_in_kx_predict_cnt", 1);
            if(redisGet(predict_key, &predict_value)){
              if(!safe_stoul(predict_value, predict_delay_time, 0)){
                LOG(ERROR) << "get error predict time" << predict_value;
              }
              is_predict = true;
            } else {
              falcon::Inc("ad_log_in_kx_predict_error_unit_cnt", 1);
            }
          }
          if (!is_predict) {
            predict_key = RedisPredictAccountKXPrefix + to_string(account_id);
            if(redisGet(predict_key, &predict_value)){
              if(!safe_stoul(predict_value, predict_delay_time, 0)){
                LOG(ERROR) << "get error predict time" << predict_value;
              }
              is_predict = true;
            } else {
              falcon::Inc("ad_log_in_kx_predict_error_account_cnt", 1);
              LOG(INFO) << "predict kx error, unit : " << unit_id << " ; account : " << account_id;
            }
          } 
        }
        else if (is_un){
        // 联盟
          if (!is_predict){
            predict_delay_time = FLAGS_default_un_delay_ms;
            // deault
            predict_key = RedisPredictUnitUNPrefix + to_string(unit_id);
            falcon::Inc("ad_log_in_un_predict_cnt", 1);
            if(redisGet(predict_key, &predict_value)){
              if(!safe_stoul(predict_value, predict_delay_time, 0)){
                LOG(ERROR) << "get error predict time" << predict_value;
              }
              is_predict = true;
            } else {
              falcon::Inc("ad_log_in_un_predict_error_unit_cnt", 1);
            }
          }
          if (!is_predict) {
            predict_key = RedisPredictAccountUNPrefix + to_string(account_id);
            if(redisGet(predict_key, &predict_value)){
              if(!safe_stoul(predict_value, predict_delay_time, 0)){
                LOG(ERROR) << "get error predict time" << predict_value;
              }
              is_predict = true;
            } else {
              falcon::Inc("ad_log_in_un_predict_error_account_cnt", 1);
              LOG(INFO) << "predict un error, unit : " << unit_id << " ; account : " << account_id;
            }
          }
        }
        else {
          // deault
          if (!is_predict){
            predict_key = RedisPredictUnitPrefix + to_string(unit_id);
            falcon::Inc("ad_log_in_predict_cnt", 1);
            if(redisGet(predict_key, &predict_value)){
              if(!safe_stoul(predict_value, predict_delay_time, 0)){
                LOG(ERROR) << "get error predict time" << predict_value;
              }
              is_predict = true;
            } else {
              falcon::Inc("ad_log_in_predict_error_unit_cnt", 1);
            }
          }
          if (!is_predict) {
            predict_key = RedisPredictAccountPrefix + to_string(account_id);
            if(redisGet(predict_key, &predict_value)){
              if(!safe_stoul(predict_value, predict_delay_time, 0)){
                LOG(ERROR) << "get error predict time" << predict_value;
              }
              is_predict = true;
            } else {
              falcon::Inc("ad_log_in_predict_error_account_cnt", 1);
              LOG(INFO) << "predict error, unit : " << unit_id << " ; account : " << account_id;
            }
          }
        }

        //联盟快享 是否走默认
        if((is_kx && FLAGS_kx_use_default_all == 1)||(is_un && FLAGS_un_use_default_all == 1)){
          if (!is_predict){
            predict_key = RedisPredictUnitPrefix + to_string(unit_id);
            if(is_kx){
              falcon::Inc("ad_log_in_kx_default_predict_cnt", 1);
            } else if (is_un){
              falcon::Inc("ad_log_in_kx_default_predict_cnt", 1);
            }

            if(redisGet(predict_key, &predict_value)){
              if(!safe_stoul(predict_value, predict_delay_time, 0)){
                LOG(ERROR) << "get error predict time" << predict_value;
              }
              is_predict = true;
            } else {
              if(is_kx){
                falcon::Inc("ad_log_in_kx_default_predict_error_unit_cnt", 1);
              } else if (is_un){
                falcon::Inc("ad_log_in_un_default_predict_error_unit_cnt", 1);
              }
            }
          }
          if (!is_predict) {
            predict_key = RedisPredictAccountPrefix + to_string(account_id);
            if(redisGet(predict_key, &predict_value)){
              if(!safe_stoul(predict_value, predict_delay_time, 0)){
                LOG(ERROR) << "get error predict time" << predict_value;
              }
              is_predict = true;
            } else {
              if(is_kx){
                falcon::Inc("ad_log_in_kx_default_predict_error_account_cnt", 1);
              } else if (is_un){
                falcon::Inc("ad_log_in_un_default_predict_error_account_cnt", 1);
              }
              LOG(INFO) << "predict error, unit : " << unit_id << " ; account : " << account_id;
            }
          }
        }

        predict_delay_time = predict_delay_time;
        if (is_kx){
          predict_delay_time = max(FLAGS_min_kx_delay_ms, predict_delay_time);
          predict_delay_time = min(FLAGS_max_kx_delay_ms, predict_delay_time);
          falcon::Stat("ad_log_in_kx_predict_delay", predict_delay_time);
        } else if (is_un){
          predict_delay_time = max(FLAGS_min_un_delay_ms, predict_delay_time);
          predict_delay_time = min(FLAGS_max_un_delay_ms, predict_delay_time);
          falcon::Stat("ad_log_in_un_predict_delay", predict_delay_time);
        } else {
          predict_delay_time = max(FLAGS_min_delay_ms, predict_delay_time);
          predict_delay_time = min(FLAGS_max_delay_ms, predict_delay_time);
          falcon::Stat("ad_log_in_predict_delay", predict_delay_time);
        }
        redis_ext_data.predict_delay_time = predict_delay_time;
        uint64 run_time = ad_joint_labeled_log.time() / 1000 + redis_ext_data.predict_delay_time / 1000; // server time 

        // 处理unknow晚到的情况
        if(!redis_ext_data.IsCallback()){
          uint64 now_program_run_time = base::GetTimestamp() / 1000000 + 30;
          // 修正时间
          if(run_time < now_program_run_time){
            run_time = now_program_run_time + 3600;
            falcon::Inc("ad_log_in_timefix_cnt", 1);
          }
        }
        
        // run_time的key
        string delay_key = RedisDelayPrefix + "#" + to_string(run_time) + "#" + to_string(dist_int(rng));
        //LOG(INFO) << "add delay_key : " << delay_key<< " , key : " << key;
        redisListPushLeft(delay_key, key,  (run_time - base::GetTimestamp() / 1000000 + FLAGS_backup_second) * 1000); // 多等备份时间
        falcon::Inc("ad_log_in_runtime_cnt", 1);

        redis_ext_data.ToString(&value);
        if (is_kx){
          redisSet(key, value, FLAGS_max_kx_delay_ms + FLAGS_backup_second * 1000); // redis life = 1.5天
        } else if (is_un) {
          redisSet(key, value, FLAGS_max_un_delay_ms + FLAGS_backup_second * 1000); // redis life = 1.5天
        } else {
          redisSet(key, value, FLAGS_max_delay_ms + FLAGS_backup_second * 1000); // redis life = 1.5天
        }
      } 

      uint64 run_time = ad_joint_labeled_log.time() / 1000 + redis_ext_data.predict_delay_time / 1000; //  
      uint64 now_program_run_time = base::GetTimestamp() / 1000000 + 30;

      if(run_time > now_program_run_time){
        falcon::Inc("ad_log_in_out_fast_cnt", 1);
      } else {
        falcon::Inc("ad_log_in_out_slow_cnt", 1);
      }
    }
  };

  // 从redis取 
  auto delay_func = [&](){
    // 最多可以恢复6个小时之前的
    uint64 run_time =  base::GetTimestamp() / 1000000 - FLAGS_backup_second;   // second
    while(true){
      // 执行时间不能超过当前时间-10s，最快执行10秒前的。
      while(run_time > base::GetTimestamp() / 1000000 - 10){
        this_thread::sleep_for(chrono::milliseconds(SLEEP_TIME));
      }
      LOG(INFO) << "now running : " << run_time;
      for(int partition = 0; partition < FLAGS_delay_partitions; partition++){
        //每次pop一个key
        while(true){
          //// step 1. 统计信息
          string delay_key_for_stat = "ad_log_delay_" + to_string((long)(log2(base::GetTimestamp() / 1000000 - run_time))) + "_cnt";
          falcon::Inc(delay_key_for_stat.c_str(), 1);

          //// step 2. 获取key

          // runtime key
          string delay_key = RedisDelayPrefix + "#" + to_string(run_time) + "#" + to_string(partition);
          // callback info key
          string key = "";
          // callback info
          string value = "";

          // get runtime
          // pop不出来说明被pop空了
          if(!redisListPopLeft(delay_key, &key)){
            break;
          }
          falcon::Inc("ad_log_out_in_cnt", 1);


          //// step 3. 根据key，获取callback info (redis_ext_data)

          // get redis ext data
          // LOG(INFO) << "get key : " << key;
          bool is_ext = redisGet(key, &value);
          RedisExtData redis_ext_data;
          if(is_ext){
            //LOG(INFO) << "get key : " << key << " , value : " << value;
            if(!redis_ext_data.ParseFromString(value)){
              is_ext = false;
              LOG(INFO) << "parse key : " << key << " error , value : " << value;
            }
          }


          //// step 4. 从picasso获取 log 

          // 从key切割出llsid和cid
          string llsid;
          string creative_id;
          vector<string> res;
          base::SplitStringUsingSubstr(key, "#", &res);
          if(res.size() != 3){
            LOG(INFO) << "get_error_key : " << key;
            falcon::Inc("ad_log_out_faild_getkey_cnt", 1);
            continue;
          }
          llsid = res[1];
          creative_id = res[2];

          // get from picasso
          string picasso_key = "";
          string picasso_value = "";
          bool exist = false;

          if(ModelUseOldDB()){
            //尝试从快享picasso读
            std::reverse(llsid.begin(), llsid.end());
            picasso_key = DETAIL_IMPRESSION_PREFIX + "#" + llsid + "#" + creative_id;
            std::reverse(llsid.begin(), llsid.end());
            bool ret = picassoBGet(DETAIL_TABLE_NAME, picasso_key, picasso_value, 5);
            falcon::Inc("ad_detail_label_read_cnt", 1);
            if (ret) {
              falcon::Inc("ad_detail_label_match_cnt", 1);
              exist = true;
            } 
            /*
            // 从老日志流的picasso读
            if (!exist) {
              picasso_key = DSP_CLICK_LOG_PREFIX + "#" +  llsid + "#"+ creative_id;
              bool ret = picassoBGet(DSP_TABLE_NAME, picasso_key, picasso_value, 5);
              falcon::Inc("ad_dsp_label_read_cnt", 1);
              if (ret) {
                falcon::Inc("ad_dsp_label_match_cnt", 1);
                exist = true;
              }
            }
            */
          }
          // 从新的picasso中读
          if (!exist) {
            picasso_key = IMPRESSION_LOG_PREFIX + "#" + llsid + "#"+ creative_id;
            bool ret = picassoBGet_tmp(DSP_TABLE_NEW_NAME, TMP_TABLE_NAME, picasso_key, picasso_value, 5);
            falcon::Inc("ad_impression_label_read_cnt", 1);
            if (ret) {
              falcon::Inc("ad_impression_label_match_cnt", 1);
              exist = true;
            }
          }

          //// step 4. 用redis_ext_data 和 account维度的call_back_type 填充log，发出去

          // 读到，修改call_back_event
          if (exist) {
            AdJointLabeledLog * ad_joint_labeled_log =  new AdJointLabeledLog();
            if(!ad_joint_labeled_log->ParseFromArray(picasso_value.data(),picasso_value.size())){
              falcon::Inc("parser_ad_joint_labeled_log_error");
              LOG(ERROR) << "parse ad_joint_labeled_log error !!!!";
              continue;
            }

            //LOG(INFO) << "ad_joint_labeled_log size : " << picasso_value.size();
            auto item = ad_joint_labeled_log->mutable_item(0);
            auto account_id = item->ad_dsp_info().creative().base().account_id();
            std::string call_back_key = ACCOUNT_ACTION_TYPE_PREFIX + "#act#accountid#" + std::to_string(account_id);
            kuaishou::ad::algorithm::CallBackType call_back_type;
            string call_back_key_str;
            bool ret = picassoBGet(DSP_TABLE_NEW_NAME, call_back_key, call_back_key_str);
            bool is_conv_data = false;
            bool is_only_conv = IsShuffleFlowOnlyConv();
            if (ret) {
              if (!call_back_type.ParseFromArray(call_back_key_str.data(), call_back_key_str.size())) {  // 解析失败处理
                falcon::Inc("parser_call_back_type_error");
                LOG(ERROR) << "parse call_back_type error !!!!";
              } else {
                //callback中是否包含激活
                const auto & action_types = call_back_type.callback_event();
                for ( size_t i = 0; i < action_types.size(); i++ ) {
                  auto type = action_types[i];
                  if (type == kuaishou::ad::AdCallbackLog::EVENT_CONVERSION) {
                    is_conv_data = true;
                  }
                }
              }
            } 
            if (is_ext){
              //LOG(INFO) << "ad_log_redis_found size : " << redis_ext_data.callback_ext_datas.size();
              //for(AdCallbackExtData callback_ext_data : redis_ext_data.callback_ext_datas){
              if(redis_ext_data.item_click == 1){
                falcon::Inc("ad_log_out_out_item_click_cnt", 1);
                item->mutable_label_info()->set_item_click(true);
              }
              if(redis_ext_data.item_impression == 1){
                falcon::Inc("ad_log_out_out_item_impression_cnt", 1);
                item->mutable_label_info()->set_item_impression(true);
              }
              if(redis_ext_data.item_click == 1 || redis_ext_data.item_impression == 1){
                falcon::Inc("ad_log_out_out_item_click&impression_cnt", 1);
              }
              item -> mutable_ad_dsp_info() -> mutable_call_back_type() -> CopyFrom(call_back_type);

              // > 0 至少有一个callback （unknow and conversion）
              if(redis_ext_data.callback_ext_datas.size() > 0){
                if(redis_ext_data.IsCallback()){
                  for(AdCallbackExtData callback_ext_data : redis_ext_data.callback_ext_datas){
                    if(callback_ext_data.IsCallback()){
                      //AdCallbackExtData callback_ext_data = redis_ext_data.callback_ext_datas[0];
                      item -> mutable_label_info()->set_callback_event(callback_ext_data.action_type);
                      item -> mutable_label_info()->set_purchase_amount(callback_ext_data.purchase_amount);
                      item -> set_delayed_time(callback_ext_data.delayed_time);
                      falcon::Inc(("ad_log_out_out_callback_type_"+to_string(callback_ext_data.action_type)+"_cnt").c_str(), 1);
                      break;
                    }
                  }
                } else {
                  for(AdCallbackExtData callback_ext_data : redis_ext_data.callback_ext_datas){
                    if(!callback_ext_data.IsCallback()){
                      //AdCallbackExtData callback_ext_data = redis_ext_data.callback_ext_datas[0];
                      item -> mutable_label_info()->set_callback_event(callback_ext_data.action_type);
                      item -> mutable_label_info()->set_purchase_amount(callback_ext_data.purchase_amount);
                      item -> set_delayed_time(callback_ext_data.delayed_time);
                      falcon::Inc(("ad_log_out_out_callback_type_"+to_string(callback_ext_data.action_type)+"_cnt").c_str(), 1);
                      break;
                    }
                  }
                }
                //LOG(INFO) << "ad_log_callbak_type_"+to_string(callback_ext_data.action_type);
              } else {
                falcon::Inc("ad_log_out_out_callback_none_cnt", 1);
                LOG(INFO)<<"redis_miss:"<<key
                    <<";run_time:"<<run_time
                    <<";log_time:"<<(ad_joint_labeled_log->time() / 1000)
                    <<";delay_time:"<<(run_time - ad_joint_labeled_log->time() / 1000)
                    <<";item_click:"<<item->mutable_label_info()->item_click()
                    <<";item_impression:"<<item->mutable_label_info()->item_impression();
              }
              if (is_only_conv){
                if (is_conv_data){
                  shuffle_buffer->push(ad_joint_labeled_log);
                  falcon::Inc("ad_log_out_out_cnt", 1);
                } else {
                  delete ad_joint_labeled_log;
                  falcon::Inc("ad_log_out_noconv_cnt", 1);
                }
              } else {
                shuffle_buffer->push(ad_joint_labeled_log);
                falcon::Inc("ad_log_out_out_cnt", 1);
              }
              
              //}
            } else {
              falcon::Inc("ad_log_out_faild_redis_nofound_cnt", 1);
              LOG(INFO)<<"redis_miss:"<<key
                  <<";run_time:"<<run_time
                  <<";log_time:"<<(ad_joint_labeled_log->time() / 1000)
                  <<";delay_time:"<<(run_time - ad_joint_labeled_log->time() / 1000)
                  <<";item_click:"<<item->mutable_label_info()->item_click()
                  <<";item_impression:"<<item->mutable_label_info()->item_impression();
            }
            falcon::Inc("ad_log_out_success_picasso_cnt", 1);
          } else {
            falcon::Inc("ad_log_out_faild_picasso_nofound_cnt", 1);
            LOG(INFO)<<"picasso_miss:"<<picasso_key;
          }
        }
      }
      run_time++;
    }
  };

  //读取kafka,预测 并写入redis
  std::thread *predict_threads = new std::thread[FLAGS_num_threads_predict];
  // 从redis读 key 写kafka
  std::thread *delay_threads = new std::thread[FLAGS_num_threads_delay];

  for (size_t i = 0; i < FLAGS_num_threads_predict; ++i) {
    predict_threads[i] = std::thread(predict_func);
    LOG(INFO) << "+ thread predict_func";
  }
  for (size_t i = 0; i < FLAGS_num_threads_delay; ++i) {
    delay_threads[i] = std::thread(delay_func);
    LOG(INFO) << "+ thread delay_func";
  }

  for (size_t i = 0; i < FLAGS_num_threads_predict; ++i) {
    predict_threads[i].join();
    LOG(INFO) << "thread predict_func join";
  }
  for(size_t i = 0; i < FLAGS_num_threads_delay; ++i){
    delay_threads[i].join();
    LOG(INFO) << "thread delay_func join";
  }

  return 0;
}
