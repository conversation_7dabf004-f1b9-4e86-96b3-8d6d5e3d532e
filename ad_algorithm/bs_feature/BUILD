import os
import sys
sys.path.append(r'teams/ad/ad_algorithm')
from util import generate_feature_build_path
enable_kuiba = os.getenv('ENABLE_KUIBA', 'TRUE')
enable_bs_fast_feature = os.getenv('ENABLE_BS_FAST_FEATURE', 'FALSE')
kuiba_deps = [
    "//ks/reco_proto/proto/BUILD:proto",
    ]
kuiba_flag = ""
exclude_kuiba_srcs = []

if enable_kuiba == 'TRUE':
  kuiba_deps = [
        "//ks/krp/c++/kuiba/reco_ad_model_server/reco_feature/BUILD:sign_feature",
      ]
  kuiba_flag = "-D ENABLE_KUIBA"
  exclude_kuiba_srcs = []

bs_fast_fea_flag = "" 
if enable_bs_fast_feature == 'TRUE':
  bs_fast_fea_flag = "-D ENABLE_BS_FAST_FEATURE"

debug_info_level = os.getenv("FEATURE_DEBUG_INFO_LEVEL", '-1')
debug_info_flag = ""
if debug_info_level != '-1':
  debug_info_flag = "-g" + debug_info_level
print("Debug info level: {}".format(debug_info_flag))

enable_ad_bs_both_flag = ""
if os.getenv("ENABLE_AD_BS_BOTH", "FALSE").lower() in ['true', '1']:
    enable_ad_bs_both_flag = "-D ENABLE_AD_BS_BOTH"

remap_bs_field_enum = os.getenv("REMAP_BS_FIELD_ENUM", 'false').lower() in ['true', '1']

remap_bs_field_enum_flag = []
if remap_bs_field_enum:
   remap_bs_field_enum_flag = ["-D REMAP_BS_FIELD_ENUM"]
   print("flag: ", remap_bs_field_enum_flag)

# 此参数用于选择特征进行编译
sub_feature_path = os.getenv("SUB_FEATURE_PATH", "")
# 自动创建分组, 并软链至teams/ad/ad_algorithm/bs_feature/fast/impl_group/bs_feature_def.cc
assert os.system(
    '[ $(grep "^#include" -c %s) -lt 2000 ]' %
    'teams/ad/ad_algorithm/bs_feature/fast/impl_group/bs_feature_def.cc'
) == 0, ("fast/impl_group/bs_feature_def.cc include 文件过多,为避免编译耗时劣化,"
         "请执行 python3 teams/ad/ad_algorithm/bs_feature/fast/impl_group/create_new_group.py , 创建新的分组")


proto_library(
  name = 'bs_common_info_attr_enum_pb',
  srcs = [
    "fast/proto/bs_common_info_attr_enum.proto",
  ],
  deps = [
    "//third_party/protobuf_v3/BUILD:protobuf",
  ],
)

proto_library(
  name = 'bs_sim_pb',
  srcs = [
    "fast/sim_proto/*.proto",
  ],
  deps = [
    "//third_party/protobuf_v3/BUILD:protobuf",
  ],
)

cc_library(name = "bs_fea_util",
           srcs = [
                   "fast/frame/bs_info_util.cc",
                   "fast/frame/bs_info_static.cc",
                   "fast/frame/bs_leaf_util.cc",
                   "fast/frame/bs_action_util.cc"
                   ],
           hdrs = [
                  ],
           deps = [
                   ":bs_sim_pb",
                   ":bs_common_info_attr_enum_pb",
                   "//teams/ad/ad_algorithm/feature_interface/BUILD:interface",
                   "//serving_base/jansson/BUILD:json",
                   "//base/common/BUILD:base",
                   "//base/strings/BUILD:strings",
                   "//base/thread/BUILD:thread",
                   "//third_party/abseil/BUILD:abseil",
                   "//teams/ad/ad_target_search/BUILD:query_utils",
                   "//third_party/flatbuffers/BUILD:flatbuffers",
                   "//teams/ad/ad_nn/feature_extract/BUILD:ad_extract_framework",
                   "//teams/ad/ad_nn/feature_extract/BUILD:bs_wrapper",
                   "//teams/ad/ad_nn/bs_field_helper/BUILD:bs_field_helper",
                   "//infra/redis_proxy_client/BUILD:redis_client",
                   "//teams/reco-arch/colossus/BUILD:client",
                   "//third_party/folly/BUILD:folly"
                  ],
           cppflags = [
             "-Ithird_party/flatbuffers/include/"
           ] + remap_bs_field_enum_flag,
           link_all_symbols = True,
           )

feature_impl_deps = [
    ":bs_sim_pb",
    ":bs_common_info_attr_enum_pb",
    ":bs_fea_util",
    "//teams/ad/ad_algorithm/feature_interface/BUILD:interface",
    "//teams/ad/ad_nn/model/BUILD:lru",
    "//serving_base/ip/BUILD:ip",
    "//serving_base/region/BUILD:region",
    "//serving_base/location/BUILD:location",
    "//serving_base/jansson/BUILD:json",
    "//base/common/BUILD:base",
    "//base/strings/BUILD:strings",
    "//base/thread/BUILD:thread",
    "//base/hash_function/BUILD:hash_function",
    "//third_party/abseil/BUILD:abseil",
    "//teams/ad/picasso/BUILD:picasso_sdk",
    "//teams/ad/picasso/BUILD:picassobatch_sdk",
    "//teams/ad/ad_algorithm/log_preprocess/BUILD:ad_model_kconf_util",
    "//teams/ad/ad_proto/kuaishou/BUILD:picasso_update_proto",
    "//teams/ad/ad_base/src/geohash/BUILD:location",
    "//teams/ad/ad_target_search/BUILD:query_utils",
    "//third_party/flatbuffers/BUILD:flatbuffers",
    "//teams/ad/ad_nn/feature_extract/BUILD:sample_interface",
    "//teams/ad/ad_nn/bs_field_helper/BUILD:bs_field_helper",
    "//infra/redis_proxy_client/BUILD:redis_client",
    "//teams/reco-arch/colossus/BUILD:client",
    "//third_party/folly/BUILD:folly",
    "//teams/ad/ad_algorithm/fed/BUILD:bslog_field_getter",
    "//teams/ad/ad_algorithm/fed/BUILD:compute",
    "//teams/ad/ad_algorithm/label_centre/BUILD:label_centre",
    "//teams/ad/ad_algorithm/feature_interface/BUILD:common_flags",
    "//teams/ad/ad_nn/flatten_raw_feature/BUILD:flatten_helper",
  ] + kuiba_deps

cc_library(
    name = "bs_fast_feature_group",
    srcs = [
        "fast/impl_group/groups/*.cc",
        "fast/impl_group/init_bs_feature_groups.cc",
        "fast/impl_offline/*.cc",
    ],
    # 软连接, 已经在 groups 中被编过
    excludes = ["fast/impl_group/bs_feature_def.cc"],
    deps = feature_impl_deps,
    cppflags = [
        kuiba_flag,
        bs_fast_fea_flag,
        enable_ad_bs_both_flag,
        "-Ithird_party/flatbuffers/include/",
        debug_info_flag,
    ] + remap_bs_field_enum_flag,
    link_all_symbols = True
)

cc_library(name = "bs_fast_feature_base",
    srcs = [
        "fast/frame/bs_fast_feature.cc",
        "fast/frame/bs_common_info_attr_feature_factory.cc",
        "fast/frame/bs_common_info_attr_feature.cc",
        "fast/frame/bs_account_id_visitor_manager.cc",
        "fast/bs_log.cc"],
    hdrs = [],
    deps = feature_impl_deps,
    cppflags = [
        kuiba_flag,
        bs_fast_fea_flag,
        enable_ad_bs_both_flag,
        "-Ithird_party/flatbuffers/include/"
    ] + remap_bs_field_enum_flag,
    link_all_symbols = True,
)


bs_feature_list = []
bs_hdrs = []
bs_feature_list_dep = [":bs_fast_feature_group"]

if sub_feature_path != "":
    bs_feature_list_dep = []
    bs_feature_list = []
    generate_feature_build_path(sub_feature_path, bs_hdrs ,bs_feature_list, True)

cc_library(name = "bs_fast_feature",
    hdrs = bs_hdrs,
    srcs = bs_feature_list,
    deps = [":bs_fast_feature_base"] + bs_feature_list_dep,
    cppflags = [kuiba_flag],
    link_all_symbols = True,
)

cc_library(name = "bs_log",
           srcs = [
                   "fast/bs_log.cc",
                  ],
           hdrs = [
                  ],
           deps = [
                   ":bs_sim_pb",
                   "//teams/ad/ad_algorithm/feature_interface/BUILD:interface",
                   "//teams/ad/ad_nn/model/BUILD:lru",
                   "//serving_base/ip/BUILD:ip",
                   "//serving_base/region/BUILD:region",
                   "//serving_base/location/BUILD:location",
                   "//serving_base/jansson/BUILD:json",
                   "//base/common/BUILD:base",
                   "//base/strings/BUILD:strings",
                   "//base/thread/BUILD:thread",
                   "//third_party/abseil/BUILD:abseil",
                   "//teams/ad/picasso/BUILD:picasso_sdk",
                   "//teams/ad/picasso/BUILD:picassobatch_sdk",
                   "//teams/ad/ad_proto/kuaishou/BUILD:picasso_update_proto",
                   "//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_full_goods_action__proto",
                   "//teams/ad/ad_base/src/geohash/BUILD:location",
                   "//teams/ad/ad_target_search/BUILD:query_utils",
                   "//third_party/flatbuffers/BUILD:flatbuffers",
                   "//teams/ad/ad_nn/feature_extract/BUILD:sample_interface",
                   "//teams/ad/ad_nn/bs_field_helper/BUILD:bs_field_helper",
                   "//infra/redis_proxy_client/BUILD:redis_client",
                   "//teams/reco-arch/colossus/BUILD:client",
                  ] + kuiba_deps,
           cppflags = [bs_fast_fea_flag,
                       enable_ad_bs_both_flag,
                       "-Ithird_party/flatbuffers/include/",
                      ],
           )

cc_library(name = "bs_meta",
           hdrs = ["fast/frame/bs_meta.h",
                 ],
           srcs = ["fast/frame/bs_meta.cc"],
           cppflags = [
             "-Wno-unused-variable",
             "-Wno-unused-parameter",
             "-Wno-unknown-pragmas",
             "-Wno-unused-local-typedefs",
             "-D GOOGLE_LOGGING=1",
           ],
           )
