#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/init_bs_feature_groups.h"

#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_8.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_0.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_1.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_3.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_4.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_5.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_6.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_7.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_8.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_9.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_10.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_11.h"

namespace ks {
namespace ad_algorithm {

bool register_all_bs_feature_groups() {
  register_bs_feature_group_8();
  register_bs_feature_group_0();
  register_bs_feature_group_1();
  register_bs_feature_group_2();
  register_bs_feature_group_3();
  register_bs_feature_group_4();
  register_bs_feature_group_5();
  register_bs_feature_group_6();
  register_bs_feature_group_7();
  register_bs_feature_group_9();
  register_bs_feature_group_10();
  register_bs_feature_group_11();
  return true;
}

}  // namespace ad_algorithm
}  // namespace ks

#endif
