import os
import re


origin_file = "teams/ad/ad_algorithm/bs_feature/fast/impl_group/bs_feature_def.h.back"

header_common = """
#include <string>
using std::string;"""

cc_common = '#include "third_party/abseil/absl/strings/str_split.h"'

def_begin = """

#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)
"""
def_end = """
#endif
"""
namespace_end = """
}  // namespace ad_algorithm
}  // namespace ks
"""
namespace_begin = """
namespace ks {
namespace ad_algorithm {

"""

def create_group(data, idx):
    root_dir = "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups"
    if not os.path.isdir(root_dir):
        os.makedirs(root_dir)
    header = f"{root_dir}/bs_feature_group_{idx}.h"
    
    with open(header, "w") as f:
        f.write(
            f"#pragma once\n{header_common}"
            f"{def_begin}{namespace_begin}"
            f"void register_bs_feature_group_{idx}();\n"
            f"{namespace_end}{def_end}")
    with open(f"{root_dir}/bs_feature_group_{idx}.cc", "w") as f:
        f.write(f'#include "{header}"')
        f.write(f"{def_begin}\n{cc_common}\n")
        f.writelines(data)
        f.write(f"{namespace_begin}\nvoid register_bs_feature_group_{idx}() {{}}\n")
        f.write(f"{namespace_end}{def_end}")
        # 测试代码，让缓存不命中
        # f.write(f"static int aaa = 122;\n")

def create_group_init(number):
    root_dir = "teams/ad/ad_algorithm/bs_feature/fast/impl_group"
    header = f"{root_dir}/init_bs_feature_groups.h"
    cc = f"{root_dir}/init_bs_feature_groups.cc"
    
    with open(header, "w") as f:
        f.write(
            "#pragma once"
            f"{def_begin}{namespace_begin}"
            "bool register_all_bs_feature_groups();"
            f"{namespace_end}{def_end}"
        )
    # include header , 所有group.h, 并在 register_all_bs_feature_groups 调用 register_bs_feature_group_*()
    group_includes = [ f'#include "{root_dir}/groups/bs_feature_group_{i}.h"\n'  for i in range(number)]
    call_groups = [ f'  register_bs_feature_group_{i}();\n'  for i in range(number)]
    with open(cc, "w") as f:
        f.write(f'#include "{header}"')
        f.write(def_begin)
        f.writelines(group_includes)
        f.writelines((
            namespace_begin,
            "bool register_all_bs_feature_groups() {\n"))
        f.writelines(call_groups)
        f.write(f"  return true;\n}}\n {namespace_end}{def_end}")
    link_dest = f"{root_dir}/bs_feature_def.cc"
    if os.path.lexists(link_dest):
        os.unlink(link_dest)
    os.symlink(f"groups/bs_feature_group_{number-1}.cc", link_dest, target_is_directory=False)

# 几个耗时比较长的，单独分一组
# 以及部分头文件手动分组 
long_group = {
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_live_action_feature.h": 0,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_photo_action_feature.h": 0,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_ecomm_gsu_sep.h": 0,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_seq_item_hash_id.h": 0,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_backflow.h": 0,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_realtime_new_extend.h": 0,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_match_cnt_v2.h": 0,

    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_top_action_list_sim_inc_cut_v5.h": 0,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_top_action_list_sim_inc_cut_v2.h": 0,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_for_photo_v1.h": 0,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_inc_cut_join.h": 0,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_realtime_action_list_match_n_detail.h": 0,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_inner_ad_creative_type.h": 0,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_author_fans_detail.h": 0,

    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_picasso.h": 1,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_picasso_count.h": 1,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_realtime_seq_lp_no_dup.h": 1,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_graphsim.h": 1,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_app_active_info.h": 1,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_top_action_list_sim_inc_cut_v5.h": 1,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_top_action_list_sim_inc_cut_v2.h": 1,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_for_photo_v1.h": 1,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_inc_cut_join.h": 1,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_realtime_action_list_match_n_detail.h": 1,

    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_match_num_long_merge.h": 2,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_tag_prior.h": 2,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_gsu_deep_ret.h": 2,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu.h": 2,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_match_cnt.h": 2,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_click_rewarded.h": 2,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_mmu_item_click_match_cnt.h": 2,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_mmu_p5s_match_cnt.h": 2,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_play5s_more_combine_new.h": 2,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_cross.h": 2,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_play_lt1s_match.h": 3,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_lsp_segment_info.h": 3,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_4_label.h": 6,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_list.h": 6,
    "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_recruit_live_jobs_feature.h": 6,

}

long_group_data = []

def main():
    with open(origin_file, "r") as f:
        all_groups = [[] for _ in range(10)]
        # 前两个组, 放几个耗时比较长的文件, idx从2开始
        group_idx, group_line_number = 2, 0
        cur_includes = all_groups[group_idx]
        added_long = set(long_group.keys())
        # include 
        added = set()
        for line in f:
            if line.startswith("#include"):
                include_file = re.search(r'"(.+)"', line).group(1)
                if include_file in added:
                    print("重复include", include_file)
                    continue
                added.add(include_file)
                ret = os.popen(f'grep "^\s*$" -E -c -v {include_file}').read()
                line_number = int(ret.split()[0])
                # if line_number > 300:
                #     long_group_data.append((line, line_number))
                #     continue
                # 不过是不是独立的分组都计算行数
                # 避免手动调整分组后，number 改变，导致顺序乱掉
                group_line_number += line_number
                if include_file in long_group:
                    all_groups[long_group[include_file]].append(line)
                    # all_groups[long_group[include_file]].append(f"// {ret}")
                    added_long.remove(include_file)
                    continue
                cur_includes.append(line)
                # cur_includes.append(f"// {ret}")
            else:
                cur_includes.append(line)

            if group_line_number > 20000:
                print(group_idx, group_line_number)
                group_idx += 1
                cur_includes = all_groups[group_idx]
                group_line_number = 0
        # cur_includes = []
        # group_line_number = 0
        # for data, number in long_group_data:
        #     if group_line_number + number > 6000:
        #         group_line_number = 0
        #         all_groups.insert(0, cur_includes.copy())
        #         cur_includes.clear()
        #     cur_includes.append(data)
        #     cur_includes.append(f"// {number}\n")
        #     group_line_number += number
        # all_groups.insert(0, cur_includes.copy())
        print(added_long)
        for idx, data in enumerate(all_groups):
            if not data:
                continue
            create_group(data, idx)
        create_group_init(group_idx + 1)



if __name__ == "__main__":
    main()