import os
import logging
import subprocess


def get_logger():
    logger_ = logging.getLogger("check_group")
    logger_.setLevel(level=logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s-%(name)s-%(levelname)s: %(message)s')
    console = logging.StreamHandler()
    console.setFormatter(formatter)
    console.setLevel(logging.INFO)
    logger_.addHandler(console)
    return logger_


logger = get_logger()


def_begin = """

#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)
"""
def_end = """
#endif
"""
namespace_end = """
}  // namespace ad_algorithm
}  // namespace ks
"""
namespace_begin = """
namespace ks {
namespace ad_algorithm {

"""

class CheckGroup:
    def __init__(self) -> None:
        self.group_prefix = 'bs_feature_group_'
        self.current_group = 'bs_feature_def.cc'
        self.init_all_group = 'init_bs_feature_groups.cc'
        self.group_root = 'groups'
        # 单个header最多运行include的数量
        self.max_size = 200
        self.root_dir = "teams/ad/ad_algorithm/bs_feature/fast/impl_group"

    def create_new_group(self):
        # 新增一个group
        cc_group = ''
        header_group = ''
        # 设置一个最大值避免死循环
        max_group_num = 1000
        suffix = 1
        while suffix < max_group_num:
            cc_group = f'{self.group_root}/{self.group_prefix}{suffix}.cc'
            header_group = f'{self.group_root}/{self.group_prefix}{suffix}.h'
            if not os.path.exists(cc_group) and not os.path.exists(header_group):
                break
            suffix += 1
        if suffix > max_group_num or os.path.exists(cc_group) or os.path.exists(header_group):
            logger.error(f"创建 {cc_group} 失败")
            return

        header_data = (
            "#pragma once\n"
             f"{def_begin}{namespace_begin}"
            f"void register_bs_feature_group_{suffix}();\n"
            f"{namespace_end}{def_end}"
        )
        cc_data = (
            f'#include "{self.root_dir}/{header_group}"'
            f"{def_begin}{namespace_begin}// auto create for register\n"
            f"void register_bs_feature_group_{suffix}() {{}}\n"
            f"{namespace_end}{def_end}"
        )
        with open(header_group, "w", encoding="utf-8") as f:
            f.write(header_data)

        with open(cc_group, "w", encoding="utf-8") as f:
            f.write(cc_data)

        if not self.insert_register_func(header_group, suffix):
            return
        self.change_link(cc_group)

    def insert_register_func(self, header_group, suffix) -> bool:
        # 向 init_all_group 插入header、register

        # 插入include
        insert_cmd = f"sed '4a\#include \"{self.root_dir}/{header_group}\"' -i {self.init_all_group}\n"
        # 向函数中插入调用
        line_pattern = '^bool register_all_bs_feature_groups() {'
        insert_data = f"  register_bs_feature_group_{suffix}();"
        insert_cmd += f"sed '/{line_pattern}/a\{insert_data}' -i {self.init_all_group}"
        ret, output = subprocess.getstatusoutput(insert_cmd)
        if ret != 0:
            logger.error(f"插入 include 失败: {output}")
        return bool(ret == 0)

    def change_link(self, cc_group):
        # 修改 current_group 的软链
        cmd = f"ln -sf {cc_group} {self.current_group}"
        ret, output = subprocess.getstatusoutput(cmd)
        if ret != 0:
            logger.error(f"{cmd} 执行失败: {output}")

    def check_curent_group(self):
        os.chdir(self.root_dir)
        ret, data = subprocess.getstatusoutput(f'grep "^#include" {self.current_group} -c')
        if ret not in (0, 1):
            logger.error("自动分组失败")
            return
        include_size = int(data)
        if include_size > self.max_size:
            logger.info(f"include 数量为: {include_size} 超出 {self.max_size}, 自动创建新的group")
            self.create_new_group()


if __name__ == "__main__":
    CheckGroup().check_curent_group()