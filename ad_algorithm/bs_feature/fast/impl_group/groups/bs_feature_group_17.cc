
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_17.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_embedding_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_cost_total.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_cost_user_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_day_cancel_collect_cnt_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_day_cancel_like_photo_cnt_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_day_collect_cnt_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_day_complete_play_cnt_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_day_dislike_photo_cnt_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_day_like_photo_cnt_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_day_long_time_play_cnt_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_day_send_damaku_cnt_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_day_short_time_play_cnt_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_day_show_cnt_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_day_tube_cover_click_cnt_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_day_tube_cover_show_cnt_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_day_valid_play_duration_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_cancel_collect_cnt_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_cancel_collect_cnt_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_cancel_like_photo_cnt_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_cancel_like_photo_cnt_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_collect_cnt_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_collect_cnt_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_complete_play_cnt_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_complete_play_cnt_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_dislike_photo_cnt_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_dislike_photo_cnt_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_like_photo_cnt_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_like_photo_cnt_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_long_time_play_cnt_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_long_time_play_cnt_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_send_damaku_cnt_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_send_damaku_cnt_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_short_time_play_cnt_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_short_time_play_cnt_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_show_cnt_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_show_cnt_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_tube_cover_click_cnt_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_tube_cover_click_cnt_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_tube_cover_show_cnt_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_tube_cover_show_cnt_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_valid_play_cnt_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_valid_play_cnt_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_valid_play_duration_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_days_valid_play_duration_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tube_score.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_update_time.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_day_valid_play_cnt_1.cc"   // NOLINT
#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

namespace ks {
namespace ad_algorithm {

void register_bs_feature_group_17() {}

// auto create for register

}  // namespace ad_algorithm
}  // namespace ks

#endif
