#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_7.h"

#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "third_party/abseil/absl/strings/str_split.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_like_author_id_newhash.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_event_type_product_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_fin.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conv_extend_fin.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_credit_extend.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_fin.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_fin_with_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_fin_days.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_lps_fin.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_no_click_fin.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_no_play3s_fin.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_jinjian_extend_short.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_jinjian_extend.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_fin.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_pay_extend_fin.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_fin.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_third_ecom_with_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_first_cate_with_passed_day.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top3_click.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_realtime_action_list_sim_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sdpa_cross_product_name_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sdpa_cross_product_name_dense_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_industry_merge_recall_context_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sdpa_ecom_product_kwds.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sdpa_ecom_user_action_kwds.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_item_impression_ad_realtime.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_last_item_impression_mingap_realtime.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_action_picasso.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_apm_goods_action_seq_picasso.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_apm_goods_action_sample_info.h"
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_action_spu_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_price_4_label.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_creative_type_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_special_item.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_yellowtrolley_item.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_action_spu_list.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_p_author_univ.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_p_author_avg_explore_ratio.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_author_media_app_avg_explore_ratio.h"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_author_medium_industry_avg_explore_ratio.h"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_p_medi_style_univ.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_spu_attr.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_spu_action_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_cnt.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mixup_dense_callback_event_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mixup_dense_callback_event.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mixup_dense_ocpc_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_photo_field_id_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo_field_id_list.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_uni_user_merchant_flag.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_is_recruit_live.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_recruit_live_author_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_field_id_list_new.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_goods_feature_int_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_goods_feature_int.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_goods_feature_float_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_goods_gsu_feature.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_delivery.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_item_impression.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_item_click.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_mmu_cates.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_sdpa_novel_info.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_tag_list_order_paid_combine.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_akg_cluster.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_new_neg.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_akg_indus_user_ad_interest_seq.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_rank_cpm_info_v3.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_topk_industry_sequence_matched.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_author_fans.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_info_feature.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_spu_v3_emb.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_id_no_prefix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_dense_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_dense_spuid.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_recall_product_name_embedding_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_industry.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_tag_category_new_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_sim_action_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_goods_gsu_feature_v1.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_product_deep_conv_medium.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_attribute.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_swing.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ocpc_action_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_sequence.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_hist_action_emb.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dsplive_realtime_action_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_style_server_lp_sequence_share_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_short_play_stat.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_subtitle_segment.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_thanos_play_rate_stat.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_tag_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_item_dynamic_style_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate3_id_longterm.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate1_id_longterm.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_author_fusion.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_negative_author_id.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_delay_conversion_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_delay_delivery_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_delay_label.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_delay_p90_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_delay_pctr.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_delay_pcvr.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_category_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_datetime_natural_day_event_pay_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_datetime_hour_till_natural_day_share.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_datetime_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_action_list_add.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_match_add.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_match_add_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_outside_mmu_ids.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_extend_info.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_app_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_goods_gsu_feature_topk_target.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_realtime_seq_no_dup_share_opt.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_account_id_dense.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_action_type_dense_new.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_new_industry_v3_dense_outside.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_rewarded_pageid_new.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_product_name_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reco_user_click_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_live_newlands_tag_idx_emb_feat.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_live_newlands_sparse_feat.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rank_quota_with_top_cpm_outer.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_phd_play_ids.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_actions_prefix.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_item_price.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_item_keyword.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_user_interact_feature.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_histaction_firstidv3_seq.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_histaction_pdct_seq.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_histaction_secondidv3_seq.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_2_user_up_emb.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_attr_list_fix.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtb_flag.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_traffic_boost_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_conv_scene_flag.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_newlands_click_top_emb.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_newlands_click_tail_emb.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_invoke_product_name_each_dense_v3.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_landingpage_realtime_action_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_realtime_new_extend_ict3.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_follow_ocpc_action_type_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_follow_ocpc_action_type_dense_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_search_query_statistical.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_search_query_statistical_prefix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_search_query_ids_data.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mgi_e2e_photo_emb.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_follow_ocpc_action_type_dense_four.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dsp_ecom_count_fea.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_eshop_embedding_spu.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_item_click_list_30d.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_cpm.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_cpm_dense.cc"    // NOLINT


#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_outer_inner_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_outer_inner_feature_float.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_info_4_label.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_info_4_label.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_shop_item_detail_info_v1.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_akg_user_graph_emb.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_conv.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_conv_fix.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_item_clk_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_item_clk_v2_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_match_dense_num_14d.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_search_feature_emb.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_merchant_photo_id_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_no_conv_ecom.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_merchant_photo_id_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_merchant_product_name_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_buy_30d_cat2_list_scale.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_commerce_buy_item_attr_list.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_commerce_comm_6m_cat3_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_commerce_like_6m_cat3_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_play7s_1d_second_cat_list_scale.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_industry_id_deep_action_90d.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_industry_id_shallow_action_30d.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_industry_id_shallow_action_3d.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_photo_id_shallow_action_30d.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_photo_id_shallow_action_7d.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_product_name_deep_action_90d.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_product_name_shallow_action_3d.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_15d_cat2_list_scale.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_reco_user_realtime_action_and_creative.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_longterm_photo_p5s.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_reco_user_realtime_action_and_mmu_class.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_eds_comp_item_invoke_list_ts.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_eds_comp_item_ped_list_ts.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_play7s_7d_third_cat_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_playend_1d_first_cat_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_playend_15d_third_cat_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_akg_photo_graph_emb.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_car_tag_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_reco_embedding_feed_uid.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_reco_embedding_single_uid.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_reco_embedding_feed_aid.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_reco_embedding_single_aid.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_page_id_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_view_component_type.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rank_ctcvr_scale_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_merchant_price.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_merchant_price_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_play_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action1_no_action2.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_act_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_indusid.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_photo_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_product_id.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ocpc_itemclick_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ks_flow_data_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_akg_hop5_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_akg_hop5_reverse_list.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_rtb_bid.cc"    // NOLINT
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_product_name_coldstart.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_sdpa_photo_cnt_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_ecom_action_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_sim_picasso.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_goods_tp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_jinniu_xiaodian_product_id_v2_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_callback_event.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_credit_profile_online.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_label_elapsed_hour.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_realtime_new_extend_ict.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_callback_action_product_feature_jinjian.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_conv_fin.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_merchant_sku_price.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_price_combine_sku.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_industry_group.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_education_class_info.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_financial_product_info.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_edu.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_edu.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_edu.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_smb_with_time.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_edu.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_edu.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_avegap.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_impression_avegap.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_ctr.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_scvr.cc"    // NOLINT



#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_style_server_card_material_feature.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_total_order_cnt.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_stat_ftr_new_flow.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_stat_ctr_new_flow.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dl_cluster.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_submit_buyer_uv.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_stat_fix.cc"    // NOLINT



#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_gauss_dropout_dense.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_interest_click_keywords.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_deviceid.cc"    // NOLINT



#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ocpc_action_type_encode_dense.cc"    // NOLINT



#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_landingpage_neg_realtime_action_list.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_follow_ocpx_action_type.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_item_ad_queue_type_new.cc"    // NOLINT

namespace ks {
namespace ad_algorithm {

void register_bs_feature_group_7() {}

}  // namespace ad_algorithm
}  // namespace ks

#endif
