#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_2.h"

#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "third_party/abseil/absl/strings/str_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_reco_feature_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_interactive_form.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_large_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_ecom_short_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_negative_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_app_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conversion_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_pay.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_purchase_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_short.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_author_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_lps_user_stats_30d.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_ecommerce_product_name.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_ctr_test.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_brand.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sjh_follow.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_click_card_seller_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_context_info_app.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_click_item_id_list_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_20wconv_ec_kwd_idxs.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_larger_author_photo.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_app_list_ad_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_feature2.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_report_count.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_face_ids.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_face_attribute.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_add_fans_cnt.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playedend_no_lps_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_pay_extend.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_detail_item_imp_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_16.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lpc_extend_with_time_real.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_search_pos_photo.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_price_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_cat_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_seq_item_hash_id_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_id_dense_v3.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_uri_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_20wspeech_ec_kwd_idxs.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_product_labels.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_pay_info.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_pay_info_dense.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_inc.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_app_new.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_consumption_level.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_simple_product.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_category_ad_degree_180d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_auto_cpa_bid_nebula.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_region.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_cnt_ts_form_submit.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_item_click_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_attribute_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_attribute.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_jinjian_extend_xiaodai_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_click_mat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_impression_rewarded.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_new_install_app_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_detail_form_submit.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_purchase_extend_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_photo_media_ad_style.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_invoke.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_base_uri_lps.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_product_conv_medium.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_fin_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_coin_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_coin_count_fix.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_has_yello_trolley.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_ecom_days.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_scvr.h"  //
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_comp_realtime_pe_mmu1_with_time.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_lps_fin.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_comp_realtime_click2_pn_with_time.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_cate1_id_scale.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_gambling_app_installed_num.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_info.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_author_fans.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dowload_industry_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_match.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_item_impression.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_ad_style_sub_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_action_nebula_v1.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_querytoken_ad_unit_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_match_dense_num.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_ecom_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_ecom_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_product_name.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_product_name_vacation_holiday.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_flash_duration.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_enhance_follow_author_htag_lv3.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_productids.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_cost.cc"  // NOLINT
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_recall_target_info.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_recall_target_info_v2.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_last_impression_mingap_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_lps_user_stats_7d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_simple_industry.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_merchant_list_online_item.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_user_rt_browse_type.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_virtual_creative_title_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_thanos_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_conversion.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_product_name.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_itemclick.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_fin_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click2_productIds.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_dislike_topic.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_embedding_play_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_browse_category_tag_sum.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_same_author_new.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_same_author_new.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_app_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_photo_id_shallow_action_3d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_ocpx_action_type.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_replayed.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_pay_info.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_ad_lda.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_detail_context_author_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_cover_feature_ctr_imp.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_app_package_size.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag_fix_v1.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_product_name_clk_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_union_dense_callback_event_train.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_interest_imp_keywords.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_media_third_class.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_lps_account_stats_30d.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_spuids.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_industry_id_shallow_action_3d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_no_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reco_user_like_list.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_click2_second_category_30d.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_video_feature_moco_conv_thanos.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_view_like_photo_label_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_slogan.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_ad_app_id_media.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_action_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_action_timestamp_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_action_list_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_action_timestamp_list_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_realtime_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_realtime_v3.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_realtime_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_realtime_v3.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_base_uri_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conversion.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_standard_live_room_seller_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_invoke_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_id.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_realtime_playend.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cpm_account_d7.h"
// // NOLINT
// need fix
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_available_disk_percent.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_cate3ids.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_dense_getui_data_extend.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_querytoken_ad_campaign_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_user_comp_ad_play5s_extend_photo_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_rate_7d_10s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_new_industry.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_item_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_context_info.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_photo_id_shallow_action_7d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cpa_bid.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_bid.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_interest_click2_keywords.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_union_dense_callback_event_infer.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_union_dense_adstyle.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_union_ecpm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_mmu_hetu_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_photo_mmu_hetu_tag.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_ecom_days.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longtermaction_and_industryid.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_callback_event_train.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_soureType.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top3_item_impression_realtime.h"  // NOLINT
// need fix #include
#include  "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_new_slot.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ecom_kuai_category_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_detail_ads_resource_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_negative_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_click_short.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_datetime_hour_week_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_industry_id_deep_action_90d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_past_app_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_count_feature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_new_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conv_extend_edu_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_item_id_list_short.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_eds_comp_product_name_cnt_fix.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_interest_categorys.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_paid_seller_id_list_scale.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_deep_conv_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_lda_photo_ad_lda.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_item_clk_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_realtime.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_wangzhuan.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_click_photo_product_sequence.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_active_level_from_comminfo.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_photo_face_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rewarded_candidate_queue_size.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_enhance_follow_author_htag_lv1.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_mmu_item_attr.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_p5s_v2_fix.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_credit_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_cname_2.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cpm_unit_d7.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_cnt_7d_1m.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_quality_filter_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shortterm_action_cnt_ts_item_imp.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_detail_context_pattern_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_query_token.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_order_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_order_count_fix.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_live_ave_gap.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_material_tag_cat.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_tag_sum.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_game.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_medium_attribute.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_app_list_product.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_callback_event128_lsp2appoint.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_playend_action_nebula_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_search_pos.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_app_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_conversion_industry_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_app_list_ad_app_id.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_channel_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_click_product_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_industry_id_deep_action_30d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_seller_clk.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_no_click_fin.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_callback_event_sparse.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_live_info_replayed.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_fin_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_retention_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_fin_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_fin.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_game.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_follow_htag_match.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_14.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_region_from_ip_v1.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_gender.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_ecom_days.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_cate2_id_gsu_item_id_list_longterm.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_text_feature_bert_conv_feed.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_installed_app_sequence_share.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_device_disk_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_adx_user_sourceType.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_effect_purchase_cpm_pos_d7.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_eds_comp_item_click_list_ts.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_photo_target_cpm_medium.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_search_pos_account_id.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_photo_media_pos.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_avg_play_rate_global.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_datetime_hour_week_brand.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_time_sequence_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_position_status.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_negative_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_imei_industry_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_description.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_ad_info_playend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_behav_stat_online.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_conversion_ratio.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_comp_click2_photo_id_list_scale.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_conv_medium_industry_ctr.cc"   // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_account_target_cpm_medium.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_device_info_ad_user.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_cate1_id_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_xiaodai_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_author_id_longterm.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_conv_medium_sub_industry_cvr.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_retention_media_id_product.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_deep_conversion_industry_d7.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_ped_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_uidcontextproduct.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_ocr_title.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_photo_impression_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_video_quality.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_conv_fin.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_negative_newhash.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_native_photo_item_impression_photo_id.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_product_name_pe.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_keyword_app_degree_7d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_negative_ad.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_ad_style.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_share_extend.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_knews_user_coin_level.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_ecom_short_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_to_live_author_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_714_app_installed_num.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_comp_realtime_click2_mmu2_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_last_click_mingap_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_photo_author_fans.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_id_media_purchase_property.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_nebula_ad_playend_extend.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_interests_label_response_uv_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_ad_info_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_num_extend_ecom_dense_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_num_extend_ecom_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo_buy_item_seller_id_list_short.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_photo_follow.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_medium_sub_industry_id.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_industry_ad_industry.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_creative_endcard_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_medium_vacation_holiday.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_exp_model.h"  //
// NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_download_new1.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_pos.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_car_tag_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_cate2_id_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_shallow_action_product_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_life_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_medium_industry_id.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_ecom_days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_querytoken_photo.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_item_id.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_universe_flag_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_universe_flag.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cpm_sec_industry_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_match_num_long_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_creative_material_dense_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_dup_cover_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_author_id_seq_longterm.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_play_ecom_cate2_v1_seq_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_enter_profile_photo.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_landing_jd_brand_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_15.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_yellow_cart_click_seller_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_to_live_photo_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_item_imp_v2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_photo_invoke.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_user_price_level_distribution_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_simple_industry.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_click_industry_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_playend_more_combine_new.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_feature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_impression_cnt_seven_day.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_text_feature_bert_imp_feed.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dsp_embedding.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_game_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_old_second_cate_list.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dup_photo_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_edu_with_time.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_rank_cpm_info.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_rank_cpm_info_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_rank_ctcvr_info.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_shallow_act_photo.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_photo_comment_game.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_like_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_like_count_fix.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_play3s_action_nebula_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_dup_cover_id.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_hetu_tag_l2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_play3s.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_industry_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_play3s_ratio.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_appcontextproduct.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_app_list_sequence_share.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_spu_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag_no_userid.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_active_apps.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_account_deep_conv_pos.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_total_memory.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dl_click_cluster.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_ad_campaign_type.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shortterm_action_cnt_ts_form_submit.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_has_yello_trolley.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_play_end_hetu_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_item_impr_7d_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_shop_keeper_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_bid_type_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_landing_jd_cate1_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_loc_advertiser.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_live_last_gap.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_name_media_online_property.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_comp_playend_product_id_list_scale.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_distance.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_browse_type.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_match_num_long.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_category_app_degree_7d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_followed_author_list_v2.cc"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_action_uv_submit_cpm_pos_d7.cc"    // NOLINT
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_photo_id_user_ad_lda.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_second_cate_with_passed_day.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_photo_bid_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_advertiser_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_info.cc"    // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_photo_slot.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_photo.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_callback_event_dense.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_reco_mat_embedding.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_stream_item_info.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_diff_app_list.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_creative_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_user_distance_tenkm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_search_pos_dup_cover_id.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_like_author_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_device_photo_new_v1.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click1_no_click2_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_apps_type.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_cooperation_mode_new.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_conv_medium_sub_industry_ctr_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_author_user_lda.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_like.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_item_impression_industry_ad_industry_realtime_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_item_impression_industry_ad_industry_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_tag_prior.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_gsu_deep_ret.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_click_rewarded.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_mmu_item_click_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_mmu_p5s_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_play5s_more_combine_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_cross.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_match_num_long_merge.h"

namespace ks {
namespace ad_algorithm {

void register_bs_feature_group_2() {}

}  // namespace ad_algorithm
}  // namespace ks

#endif
