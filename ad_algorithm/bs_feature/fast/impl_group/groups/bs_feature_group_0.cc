#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_0.h"

#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "third_party/abseil/absl/strings/str_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_live_action_feature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_photo_action_feature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_ecomm_gsu_sep.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_seq_item_hash_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_inner_ad_creative_type.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_author_fans_detail.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_realtime_new_extend.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_match_cnt_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_backflow.cc"    // NOLINT

namespace ks {
namespace ad_algorithm {

void register_bs_feature_group_0() {}

}  // namespace ad_algorithm
}  // namespace ks

#endif
