
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_16.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_slot_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_slot_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_chain_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_roi_2_storewide.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_active_app_list_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_install_app_list_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_main_tag_id_user_tag_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_main_tag_id_user_active_app_list_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_gender_user_gender.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_episode_user_gender.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_num_user_gender.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_view_count_user_gender.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_source_user_gender.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_biz_type_user_gender.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_gender.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_main_tag_id_user_gender.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_gender_user_age_seg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_episode_user_age_seg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_num_user_age_seg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_view_count_user_age_seg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_source_user_age_seg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_biz_type_user_age_seg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_age_seg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_main_tag_id_user_age_seg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tube_id_user_comp_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tube_id_user_long_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tube_id_user_like_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tube_id_user_collect_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tube_id_user_repoet_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tube_id_user_comp_id_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tube_id_user_long_id_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tube_id_user_like_id_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tube_id_user_collect_id_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tube_id_user_repoet_id_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_episode_user_tag_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_main_tag_id_user_tag_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_gender_user_tag_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_episode_user_tag_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_main_tag_id_user_tag_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_gender_user_tag_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_episode_user_tag_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_dense_atv.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_tag_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_like_photo_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_tube_bottom_bar_click_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_collect_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_valid_play_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_complete_play_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_report_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_cancel_collect_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_tube_share_task_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_dislike_photo_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_send_damaku_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_cancel_like_photo_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_tube_entrance_bottom_bar_click_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_tag_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_tube_play_photo_click_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_long_time_play_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_valid_play_duration_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_tag_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_tag_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_valid_play_duration_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_main_tag_id_user_install_app_list_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_long_time_play_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_play_photo_click_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_cancel_like_photo_cnt_lis.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_send_damaku_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_dislike_photo_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_share_task_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_cancel_collect_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_report_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_complete_play_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_valid_play_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_collect_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_bottom_bar_click_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_entrance_bottom_bar_click_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tag_id_list_user_like_photo_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_feed_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_feed_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_single_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_single_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_his_feed_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_his_feed_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_his_single_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_his_single_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_feed_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_feed_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_single_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_single_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_item_his_feed_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_item_his_feed_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_item_his_single_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_item_his_single_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_user_item_card_pay_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_user_item_card_pay_amount.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_crosspv_time.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_conversion_delivery_diff_whether_le_20_min.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_lc_rec_sid_match_first.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_lc_rec_sid_match_second.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_lc_rec_sid_match_three.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_lable_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_no_ltv_bid.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_outer_photo_is_aigc.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_pred_cpm.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_pred_fill_rate.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_dnc_score.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_is_live_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_more_author_all_merchant_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_more_author_id_user_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_chain_expand_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_tube_cover_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_gmv_static.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_gmv_avg_bucket.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_gmv_sum_bucket.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_order_spu_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_spu_price_std.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_spu_price_avg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_spu_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_store_wide.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_biz_center_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_order_biz_center_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_click_biz_center_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_photo_campaign_type_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sku_id_top_20_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sku_price_top_20_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_pctr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_plvtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_psvr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_pvtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_pwatchtime_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_pwtd_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_pcpr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_pltr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_pwtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_pftr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_pcmtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_phtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_pclick_live_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_p_effective_watch_live_time_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_pptr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_pepstr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_plstr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_petcm_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_pcmef_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pctr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_psvr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_plvtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pltr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pwtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pcmtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_phtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pin_lvtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pin_etr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_petr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pctetr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pctlvtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pfanstr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pgtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pnegtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pptr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_preportr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pshtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pvtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_inner_merge_top_layer_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_pctr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_plvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_psvr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_pvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_pwatchtime_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_pwtd_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_pcpr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_pltr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_pwtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_pftr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_pcmtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_phtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_pclick_live_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_p_effective_watch_live_time_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_pptr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_pepstr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_plstr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_petcm_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_frrank_uescore_pcmef_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_gmv_mean_15m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_gmv_mean_30m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_gmv_mean_60m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_gmv_std_30m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_gmv_std_15m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_gmv_rate_15m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_gmv_rate_30m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_llm_cid_l_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_llm_cid_l_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_llm_cid_l_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_llm_predict_top_cid_l_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_llm_predict_top_cid_l_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_llm_predict_top_cid_l_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_calibrated_cpm_score.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pctr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_psvr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_plvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pltr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pwtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pcmtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_phtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pin_lvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pin_etr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_petr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pctetr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pctlvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pfanstr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pgtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pnegtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pptr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_preportr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pshtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_frrank_uescore_live_pvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_account_dense_cid_offsite_order_cate_1_id_180d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_x_7_level_1_mmub.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_x_7_level_2_mmub.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_x_7_level_3_mmub.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_x_7_level_4_mmub.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_has_frrank_uescore.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_has_frrank_uescore_live.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_has_frrank_uescore_itemcontext.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_has_frrank_uescore_live_itemcontext.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_natural_share_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_natural_like_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_natural_collect_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_natural_download_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_natural_long_play_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_natural_comment_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_natural_follow_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_ad_wechat_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_ad_clk_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_ad_eca_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_5.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_8.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_9.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_10.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_11.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_12.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_13.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_14.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_15.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_16.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_17.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_18.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_19.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_20.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_21.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_22.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_23.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_24.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_25.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_26.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_28.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_29.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_b_30.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_5.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_8.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_9.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_10.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_11.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_12.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_13.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_14.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_15.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_16.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_17.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_18.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_19.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_20.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_21.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_22.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_23.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_24.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_25.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_26.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_28.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_29.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_adcu_text_g_c_30.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_ad_ls_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_ad_lps_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_ad_pm_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_id_user_activate_app.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_user_activate_app.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_user_install_app.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_id_user_install_app.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pri_msg_day_session_round_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pri_msg_first_message_type_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pri_msg_day_session_round.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pri_msg_first_message_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pri_msg_current_session_round_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pri_msg_current_session_round.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pri_msg_item_click_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cold_start_prm_leads_num_segment.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cold_start_wechat_num_segment.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_order_sku_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_order_gmv_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_match_lc_rec_item_g_v_fir.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_match_lc_rec_item_g_v_sec.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_match_lc_rec_item_g_v_thr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_match_lc_rec_sid_fir.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_match_lc_rec_sid_sec.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_match_lc_rec_sid_thr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_price_label.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cold_start_is_new_product_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cold_start_product_stage.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cold_start_is_new_creative_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cold_start_creative_stage.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cold_start_is_new_sdpa_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cold_start_sdpa_stage.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cold_start_is_new_corp_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_valid_message_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mix_context_pos_dense_onehot_train.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_user_llm_dpsk_valid_user.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_user_llm_exp_1_user.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_photo_llm_dpsk_photo_top_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_item_inner_cid_top_account.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_item_align_rqvae_id_p_2l.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_item_align_similar_p_2l.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_user_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_item_live_top_layer_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_user_live_action_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_product_name_discount_ratio.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mini_game_fix_roi_ratio.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_msg_clk_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_msg_clk_type_x_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_msg_clk_type_x_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_msg_cot.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_regular_place_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_home_place_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_work_place_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_residence_place_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_query_quant_seq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_live_uplift_ctr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_gmv_mean_15m_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_gmv_mean_30m_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_gmv_std_30m_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_gmv_rate_30m_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_yellow_car_avg_price_v_2_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_aigc_live_order_author_seq_30d_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_explore_sample_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_live_use_main_ctr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_live_p_2l_audience_elastic_c_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_live_p_2l_audience_elastic_c_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_msg_chat_stay_time.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_match_lc_recl_2_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_match_lc_recl_3_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_match_lc_recl_2l_3_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_user_live_action_real_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mixup_dense_photo_label_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_profile_label.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_account_maa_cost_level_90d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_account_is_first_cost_in_cur_m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_account_is_first_cost_in_cur_q.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_mix_uescore_live_ctr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_mix_uescore_live_etr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_mix_uescore_live_lvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_mix_uescore_live_in_etr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_mix_uescore_live_in_lvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_mix_uescore_live_ltr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_mix_uescore_live_wtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_mix_uescore_live_htr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_inner_trigger_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_inner_trigger_page.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_inner_trigger_position.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sku_price_top_20_list_rm_dup.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_pos_imp_rate_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_imp_rate_5pp_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_imp_rate_10pp_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_imp_rate_15pp_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_cpm_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_cpm_rate_5pp_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_cpm_rate_10pp_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_cpm_rate_15pp_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_inspire_search_query.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_dense_live_cvr_onemodel_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_sparse_live_cvr_onemodel_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_play_cnt_log_int_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_tube_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_complete_play_cnt_log_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_long_time_play_cnt_log_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_play_cnt_log_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_complete_play_cnt_log_int_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_main_tag_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_delivery_timestamp_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_label_val_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_long_time_play_cnt_log_int_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_callback_item_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_callback_event_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_visitor_lp_leave_time_7_day.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sku_price_top_20_list_rm_dup_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_dense_abtest_param_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_sparse_abtest_param_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_combine_query_to_ad_product_aggr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_action_semantic_id_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_action_semantic_id_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_action_semantic_id_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_action_semantic_id_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_id_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_fans_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_upload_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_whether_ks_or_fake_user.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_live_p_2l_audience_elastic_d_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_live_p_2l_audience_elastic_d_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_qcpx_dense_live_cvr_onemodel_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_qcpx_sparse_live_cvr_onemodel_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_search_order_hc.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_low_cpm_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_cpm_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_cot_matched_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ttube_day_valid_play_cnt_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_play_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_complete_play_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_inspire_ad_cost.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_cost_total_iaa_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_inspire_ad_pay_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_cpm_iap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_pay_cnt_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_iaa_action_show.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_cpm_iaa.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_cpm_iaa_17.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_cost_total_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_cost_total_iap_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_total_cost_li.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_iap_action_show_17.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_cost_total_iaa_17.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_iaa_action_show_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_description_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_pay_purchase_amt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_item_click_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_cost_total_iap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_actor_role.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_share_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_play_duration.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_manager_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_tube_source_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_actor_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_collect_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_activation_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_iaa_action_show_17.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_like_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ad_show_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_iap_action_show_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_cpm_iap_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_show_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_entity_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_iap_action_show.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_cost_total_iaa.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_create_time_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_item_show_cnt_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_comment_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_cpm_iap_17.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_cost_total_iap_17.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ad_photo_played_3s.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_director.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_producer.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_cpm_iaa_814.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_play_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_long_play_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ks_cost_total.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_conversion_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_valid_action_semantic_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_valid_action_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_action_sequence_semantic_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_action_sequence_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_search_pos_v_1.cc"   // NOLINT
#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

namespace ks {
namespace ad_algorithm {

void register_bs_feature_group_16() {}

// auto create for register

}  // namespace ad_algorithm
}  // namespace ks

#endif
