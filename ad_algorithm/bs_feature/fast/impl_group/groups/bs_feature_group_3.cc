#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_3.h"

#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "third_party/abseil/absl/strings/str_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_channel_id.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_reco_live_wtr.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_goods_view_seller_id_list_scale.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_novel_plot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_attribute_live.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_app_score.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_rank_ad_playtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shortterm_action_list_ts_item_imp.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_product_name_clk.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_title.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_available_memory_percent.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_seller_pe.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_eds_comp_item_invoke_list_ts.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_item_click_more_combine_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_mmu_tag_online.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conv_extend_fin_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_ks_map_cate_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dedup_photo_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_media_second_class.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_retention_media_id_product_is_new.cc"   // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_bclick_list.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_datetime.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_share.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_asr_2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_device_info_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_photo_new_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_ecom_order_type_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_topic_photo_topic.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_text_feature_ctr_imp.cc"   // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_photo_num.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_deep_action_product_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_cate3_id_scale.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_gender_n.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_pos_id_3t.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_1t.h"  //
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_model.cc"    // NOLINT
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_retention_product_1d.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_num_extend_ecom_dense_v2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_fin_days.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_pastday_list.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_crm_is_wangzhuan.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_pname.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_buy_cat_industy.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_play_ecom_photo_v1_seq_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_realtime_ad_info.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_holidays.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_author_p2l_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_novel_theme.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_id_unlogin.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_order_submit.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_click_bow_3d_words.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_click_bow_3d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_click_bow_7d.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_uid.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_simple_product.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag_no_userid_fix_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_match_dense_num_14d_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_cate2_id_scale.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_comp_ecom_with_time.h"
// // NOLINT
#include  "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_landing_tb_cate2_list.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_standard_live_room_live_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_author_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_extend_type.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_to_detail_merchant_item_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_mmu_cluster2w.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtb_flag.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_type_count_feature.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_media_sub_industry_id_v2.cc"    // NOLINT
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_detail_ads_union_special.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_follow_count.cc"    // NOLINT

// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_account_target_cpm_pos.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_region_from_ipv6.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_click_cart_seller_id_list_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_text_feature_bert_emb.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_apps_games_label.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_click_author_slot.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_top_short_topic.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_followed_merchant_author_list_no_author.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_unfollow_slot.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_unfollow.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top_like_author.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_thanos_play_rate_stat.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_level_author_stat.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_third_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_like_photo_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_watched_author_list_v2.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_photo_id.cc"    // NOLINT

// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_download_installed.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_to_detail_merchant_author_list_no_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_product_name_deep_action_30d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_detail_like_fix.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_business_interest_newhash.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_comp_realtime_pe_mmu3_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_edu_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_name.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_rank_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_mmu_cate2_id_list.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_material_tag_industry.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_item_price.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cost_account_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_comp_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_game_play_method.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_order_submit_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_mmu_cate1_id_list.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_conversion_extend_short.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_nebula_ad_click_extend.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_explore_stat_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_negative_brand.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cpm_unit_d7.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_12.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_follow_author_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_follow_author_id_v2.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sjh_30_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_cnt_ts_item_imp_fix.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_his_stat_dis.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_text_feature_bert_cluster.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_retention_product_3d.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_comment_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click2_itemids.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_cover.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conversion_extend_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_item_click_list.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_conversion_product_d30.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_pos_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_loc.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_topic.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_no_click_edu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_click1_no_click2_extend.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_fanstop_userinfo_and_photoinfo.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_browse_brand_tag_sum.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_v2_dup_cover_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_simple_industry.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_lps_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_lps.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_live_info_playend.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_rate_7d_1m.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_share_extend_edu.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_nebula_ad_item_click_extend.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_goods_impression_seller_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_item_click_type_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_new_industry_v3_dense.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_new_industry_v3_select.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_query_item_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_query_user_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_query_user_onehot_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_query_item_onehot_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_keywords.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shortterm_action_cnt_ts_order_pay.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_author_id_longterm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_download_install_rewarded.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_retarget_same_auhor_new.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_industry.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_hate_topic.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_dup_cover_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_live_play_cnt_seven_day.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_account_deep_conv_medium.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_credit_profile_online.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_fin_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_universe_unit_stat_online.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_seller_id_short.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_event_jinjian_account.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_p3s_live_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_age_industry.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_fanstop_embedding_wtr.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_item_category_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_combine_product_pos_id.cc"   // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_follow_author_id_v1.cc"    // NOLINT

// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_level_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_likelist_liveauthor.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top3_impression_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top3_impression.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_cate3_id_gsu_item_id_list_longterm_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_action_search_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_deviceid_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_deviceid_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_slot.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_photo_target_cpm_pos.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_video_feature_moco_emb.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_ecom_short_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_pay_fin.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_label.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_play5s_ratio.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_avg_play_rate_thanos.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_financial_product_info.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_account_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_creative_rule_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_third_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_neg_hetu_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cost_unit_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_only_author1t.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_download_installed.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_embedding_feature2.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_feed_user_group.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_deep_action_industry_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_download_rewarded.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_like_topic.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_creative_behavior_intention_keyword.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_education_action.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_sellerids.cc"   // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_online_retail_info_pay_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_played_3s_no_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_impression.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_lps_ecom.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_product_conv_pos.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_actionbar.cc"    // NOLINT
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_ad_campaign_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_label_match_type.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_creative_material_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_last_item_impression_mingap_realtime.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_num.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_is_connected_live.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_with_time_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_match_cnt.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_reco_user_realtime_action_and_creative.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_click1_no_play3s_extend.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_playend_ratio.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_cpu_count.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_comp_ecom_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_new_industry_v3.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_slot.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_no_prefix.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_hetu_tag_l1.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_photo_deep_conv_pos.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_media_id_ad_style.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_city_village.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_education_class_info3.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_imporession_newhash.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_play3s_extend_short.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_no_conv.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_target_id.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_no_item_click_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_fanstop_live_author_fans_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_comp_click2_app_id_list_scale.cc"   // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_cover_feature_ctr_conv.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_info_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_conversion_product_d7.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_recall_target_info_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_online_retail_info_spu_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sub_page_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_network.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_interactive_form_native.cc"    // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_app_list_media_industry.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_edu_course.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_detail_context_photo_mmu_text_cluster.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_caption_segment_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_duration.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_stat.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_querytoken_account_id.cc"   // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_photo_embedding_ctr.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_old_second_cate_rfm.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_impression_noclick_new.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_negative_new_slot.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_retention_product_7d.cc"   // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reco_slide_fm_re.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dsp_embedding.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_item_impression_list.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_click_author_mat.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_playedend.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_creative_hori_vert.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_emb_query2.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_adload_category_online.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_rewrite_query.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_vision_feature_cover_ctr_imp.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_author_new_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_playend_photo_second_industry_sequence.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_product_real_name.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_corporation.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_interest.h"  // NOLINT
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_impression_extend_short.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_item_impression_industry_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_product_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_poi_author_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_app_start_type.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_lps_user_stats_30d.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_gift_count.cc"    // NOLINT
// // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_cnt.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_retarget_author_slot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_reco_live_ctr.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_name_media_purchase_property.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_age.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_ad_info_play5s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_product_name_shallow_action_30d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_crm_product_type.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_video_feature_moco_cluster.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_has_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_ad_style_new.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_product_target_cpm_medium.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_mark_num.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_install_app_flag.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_search_feature_emb.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_reco_user_realtime_action_and_mmu_class.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_text_feature_bert_emb.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_yellow_cart_show_live_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_edu_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_ecomm_hetu_tag_l2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_context_posid_cluster.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_smart_target_calibrator.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_combine_account_ad_style.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_landing_jd_cate2_list.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_creative_template_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_campaign_id.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_audience_count.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_audience_count_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_account_conv_pos.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_name_media_second_class.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_itemclick_action_nebula.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_comp_realtime_cv_mmu1_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_week_stay_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_cnt_7d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_conversion_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_brand.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_cnt_ts_p3s_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_ad_style.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_material_tag_age.cc"   // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_age_segment_vacation_holiday.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_smart_matching_thres_nebula2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_predict_scvr.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_played5s_no_item_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_app_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_impression_noclick_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_music_global_stat.cc"    // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate3_id_gsu_author_id_list_longterm.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_cnt_7d_10s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_lps_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_online_time.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_media_first_class.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_last_click_mingap_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_no_play3s_ecom.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_material_feature_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_photo_conv_medium.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_from_page.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_pos_predict.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_info_new.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_mmu_cluster_2w.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cpm_product_d7.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_app_conversion.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_author.cc"   // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_enhance_follow_author_htag_lv2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_native_photo_goods_view_photo_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_download_complete_rewarded.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_cluster.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_avg_play_rate_explore.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_photo_conv_pos.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_level_photo.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_level_photo_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_mmu_cate3_id_list.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_pay_buyer_uv.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_item_click_uv.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_total_order_gmv.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_watch_uv.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_total_watch_uv.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reward_distbt_coin_no_ad_cnt_sd.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_device_realtime_network.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_app_name.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_item_impression_ad_realtime.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_userid_livecoverurl.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_product_deep_conv_medium.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_htag_uniq.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_detail_item_imp.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_callback_action_industry_feature_credit.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_new_context_info_native.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_hate_topic.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_media_industry_id_v2.cc"    // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_strategy_type.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_nextday_stay_rewarded.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_nebula_ad_play3s_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_source_type.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_first_industry_dense_d7.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ipc.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_callback_action_industry_feature_jinjian.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_deep_conversion_product_d30.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_app_category_app_name.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_attribute_photo_new_v1.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_ad_item_click_num.cc"    // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_product_name_pe_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_photo_media_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_unit_campaign_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_jinjian_extend_short.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_universe_creative_stat_online.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sjh_imp.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_callback_event_onoff_consistent.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_second_cate_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_description.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_novel_role.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_third_ecom_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reco_user_click_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_ocr_2.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click2_cate2ids.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conversion_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_action_uv_purchase_cpm_pos_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_reco_user_realtime_action_and_product_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate2_id_longterm_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_unit_bid_sparse.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo_click_cart_seller_id_list_short.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_large_new_photo_slot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_inner_delivery.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cost_account_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_report.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_account_conv_medium.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_jlrw_show_pv_cnt_seven_day.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_deep_conversion_industry_d30.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_campaign_id.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_ad_style_3t.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_cate3_short.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_event_type_product_name_no_pdd.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_photo_bid_type_ocpx_action_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cost_sec_industry_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_embedding_target_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_rate_7d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_download_started.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_watch_tag_live_mmu_tag_online.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_photo_bid_price.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_uri_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_uri.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cost_sec_industry_d7.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_exp_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_money_pv_cnt_seven_day.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate1_id_longterm_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_user_click_industry_time_gap.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_is_soft.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_item_put_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_lda.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_no_play3s_xiaodai.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_width_height_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_width_height.cc"    // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_rerank_pos.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_abtest_hash_id_native.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_source.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_ad_unit_id.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_conv_medium_industry_dr_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_pname_2.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_installed_app_newhash.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_order_amount.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_shallow_conv_ocpc_action_type.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_reco_emp_xtr_dis.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_text_feature_ctr_conv.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_no_conv_ecom.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_photo_like_list.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ecom_and_other_lps_item_ocpx_type.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_htag1.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_credit_jinjian_grant_cnt.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_impression_avegap_new.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_share_count.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_last_impression_mingap_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag_no_uid.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_level_photo_region_from_ip.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_matchtype.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_matchtype_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_rewrite_query_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_enter_source.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_bidword.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_productname_token.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_productname_token_reverse.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_extend_type_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_qr_score_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_relevance_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_strategy_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_strategy_type_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_parser_text_v1.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_parser_text_token_v1.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_query_combine_match_num.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_kbox_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_query_source.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_query_catgory_class3.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_query_catgory_class2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_refer_photo_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_akg_query_embedding.cc"    // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_ad_style_fix.cc"    // NOLINT
// // NOLINT
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_app_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_promotion_type.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_title.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_tags_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_edu_days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_create_time.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_creative_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_native_live_yellow_carts_click_seller_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_no_invoke.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_clk_zhanfen_tag_online.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_page_id_dense.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_hour.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_negative_photo_ad_lda.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_product_list.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_text_feature_bert_imp.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_live_info_itemclick.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_playend_conv_ratio.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_light.cc"  //    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_htag.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_keyword_app_degree_180d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_action_uv_conversion_cpm_pos_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_info_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_eds_comp_item_conv_list_ts.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top1_interact_industry.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_impression_noclick_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_xiaodai_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_play_lt1s_match.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_lsp_segment_info.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_lsp_segment_info_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_author_sim_action_match_cnt_aid_32_fix.h"  // NOLINT
namespace ks {
namespace ad_algorithm {

void register_bs_feature_group_3() {}

}  // namespace ad_algorithm
}  // namespace ks

#endif
