#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_4.h"

#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "third_party/abseil/absl/strings/str_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_followlist_liveauthor_seq.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_old_first_cate_with_passed_day.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_enter_profile_author.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_played3s_no_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_playend_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_newhash.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_hetu_tag_l3.cc"    // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_report_ad.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_first_cate_with_passed_day.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_os.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_app_installed_cate.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_fix.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_comp_ecom_with_time.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rewarded_conv_pred_val_infer.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_click_ratio.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click2_no_conv_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_global_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_cost.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_universe_campaign_stat_online.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ecom_product_title_emb.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_item_clk_v2_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_product_name_conv_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_new_fanstop_photo_bid_price.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_interests_label_query_uv_d7.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_holiday_vacation.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_photo_video_feature_moco_cluster.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_cnt_ts_item_clk.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_no_conv_ecom.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_click_industry.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_detail_p5s_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_edu_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_brand_tag_sum.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_collect_tag.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_gender_industry.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_follow_mat.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_hetu_tag_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_hetu_tag.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_purchase_extend_game.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_knews_app_uid.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_level_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_cnt_conv.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_rank_server_show_ctr.h"
// // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_comment_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_comment_count_fix.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_mmu_class.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_is_ad.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_detail.h"  //
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_adx_cvr.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_photo_real_product_name.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_item_id_longterm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_creative_behavior_intention_category_backtrace.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_click_photo_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lda.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_exp_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_played_fix5s_no_item_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_p3s_seller_id.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_query_keywordcnt.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo2live_photo_id_seq.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo2live_author_id_seq.cc"    // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_action_uv_d7.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_conv_fix.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_collect_author.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_lps.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_cooperation_mode_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rt_browse_type_native.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_ad_info_itemclick.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cost_product_d7.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_industry_new.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_live_bid_type_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_yellow_cart_show_seller_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_sparse_getui_data_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_ocpc_id.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_new_fanstop_photo_bid_type_ocpx_action_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_11.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_refresh_direction.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_id.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_realtime_play5s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_to_detail_author_list_v2.cc" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_info_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_info_new_fix.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_user_detail_universe_context_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_prodtct_name_ug_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_photo_id_user_lda.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_cs_count_data.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_seller_conv.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_account_id.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_lda_photo_lda.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_watched_author_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_watched_author_list_new.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_education_class_info.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_cnt_ts_p5s_fix.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_mmu_classification_143.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dmp_id_fea.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_interest_click_keywords.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_live_user_fans_flag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_live_user_fans_flag_sub_page_id.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_live_user_fans_flag_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_click2_no_invoke_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_vision_feature_cover_ctr_conv.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_old_first_cate_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_info_with_time.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_old_first_cate_rfm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_like_mat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_download_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_download.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_sold_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_brand.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_request_times_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_color.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_short_game.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_emb_query3.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_pdn_sim_seq_v0.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_pdn_sim_seq_v1.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_pdn_sim_seq_num_v0.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_text_feature_bert_cluster.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_unit_id.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_action_sum_online_dense_combine_new_lookalike.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_follow_author_time_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_app_list_media_ad_style.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_click2_no_conv_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_info_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_conv_medium_industry_ctr_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_querytoken_ad_campaign_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_account_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_played5s_no_lps_newhash.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_upload_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_product_name.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_text_feature_bert_conv.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_device_live.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_device_realtime_os.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_orig_music_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_new_context_info_rp.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_info_noip.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playedend_no_lps_newhash.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_online_retail_info_category.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_landing_tb_brand_list.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_enter_profile_tag.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_click_industry_d30.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_ocpc_action_type.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_pay_edu.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_level_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_author_id_match_num_sparse_longterm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_play5s_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_creative_behavior_intention_category.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_played5s.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_browse_tag_sum.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_follow_photo_slot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_no_author_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_play_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cover_star_cluster.h"  // NOLINT
// need fix
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_total_disk.cc"    // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_imp_second_category_30d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_play_brand.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_ad_follow_lda.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cover_mmu_embedding.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_material_tag_benefit.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top3_interact_industry.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click1_no_play3s_extend.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_rate_7d_5s.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_datetime.cc"  // NOLINT
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_lps_poster_rate.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_clickliveauthorlist_near.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_wangzhuan.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_landingpage_emb.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_photo_id_deep_action_30d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_like_author_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click2_cate1ids.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_combine_product_media_wangzhuan.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag_no_userid_fix_v1.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_exp_ctr.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dnn_cluster_id.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_info_imei.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_count_feature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_description_2.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_dup_cover_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_item_click_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_num.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_lps_xiaodai.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_like_extend.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_unit_type.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_cvr.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_extend_ecom_days.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cpm_sec_industry_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_id.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_explore_stat_ctr.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_mmu_classification_143.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_corporation_name.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shortterm_action_cnt_ts_item_clk.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_vision_feature_cover_ctr.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_author_weight.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_ocpc_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_ocpc_dense2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_ocpc_dense3.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_ocpc_dense4.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_brand.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_enhance_tag_favor.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_embedding.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_app_id_3t.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_edu_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_id_slot.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_id.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_mmu_cluster_id.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_mmu_global_cluster_id.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_wide_item_feature_dense.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_live_bid_price.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_buy_cat_item.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_inc_cut.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_playend_photo_product_sequence.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_live_info_play5s.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top6_interact_industry.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_support_cat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_conv_medium_sub_industry_dr_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_video_feature_moco_imp_thanos.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_app_list_media_uid.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_source_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_edu_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_target_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sjh_p3s.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_photo_media_wangzhuan.h"
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_rewarded_pageid.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_combine_account_media_uid.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_keyword_ad_degree_7d.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_num.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_medium_uid.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_photo_id_media_purchase_property.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_jlrw_task_click_pv_cnt_seven_day.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_click_industry.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_eds_comp_item_invoke_list_ts_fix.h"
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_upload_type.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_second_category.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_mmu_class623.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_pos_id.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_category_app_degree_180d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_xiaodai_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_app_list_ad_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_product_share.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_longterm_photo_p5s.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_giftlist_liveauthor.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_id_media_first_class.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_ecom_short_with_time_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_app_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sjh_like.cc"    // NOLINT
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_collect_photo.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_novel_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_app_category.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_universe_long_item_impression.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_embedding_p3s.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_photo_ad_lda.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_invoke.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_action_upload_tag.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_enhance_follow_htag_all.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_ecomm_hetu_tag_l3.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_media_purchase_property.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag_fix_v2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_xiaodai_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_vision_feature_cover_ctr.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_device_battery_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_cate3_id_gsu_author_id_list_longterm_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click_product_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_tag_list.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_comp_realtime_click2_mmu3_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_hate_tag.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_page_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_hate_photo.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_comp_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_is_living.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_temids.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_imp_second_category_14d.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_uid_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_c2_retention_new_pay_stat_3days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cpm_product_d7.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_tag_category.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_app_developer_name.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_creative_style_material_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_game.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_jk_samples_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_creative_material_dense_d7.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_smb_with_time.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_ecommerce_product_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_item_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click2_spuids.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_medium_game_category_id.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_slot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_block.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_landing_tb_cate1_list.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_category_ad_degree_7d.h" // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_target_info.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_description_bert.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_universe_photo_conv_medium_industry_dr.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_ocr_title_2.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_fin.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_like_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_played3s_author_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_combine_product_ad_style.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_click_ad.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_library_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_category_app_install.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_shallow_action_industry_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_price.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_invoke_extend_days.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_itemcf_product_name.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ecom_and_other_lps_item_type.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_slogan_2.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_age.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_deep_conversion_product_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_comment_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_ecomm_hetu_tag_l1.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_played_1m_no_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_played3s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_new_detail_page_click_list.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_id_slot.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_user_click_action_nebula.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_video_feature_moco_cluster.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_c2_lps_cmd.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_photo_deep_conv_medium.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_media_wangzhuan.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_ecom_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_app_invoke_extend.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_ad_like_lda.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_watch_tag_online.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lda.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_active_app_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_click_photo_second_industry_sequence.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_fans_count.cc"    // NOLINT

// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_impression_avegap_realtime.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click2_sellerids.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_product_deep_conv_pos.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_third_ecom_with_time.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_photo_media_uid.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_first_cate_list.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_like.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_attribute_dup_photo.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_large_new_author_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cpm_account_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_ecom_days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_cate1ids.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate3_id_gsu_item_id_list_longterm.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_replayed.cc" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_live_info_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_applist_cate_match_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_last_item_impression_gap.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_hate_author.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_app_list_media_pos.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_live_face_two_fix.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_audience.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_media_online_property.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_native_photo_goods_view_cate3_id.cc" // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_auto_cpa_bid_smart.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_embedding_ctr.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_stat.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_stat_ftr.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_selling_goods_embedding_v2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_detail_p3s_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dl_cluster.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_ad_industry.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_virtual_creative_sticker_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_invoke_fix.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dislike_topic.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_eds_comp_photo_cnt.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_playend_rewarded.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_stat_ctr.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top4_interact_industry.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate3_id_longterm_v2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_level_loc.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_cnt_7d_5s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_c1_retention_new_pay_stat_3days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_campaign_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_industry_group_id_rr.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_classification_143.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_playend.cc" // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_emb_query1.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_pos_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_username_seg.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_product_name_shallow_action_7d.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_user_comp_ad_click2_extend_product_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_cnt_form_submit_v2.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_photo_mmu_hetu_tag_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_user_combine_channel.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_cnt_item_clk_v2.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playedend_no_invoke.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_follow_no_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sm_dense_infer_callback_event.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_photo_click_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_conversion_industry_d30.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_holiday_next.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_nebula_ad_play5s_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_is_filter_by_feedback.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_pos_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_forwardlist_liveauthor.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_imp_zhanfen_tag_online.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_id_new.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_pay_extend_fin_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_first_cate_rfm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shortterm_action_list_ts_p3s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_play3s.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_no_play3s_fin.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_callback_action_product_feature_jinjian.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_delivery_type.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_tab.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_category_tag_sum.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate2_id_longterm.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_novel_name.cc"    // NOLINT
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_followed_author_list_new.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_online_retail_info_product_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reward_distbt_coin_watch_photo_cnt_sd.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_app_list_media_app.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_comp_ecom_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_seller_id_scale.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_num_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_ad_render_type_distribution_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_extend_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_userlevel_liveauthor.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_click_match_num.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_native_live_goods_view_cate3_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top5_interact_industry.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_app_list_ad_photo_id.h"
// // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_action_click_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_weekly_behav_author_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_element_type_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_conversion_rewarded.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_search_pos_user_id.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_click_bow_7d_words.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_video_feature_moco_emb.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_nextday_stay.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_filter.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_crm_info_product_feature.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_fin_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_c2_retention_new_pay_stat_7days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_lps_url_domain_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_slot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_live_info_play3s.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click2_cate3ids.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_direct_creative_cluster.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo_click_card_seller_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_global_brand.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_click_rewarded.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_c1_retention_new_pay_stat_7days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_game.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_purchase_extend_short_game.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_to_detail_author_list_new.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_to_detail_author_list.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_cate_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_retention_product_14d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_order_paied_extend_ecom_days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_entity_tag_sum.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_itemcf_sec_industry_name.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_cnt_item_imp.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_yellow_cart_click_live_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_dense_photo_roi_ltv.cc"   // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_description_seg.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_comp_realtime_click2_mmu1_with_time.h"
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_uid_fix.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_ecom_with_time.h"  // NOLINT
// need fix
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_wangzhuan_media_wangzhuan.cc"    // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_click_author_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_region.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_embedding_feature.h"  // NOLINT

namespace ks {
namespace ad_algorithm {

void register_bs_feature_group_4() {}

}  // namespace ad_algorithm
}  // namespace ks

#endif
