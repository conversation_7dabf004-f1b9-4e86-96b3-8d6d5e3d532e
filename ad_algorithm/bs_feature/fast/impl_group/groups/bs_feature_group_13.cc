
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_13.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_novel_search_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_keyword_intent_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_novel_interactive_times_segment.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_sim_account_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_sim_action_label_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_sim_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_sim_industry_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_sim_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_sim_product_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_sim_timestamp_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_action_list_with_multi_key_hard_search_pooling.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_action_list_with_multi_key_pooling.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_add_cart_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_add_cart_seller_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_add_cart_item_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_update_cart_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_update_cart_seller_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_update_cart_item_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_delete_cart_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_delete_cart_seller_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_delete_cart_item_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_comment_seller_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_comment_item_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_comment_update_seller_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_comment_update_item_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_im_message_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_refund_seller_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_buy_history_llm_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_photo_authorid_split.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_30_d_atv_seq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_click_action_seq_product_name_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_click_action_seq_industry_tag_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_click_action_seq_is_playable_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_click_action_seq_ad_style_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_click_action_seq_medium_cooperation_mode_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_click_action_seq_material_feature_type_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_product_name_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_industry_tag_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_is_playable_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_ad_style_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_medium_cooperation_mode_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_material_feature_type_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_click_action_seq_industry_tag_extend_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_click_action_seq_is_playable_extend_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_click_action_seq_ad_style_extend_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_click_action_seq_medium_cooperation_mode_extend_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_click_action_seq_material_feature_type_extend_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_industry_tag_extend_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_is_playable_extend_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_ad_style_extend_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_medium_cooperation_mode_extend_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_material_feature_type_extend_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_yellow_cart_goods_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_item_live_author_max_ctcvr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_user_follow_coeff.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_buy_history_llm_emb_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_click_history_llm_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_long_term_combine_order_paied_attr_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_real_time_combine_order_paied_attr_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_real_time_combine_goods_view_attr_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_long_term_combine_goods_view_attr_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_coldstart.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_app_context_product_coldstart.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_uid_context_product_coldstart.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_sub_context_product_coldstart.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_attribute_photo_new_coldstart.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_7_d_g_m_v_dist_p_10.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_7_d_g_m_v_dist_p_20.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_7_d_g_m_v_dist_p_30.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_7_d_g_m_v_dist_p_40.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_7_d_g_m_v_dist_p_50.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_7_d_g_m_v_dist_p_60.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_7_d_g_m_v_dist_p_70.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_7_d_g_m_v_dist_p_80.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_7_d_g_m_v_dist_p_90.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_two_hours_seven_days_a_t_r.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_product_with_user_pay_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_interest_item_info.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_platform_item_info.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_gender_age_item_info.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_region_item_info.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_consump_item_info.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_meta_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_meta_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_meta_account_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_meta_label.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_meta_timestamp_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_product_with_user_conversion_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_product_name_id_densev_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mix_rank_ecom_reco_photo_item_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mix_rank_ecom_reco_live_item_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mix_rank_ecom_reco_photo_hetu_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mix_rank_ecom_reco_live_hetu_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mix_rank_ecom_reco_photo_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mix_rank_ecom_reco_live_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mix_rank_ecom_reco_photo_first_industry_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mix_rank_ecom_reco_live_first_industry_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mix_rank_ecom_reco_photo_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mix_rank_ecom_reco_live_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mix_rank_ecom_reco_context_match_target_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_long_term_combine_event_order_submit_click_attr_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_meta_label_embed.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_im_indirect_turn_cnt_bin_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_im_indirect_c_2b_send_msg_cnt_bin_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_im_indirect_b_2c_after_send_msg_cnt_bin_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_im_indirect_turn_cnt_bin_list_15_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_im_indirect_c_2b_send_msg_cnt_bin_list_15_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_im_indirect_b_2c_after_send_msg_cnt_bin_list_15_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_im_indirect_turn_cnt_bin_list_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_im_indirect_c_2b_send_msg_cnt_bin_list_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_im_indirect_b_2c_after_send_msg_cnt_bin_list_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_im_indirect_turn_cnt_bin_list_1_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_im_indirect_c_2b_send_msg_cnt_bin_list_1_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_im_indirect_b_2c_after_send_msg_cnt_bin_list_1_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_sparse_search_series_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_yellow_car_top_5_goods_spu_v_3_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_explaining_goods_spu_v_3_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_explaining_goods_x_7_cate_leaf_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_explaining_goods_spu_cluster_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_explaining_goods_x_7_cate_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_explaining_goods_x_7_cate_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_explaining_goods_x_7_cate_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_explaining_goods_x_7_cate_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_explaining_goods_merchant_product_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_explaining_goods_spu_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_combine_has_smart_offer.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_is_aigc.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_aigc_fea.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_author_ltype.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_author_price_all_site_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_author_realtime_price_all_site_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_7_d_price_dist_p_10_all_site_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_7_d_price_dist_p_50_all_site_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_7_d_price_dist_p_90_all_site_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_sell_history_llm_dense_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_submit_utype_spu_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_submit_utype_x_7_level_2_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_submit_utype_x_7_level_3_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_submit_utype_x_7_level_4_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_paid_utype_spu_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_paid_utype_x_7_level_2_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_paid_utype_x_7_level_3_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_paid_utype_x_7_level_4_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_submit_item_price_spu_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_submit_item_price_x_7_level_2_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_submit_item_price_x_7_level_3_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_submit_item_price_x_7_level_4_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_paid_item_price_spu_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_paid_item_price_x_7_level_2_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_paid_item_price_x_7_level_3_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_paid_item_price_x_7_level_4_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_dmp_deep_emb_1024.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_hetu_photo_emb_128.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_submit_item_author_spu_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_submit_item_author_x_7_level_2_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_submit_item_author_x_7_level_3_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_submit_item_author_x_7_level_4_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_paid_item_author_spu_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_paid_item_author_x_7_level_2_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_paid_item_author_x_7_level_3_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_merchant_paid_item_author_x_7_level_4_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_x_7_cate_3_id_dark_dense_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_spu_id_dark_dense_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_id_dark_dense_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_id_product_id_dark_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_id_product_id_dark_dense_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_2_live_shark_2_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_live_goods_type_dark_v_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_live_goods_type_dark_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_x_7_cate_leaf_id_dark_v_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_x_7_cate_leaf_id_dark_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_x_7_cate_score_dark_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_x_7_cate_4_id_dark_v_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_x_7_cate_4_id_dark_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_x_7_cate_3_id_dark_v_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_x_7_cate_3_id_dark_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_x_7_cate_2_id_dark_v_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_x_7_cate_2_id_dark_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_x_7_cate_1_id_dark_v_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_x_7_cate_1_id_dark_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_cluster_id_id_dark_v_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_cluster_id_id_dark_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_spu_id_dark_v_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_spu_id_dark_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_id_dark_v_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_id_dark_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_id_item_id_dark_v_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_id_item_id_dark_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_owner_id_dark_v_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_owner_id_dark_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_seller_id_dark_v_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_seller_id_dark_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_item_outer_photo_text_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ecom_cot_input_open_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ecom_cot_input_close_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ecom_cot_output_open_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ecom_cot_output_close_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ecom_sft_input_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ecom_sft_output_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_cot_input_open_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_cot_input_close_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_cot_output_open_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_cot_output_close_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_sft_input_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_sft_output_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_yellow_car_top_5_goods_length.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_yellow_car_top_5_goods_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_page_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_shark_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_explaining_goods_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_shark_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dense_caption_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dense_semantic_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_price_40.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_price_60.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_price_80.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_price_20.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_llm_emb_1024_dim.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_llm_emb_1024_dim.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_prerank_candidates_top_list_hard_account_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_prerank_candidates_top_list_hard_photo_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_prerank_candidates_top_list_hard_industry_id_v_3_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_prerank_candidates_top_list_hard_city_product_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_prerank_candidates_top_list_hard_ocpx_action_type_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_prerank_candidates_top_list_soft_account_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_prerank_candidates_top_list_soft_photo_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_prerank_candidates_top_list_soft_industry_id_v_3_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_prerank_candidates_top_list_soft_city_product_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_prerank_candidates_top_list_soft_ocpx_action_type_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_item_outer_photo_text_embedding_1024.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dense_semantic_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_hour_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_date_interval_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_yellow_car_item_pay_cnt_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_mechant_history_item_pay_cnt_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_uid_context_product_coldstart_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_app_context_product_coldstart_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_coldstart_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_sub_context_product_coldstart_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_atv_grade_prob.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_one_hot_live_creative_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_realtime_click_no_submit.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_realtime_p_3s_no_submit.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_realtime_imp_no_submit.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_realtime_ped_no_submit.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_realtime_apxm_no_submit.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_is_login.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_age_seg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_gender.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_item_episode.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_item_gender.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_item_tag_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_creative_ad_style_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_log_minute_interval_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_item_pay_type.cc"   // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_click.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_conversion.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_play3s.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_play5s.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_playend.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_uri.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_play5s_more_combine.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_playend_more_combine.cc"  // NOLINT
  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_cover_feature_ctr.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_text_feature_ctr.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_author_atv_6_grade_prob.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_item_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_bid_month.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_bid_week.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_category_list_month.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_category_list_week.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_novel_dnc.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_novel_doc.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_order_month.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_order_week.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_pay_click_month.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_pay_click_week.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_pay_show_month.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_pay_show_week.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_read_page_showcnt_month.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_read_page_showcnt_week.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_single_pay_amount_month.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_single_pay_amount_week.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_single_stay_duration_month.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_single_stay_duration_week.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_active_product_num_segment.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_avg_pay_coefficient_segment.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_max_pay_coefficient_segment.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_active_product_list_3d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_active_product_list_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_active_product_list_15d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_active_product_listnd.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_high_pay_product_list_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_adcu_text.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_llm_dmp_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_base_uri.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_duration_bucket.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_brand_match_account_30d.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_brand_match_account_7d.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mix_context_session_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_ocr.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_pos.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_sequence.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag_fix.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_action_brand_account_30d.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_action_brand_account_7d.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_level.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_ecomm_hetu_tag.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_real_device_left_battery.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_real_device_left_disk.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_real_device_left_memory.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_industry_group.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_unit_id.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_photo.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_item_source.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_item_view_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_item_tag_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_item_status.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_item_finished.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_item_biz_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_tag_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_tag_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_tag_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_city.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_connection_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_ip.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_os.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_app_version.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_did.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_gid.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_esp_qcpx_origin_pcvr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_long_term_order_paied_ts_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_long_term_goods_view_ts_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_long_term_order_paied_ts_bins.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_long_term_goods_view_ts_bins.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rank_quota_cpm_list_outer_soft_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_qcpx_coupon_amt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_qcpx_coupon_threshold.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_is_conversion_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_no_conversion_is_page_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_no_conversion_is_r_o_i_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_no_conversion_is_pay_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_outer_flag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_denser_universe_exp_tags.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dense_caption_top_k.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_first_intustry_name_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_second_intustry_name_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_media_app_id_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_media_app_id_extend_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_convert_action_seq_industry_tag_extend_re_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_short_video_and_live_order_paid_model_rank_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_short_video_and_live_order_paid_model_rank_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_universe_ad_style.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dense_inner_ad_dense_caption_image_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dense_inner_ad_dense_caption_text_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adcu_act_seq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adcu_dt_seq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adcu_cid_seq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adcu_pid_seq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_edu_search_cate_4_list_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_edu_search_cate_3_list_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_edu_search_ori_cate_2_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_edu_search_cate_4_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_edu_search_cate_2_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_edu_search_ori_cate_2_list_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_edu_search_cate_3_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_edu_search_num_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_click_spu_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_click_x_7_level_1_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_click_x_7_level_2_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_click_x_7_level_3_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_ped_spu_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_ped_x_7_level_1_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_ped_x_7_level_2_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_ped_x_7_level_3_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_13_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_2_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_1_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_4_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_13_day_15.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_4_day_15.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_2_day_15.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_1_day_15.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_2_day_30.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_1_day_30.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_13_day_30.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_4_day_30.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_13_day_90.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_1_day_90.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_4_day_90.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_2_day_90.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_13_day_7_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_2_day_7_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_1_day_7_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_4_day_7_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_13_day_15_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_4_day_15_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_2_day_15_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_1_day_15_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_2_day_30_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_1_day_30_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_13_day_30_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_4_day_30_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_13_day_90_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_1_day_90_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_4_day_90_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lose_cpm_style_2_day_90_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_merchant_stat_ple_dense_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_industry_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_is_playable.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_ad_style.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_medium_cooperation_mode.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_material_feature_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_medium_app_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_first_industry_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_second_industry_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_endcard_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_playcard_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_hour.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_date_interval.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_log_minute_interval.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_action_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_agg_action_seq_last_action_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_pay_goods_num_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_click_poi_cate_2nd_ids_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_pay_goods_cate_3rd_ids_60d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_click_poi_ids_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_stats_pay_amt_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_pay_goods_num_60d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_stats_pay_amt_60d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_pay_goods_ids_60d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_pay_goods_num_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_pay_poi_ids_60d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_click_goods_poi_ids_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_stats_pay_amt_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_stay_more_ten_second_poi_ids_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_click_goods_poi_cate_2nd_ids_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_pay_poi_ids_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_stay_more_ten_second_poi_cate_2nd_ids_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_pay_goods_cate_2nd_ids_60d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_pay_poi_ids_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_local_pay_goods_cate_1st_ids_60d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_all_ecom_cot_input_open_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_all_ecom_cot_input_close_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_all_ecom_cot_output_open_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_all_ecom_cot_output_close_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dac_all_impression_15d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dac_outer_impression_7d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dac_outer_conversion_7d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dac_outer_impression_15d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dac_ecom_conv_impression_7d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_outer_ac_status_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dac_ecom_conv_conversion_7d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dac_ecom_conv_conversion_15d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dac_all_conversion_7d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dac_outer_conversion_15d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dac_all_impression_7d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dac_all_conversion_15d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dac_ecom_conv_impression_15d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_edu_deep_photo_lps_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_item_shark_top_k.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_click_product_names.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_click_material_types.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_conversion_device_os_ids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_click_page_ids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_click_marketing_objectives.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_conversion_resource_types.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_impression_marketing_objectives.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_activation_optimization_goals.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_click_device_os_ids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_click_optimization_goals.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_impression_optimization_goals.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_activation_product_names.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_click_mini_game_ids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_impression_primary_entry_sources.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_activation_marketing_objectives.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_conversion_marketing_objectives.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_playtime_material_types.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_impression_marketing_objectives.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_impression_material_types.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_click_resource_types.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_playtime_marketing_objectives.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_playtime_resource_types.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_conversion_page_ids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_impression_resource_types.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_click_optimization_goals.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_click_primary_entry_sources.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_impression_resource_types.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_conversion_product_names.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_click_material_types.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_impression_material_types.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_impression_product_names.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_playtime_optimization_goals.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_playtime_product_names.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_activation_mini_game_ids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_click_marketing_objectives.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_click_resource_types.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_impression_mini_game_ids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_impression_device_os_ids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_activation_material_types.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_conversion_material_types.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_conversion_optimization_goals.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_impression_page_ids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_impression_product_names.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_conversion_mini_game_ids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_click_mini_game_ids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_conversion_primary_entry_sources.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_click_product_names.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_playtime_mini_game_ids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_click_ad_behaviors.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_ad_activation_resource_types.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_impression_mini_game_ids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mini_app_game_rewarded_ad_impression_optimization_goals.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_create_order_timestamp_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lps_action_timestamp_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_poi_arrive_timestamp_list_90_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_clue_submit_timestamp_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_global_action_sdpa_cate_3_sort_by_timestamp.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_products_predict_scores.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_high_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_p_2_p.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_coldstart_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_sub_context_product_coldstart_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_uid_context_product_coldstart_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_app_context_product_coldstart_v_2.cc"   // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_attributes.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_sellingpoint.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_comments.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_user_ec_intent.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_user_pos_sentiment.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_photo_comment_stats.cc"   // NOLINT
#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

namespace ks {
namespace ad_algorithm {

void register_bs_feature_group_13() {}

// auto create for register

}  // namespace ad_algorithm
}  // namespace ks

#endif

