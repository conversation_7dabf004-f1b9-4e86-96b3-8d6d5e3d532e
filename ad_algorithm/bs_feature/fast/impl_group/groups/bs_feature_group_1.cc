#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_1.h"

#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "third_party/abseil/absl/strings/str_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_realtime_action_list_match_n_detail.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_inc_cut_join.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_for_photo_v1.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_top_action_list_sim_inc_cut_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_top_action_list_sim_inc_cut_v5.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_picasso.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_graphsim.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_realtime_seq_lp_no_dup.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_picasso_count.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_app_active_info.h"

namespace ks {
namespace ad_algorithm {

void register_bs_feature_group_1() {}

}  // namespace ad_algorithm
}  // namespace ks

#endif
