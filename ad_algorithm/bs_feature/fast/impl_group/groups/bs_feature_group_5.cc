#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_5.h"

#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "third_party/abseil/absl/strings/str_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_app_dense.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_conv_medium_industry_cvr_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_photo_comment.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_clickliveauthorlist_hot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_cate_list_short.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_online_retail_info_item_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_new_install_app_all.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_ecom_mmu_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_conv.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_17.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sm_dense_train_callback_event.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_id_newhash.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_qr_score.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_cname.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_cat_new_seq.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_comp_playend_app_id_list_scale.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_download_installed_game.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conversion_extend_game.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_only_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_education_class_info2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_watched_merchant_author_list_no_author.h" // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_next_stay_vacation.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_subcontextproduct.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_cate1_id_match_num_sparse_longterm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_slot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_price_list.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_conv_medium_sub_industry_cvr_new.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_cate2Ids.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_p3s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ecom_product_cover_emb.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_live_yellow_trolley_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_text_feature_bert_click_feed.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_live_author_fans.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_effect_conversion_cpm_pos_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_creative_id.cc"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_age.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_is_playable.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_rank_unify_cvr.cc"   // NOLINT

// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_subtitle_segment.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_merchant_list_online_author_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top2_interact_industry.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_product_target_cpm_pos.cc"   // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lik_zhanfen_tag_online.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_campaign_type_rewarded_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top3_click_realtime.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_baterry_percent.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_adload_feature_duration_online_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_extend.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_commerce_search_query_game_cate_1d_online.cc"    // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_comp_ecom_with_time.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_roi_reciprocal.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_combine_account_pos_id.cc"   // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_search_pos_product_name.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_no_conv.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_action_nebula_fix.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_play3s_extend.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_htag0.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cover_mmu_cluster.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cost_unit_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_xiaodai_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_user_comp_ad_click_extend_photo_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_eds_comp_item_ped_list_ts_fix.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_comp_click2_product_id_list_scale.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_played5s_no_lps_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_conversion_product_name_ad_product_name.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_template.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_played3s_no_item_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_device_memory_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_effect_submit_cpm_pos_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_industry_stats_online.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_posidv2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_industry_time_gap.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_dup_photo_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_cnt_ts_item_clk_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_duration.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_order_by_live.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_sound.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_kuai_cate_list.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ocpc_action_type_rewarded_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_avegap_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_xiaodai_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_callback_action_product_feature_credit.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_support_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_jinniu_xiaodian_product_id_v2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_landing_jd_cate3_list.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_id_list_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_mmu_class_623.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_comment_extend_edu.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_old_second_cate_with_passed_day.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cost_product_d7.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_callback_event_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_watched_merchant_author_list_scale.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_no_click_info.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_querytoken_ad_industry.cc"   // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_industry_name.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_third_ecom_with_time.cc" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rewarded_str_pred_val_infer.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rewarded_lps_pred_val_infer.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_conv_next_stay.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_13.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_embedding_multi_play.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_campaign_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_product_name_invoke.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_play5s.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_nocnt.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_text_feature_bert_click.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_longterm_photo_p3s.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_p5s_v2.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_smb_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_relevance.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_no_conv_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_first_industry_dense_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_fake_info.cc"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_product_name_shallow_action_3d.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_second_cate_rfm.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_effect_roi_cpm_pos_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_combine_product_media_uid.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_conv_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_purchase_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_signature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_creative_style_type.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_rtb_flag.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_cate2_id_match_num_sparse_longterm.cc" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_camera.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_download_completed.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rt_browse_type_1.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_universe_account_stat_online.cc" // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_days.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_photo_id_media_second_class.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_app_list_ad_cate_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_start_time_gap.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_order_paied_extend.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_item_optimization.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_creative_video_pic.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click_cate_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_price.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_cate3_id_match_num_sparse_longterm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_third_ecom_with_time.cc" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_click_product_d30.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reward_raw_cpm.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_video_feature_moco_click_thanos.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_eds_comp_item_ped_list_ts.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_keyword_ad_degree_180d.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_gender_vacation_holiday.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_deep_conversion_bid.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_ad_conversion_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_account_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_asr.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_is_smart_matching.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_mmu_class_623_all.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_smart_matching_thres_nebula.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_material_tag_actor.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_third_ecom_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_behav_online.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_union_type.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_userlevel_live.cc"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_conversion.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_advertiser_info.cc"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_material_tag_style.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_new_context_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_follow.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_like.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_followed_merchant_author_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_conv.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_to_detail_merchant_author_list.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_ecom.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_reco_like_num_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_reco_seq_mask.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_reco_seq_num.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_id_list_with_length.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_action_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_action_num.h"  // NOLINT

// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_seller_category_id.h"
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_seller_click_stat.cc"    // NOLINT
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_seller_live_trolley_stat.h"
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_seller_pay_cate3_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_datetime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_action_nebula.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_click_item_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_item_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_click_cart_seller_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo_click_cart_seller_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo_buy_item_seller_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_cate2_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_cate3_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_cate1_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_cate2_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_price.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_dense_hash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_region_from_ip.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_category.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_new_industry.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_dense_onehot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_creative_rule_id_sequence_share.cc"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_installed_app.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_bid_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_caption_segment.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_ad.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_attribute_photo_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_device_photo_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_view_like_photo_label.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_business_interest.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_music.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_request_times.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_build_times.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_build_time2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_datetime_hour_week.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_retarget_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_offline_retailer_keyword.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_author_attr_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_impression_ad.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_author_user_ad_lda.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_follow.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_played_1m.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_merchant_list_online.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_played_3s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_week_stay.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_watched_merchant_author_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_cate1_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_item_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_playend_action_nebula.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_pos.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_author_id_longterm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_followed_author_list.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_ecom_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_vision_feature_cover_ctr.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_play3s_action_nebula.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_seller_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_cate3_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_seller_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_followlist_liveauthor.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rt_browse_type.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_campaign_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_num_extend_ecom_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playedend_no_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_from_client.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_channel.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_game_tag_sim.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sdk_action_feature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_cat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_match_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_high_quality_author_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_played5s_no_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_recall_target_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_live_goods_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_seq_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_id_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_base_uri.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_akg_user_int_seq.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_akg_user_tag_seq.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_akg_user_simi_seq.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_akg_user_hweight_rec.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_realtime_action_list_sim.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_unit_type.h"  // NOLINT

// mmu kge feature
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_kge.cc"    // NOLINT

// live start time
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_start_time_gap_plain_dis_src2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_start_time_gap_plain_dis_n_aid_src2.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_start_time_gap_plain_dis_n_aid_src2_dis_v2.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_page_id_dense_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_univ_app_package_std_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sim_gsu_feature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sim_seq_ad_goods_v0.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sim_seq_ad_goods_v0_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sim_seq_ad_goods_v0_num.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_normalized_attribute_score_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_u2u_gsu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_u2u_match.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_u2u_match_category.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_callback_event128.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_author_sim_action_match_cnt.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_author_sim_action_match_cnt_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_ecomm_sim_action_match_cnt.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_global_gsu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_ecomm_gsu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_ecomm_match_category.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_author_cluster_gsu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_cid3_gsu.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_remote_cluster_gsu.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_spu_gsu.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_level_author_stat.cc"    // NOLINT

namespace ks {
namespace ad_algorithm {

void register_bs_feature_group_5() {}

}  // namespace ad_algorithm
}  // namespace ks

#endif
