#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_6.h"

#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "third_party/abseil/absl/strings/str_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_item_a2a_gsu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_item_cluster_gsu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_global_topn.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_item_author_cluster_gsu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_item_remote_cluster_gsu.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_item_v1_remote_cluster_gsu.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reid_photo_tag.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reid_user_tag.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_top28_seq_num.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_top28_seq_sparse.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_top28_seq_sparse_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_inc_cut_new.h"

// add by zhaolei06 0315

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_campaign_type_only_dsp.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_quality_emb.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_tag_category_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_level_id_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_commerce_user_v5.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_htag_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_is_connected_live_new_flow_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sdpa_user_interact_pid_seq.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ali_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_outer_inner_spu_feature_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_action_spu_list_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ali_feature_float.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ecom_brand_id_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_tags.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_order_poster_rate.cc"    // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_top_item.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_item.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_external_rank_quota.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_rank_quota.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_external_prerank_quota.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sdpa_ecom_user_interact_cate_seq.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_bert.h"  // NOLINT

// live feature by zhaolei06
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_value_template.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_list_value_template.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_list_value_template_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_list_value_seq_template.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_embedding_template.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_high_light.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_price.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_volume.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_value_template_fix.h"

// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_goods_tp.h
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_unify_ctr.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_unify_cvr.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_mmu_author_cluster_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_sdpa_photo_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_photo_bid_type_new.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_tag.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ecom_photo_spu_emb.h
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_live.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_gsu_live_realtime.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_gsu_live_top.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_gsu_photo_realtime.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_live_long.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_item_long.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_gsu_photo_realtime_v2.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_gsu_live_realtime_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_item_v3.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_top_item_v3.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_attr_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_id_list_size.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_field_id_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_commerce_user_buy180d_Item_cat3_sequence.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_id_slot.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_cate_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_ecom_order_type.h"


#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_action_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_dsp_last_gap_live_played.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_ecom.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_ad_splash.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_fans_top_last_gap_live_played.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_time_stamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_stat_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_play_lt1s.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_play_lt1s_last_gap.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_play_lt1s_stat_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_play_lt1s_time_stamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_play_lt1s_match_cnt.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_sp_match_cnt.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_action_sp_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_invoke_product_name_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_mmu_ped_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_ecom_short_with_time.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_direct_ecom_click2_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_direct_ecom_lps_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_colossus_filtered_by_playtime.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_sdpa_product_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_action_with_action_and_id_given_len.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_common_author_brand.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_common_author_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_common_author_industry_level.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_common_enhance_author_fusion.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_item_v3_tp.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_target_hash.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_id_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_action_seq.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_action_ts_seq.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_action_len.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_queue_type.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_list.h"

// add reco & ad resume submit action list
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_extend_action_list_match_stat_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_extend_action_list_stat_dense.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_item_v2_tp.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ecom_brand_id_feature_new.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_item_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_commerce_user_v5_time_sort.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_for_photo.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_photo_spu_swing.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_photo_spu_swing_new.cc"    // NOLINT


#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_cmt_hetu_tag.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_commerce_user_buy270d_time_sort_Item_cat3_sequence.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_commerce_user_click180d_time_sort_Item_cat3_sequence.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dense_tag.cc"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_ad_device_info_context_dup_photo.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_context_info_dup_photo.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_pageid_dup_photo.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_datetime_info_dup_photo.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_industry_item_click_cnt.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_photo_id_item_click_cnt.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_user_interactive_form_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_detail_info.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_product_name_dense_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_detail_info_ad_info_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_ad_device_info_ad_info_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_detail_info_context_ad_info_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_location_ad_info_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_invoke_product_name_each_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_invoke_product_name_each_dense_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_invoke_scene_flag.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_plc_biz_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_seq_app_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_seq_photo_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_seq_product_id_hash.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_seq_second_industry_id_hash.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_fans_valued_label.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_valued_followed_author.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_info_fans_range.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rewarded_is_show_order_paied_inspire.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_next_stay_day_state.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_next_stay_minute_of_day.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_next_stay_bn_state_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_edg_graph_emb.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_product_name_id_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_page_id_sparse.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_cate.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_category_seq.cc"    // NOLINT
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_univ_rank_unify_cxr.h"
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_uplift_feature.h"
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_uplift_float_feature.h"
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_univ_rank_cxr_fix.h"
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_univ_combine_rank_cxr.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_explore_stat.cc"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_xdt_mmu_embedding.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_latest_live_time_interval.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_pay_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_slp_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_match_cnt_live.cc"    // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_commerce_action_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_is_follow.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_is_follow_new.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_colossus_filtered_by_play_1m.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_top_action_list_sim_inc_cut_v4.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_inc_cut_nio.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_lps.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_no_filter.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_window_sample.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_no_filter_meta.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_price_combine.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_adsim_photo_info.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_second_industry_dense_268.cc"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_second_industry_dense_268.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_4_label.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_recruit_live_jobs_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_merchant_action_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_merchant_action_match_detail.h"

namespace ks {
namespace ad_algorithm {

void register_bs_feature_group_6() {}

}  // namespace ad_algorithm
}  // namespace ks

#endif
