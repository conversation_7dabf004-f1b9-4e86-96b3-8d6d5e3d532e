
#include "teams/ad/ad_algorithm/bs_feature/fast/impl_group/groups/bs_feature_group_15.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_live_merge_posid_combine_jump_click_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_live_merge_posid_combine_played_mean_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_live_merge_posid_combine_to_profile_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_live_merge_posid_combine_live_click_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_live_merge_posid_combine_played_median_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_live_merge_posid_combine_live_like_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_live_merge_posid_combine_live_comment_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_live_merge_posid_combine_order_submit_click_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_real_time_merchant_posid_combine_order_impression_item_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_real_time_merchant_posid_combine_order_impression_item_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_real_time_merchant_posid_combine_order_submit_click_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_real_time_merchant_posid_combine_order_submit_click_item_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_inner_live_pay_cnt_front.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_inner_live_pay_cnt_end.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_industry_id_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_product_name_hash.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_fre_province.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_high_csm_score.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_fre_distinct.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_fre_city.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_brith_pred.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_peofession_level_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_device_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_consumption_level.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_profession_level_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_profession_level_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_device_model.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_device_brand.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_age_segment.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_platform.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_pred_label.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_photov_3_level_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_cate_id_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_cate_id_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_fre_city_level.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_cate_id_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_level_1_score.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_cate_id_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_ecomm_level_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_theme_level_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_merchant_posid_sim_order_paied_distributor_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_real_time_merchant_posid_combine_order_submit_distributor_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_real_time_merchant_posid_combine_goods_view_user_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_real_time_merchant_posid_combine_order_impression_user_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_real_time_merchant_posid_combine_product_buy_click_user_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_real_time_merchant_posid_combine_shop_click_user_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_u_2_u_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_invoke_hack_label_mark.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_buyer_effective_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_request_freq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_sparse_id_no_prefix_new_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_id_no_prefix_new_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_author_id_no_prefix_new_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_photo_and_live_author_id_new_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_left_slide_stats.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_left_slide_gesture_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_realtime_meta_ecom_spu_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_ecom_spu_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_cid_qcpx_allow_dispense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_cid_qcpx_gear_position.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_cid_qcpx_is_account.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_unify_ctr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_merchant_action_author_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_author_action_merchant_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_merchant_action_target_merchant_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_huoke_product_name_ratio_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_live_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_live_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_creative_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_photo_or_live_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_photo_or_live_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_creative_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_user_id_creative_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_photo_or_live_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_user_id_photo_or_live_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_photo_and_live_author_id_no_prefix_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_unify_inner.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_combine_smart_offer_multi_random_sample.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_combine_smart_offer_multi_random_sample.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_realtime_time_diff.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_item_price_mode_dense_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_item_price_mode_sparse_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_query_hot_sequence_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_p2l_top_layer_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_outerctr_top_layer_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_outerprm_top_layer_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_outerprm_itembias_top_layer_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_outerleads_top_layer_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_outerleads_itembias_top_layer_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_outerprm_ori_prm_output.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_outerprm_ctr_top_layer_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_outerprm_pcvr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_outerleads_pcvr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_account_type_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ocpc_action_type_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_qcpx_discount_rate.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_qcpx_discount_rate_cap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_item_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_item_chain_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_no_bid.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_item_align_rqvae_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_item_align_similar.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_item_align_rqvae_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_item_align_similar.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_photo_10s.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_app_invoked.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_click_searched_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_ecom_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_click_2_s_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_media_pay_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_app_invoked_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_click_15d_21d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_search_click_2_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_feed_long_play_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_click_2_p_10s_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_deeplink_evocation_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_feed_like_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_search_playlet.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_feed_collect_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_conversion.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_search_playlet_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_click_2_p_10s_7d_15d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_conversion_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_search.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_search_com_play.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_feed_follow.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_deeplink_evocation.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_photo_10s_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_feed_follow_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_search_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_click_7d_15d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_search_valid_play_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_media_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_search_valid_play.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_photo_end.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_search_click_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_feed_long_play.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_feed_collect.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_ecom_pay_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_photo_end_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_search_10s_7d_15d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_search_com_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_click_21d_28d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_search_7d_15d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_searched_10s_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_feed_like.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_pay_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shelf_merchant_ecom_goods_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shelf_merchant_photo_play_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shelf_merchant_photo_click_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shelf_merchant_live_play_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shelf_merchant_live_click_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shelf_merchant_live_goods_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_item_impression_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_item_click_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_play_3s_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_play_5s_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_play_end_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_order_paied_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_order_submit_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_goods_view_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_search_item_impre_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_search_item_click_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_search_play_3s_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_search_play_5s_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_search_play_end_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_real_time_search_order_paied_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_author_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_account_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_product_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_second_industry_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_cluster_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_photo_min_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_author_min_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_account_min_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_product_min_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_second_industry_min_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_cluster_min_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_ecom_paycnt_label.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_live_threshold_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_ocpx_action_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_goods_sid_matched_cnt_goods_view_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_goods_sid_matched_cnt_goods_view_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_goods_sid_matched_cnt_goods_view_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_goods_sid_matched_cnt_order_submitl_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_goods_sid_matched_cnt_order_submitl_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_goods_sid_matched_cnt_order_submitl_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_photo_sid_matched_cnt_play_10s_l_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_photo_sid_matched_cnt_play_10s_l_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_photo_sid_matched_cnt_play_10s_l_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_photo_sid_matched_cnt_play_end_l_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_photo_sid_matched_cnt_play_end_l_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_photo_sid_matched_cnt_play_end_l_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_title_select_tokens_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_title_select_tokens_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_title_select_tokens_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_title_select_tokens_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_title_select_tokens_5.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_title_select_tokens_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_title_select_tokens_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_title_select_tokens_8.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_title_select_tokens_9.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_title_select_tokens_10.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_title_select_tokens_11.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_title_select_tokens_12.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_sdpa_name_fix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_global_pay_cnt_gmv.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_time_window_cost_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_time_window_gmv_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_time_window_ctr_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_time_window_cvr_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_time_window_send_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_time_window_convert_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sdpa_product_id_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_account_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_second_industry_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_uni_rank_size.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_uni_recall_size.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_uni_rank_size.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_uni_recall_size.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_cost_one_hour.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_photo_send_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_photo_convert_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_photo_gmv_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_photo_ctr_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_photo_cvr_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_live_unify_ctr_info_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_live_audience.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_live_cvr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_live_server_show_ctr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_ctr_model_unify_live_audience_rate.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_title_select_tokens_13.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_title_select_tokens_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_dense_p_2l_soft_pec_top_bar_status.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_qcpx_sparse_p_2l_soft_pec_top_bar_status.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_photo_send_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_photo_send_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_author_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_advertiser_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_photo_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_live_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_yellow_cart_cate_2_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_yellow_cart_cate_3_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_u_type_author_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_l_type_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_u_type_l_type_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_qcpx_photo_self_train_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_inner_photo_qcpx_coupon.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_paycnt_indirect.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_qcpx_live_pec_style_abtest_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_sparse_qcpx_live_pec_style_abtest_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_qcpx_p_2l_soft_pec_style_abtest_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_sparse_qcpx_p_2l_soft_pec_style_abtest_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_qcpx_p_2l_hard_pec_style_abtest_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_sparse_qcpx_p_2l_hard_pec_style_abtest_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_photo_cost_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_photo_gmv_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_unit_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_photo_creative_num_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_unit_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_unit_gmv_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_recommend_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_recommend_type_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_cost_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_cost_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_convert_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_convert_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_gmv_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_gmv_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_send_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_send_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_photo_cost_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_photo_cost_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_photo_convert_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_photo_convert_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_photo_gmv_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_photo_gmv_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_creative_num_cur.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_p_2l_photo_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_qcpx_live_pec_style_abtest_hash_id_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_sparse_qcpx_live_pec_style_abtest_hash_id_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_qcpx_p_2l_soft_pec_style_abtest_hash_id_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_sparse_qcpx_p_2l_soft_pec_style_abtest_hash_id_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_qcpx_p_2l_hard_pec_style_abtest_hash_id_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_sparse_qcpx_p_2l_hard_pec_style_abtest_hash_id_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_industry_14d_paycnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_industry_14d_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_industry_14d_gmv.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_industry_7d_paycnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_industry_7d_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_industry_7d_gmv.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_impression_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_imp_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_play_end_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_click_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_good_view_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_order_pay_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_order_submit_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_order_pay_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_good_view_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_pos_comment_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_neg_comment_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_neu_comment_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_comment_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_comment_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_unify_inner_behavior.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_item_align_res_kmeans.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_item_align_pq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_align_oc_tree.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_item_align_ann.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_item_align_ann_hot.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_qcpx_photo_self_train_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_all_merchant_author_id_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_media_new_user_pay_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iap_purchase_page_impression_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_slg_new_user_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_id_combine.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_gender_user_tag_1_combine.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_media_new_user_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_imp_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_imp_industry_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_imp_account_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_imp_author_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_play_end_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_play_end_industry_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_play_end_account_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_play_end_author_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_click_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_click_industry_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_click_account_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_click_author_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_good_view_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_good_view_industry_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_good_view_account_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_good_view_author_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_orderpay_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_orderpay_industry_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_orderpay_account_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_t_0_orderpay_author_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_order_submit_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_order_submit_x_7_cat_2_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_order_submit_x_7_cat_3_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_order_submit_x_7_cat_4_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_orderpay_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_orderpay_x_7_cat_2_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_orderpay_x_7_cat_3_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_orderpay_x_7_cat_4_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_good_view_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_good_view_x_7_cat_2_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_good_view_x_7_cat_3_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_t_0_good_view_x_7_cat_4_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_ad_purchase_pay_amount_1month_pay_only.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iaa_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iap_purchase_page_actionbar_click.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_social_new_user_pay_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iap_purchase_page_click_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_pzl_new_user_pay_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iaa_collection_lage_item_impression_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_ad_purchase_callback_purchase_amount_1month.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_social_new_user_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_plc_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iaa_collection_favorites_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_game_new_user_pay_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iap_collection_favorites_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_feed_landscape_complete_play_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_pzl_new_user_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_social_pay_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iap_collection_lage_impression_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_plc_pay_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_ad_purchase_pay_dedup_amount_1month.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_minigame_iaa_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_social_pay_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_ad_purchase_callback_purchase_amount_1month_inner.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_chess_card_fish_new_user_pay_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_ad_purchase_pay_dedup_amount_1month_pay_only.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_search_valid_long_play_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_game_new_user_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iaa_amount.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_ecom_new_user_pay_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iaa_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iap_collection_lage_impression_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_paid_fiction_new_user_pay_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_game_pay_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iap_collection_lage_item_impression_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_slg_new_user_pay_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iap_purchase_page_impression.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iap_purchase_page_actionbar_click_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_feed_non_left_click_2_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_paid_fiction_new_user_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_rpg_new_user_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iaa_collection_favorites_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_ad_purchase_pay_amount_1month.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_natural_search_valid_long_play_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_ad_purchase_callback_purchase_amount_1month_pay_only.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iaa_collection_lage_item_impression_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_new_user_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_rpg_new_user_pay_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iap_purchase_page_click.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_paid_fiction_pay_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iap_collection_lage_item_impression_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_game_pay_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_paid_fiction_pay_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iaa_amount_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iaa_collection_lage_impression_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_minigame_iaa_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iap_amount_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iap_amount.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_ad_purchase_order_amount_1month_inner.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_new_user_pay_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_ecom_new_user_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_feed_non_left_click_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iaa_collection_lage_impression_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_chess_card_fish_new_user_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_playlet_iap_collection_favorites_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_search_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_app_use_duration.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_external_flow_play_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_is_cny.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_search_stay_duration.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_collect_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_like_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_is_zhuzhan.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_is_jili.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_internal_flow_play_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_app_download_channel.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_search_inner_play_duration.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_channel.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_complete_play_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_is_neitou_today.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_user_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_device_active_degree.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_is_new_device.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_long_time_play_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_play_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_is_retained_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_is_zhuanqian_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_is_zhuanqian_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_is_push.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_is_retained_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_launch_search_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_play_tube_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_task_amount_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_channel_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_visit_count_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_task_count_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_is_retained_15d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_is_retained_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_play_duration.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_days_since_last_visit.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_media_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_play_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_is_neitou.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_show_cnt_log.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_complete_play_cnt_log.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_long_time_play_cnt_log.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_valid_play_duration_7d_log.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_like_photo_cnt_log.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_valid_play_cnt_7d_log.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_collect_cnt_log.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_valid_play_cnt_log.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_like_photo_cnt_7d_log.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_valid_play_duration_log.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_complete_play_cnt_7d_log.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_collect_cnt_7d_log.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_long_time_play_cnt_7d_log.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_show_cnt_7d_log.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_create_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_new_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_main_tag_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_create_time.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_xi_fan_is_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_xi_fan_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_xi_fan_is_top.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_month.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_sign.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_materials_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_hot_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_ranking.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_hot_sumvalue.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_a.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_b.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_5.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_8.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_9.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_10.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_11.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_12.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_13.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_14.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_15.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_16.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_17.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_18.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_19.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_20.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_adcu_text_g_21.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_emb_256.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_tube_user_emb_1024.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_use_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_qcpx_photo_self_train_tag_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_yellow_car_avg_price_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_yellow_car_max_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_sparse_yellow_car_min_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_price_1h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_cnt_1h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_gmv_mean.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_gmv_max.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_gmv_min.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_ordercnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_price_3h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sparse_item_live_realtime_cnt_3h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_lps_20_in_sample_weight.cc"   // NOLINT
#if defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

namespace ks {
namespace ad_algorithm {

void register_bs_feature_group_15() {}

// auto create for register

}  // namespace ad_algorithm
}  // namespace ks

#endif
