#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_reco_feature_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_interactive_form.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_large_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_ecom_short_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_negative_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_app_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conversion_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_pay.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_purchase_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_short.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_author_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_lps_user_stats_30d.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_ecommerce_product_name.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_ctr_test.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_brand.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sjh_follow.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_click_card_seller_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_context_info_app.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_click_item_id_list_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_20wconv_ec_kwd_idxs.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_larger_author_photo.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_app_list_ad_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_feature2.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_report_count.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_face_ids.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_face_attribute.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_add_fans_cnt.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playedend_no_lps_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_pay_extend.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_detail_item_imp_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_16.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lpc_extend_with_time_real.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_search_pos_photo.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_price_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_cat_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_seq_item_hash_id_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_id_dense_v3.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_uri_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_20wspeech_ec_kwd_idxs.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_product_labels.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_pay_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_pay_info_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_inc.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_app_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_consumption_level.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_simple_product.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_category_ad_degree_180d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_auto_cpa_bid_nebula.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_region.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_cnt_ts_form_submit.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_item_click_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_attribute_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_attribute.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_jinjian_extend_xiaodai_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_click_mat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_impression_rewarded.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_new_install_app_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_detail_form_submit.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_purchase_extend_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_photo_media_ad_style.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_invoke.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_base_uri_lps.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_product_conv_medium.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_fin_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_coin_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_coin_count_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_has_yello_trolley.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_ecom_days.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_scvr.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_comp_realtime_pe_mmu1_with_time.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_lps_fin.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_comp_realtime_click2_pn_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_cate1_id_scale.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_gambling_app_installed_num.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_author_fans.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dowload_industry_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_match.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_item_impression.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_ad_style_sub_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_action_nebula_v1.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_querytoken_ad_unit_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_match_dense_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_ecom_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_ecom_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_product_name.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_product_name_vacation_holiday.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_flash_duration.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_enhance_follow_author_htag_lv3.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_productids.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_cost.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_recall_target_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_recall_target_info_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_last_impression_mingap_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_lps_user_stats_7d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_simple_industry.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_merchant_list_online_item.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_user_rt_browse_type.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_virtual_creative_title_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_thanos_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_conversion.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_product_name.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_itemclick.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_fin_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click2_productIds.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_dislike_topic.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_embedding_play_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_browse_category_tag_sum.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_same_author_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_same_author_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_app_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_photo_id_shallow_action_3d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_ocpx_action_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_replayed.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_pay_info.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_ad_lda.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_detail_context_author_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_cover_feature_ctr_imp.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_app_package_size.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag_fix_v1.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_product_name_clk_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_union_dense_callback_event_train.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_interest_imp_keywords.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_media_third_class.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_lps_account_stats_30d.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_spuids.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_industry_id_shallow_action_3d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_no_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reco_user_like_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_click2_second_category_30d.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_video_feature_moco_conv_thanos.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_view_like_photo_label_newhash.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_slogan.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_ad_app_id_media.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_action_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_action_timestamp_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_action_list_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_action_timestamp_list_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_realtime_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_realtime_v3.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_realtime_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_realtime_v3.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_base_uri_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conversion.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_standard_live_room_seller_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_invoke_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_id.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_realtime_playend.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cpm_account_d7.h"
// // NOLINT
// need fix
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_available_disk_percent.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_cate3ids.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_dense_getui_data_extend.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_querytoken_ad_campaign_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_user_comp_ad_play5s_extend_photo_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_rate_7d_10s.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_new_industry.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_item_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_context_info.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_photo_id_shallow_action_7d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cpa_bid.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_bid.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_interest_click2_keywords.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_union_dense_callback_event_infer.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_union_dense_adstyle.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_union_ecpm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_mmu_hetu_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_photo_mmu_hetu_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_ecom_days.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longtermaction_and_industryid.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_callback_event_train.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_soureType.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top3_item_impression_realtime.h"  // NOLINT
// need fix #include
#include  "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_new_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ecom_kuai_category_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_detail_ads_resource_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_negative_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_click_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_datetime_hour_week_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_industry_id_deep_action_90d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_past_app_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_count_feature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_new_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conv_extend_edu_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_item_id_list_short.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_eds_comp_product_name_cnt_fix.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_interest_categorys.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_paid_seller_id_list_scale.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_deep_conv_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_lda_photo_ad_lda.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_item_clk_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_realtime.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_wangzhuan.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_click_photo_product_sequence.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_active_level_from_comminfo.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_photo_face_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rewarded_candidate_queue_size.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_enhance_follow_author_htag_lv1.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_mmu_item_attr.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_p5s_v2_fix.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_credit_extend_short.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_cname_2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cpm_unit_d7.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_cnt_7d_1m.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_quality_filter_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shortterm_action_cnt_ts_item_imp.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_detail_context_pattern_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_query_token.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_order_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_order_count_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_live_ave_gap.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_material_tag_cat.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_tag_sum.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_game.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_medium_attribute.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_app_list_product.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_callback_event128_lsp2appoint.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_playend_action_nebula_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_search_pos.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_app_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_conversion_industry_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_app_list_ad_app_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_channel_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_click_product_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_industry_id_deep_action_30d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_seller_clk.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_no_click_fin.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_callback_event_sparse.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_live_info_replayed.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_fin_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_retention_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_fin_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_fin.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_game.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_follow_htag_match.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_14.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_region_from_ip_v1.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_gender.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_ecom_days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_cate2_id_gsu_item_id_list_longterm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_text_feature_bert_conv_feed.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_installed_app_sequence_share.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_device_disk_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_adx_user_sourceType.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_effect_purchase_cpm_pos_d7.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_eds_comp_item_click_list_ts.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_photo_target_cpm_medium.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_search_pos_account_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_photo_media_pos.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_avg_play_rate_global.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_datetime_hour_week_brand.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_time_sequence_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_position_status.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_negative_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_imei_industry_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_description.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_ad_info_playend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_behav_stat_online.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_conversion_ratio.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_comp_click2_photo_id_list_scale.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_account_target_cpm_medium.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_device_info_ad_user.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_cate1_id_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_xiaodai_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_author_id_longterm.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_deep_conversion_industry_d7.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_ped_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_uidcontextproduct.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_ocr_title.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_photo_impression_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_video_quality.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_conv_fin.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_negative_newhash.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_native_photo_item_impression_photo_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_product_name_pe.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_keyword_app_degree_7d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_negative_ad.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_ad_style.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_share_extend.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_knews_user_coin_level.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_ecom_short_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_to_live_author_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_714_app_installed_num.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_comp_realtime_click2_mmu2_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_last_click_mingap_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_photo_author_fans.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_id_media_purchase_property.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_nebula_ad_playend_extend.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_interests_label_response_uv_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_ad_info_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_num_extend_ecom_dense_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_num_extend_ecom_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo_buy_item_seller_id_list_short.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_photo_follow.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_medium_sub_industry_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_industry_ad_industry.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_creative_endcard_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_medium_vacation_holiday.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_exp_model.h"  //
// NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_download_new1.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_pos.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_car_tag_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_cate2_id_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_shallow_action_product_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_life_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_medium_industry_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_ecom_days.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_querytoken_photo.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_item_id.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_universe_flag_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_universe_flag.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cpm_sec_industry_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_match_num_long_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_creative_material_dense_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_dup_cover_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_author_id_seq_longterm.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_play_ecom_cate2_v1_seq_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_enter_profile_photo.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_landing_jd_brand_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_15.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_yellow_cart_click_seller_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_to_live_photo_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_item_imp_v2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_photo_invoke.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_user_price_level_distribution_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_simple_industry.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_click_industry_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_playend_more_combine_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_feature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_impression_cnt_seven_day.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_text_feature_bert_imp_feed.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dsp_embedding.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_game_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_old_second_cate_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dup_photo_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_edu_with_time.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_rank_cpm_info.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_rank_cpm_info_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_rank_ctcvr_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_shallow_act_photo.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_photo_comment_game.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_like_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_like_count_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_play3s_action_nebula_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_dup_cover_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_hetu_tag_l2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_play3s.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_industry_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_play3s_ratio.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_appcontextproduct.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_app_list_sequence_share.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_spu_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag_no_userid.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_active_apps.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_account_deep_conv_pos.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_total_memory.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dl_click_cluster.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_ad_campaign_type.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shortterm_action_cnt_ts_form_submit.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_has_yello_trolley.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_play_end_hetu_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_item_impr_7d_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_shop_keeper_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_bid_type_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_landing_jd_cate1_list.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_loc_advertiser.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_live_last_gap.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_name_media_online_property.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_comp_playend_product_id_list_scale.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_distance.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_browse_type.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_match_num_long.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_category_app_degree_7d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_followed_author_list_v2.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_action_uv_submit_cpm_pos_d7.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_photo_id_user_ad_lda.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_second_cate_with_passed_day.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_photo_bid_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_advertiser_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_info.h"
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_photo_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_photo.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_callback_event_dense.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_reco_mat_embedding.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_stream_item_info.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_diff_app_list.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_creative_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_user_distance_tenkm.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_search_pos_dup_cover_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_like_author_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_device_photo_new_v1.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click1_no_click2_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_apps_type.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_cooperation_mode_new.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_conv_medium_sub_industry_ctr_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_author_user_lda.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_like.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_item_impression_industry_ad_industry_realtime_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_item_impression_industry_ad_industry_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_short.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_channel_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_reco_live_wtr.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_goods_view_seller_id_list_scale.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_novel_plot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_attribute_live.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_app_score.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_rank_ad_playtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shortterm_action_list_ts_item_imp.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_product_name_clk.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_title.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_available_memory_percent.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_seller_pe.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_eds_comp_item_invoke_list_ts.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_item_click_more_combine_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_mmu_tag_online.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conv_extend_fin_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_ks_map_cate_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dedup_photo_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_media_second_class.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_retention_media_id_product_is_new.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_bclick_list.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_datetime.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_share.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_asr_2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_device_info_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_photo_new_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_ecom_order_type_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_topic_photo_topic.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_text_feature_ctr_imp.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_photo_num.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_deep_action_product_list.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_cate3_id_scale.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_gender_n.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_pos_id_3t.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_1t.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_model.h"
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_retention_product_1d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_num_extend_ecom_dense_v2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_fin_days.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_pastday_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_crm_is_wangzhuan.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_pname.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_buy_cat_industy.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_play_ecom_photo_v1_seq_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_realtime_ad_info.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_holidays.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_author_p2l_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_novel_theme.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_id_unlogin.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_order_submit.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_click_bow_3d_words.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_click_bow_3d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_click_bow_7d.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_uid.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_simple_product.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag_no_userid_fix_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_match_dense_num_14d_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_cate2_id_scale.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_comp_ecom_with_time.h"
// // NOLINT
#include  "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_landing_tb_cate2_list.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_standard_live_room_live_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_author_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_extend_type.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_to_detail_merchant_item_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_mmu_cluster2w.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtb_flag.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_type_count_feature.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_media_sub_industry_id_v2.h"
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_detail_ads_union_special.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_follow_count.h"

// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_account_target_cpm_pos.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_region_from_ipv6.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_click_cart_seller_id_list_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_text_feature_bert_emb.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_apps_games_label.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_click_author_slot.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_top_short_topic.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_followed_merchant_author_list_no_author.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_unfollow_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_unfollow.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top_like_author.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_thanos_play_rate_stat.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_level_author_stat.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_third_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_like_photo_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_watched_author_list_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_photo_id.h"

// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_download_installed.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_to_detail_merchant_author_list_no_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_product_name_deep_action_30d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_detail_like_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_business_interest_newhash.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_comp_realtime_pe_mmu3_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_edu_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_name.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_rank_name.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_mmu_cate2_id_list.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_material_tag_industry.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_item_price.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cost_account_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_comp_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_game_play_method.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_order_submit_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_mmu_cate1_id_list.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_conversion_extend_short.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_nebula_ad_click_extend.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_explore_stat_new.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_negative_brand.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cpm_unit_d7.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_12.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_follow_author_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_follow_author_id_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sjh_30_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_cnt_ts_item_imp_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_his_stat_dis.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_text_feature_bert_cluster.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_retention_product_3d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_comment_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click2_itemids.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_cover.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conversion_extend_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_item_click_list.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_conversion_product_d30.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_pos_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_loc.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_topic.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_no_click_edu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_click1_no_click2_extend.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_fanstop_userinfo_and_photoinfo.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_browse_brand_tag_sum.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_v2_dup_cover_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_simple_industry.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_lps_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_lps.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_live_info_playend.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_rate_7d_1m.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_share_extend_edu.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_nebula_ad_item_click_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_goods_impression_seller_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_item_click_type_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_new_industry_v3_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_new_industry_v3_select.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_query_item_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_query_user_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_query_user_onehot_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_query_item_onehot_dense.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_keywords.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shortterm_action_cnt_ts_order_pay.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_author_id_longterm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_download_install_rewarded.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_retarget_same_auhor_new.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_industry.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_hate_topic.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_dup_cover_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_live_play_cnt_seven_day.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_account_deep_conv_medium.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_credit_profile_online.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_fin_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_universe_unit_stat_online.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_seller_id_short.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_event_jinjian_account.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_p3s_live_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_age_industry.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_fanstop_embedding_wtr.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_item_category_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_combine_product_pos_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_follow_author_id_v1.h"

// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_level_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_likelist_liveauthor.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top3_impression_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top3_impression.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_cate3_id_gsu_item_id_list_longterm_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_action_search_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_deviceid_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_deviceid_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_photo_target_cpm_pos.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_video_feature_moco_emb.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_ecom_short_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_pay_fin.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_label.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_play5s_ratio.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_avg_play_rate_thanos.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_financial_product_info.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_account_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_creative_rule_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_third_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_neg_hetu_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cost_unit_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_only_author1t.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_download_installed.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_embedding_feature2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_feed_user_group.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_deep_action_industry_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_download_rewarded.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_like_topic.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_creative_behavior_intention_keyword.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_education_action.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_sellerids.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_online_retail_info_pay_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_played_3s_no_author.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_impression.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_lps_ecom.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_product_conv_pos.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_actionbar.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_ad_campaign_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_label_match_type.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_creative_material_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_last_item_impression_mingap_realtime.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_num.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_is_connected_live.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_with_time_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_match_cnt.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_reco_user_realtime_action_and_creative.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_click1_no_play3s_extend.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_playend_ratio.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_cpu_count.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_comp_ecom_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_new_industry_v3.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_slot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_hetu_tag_l1.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_photo_deep_conv_pos.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_media_id_ad_style.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_city_village.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_education_class_info3.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_imporession_newhash.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_play3s_extend_short.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_no_conv.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_target_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_no_item_click_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_fanstop_live_author_fans_count.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_comp_click2_app_id_list_scale.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_cover_feature_ctr_conv.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_info_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_conversion_product_d7.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_recall_target_info_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_online_retail_info_spu_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sub_page_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_network.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_interactive_form_native.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_app_list_media_industry.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_edu_course.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_detail_context_photo_mmu_text_cluster.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_caption_segment_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_duration.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_stat.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_querytoken_account_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_photo_embedding_ctr.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_old_second_cate_rfm.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_impression_noclick_new.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_negative_new_slot.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_retention_product_7d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reco_slide_fm_re.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dsp_embedding.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_item_impression_list.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_click_author_mat.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_playedend.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_creative_hori_vert.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_emb_query2.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_adload_category_online.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_rewrite_query.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_vision_feature_cover_ctr_imp.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_author_new_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_playend_photo_second_industry_sequence.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_product_real_name.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_corporation.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_interest.h"  // NOLINT
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_impression_extend_short.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_item_impression_industry_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_product_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_poi_author_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_app_start_type.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_lps_user_stats_30d.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_gift_count.h"
// // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_action_cnt.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_retarget_author_slot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_reco_live_ctr.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_name_media_purchase_property.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_age.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_ad_info_play5s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_product_name_shallow_action_30d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_crm_product_type.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_video_feature_moco_cluster.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_has_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_ad_style_new.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_product_target_cpm_medium.h"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_mark_num.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_install_app_flag.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_search_feature_emb.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_reco_user_realtime_action_and_mmu_class.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_text_feature_bert_emb.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_yellow_cart_show_live_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_edu_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_ecomm_hetu_tag_l2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_context_posid_cluster.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_smart_target_calibrator.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_combine_account_ad_style.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_landing_jd_cate2_list.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_creative_template_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_campaign_id.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_audience_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_audience_count_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_account_conv_pos.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_name_media_second_class.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_itemclick_action_nebula.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_comp_realtime_cv_mmu1_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_week_stay_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_cnt_7d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_conversion_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_brand.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_cnt_ts_p3s_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_ad_style.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_material_tag_age.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_age_segment_vacation_holiday.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_smart_matching_thres_nebula2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_predict_scvr.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_played5s_no_item_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_app_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_impression_noclick_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_music_global_stat.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate3_id_gsu_author_id_list_longterm.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_cnt_7d_10s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_lps_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_online_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_media_first_class.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_last_click_mingap_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_no_play3s_ecom.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_material_feature_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_photo_conv_medium.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_from_page.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_pos_predict.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_info_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_mmu_cluster_2w.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cpm_product_d7.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_app_conversion.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_author.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_enhance_follow_author_htag_lv2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_native_photo_goods_view_photo_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_download_complete_rewarded.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_cluster.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_avg_play_rate_explore.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_photo_conv_pos.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_level_photo.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_level_photo_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_mmu_cate3_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_pay_buyer_uv.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_item_click_uv.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_total_order_gmv.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_watch_uv.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_total_watch_uv.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reward_distbt_coin_no_ad_cnt_sd.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_device_realtime_network.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_app_name.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_item_impression_ad_realtime.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_userid_livecoverurl.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_product_deep_conv_medium.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_htag_uniq.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_detail_item_imp.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_callback_action_industry_feature_credit.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_new_context_info_native.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_hate_topic.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_media_industry_id_v2.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_strategy_type.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_nextday_stay_rewarded.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_nebula_ad_play3s_extend.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_source_type.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_first_industry_dense_d7.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ipc.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_callback_action_industry_feature_jinjian.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_deep_conversion_product_d30.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_app_category_app_name.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_attribute_photo_new_v1.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_ad_item_click_num.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_product_name_pe_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_photo_media_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_unit_campaign_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_jinjian_extend_short.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_universe_creative_stat_online.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sjh_imp.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_callback_event_onoff_consistent.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_second_cate_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_description.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_novel_role.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_third_ecom_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reco_user_click_list.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_ocr_2.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click2_cate2ids.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conversion_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_action_uv_purchase_cpm_pos_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_reco_user_realtime_action_and_product_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate2_id_longterm_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_unit_bid_sparse.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo_click_cart_seller_id_list_short.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_large_new_photo_slot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_inner_delivery.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cost_account_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_report.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_account_conv_medium.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_jlrw_show_pv_cnt_seven_day.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_deep_conversion_industry_d30.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_campaign_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_ad_style_3t.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_cate3_short.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_event_type_product_name_no_pdd.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_photo_bid_type_ocpx_action_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cost_sec_industry_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_embedding_target_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_rate_7d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_download_started.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_watch_tag_live_mmu_tag_online.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_photo_bid_price.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_uri_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_uri.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cost_sec_industry_d7.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_exp_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_money_pv_cnt_seven_day.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate1_id_longterm_v2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_user_click_industry_time_gap.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_is_soft.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_item_put_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_lda.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_no_play3s_xiaodai.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_width_height_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_width_height.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_rerank_pos.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_abtest_hash_id_native.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_source.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_ad_unit_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_conv_medium_industry_dr_new.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_pname_2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_installed_app_newhash.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_order_amount.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_shallow_conv_ocpc_action_type.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_reco_emp_xtr_dis.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_text_feature_ctr_conv.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_no_conv_ecom.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_photo_like_list.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ecom_and_other_lps_item_ocpx_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_htag1.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_credit_jinjian_grant_cnt.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_impression_avegap_new.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_share_count.h"  // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_last_impression_mingap_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag_no_uid.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_level_photo_region_from_ip.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_matchtype.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_matchtype_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_rewrite_query_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_enter_source.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_bidword.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_productname_token.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_productname_token_reverse.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_extend_type_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_qr_score_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_relevance_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_strategy_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_strategy_type_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_parser_text_v1.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_parser_text_token_v1.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_query_combine_match_num.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_kbox_type.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_query_source.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_query_catgory_class3.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_query_catgory_class2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_refer_photo_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_akg_query_embedding.h"
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_ad_style_fix.h"
// // NOLINT
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_app_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_promotion_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_title.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_tags_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_edu_days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_create_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_creative_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_native_live_yellow_carts_click_seller_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_no_invoke.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_clk_zhanfen_tag_online.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_page_id_dense.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_hour.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_negative_photo_ad_lda.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_product_list.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_text_feature_bert_imp.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_live_info_itemclick.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_playend_conv_ratio.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_light.h"  //
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_htag.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_keyword_app_degree_180d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_action_uv_conversion_cpm_pos_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_info_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_eds_comp_item_conv_list_ts.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top1_interact_industry.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_impression_noclick_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_xiaodai_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_followlist_liveauthor_seq.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_old_first_cate_with_passed_day.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_enter_profile_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_played3s_no_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_playend_extend_short.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_newhash.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_hetu_tag_l3.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_report_ad.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_first_cate_with_passed_day.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_os.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_app_installed_cate.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_fix.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_comp_ecom_with_time.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rewarded_conv_pred_val_infer.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_click_ratio.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click2_no_conv_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_global_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_cost.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_universe_campaign_stat_online.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ecom_product_title_emb.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_item_clk_v2_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_product_name_conv_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_new_fanstop_photo_bid_price.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_interests_label_query_uv_d7.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_holiday_vacation.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_photo_video_feature_moco_cluster.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_cnt_ts_item_clk.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_no_conv_ecom.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_click_industry.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_detail_p5s_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_edu_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_brand_tag_sum.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_collect_tag.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_gender_industry.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_follow_mat.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_hetu_tag_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_hetu_tag.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_purchase_extend_game.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_knews_app_uid.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_level_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_cnt_conv.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_rank_server_show_ctr.h"
// // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_comment_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_comment_count_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_mmu_class.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_is_ad.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_detail.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_adx_cvr.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_dense_has_yello_trolley.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_photo_real_product_name.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_item_id_longterm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_creative_behavior_intention_category_backtrace.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_click_photo_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lda.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_exp_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_played_fix5s_no_item_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_p3s_seller_id.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_query_keywordcnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo2live_photo_id_seq.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo2live_author_id_seq.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_action_uv_d7.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_conv_fix.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_collect_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_cooperation_mode_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rt_browse_type_native.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_ad_info_itemclick.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cost_product_d7.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_industry_new.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_live_bid_type_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_yellow_cart_show_seller_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_sparse_getui_data_extend.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_ocpc_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_new_fanstop_photo_bid_type_ocpx_action_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_11.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_refresh_direction.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_id.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_realtime_play5s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_to_detail_author_list_v2.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_info_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_info_new_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_user_detail_universe_context_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_prodtct_name_ug_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_photo_id_user_lda.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_cs_count_data.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_seller_conv.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_account_id.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_lda_photo_lda.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_watched_author_list.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_watched_author_list_new.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_education_class_info.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_cnt_ts_p5s_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_mmu_classification_143.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dmp_id_fea.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_interest_click_keywords.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_live_user_fans_flag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_live_user_fans_flag_sub_page_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_live_user_fans_flag_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_click2_no_invoke_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_vision_feature_cover_ctr_conv.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_old_first_cate_list.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_info_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_old_first_cate_rfm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_like_mat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_download_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_download.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_sold_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_brand.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_request_times_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_color.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_short_game.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_emb_query3.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_pdn_sim_seq_v0.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_pdn_sim_seq_v1.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_pdn_sim_seq_num_v0.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_text_feature_bert_cluster.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_unit_id.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_action_sum_online_dense_combine_new_lookalike.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_follow_author_time_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_app_list_media_ad_style.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_click2_no_conv_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_info_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_conv_medium_industry_ctr_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_querytoken_ad_campaign_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_account_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_played5s_no_lps_newhash.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_upload_count.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_product_name.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_text_feature_bert_conv.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_device_live.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_device_realtime_os.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_orig_music_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_new_context_info_rp.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_info_noip.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playedend_no_lps_newhash.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_online_retail_info_category.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_landing_tb_brand_list.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_enter_profile_tag.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_click_industry_d30.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_ocpc_action_type.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_pay_edu.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_level_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_author_id_match_num_sparse_longterm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_play5s_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_creative_behavior_intention_category.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_played5s.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_browse_tag_sum.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_follow_photo_slot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_no_author_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_play_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cover_star_cluster.h"  // NOLINT
// need fix
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_total_disk.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_imp_second_category_30d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_play_brand.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_ad_follow_lda.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cover_mmu_embedding.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_material_tag_benefit.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top3_interact_industry.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click1_no_play3s_extend.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_rate_7d_5s.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_datetime.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_lps_poster_rate.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_clickliveauthorlist_near.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_wangzhuan.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_landingpage_emb.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_photo_id_deep_action_30d.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_like_author_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click2_cate1ids.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_combine_product_media_wangzhuan.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag_no_userid_fix_v1.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_exp_ctr.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dnn_cluster_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_info_imei.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_count_feature.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_description_2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_dup_cover_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_item_click_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_num.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_lps_xiaodai.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_like_extend.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_unit_type.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_cvr.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_extend_ecom_days.h"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cpm_sec_industry_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_explore_stat_ctr.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_mmu_classification_143.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_corporation_name.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shortterm_action_cnt_ts_item_clk.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_vision_feature_cover_ctr.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_author_weight.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_ocpc_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_ocpc_dense2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_ocpc_dense3.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_ocpc_dense4.h"

// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_brand.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_enhance_tag_favor.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_embedding.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_app_id_3t.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_edu_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_id_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_mmu_cluster_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_mmu_global_cluster_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_wide_item_feature_dense.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_live_bid_price.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_buy_cat_item.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_inc_cut.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_playend_photo_product_sequence.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_live_info_play5s.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top6_interact_industry.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_support_cat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_conv_medium_sub_industry_dr_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_video_feature_moco_imp_thanos.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_app_list_media_uid.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_source_type.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_edu_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_target_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sjh_p3s.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_photo_media_wangzhuan.h"
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_rewarded_pageid.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_combine_account_media_uid.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_keyword_ad_degree_7d.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_num.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_medium_uid.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_photo_id_media_purchase_property.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_jlrw_task_click_pv_cnt_seven_day.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_click_industry.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_eds_comp_item_invoke_list_ts_fix.h"
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_upload_type.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_second_category.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_mmu_class623.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_pos_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_category_app_degree_180d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_xiaodai_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_app_list_ad_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_product_share.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_longterm_photo_p5s.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_giftlist_liveauthor.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_id_media_first_class.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_ecom_short_with_time_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_app_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sjh_like.h"
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_collect_photo.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_novel_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_app_category.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_universe_long_item_impression.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_embedding_p3s.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_photo_ad_lda.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_invoke.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_action_upload_tag.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_enhance_follow_htag_all.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_ecomm_hetu_tag_l3.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_media_purchase_property.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag_fix_v2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_xiaodai_with_time.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_vision_feature_cover_ctr.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_device_battery_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_cate3_id_gsu_author_id_list_longterm_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click_product_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_tag_list.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_comp_realtime_click2_mmu3_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_hate_tag.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_page_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_hate_photo.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_comp_ecom_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_is_living.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_temids.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_imp_second_category_14d.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_uid_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_c2_retention_new_pay_stat_3days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cpm_product_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_tag_category.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_app_developer_name.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_creative_style_material_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_game.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_jk_samples_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_creative_material_dense_d7.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_smb_with_time.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_ecommerce_product_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_item_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click2_spuids.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_medium_game_category_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_slot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_block.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_landing_tb_cate1_list.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_category_ad_degree_7d.h" // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_target_info.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_description_bert.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_universe_photo_conv_medium_industry_dr.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_ocr_title_2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_fin.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_like_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_played3s_author_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_combine_product_ad_style.h"
// // NOLINT
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_click_ad.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_library_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_category_app_install.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_shallow_action_industry_list.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_device_price.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_invoke_extend_days.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_itemcf_product_name.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ecom_and_other_lps_item_type.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_slogan_2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_age.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_deep_conversion_product_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_comment_num.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_ecomm_hetu_tag_l1.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_played_1m_no_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_played3s.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_new_detail_page_click_list.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_id_slot.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_user_click_action_nebula.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_video_feature_moco_cluster.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_c2_lps_cmd.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_photo_deep_conv_medium.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_media_wangzhuan.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_ecom_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_app_invoke_extend.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_ad_like_lda.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_watch_tag_online.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lda.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_active_app_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_click_photo_second_industry_sequence.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_fans_count.h"

// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_impression_avegap_realtime.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click2_sellerids.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_product_deep_conv_pos.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_third_ecom_with_time.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_photo_media_uid.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_first_cate_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_like.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_attribute_dup_photo.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_large_new_author_slot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid_target_cpm_account_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_ecom_days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_cate1ids.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate3_id_gsu_item_id_list_longterm.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_replayed.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_live_info_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_applist_cate_match_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_last_item_impression_gap.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_hate_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_app_list_media_pos.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_live_face_two_fix.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_audience.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_media_online_property.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_native_photo_goods_view_cate3_id.h" // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_auto_cpa_bid_smart.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_embedding_ctr.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_stat_ftr.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_selling_goods_embedding_v2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_detail_p3s_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dl_cluster.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_query_ad_industry.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_virtual_creative_sticker_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_invoke_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dislike_topic.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_eds_comp_photo_cnt.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_playend_rewarded.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_order_stat_ctr.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top4_interact_industry.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate3_id_longterm_v2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_level_loc.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_simple_to_standard_cnt_7d_5s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_c1_retention_new_pay_stat_3days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_adx_campaign_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_industry_group_id_rr.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_classification_143.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_playend.h" // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_emb_query1.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_pos_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_username_seg.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_product_name_shallow_action_7d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_user_comp_ad_click2_extend_product_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_cnt_form_submit_v2.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_photo_mmu_hetu_tag_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_user_combine_channel.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_cnt_item_clk_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playedend_no_invoke.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_follow_no_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sm_dense_infer_callback_event.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_photo_click_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_conversion_industry_d30.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_holiday_next.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_nebula_ad_play5s_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_is_filter_by_feedback.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_pos_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_forwardlist_liveauthor.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_imp_zhanfen_tag_online.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_id_new.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_pay_extend_fin_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_first_cate_rfm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_shortterm_action_list_ts_p3s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_play3s.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_no_play3s_fin.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_callback_action_product_feature_jinjian.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_delivery_type.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_tab.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_category_tag_sum.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate2_id_longterm.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_novel_name.h"
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_followed_author_list_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_online_retail_info_product_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reward_distbt_coin_watch_photo_cnt_sd.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_app_list_media_app.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend_comp_ecom_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_seller_id_scale.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_num_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_ad_render_type_distribution_d7.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_extend_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_userlevel_liveauthor.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_click_match_num.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_native_live_goods_view_cate3_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top5_interact_industry.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_app_list_ad_photo_id.h"
// // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_action_click_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_weekly_behav_author_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_element_type_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_conversion_rewarded.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_search_pos_user_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_click_bow_7d_words.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_video_feature_moco_emb.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_nextday_stay.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_filter.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_crm_info_product_feature.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_fin_with_time.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_c2_retention_new_pay_stat_7days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_lps_url_domain_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_slot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_and_live_info_play3s.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click2_cate3ids.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_direct_creative_cluster.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo_click_card_seller_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_global_brand.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_click_rewarded.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_c1_retention_new_pay_stat_7days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_game.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_purchase_extend_short_game.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_to_detail_author_list_new.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_to_detail_author_list.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_cate_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_retention_product_14d.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_order_paied_extend_ecom_days.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_entity_tag_sum.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_itemcf_sec_industry_name.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_longterm_action_cnt_item_imp.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_live_yellow_cart_click_live_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_dense_photo_roi_ltv.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_description_seg.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_comp_realtime_click2_mmu1_with_time.h"
// // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_media_uid_fix.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_ecom_with_time.h"  // NOLINT
// need fix
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_product_wangzhuan_media_wangzhuan.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_click_author_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_region.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_embedding_feature.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_app_dense.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_conv_medium_industry_cvr_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_photo_comment.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_clickliveauthorlist_hot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_cate_list_short.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_online_retail_info_item_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_new_install_app_all.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_ecom_mmu_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_conv.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_17.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sm_dense_train_callback_event.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_id_newhash.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_qr_score.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_cname.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_cat_new_seq.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_comp_playend_app_id_list_scale.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_download_installed_game.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_conversion_extend_game.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_only_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_education_class_info2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_watched_merchant_author_list_no_author.h" // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_next_stay_vacation.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_subcontextproduct.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_cate1_id_match_num_sparse_longterm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_slot.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_price_list.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_photo_conv_medium_sub_industry_cvr_new.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_cate2Ids.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_p3s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ecom_product_cover_emb.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_live_yellow_trolley_new.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_text_feature_bert_click_feed.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_live_author_fans.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_effect_conversion_cpm_pos_d7.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_creative_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_age.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_is_playable.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_rank_unify_cvr.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_subtitle_segment.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_merchant_list_online_author_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top2_interact_industry.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_product_target_cpm_pos.h"   // NOLINT
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_lik_zhanfen_tag_online.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_campaign_type_rewarded_dense.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_top3_click_realtime.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_baterry_percent.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_adload_feature_duration_online_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_extend.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_commerce_search_query_game_cate_1d_online.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_comp_ecom_with_time.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_roi_reciprocal.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_combine_account_pos_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_search_pos_product_name.h"  // NOLINT
// need fix #include
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_no_conv.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_action_nebula_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_ad_play3s_extend.h"  // NOLINT
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_htag0.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_cover_mmu_cluster.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cost_unit_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_xiaodai_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_user_comp_ad_click_extend_photo_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_eds_comp_item_ped_list_ts_fix.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_comp_click2_product_id_list_scale.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_played5s_no_lps_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_conversion_product_name_ad_product_name.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_template.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_played3s_no_item_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_device_memory_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_effect_submit_cpm_pos_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_industry_stats_online.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_posidv2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_industry_time_gap.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_dup_photo_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_cnt_ts_item_clk_fix.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_duration.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_order_by_live.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_sound.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_kuai_cate_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ocpc_action_type_rewarded_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_avegap_realtime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_xiaodai_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_callback_action_product_feature_credit.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_support_tag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_jinniu_xiaodian_product_id_v2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_adx_landing_jd_cate3_list.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_id_list_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_mmu_class_623.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_comment_extend_edu.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_old_second_cate_with_passed_day.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id_target_cost_product_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_callback_event_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_watched_merchant_author_list_scale.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression_no_click_info.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_querytoken_ad_industry.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_industry_name.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend_third_ecom_with_time.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rewarded_str_pred_val_infer.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rewarded_lps_pred_val_infer.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_model_conv_next_stay.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_13.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_embedding_multi_play.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_campaign_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_eds_comp_product_name_invoke.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_play5s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_nocnt.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_text_feature_bert_click.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cross_longterm_photo_p3s.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_list_ts_p5s_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_v2.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_smb_with_time.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_recall_relevance.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_no_conv_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_first_industry_dense_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_universe_fake_info.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtl_product_name_shallow_action_3d.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_order_second_cate_rfm.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_flow_profile_apps_effect_roi_cpm_pos_d7.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ug_combine_product_media_uid.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_conv_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_purchase_extend_short.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_signature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_creative_style_type.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_rtb_flag.h"  //
// NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_cate2_id_match_num_sparse_longterm.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_camera.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_download_completed.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rt_browse_type_1.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_universe_account_stat_online.h" // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_days.h"  //
// NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_photo_id_media_second_class.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_app_list_ad_cate_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_start_time_gap.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_order_paied_extend.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_item_optimization.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_creative_video_pic.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_click_cate_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dpa_price.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_click_cate3_id_match_num_sparse_longterm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_third_ecom_with_time.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_user_click_product_d30.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reward_raw_cpm.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_video_feature_moco_click_thanos.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_eds_comp_item_ped_list_ts.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_behavior_intention_keyword_ad_degree_180d.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_slot.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_gender_vacation_holiday.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_deep_conversion_bid.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dense_ad_conversion_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_account_id.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_photo_asr.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_is_smart_matching.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_mmu_class_623_all.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_smart_matching_thres_nebula.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_material_tag_actor.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_third_ecom_with_time.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_behav_online.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_union_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_userlevel_live.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_embedding_conversion.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_advertiser_info.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_material_tag_style.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_impression.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_new_context_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_follow.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_nebula_like.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_followed_merchant_author_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_no_conv.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_to_detail_merchant_author_list.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play5s_extend_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_ecom.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_live_action_feature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_photo_action_feature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_reco_like_num_v2.h"  // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_reco_seq_mask.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_fanstop_reco_seq_num.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_id_list_with_length.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_action_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_action_num.h"  // NOLINT

// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_seller_category_id.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_seller_click_stat.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_seller_live_trolley_stat.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_seller_pay_cate3_id.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_play3s_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playend_extend.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_datetime.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_click_action_nebula.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_click_item_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_item_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_click_cart_seller_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo_click_cart_seller_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo_buy_item_seller_id_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_cate2_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_cate3_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_cate1_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_cate2_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_price.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_dense_hash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_region_from_ip.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_category.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_new_industry.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_dense_onehot.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_creative_rule_id_sequence_share.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_installed_app.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_bid_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_caption_segment.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_ad.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_attribute_photo_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_device_photo_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_view_like_photo_label.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_business_interest.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_music.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_request_times.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_datetime_hour_week.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_retarget_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_offline_retailer_keyword.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_id_author_attr_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_impression_ad.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_author_user_ad_lda.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_follow.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_played_1m.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_merchant_list_online.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_played_3s.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_week_stay.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_user_watched_merchant_author_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_cate1_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_product_name_item_click.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_playend_action_nebula.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_combine_account_media_pos.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_author_id_longterm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_merchant_followed_author_list.h" // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_item_click_extend_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_click_extend_ecom_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_vision_feature_cover_ctr.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_play3s_action_nebula.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_seller_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_buy_item_cate3_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eshop_click_item_seller_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_followlist_liveauthor.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rt_browse_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_campaign_type.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_num_extend_ecom_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_playedend_no_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_from_client.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_newhash.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_combine_channel.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_game_tag_sim.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sdk_action_feature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_cat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_match_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_server_no_item_click_flag.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_high_quality_author_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_played5s_no_lps.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_recall_target_info.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_extend_ecom.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_pos_id.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_seq_num.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_id_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_base_uri.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_akg_user_int_seq.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_akg_user_tag_seq.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_akg_user_simi_seq.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_akg_user_hweight_rec.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_realtime_action_list_sim.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_unit_type.h"  // NOLINT

// mmu kge feature
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_mmu_kge.h"

// live start time
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_start_time_gap_plain_dis_src2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_start_time_gap_plain_dis_n_aid_src2.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_start_time_gap_plain_dis_n_aid_src2_dis_v2.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_page_id_dense_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_univ_app_package_std_list.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sim_gsu_feature.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sim_seq_ad_goods_v0.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sim_seq_ad_goods_v0_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sim_seq_ad_goods_v0_num.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_normalized_attribute_score_dense.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_u2u_gsu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_u2u_match.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_u2u_match_category.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_callback_event128.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_author_sim_action_match_cnt.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_author_sim_action_match_cnt_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_live_ecomm_sim_action_match_cnt.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_global_gsu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_ecomm_gsu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_ecomm_match_category.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_ecomm_gsu_sep.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_author_cluster_gsu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_cid3_gsu.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_remote_cluster_gsu.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_spu_gsu.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_item_a2a_gsu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_item_cluster_gsu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_item_global_topn.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_item_author_cluster_gsu.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_item_remote_cluster_gsu.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_reco_live_item_v1_remote_cluster_gsu.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reid_photo_tag.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reid_user_tag.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_top28_seq_num.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_top28_seq_sparse.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_top28_seq_sparse_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_seq_item_hash_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_inc_cut_new.h"

// add by zhaolei06 0315
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_eds_realtime_action_list_match_n_detail.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_inner_ad_creative_type.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_author_fans_detail.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_campaign_type_only_dsp.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_quality_emb.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_tag_category_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_author_level_id_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_commerce_user_v5.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_htag_new.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_is_connected_live_new_flow_fix.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_realtime_new_extend.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sdpa_user_interact_pid_seq.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ali_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_outer_inner_spu_feature_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_action_spu_list_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ali_feature_float.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ecom_brand_id_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_inc_cut_join.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_like_tags.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_order_poster_rate.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_top_item.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_item.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_external_rank_quota.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_rank_quota.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_external_prerank_quota.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sdpa_ecom_user_interact_cate_seq.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_search_bert.h"  // NOLINT

// live feature by zhaolei06
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_value_template.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_list_value_template.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_list_value_template_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_list_value_seq_template.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_embedding_template.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_high_light.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_price.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_volume.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_value_template_fix.h"

// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_goods_tp.h
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_unify_ctr.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_unify_cvr.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_mmu_author_cluster_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_sdpa_photo_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_photo_bid_type_new.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_tag.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_tag_prior.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_gsu_deep_ret.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ecom_photo_spu_emb.h
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_live.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_gsu_live_realtime.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_gsu_live_top.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_gsu_photo_realtime.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_live_long.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_item_long.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_gsu_photo_realtime_v2.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_gsu_live_realtime_v2.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_item_v3.h"
// // NOLINT
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_top_item_v3.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_attr_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_id_list_size.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_id_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_field_id_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_commerce_user_buy180d_Item_cat3_sequence.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_id_slot.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ecom_lps_cate_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_ecom_order_type.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_longterm_action_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_click_rewarded.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_mmu_item_click_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_mmu_p5s_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_and_ad_info_play5s_more_combine_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_match_cnt_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_cross.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_backflow.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_action_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_dsp_last_gap_live_played.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_fans_top_last_gap_live_played.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_time_stamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_stat_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_play_lt1s.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_play_lt1s_last_gap.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_play_lt1s_stat_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_play_lt1s_time_stamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_play_lt1s_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_play_lt1s_match.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_sp_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_action_sp_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_invoke_product_name_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_mmu_ped_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_ecom_short_with_time.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_direct_ecom_click2_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_direct_ecom_lps_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_colossus_filtered_by_playtime.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_sdpa_product_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_action_with_action_and_id_given_len.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_common_author_brand.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_common_author_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_common_author_industry_level.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_common_enhance_author_fusion.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_item_v3_tp.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_target_hash.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_id_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_action_seq.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_action_ts_seq.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_action_len.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_queue_type.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_list.h"

// add reco & ad resume submit action list
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_extend_action_list_match_stat_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_extend_action_list_stat_dense.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_item_v2_tp.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ecom_brand_id_feature_new.h"
// need fix #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_longterm_item_v2.h"
// // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_commerce_user_v5_time_sort.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_for_photo.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_for_photo_v1.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_photo_spu_swing.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_photo_spu_swing_new.h"


#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_cmt_hetu_tag.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_commerce_user_buy270d_time_sort_Item_cat3_sequence.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_commerce_user_click180d_time_sort_Item_cat3_sequence.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_dense_tag.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_ad_device_info_context_dup_photo.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_context_info_dup_photo.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_pageid_dup_photo.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_datetime_info_dup_photo.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_industry_item_click_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_photo_id_item_click_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_user_interactive_form_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_detail_info.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_product_name_dense_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_detail_info_ad_info_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_ad_device_info_ad_info_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_detail_info_context_ad_info_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_location_ad_info_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_invoke_product_name_each_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_invoke_product_name_each_dense_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_invoke_scene_flag.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_plc_biz_type.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_seq_app_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_seq_photo_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_seq_product_id_hash.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_seq_second_industry_id_hash.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_dense_fans_valued_label.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_valued_followed_author.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_info_fans_range.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rewarded_is_show_order_paied_inspire.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_next_stay_day_state.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_next_stay_minute_of_day.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_next_stay_bn_state_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_edg_graph_emb.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_product_name_id_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_page_id_sparse.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_cate.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_merchant_buy_category_seq.h"
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_univ_rank_unify_cxr.h"
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_uplift_feature.h"
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_creative_uplift_float_feature.h"
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_univ_rank_cxr_fix.h"
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_univ_combine_rank_cxr.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_explore_stat.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_xdt_mmu_embedding.h"  // NOLINT

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_latest_live_time_interval.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_pay_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_slp_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_realtime_action_match_cnt_live.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_commerce_action_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_is_follow.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_is_follow_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_live_colossus_filtered_by_play_1m.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_top_action_list_sim_inc_cut_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_top_action_list_sim_inc_cut_v4.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_inc_cut_nio.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_lps.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_no_filter.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_window_sample.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_ad_action_list_sim_no_filter_meta.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_price_combine.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_adsim_photo_info.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_second_industry_dense_268.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_second_industry_dense_268.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_top_action_list_sim_inc_cut_v5.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sdpa_cross_product_name_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sdpa_cross_product_name_dense_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_industry_merge_recall_context_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sdpa_ecom_product_kwds.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_sdpa_ecom_user_action_kwds.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_action_picasso.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_apm_goods_action_seq_picasso.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_apm_goods_action_sample_info.h"
// #include
// "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_action_spu_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_price_4_label.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_creative_type_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_special_item.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_realtime_yellowtrolley_item.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_action_spu_list.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_p_author_univ.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_p_author_avg_explore_ratio.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_author_media_app_avg_explore_ratio.h"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_author_medium_industry_avg_explore_ratio.h"    // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_p_medi_style_univ.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_spu_attr.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_spu_action_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_cnt.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mixup_dense_callback_event_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mixup_dense_callback_event.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mixup_dense_ocpc_type.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_photo_field_id_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_photo_field_id_list.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_click_match_num_long_merge.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_uni_user_merchant_flag.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_4_label.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_recruit_live_jobs_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_is_recruit_live.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_fanstop_recruit_live_author_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_search_field_id_list_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_goods_feature_int_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_goods_feature_int.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_goods_feature_float_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_goods_gsu_feature.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_delivery.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_item_impression.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_item_click.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_mmu_cates.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_sdpa_novel_info.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_tag_list_order_paid_combine.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_akg_cluster.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_new_neg.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_picasso.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_akg_indus_user_ad_interest_seq.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_rank_cpm_info_v3.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_topk_industry_sequence_matched.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_author_fans.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_info_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_spu_v3_emb.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_goods_dense_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_product_dense_spuid.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_recall_product_name_embedding_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_dense_industry.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_author_tag_category_new_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_sim_action_match_cnt.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_goods_gsu_feature_v1.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_ad_product_deep_conv_medium.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_attribute.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_swing.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_medium_uid.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ocpc_action_type.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_follow_sequence.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_hist_action_emb.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dsplive_realtime_action_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_style_server_lp_sequence_share_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_short_play_stat.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_subtitle_segment.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_thanos_play_rate_stat.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action_tag_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_item_dynamic_style_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_graphsim.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_goods_gsu_feature_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate3_id_longterm.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_live_click_cate1_id_longterm.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_type.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_enhance_author_fusion.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_negative_author_id.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_delay_conversion_time.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_delay_delivery_time.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_delay_label.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_delay_p90_time.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_delay_pctr.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_delay_pcvr.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_category_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_datetime_natural_day_event_pay_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_datetime_hour_till_natural_day_share.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_datetime_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_u_realtime_action_list_add.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_match_add.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_realtime_action_match_add_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_outside_mmu_ids.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_user_extend_info.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_app_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_goods_gsu_feature_topk_target.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_realtime_seq_lp_no_dup.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_realtime_seq_no_dup_share_opt.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_account_id_dense.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_action_type_dense_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_new_industry_v3_dense_outside.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_rewarded_pageid_new.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_product_name_dense.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_lsp_segment_info.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_live_newlands_tag_idx_emb_feat.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mmu_live_newlands_sparse_feat.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rank_quota_with_top_cpm_outer.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_phd_play_ids.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_actions_prefix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_goods_gsu_picasso_count.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_item_price.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_item_keyword.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_merchant_user_interact_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_histaction_firstidv3_seq.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_histaction_pdct_seq.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_histaction_secondidv3_seq.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_2_user_up_emb.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_goods_attr_list_fix.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_rtb_flag.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_traffic_boost_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_conv_scene_flag.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_newlands_click_top_emb.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_newlands_click_tail_emb.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_invoke_product_name_each_dense_v3.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_landingpage_realtime_action_list.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_impression_realtime_new_extend_ict3.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_follow_ocpc_action_type_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_follow_ocpc_action_type_dense_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_search_query_statistical.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_search_query_statistical_prefix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_search_query_ids_data.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_mgi_e2e_photo_emb.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_follow_ocpc_action_type_dense_four.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_dsp_ecom_count_fea.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_item_eshop_embedding_spu.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_outer_inner_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_outer_inner_feature_float.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_item_info_4_label.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_info_4_label.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_shop_item_detail_info_v1.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_reco_embedding_feed_uid.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_reco_embedding_single_uid.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_reco_embedding_feed_aid.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_live_reco_embedding_single_aid.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_reward_page_id_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_view_component_type.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_app_active_info.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_rank_ctcvr_scale_fix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_merchant_price.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_dense_merchant_price_v2.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_play_time.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_realtime_action1_no_action2.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_act_time.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_indusid.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_photo_id.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ad_lps_extend_product_id.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ocpc_itemclick_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_ks_flow_data_dense.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_akg_hop5_list.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_universe_rtb_bid.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/i18n_impl/bs_extract_test.h"  // NOLINT
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_product_name_coldstart.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_sdpa_photo_cnt_feature.h"
