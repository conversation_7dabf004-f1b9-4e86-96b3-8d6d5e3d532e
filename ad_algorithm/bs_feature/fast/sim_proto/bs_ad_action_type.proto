// copy from teams/ad/ad_proto/kuaishou/ad/common_ad_log.proto
syntax = "proto3";

package bs.kuaishou.ad;

enum AdActionType {
  UNKNOWN_ACTION_TYPE = 0;

  // ad item
  AD_ITEM_IMPRESSION = 1; // 打开播放页就上报,区分上下滑点击、进入方式放在client_params,itemShowType，直播预约投快享卡片参数放入了client_params
  AD_ITEM_CLICK = 2; // 点击转化链接，四个地方：蓝条，重播转化按钮，caption“查看详情”，电商caption首位链接,client_params中增加itemClickType，直播预约投快享卡片参数放入了client_params
  AD_ITEM_CLOSE = 3; // 返回、上滑、下滑->关闭，仅在这三种情况下上报，需要上报各种播放时长（分享回来、切home键、进profile返回->resume),关闭方式放在client_params,itemCloseType
  AD_ITEM_NEGATIVE = 4; // 播放页减少此类作品
  AD_ITEM_BOTTOM_ICON_IMPRESSION = 5; // 详情页右下角转化链接icon曝光
  AD_ITEM_IOS_NEW_CLICK = 6; // iOS新详情页item_click上报,仅限iOS新详情页
  AD_ITEM_MEDIA_BAR_IMPRESSION = 7; // 全屏视频下,转化入口弹现时上报,需要上报弹窗类型MediaBarType
  AD_ITEM_MEDIA_BAR_CLOSE = 8; // 全屏视频下,转化入口关闭时上报,需要上报弹窗类型MediaBarType
  AD_ITEM_COMMENTS_BAR_IMPRESSION = 9; // 评论区首屏转化条曝光包括android&ios
  AD_ITEM_DOWNLOAD_STARTED_AUTO = 610; //触发了自动下载功能，开始下载时上报
  AD_ITEM_DOWNLOAD_COMPLETED_AUTO = 611; //自动下载完成时上报
  AD_ITEM_DOWNLOAD_DELETED_AUTO = 612; //触发了预下载功能，删除下载内容时上报

  // ad photo
  AD_PHOTO_IMPRESSION = 10; // 展示，瀑布流
  AD_PHOTO_CLICK = 11; // 点击，瀑布流
  AD_PHOTO_LIKE = 12; // 点赞，红心+双击
  AD_PHOTO_COMMENT = 13; // 转发，不区分原始评论/回复别人
  AD_PHOTO_FOLLOW = 14; // 关注，详情页点关注
  AD_PHOTO_SHARE = 15; // 转发，需要上报转发的目标渠道,client_params带上转发渠道
  AD_PHOTO_CANCEL_LIKE = 16; // 取消点赞
  AD_PHOTO_REPORT = 17; // 举报
  AD_PHOTO_NEGATIVE = 18; // 瀑布流减少此类作品
  AD_PHOTO_UNFOLLOW = 19; // 取消关注
  AD_PHOTO_BLOCK = 20; // 加入黑名单
  AD_PHOTO_PLAYED_3S = 21; // 已播放 3 秒（第一遍）
  AD_PHOTO_PLAYED_5S = 22; // 已播放 5 秒（第一遍）
  AD_PHOTO_PLAYED_END = 23; // 已播放完成（第一遍）
  AD_PHOTO_REPLAYED = 24; // 视频重播
  AD_PHOTO_HATE = 25; // 点踩
  AD_PHOTO_CANCEL_HATE = 26; // 取消点踩
  AD_HIDE_PHOTO_INFO = 27; // 点击视频播放页面，隐藏视频信息时上报
  AD_PHOTO_LEAVE = 28; // 看完视频返回发现页信息流
  AD_PHOTO_50_IMPRESSION = 29; // 展示超过50%时上报

  // Android downloader
  AD_ITEM_DOWNLOAD_STARTED = 30; // 开始下载
  AD_ITEM_DOWNLOAD_COMPLETED = 31; // 下载完成
  AD_ITEM_DOWNLOAD_INSTALLED = 32; // 安装完成，同时上报参数IsPackageChanged
  AD_ITEM_DOWNLOAD_PAUSED = 33; // 暂停，同步上报下载进度
  AD_ITEM_DOWNLOAD_RESUMED = 34; // 继续
  AD_ITEM_DOWNLOAD_DELETED = 35; // 删除，同步上报下载进度
  AD_ITEM_DOWNLOAD_LOW_STORAGE = 36; // 内存不足，同步上报下载进度
  AD_ITEM_DOWNLOAD_INSTALL_STARTED = 37; // 点击"立即安装"
  AD_ITEM_DOWNLOAD_OPENED = 38; // 点击"立即打开"
  AD_STORAGE_PERMISSION_REFUSED = 39; // 拒绝授权存储权限
  AD_ITEM_DOWNLOAD_FAILED = 40; // 下载失败，同时上报失败原因download_failed_reason
  AD_ITEM_DOWNLOAD_FORCE_OPENED = 41; // 安卓下载类广告安装后强制打开app

  // Android install
  AD_ITEM_INSTALL_NOTICE_CONFIRMED = 45; //应用下载未安装提示,确认
  AD_ITEM_INSTALL_NOTICE_CANCELLED = 46; //应用下载未安装提示,取消
  AD_ITEM_CONVERSION_NOTICE_CONFIRMED = 47; //应用下载未激活提示,确认
  AD_ITEM_CONVERSION_NOTICE_CANCELLED = 48; //应用下载未激活提示,取消

  // ad landing page
  AD_LANDING_PAGE_ENTERED = 50; // 进入广告H5页面时上报,添加来源入口参数ClientParams.LandingPageEntrySource,页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_LOADED = 51; // 请求第一个元素返回时,添加来源入口参数ClientParams.LandingPageEntrySource,页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_CLOSED = 52; // 页面退出：右滑和关闭,添加来源入口参数ClientParams.LandingPageEntrySource,页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_FORM_SUBMITTED = 53; // 落地页面点击“提交”时上报,添加来源入口参数ClientParams.LandingPageEntrySource,页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_POP = 54; // 落地页弹出,添加页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_UP_SLIDE = 55; // 落地页被上滑（打开),添加页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_DOWN_SLIDE = 56; // 落地页被下滑（关闭),添加页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_CLICK = 57; // 落地页面内有效点击行为,添加页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_RETURNED = 58; // 落地页返回按钮点击成功即上报
  AD_LANDING_PAGE_ENTER_FAILED = 59 ; // 前置落地页加载失败&落地页加载失败

  //living action
  AD_LIVE_IMPRESSION = 60; //直播展现
  AD_LIVE_CLICK = 61; //直播点击
  AD_LIVE_PLAYED_3S = 62; //直播播放3秒
  AD_LIVE_PLAYED_1M = 63; //直播播放一分钟
  AD_LIVE_PLAYED_MEDIAN = 64; //直播播放时间到达统计中位数时间(当前14s)
  AD_LIVE_PLAYED_MEAN = 65; //直播播放时间到达统计平均数时间(当前200s)
  AD_LIVE_PLAYED_END = 66; //直播播放完毕 // 20200609 同时上报played_seconds
  AD_LIVE_LEAVE = 67; //离开直播间
  AD_LIVE_PLAYED_STARTED = 68; //进入直播间开始直播
  AD_LIVE_PLAYED_SECONDS = 69; //直播播放x秒事件，配合played_seconds参数使用

  //ad report 广告举报
  AD_PHOTO_REPORT_SUBMIT = 70; //提交广告举报信息，点击广告举报提交按钮,client_params带上举报类型
  AD_PHOTO_50_IMPRESSION_1S = 71; // 封面50%曝光超过1s

  // 信息流电商的涨粉行为
  AD_MERCHANT_FOLLOW = 72; // 信息流电商曝光一定时间后的关注为涨粉

  //开屏广告 85 ~ 99
  AD_SPLASH_IMPRESSION = 85;  // 开屏广告曝光
  AD_SPLASH_CLICK = 86;       // 开屏广告点击（包括开屏action_bar的点击，行为与开屏点击一致）
  AD_SPLASH_FEED_PLAY_START = 87;   // 开屏缩短到信息流进行动画播放成功
  AD_SPLASH_FAIL = 88; // 开屏曝光失败
  AD_SPLASH_PRELOAD_START = 89; // 开屏素材开始预加载
  AD_SPLASH_PRELOAD_SUCCESS = 90; // 开屏素材预加载成功
  AD_SPLASH_PRELOAD_FAIL = 91; // 开屏素材预加载失败


  //电商事件
  AD_MERCHANT_CAPTION_URL_CLICK = 100; //电商正文caption url及第三方链接点击上报，通过put_type及id来确认点击具体内容
  AD_MERCHANT_YELLOW_SHOPPING_TROLLEY_IMPRESSION = 101; //展示电商作品黄色购物车,补充参数ext_data.default_type标识默认态
  AD_MERCHANT_YELLOW_SHOPPING_TROLLEY_OPEN = 102; //打开电商作品黄色购物车,补充参数ext_data.default_type标识默认态
  AD_MERCHANT_YELLOW_SHOPPING_TROLLEY_CLOSE = 103; //关闭电商作品黄色购物车,补充参数ext_data.default_type标识默认态
  AD_MERCHANT_PHOTO_ITEM_FLOAT_WINDOW_IMPRESSION = 104; //展示电商作品商品悬浮卡片,补充参数ext_data.default_type标识默认态,ext_data.expand_type标识展开方式
  AD_MERCHANT_PHOTO_ITEM_FLOAT_WINDOW_CLICK = 105; //点击电商作品商品卡片,补充参数ext_data.default_type标识默认态
  AD_COURSE_FREE_CONTENT_IMPRESSION = 106; //课堂免费内容卡片曝光
  AD_COURSE_FREE_CONTENT_CLICK = 107; // 课堂免费内容卡片点击
  AD_MERCHANT_PHOTO_ITEM_FLOAT_WINDOW_CLOSE = 108; //点击电商作品商品卡片关闭按钮,补充参数ext_data.default_type标识默认态
  AD_MERCHANT_THANOS_PHOTO_ITEM_IMPRESSION = 109; //滑滑版短视频商品曝光
  AD_MERCHANT_THANOS_PHOTO_ITEM_CLICK = 110; //滑滑板短视频商品点击
  AD_MERCHANT_YELLOW_SHOPPING_TROLLEY_CLICK = 111; //弱样式小黄车点击
  AD_MERCHANT_ORDER_PAY_RESULT_PAGE_IMPRESSION = 112; //订单支付成功页面曝光
  AD_MERCHANT_THANOS_PHOTO_ITEM_V1_IMPRESSION = 113; //滑滑板短视频商品曝光,小黄车图标空心样式
  AD_MERCHANT_THANOS_PHOTO_ITEM_V1_CLICK = 114; //滑滑板短视频商品点击,小黄车图标空心样式

  // Profile页相关
  AD_PHOTO_TO_PROFILE = 120; //从详情页进入Profile页:左滑，非直播状态点击头像，点击视频简单作者名称，点击评论区作者回复评论名称
  AD_PHOTO_TO_PROFILE_FOLLOW = 121; //从详情页进入Profile页，点击关注
  AD_PHOTO_TO_PROFILE_UNFOLLOW_CLICK = 122; //从详情页进入Profile页，点击取消关注
  AD_PHOTO_TO_PROFILE_UNFOLLOW_CONFIRM = 123; //从详情页进入Profile页，确定取消关注
  AD_PHOTO_TO_LIVE = 124; // 从详情页进入直播页:直播状态点击头像
  AD_PHOTO_TO_PROFILE_TO_LIVE = 125; // 从粉条作品详情页进入profile页再进直播


  // 广告素材相关行为 140 ~ 159
  AD_ELEMENT_IMPRESSION = 140;    // 标识广告元素的曝光，配合参数ElementType 标识具体的广告元素
  AD_ELEMENT_CLICK = 141;         // 标识广告元素的点击，配合参数ElementType 标识具体的广告元素
  AD_PENDANT_CLICK = 142;         // 红包挂件点击

  AD_OPEN_DOWNLOAD_CENTER_ITEM = 150; //进入下载中心点击相关app(or h5)

  // 详情页相关事件
  AD_DETAIL_PAGE_CLOSED = 160; //退出详情页，返回、上滑、下滑->关闭，仅在这三种情况下上报，需要上报各种播放时长

  AD_EXPAND_COMMENT_DIALOG = 170; // 评论弹窗展开：1：上下滑状态详情页底部评论按钮点击，2：上下滑部分评论状态下详情页底部评论按钮点击，3：上下滑部分评论状态下展开更多评论按钮点击,
  // 上报时client_params带上弹窗展开方式
  AD_CONVERSION = 180; //激活
  AD_PURCHASE = 190; //付费
  AD_ROAS = 191; // Return on Ad Spend 目前没有数据
  AD_MERCHANT_ROAS = 192; // 小店电商 ROAS 出价产品

  AD_PHOTO_INTRO = 200; // 快手广告共享计划介绍

  AD_NEW_DETAIL_PAGE_LOADED = 210; //  新详情页加载完成、WebView内页面加载完成

  // 负反馈相关事件
  // 还有已经定义的AD_PHOTO_NEGATIVE AD_ITEM_NEGATIVE
  AD_PHOTO_NEGATIVE_IMPRESSION = 220;
  AD_ITEM_NEGATIVE_IMPRESSION = 221;
  AD_ITEM_DISLIKE_IMPRESSION = 222;
  AD_ITEM_SHIELD_IMPRESSION = 223;
  AD_ITEM_QUALITY_PANEL_IMPRESSION = 224;
  AD_ITEM_QUALITY_PANEL_CLICK = 225;

  // 通用卡片事件
  AD_ACTIONBAR_COLOR_CHANGED = 240;
  AD_CARD_IMPRESSION = 241;
  AD_CARD_IMPRESSION_FAILED = 242;
  AD_CARD_CLOSE = 243;

  //live action 添加
  AD_SIMPLE_LIVE_CLICK = 280; // 直播点击,点击简版直播间进入正式直播间时上报
  AD_LIVE_SHOP_IMPRESSION = 281; // 直播购物车曝光
  AD_LIVE_ITEM_IMPRESSION = 282; // 直播商品列表曝光,同时上报AdLiveItemImpressionType
  AD_LIVE_JUMP_CLICK = 283; // 点击简版直播间进入正式直播间时上报
  AD_LIVE_TO_PROFILE = 284; // （极速版+主APP设置版）下直播直投：当滑到直播流且为结束态时，显示5s倒计时，期间点击进入Profile页,同时上报AD_LIVE_TO_PROFILE_TYPE
  AD_LIVE_SKU_ITEM_IMPRESSION = 285; //半屏sku页商品曝光

  AD_LIVE_FOLLOW = 300; //直播关注
  AD_LIVE_LIKE = 301; //直播点赞
  AD_LIVE_REWARD = 302; //直播打赏
  AD_LIVE_SHARE = 303; //直播分享
  AD_LIVE_CANCEL_FOLLOW = 304; //直播取消关注
  AD_LIVE_REPORT = 305; //直播举报
  AD_LIVE_HATE = 306; //直播hate上报
  AD_LIVE_SHOP_CLICK = 307; //直播购物车点击 // 20200606 同时上报AdLiveShopClickType
  AD_LIVE_SHOP_LINK_JUMP = 308; //直播购物车链接跳转 // 20200606 同时上报AD_LIVE_SHOP_LINK_JUMP_TYPE&merchant_item_id(商品ID)
  AD_LIVE_LANDING_PAGE_LOAD_FAILED = 309; //直播落地页加载失败

  AD_NEW_DETAIL_PAGE_CLICK = 310; // 视频播放页改造:WebView页面内有效点击行为
  AD_NEW_DETAIL_PAGE_ENTERED = 311; // WebView内页面开始加载
  AD_NEW_DETAIL_PAGE_TAB_CLICK = 312; // 详情页tab点击，具体点击位置通过ClientParams.NewDetailPageTabClickType上报
  AD_NEW_DETAIL_PAGE_GUIDE_IMPRESSION = 313; // 新详情页引导按钮曝光
  AD_NEW_DETAIL_PAGE_GUIDE_CLICK = 314; // 新详情页引导按钮点击

  // APP Deeplink 事件 320 ~ 329
  AD_DEEPLINK_EVOCATION_SUCCESS = 320; // Deeplink唤起成功
  AD_DEEPLINK_EVOCATION_FAIL = 321; // Deeplink唤起失败
  AD_DEEPLINK_REBOUND_SUCCESS = 322; // Deeplink回跳成功
  AD_APPSTORE_DEEPLINK_EVOCATION_SUCCESS = 323; // 厂商应用商店详情页deeplink唤起成功

  EVENT_APP_INVOKED = 324;  // app唤端

  AD_KUAIXIANG_NEGATIVE = 330; // 快享计划主动关闭广告

  AD_POI_LABEL_IMPRESSION = 340; // POI字段曝光
  AD_POI_LABEL_CLICK = 341; // POI字段点击
  AD_POI_DETAIL_PAGE_IMPRESSION = 342; // POI详情页曝光
  AD_POI_DETAIL_PAGE_DIAL_CLICK = 343; // POI详情页-电话拨打点击数据
  AD_POI_DETAIL_PAGE_ITEM_CLICK = 344; // POI详情页-action按钮点击

  EVENT_NEXTDAY_STAY = 346; //次日留存（自然日）
  EVENT_WEEK_STAY = 347; //7日留存
  EVENT_VALID_CLUES = 348; //有效线索
  EVENT_PAY = 349; //付费

  AD_IMPRESSION_MODULE = 380; // 快享计划广告按钮转化条曝光

  AD_ITEM_MEDIA_BAR_CLICK = 381; // 广告信息搜集卡片转化点击
  AD_ITEM_MEDIA_BAR_DETAIL = 382; // 广告信息搜集卡片了解详情点击

  AD_CREDIT_GRANT = 383; // 广告用户授信
  EVENT_JINJIAN = 384; // 金融广告完件

  AD_DEEPLINK_EVOCATION_WEB_RECEIVE = 385; //跳转信息接收监听
  AD_DEEPLINK_EVOCATION_WEB_SUCCESS = 386; //跳转成功

  AD_PHOTO_IMPRESSION_END = 387; //信息流作品曝光结束
  AD_BRAND_TOP_IMPRESSION = 388; //该下拉广告素材曝光结束触发，动作以素材为准

  EVENT_ACTIVE = 389; //打开APP、被认为一次活跃的事件
  EVENT_HIGH_QUALITY = 390;//优质用户，优化模型使用
  EVENT_FORM_SUBMIT = 391; //表单提交
  EVENT_GOODS_VIEW = 392; //电商的商品浏览行为
  EVENT_ADD_SHOPPINGCART = 393; //电商的添加购物车
  EVENT_ORDER_SUBMIT = 394; //电商的提交订单成功
  EVENT_ORDER_PAIED = 395; //订单付款成功
  EVENT_REGISTER = 396; // 注册
  AD_TEST_EVENT = 397; // 主要用于新增临时样式的特殊行为（如点击浮窗视频放大为后贴片全屏广告）

  AD_DELIVERY = 398; // 广告从 athena 吐出事件
  AD_ITEM_PLAY_START = 399;  // 广告开始播放 用于中台
  AD_ITEM_PLAY_END = 400;  // 广告播放完成 用于中台
  AD_ITEM_CLICK_BACK = 401; // 广告视频详情页二跳点击并返回
  AD_PHOTO_PLAYED_NS = 402; // 广告播放play N秒
  AD_APPROXIMATE_PURCHASE = 403; // 近似购买
  // 20200420 新加
  EVENT_BUTTON_CLICK = 404; // 按钮点击，后续会作为一个ocpc_action_type
  AD_PHOTO_PLAYED_2S = 405; // 广告播放play 2秒

  //直营电商事件
  AD_ECOM_SHOPPING_CARD_IMPRESSION = 430; //直营电商卡片视频区曝光
  AD_ECOM_SHOPPING_CARD_COMMENT_AREA_IMPRESSION = 431; //直营电商卡片评论区曝光
  AD_ECOM_SHOPPING_TROLLEY_IMPRESSION = 432; //直营电商小购物车曝光
  AD_ECOM_SHOPPING_TROLLEY_CLICK = 433; //直营电商小购物车点击

  // 极速版广告播放页&播放结束页埋点需求
  AD_DOWNLOAD_BOX_IMPRESSION = 434; // 仅使用于android端下载类场景，点击相关按钮后触发曝光立即下载悬浮框
  AD_DOWNLOAD_BOX_CANCELLED = 435; // 仅使用于android端下载类场景，立即下载悬浮框关闭，需要上报BoxCancelledType

  AD_DOWNLOAD_MANAGEMENT_IMPRESSION = 440; //Android&iOS,点击设置页后，进入下载管理器页面曝光；同时上报任务个数参数download_amount；同时上报展开按钮是否出现参数is_download_extend
  AD_DOWNLOAD_DELETE_POPUP_IMPRESSION = 445; //Android&iOS,从下载管理器页面，删除弹窗页面曝光

  // 广告弹窗
  AD_CONFIRM_CLOSED_CARD_IMPRESSION = 450; // 确认退出广告卡片曝光
  AD_CONTINUE_VIEW_CLICK = 451; // 用户点击继续观看按钮触发上报

  // AD ITEM 无位置，后续接这里
  AD_ITEM_SKIPED = 501; // 用户成功点击跳过按钮
  AD_ITEM_IMPRESSION_1FRAME = 502; // 第一帧曝光

  //POI打点相关
  AD_POI_COMMENT_AREA_IMPRESSION = 520; //POI评论区位置曝光

  //快接单
  AD_SOCIAL_NATIVE_IMPRESSION = 530; //快接单弱样式卡片曝光

  AD_ITEM_PLAY_END_CARD_IMPRESSION = 531; //当视频播放结束，且通用卡片元素渲染完毕后上报一条播放结束页卡片曝光；两种样式播放结束语均统一上报

  //快享相关
  AD_KUAIXIANG_PREVIOUS_IMPRESSION = 532; //快享极速版-前链曝光，作品播放Ns后出现转化条记一次曝光
  AD_KUAIXIANG_PREVIOUS_CLICK = 533; //快享极速版-前链点击，点击转化条记一次点击

  //直播预约投快享相关
  AD_LIVE_RESERVATION_HALF_IMPRESSION = 534; //直播预约半层曝光
  AD_LIVE_RESERVATION_HALF_BUTTON_CLICK = 535; //直播预约半层按钮点击
  AD_LIVE_RESERVATION_HALF_HEAD_CLICK = 536;  //直播预约半层头像点击
  AD_LIVE_RESERVATION_SUCCESS = 537; //直播预约成功
  AD_LIVE_RESERVATION_FAIL = 538;  //直播预约失败
  AD_LIVE_RESERVATION_PUSH_IMPRESSION = 539; //直播预约push权限弹窗曝光，弹窗参数放入了ClientParams.live_reservation_push
  AD_LIVE_RESERVATION_PUSH_CLICK = 540; //直播预约push权限弹窗点击,弹窗参数放入了ClientParams.live_reservation_push
  AD_LIVE_RESERVATION_CANCEL = 541; //直播预约取消

  //快直播打点
  AD_SOCIAL_LIVE_CONVERSION_ENTRANCE_CLICK = 550; //快直播转化入口点击
  AD_SOCIAL_LIVE_CONVERSION_HINT_IMPRESSION = 551; //快直播转化提示曝光
  AD_SOCIAL_LIVE_CONVERSION_FORM_IMPRESSION = 552; //快直播转化表格曝光
  AD_SOCIAL_LIVE_CONVERSION_FORM_CLICK = 553; //快直播转化表格输入，输入任意一项就上报，只上报一次
  AD_SOCIAL_LIVE_CONVERSION_FORM_SUBMIT = 554; //快直播转化表格提交，预约成功时上报
  AD_SOCIAL_LIVE_CONVERSION_ENTRANCE_IMPRESSION = 555; //快直播转化任务入口曝光

  //粉条事件
  AD_FANS_TOP_SERVER_FOLLOW = 570; //粉条服务端的关注事件，客户端不要使用
  AD_LIVE_SERVER_FOLLOW = 571; // 直播的关注事件，客户端不要使用
  AD_FANS_TOP_FOLLOW = 572; // 粉条服务端的关注事件包含作品和直播，客户端不要使用


  //联盟相关
  AD_WIN_NOTICE_UNION = 600; // 联盟竞价成功后，上报actionType
  AD_SPREAD_CACHE_QUERY = 601; // 开屏缓存查询事件
  EVENT_PAY_UNION = 602; // 联盟ROI变现使用

  //金牛相关620~629
  AD_DELIVERY_NO_CHARGE = 620; //金牛聚合落地页,引流广告不计费

  // 浮窗页相关
  AD_PLAYABLE_PAGE_IMPRESSION = 630;  // 浮窗曝光，进入浮窗页面时上报
  AD_PLAYABLE_PAGE_INCLICK = 631;     // 浮窗内点击
  AD_PLAYABLE_PAGE_OUTCLICK = 632;    // 浮窗外点击
  AD_PLAYABLE_PAGE_CLOSED = 633;      // 浮窗关闭

  // 预约表单
  EVENT_APPOINT_FORM = 634; // 预约表单
  EVENT_APPOINT_JUMP_CLICK = 635; // 预约跳转点击

  // 浮窗页20200927新加
  AD_PLAYABLE_PAGE_PLAYED_END = 636; // 浮窗，惊喜视频播放完成

  //分析型字段
  AD_ABANDON = 650; // 广告丢弃,同时上报丢弃场景DiscardScene
  AD_APPSTORE_IOS_IMPRESSION = 651; // IOS APPSTORE应用商店详情页展现

  AD_APPSTORE_DEEPLINK_EVOCATION_FAILED = 652 ; // APPStore deeplink唤起失败
  AD_CONFIRM_DOWNLOAD_BOX_IMPRESSION = 653 ; // 确认下载框曝光
  AD_CONFIRM_DOWNLOAD_BOX_CANCELLED = 654 ; // 确认下载框取消
  AD_VPN_STARTED = 655 ; // 反厂商拦截VPN开启
  AD_SPLASH_ANDROID_RETURN_CLICK = 656 ; // 开屏5s阶段，安卓返回键点击

  AD_GUIDE_PAGE_IMPRESSION = 700 ; //引导场景页面的曝光

  AD_RERANK_STOCK_NOT_ENOUGH = 710; //用户命中频次ad_load or 时长ad_load策略需要展现广告,但服务端并未下发广告时上报，同时上报参数client_params.exposure_reason
  AD_LANDING_PAGE_IMPRESSION = 711; // 前置落地页展现X%时即上报，目前X=90

  AD_LOAD_PHOTO_FAILED = 712; //国庆节活动激励广告视频,接收广告下发后上报
  AD_ITEM_DOWNLOAD_CARD_CLOSED = 713; // 下载卡片关闭
  AD_GOLD_BUTTON_CLICK = 714; // 点击弹窗中金币icon，弹框消失，视频继续播放

  EVENT_ADD_WECHAT = 715;// 微信复制
  EVENT_MULTI_CONVERSION = 716; // opcx优化目标：多转化事件
  EVENT_AD_WATCH_TIMES = 717; //ocpx优化目标：广告观看次数

  AD_SOCIAL_LIVE_CONVERSION_CLOSED = 718; // 快直播转化任务关闭
  AD_SOCIAL_LIVE_ELEMENT_CLICK = 719; // 快直播转化任务中元素点击

  AD_IS_INNER_DELIVERY = 720;   // 粉条内部订单

  AD_TRY_PLAY_GAME_PRELOAD = 721; //试玩页面开始加载
  AD_TRY_PLAY_GAME_LOADED = 722; // 试玩页面加载完成
  AD_TRY_PLAY_GAME_START = 723; //开始试玩
  AD_TRY_PLAY_GAME_END = 724; //完成试玩

  AD_DETAIL_PAGE_IMPRESSION = 725; // 详情页曝光
  AD_APPSTORE_IOS_CLOSED = 726; // AppStore关闭
  AD_ITEM_NEGATIVE_CANCEL = 727; // 负反馈，取消按钮点击

  AD_LIVE_COMMENT = 728; //直播粉条-直播间评论事件action，用户点击发送评论时触发上报
  AD_ACTIONBAR_IMPRESSION = 729;  // actionbar的曝光事件
  AD_DATA_VERIFICATION_SUCCESS = 730; // 激励视频数据校验成功

  EVENT_AD_WATCH_5_TIMES = 731; // 广告观看，累计到5次
  EVENT_AD_WATCH_10_TIMES = 732; // 广告观看，累计到10次
  EVENT_AD_WATCH_20_TIMES = 733; // 广告观看，累计到20次

  AD_FANSTOP_PHOTO_SHOW = 734; // 粉条作品曝光
  AD_FANSTOP_MERCHANT_SHOW = 735; //粉条商品曝光

  AD_DATA_RECEIVED_SUCCESS = 736; //客户端成功接收引擎端下发的广告时上报

  EVENT_WATCH_APP_AD = 737; //广告观看事件，当前仅在dsp平台的追踪工具联调目标中使用

  AD_BANNERCARD_IMPRESSION = 738; // 激励视频bannercard曝光

  EVENT_7_DAY_PAY_TIMES = 739; // 七日付费次数

  AD_LIVE_AUDIENCE = 740;  // 新增直播间进人 ocpc

  AD_PROFILE_TO_LIVE = 741; // 点击头像跳转个人主页再跳转正式直播间 https://docs.corp.kuaishou.com/d/home/<USER>

  AD_STANDARD_LIVE_PLAYED_STARTED = 742; // 进入标准直播间开始直播, 目前非客户端上报，仅作为模型数据流使用（使用AD_LIVE_PLAYED_STARTED和log proto中的live_room_pattern判断）
  //正负反馈
  AD_PHOTO_SEE = 743; //点击为何看到此广告按钮
  AD_PHOTO_DISLIKE_IMPRESSION = 744; // 不感兴趣页面曝光

  AD_PHOTO_PLAYED_RATE = 745;//75%进度播放数
  
  AD_PHOTO_REPORT_IMPRESSION = 746;//举报页面曝光
  
  AD_PHOTO_SEE_IMPRESSION = 747;//为何看到此广告页面	曝光

  AD_PHOTO_PUT_IMPRESSION = 748;//我也相投广告页面 曝光

  AD_PHOTO_PUT = 749;//点击想投广告按钮	

  AD_PHOTO_PUT_SUBMIT = 750;//点击0元开通按钮		

  AD_PHOTO_INTEREST = 751;// 广告兴趣管理-仅点击按钮

  AD_LIVE_UNFOLLOW = 752;// 直播取消关注
  AD_LIVE_BLOCK = 753;// 直播加入黑名单

  AD_LANDING_PAGE_SHARE_CLICK = 754; // 落地页分享按钮点击
  AD_LANDING_PAGE_SHARE_SUCCESS = 755; // 落地页分享成功

  AD_REQUEST_SKA_DATA_FAILED = 756; //请求SKA失败

  AD_BUTTON_IMPRESSION = 757; // 图标曝光
  AD_BUTTON_CLICK = 758; // 图标点击
  
  AD_COUNT_DOWN_END = 759; //代表激励任务完成，包括一阶段任务，二阶段任务（激活APP、下单）

  AD_FANS_TOP_PLAY = 760; //粉条播放事件

  EVENT_PHONE_GET_THROUGH = 761; // 营销链路中，电话建联成功，区别于智能电话拨通；eg.招商加盟、家居建材、生活服务、教育
  EVENT_INTENTION_CONFIRMED = 762; // 营销链路中，意向确认；eg.招商加盟、家居建材、生活服务、教育
  EVENT_WECHAT_CONNECTED = 763; // 营销链路中，微信加粉；eg.招商加盟、家居建材、生活服务、教育
  EVENT_ORDER_SUCCESSED = 764; // 营销链路中，成交；eg.招商加盟、家居建材、IT消电
  RESERVATION_BUTTON_CLICK = 765; //预约组建点击
  AD_BUTTON_CLICK_DOWNLOAD = 766; //下载图标点击
  AD_BUTTON_CLICK_CONSULT = 767; //咨询图标点击

  AD_FORCE_ACTIVE = 768; //强制激活
  AD_BUTTON_IMPRESSION_DOWNLOAD = 769; //下载图标曝光
  AD_BUTTON_IMPRESSION_CONSULT = 770; //咨询图标曝光

  AD_JUMP_TO_BROWSER_SUCCESS = 771;//尝试跳转浏览器成功时上报
  AD_JUMP_TO_BROWSER_FAILED = 772;//尝试跳转浏览器失败时上报

  EVENT_KEY_INAPP_ACTION = 773; // 关键行为 优化目标
  AD_SEVEN_DAY_ROAS = 774; // 七日ROI 优化目标

  AD_BUTTON_CLICK_DEEPLINK_INVOKED = 775; //点击半屏模板中的跳转小程序按钮时上报

  AD_HALF_PAGE_BANNER_IMPRESSION = 776; //半屏直播导流条曝光  https://docs.corp.kuaishou.com/d/home/<USER>

  EVENT_PHONE_CARD_ACTIVATE = 777; //IT消电行业，电话卡激活 超深度优化目标
  EVENT_MEASUREMENT_HOUSE = 778; //针对客户三亚菲林、业之峰，新增用户量房事件 超深度优化目标

  // 服务号后链路行为
  CLUE_LIST_PV = 779; // 商品列表页-咨询按钮曝光
  CLUE_LIST_CLICK = 780; // 商品列表页-咨询按钮点击
  GOODS_DETAIL_PV = 781; // 商品详情页-咨询按钮曝光
  GOODS_DETAIL_CLICK = 782; // 商品详情页-咨询按钮点击
  CLUE_PV = 783; // 免费咨询页-提交线索按钮曝光
  CLUE_CLICK = 784; // 免费咨询页-提交线索按钮点击
  CLUE_SUCCESS = 785; // 线索提交成功 (h5埋点)
  LEADS_SUBMIT = 786; // 线索提交成功（后端落库）
  DOWNLOAD_PV = 787; // 应用下载弹窗页曝光
  DOWNLOAD_CLICK = 788; // 应用下载弹窗页-下载按钮点击


  AD_ITEM_CLICK_DOWNLOAD   = 789; //聚星链接点击
  AD_ITEM_CLICK_CLUE       = 790; //咨询点击
  AD_ITEM_CLICK_TROLLEY    = 791; //小黄车点击
  AD_ITEM_CLICK_PROGRAM    = 792; //小程序点击
  AD_ITEM_CLICK_POI        = 793; //位置点击
  AD_ITEM_CLICK_GAME       = 794; //游戏点击
  AD_ITEM_CLICK_LIVE_RESERVATION       = 795; //直播预约点击

  AD_COVER_LIKE = 796; // 封面点赞
  AD_COVER_CANCEL_LIKE = 797; // 封面取消点赞

  //20210618 add
  AD_LIVE_PLAYED_30S = 798; //直播播放30S

  AD_FANS_TOP_LIVE_SHOW = 799;
  AD_FANS_TOP_ROI = 800;

  EVENT_ORDER_PAYED_INDIRECT = 801; //间接链路（点击）带来的成交
  EVENT_ORDER_PAYED_SERVER_FOLLOW = 802; //涨粉链路带来的成交
  AD_TRUE_DEEPLINK_EVOCATION_SUCCESS = 803; //deeplink真实唤起成功

  //20210708新增
  AD_AWARD_SUCCESS = 804; // 成功领取积分/金币
  AD_AWARD_FAIL = 805; //未成功领取积分/金币
  //20210730 add
  AD_LOSE_NOTICE_UNION = 806; //联盟竞价失败
  //20210802新增
  AD_AUDIENCE_FAST = 807;  //粉条CPC极速进人

  AD_WEAK_ACTIONBAR_IMPRESSION = 808; //弱样式actionbar成功曝光时上报

  AD_IMPRESSION_FAIL_UNION = 809; //联盟返回广告但未成功曝光

  //20210825 add
  AD_PURCHASE_CONVERSION = 810; //激活付费优化目标

  // 兼容AdCallbackLog::EventType类型
  EVENT_CONVERSION = 900; // 激活
  EVENT_PRODUCT_BUY_CLICK = 901; // 立即购买按钮点击,品牌模型使用
  EVENT_ORDER_SUBMIT_CLICK = 902; // 订单提交按钮点击，品牌模型使用
  AD_LIVE_PLAYED_SECIOND_TOTAL=903; // (已废弃，迁移到905）用户进入简易/正式直播间到离开一次消费时长

  //20210909 add, 投放平台新增优化目标
  EVENT_AUDITION = 904; //首次试听到课

  AD_LIVE_PLAYED_SECONDS_TOTAL = 905; // 用户进入简易/正式直播间到离开一次消费时长ElementType

  AD_LIVE_EFFECTIVE_PLAY = 906; // ESP 增加转化目标短视频推广-有效播放
  //20210916 聚星新增
  COME_BACK_MAIN_MEDIA = 907; //返回快手app
  //20211025 add 激励电商
  AD_GOODS_VIDEO_PLAYED_STARTED = 908; // 商品视频素材开始播放
  AD_GOODS_VIDEO_PLAYED_PAUSE = 909; // 商品视频素材暂停播放
  AD_GOODS_VIDEO_PLAYED_END = 910;// 商品视频素材播放完成
  //20211027 add 中间页
  AD_MIDDLE_LANDING_PAGE_LOADED = 911; //中间页进入广告H5页面时上报，本次用于智能下载中间页
  AD_MIDDLE_LANDING_PAGE_IMPRESSION = 912; //中间页曝光
  AD_MIDDLE_LANDING_PAGE_CLOSED = 913; // 中间页的关闭
  //20211101 add
  AD_SENSOR_FAILED = 914 ;// 传感器失败打点，目前包括摇一摇和扭一扭传感器
  EVENT_24H_STAY  = 915 ;// 激活后24小时次日留存
  //20211125 cyl_add
  AD_BLACKLIST_URL_BLOCK = 916 ;//黑名单url发生拦截时打点，一般是在loading页的时候发生黑名单拦截的情况
  //20220104 add 磁力金牛
  AD_MERCHANT_FOLLOW_ROI = 917;// 涨粉roi

  AD_MERCHANT_FOLLOW_QUALITY = 927; //涨粉质量 

  //20220811 cyl_add
  AD_FALL_ELEMENT_IMPRESSION = 928 ;//飘落彩蛋曝光，目前用于开屏
  //20220817 cyl_add 激励互动卡片
  AD_AWARD_INITIALIZE_IMPRESSION = 929 ;// 激励互动卡片初始样式曝光
  AD_AWARD_CARD_IMPRESSION = 930 ; //激励互动卡片卡片曝光
  AD_AWARD_CARD_END_IMPRESSION = 931 ;//激励互动卡片最终样式曝光
  AD_COMPONENT_IMPRESSION = 932 ;//组件曝光
  AD_LIVE_AUDIENCE_QUALITY = 933; // 直播进人质量优化目标
  //20220901 cyl_add
  AD_ITEM_SLIDE = 934 ;//表示用户从当前广告，划走到下一个广告，或划走到非联盟广告区域。这个重复上报，指标计算的时候不去重
  AD_DEEPLINK_EVOCATION_WEB_FAIL = 935 ;//跳转失败.广告主链接换端
  AD_COMPONENT_CLICK = 936 ;//组件点击
  EVENT_ENTER_MINI_PROGRAM = 937 [deprecated = true]; // 进入微信小程序优化目标，由于没有体现roi，现阶段废弃，改用MINI_APP_ROAS
  //20221117 cyl_add 搜索有效曝光
  AD_RATE_IMPRESSION = 938 ;//曝光比例，搜索有效曝光控制为100%曝光。
  AD_LIVE_AUDIENCE_FAST = 939 ;// 新增快速进人目标
  AD_SIDE_SLIDE_WINDOW_CLOSE = 940 ;////侧滑小窗退出
  AD_MERCHANT_FOLLOW_FAST = 941; // ESP-诉求版-进人速度最优
  AD_EVOCATION_WEB_RECEIVE = 942; //端内跳转信息接收监听，新增
  MINI_APP_ROAS = 943 ;// 小程序ROI的优化目标
  AD_STOREWIDE_ROAS = 944 ;// 磁力金牛全店 ROI
  AD_PHOTO_CO_CREATOR_IMPRESSION = 945;// 共创者头像曝光。目前这三个聚星在使用
  AD_PROFILE_PAGE_IMPRESSION  = 946;//个人主页曝光
  AD_PROFILE_PAGE_CLOSED  = 947 ; //个人主页关闭
  AD_EFFECTIVE_CUSTOMER_ACQUISITION = 948;//有效客户获取，优化目标
  AD_ITEM_POSITIVE = 949;//正反馈
  AD_PROFILE_TAG_IMPRESSION = 950; //p页标签曝光，品牌
  AD_COLLECTION_LANDING_PAGE_ITEM_IMPRESSION = 951; //合集页素材曝光
  AD_SERVICE_TAB_IMPRESSION = 952; //服务tab曝光
  AD_FANSTOP_PUSH = 953; // 粉条push相关优化目标
  AD_SERVICE_MODULE_IMPRESSION = 954 ;// 服务tab下首个模块曝光
  AD_ITEM_NEGATIVE_SHORT_LINK = 955 ;//短链路负反馈点击，和AD_ITEM_NEGATIVE功能一致，只是负反馈链路缩短
  AD_ITEM_DISLIKE_IMPRESSION_SHORT_LINK = 956 ;//短链路负反馈不感兴趣页面曝光，和AD_ITEM_DISLIKE_IMPRESSION功能一致
  AD_ITEM_NEGATIVE_2ND_PAGE_SHORT_LINK = 957 ;//短链路负反馈二级页面点击，和AD_ITEM_NEGATIVE的二级页面一致
  AD_ON_FOREGROUND = 958 ;//从后台切换回快手广告
  AD_ON_BACKGROUND = 959 ;//从广告切换至后台
  AD_GET_NEO_GOLD_SUCCESS = 960 ;//激励金币金领取成功
  AD_LANDING_PAGE_LCP_IMPRESSION = 961 ;//落地页最大元素曝光，web的largest contentful paint
  AD_CID_ROAS = 962 ; // cid roi 出价
  AD_POI_CLICK = 963 ; // 本地推 poi 点击
  AD_GOODS_CLICK = 964 ; // 本地推 团购 点击
  AD_PHOTO_CO_CREATOR_CLICK = 965 ;//聚星共创模式下，点击共创头像。业务：聚星
  EVENT_RETENTION_DAYS = 966; //每日留存，https://team.corp.kuaishou.com/task/T4079073
  AD_ITEM_DOWNLOAD_DELETE_APPS_FOR_MEMORY = 967 ;// 下载内存不足，点击删除其他app
  AD_PAGE_LEAVE = 968;//广告页面离开，不包括上下滑AD_DETAIL_PAGE_CLOSED场景，比如tab切换，头像点击等
  //20231114 add 短剧相关事件
  AD_COLLECTION_LANDING_PAGE_ITEM_CLOSE = 969 ;//合集页素材关闭，短剧的合集页的单素材详情页关闭的时候上报
  AD_COLLECTION_LANDING_PAGE_IMPRESSION = 970 ;//合集页面板曝光，短剧的合集面板出现的时候上报
  AD_COLLECTION_LANDING_PAGE_CLICK  = 971 ;//合集页面板点击，短剧合集面板，点击某一集
  AD_COLLECTION_FAVORITES = 972  ;//合集页点击收藏，收藏的是整个合集
  AD_COLLECTION_CANCEL_FAVORITES = 973 ;//合集页点击取消收藏
  AD_COLLECTION_SHARE = 974 ;//合集页分享
  AD_PURCHASE_PAGE_IMPRESSION = 975 ;//付费面板曝光，短剧付费面板
  AD_PURCHASE_PAGE_CLICK = 976 ;//付费面板套餐点击，每次点击都重复上报
  AD_PURCHASE_PAGE_ACTIONBAR_CLICK = 977 ;//付费面板支付点击，付费面板的actionbar点击的时候上报
  AD_IAA_ROAS = 978;  // 首日变现ROI（iaa专用）
  //20240418 add 私信-表单挂载私信组件的相关事件
  AD_IM_COMPONENT_IMPRESSION = 979;   // 私信组件曝光
  AD_IM_COMPONENT_CLICK = 980;    // 私信组件点击
  AD_ITEM_SHARE_CLICK = 981; //点击快小游新样式分享按钮上报
  EVENT_FORM_SUBMIT_INDIRECT = 982; // 间接链路（提交）带来的成交，商业化归因使用，无实际对应的客户端埋点
  AD_IM_COMPONENT_BUBBLE_IMPRESSION = 983; // 表单挂载私信组件气泡曝光
  AD_IM_COMPONENT_BUBBLE_CLICK = 984; // 表单挂载私信组件气泡点击
  AD_ROAS_IAAP = 985;  // 混变 roi
  EVENT_PHONE_CALL = 986; // 400电话拨打
  AD_MIDPAGE_ITEM_SHOW = 987;// 线索中间页组件曝光
  AD_MIDPAGE_ITEM_CLICK = 988;// 线索中间页组件点击
  AD_FEED_PHOTO_SUCCESS_UNLOCK = 989;// 观看信息流视频成功解锁
  AD_SEVEN_DAY_ROAS_IAAP = 990; // 七日混变 roi
}
