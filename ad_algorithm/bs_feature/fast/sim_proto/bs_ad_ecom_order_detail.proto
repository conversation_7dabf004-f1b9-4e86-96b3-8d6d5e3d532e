// copy from teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.proto

syntax = "proto3";

package bs.kuaishou.ad.algorithm;

// 直营电商后链路转化的订单信息
message AdEcomOrderDetail {
    // order 信息
    uint64 order_id = 1;
    uint64 sku_id = 2;
    uint64 sku_price = 3; //单价
    uint64 sku_origin_price = 4; //原价
    uint32 sku_num = 5; // 数量
    uint64 total_amount = 6;
    uint64 seller_id = 7;
    int32 status = 8; // 1待发货 2 已发货  3 系统取消  4 已签收(货到付款完成态) 5 拒签(货到付款逆向终态  6 物流状态异常 8 卖家取消 9买家取消  10待付款  60 用户确认收货 91在线支付超时关闭93 退款成功  96 订单已分账
    uint64 address_id = 9;
    uint32 risk_level = 10; // 订单风险等级
    uint64 delivery_time = 11; // 发货时间
    uint32 channel_type = 12; //渠道类型枚举
    uint32 channel = 13; //渠道枚举
    uint32 pay_type = 14; //支付方式
    string pay_id = 15; // 支付中台id
    uint64 pay_time = 16; //支付时间
    uint64 pay_done_time = 17; //支付完成时间
    uint64 pay_expire_on = 18; //在线支付过期时间
    uint64 close_time = 19; //关闭时间
    uint64 refund_apply_time = 20; //退款申请时间
    uint64 refund_confirm_time = 21; //商家确认退款时间
    uint64 refund_done_time = 22; //退款完成时间
    uint32 refund_status = 23; //退款状态
    uint32 pay_channel = 24; //支付渠道
    uint32 pay_status = 25; //支付状态
    uint32 send_time = 26; //承诺发货时间
    uint32 pay_type_opt = 27; //默认支付方式

    // 物流信息
    uint64 logistics_id = 28; //物流中台id
    uint32 delivery_corp_id = 29; // 物流公司编号
}
