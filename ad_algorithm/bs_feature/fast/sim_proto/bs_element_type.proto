// copy from teams/ad/ad_proto/kuaishou/ad/common_ad_log.proto
syntax = "proto3";

package bs.kuaishou.ad;

// 广告元素类型，作为 AD_ELEMENT_XXX 事件的参数
enum ElementType {
    UNKONWN_ELEMENT_TYPE = 0;
    ELEMENT_SPLASH = 1; // 开屏主体，AD_SPLASH_CLICK时候会设置
    ELEMENT_SPLASH_ACTION_BAR = 2; // 开屏action bar
    ELEMENT_SPLASH_JUMP_OVER = 3; // 开屏跳过按钮
    ELEMENT_COVER_ACTION_BAR = 4; // 封面的action bar
    ELEMENT_MERCHANT_SHOPPING = 5; // 小店中间页的去购买

    ELEMENT_CARD_ICON_CLICK = 6; // android 下载场景点击通用卡片app icon、
    ELEMENT_CARD_ICON_NAME_CLICK = 7; //  android 下载场景点击通用卡片app 名称
    ELEMENT_CARD_ICON_COMMENT_CLICK = 8; // android 下载场景点击通用卡片app描述
    ELEMENT_PLAY_END_ICON_CLICK = 9; // 用户成功点击播放结束页appicon时触发，android端点击触发成功后跳转悬浮框
    ELEMENT_PLAY_END_ICON_TITLE_CLICK = 10; // 用户成功点击app名称时触发，android端点击触发成功后跳转悬浮框
    ELEMENT_PLAY_END_ICON_DESCRIPTION_CLICK = 11; // 用户成功点击详细描述时触发，android端点击触发成功后跳转悬浮框
    ELEMENT_PLAYEND_APP_SCORE_CLICK = 12; // 用户成功点击app评分时触发，android端点击触发成功后跳转悬浮框
    ELEMENT_PENDANT = 13; // 红包挂件曝光, 根据具体广告位信息上报

    ELEMENT_COVER_LAYER = 14; // 蒙层
    ELEMENT_PLAY_AGAIN_ON_COVER_LAYER = 15; // 蒙层上的【再玩一次】按钮
    ELEMENT_CARD_IMPRESSION = 16; // 弹窗卡片展现

    ELEMENT_PLAY_END_CALL_IMPRESSION = 17; // 播放结束页号召按钮元素被成功曝光
    ELEMENT_PLAY_DETAIL_CALL_IMPRESSION = 18; // 播放详情页号召按钮元素被成功曝光
    ELEMENT_ACTION_BAR = 19; // 蓝条转化按钮曝光
    ELEMENT_LABEL_IMPRESSION = 20; // 展示标签时上报
    ELEMENT_LABEL_CLICK = 21; // 点击标签时上报
    ELEMENT_PORTRAIT_CLICK = 22; // 点击头像进入中间页
    ELEMENT_COMMENT_CLICK = 23; // 点击名字进入中间页
    ELEMENT_COMMENT_DETAIL_CLICK = 24; // 点击作品描述进入中间页
    ELEMENT_LEFT_SLIDE_CLICK = 25; // 左滑进入中间页
    ELEMENT_COMMENT_AREA_ICON_CLICK = 26; // 评论区第一条头像跳转中间页
    ELEMENT_COMMENT_AREA_NAME_CLICK = 27; // 评论区第一条名字跳转中间页
    ELEMENT_COMMENT_AREA_DETAIL_CLICK = 28; // 评论区第一条描述跳转中间页

    ELEMENT_BULLET_IMPRESSION = 29; // 弹幕曝光时上报
    ELEMENT_BULLET_CLICK = 30 ; // 点击弹幕时上报（无跳转）

    ELEMENT_PENDANT_CLICK = 31 ; // 直播页面，挂件广告点击
    ELEMENT_VOLUME_OFF_CLICK = 32 ; // 播放中转静音
    ELEMENT_VOLUME_ON_CLICK = 33 ; // 静音转播放中

    // Android（不含下载类）&iOS - 【双feed】多利益点卡片组件埋点：图标、图片、主标题、子标题等
    ELEMENT_CARD_ICON_OTHERAREA_CLICK = 34 ;
    ELEMENT_CARD_PICTURE_CLICK_ONE = 35 ;
    ELEMENT_CARD_PICTURE_CLICK_TWO = 36 ;
    ELEMENT_CARD_PICTURE_CLICK_THREE = 37 ;
    ELEMENT_CARD_PICTURE_TITLE_CLICK_ONE = 38;
    ELEMENT_CARD_PICTURE_TITLE_CLICK_TWO = 39 ;
    ELEMENT_CARD_PICTURE_TITLE_CLICK_THREE = 40 ;
    ELEMENT_CARD_PICTURE_SUBTITLE_CLICK_ONE = 41 ;
    ELEMENT_CARD_PICTURE_SUBTITLE_CLICK_TWO = 42 ;
    ELEMENT_CARD_PICTURE_SUBTITLE_CLICK_THREE = 43 ;

    ELEMENT_CONFIRM_DOWNLOAD_POPUP_WINDOW_POSITIVE_CLICK = 44 ; // 非actionbar的点击：点击下载确认弹窗的确认按钮

    ELEMENT_PENDANT_ICON_CLICK = 45 ;//点击挂件icon
    ELEMENT_PENDANT_BUTTON_CLICK = 46 ;//点击按钮
    ELEMENT_PENDANT_NAME_CLICK = 47 ;  // 直播挂件点击名称描述
    ELEMENT_PENDANT_OTHER_AREA_CLICK = 48 ;//点击其他区域
    ELEMENT_BUSINESS_GOODS = 49 ;//电商商品
    ELEMENT_BUSINESS_COUPON = 50 ;//电商优惠券
    ELEMENT_EXTEND_CLICK  = 51 ; //展开按钮点击
    ELEMENT_ONEBUTTON_INSTALL_CLICK = 52 ; //一键安装页面中，一键安装按钮点击数量
    ELEMENT_DOWNLOAD_DELETE_CANCELLED = 53 ; //从下载管理器页面，删除弹窗页面关闭；同时上报参数BoxCancelledType
    ELEMENT_DOWNLOAD_DELETE_CONFIRMED = 54 ; //从下载管理器页面，进入删除弹窗页面中，确定按钮点击
    ELEMENT_DOWNLOAD_MANAGEMENT_ENTRY_IMPRESSION = 55 ; // 广告短视频播放页，下载管理器入口曝光时上报；同时上报红点是否出现参数ClientParams.is_reddot_show；
    ELEMENT_DOWNLOAD_MANAGEMENT_ENTRY_CLICK = 56 ; // 下载管理器入口点击；同时上报红点是否出现参数ClientParams.is_reddot_show
    ELEMENT_DOWNLOAD_MANAGEMENT_ENTRY_CLOSE = 57 ; // 下载管理器入口下方关闭按钮点击；同时上报红点是否出现参数ClientParams.is_reddot_show；

    ELEMENT_BULLET_CANCEL_LIKE = 58; // 点击点赞弹幕取消点赞

    ELEMENT_CAPTION_CLICK = 59; // caption中“查看详情”链接点击
    ELEMENT_COMMENT_ITEM_CLICK = 60; // 评论区转化链接点击
    ELEMENT_CARD_RANK_CLICK = 61; // 用户成功点击卡片上的评分
    ELEMENT_CARD_DOWNLOAD_CNT_CLICK = 62; // 用户成功点击卡片里的下载次数
    ELEMENT_CARD_TAG_CLICK = 63; // 用户成功点击卡片里的二级行业
    ELEMENT_CARD_LABEL_CLICK = 64; // 用户点击卡片里的标签

    ELEMENT_INTERACTIVE_COVER_TAG = 65;//分屏广告封面标签
    ELEMENT_INTERACTIVE_PHOTO_TAG = 66;//分屏广告素材标签
    ELEMENT_CARD_ACTION_BAR_CLICK = 67; //H5卡片中，ActionBar点击
    ELEMENT_CARD_VIEW_DETAIL_CLICK = 68; //H5卡片中，点击查看详情

    ELEMENT_CLOSE_ICON = 69; // 关闭按钮

    // 隐私提示相关
    ELEMENT_PRIVACY_IMPRESSION = 70; //隐私提示曝光时上报
    ELEMENT_PRIVACY_CLICK = 71; //点击隐私提示时上报
    ELEMENT_PRIVACY_BULLET_IMPRESSION = 72; //隐私弹框展现
    ELEMENT_PRIVACY_BULLET_CLICK = 73; //点击隐私弹框Tab，同时上报场景参数
    ELEMENT_PRIVACY_BULLET_CLICK_CANCELLED = 74; //隐私弹框关闭，同时上报参数
    ELEMENT_PRIVACY_BULLET_CLICK_RETURNED = 75; //点击隐私二级弹框返回按钮
    ELEMENT_PRIVACY_PROMPT_IMPRESSION = 76; //落地页风险提示横幅展现
    ELEMENT_PRIVACY_PROMPT_CLICK_CLOSED = 77; //点击隐私弹框框关闭按钮
    ELEMENT_PRIVACY_PROMPT_SLIDE_CLOSED = 78; //风险提示-上滑落地页关闭提示条

    ELEMENT_BLANK_AREA_CLICK = 79; // 任务空白区域点击

    ELEMENT_CARD_RECO_IMPRESSION = 80; //推荐理由曝光
    ELEMENT_CARD_RECO_CLICK = 81; // 用户点击推荐理由
    ELEMENT_PLAYABLE_JUMP_OVER = 82 ;//playable跳过
    ELEMENT_PHOTO_CLICK = 83; //点击作品描述进入中间页

    //存储权限引导弹窗按钮
    ELEMENT_DOWNLOAD_MEMORY_FORBIDDEN_TOAST = 84;
    ELEMENT_DOWNLOAD_MEMORY_FORBIDDEN_FOREVER_TOAST = 85;

    ELEMENT_DOWNLOAD_CARD_IMPRESSION = 86; // 下载卡片中间页曝光
    ELEMENT_PLAYEND_CARD_IMPRESSION = 87; // 播放结束页卡片展示
    ELEMENT_PLAYEND_ICON_RECO_CLICK = 88; // 点击播放结束页推荐理由

    ELEMENT_COMMENTS_ACTION_BAR_IMPRESSION = 89; // 评论区蓝条转化按钮曝光
    ELEMENT_PRIVACY_POLICY = 90; // 快直播任务，隐私政策点击
    ELEMENT_PERMISSION_INFO = 91; // 快直播任务，权益信息点击

    ELEMENT_INSTALL_NOTICE_IMPRESSION = 92; // 安装提醒元素曝光
    ELEMENT_CONVERSION_NOTICE_IMPRESSION = 93; // 激活提醒元素曝光

    ELEMENT_VPN_AUTHORITY_GUIDE_IMPRESSION = 94; //VPN权限引导 - 卡片曝光
    ELEMENT_INSTALL_INTERCEPTION_DESCRIPTION_CLICK = 95; //VPN权限引导 - 点击「什么是安装拦截」
    ELEMENT_VPN_AUTHORIZATION_CLICK = 96; //VPN权限引导 - 点击「立即授权」
    ELEMENT_VPN_AUTHORITY_GUIDE_CANCELLED = 97; //VPN权限引导 - 点击「关闭」
    ELEMENT_GET_INSTALL_INTERCEPTION_CLICK = 98; //VPN权限引导 - 点击「我知道了」
    ELEMENT_PLAYING_LANDINGPAGE_IMPRESSION = 99; //播放过程落地页展示
    ELEMENT_PLAYING_FORM_CARD_IMPRESSION = 100; //播放过程中的表单卡片展示
    ELEMENT_PLAYING_FORM_CARD_CLICK = 101; //播放过程中的表单卡片点击
    ELEMENT_PLAYEND_FORM_CARD_CLICK = 102; //播放结束页卡片点击
    ELEMENT_CONFIRM_CLOSED_CARD_IMPRESSION = 103; //确认退出广告卡片曝光
    ELEMENT_CONFIRM_CLOSED_CARD_STAY_CLICK = 104; //退出拦截弹窗中“留下看看”按钮点击
    ELEMENT_CONFIRM_CLOSED_CARD_LEAVE_CLICK = 105; //退出拦截弹窗中“残忍离开”按钮点击
    ELEMENT_CONFIRM_CLOSED_CARD_BLANK_AREA_CLICK = 106; //退出拦截弹窗外空白区域点击

    TAG = 107; //标签点击 已废弃
    ELEMENT_TAG_CLICK = 108; //标签点击

    ELEMENT_PAUSE = 109; //暂停按钮点击
    ELEMENT_CLEARSCREEN = 110; //清屏操作点击

    ELEMENT_PAUSE_DOWNLOAD_CONFIRMED_IMPRESSION = 111;//弹框成功曝光时上报
    ELEMENT_CONTINUE_DOWNLOAD_CONFIRMED = 112;//用户成功点击继续按钮
    ELEMENT_PAUSE_DOWNLOAD_CONFIRMED = 113;//用户成功关闭弹窗时上报
    ELEMENT_SPLASH_ENTER_HOMEPAGE_BUTTON_CLICK = 114; //开屏进入首页按钮点击
    ELEMENT_SPLASH_SMALL_WINDOW_IMPRESSION = 115; //开屏小窗曝光
    ELEMENT_INSTALL_NOTICE_CLOSED = 116; // 引导弹窗成功关闭
  
    ELEMENT_CARD_NON_ACTION_BAR_CLICK = 117; //点击非actionBar区域 上报
    ELEMENT_CARD_LEFT_ACTION_BAR_CLICK = 118; // 点击左边actionBar区域 上报
    
    ELEMENT_GET_POINTS = 119; //领取积分

    ELEMENT_SURVEY = 120; //广告信任生态-用户反馈入口：问卷
    ELEMENT_PENDANT_ICON_DESCRIPTION_CLICK = 121;//点击图标描述

    ELEMENT_SEARCH_PHOTO_CLICK = 122; // 搜索结果页点击，进入视频详情页
    //20210510 add
    SPLASH_CONVERSION_BUTTON_IMPRESSION = 123; //开屏转化按钮曝光
    SPLASH_SKIPED_5S_IMPRESSION = 124; //开屏跳过倒计时5秒曝光
    ELEMENT_SPLASH_SMALL_WINDOW_PICTURE_IMPRESSION = 125; //开屏图片小窗曝光
    ELEMENT_SEARCH_TOPIC_CLICK = 126; // 搜索结果页-点击广告描述区的话题高亮部分，跳转话题广场
    //20210527 add
    ELEMENT_BIG_CARD_IMPRESSION = 127; //大表单曝光
    ELEMENT_BIG_CARD_CLOSE_CLICK = 128; //大表单关闭按钮点击
    ELEMENT_BIG_CARD_SUBMIT_CLICK = 129; //大表单提交按钮点击
    ELEMENT_BIG_CARD_ENTER_LANDING_PAGE = 130; //大表单跳转至落地页按钮点击

    ELEMENT_TOAST_CLOSE = 131; //toast关闭
    //20210618 add
    ELEMENT_VISIRER_AREA = 132; //观众区域
    ELEMENT_GUAID_AREA = 133; //用户引导区域
    ELEMENT_NEW_GOODS_REMINDING = 134; //新上架气泡
    ELEMENT_GOODS_REVIEW_PAGE = 135; //回放页点击
    ELEMENT_GOODS_DETAIL_PAGE = 136; //商品详情页
    ELEMENT_BIG_SHOP_CARD = 137; //正在讲解卡片
    ELEMENT_GOODS_ITEM = 138; // 列表商品
    ELEMENT_STRONG_CARD = 139; //强卡片
    ELEMENT_WEAK_CARD = 140; //弱卡片
    ELEMENT_ITEM_PLAY_BACK_CLICK = 141; //商品讲解回放点击
    ELEMENT_ITEM_CARD_CLICK = 142; //回放页-商品卡片点击
    ELEMENT_CARD_STRONG_STYLE = 143; //强样式卡片
    ELEMENT_CARD_WEAK_STYLE = 144; //弱样式卡片
    ELEMENT_ITEM_CLOSE_ICON_CLICK = 145; //回放页-关闭icon点击
    ELEMENT_RETURN_LIVEROOM_CLICK = 146; //回放页-返回直播间点击
    ELEMENT_PAUSE_BUTTOM_CLICK = 147; //回放页-暂停按钮点击
    ELEMENT_PROGRESS_BUTTON_CLICK = 148; //回放页-进度条点击
    //20210622 add
    ELEMENT_RETAIN_CARD_IMPRESSION = 149; //挽留用户弹窗曝光
    ELEMENT_RETAIN_CARD_CONTINUE_CLICK = 150; //挽留用户弹窗内继续观看按钮点击
    ELEMENT_RETAIN_CARD_LEAVE_CLICK = 151; //挽留用户弹窗内离开按钮点击
    MIDDLE_PAGE_DOWNLOAD_BUTTON_CLICK = 152; //中间页触发下载按钮点击
    MIDDLE_PAGE_BACK_BUTTON_CLICK = 153; //中间页返回上一级按钮点击
    // POPLAY 彩蛋弹窗
    ELEMENT_POPLAY_PICTURE_IMPRESSION = 154; // POPLAY 彩蛋弹窗，弹窗曝光，浮现时上报埋点，重新播放时重新上报
    ELEMENT_POPLAY_CLOSE_CLICK = 155; // POPLAY 彩蛋弹窗，点击彩蛋弹窗关闭按钮
    ELEMENT_POPLAY_OTHER_CLICK = 156; // POPLAY 彩蛋弹窗，彩蛋蒙层其他区域点击，点击后无反应
    ELEMENT_SPLASH_INTERACTION_ROTATE_CLICK = 157; // 开屏轻互动点击（通过旋转手机触发或手势触发）
    EVOCK_DOUBLE_CARD_IMPRESSION = 158; //再次开启引导弹窗曝光
    EVOCK_DOUBLE_CARD_OK_CLICK = 159; //再次开启引导弹窗中「好的」点击
    EVOCK_DOUBLE_CARD_CANCEL_CLICK = 160; //再次开启引导弹窗中「取消」点击
    //20210714 add
    FORCE_WATCH_ELEMENT_IMPRESSION = 161; //强制观看元素曝光
    //20210716 add
    ELEMENT_ANVIDEO_CONFIRMED = 162; // 激励视频再看一个弹窗中「再看一个按钮」
    ELEMENT_ANVIDEO_CLOSE = 163; // 激励视频再看一个弹窗中「坚持退出」按钮
    ELEMENT_PLAYEND_ANVIDEO = 164; //激励视频播放结束页「再看一个广告按钮」
    //20210722 add
    ELEMENT_AD_AUTO_CLOSE_IMPRESSION = 165; //自动离开
    ELEMENT_SPLASH_SHAKE_CLICK = 166; // 开屏摇一摇点击（通过摇动手机触发）
    ELEMENT_SPLASH_SLIDE_CLICK = 167; // 开屏横划解锁点击（通过滑动触发）
    //20210727 add
    ELEMENT_RETAIN_CARD_FAILED_IMPRESSION = 168; //挽留用户弹窗因触发频控未展示上报

    //20210825 add
    ELEMENT_IMMEDIATELY_OBTAIN_IMPRESSION = 169;//立即领取元素曝光
    ELEMENT_COPY_CODE_IMPRESSION = 170;//复制礼包码元素曝光
    ELEMENT_IMMEDIATELY_OBTAIN_CLICK = 171;//立即领取元素点击
    ELEMENT_PROMPT_COPY_CODE_CLICK = 172;//复制礼包码元素点击
    ELEMENT_OBTAIN_SUCCESS_WINDOW_IMPRESSION = 173;//领取成功弹窗曝光
    ELEMENT_COPY_CODE_CONFIRM_CLICK = 174;//复制礼包码确认元素点击
    ELEMENT_COPY_CODE_CLOSE_CLICK = 175;//复制礼包码取消元素点击
    ELEMENT_TOAST_COPY_CODE_IMPRESSION = 176;//复制礼包码成功toast提示弹窗曝光

    //20210915 add 激励视频小黄车
    ELEMENT_TROLLEY_IMPRESSION = 177; //小黄车元素的曝光
    //20211025 add 激励电商
    ELEMENT_GOODS_PHOTO_IMPRESSION = 178; //商品素材曝光
    //20211027 add 中间页
    ELEMENT_MIDDLE_PAGE_CLICK = 179; //中间页图片点击，本次用于智能下载中间页
    //20211029 add
    ELEMENT_PLAY_PAUSE = 180; //点击视频暂停播放，适用于效果广告业务
    ELEMENT_PLAY_CONTINUE = 181;//点击视频继续播放，适用于效果广告业务
    ELEMENT_RECOMMEND_CARD_IMPRESSION = 182;// 相关推荐场景卡片曝光
    ELEMENT_RECOMMEND_CARD_ICON_CLICK = 183;//  相关推荐场景卡片点击
    //20211101 add
    ELEMENT_ROTATE_ACTION_BAR  = 184 ;//扭一扭元素曝光
    ELEMENT_SHAKE_ACTION_BAR = 185;// 摇一摇元素曝光
    ELEMENT_SPLASH_BALL_ROLL_CLICK = 186; // 滚动小球开屏
    ELEMENT_SPLASH_FLIP_X_CLICK = 187; // 翻转 x 轴开屏
    ELEMENT_SPLASH_FLIP_Y_CLICK = 188; // 翻转 y 轴开屏
    //20211116 add
    ELEMENT_PAGE = 189 ;//页面元素，配合业务场景类型business_scene_type，可以知道是什么页面
    //20211125  cyl_add
    ELEMENT_FINGER_SWIPE = 190 ;//滑动元素，通过滑动进入广告
    ELEMENT_POP_UP = 191 ;//弹窗元素
    //20211202 cyl_add
    ELEMENT_PENDANT_TRY_PLAY = 192; //试玩挂件元素
    //20211220 cyl_add
    ELEMENT_FINGER_SWIPE_ACTIONBAR = 193 ;//滑动元素，必须经过actionbar，和ELEMENT_FINGER_SWIPE的区别是必须经过actionbar
    //20211222 cyl_add
    ELEMENT_BUTTON = 194 ;//按钮元素，可以配合button_type进行判断按钮类型
}
