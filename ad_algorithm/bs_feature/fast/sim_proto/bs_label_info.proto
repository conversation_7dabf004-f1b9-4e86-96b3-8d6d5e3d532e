// copy from teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.proto
syntax = "proto3";

package bs.kuaishou.ad.algorithm;

import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_action_type.proto";
import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_callback_log_event_type.proto";
import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_universe_client_info.proto";
import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_ecom_order_detail.proto";
import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_referrer_info.proto";
import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_label_info_common_attr.proto";
import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_poi_params.proto";
import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_label_attr.proto";


/////// 打标信息 /////////////
message LabelInfo {
    bool click = 1;
    bool like = 2;
    bool follow = 3;
    bool forward = 4;
    bool profile_enter = 5; //这个字段目前没有用到，是从推荐copy过来的。先别用
    bool all_hot_follow = 6;
    bool unlike = 7;
    bool unfollow = 8;
    bool feedback_negative = 9;
    bool item_click = 10;
    bool feed_impression = 11; // feed流中的曝光
    bool played_3s = 12;
    bool played_5s = 13;
    bool played_end = 14; // 播放结束
    bool comment = 15;
    uint64 played_time = 16; //播放时长
    uint64 infer_played_time = 17; //由于只等20分钟导致的没有close行为-推测播放时长
    bool photo_to_profile = 18; // 播放页面点击头像进入profile
    bool item_download_completed = 19; // 下载完成
    bool recall = 20; // 是否被粗排召回
    bool delivery = 21; // 被粗排召回，并且被下发（有可能没有得到展示 ）
    bool landing_page_form_submitted = 22; // landing page表单提交
    bool item_impression = 23; // 视频开始播放
    bool item_conversion = 24; // item转化，根据客户的需求定义转化
    bool item_purchase = 25; // item付费
    bool click_to_live = 26; //点击跳转到直播
    uint64 price = 27; // 计费信息
    AdActionType call_back_label = 28;  // 当前回传的行为标签
    bool item_ios_new_click = 29; // AD_ITEM_IOS_NEW_CLICK, iOS新详情页新增打点, 利用appstore获取下载时跳出快手应用的行为监控
    bool ad_photo_to_profile_follow = 30; // AD_PHOTO_TO_PROFILE_FOLLOW, 用户从photo点进profile页,然后再follow
    bool ad_new_datail_page_click = 31; // adx详情页点击
    bool ad_new_detail_page_entered = 32; //  adx详情页进入
    bool ad_new_detail_page_guide_impression = 33; //adx详情页展现
    bool ad_new_detail_page_guide_click = 34;
    bool ad_landing_page_entered = 35;
    bool ad_landing_page_click = 36;
    int64 ueq = 37;
    uint64 purchase_amount = 38; // 单次付费金额
    repeated AdActionType user_action_type = 39; // 合并所有的action type到一个list里
    bool ad_photo_negative = 40;
    bool ad_item_negative = 41;
    bool ad_photo_report = 42;
    bool small_shop_purchase = 43;
    AdCallbackLog.EventType callback_event = 44; // 用callabck的event表示回传行为
    bool large_payment = 45; // 大额付费
    bool approximate_purchase = 46;  // 近似购买
    bool shop_cart_click = 47;  // 小黄车点击
    bool live_shop_cart_click = 48;  // 直播小黄车点击
    bool shop_item_click = 49;  // 小黄车卡片点击
    bool live_shop_link_jump = 50;  // 直播购物车链接跳转
    bool merchant_order_pay = 51; //是否小店支付成功
    bool ad_poi_comment_area_impression = 52; //poi曝光
    PoiParams poi_parms = 53; //poi参数，带有这个参数的item_click，才是poi点击
    repeated AdCallbackLog.EventType multi_label_fact = 54; // 多label标注的实际label，也就是广告主真的回传了什么行为
    bool target_hit = 55; // 是否被定向命中，用来做定向模型label
    uint64 callback_occur_ts = 56; // 回传事件的发生时间，用于 delay model
    uint64 callback_check_ts = 57; // 回传事件的检查时间，用于 delay model
    bool target_result = 58; // target server之后结果
    bool prerank_result = 59; // 粗排之后结果
    bool unlabel = 60; // 未标注数据
    UniverseClientInfo universe_client_info = 61;
    string live_room_pattern = 62; // 加入直播间样式
    uint64 extend_label = 63; // 用于离线临时添加非常规label
    bool direct_follow = 64; // 直接关注
    int64 is_inner_delivery = 65;  // 是否粉条内部订单
    repeated uint64 multi_label_occur_ts = 66; // 多label标注的实际label发生时间
    bool live_played_started = 67; // 直播开始播放
    AdEcomOrderDetail ad_ecom_order_detail = 68; // 直营电商订单信息
    int64 purchase_amount_by_follow = 69; // 涨粉后支付的订单金额
    bool event_order_paied_by_follow = 70; // 涨粉后支付行为
    bool live_play_3s = 71;  // 直播 p3s
    uint64 live_played_time = 72;  // 直播播放时长
    bool order_paid = 73; // EVENT_ORDER_PAIED
    bool landing_page_paid = 74; // 表单类付费
    uint64 log_process_timestamp = 75; // callback 回传的时间 fastemit流添加
    uint64 delivery_time = 76; // 日志下发的时间 fastemit流添加
    uint64 delayed_time = 77; // 真实的回传等待时间 fastemit流添加
    uint64 pred_delayed_time = 78; // 预估的回传等待时间 fastemit流添加
    bool sim_multi_touch_install = 79;
    uint64 add_wechat = 80; // 微信添加
    uint64 multi_conv = 81; // 多转化
    bool live_comment = 82; // 直播间评论
    bool live_reward = 83; // 直播间打赏
    int64 purchase_amount_by_indirect = 84; // 间接支付的订单金额
    bool event_order_paied_by_indirect = 85; // 间接支付行为
    bool is_assemble_page = 86; // 标记二电引流广告
    ReferrerInfo referrer_info = 87; // 聚合落地页引流页信息
    uint32 assemble_page_convert = 88; // 聚合页非引流广告的转化数
    int32 pos = 89; // 广告展现的位置(广告在单次请求中的位置);
    bool referral_ad_convert = 90; // 引流广告是否转化
    // 服务端下发的 label_info, 从 used_item.ad_rank_infos[i].label_info_attr
    repeated LabelInfoCommonAttr label_info_attr = 91;
    int32 xdt_live_played_seconds = 92; //小店通直播时长
    bool event_page_component_click = 93; // 组件点击
    uint32 event_page_stay_duration = 94; // 页面停留时长
    double page_viewed_ratio = 95; // 页面已浏览比例
    bool shared = 96; // 分享
    bool standard_live_played_started = 97; // 标准直播开始播放
    uint64 order_paid_last_time = 98; // 直接支付，最后一次转化时间
    uint64 order_paid_by_follow_last_time = 99; // 涨粉支付，最后一次转化时间
    uint64 order_paid_by_indirect_last_time = 100; // 间接支付，最后一次转化时间
    uint64 order_paid_cnt = 101; // 直接支付次数
    uint64 order_paid_by_follow_cnt = 102; // 间接支付次数
    uint64 order_paid_by_indirect_cnt = 103; // 涨粉支付次数
    uint64 order_paid_first_time = 104; // 直接支付，第一次转化时间
    uint64 order_paid_by_follow_first_time = 105; // 涨粉支付，第一次转化时间
    uint64 order_paid_by_indirect_first_time = 106; // 间接支付，第一次转化时间
    bool splash_impression = 107; // 开屏曝光
    bool splash_click = 108; // 开屏点击
    bool splash_event_form_submit = 109; //开屏表单提交
    bool live_play_5s = 110;  // 直播 p5s
    bool download_click = 111;  // 下载点击
    bool consult_click = 112;  // 咨询点击
    bool download_imp = 113;  // 下载曝光
    bool consult_imp = 114;  // 咨询曝光
    bool actionbar_impression = 115;
    bool live_play_1m = 116;  // 直播 p1m
    bool goods_view = 117; //直播商品观看
    uint64 plc_ocpx_action_type = 118; // plc 优化目标
    uint64 plc_biz_type = 119; // plc biz type
    bool live_play_30s = 120;  // 直播 p5s
    bool leads_submit = 121;  // 线索提交
    bool played_long_time = 122;  // 长播
    bool slive_component_impression = 123; //行业直播-组件曝光
    bool slive_component_click = 124; //行业直播-组件点击
    bool slive_form_submit = 125; //行业直播-组件转化-线索收集数
    bool slive_deeplink_invoke = 126; //行业直播-组件转化-品牌推广数
    bool slive_app_conversion =127; //行业直播-组件转化-应用激活数
    bool slive_h5_impression = 128; //行业直播-组件转化-H5落地页曝光数
    uint64 live_reward_amount = 129; //粉条直播打赏金额
    bool live_reservation_success = 130; // 直播预约成功
    bool stlr_live_play_1m = 131; // 正式直播间观看1m
    bool silr_live_play_1m = 132; // 简易直播间观看1m
    bool stlr_live_play_3s = 133; // 正式直播间观看3s
    bool silr_live_play_3s = 134; // 简易直播间观看3s
    bool stlr_live_play_5s = 135; // 正式直播间观看5s
    bool silr_live_play_5s = 136; // 简易直播间观看5s
    bool stlr_live_play_15s = 137; // 正式直播间观看15s
    bool silr_live_play_15s = 138; // 简易直播间观看15s
    bool stlr_live_play_30s = 139; // 正式直播间观看30s
    bool silr_live_play_30s = 140; // 简易直播间观看30s
    bool live_play_mean = 141;  // 直播 mean
    bool live_play_median = 142;  // 直播 median
    bool order_submit = 143; // EVENT_ORDER_SUBMIT
    bool live_follow = 144; // 直播间关注
    bool live_like = 145; // 直播间点赞
    bool product_buy_click = 146; // 立即购买按钮点击
    bool order_submit_click = 147; // 订单提交按钮点击
    bool ad_landing_page_loaded = 148;  //h5加载成功
    bool ad_deeplink_evocation_success = 149; //自动唤端成功
    bool ad_deeplink_evocation_web_success = 150;//h5唤端成功
    bool ad_deeplink_evocation_fail = 151; //唤端失败
    bool stlr_live_play_started = 152; //简易直播间开始播放
    bool merchant_follow = 153; //电商涨粉
    repeated uint64 ad_action_list = 154; //废弃，没有使用
    repeated LabelAttr label_attr = 155; // 废弃，没有使用
    uint64 live_reward_cnt = 156; //粉条直播打赏次数
    bool played_10s = 157; // 粉条简易直播间准备上线播放10s的产品
    bool played_7s = 158; // 直播预约有效播放项目准备上线播放7s的产品
    uint64 photo_play_second = 159; // 直播预约有效播放项目增加播放时常label辅助训练
    map<uint64, LabelAttr> label_infos=160; // 新label都放在这里，key是labelId
    uint64 cpm = 161; // 精排预估 ecpm 辅助训练
    bool item_download_installed = 162; // 下载安装完成
    double normalized_attribute_score = 163; // 归因质量分
    bool true_deeplink_evocation_success = 164; // deeplink真实唤起成功
    repeated uint64 label_id = 165; // 当前样本的流量归属，目前只用于深度拼接
    uint64 label_match_delay_time_seconds = 166; // label match 拼接延迟，计算口径为，拼接时刻减去 impression 时刻的差值
    repeated uint64 label_id_record = 167; // 历史回传的所有 label
};
