// copy from teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_base.proto
syntax = "proto3";

package bs.kuaishou.ad.algorithm;

option cc_enable_arenas = true;

message AdActionBaseInfo {
    uint64 photo_id = 1;
    uint64 second_industry_id_hash = 2; // 广告主二级行业群名称hash
    uint64 product_id_hash = 3; // 广告主产品名称hash
    uint64 app_id = 4; // App版本：unknown-0/主站-1/极速版-2
    uint64 action_timestamp = 5; // action 时间戳
    uint64 source_type = 6; // 广告类型: UNKNOWN-0/DSP-1/ADX-2/BRAND-3
    uint64 crm_defined_product_name_hash = 7; //目前没有写入数据
    uint64 crm_defined_product_type_hash = 8; //运营定义产品类型hash值
    uint64 second_industry_id_hash_v3 = 9; // 广告主二级行业群名称hash 3.0
}

message AdActionInfoList {
    repeated AdActionBaseInfo list = 1;
}
