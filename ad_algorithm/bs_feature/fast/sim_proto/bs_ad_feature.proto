// copy from teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_feature.proto
syntax = "proto3";

package bs.kuaishou.ad.algorithm;

import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_action_type.proto";
import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_label_info.proto";
import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_callback_log_event_type.proto";

option cc_enable_arenas = true;

message SparseFeature {
    uint32 index = 1;
    repeated uint64 values = 2;
}

message DenseFeature {
    uint32 index = 1;
    repeated float values = 2;
}

message LabelList {
    repeated int64 label = 1;
}

message FilterResult {
    map<int32, bool> log_filter_result = 1;
    map<int32, bool> item_filter_result = 2;
    map<int32, LabelList> label_extractor_result = 3;
}

message AdPersiaFeature {
    uint32 version = 1; // 版本
    uint64 llsid = 2;
    uint64 user_id = 3;
    uint64 item_id = 4;
    repeated SparseFeature sparse_feature = 5;
    repeated DenseFeature dense_feature = 6;

    // 以下label相关字段是以前的用于label fix的字段，暂时没有用, 只有final_label在用
    repeated AdActionType labels = 7;
    repeated AdActionType call_back_type = 8;
    double purchase_amount = 9;
    LabelInfo label_info = 10;
    repeated AdActionType call_back_label = 11;
    AdCallbackLog.EventType callback_event = 12; // 用callabck的event表示回传行为

    repeated int64 final_label = 13;
    uint64 photo_id = 14;
    uint64 time = 15;

    uint64 account_id = 16;

    FilterResult filter_result = 17;
    uint64 feature_list_hash = 18;
    uint64 cover_id = 19;
    uint64 author_id = 20;
    uint64 unit_id = 21;
    string debug_info = 22;
    int32 line_number = 23;
    int32 item_index = 24;
}

message AdFederateFeature {
    uint64 llsid = 1;
    uint64 item_id = 2;
    repeated SparseFeature sparse_feature = 3;
    repeated DenseFeature dense_feature = 4;
    int64 label = 5;
}

message AdUserAdAction {
    uint64 event_server_timestamp = 1;
    uint64 account_id = 2;
    uint64 product_name_hash = 3;
    AdActionType action_type = 4;
    AdCallbackLog.EventType callback_event = 5;
    uint64 photo_id = 6;
    uint64 author_id = 7;
    uint64 package_name_hash = 8;
    repeated uint64 extra = 9;
}

message AdUserAdActionList{
    repeated AdUserAdAction ad_user_ad_action_list = 1;
}

message AdUserAdActionMap {
    uint64 user_id = 1;
    map<uint32,AdUserAdActionList> ad_user_ad_action_list_filter_map = 2;
}


message AdFullGoodsItem {
  uint64 item_id = 1;  //商品id 8个字节64位
  uint32 timestamp=2;  // 曝光时间 4个字节32位
  uint32 src_type=3;  // 内容类型：photo/live/xiaodian_item/jinniu_item, 1个字节
  uint32 action_type = 4;  //点击，下单，购买，1个字节8位
  uint64 spu_id = 5;  //多级spu_id  8个字节64位 
  uint64 stable_x7_category = 6; //一级、二级、三级、四级商品类目，每16位存一级， 8个字节64位
  uint64 author_id = 7; //商品对应的卖家id 8个字节64位
  uint64 item_attribute =8; //商品其他属性，is_ad,价格等等
};

message AllGoods {
	repeated AdFullGoodsItem items = 1;
 	uint32 max_len = 2;
}