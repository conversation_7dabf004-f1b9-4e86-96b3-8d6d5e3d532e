// copy from teams/ad/ad_proto/kuaishou/ad/ad_base.proto
syntax = "proto2";

package bs.kuaishou.ad;

message LabelCommonTypeEnum {
    enum AttrType {
        UNKNOW_ATTR = 0;
        UINT64_ATTR = 1;
        FLOAT_ATTR = 2;
        BOOL_ATTR = 3;
        STRING_ATTR = 4;
        MAP_INT64_INT64_ATTR = 5;
    };
}

message LabelAttr {
    enum Name {
        UNKNOW_NAME = 0;
    };
    optional LabelCommonTypeEnum.AttrType type = 1;
    optional int64 name_value = 2;
    optional int64 int_value = 3;
    optional float float_value = 4;
    optional bool bool_value = 5;
    optional string string_value = 6;
    map<int64, int64> map_int64_int64_value = 7;

    optional uint64 first_occur_timestamp = 99; // 该 label 首次出现时间点
}
