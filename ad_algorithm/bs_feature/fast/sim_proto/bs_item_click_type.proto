// copy from teams/ad/ad_proto/kuaishou/ad/common_ad_log.proto
syntax = "proto3";

package bs.kuaishou.ad;

enum ItemClickType {
    UNKNOWN_ITEM_CLICK_TYPE = 0;
    ACTION_BAR_CLICK = 1; // 蓝条点击
    PLAY_END_CLICK = 2; // 重播处点击
    CAPTION_CLICK = 3; // caption中“查看详情”链接点击
    CAPTION_ITEM_CLICK = 4; // 电商caption首位链接
    PROFILE_ITEM_CLICK = 5; // profile页点击
    BRAND_PROMOTION_ITEM_CLICK = 6; // caption首位(caption上方)品牌推广链接点击
    APP_DOWNLOAD_ITEM_CLICK = 7; // caption首位(caption上方)应用下载链接点击
    COMMENT_ITEM_CLICK = 8; // 评论区转化链接点击
    BOTTOM_ICON_CLICK = 9; // 详情页右下角转化链接icon点击
    MERCHANT_SHOWCASE_CLICK = 10; //  商品橱窗卡片商品点击
    DYNAMIC_CARD_CLICK = 11; // 动态卡片点击
    LEFT_SLIDE_CLICK = 12; //左滑视频跳转
    PORTRAIT_CLICK = 13; //点击头像跳转
    COMMENT_CLICK = 14; //评论区点击跳转
    PLAY_END_ICON_CLICK = 15; // 播放结束页点击icon跳转落地页
    PLAY_END_ICON_TITLE_CLICK = 16; // 播放结束页点击icon标题跳转落地页
    PLAY_END_ICON_DESCRIPTION_CLICK = 17; // 播放结束页点击icon描述跳转落地页
    FEED_COVER_ACTION_BAR = 18; // 信息流封面页的action bar
    ECOM_CARD_CLICK = 19; // 直营电商卡片视频区点击
    ECOM_SHOPPING_TROLLEY_CLICK = 20; // 直营电商购物车视频区点击
    ECOM_CARD_COMMENT_AREA_CLICK = 21; // 直营电商卡片评论区点击
    ECOM_DISCOUNT_COMMENT_AREA_CLICK = 22; // 直营电商评论区折扣点击

    // 极速版广告播放页&播放结束页埋点需求
    COMMENT_PHOTO_CLICK = 23; // 用户成功点击描述区作者头像，头像在右侧
    COMMENT_NAME_CLICK = 24; // 用户成功点击描述区作者名称
    COMMENT_DETAIL_CLICK = 25; // 用户成功点击描述区详细描述触发转化时跳转；当场景为用户点击详情展示完整信息时，不触发此事件，只有跳转时才进行跳转；
    PLAYEND_ACTION_BAR_CLICK = 26; // 成功点击播放结束页查看详情
    PLAYEND_APP_SCORE_CLICK = 27; // 成功点击播放结束页app评分
    PLAYEND_ACTION_BAR_SUSPENSION = 28; // 用户成功点击悬浮框下载按钮，android 下载类型

    // 通用卡片埋点需求
    CARD_ACTION_BAR_CLICK = 29; // 用户成功点击卡片上的actionbar跳转到落地页时即可触发
    CARD_ICON_CLICK = 30; // 用户成功点击卡片里的app头像跳转到落地页时即可触发
    CARD_ICON_NAME_CLICK = 31; // 用户成功点击卡片里的app名称跳转到落地页时即可触发
    CARD_ICON_COMMENT_CLICK = 32; // 用户成功点击卡片里的描述信息跳转到落地页时即可触发

    // 评论区Action Bar点击
    COMMENTS_TOP_ACTIONBAR_CLICK = 33; // 用户成功点击首屏评论区转化条触发上报
    COMMENTS_HANG_ACTIONBAR_CLICK = 34; // 用户成功点击评论区非首悬浮转化条触发上报

    BLANK_AREA_CLICK = 35; // 任意背景位点击

    TRY_PLAY_GAME_CLICK = 36; // 试玩页面的行为点击

    PLAY_END_CLICK1 = 37; // 联盟播放结束页双button--左侧点击按钮
    PLAY_END_CLICK2 = 38; // 联盟播放结束页双button-右侧点击按钮
    PLAY_END_CALL_CLICK = 39; // 联盟播放结束页点击号召按钮
    PLAY_DETAIL_CALL_CLICK = 40; // 联盟播放详情页击号召按钮

    PENDANT_ICON_CLICK = 41; // 直播挂件点击挂件icon
    PENDANT_BUTTON_CLICK = 42; // 直播挂件点击按钮
    PENDANT_OTHER_AREA_CLICK = 43; // 直播挂件点击其他区域

    PLAYEND_APP_DOWNLOAD_NUM = 44; // 播放结束页下载次数元素
    // 实际也是通用卡片埋点需求
    CARD_APP_SCORE_CLICK = 45; // 通用卡片评分元素
    CARD_APP_DOWNLOAD_NUM = 46; // 通用卡片下载次数元素

    // 增加媒体形态
    COMMENT_AREA_NAME_CLICK = 47; // 评论命名元素
    COMMENT_AREA_DETAIL_CLICK = 48; // 评论描述元素

    // 评论区描述
    COMMENT_AREA_ICON_CLICK = 49; // 评论头像因素

    // 点击中间页actionbar
    LANDING_PAGE_ACTION_BAR_CLICK = 50; // 点击中间页actionbar

    AD_WEAK_PATCH_AD_CLICK = 51 ; // 弱样式广告描述条点击

    CARD_ICON_TESTBOX_CLICK = 52 ; // 卡片文本框点击时上报
    CARD_ICON_OTHERAREA_CLICK = 53 ; // 卡片其他区域点击时上报
    CARD_ICON_MOBILE_CLICK = 54 ; // 点击手机号码输入框时触发

    PHOTO_PORTRAIT_CLICK = 55; // 点击视频详情页头像进入直播间时上报
    POPUP_WINDOW_CLICK = 56 ;  // 弹窗点击

    // Android（不含下载类）&iOS - 【双feed】多利益点卡片组件埋点
    CARD_PICTURE_CLICK_ONE = 57 ; // 样式1
    CARD_PICTURE_CLICK_TWO = 58 ; // 样式2
    CARD_PICTURE_CLICK_THREE = 59 ; // 样式3
    CARD_PICTURE_TITLE_CLICK_ONE = 60 ;
    CARD_PICTURE_TITLE_CLICK_TWO = 61 ;
    CARD_PICTURE_TITLE_CLICK_THREE = 62 ;
    CARD_PICTURE_SUBTITLE_CLICK_ONE = 63 ;
    CARD_PICTURE_SUBTITLE_CLICK_TWO = 64 ;
    CARD_PICTURE_SUBTITLE_CLICK_THREE = 65 ;

    RED_PACKET_AREACLICK = 66 ; // 红包区域元素
    PENDANT_NAME_CLICK = 67 ;  // 直播挂件点击名称描述
    BRAND_WALK_CLICK = 68 ; //品牌去逛逛点击
    BRAND_MORE_CLICK = 69 ; //品牌发现更多点击
    UP_SLIDE_FRONT_LANDING_PAGE_CLICK = 70; //上滑跳转前置落地页
    CARD_ICON_CLOSE_CLICK = 71 [deprecated=true];  //通用卡片关闭(废弃)
    ANY_AREA_CLICK = 72 ; // 播放页任意位置
    GUIDE_ICON_CLICK = 73 ; // 引导页侧边栏点击（任意位置），进入落地页
    GUIDE_LEFT_SLIDE_CLICK = 74 ; //引导页面场景左滑，进入落地页
    CARD_RANK_CLICK = 75 ; // 用户成功点击卡片上的评分，跳转到落地页时即可触发
    CARD_DOWNLOAD_CNT_CLICK = 76 ; // 用户成功点击卡片里的下载次数，跳转到落地页时即可触发
    CARD_TAG_CLICK =  77 ; // 用户成功点击卡片里的二级行业，跳转到落地页时即可触发
    CARD_LABEL_CLICK = 78 ; // 用户成功点击卡片里的标签跳转到落地页时即可触发

    ITEM_LABEL_CLICK = 79 ; // 视频播放页，广告标签点击
    ITEM_BULLET_CLICK = 80; // 弹幕点击；安卓：点击转化弹幕后跳转中间页；ios：跳转应用商店

    CONFIRM_DOWNLOAD_BOX_CONFIRMED = 81; // 弹出下载弹窗，点击确认时上报

    PHOTO_COMMENT_NAME_CLICK = 82; //封面作者名称点击
    PHOTO_ACTION_BAR_CLICK = 83; // 封面号召按钮点击

    CARD_RECO_CLICK = 84; //用户点击推荐理由
    CARD_PAGE_CLICK = 85; // 卡片弹窗点击
    PLAYEND_CARD_CLICK = 86; // 播放结束页卡片点击
    PLAYEND_PAGE_CLICK = 87; // 播放结束页点击
    PLAYEND_ICON_RECO_CLICK = 88; // 点击播放结束页推荐理由

    COMMON_CARD_CLICK = 89; // 通用卡片点击

    COMMENTS_HANG_NAME_CLICK = 90; // 用户成功点击评论区，非首(第一位)悬浮广告，命名元素点击时上报
    COMMENTS_HANG_DETAIL_CLICK = 91; // 用户成功点击评论区，非首(第一位)悬浮广告，描述元素点击时上报

    TRY_PLAYING_BUTTON_CLICK = 92; // 试玩过程转化button点击；
    TRY_PLAYED_BUTTON_CLICK = 93; // 试玩结束转化button点击；
    TRY_PLAYING_ANY_AREA_CLICK = 94; // 试玩过程任意区域转化点击
    TRY_PLAYED_ANY_AREA_CLICK = 95; // 试玩结束任意区域转化点击；

    SHARE_BUTTON_CLICK = 96; //点击分享跳转
    COMMENT_BUTTON_CLICK = 97; //点击评论跳转待定名字
    ALTERNATIVE_PAGE_ACTION_BAR_CLICK = 98; // 备选页ActionBar点击
    PLAYING_LANDINGPAGE_ACTION_BAR_CLICK = 99; // 播放过程落地页的action bar点击
    COMMENT_PICTURE_CLICK = 100; // 评论图片点击

    COMMENT_RECO_CLICK = 101; //点击推荐理由
    LANDING_PAGE_APP_AUTO_DOWNLOAD = 102; // 落地页内成功开始自动下载时上报
    AGG_PAGE_CLICK = 103; // 聚合页点击跳转
    PLAYEND_ICON_OTHERAREA_CLICK = 104; // 播放结束页卡片其他区域点击时上报

    LIVE_RESERVATION_CLICK = 105; //直播预约点击
    //20210112 add
    KWAI_ENTER_INTRODUCE_CLICK = 106; //快手入口引导元素点击，应用于联盟增长类广告

    LANDING_PAGE_TOP_ACTION_BAR_CLICK = 107; //点击落地页内顶部小actbar

    //20210304 add
    PHOTO_BLANK_AREA_CLICK = 108; //封面其他区域点击
    //20210307 add
    AUTO_ENTER_LANDING_PAGE = 109; //自动跳转落地页
    INTERACT_LANDING_PAGE_CLICK = 110; //互动落地页内行为点击
    INSTANT_TRY_WEEK_CLICK = 111;   // “即刻体验”弱样式按钮点击
    INSTANT_TRY_STRONG_CLICK = 112; // "即刻体验”强样式按钮点击
    MISOPERATION_CLICK = 113;   // 强卡右上角误触按钮点击
    SPLASH_SMALL_WINDOW_CLICK = 114; //开屏小窗点击

    //20210325 add
    ACTIONBAR_ICON_CLICK = 115; // 成功点击actbar里的app头像跳转到时即可触发
    ACTIONBAR_ICON_NAME_CLICK = 116; // 用户成功点击actbar里的app名称跳转时即可触发
    ACTIONBAR_ICON_COMMENT_CLICK = 117; // 用户成功点击actbar里的描述信息跳转时即可触发
    ACTIONBAR_ICON_OTHER_AREA_CLICK = 118 ; // actbar其他空白区域点击时上报

    //20210413 add
    POPUP_WINDOW_ICON_CLICK = 119; //弹窗头像点击
    POPUP_WINDOW_OTHERAREA_CLICK = 120; // 弹窗其他区域点击时上报

    //20210425 add
    PHOTO_PICTURE_CLICK = 121; //封面图片点击
    PHOTO_DETAIL_CLICK = 122; //封面描述点击
    RECO_DETAIL_CLICK = 123; //推荐广告的描述点击
    RECO_COMMENT_NAME_CLICK = 124; //推荐广告的作者名称点击
    RECO_PICTURE_CLICK = 125; //推荐广告的图片点击
    RECO_ACTION_BAR_CLICK = 126; //推荐广告的号召按钮点击
  
    //20210425
    CLOSE_BULLET_ICON_CLICK = 127;//点击图标
    CLOSE_BULLET_ICON_TITLE_CLICK = 128;//点击名称
    CLOSE_BULLET_ICON_DESCRIPTION_CLICK = 129;//点击广告语
    CLOSE_BULLET_OTHERAREA_CLICK = 130;//点击退出弹框白底区域（包含“观看多少秒。。”的文字区域
    CLOSE_BULLET_ACTION_BAR_CLICK = 131;//点击行为按钮时上报
    //20210510 add
    SPLASH_CONVERSION_BUTTON_CLICK = 132; //开屏转化按钮点击
    SPLASH_SKIPED_5S_CLICK = 133; //开屏跳过倒计时5秒点击
    SPLASH_SMALL_WINDOW_PICTURE_CLICK = 134; //开屏图片小窗点击
    //20210512 add
    POPUP_WINDOW_ICON_NAME_CLICK = 135; //弹窗应用名称点击时上报
    POPUP_WINDOW_ICON_COMMENT_CLICK = 136; // 弹窗应用描述点击时上报
    POPUP_WINDOW_ICON_DETAIL_CLICK = 137; // 弹窗了解详情点击时上报

    //搜索结果页 非action bar
    PORTRAIT_CLICK_SEARCH = 138; //点击搜索结果页作者头像跳转
    NAME_CLICK_SEARCH = 139;//点击搜索结果页作者昵称/名称跳转
    DETAIL_CLICK_SEARCH = 140;//点击搜索结果页广告描述区（除点击“更多”外部分）调整
    OTHER_AREA_CLICK_SEARCH = 141;//点击搜索结果页空白区域跳转

    //搜索结果页 action bar
    CARD_ACTION_BAR_CLICK_SEARCH = 142; // 用户成功点击卡片上的actionbar跳转即可触发
    CARD_ICON_CLICK_SEARCH = 143; // 用户成功点击卡片里的头像跳转时即可触发
    CARD_ICON_NAME_CLICK_SEARCH = 144; // 用户成功点击卡片里的app名称跳转时即可触发
    CARD_ICON_COMMENT_CLICK_SEARCH = 145; // 用户成功点击卡片里的描述信息跳转时即可触发
    CARD_ICON_OTHER_AREA_CLICK_SEARCH = 146; //用户成功点击卡片上其他空白区域点击时上报
    IOS_STOREKIT_AUTO_JUMP = 147; //ios storekit自动跳转
    FRONT_CARD_RENDER_SUCCESS_CLICK = 148; //渲染成功前卡点击
    FRONT_CARD_RENDER_FAIL_CLICK = 149; //渲染失败前卡点击

    CARD_LEFT_ACTION_BAR_CLICK = 150; //点击左边actionBar区域 上报
    // POPLAY 彩蛋弹窗
    POPLAY_PICTURE_CLICK = 151; // POPLAY 彩蛋弹窗，彩蛋图片点击
    POPLAY_WORD_CLICK = 152; // POPLAY 彩蛋弹窗，彩蛋文字点击
    //20210630 add
    FINGER_SWIPE_CLICK = 153; //手动滑动进入广告,用户在开屏广告上朝任意方向滑动都可以进入广告
    //20210714 add
    ONLINE_AUDIENCE_NUM = 154; //在线观众人数点击
    H5_LANDING_PAGE_AUTO = 155; //h5落地页自动跳转
    //20210722 add
    RETAIN_CARD_BUTTON = 156; //激励视频挽留弹窗按钮

    //20210825 ADD
    SHAKE_ICON_SHACK = 157;//用户成功摇一摇进入落地页
    SHAKE_BIG_ICON_CLICK = 158;//用户成功点击摇一摇区域进入落地页
    SHAKE_SMALL_ICON_CLICK = 159;//用户成功点击摇一摇区域进入落地页

    ITEM_CLICK_ACTION_OPEN_POPSHOW = 160; //打开全屏彩蛋播放页 废弃
    //20210908 add  扭一扭相关
    ROTATE_ICON_ROTATE = 161;  //用户扭一扭进入落地页
    ROTATE_BIG_ICON_CLICK = 162;  //用户成功点击扭一扭大挂件区域进入落地页
    ROTATE_SMALL_ICON_CLICK = 163; //用户成功点击扭一扭小挂件区域进入落地页
    //20210915  add 激励视频中小黄车
    TROLLEY_ICON_CLICK = 164; //小黄车的点击
    //20210922 add
    FINGER_SWIPE_CLICK_ACTIONBAR = 165; // 开屏广告滑动，滑动轨迹经过actionbar区域后进入广告，用户无感知
  //20211018 add 挽留弹窗点击
    CLOSE_BULLET_SECOND_STEP_ACTION_BAR_CLICK = 166; //点击继续二阶段按钮上报
  //20211105 add
    MARQUEE_COMMENT_CLICK = 167; //点击跑马灯文案跳转快手小店。
}
