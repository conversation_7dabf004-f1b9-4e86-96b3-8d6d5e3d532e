// copy from teams/ad/ad_proto/kuaishou/ad/ad_base.proto
syntax = "proto2";

package bs.kuaishou.ad;

message CommonTypeEnum {
    enum AttrType {
        UNKNOW_ATTR = 0;
        INT_ATTR = 1;
        FLOAT_ATTR = 2;
        STRING_ATTR = 3;
        INT_LIST_ATTR = 4;
        FLOAT_LIST_ATTR = 5;
        STRING_LIST_ATTR = 6;
        MAP_INT64_INT64_ATTR = 7;
        MAP_STRING_INT64_ATTR = 8;
        MAP_INT64_FLOAT_ATTR = 9;
        MAP_STRING_FLOAT_ATTR = 10;
        UINT64_ATTR = 11;
        UINT64_LIST_ATTR = 12;
        MAP_UNIT64_BOOL_ATTR = 13;
        MAP_UNIT64_UNIT64_ATTR = 14;
        MAP_INT64_MULTI_ATTR = 15;
        MAP_INT64_STRING_ATTR = 16;
    };
}
