// copy from teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.proto
syntax = "proto3";

package bs.kuaishou.ad.algorithm;
import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_item_click_type.proto";
import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_element_type.proto";
import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_style_sub.proto";

// 联盟客户端返回数据
message UniverseClientInfo {
    ItemClickType item_click_type = 1;
    ElementType element_type = 2;
    AdStyleSub ad_style_sub = 3;
}
