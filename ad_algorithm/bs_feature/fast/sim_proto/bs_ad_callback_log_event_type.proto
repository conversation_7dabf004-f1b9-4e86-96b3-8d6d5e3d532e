// copy from teams/ad/ad_proto/kuaishou/ad/ad_log.proto
syntax = "proto3";

package bs.kuaishou.ad;

option cc_enable_arenas = true;

message AdCallbackLog {
    enum EventType {
        EVENT_UNKNOWN = 0; // 部分老广告主的回传没有填写type，相当于归结到激活中！！！
        EVENT_CONVERSION = 1; // 激活
        EVENT_REGISTER = 2; // 注册
        EVENT_PAY = 3; // 付费
        EVENT_JS = 4; //js嵌入
        EVENT_ACTIVE = 5; //打开APP、被认为一次活跃的事件
        EVENT_HIGH_QUALITY = 6; //优质用户，优化模型使用
        EVENT_NEXTDAY_STAY = 7; //次日留存
        EVENT_WEEK_STAY = 8; //7日留存
        EVENT_FORM_SUBMIT = 9; //表单提交
        EVENT_JINJIAN = 10; //金融行业的进件完成
        EVENT_CREDIT_GRANT = 11; //金融行业的授信成功
        EVENT_GOODS_VIEW = 12; //电商的商品浏览行为
        EVENT_ADD_SHOPPINGCART = 13; //电商的添加购物车
        EVENT_ORDER_SUBMIT = 14; //电商的提交订单成功
        EVENT_ORDER_PAIED = 15; //订单付款成功
        EVENT_PAGE_IMPRESSION = 16; // 页面曝光，目前用于自建站
        EVENT_PAGE_LOAD = 17; // 加载成功，目前用于自建站
        EVENT_PAGE_CLOSE = 18; // 跳出，目前用于自建站
        EVENT_ERROR = 19; // 错误类型
        EVENT_NEW_ORDER = 20; // 新客下单（目前用于唯品会）
        EVENT_OLD_ORDER = 21; // 老客下单（目前用于唯品会）
        EVENT_PAGE_COMPONENT_CLICK = 22; // 组件点击
        EVENT_ECOM_DELIVERY = 23; // 金牛系统物流发货行为
        EVENT_ECOM_REFUSE_SIGN = 24; // 金牛系统物流拒签行为
        EVENT_ECOM_SIGN = 25; // 金牛系统物流签收行为
        EVENT_MAP_NAVIGATION_CLICK = 26; // 地图导航按钮点击
        EVENT_MAP_IMPRESSION = 27; // 地图选项展示，会带上曝光的地图名称，地图名称可能有多个，地图名称存放在impressioned_map字段中
        EVENT_MAP_CLICK = 28; // 地图选项点击，需要带上点击的地图名称，地图名称存放在clicked_map字段中
        EVENT_MAP_NAVIGATION_IMPRESSION = 29; //地图导航页曝光，需要带上页面id，页面id存放在lpPageId字段中，此页面id是上一个H5页面的page id
        EVENT_GAME_REPORT_DURATION = 30; // 上报游戏时长
        EVENT_GAME_CREATE_ROLE = 31; // 创建游戏角色
        EVENT_GAME_UPGRADE_ROLE = 32; // 升级游戏角色
        EVENT_GAME_WATCH_REWARD_VIDEO = 33; // 观看激励视频
        EVENT_GAME_CONSUMPTION = 34; // 游戏消费
        EVENT_ORDER_CANCEL = 35; // 取消订单，目前用于直营电商
        EVENT_BUTTON_CLICK = 36; // 按钮点击事件，用户成功点击按钮即可触发上报，不包含表单提交
        EVENT_LANDINGPAGE_STARTED_DOWNLOAD_CLICK = 37; // 开始下载按钮点击事件，用户成功点击自建站页面开始下载按钮，即触发上报
        EVENT_PAGE_LAUNCH_END = 38; // 金牛电商小程序，页面第一帧渲染完成
        EVENT_FACE_RECOGNITION_COMPLETED = 39; // 金融小贷行业，人脸识别完成，
        EVENT_ID_CARD_INFO_WRITE_COMPLETED = 40; // 金融小贷行业，身份证信息填写完成
        EVENT_BANK_CARD_INFO_WRITE_COMPLETED = 41; // 金融小贷行业，银行卡信息填写完成
        EVENT_SUPPLEMENT_PERSONAL_INFO_WRITE_COMPLETED = 42; // 金融小贷行业，补充个人信息填写完成
        EVENT_AUDITION = 43; // 教育行业，试听
        EVENT_VALID_CLUES = 44; // 有效线索
        EVENT_PAY_FIX = 45; // 模型侧修复归因的付费
        EVENT_SHOP_CLICK = 46; // 商品详情页，进入店铺按钮
        EVENT_PRODUCT_SKUBUY_CLICK = 47; // 商品详情页，SKU浮层购买
        EVENT_PRODUCT_BUY_CLICK = 48; // 商品详情页，立即购买
        EVENT_ORDER_IMPRESSION = 49; // 订单确认页，页面曝光
        EVENT_ORDER_SUBMIT_CLICK = 50; // 订单确认页，提交订单，按钮点击
        EVENT_PAY_IMPRESSION = 51; // 支付页曝光
        EVENT_FORM_APPROVED = 52; // 表单提交后的返回请求
        AD_MERCHANT_FOLLOW = 53; // 小店通涨粉，从前链路切换过来，命名与之前一致～
        EVENT_FORM_SUBMIT_SUCCESS = 54; // submit完成后，后端的res如果是success,则上报此埋点
        EVENT_FORM_SUBMIT_FAIL = 55; // submit完成后，后端的res如果是failure,则上报此埋点
        EVENT_FORM_SUBMIT_REQ_SUCCESS = 56; // submit时调用wx.request触发状态钩子时埋点， 如果返回success, 则上报此埋点
        EVENT_FORM_SUBMIT_REQ_FAIL = 57; // submit时调用wx.request触发状态钩子时埋点， 如果返回failure, 则上报此埋点
        EVENT_FORM_SUBMIT_REQ_COMPLETE = 58; // 有submit，就会有上报

        EVENT_RELEASE_CLICK = 59; // 落地页-模板组件，发布按钮点击，触发的发布事件
        EVENT_CUSTOMER_SERVICE_CLICK = 60; // 客服组件-按钮点击；用户成功点击客户组件按钮，即触发上报
        EVENT_PAGE_CLOSE_FIRST_SCREEN = 61; // 客服咨询页首屏跳出；页面加载完成后，未点击客服咨询按钮则跳出
        EVENT_CONVERSION_CLICK = 62; // 客服咨询页-客户对话按钮点击；用户点击客户并触发消息发送成功，即上报

        EVENT_MAKING_CALLS = 63; // 自建站，打电话事件
        EVENT_ADD_WECHAT = 64; // 自建站，加微信号事件
        EVENT_PAY_UNION = 65; // 联盟ROI变现使用

        EVENT_CONFIRM_RECEIPT = 66; // 确认收货
        EVENT_PAID_REFUND = 67; // 已支付退款
        EVENT_REFUND_ON_RECEIPT = 68; //已收货退款

        EVENT_HIGH_PRICE_CLASS_PAY = 69; // 教育行业，正价课付费

        EVENT_WAKE_APP_FAIL = 70; // 唤醒app失败，比如淘宝
        EVENT_GOTO_TAOBAO = 71; // "去淘宝"事件,用户点击后唤起淘宝
        EVENT_APPOINT_DOWNLOAD = 72; //预约下载事件
        EVENT_POPUP_WINDOW_CLICK = 73; //淘系中间页弹窗点击事件
        EVENT_POPUP_WINDOW_OPENED = 74; //淘系中间页弹窗点击打开事件
        EVENT_DRAW_CREDIT_LINE = 75; //金融下小额贷款二级行业，用信事件，即实际发生支取行为
        EVENT_GOTO_APPSTORE = 76; // 跳转至app store下载
        EVENT_FORM_SUBMIT_GENUINE = 77; // 当组件为真正表单提交时上报；跳转成功时上报
        EVENT_APPOINT_FORM = 78; // 预约表单
        EVENT_APPOINT_JUMP_CLICK = 79; // 预约跳转点击
        EVENT_ITEM_CLICK = 80; // fake item click for recall

        EVENT_MAKING_SMART_PHONES = 81; //点击拨打智能电话
        EVENT_OUTBOUND_CALL = 82; //电话呼出，拨打智能电话
        EVENT_GET_THROUGH = 83; //电话接通，接通智能电话
        EVENT_APP_INVOKED = 84; //唤醒APP
        EVENT_WATCH_APP_AD = 85; //广告观看事件APP内

        EVENT_PAGE_BACKGROUND_CLICK = 86; // 淘系长中间页，背景图点击
        EVENT_CONSULTATION_DISCERN_RETAINED = 87; // 自建站，咨询组件识别留咨
        EVENT_CONSULTATION_VALID_RETAINED = 88; // 自建站，咨询组件有效留咨

        EVENT_AD_WATCH_TIMES = 89; // opcx优化目标：广告观看次数
        EVENT_MULTI_CONVERSION = 90; // opcx优化目标：多转化事件
        EVENT_OPEN_WECHAT = 91; // 微信调起
        EVENT_PAY_FIRST_DAY = 92; // 首日付费：产生激活、表单，当天发生付费行为
        EVENT_ORDER_PAYED_SERVER_SHOW = 93 [deprecated = true]; // 涨粉链路带来的成交
        EVENT_ORDER_SUBMIT_SERVER_SHOW = 94 [deprecated = true]; // 涨粉链路带来的订单提交
        EVENT_ORDER_PAYED_SERVER_FOLLOW = 95; // 涨粉链路带来的成交
        EVENT_ORDER_SUBMIT_SERVER_FOLLOW = 96; // 涨粉链路带来的订单提交
        EVENT_POPUP_WINDOW_IMPRESSION = 97; // 弹窗曝光，区分弹窗类型，通过 LpPopupWindowType
        EVENT_POPUP_WINDOW_CLOSE = 98; // 弹窗点击，区分弹窗类型，通过 LpPopupWindowType
        EVENT_ORDER_WRITEOFF = 99; // 订单核销
        EVENT_ORDER_PAYED_INDIRECT = 100; //间接链路（点击）带来的成交

        EVENT_2_DAY_STAY = 102; //2日留存
        EVENT_3_DAY_STAY = 103; //3日留存
        EVENT_4_DAY_STAY = 104; //4日留存
        EVENT_5_DAY_STAY = 105; //5日留存
        EVENT_6_DAY_STAY = 106; //6日留存
        EVENT_14_DAY_STAY = 107; //14日留存
        EVENT_30_DAY_STAY = 108; //30日留存

        EVENT_PHONE_CARD_ACTIVATE = 109; // IT消电行业，电话卡激活；表单提交->客户寄出电话卡->用户验收->用户激活电话卡
        EVENT_AD_WATCH_5_TIMES = 110; // 广告观看，累计到5次
        EVENT_AD_WATCH_10_TIMES = 111; // 广告观看，累计到10次
        EVENT_AD_WATCH_20_TIMES = 112; // 广告观看，累计到20次

        EVENT_CLICK_WECHAT = 113; //微信点击（若弹窗样式选择「长按复制弹窗」，在页面中点击微信组件时上报此打点）
        EVENT_VIEW_WECHAT = 114; //用户点击微信组件后，成功弹出弹框时上报
        EVENT_COPY_WECHAT = 115; //长按微信号（触发选中状态）上报

        EVENT_PHONE_GET_THROUGH = 116; // 营销链路中，电话建联成功，区别于智能电话拨通；eg.招商加盟、家居建材、生活服务、教育
        EVENT_INTENTION_CONFIRMED = 117; // 营销链路中，意向确认；eg.招商加盟、家居建材、生活服务、教育
        EVENT_WECHAT_CONNECTED = 118; // 营销链路中，微信加粉；eg.招商加盟、家居建材、生活服务、教育
        EVENT_ORDER_SUCCESSED = 119; // 营销链路中，成交；eg.招商加盟、家居建材、IT消电
        EVENT_PHONE_NO_GET_THROUGH = 120; // 营销链路中，电话建联失败，需回传失败原因 action_reason
        EVENT_NO_INTENTION = 121; // 营销链路中，用户无意向，需回传无意向原因 action_reason
        EVENT_TERMINATION = 122; // 解约；eg.金融-保险
        EVENT_M2_RENEWAL_INSURANCE = 123; // 第2个月续费保险，且扣费成功；eg.金融-保险
        EVENT_M3_RENEWAL_INSURANCE = 124; // 第3个月续费保险，且扣费成功；eg.金融-保险
        EVENT_HIGH_VALUE_CLUE = 125; // 高价值线索，由意向确认+微信加粉+成交衍生而来

        EVENT_LIVE_INTERACTION = 126; // ecpx优化目标：互动
        EVENT_LIVE_REWARD = 127; // ecpx优化目标：打赏
        EVENT_LIVE_COMMODITY_PURCHASE = 128; // ecpx优化目标：商品购买

        EVENT_PASS_KEY_GAME_CARD = 129; // SDK游戏回传中，通过游戏关键卡点。目前为首次激活后，第一个卡点
        EVENT_VIP_LEVEL_UP = 130; // SDK游戏回传中，VIP等级提升，附带回传vipLevel参数

        EVENT_CREDIT_CARD_JINJIAN = 131; //金融-信用卡,信用卡完件：用户填写完所有的H5表单，大概3-4页
        EVENT_CREDIT_CARD_PRESCREEN = 132; //金融-信用卡,信用卡初筛：银行初步审核资质和信息的真实性，银行可对接回传
        EVENT_CREDIT_CARD_RECHECK = 133; //金融-信用卡,信用卡核卡：银行资质审核通过，且给了额度，银行可对接回传
        EVENT_CREDIT_CARD_ACTIVE = 134; //金融-信用卡,信用卡激活：银行网点面签激活，银行可对接回传
        EVENT_CREDIT_CARD_EXPENSE = 135; //金融-信用卡,信用卡动账：刷卡消费,银行可对接回传

        EVENT_FRONT_MONEY = 136; //超深度转化目标，针对客户三亚菲林、业之峰，新增用户交定金事件
        EVENT_MEASUREMENT_HOUSE = 137; //超深度转化目标，针对客户三亚菲林、业之峰，新增用户量房事件
        EVENT_WECHAT_QR_CODE_LINK_CLICK = 138; // 跳转到小程序加微信场景下：微信二维码链接点击

        EVENT_FIRST_LESSON_FINISH = 139; // 教育行业，首课完课
        EVENT_ENTER_SECOND_AUDITION = 140; // 教育行业，第2次试听课到课
        EVENT_ALL_LESSON_FINISH = 141; // 教育行业，全部试听完课

        EVENT_DEEPLINK_INVOKED = 142; // 落地页唤端：引导到端内打开h5或跳出唤起其他app

        EVENT_KEY_INAPP_ACTION = 143; // 客户回传的关键行为。如：广告观看、LTV、时长等。
        EVENT_CLEAR_GAME_STAGE = 144; // 通过游戏关卡
        EVENT_WITHDRAW_CASH = 145; // 提现
        EVENT_PAY_SEVEN_DAY = 146; // 7日内付费。激活、表单发生后，七日内的产生付费时衍生。
        EVENT_FOLLOW_WECHAT_OFFICIAL_ACCOUNT = 147; // 关注微信公众号

        EVENT_ENTER_WECHAT_GROUP = 148; // 进入微信群。eg:正价课场景
        EVENT_CLICK_PAYMENT_BUTTON = 149; // 付费按钮点击。eg:建站付费表单
        EVENT_READ_NOVEL_CHAPTER = 150; // 阅读小说章节
        EVENT_R_PAY = 151; // R付费。对应小说行业，广告主自主划分客户群，大、中、小R付费级别，对应参数pay_level（S/M/L）

        EVENT_PASSENGER_FIRST_ORDER = 152; // 出行行业，乘客首次发单
        EVENT_PASSENGER_FIRST_COMPLETE_ORDER = 153; // 出行行业，乘客首次完单
        EVENT_DRIVER_SUBMIT_CERTIFICATION = 154; // 出行行业，车主提交认证
        EVENT_DRIVER_PASS_CERTIFICATION = 155; // 出行行业，车主认证通过
        EVENT_DRIVER_FIRST_COMPLETE_ORDER = 156; // 出行行业，车主首次完单

        EVENT_EXT_PREDICT_VALUE = 157; // 外部预估值回传。eg:小说回传第三方的预估付费率等
        EVENT_SUPPLEMENT_CONVERSION = 158; //补充激活事件
        EVENT_ORDER_TRAFFIC_PACKAGE = 159; // 本地行业，订购流量包
        EVENT_EXPERIENTIAL_CONSUMPTION = 160; // 本地行业，体验消费 eg:咨询行业
        EVENT_REGULAR_CONSUMPTION = 161; // 本地行业，正价消费 eg:咨询行业
        EVENT_GET_COUPONS = 162; // 本地行业，用户领券
        EVENT_BUILD_CARD = 163; // 汽车行业，建卡：线索入到客户的CRM线索库
        EVENT_SET_SCHEDULE = 164; // 汽车行业，排程：给用户安排行程线下到店
        EVENT_GO_SHOP = 165; // 汽车行业，到店：用户真实到店
        EVENT_TEST_DRIVE = 166; // 汽车行业，试驾
        EVENT_PAY_SMALL_DEPOSIT = 167; // 汽车行业，小订（定金支付）用户进行定金支付
        EVENT_PAY_FULL_AMOUNT = 168; // 汽车行业，大订（全款支付）用户全款支付
        EVENT_ACTIVITY_VISIT = 169; // 其他行业，活动访问
        EVENT_ACTIVITY_PARTICIPATION = 170; // 其他行业，活动参与
        EVENT_ACTIVITY_RE_PARTICIPATION = 171; // 其他行业，用户复参活动
        EVENT_SEND_COUPONS = 172; // 其他行业，活动发券
        EVENT_REAL_NAME_AUTHENTICATION = 173; // 社交行业，实名认证
        EVENT_CHAT = 174; // 社交行业，聊天
        EVENT_INTO_THE_ROOM = 175; // 社交行业，直播进房
        EVENT_GAME_ECPM = 176; // IAA行业，广告观看ECPM
        EVENT_GAME_TIME = 177; // 游戏行业，使用时长

        EVENT_ORDER_PAID_FOLLOW = 180; // 涨粉gmv
        EVENT_24H_STAY = 181; // 24h口径次留
        EVENT_SERVER_CONNECTION = 182; // 游戏行业, 用户进入游戏服务器
        EVENT_FORM_SUBMIT_FILL = 183; // 自建站, 表单有输入行为
        EVENT_CONVERSION_1SENTENCE_CLICK = 184; // 自建站, 客服咨询页-客户产生第一句会话
        EVENT_ELEMENT_NAVIGATE_CLICK = 185; // 自建站, 门店组件中导航元素的点击
        EVENT_ELEMENT_PHONE_CLICK = 186; // 自建站, 门店组件中电话元素的点击
        EVENT_COUPONS_CLICK = 187;  // 自建站, 卡券组件的点击
        EVENT_ELEMENT_COUPONS_CLICK = 188;  // 自建站, 成功领取元素点击
        EVENT_ANCHORED_SUCCESS = 189; // 互动弹窗, 锚定成功

        // IAA衍生行为
        EVENT_IAA_AD_WATCH = 190; // 广告观看
        EVENT_IAA_AD_ECPM = 191; // 广告观看+ECPM
        EVENT_IAA_AD_ARPU = 192; // 广告观看+ARPU/LTV
        EVENT_IAA_AD_CASH = 193; // 广告观看+用户提现
        EVENT_IAA_AD_ECPM_CASH_STAGE = 194; // 广告观看+ECPM+关卡+活跃次数
        EVENT_IAA_AD_ACTION = 195; // 全部广告观看，含图文、视频
        EVENT_IAA_ACTIVE_ACTION = 196; // 活跃类行为
        EVENT_IAA_WATCH_ACTION = 197; // 视频类广告行为
        EVENT_IAA_MONETIZE_ACTION = 198; // 变现类行为
        EVENT_IAA_KEY_ACTION = 199; // 其他衍生定义

        EVENT_GOODS_VIEW_V2 = 1001 [deprecated = true]; // 废弃，临时，小店上报逻辑切换使用～～
        EVENT_ORDER_PAIED_BY_FOLLOW = 1002; // 下游模型使用，与打点、归因无关
    };
}
