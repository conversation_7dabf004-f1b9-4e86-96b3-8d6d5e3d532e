// copy from teams/ad/ad_proto/kuaishou/ad/ad_base.proto
syntax = "proto2";

package bs.kuaishou.ad;

import "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_common_type_enum.proto";

message LabelInfoCommonAttr {
    enum Name {
        UNKNOW_NAME = 0;
        RANK_BENEFIT = 1;
        CPM_BONUS = 2;
        RAW_CPM = 3;
        LANDINGPAGE_SUBMIT_RATE = 4;
        APP_CONVERSION_RATE = 5;
        SERVER_SHOW_CTR = 6;
        PLAY3S = 7;
        NTR = 8;
        CTR = 9;
        CVR = 10;
        PLAYTIME_AD = 11;
        RETRIEVAL_TAG = 12;
        PRERANK_TAG = 13;
        UNIVERSE_EXPLORE_TAG = 14;
        SOFTMAX_CPM = 15;
        SERVER_SHOW_CVR = 16;
        EXPLORE_EXP_TAG = 17;
        LTR_LEVEL_VAL_GJW = 18;
        QUALITY_POS_POST_CPM = 19; // 流量质量 pos 后验 cpm
        QUALITY_PV_CPM = 20; // 流量质量 pv cpm
        CPM_INDEX = 21;
        UNIVERSE_PRERANK_EXPLORE_TAG = 22;

        PRED_CTR_SUM = 1001; // 深度 label match 使用，从 ad_log_for_algo 透传， float 类型
        PRED_CVR_SUM = 1002; // 深度 label match 使用，从 ad_log_for_algo 透传， float 类型
        DELIVERY_TIME = 1003; // 深度 label match 使用，下发时间, int 类型
        BACKEND_LABEL_MATCH_PREDICT_TIME = 1004; // 深度 label match 使用，预估拼接时间， int 类型
        CONVERSION_TIME = 1005; // 深度 label match 使用，真实的转化时间， int 类型
        BACKEND_LABEL_MATCH_CALIBRATION_TAG = 1006; // 深度 label match 使用，标识是否为补偿流， bool 类型
        
        PRERANK_ECPM_WEIGHT = 1007; // 粗排多路权重， float 类型
        PRERANK_ECPM_SCORE_WEIGHT = 1008; // 粗排多路权重， float 类型
        PRERANK_ECPM_SCORE_IDX_WEIGHT = 1009; // 粗排多路权重， float 类型
        PRERANK_E2E_ENSEMBLE_WEIGHT = 1010; // 粗排多路权重， float 类型
        PRERANK_E2E_SCORE_WEIGHT = 1011; // 粗排多路权重， float 类型
        PRERANK_E2E_SCORE_IDX_WEIGHT = 1012; // 粗排多路权重， float 类型
        PRERANK_CPM_LTR_ENSEMBLE_WEIGHT = 1013; // 粗排多路权重， float 类型
        PRERANK_CPM_LTR_SCORE_WEIGHT = 1014; // 粗排多路权重， float 类型
        PRERANK_CPM_LTR_SCORE_IDX_WEIGHT = 1015; // 粗排多路权重， float 类型
    };

    optional CommonTypeEnum.AttrType type = 1;
    optional int64 name_value = 2;
    optional int64 int_value = 3;
    optional float float_value = 4;
    optional bool bool_value = 5;
}
