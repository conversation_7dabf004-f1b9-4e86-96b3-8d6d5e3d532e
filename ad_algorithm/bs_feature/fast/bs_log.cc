#include "ks/reco_proto/proto/reco.pb.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/bs_log.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_feature.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_full_goods_action.pb.h"
#include "teams/ad/picasso/sdk/proto/kv.pb.h"

namespace ks {
namespace ad_algorithm {

BSLog::BSLog() {
  ArenaOptions arena_option;
  arena_option.start_block_size = FLAGS_arena_start_block;
  pb_arena_ = std::make_shared<Arena>(arena_option);
}

BSLog::BSLog(const ad_nn::SampleInterface* sample) : sample_(sample) {}

BSLog::~BSLog() {}

uint64_t BSLog::llsid() const {
  if (sample_ != nullptr) {
    return sample_->primary_key();
  } else {
    return 0;
  }
}

uint64_t BSLog::item_id(int index) const {
  if (sample_ != nullptr) {
    return ad_nn::BSFieldHelper::GetSingular<uint64_t>(
        *sample_, ks::ad_nn::GetItemMainId(main_type_), index);
  } else {
    return 0;
  }
}

const ::bs::kuaishou::ad::AdCallbackLog::EventType BSLog::get_ad_callback_event(
    int pos) const {
  if (pos < 0 || pos >= ad_callback_events_size()) {
    LOG_EVERY_N(INFO, 800000) << "get callback_events index overflow:" << pos
                              << ", event size: " << ad_callback_events_size();
    return ::bs::kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN;
  }
  return ad_callback_events_[pos];
}

void BSLog::add_ad_callback_event(
    ::bs::kuaishou::ad::AdCallbackLog::EventType callback_event) {
  ad_callback_events_.push_back(callback_event);
}

void BSLog::reserve_ad_callback_event(size_t size) { ad_callback_events_.reserve(size); }

int BSLog::ad_callback_events_size() const { return ad_callback_events_.size(); }

bool BSLog::has_user_info() const {
  if (sample_ != nullptr) {
    return ad_nn::BSFieldHelper::GetSingular<bool>(
        *sample_, ad_nn::BSFieldEnum::adlog_user_info_exists, 0);
  } else {
    return false;
  }
}

bool BSLog::has_context() const {
  if (sample_ != nullptr) {
    return ad_nn::BSFieldHelper::GetSingular<bool>(
        *sample_, ad_nn::BSFieldEnum::adlog_context_exists, 0);
  } else {
    return false;
  }
}

int BSLog::item_size() const {
  if (sample_ != nullptr) {
    return sample_->total_sample_len();
  } else {
    return 0;
  }
}

uint64_t BSLog::timestamp() const {
  return ad_nn::BSFieldHelper::GetSingular<uint64_t>(*sample_,
                                                     ad_nn::BSFieldEnum::adlog_time, 0);
}

const ks::reco::UserInfo* BSLog::reco_user_info() const { return reco_user_info_; }

ks::reco::UserInfo* BSLog::mutable_reco_user_info() {
  if (reco_user_info_ == nullptr) {
    reco_user_info_ = CreateMessage<ks::reco::UserInfo>();
  }
  return reco_user_info_;
}

IMPLEMENT_PROCESSOR_WITH_ARRENA(::bs::kuaishou::ad::algorithm::AdUserAdActionMap,
                                ad_user_ad_action_map, BSLog);

IMPLEMENT_PROCESSOR(::bs::kuaishou::ad::algorithm::AllGoods, ad_user_all_goods_action,
                    BSLog);
IMPLEMENT_PROCESSOR(xlib::picasso::pb::kv::HMapListAppendReq, h_maplist_append_req,
                    BSLog);
IMPLEMENT_PROCESSOR(std::vector<colossus::AdLiveItemT>, colossus_ad_live_item, BSLog);
IMPLEMENT_PROCESSOR(std::vector<colossus::LiveItemV2T>, colossus_reco_live_item, BSLog);
IMPLEMENT_PROCESSOR(std::vector<colossus::LiveItemT>, colossus_reco_live_v1_item, BSLog);
IMPLEMENT_PROCESSOR(std::vector<colossus::AdGoodsItemT>, colossus_ad_goods_item, BSLog);
IMPLEMENT_PROCESSOR(std::vector<colossus::AdGoodsItemNewT>, colossus_ad_goods_item_new,
                    BSLog);
IMPLEMENT_PROCESSOR(std::vector<colossus::AdGoodsItemNewT>,
                    colossus_ad_goods_item_new_pdn_v0, BSLog);
IMPLEMENT_PROCESSOR(std::vector<colossus::SimItemV2T>, colossus_reco_photo, BSLog);
IMPLEMENT_PROCESSOR(std::vector<colossus::SimItemV3T>, colossus_reco_photo_v3, BSLog);
IMPLEMENT_PROCESSOR(std::vector<colossus::SimItemV3T>, colossus_reco_photo_v3_pdn_v0,
                    BSLog);
IMPLEMENT_PROCESSOR(StringIntMap, colossus_ad_live_spu, BSLog);
IMPLEMENT_PROCESSOR(StringIntMap, colossus_ad_live_cid3, BSLog);
IMPLEMENT_PROCESSOR(IntIntMap, author_cluster_map, BSLog);
IMPLEMENT_PROCESSOR(IntIntMap, live_cluster_map, BSLog);
IMPLEMENT_PROCESSOR(IntIntListMap, ad_live_global_gsu_result, BSLog);
IMPLEMENT_PROCESSOR(IntIntListMap, ad_live_ecomm_gsu_result, BSLog);
IMPLEMENT_PROCESSOR(IntIntListMap, ad_live_author_gsu_result, BSLog);
IMPLEMENT_PROCESSOR(IntIntListMap, ad_live_author_cluster_gsu_result, BSLog);
IMPLEMENT_PROCESSOR(IntIntListMap, ad_live_spu_gsu_result, BSLog);
IMPLEMENT_PROCESSOR(IntIntListMap, ad_live_cid3_gsu_result, BSLog);
IMPLEMENT_PROCESSOR(IntIntListMap, ad_live_remote_cluster_gsu_result, BSLog);
IMPLEMENT_PROCESSOR(IntIntListMap, reco_live_global_gsu_result, BSLog);
IMPLEMENT_PROCESSOR(IntIntListMap, reco_live_author_gsu_result, BSLog);
IMPLEMENT_PROCESSOR(IntIntListMap, reco_live_author_cluster_gsu_result, BSLog);
IMPLEMENT_PROCESSOR(IntIntListMap, reco_live_remote_cluster_gsu_result, BSLog);
IMPLEMENT_PROCESSOR(IntIntListMap, reco_live_v1_remote_cluster_gsu_result, BSLog);
IMPLEMENT_PROCESSOR(std::vector<int>, ad_live_colossus_idx_filtered_by_playtime, BSLog);
IMPLEMENT_PROCESSOR(std::vector<int>, ad_live_colossus_idx_filtered_by_label, BSLog);
IMPLEMENT_PROCESSOR(std::vector<int>, reco_live_colossus_idx_filtered_by_playtime, BSLog);
IMPLEMENT_PROCESSOR(std::vector<std::vector<float>>, ad_user_history_photo_embedding,
                    BSLog);
IMPLEMENT_PROCESSOR(std::vector<std::vector<float>>, ad_user_history_pdct_embedding,
                    BSLog);
IMPLEMENT_PROCESSOR(std::vector<int64_t>, ad_user_histactpdct_pdct, BSLog);
IMPLEMENT_PROCESSOR(std::vector<int64_t>, ad_user_histactpdct_firstid, BSLog);
IMPLEMENT_PROCESSOR(std::vector<int64_t>, ad_user_histactpdct_secondid, BSLog);
IMPLEMENT_PROCESSOR(std::vector<float>, ad_user_histactpdct_u2uemb, BSLog);
IMPLEMENT_PROCESSOR(std::string, ad_user_histact_weight, BSLog);
IMPLEMENT_PROCESSOR(std::vector<int64_t>, ad_user_histact_type, BSLog);
IMPLEMENT_PROCESSOR(std::vector<ks::ad_picasso::sdk::AdGoodsItemT>, picasso_ad_goods_item,
                    BSLog);
// ad_live_offline_sample_set 仅用于离线样本过滤
IMPLEMENT_PROCESSOR(std::unordered_set<std::string>, ad_live_offline_sample_set, BSLog);

}  // namespace ad_algorithm
}  // namespace ks
