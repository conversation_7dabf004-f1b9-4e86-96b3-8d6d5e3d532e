#pragma once
#include <map>
#include <string>
#include <memory>
#include <vector>
#include <utility>
#include "teams/ad/ad_algorithm/feature_interface/redis_wrapper_mmf.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_multi_model_feature.pb.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/feature_interface/tool.h"

using kuaishou::ad::algorithm::MultiModelFeatureMap;

namespace ks {
namespace ad_algorithm {

template <FeaturePrefix prefix>
class BSExtractDenseMmfEmbedding : public BSFastFeature {
 public:
  BSExtractDenseMmfEmbedding() : BSFastFeature(FeatureType::DENSE_ITEM) {}

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult> *result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_value = false;
    uint64_t ad_photo_id = bs_util.BSGetPhotoInfoId(bs, pos, &has_value);
    if (!bs_util.BSHasPhotoInfo(bs, pos)) {
      return;
    }

    std::string key = "emb" + std::to_string(ad_photo_id);
    ks::infra::RedisErrorCode ret = ks::infra::RedisErrorCode::KS_INF_REDIS_ERR_UNKNOWN;
    int l_retry = 3;

    std::map<std::string, MultiModelFeatureMap> res_map;
    ret = RedisWrapperMmf::GetInstance().HashGetAllFieldsWithScan(key, res_map, l_retry);
    if (ret == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
      auto it = res_map.find(std::to_string(prefix));
      if (it != res_map.end()) {
        const MultiModelFeatureMap& ad_mmf_map = it->second;
        // watch out.
        if (ad_mmf_map.mmf_value().size() == ad_mmf_map.mmf_size()) {
          result->resize(ad_mmf_map.mmf_size());
          for (int i = 0; i < ad_mmf_map.mmf_value().size(); ++i) {
            (*result)[i] = std::move(ExtractResult(i, static_cast<float>(ad_mmf_map.mmf_value(i))));
          }
        }
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractDenseMmfEmbedding);
};

using BSExtractDenseMmfEmbedding1 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_1>;
using BSExtractDenseMmfEmbedding2 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_2>;
using BSExtractDenseMmfEmbedding3 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_3>;
using BSExtractDenseMmfEmbedding4 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_4>;
using BSExtractDenseMmfEmbedding5 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_5>;
using BSExtractDenseMmfEmbedding6 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_6>;
using BSExtractDenseMmfEmbedding7 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_7>;
using BSExtractDenseMmfEmbedding8 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_8>;
using BSExtractDenseMmfEmbedding9 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_9>;
using BSExtractDenseMmfEmbedding10 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_10>;
using BSExtractDenseMmfEmbedding11 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_11>;
using BSExtractDenseMmfEmbedding12 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_12>;
using BSExtractDenseMmfEmbedding13 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_13>;
using BSExtractDenseMmfEmbedding14 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_14>;
using BSExtractDenseMmfEmbedding15 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_15>;
using BSExtractDenseMmfEmbedding16 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_16>;
using BSExtractDenseMmfEmbedding17 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_17>;
using BSExtractDenseMmfEmbedding18 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_18>;
using BSExtractDenseMmfEmbedding19 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_19>;
using BSExtractDenseMmfEmbedding20 = BSExtractDenseMmfEmbedding<FeaturePrefix::AD_MMF_DENSE_20>;

REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding1);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding2);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding3);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding4);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding5);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding6);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding7);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding8);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding9);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding10);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding11);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding12);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding13);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding14);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding15);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding16);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding17);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding18);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding19);
REGISTER_BS_EXTRACTOR(BSExtractDenseMmfEmbedding20);

}  // namespace ad_algorithm
}  // namespace ks
