#pragma once
#include <map>
#include <string>
#include <memory>
#include <vector>
#include <utility>
#include "teams/ad/ad_algorithm/feature_interface/redis_wrapper_mmf.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_multi_model_feature.pb.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/feature_interface/tool.h"

using kuaishou::ad::algorithm::MultiModelFeatureMap;

namespace ks {
namespace ad_algorithm {

template <FeaturePrefix prefix>
class BSExtractAdMmfIdList : public BSFastFeature {
 public:
  BSExtractAdMmfIdList() : BSFastFeature(FeatureType::ITEM) {}

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult> *result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_value = false;
    uint64_t ad_photo_id = bs_util.BSGetPhotoInfoId(bs, pos, &has_value);
    if (!bs_util.BSHasPhotoInfo(bs, pos)) {
      return;
    }

    std::string key = "id" + std::to_string(ad_photo_id);
    ks::infra::RedisErrorCode ret = ks::infra::RedisErrorCode::KS_INF_REDIS_ERR_UNKNOWN;
    int l_retry = 3;

    std::map<std::string, MultiModelFeatureMap> res_map;
    ret = RedisWrapperMmf::GetInstance().HashGetAllFieldsWithScan(key, res_map, l_retry);
    if (ret == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
      auto it = res_map.find(std::to_string(prefix));
      if (it != res_map.end()) {
        const MultiModelFeatureMap& ad_mmf_map = it->second;
        if (ad_mmf_map.mmf_id().size() == ad_mmf_map.mmf_size()) {
          result->resize(ad_mmf_map.mmf_size());
          for (int i = 0; i < ad_mmf_map.mmf_id().size(); ++i) {
            (*result)[i] = std::move(ExtractResult(GetFeature(prefix, ad_mmf_map.mmf_id().Get(i))));
          }
        }
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdMmfIdList);
};

using BSExtractAdMmfIdList1 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_1>;
using BSExtractAdMmfIdList2 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_2>;
using BSExtractAdMmfIdList3 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_3>;
using BSExtractAdMmfIdList4 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_4>;
using BSExtractAdMmfIdList5 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_5>;
using BSExtractAdMmfIdList6 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_6>;
using BSExtractAdMmfIdList7 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_7>;
using BSExtractAdMmfIdList8 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_8>;
using BSExtractAdMmfIdList9 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_9>;
using BSExtractAdMmfIdList10 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_10>;
using BSExtractAdMmfIdList11 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_11>;
using BSExtractAdMmfIdList12 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_12>;
using BSExtractAdMmfIdList13 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_13>;
using BSExtractAdMmfIdList14 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_14>;
using BSExtractAdMmfIdList15 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_15>;
using BSExtractAdMmfIdList16 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_16>;
using BSExtractAdMmfIdList17 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_17>;
using BSExtractAdMmfIdList18 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_18>;
using BSExtractAdMmfIdList19 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_19>;
using BSExtractAdMmfIdList20 = BSExtractAdMmfIdList<FeaturePrefix::AD_MMF_SPARSE_ID_20>;


REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList1);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList2);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList3);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList4);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList5);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList6);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList7);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList8);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList9);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList10);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList11);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList12);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList13);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList14);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList15);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList16);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList17);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList18);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList19);
REGISTER_BS_EXTRACTOR(BSExtractAdMmfIdList20);

}  // namespace ad_algorithm
}  // namespace ks
