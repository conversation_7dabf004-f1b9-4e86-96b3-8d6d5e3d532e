#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAuthorRealtimeGmvP10BoolMask : public BSFastFeature {
 public:
  BSExtractAuthorRealtimeGmvP10BoolMask();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorRealtimeGmvP10BoolMask);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorRealtimeGmvP10BoolMask);

}  // namespace ad_algorithm
}  // namespace ks
