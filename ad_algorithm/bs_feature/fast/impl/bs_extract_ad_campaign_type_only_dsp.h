#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdCampaignTypeOnlyDsp : public BSFastFeature {
 public:
  BSExtractAdCampaignTypeOnlyDsp() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);

    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info) {
      auto campaign_type = BSFieldHelper::GetSingular<int64_t>(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
      // 粉条订单返回空特征，切换物料流打平 base
      if (campaign_type == 17 || campaign_type == 18 || campaign_type == 20 || campaign_type == 21 ||
          campaign_type == 22 || campaign_type == 23) {
        return;
      }
      AddFeature(GetFeature(FeaturePrefix::PHOTO_CAMPAIGN_TYPE, campaign_type), 1.0f, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdCampaignTypeOnlyDsp);
};

REGISTER_BS_EXTRACTOR(BSExtractAdCampaignTypeOnlyDsp);

}  // namespace ad_algorithm
}  // namespace ks
