#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

using ::bs::kuaishou::ad::AdCallbackLog;
namespace ks {
namespace ad_algorithm {
class BSExtractCallbackEventDenseConvInvoke : public BSFastFeature {
 public:
  BSExtractCallbackEventDenseConvInvoke();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCallbackEventDenseConvInvoke);
};
REGISTER_BS_EXTRACTOR(BSExtractCallbackEventDenseConvInvoke);
}  // namespace ad_algorithm
}  // namespace ks
