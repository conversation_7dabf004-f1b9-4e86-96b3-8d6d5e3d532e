#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_add_cart_author_item_match_cnt_dense.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/merge_list.h"
#include "teams/ad/ad_algorithm/fed/compute/realtime_ecom_action.h"

namespace ks {
namespace ad_algorithm {

BSExtractAddCartAuthorItemMatchCntDense::BSExtractAddCartAuthorItemMatchCntDense()
    : BSFastFeature(FeatureType::DENSE_COMBINE) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_66141);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_66149);
  attr_metas_.emplace_back(BSFieldEnum::adlog_time);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_66147);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_66145);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001118);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001123);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001107);
}
void BSExtractAddCartAuthorItemMatchCntDense::Extract(const BSLog& bslog, size_t pos,
                                                      std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_66141, pos);
  auto x2 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_66149, pos);
  auto x3 = get_bslog_time(*bs);
  auto x4 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_id, pos);
  auto x7 = get_time_segment_cnt_log_value(x1, x2, x3, x4, 8, 100);
  auto x9 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_66147, pos);
  auto x14 = get_time_segment_cnt_log_value(x1, x9, x3, x4, 8, 100);
  auto x16 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_66145, pos);
  auto x18 = get_bslog_int64_list(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001118, pos);
  auto x19 =
      get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001123, pos);
  auto x20 = get_bslog_int64_list(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001107, pos);
  auto x23 = get_time_segment_cnt_log_value_v1(x1, x16, x3, x18, x19, x20, 8, 100);
  auto x24 = merge_float_list_all(x7, x14, x23);
  add_feature_result(x24, 24, result);
}

}  // namespace ad_algorithm
}  // namespace ks
