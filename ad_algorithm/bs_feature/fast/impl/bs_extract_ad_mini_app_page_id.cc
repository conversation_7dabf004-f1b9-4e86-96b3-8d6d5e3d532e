#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mini_app_page_id.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdMiniAppPageId::BSExtractAdMiniAppPageId() : BSFastFeature(FeatureType::COMBINE) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_71_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_71_value);
}

void BSExtractAdMiniAppPageId::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  uint64 item_id = BSFieldHelper::GetSingular<uint64_t>(*bs, BSFieldEnum::adlog_item_id, pos);
  uint64 site_id = 0;
  bool has_value = BSFieldHelper::HasSingular<int64_t, true>(*bs, BSFieldEnum::adlog_context_exists, pos);
  if (has_value) {
    auto key_enum = BSFieldEnum::adlog_context_info_common_attr_key_71_key;
    auto value_enum = BSFieldEnum::adlog_context_info_common_attr_key_71_value;
    BSMapField<uint64_t, uint64_t, true> site_id_map(*bs, key_enum, value_enum, pos);
    auto iter = site_id_map.Get(item_id);
    if (iter.second) {
      site_id = iter.second;
      AddFeature(site_id, 1.0, result);
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
