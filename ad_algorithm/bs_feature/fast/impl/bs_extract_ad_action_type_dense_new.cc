#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_action_type_dense_new.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_action_type.pb.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdActionTypeDenseNew::BSExtractAdActionTypeDenseNew() : BSFastFeature(DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type);
}

void BSExtractAdActionTypeDenseNew::Extract(const BSLog& bslog, size_t pos,
                                            std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_action_type = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type;
  int ocpc_action_type = BSFieldHelper::GetSingular<int>(*bs, enum_action_type, pos);

  int type_id = 0;
  if (ocpc_action_type == ::bs::kuaishou::ad::AD_CONVERSION) {
    type_id = 1;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) {
    type_id = 2;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_PURCHASE) {
    type_id = 3;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_ITEM_CLICK) {
    type_id = 4;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::EVENT_APP_INVOKED) {
    type_id = 5;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::EVENT_KEY_INAPP_ACTION) {
    type_id = 6;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_ROAS) {
    type_id = 7;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_CREDIT_GRANT) {
    type_id = 8;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_SEVEN_DAY_ROAS) {
    type_id = 9;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::EVENT_APPOINT_JUMP_CLICK) {
    type_id = 10;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::EVENT_JINJIAN) {
    type_id = 11;
  }

  AddFeature(0, type_id, result);
}
}  // namespace ad_algorithm
}  // namespace ks
