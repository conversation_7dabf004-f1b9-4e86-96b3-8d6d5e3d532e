/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_author_id_photo_played_end_cnt_6.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/realtime_ecom_action.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

BSAuthorIdPhotoPlayedEndCnt6::BSAuthorIdPhotoPlayedEndCnt6()
    : BSFastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_1479_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_1479_value);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_id);
}
void BSAuthorIdPhotoPlayedEndCnt6::Extract(const BSLog& bslog, size_t pos,
                                           std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_map_int64_int64<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_1479_key,
                                            BSFieldEnum::adlog_context_info_common_attr_key_1479_value, pos);
  auto x2 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_id, pos);
  auto x3 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_id, pos);
  auto x4 = get_first_non_zero(x2, x3);
  auto x5 = get_value_from_map(x1, x4);
  auto x7 = value_or(x5, 0);
  auto x8 = cast_to_int64(x7);
  add_feature_result(x8, result);
}

}  // namespace ad_algorithm
}  // namespace ks
