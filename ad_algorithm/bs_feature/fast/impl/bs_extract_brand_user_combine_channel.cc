#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_user_combine_channel.h"

#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"

namespace ks {
namespace ad_algorithm {
BSExtractBrandUserCombineChannel::BSExtractBrandUserCombineChannel() : BSFastFeature(FeatureType::COMBINE) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_app_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_pos_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_page_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_sub_page_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_updown_page_label);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_updown_page_label_exists);
}

void BSExtractBrandUserCombineChannel::Extract(const BSLog& bslog, size_t pos,
                                               std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_context_exists = BSFieldEnum::adlog_context_exists;
  bool context_exists = BSFieldHelper::GetSingular<bool, true>(*bs, enum_context_exists, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  if (!info_exists) {
    return;
  }
  auto enum_app_id = BSFieldEnum::adlog_context_app_id;
  absl::string_view app_id = BSFieldHelper::GetSingular<absl::string_view, true>(*bs, enum_app_id, pos);

  auto enum_page_id = BSFieldEnum::adlog_context_page_id;
  int64_t page_id = BSFieldHelper::GetSingular<int64_t, true>(*bs, enum_page_id, pos);

  auto enum_pos_id = BSFieldEnum::adlog_context_pos_id;
  int64_t pos_id = BSFieldHelper::GetSingular<int64_t, true>(*bs, enum_pos_id, pos);

  auto enum_sub_page_id = BSFieldEnum::adlog_context_sub_page_id;
  int64_t sub_page_id = BSFieldHelper::GetSingular<int64_t, true>(*bs, enum_sub_page_id, pos);

  auto enum_action_type = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type;
  int64_t action_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_action_type, pos);

  auto enum_ad_user_info_exists = BSFieldEnum::adlog_user_info_ad_user_info_exists;
  bool ad_user_info_exists = BSFieldHelper::GetSingular<bool, true>(*bs, enum_ad_user_info_exists, pos);

  auto enum_label_exists = BSFieldEnum::adlog_user_info_ad_user_info_updown_page_label_exists;
  bool label_exists = BSFieldHelper::GetSingular<bool, true>(*bs, enum_label_exists, pos);

  auto enum_user_info_exists = BSFieldEnum::adlog_user_info_exists;
  bool user_info_exists = BSFieldHelper::GetSingular<bool, true>(*bs, enum_user_info_exists, pos);

  if (context_exists) {
    int page_label = 0;
    std::string channel = std::string(app_id.data(), app_id.size()) + "_" + std::to_string(page_id) + "_" +
                          std::to_string(sub_page_id) + "_" + std::to_string(pos_id) + "_" +
                          std::to_string(page_label) + "_" + std::to_string(action_type);
    int hashid = ad_nn::bs::Hash(channel);
    AddFeature(GetFeature(FeaturePrefix::USER_BROWSE_TYPE, hashid), 1.0, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
