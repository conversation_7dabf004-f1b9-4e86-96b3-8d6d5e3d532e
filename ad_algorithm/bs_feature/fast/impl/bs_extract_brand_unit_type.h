#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandUnitType : public BSFastFeature {
 public:
  BSExtractBrandUnitType() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_75008);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_75009);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if (item_type != bs::ItemType::AD_BRAND) {
      return;
    }
    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);

    bool has_value = false;
    if (has_ad_dsp_info) {
      auto interactive_style_attr = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_75008;
      auto brand_creative_interactive_style =
          BSFieldHelper::GetSingular<int>(*bs, interactive_style_attr, pos, &has_value);
      if (has_value) {
        AddFeature(GetFeature(FeaturePrefix::ADX_CREATIVE_TYPE, brand_creative_interactive_style), 1.0,
                   result);
      }
      auto unit_style_attr = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_75009;
      auto unit_style = BSFieldHelper::GetSingular<int>(*bs, unit_style_attr, pos, &has_value);
      if (has_value) {
        AddFeature(GetFeature(FeaturePrefix::UNIT_ID, unit_style), 1.0, result);
      }
    }
  }

 private:
  const std::string USED_FEATURES[2] = {"item.ad_dsp_info.common_info_attr.BRAND_CREATIVE_INTERACTIVE_STYLE",
                                        "item.ad_dsp_info.common_info_attr.BRAND_UNIT_STYLE_TYPE"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandUnitType);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandUnitType);

}  // namespace ad_algorithm
}  // namespace ks
