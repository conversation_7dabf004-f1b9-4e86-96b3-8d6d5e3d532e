#pragma once
#include <unordered_map>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdPcreativeImpressionCost11 : public BSFastFeature {
 public:
  BSExtractAdPcreativeImpressionCost11();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdPcreativeImpressionCost11);
};

REGISTER_BS_EXTRACTOR(BSExtractAdPcreativeImpressionCost11);

}  // namespace ad_algorithm
}  // namespace ks
