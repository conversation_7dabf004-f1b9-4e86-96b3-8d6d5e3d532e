#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_realtime_gmv_max_bool_mask_v2.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/action.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {
BSExtractAuthorRealtimeGmvMaxBoolMaskV2::BSExtractAuthorRealtimeGmvMaxBoolMaskV2()
    : BSFastFeature(DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82067);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82005);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82084);
}
void BSExtractAuthorRealtimeGmvMaxBoolMaskV2::Extract(const BSLog& bslog, size_t pos,
                                                      std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  auto enum_key_82084 = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82084;
  absl::optional<float> key_82084_opt = get_bslog_float(*bs, enum_key_82084, pos);
  auto enum_key_82067 = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82067;
  auto enum_key_82005 = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82005;
  absl::Span<const int64_t> key_82067_opt = get_bslog_int64_list(*bs, enum_key_82067, pos);
  absl::optional<int64_t> key_82005_opt = get_bslog_int64(*bs, enum_key_82005, pos);
  float gmv = get_max_gmv_v2(get_max(key_82067_opt), key_82005_opt, key_82084_opt);
  std::vector<ExtractResult> result_value =
      find_mask_from_segments_by_target_value(gmv, get_gmv_buckets_lining10());
  add_feature_result(result_value, result);
}
}  // namespace ad_algorithm
}  // namespace ks
