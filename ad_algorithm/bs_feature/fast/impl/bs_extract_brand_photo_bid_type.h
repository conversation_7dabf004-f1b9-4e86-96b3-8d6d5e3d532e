#pragma once
#include <string>
#include <vector>

#include "base/hash_function/city.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandPhotoBidType : public BSFastFeature {
 public:
  BSExtractBrandPhotoBidType() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    bool has_unit =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_exists, pos);
    bool has_base =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists, pos);

    if (!has_ad_dsp_info || !has_unit || !has_base) {
      return;
    }
    int64_t ocpc_action_type = BSFieldHelper::GetSingular<int64_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type, pos);
    AddFeature(GetFeature(FeaturePrefix::PHOTO_OCPC_ACTION_TYPE, ocpc_action_type), 1.0f, result);
  }

 private:
  const std::string USED_FEATURES[1] = {
      "item.ad_dsp_info.unit.base.ocpc_action_type",
  };
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandPhotoBidType);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandPhotoBidType);

}  // namespace ad_algorithm
}  // namespace ks
