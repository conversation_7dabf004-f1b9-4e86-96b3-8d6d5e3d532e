#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_realtime_gmv_p10_bool_mask.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/action.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {
BSExtractAuthorRealtimeGmvP10BoolMask::BSExtractAuthorRealtimeGmvP10BoolMask() : BSFastFeature(DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82067);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82005);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82071);
}
void BSExtractAuthorRealtimeGmvP10BoolMask::Extract(const BSLog& bslog, size_t pos,
                                                    std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  auto enum_key_82071 = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82071;
  absl::optional<float> key_82071_opt = get_bslog_float(*bs, enum_key_82071, pos);
  auto enum_key_82067 = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82067;
  auto enum_key_82005 = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82005;
  absl::Span<const int64_t> key_82067_opt = get_bslog_int64_list(*bs, enum_key_82067, pos);
  absl::optional<int64_t> key_82005_opt = get_bslog_int64(*bs, enum_key_82005, pos);
  float gmv = get_quantile_gmv(key_82067_opt, 0.1, key_82005_opt, key_82071_opt);
  std::vector<ExtractResult> result_value =
      find_mask_from_segments_by_target_value_reverse(gmv, get_gmv_buckets_lining10());
  add_feature_result(result_value, result);
}
}  // namespace ad_algorithm
}  // namespace ks
