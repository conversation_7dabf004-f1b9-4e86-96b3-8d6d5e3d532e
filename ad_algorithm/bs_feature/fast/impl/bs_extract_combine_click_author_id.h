#pragma once
#include <string>
#include <vector>

#include "base/strings/string_number_conversions.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_util.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractCombineClickAuthorId : public BSFastFeature {
 public:
  static const int max_click_num = 20;

  BSExtractCombineClickAuthorId() : BSFastFeature(FeatureType::COMBINE, 26, 26) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_action_detail_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_action_detail_click_id);
    bs_util.BSHasPhotoInfo.fill_attr_metas(&attr_metas_);
    bs_util.BSHasPhotoInfoAuthorInfo.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoId.fill_attr_metas(&attr_metas_);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    if (!bs_util.BSHasPhotoInfo(bs, pos)) {
      return;
    }
    if (!bs_util.BSHasPhotoInfoAuthorInfo(bs, pos)) {
      return;
    }
    bool has_user_info =
        BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
    bool has_user_info_action_detail =
        BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_action_detail_exists, pos);
    if (!has_user_info || !has_user_info_action_detail) {
      return;
    }
    auto author_id = bs_util.BSGetPhotoInfoAuthorInfoId(bs, pos);
    BSRepeatedField<uint64_t, true> click_id(*bs, BSFieldEnum::adlog_user_info_action_detail_click_id, pos);
    int click_num = click_id.size();

    for (int i = 0; i < click_num && i < max_click_num; ++i) {
      uint64_t id = GetFeature(FeaturePrefix::COMBINE_CLICK_AUTHOR_ID, click_id.Get(i), author_id);
      AddFeature(id, 1.0, result);
    }
  }

 private:
  const std::string USED_FEATURES[4] = {
      "user_info.action_detail.click.id", "item.ad_dsp_info.photo_info.author_info.id",
      "item.fans_top_info.photo_info.author_info.id", "item.nature_photo_info.photo_info.author_info.id"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineClickAuthorId);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineClickAuthorId);
}  // namespace ad_algorithm
}  // namespace ks
