#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"

namespace ks {
namespace ad_algorithm {

class BSExtractCombineAdDeviceInfoContextDupPhoto : public BSFastFeature {
 public:
  BSExtractCombineAdDeviceInfoContextDupPhoto() : BSFastFeature(FeatureType::COMBINE, 16, 26, 10) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_dup_photo_id);

    attr_metas_.emplace_back(BSFieldEnum::adlog_context_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_page_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_sub_page_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_time);

    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_os_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_ip_v4);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_connection_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_operator_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_device_mod);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_brand);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_readable_mod);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_price);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_is_kcard);

    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_os_type_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_ip_v4_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_connection_type_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_operator_type_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_device_mod_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_brand_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_readable_mod_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_price_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_is_kcard_exists);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    auto item_type = BSFieldHelper::GetSingular<uint64_t>(*bs, BSFieldEnum::adlog_item_type, pos);
    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    bool has_creative =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_exists, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info &&
        has_creative) {
      bool has_dup_photo_id = false;
      uint64 dup_photo_id = BSFieldHelper::GetSingular<uint64>(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_dup_photo_id, pos, &has_dup_photo_id);
      bool has_user_info =
          BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
      auto key_ad_user_info_exists = BSFieldEnum::adlog_user_info_ad_user_info_exists;
      bool has_ad_user_info = BSFieldHelper::GetSingular<bool, true>(*bs, key_ad_user_info_exists, pos);
      bool has_context = BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_context_exists, pos);
      if (has_user_info && has_ad_user_info && has_context) {
        auto adlog_time = BSFieldHelper::GetSingular<int64_t, true>(*bs, BSFieldEnum::adlog_time, pos);
        char datehour[100];
        time_t tick = (time_t)(adlog_time / 1000);
        struct tm tm = {0};
        struct tm* tm1 = localtime_r(&tick, &tm);
        if (tm1 == NULL) {
          return;
        }
        strftime(datehour, sizeof(datehour), "%H", &tm);

        auto key_page_id = BSFieldEnum::adlog_context_page_id;
        uint64_t page_id = BSFieldHelper::GetSingular<uint64_t, true>(*bs, key_page_id, pos);

        auto key_sub_page_id = BSFieldEnum::adlog_context_sub_page_id;
        uint64_t sub_page_id = ad_nn::BSFieldHelper::GetSingular<uint64_t, true>(*bs, key_sub_page_id, pos);

        std::string context_info = std::to_string(page_id) + std::to_string(sub_page_id) + datehour;

        auto key_device_info_size = BSFieldEnum::adlog_user_info_ad_user_info_device_info_size;
        int device_info_size = BSFieldHelper::GetSingular<int, true>(*bs, key_device_info_size, pos);

        auto key_os_type = BSFieldEnum::adlog_user_info_ad_user_info_device_info_os_type;
        BSRepeatedField<int64_t, true> os_type(*bs, key_os_type, pos);

        auto key_ip_v4 = BSFieldEnum::adlog_user_info_ad_user_info_device_info_ip_v4;
        BSRepeatedField<absl::string_view, true> ip_v4(*bs, key_ip_v4, pos);

        auto key_connection_type = BSFieldEnum::adlog_user_info_ad_user_info_device_info_connection_type;
        BSRepeatedField<int64_t, true> connection_type(*bs, key_connection_type, pos);

        auto key_operator_type = BSFieldEnum::adlog_user_info_ad_user_info_device_info_operator_type;
        BSRepeatedField<int64_t, true> operator_type(*bs, key_operator_type, pos);

        auto key_device_mod = BSFieldEnum::adlog_user_info_ad_user_info_device_info_device_mod;
        BSRepeatedField<absl::string_view, true> device_mod(*bs, key_device_mod, pos);

        auto key_brand = BSFieldEnum::adlog_user_info_ad_user_info_device_info_brand;
        BSRepeatedField<absl::string_view, true> brand(*bs, key_brand, pos);

        auto key_readable_mod = BSFieldEnum::adlog_user_info_ad_user_info_device_info_readable_mod;
        BSRepeatedField<absl::string_view, true> readable_mod(*bs, key_readable_mod, pos);

        auto key_price = BSFieldEnum::adlog_user_info_ad_user_info_device_info_price;
        BSRepeatedField<int64_t, true> price(*bs, key_price, pos);

        auto key_is_kcard = BSFieldEnum::adlog_user_info_ad_user_info_device_info_is_kcard;
        BSRepeatedField<int64_t, true> is_kcard(*bs, key_is_kcard, pos);

        auto key_os_type_exists = BSFieldEnum::adlog_user_info_ad_user_info_device_info_os_type_exists;
        BSRepeatedField<bool, true> os_type_exists(*bs, key_os_type_exists, pos);

        auto key_ip_v4_exists = BSFieldEnum::adlog_user_info_ad_user_info_device_info_ip_v4_exists;
        BSRepeatedField<bool, true> ip_v4_exists(*bs, key_ip_v4_exists, pos);

        auto key_connection_type_exists =
            BSFieldEnum::adlog_user_info_ad_user_info_device_info_connection_type_exists;
        BSRepeatedField<bool, true> connection_type_exists(*bs, key_connection_type_exists, pos);

        auto key_operator_type_exists =
            BSFieldEnum::adlog_user_info_ad_user_info_device_info_operator_type_exists;
        BSRepeatedField<bool, true> operator_type_exists(*bs, key_operator_type_exists, pos);

        auto key_device_mod_exists = BSFieldEnum::adlog_user_info_ad_user_info_device_info_device_mod_exists;
        BSRepeatedField<bool, true> device_mod_exists(*bs, key_device_mod_exists, pos);

        auto key_brand_exists = BSFieldEnum::adlog_user_info_ad_user_info_device_info_brand_exists;
        BSRepeatedField<bool, true> brand_exists(*bs, key_brand_exists, pos);

        auto key_readable_mod_exists =
            BSFieldEnum::adlog_user_info_ad_user_info_device_info_readable_mod_exists;
        BSRepeatedField<bool, true> readable_mod_exists(*bs, key_readable_mod_exists, pos);

        auto key_price_exists = BSFieldEnum::adlog_user_info_ad_user_info_device_info_price_exists;
        BSRepeatedField<bool, true> price_exists(*bs, key_price_exists, pos);

        auto key_is_kcard_exists = BSFieldEnum::adlog_user_info_ad_user_info_device_info_is_kcard_exists;
        BSRepeatedField<bool, true> is_kcard_exists(*bs, key_is_kcard_exists, pos);

        std::string device_str = "";
        if (device_info_size > 0) {
          device_info_size = device_info_size < 3 ? device_info_size : 3;
          for (int k = 0; k < device_info_size; k++) {
            if (k < os_type.size() && k < os_type_exists.size() && os_type_exists.Get(k)) {
              device_str = device_str + std::to_string(os_type.Get(k));
            }

            if (k < ip_v4.size() && k < ip_v4_exists.size() && ip_v4_exists.Get(k)) {
              device_str = device_str + std::string(ip_v4.Get(k));
            }

            if (k < connection_type.size() && k < connection_type_exists.size() &&
                connection_type_exists.Get(k)) {
              device_str = device_str + std::to_string(connection_type.Get(k));
            }

            if (k < operator_type.size() && k < operator_type_exists.size() && operator_type_exists.Get(k)) {
              device_str = device_str + std::to_string(operator_type.Get(k));
            }

            if (k < device_mod.size() && k < device_mod_exists.size() && device_mod_exists.Get(k)) {
              device_str = device_str + std::string(device_mod.Get(k));
            }

            if (k < brand.size() && k < brand_exists.size() && brand_exists.Get(k)) {
              device_str = device_str + std::string(brand.Get(k));
            }

            if (k < readable_mod.size() && k < readable_mod_exists.size() && readable_mod_exists.Get(k)) {
              device_str = device_str + std::string(readable_mod.Get(k));
            }

            if (k < price.size() && k < price_exists.size() && price_exists.Get(k)) {
              device_str = device_str + std::to_string(price.Get(k));
            }

            if (k < is_kcard.size() && k < is_kcard_exists.size() && is_kcard_exists.Get(k)) {
              device_str = device_str + std::to_string(is_kcard.Get(k));
            }
          }
          std::string device_context_str = device_str + context_info;
          uint64_t id = GetFeature(FeaturePrefix::COMBINE_DEVICE_INFO_CONTEXT_DUP_PHOTO,
                                   ad_nn::bs::Hash(device_context_str), dup_photo_id, 0);
          AddFeature(id, 1.0, result);
        }
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineAdDeviceInfoContextDupPhoto);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineAdDeviceInfoContextDupPhoto);

}  // namespace ad_algorithm
}  // namespace ks
