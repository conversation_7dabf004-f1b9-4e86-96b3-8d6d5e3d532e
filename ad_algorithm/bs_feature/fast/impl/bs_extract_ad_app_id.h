#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdAppId : public BSFastFeature {
 public:
  BSExtractAdAppId() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_app_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
    auto key_unit_exists = BSFieldEnum::adlog_item_ad_dsp_info_unit_exists;
    bool has_unit = BSFieldHelper::GetSingular<bool>(*bs, key_unit_exists, pos);
    auto key_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists;
    bool has_base = BSFieldHelper::GetSingular<bool>(*bs, key_base_exists, pos);

    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info &&
        has_unit && has_base) {
      auto app_id = BSFieldHelper::GetSingular<uint64_t>(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_app_id, pos);
      AddFeature(GetFeature(FeaturePrefix::PHOTO_APP_ID, app_id), 1.0f, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdAppId);
};

REGISTER_BS_EXTRACTOR(BSExtractAdAppId);

}  // namespace ad_algorithm
}  // namespace ks
