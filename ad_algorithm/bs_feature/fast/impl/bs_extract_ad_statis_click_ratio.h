#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdStatisClickRatio : public BSFastFeature {
 public:
  BSExtractAdStatisClickRatio() : BSFastFeature(FeatureType::DENSE_ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_value);
    // attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_size);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    // bool has_item_statis_count = false;
    // int column_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_size;
    // (void)BSFieldHelper::GetSingular<int64_t>(*bs, column_id, pos, &has_item_statis_count);
    // if (!has_item_statis_count) {
    //   return;
    // }
    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
    if (has_ad_dsp_info) {
      auto key_column_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_key;
      auto value_column_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_value;
      BSMapField<absl::string_view, int64_t> item_statis_count(*bs, key_column_id, value_column_id, pos);

      if (item_statis_count.size() == 0) {
        return;
      }

      int click = 0;
      int impression = 0;

      auto iter = item_statis_count.Get("click");
      click = (iter.second) ? iter.first : 0;
      iter = item_statis_count.Get("impression");
      impression = (iter.second) ? iter.first : 0;
      double ctr = 0;
      if (impression >= 200) {
        ctr = 1.0 * click / impression;
      }
      if (ctr > 1) {
        ctr = 1;
      }
      AddFeature(0, ctr, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdStatisClickRatio);
};

REGISTER_BS_EXTRACTOR(BSExtractAdStatisClickRatio);

}  // namespace ad_algorithm
}  // namespace ks
