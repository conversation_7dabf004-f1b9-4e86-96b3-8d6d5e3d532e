#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_innerloop_item_inheritees_dense.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdInnerloopItemInheriteesDense::BSExtractAdInnerloopItemInheriteesDense()
    : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_66300);
  bs_util.BSHasPhotoInfo.fill_attr_metas(&attr_metas_);
  bs_util.BSGetPhotoInfoId.fill_attr_metas(&attr_metas_);
}

void BSExtractAdInnerloopItemInheriteesDense::Extract(const BSLog& bslog, size_t pos,
                                                      std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  if (pos >= bslog.item_size()) {
    return;
  }

  int64 status = 0;
  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);
  auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size;
  int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);
  auto enum_photo_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists;
  bool photo_info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_photo_info_exists, pos);
  auto enum_key_66300 = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_66300;
  int64_t key_66300 = BSFieldHelper::GetSingular<int64_t>(*bs, enum_key_66300, pos);
  auto enum_key_66300_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_66300;
  bool key_66300_exists = BSFieldHelper::HasSingular<int64_t>(*bs, enum_key_66300_exists, pos);

  if (info_exists && photo_info_exists && attr_size > 0) {
    if (key_66300_exists) {
      status = key_66300;
    }
  }
  AddFeature(0, status, result);
}
}  // namespace ad_algorithm
}  // namespace ks
