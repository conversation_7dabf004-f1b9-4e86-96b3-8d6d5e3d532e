#pragma once
#include <vector>
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandTextFeatureBertCluster : public BSFastFeature {
 public:
  BSExtractBrandTextFeatureBertCluster();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandTextFeatureBertCluster);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandTextFeatureBertCluster);

}  // namespace ad_algorithm
}  // namespace ks
