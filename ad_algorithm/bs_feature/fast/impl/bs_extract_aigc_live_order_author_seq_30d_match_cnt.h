#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_aigc_live_order_author_seq_30d_match_cnt.dark
class BSExtractAigcLiveOrderAuthorSeq30dMatchCnt : public BSFastFeature {
 public:
  BSExtractAigcLiveOrderAuthorSeq30dMatchCnt();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAigcLiveOrderAuthorSeq30dMatchCnt);
};

REGISTER_BS_EXTRACTOR(BSExtractAigcLiveOrderAuthorSeq30dMatchCnt);
}  // namespace ad_algorithm
}  // namespace ks
