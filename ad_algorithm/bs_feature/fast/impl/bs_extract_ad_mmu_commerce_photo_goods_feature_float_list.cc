#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mmu_commerce_photo_goods_feature_float_list.h"

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdMmuGoodsFeatureNewEmbed64::BSExtractAdMmuGoodsFeatureNewEmbed64()
    : BSFastFeatureNoPrefix(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25226);
}
void BSExtractAdMmuGoodsFeatureNewEmbed64::Extract(const BSLog& bslog, size_t pos,
                                                   std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  bool has_ad_dsp_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
  bool has_dsp_photo_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists, pos);
  int32_t common_info_attr_size = BSFieldHelper::GetSingular<int32_t>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size, pos);
  if (item_type == bs::ItemType::AD_DSP && has_ad_dsp_info && has_dsp_photo_info &&
      common_info_attr_size > 0) {
    auto key_enum = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25226;
    BSRepeatedField<float> attr(*bs, key_enum, pos);
    const size_t list_size = attr.size();
    for (int i = 0; i < list_size; i++) {
      AddFeature(i, attr.Get(i), result);
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
