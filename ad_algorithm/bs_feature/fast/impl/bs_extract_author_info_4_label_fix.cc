#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_info_4_label_fix.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_action_type.pb.h"

namespace ks {
namespace ad_algorithm {

BSExtractAuthorInfo4LabelFix::BSExtractAuthorInfo4LabelFix()
    : BSFastFeatureNoPrefix(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82001);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82002);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82003);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82006);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82007);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type);
}

void BSExtractAuthorInfo4LabelFix::Extract(const BSLog& bslog, size_t pos,
                                           std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  float author_price_1d = 0.0;
  float author_price_3d = 0.0;
  float author_price_14d = 0.0;
  float author_price_ocpc_roas = 0.0;
  float author_price_ocpc_roi = 0.0;
  int64_t ocpc_action_type = BSFieldHelper::GetSingular<int64_t>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type, pos);
  author_price_1d = BSFieldHelper::GetSingular<double>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82001, pos);
  author_price_3d = BSFieldHelper::GetSingular<double>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82002, pos);
  author_price_14d = BSFieldHelper::GetSingular<double>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82003, pos);
  author_price_ocpc_roi = BSFieldHelper::GetSingular<double>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82006, pos);
  author_price_ocpc_roas = BSFieldHelper::GetSingular<double>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82007, pos);
  float res = 0.0;
  if (author_price_1d > 0.0) {
    res = author_price_1d;
  } else if (author_price_3d > 0.0) {
    res = author_price_3d;
  } else if (author_price_14d > 0.0) {
    res = author_price_14d;
  }
  if (res == 0.0 && ocpc_action_type == ::bs::kuaishou::ad::AD_MERCHANT_ROAS) {
    res = author_price_ocpc_roas;
  }
  if (res == 0.0 && ocpc_action_type == ::bs::kuaishou::ad::AD_FANS_TOP_ROI) {
    res = author_price_ocpc_roi;
  }
  if (res == 0.0) {
    res = 100.0;
  }
  AddFeature(0, res, result);
  return;
}

}  // namespace ad_algorithm
}  // namespace ks
