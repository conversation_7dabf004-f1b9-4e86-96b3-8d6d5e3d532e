#include <algorithm>
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_user_click_action_nebula.h"

namespace ks {
namespace ad_algorithm {

BSExtractBrandUserClickActionNebula::
         BSExtractBrandUserClickActionNebula() : BSFastFeature(FeatureType::USER) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_photo_id);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_second_industry_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_product_id_hash);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_app_id);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_action_timestamp);
}

void BSExtractBrandUserClickActionNebula::Extract(const BSLog& bslog, size_t pos,
                                               std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  bool has_user_info = BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
  uint64_t ad_action_size = BSFieldHelper::GetSingular<uint64_t>(
      *bs, BSFieldEnum::adlog_user_info_explore_long_term_ad_action_size, pos);

  if (has_user_info && ad_action_size > 0) {
    auto key_1_list_size = BSFieldHelper::GetSingular<uint64_t>(
        *bs, BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_size, pos);
    if (key_1_list_size < 2) {
      return;
    }
    BSRepeatedField<int64_t> photo_id_list(
        *bs, BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_photo_id, pos);
    BSRepeatedField<int64_t> industry_id_list(
        *bs, BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_second_industry_id_hash,
        pos);
    BSRepeatedField<int64_t> product_id_list(
        *bs, BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_product_id_hash, pos);
    BSRepeatedField<int64_t> app_id_list(
        *bs, BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_app_id, pos);
    BSRepeatedField<uint64_t> action_timestamp_list(
        *bs, BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_action_timestamp, pos);

    uint64_t min_time = UINT_MAX;
    uint64_t max_time = 0;
    uint64_t gap_count = 0;

    stdx::unordered_map<uint64_t, uint32_t> photoId_map;
    stdx::unordered_map<uint64_t, uint32_t> industryId_map;
    stdx::unordered_map<uint64_t, uint32_t> productId_map;
    stdx::unordered_map<uint64_t, uint32_t>::iterator it;
    std::vector<uint64_t> photoIdVec;
    std::vector<uint64_t> industryIdVec;
    std::vector<uint64_t> productIdVec;

    for (int i = 0; i < key_1_list_size && i < 200 && gap_count < 100; ++i) {
      if (app_id_list.Get(i) != 2) {
        continue;
      }
      // average gap feature
      ++gap_count;
      min_time = std::min(min_time, action_timestamp_list.Get(i));
      max_time = std::max(max_time, action_timestamp_list.Get(i));

      // top3 feature
      it = photoId_map.find(photo_id_list.Get(i));
      if (it != photoId_map.end()) {
        it->second += 1;
      } else {
        photoId_map.emplace(photo_id_list.Get(i), 1);
      }

      it = industryId_map.find(industry_id_list.Get(i));
      if (it != industryId_map.end()) {
        it->second += 1;
      } else {
        industryId_map.emplace(industry_id_list.Get(i), 1);
      }

      it = productId_map.find(product_id_list.Get(i));
      if (it != productId_map.end()) {
        it->second += 1;
      } else {
        productId_map.emplace(product_id_list.Get(i), 1);
      }
    }
    GetTop3ActionTerm(photoId_map, &photoIdVec);
    GetTop3ActionTerm(industryId_map, &industryIdVec);
    GetTop3ActionTerm(productId_map, &productIdVec);

    for (auto& photo_id : photoIdVec) {
      AddFeature(GetFeature(FeaturePrefix::USER_NEBULA_TOP3_CLICK_PHOTO_ID, photo_id), 1.0f, result);
    }
    for (auto& industry_id : industryIdVec) {
      AddFeature(GetFeature(FeaturePrefix::USER_NEBULA_TOP3_CLICK_INDUSTRY_ID, industry_id), 1.0f,
                 result);
    }
    for (auto& product_id : productIdVec) {
      AddFeature(GetFeature(FeaturePrefix::USER_NEBULA_TOP3_CLICK_PRODUCT_ID, product_id), 1.0f, result);
    }
    // no click feature
    it = photoId_map.begin();
    for (it = photoId_map.begin(); it != photoId_map.end(); ++it) {
      AddFeature(GetFeature(FeaturePrefix::USER_NEBULA_NO_CLICK_PHOTO_ID, it->first), 1.0f, result);
    }
    AddFeature(GetFeature(USER_NEBULA_CLICK_PHOTO_COUNT, photoId_map.size()), 1.0f, result);
    AddFeature(GetFeature(USER_NEBULA_CLICK_INDUSTRY_COUNT, industryId_map.size()), 1.0f, result);
    AddFeature(GetFeature(USER_NEBULA_CLICK_PRODUCT_COUNT, productId_map.size()), 1.0f, result);
    if ((min_time >= max_time) || (min_time == INT_MAX) || (max_time == 0) || gap_count == 0) {
      return;
    }
    uint64_t ave_gap = (max_time - min_time) / gap_count / 60000;
    AddFeature(GetFeature(FeaturePrefix::USER_NEBULA_CLICK_AVERAGE_GAP, ave_gap), 1.0, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
