#pragma once
#include <string>
#include <vector>

#include "base/common/logging.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandFeedUserGroup : public BSFastFeature {
 public:
  BSExtractBrandFeedUserGroup() : BSFastFeature(FeatureType::USER) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_391);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool is_context_exists = BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_context_exists, pos);
    bool has_value = false;
    int brand_feed_user_group = BSFieldHelper::GetSingular<int>(
        *bs, BSFieldEnum::adlog_context_info_common_attr_key_391, pos, &has_value);
    if (is_context_exists && has_value) {
      AddFeature(GetFeature(FeaturePrefix::AD_BRAND_USER_GROUP, brand_feed_user_group), 1.0, result);
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"context.info_common_attr.BRAND_FEED_USER_GROUP"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandFeedUserGroup);
};
REGISTER_BS_EXTRACTOR(BSExtractBrandFeedUserGroup);
}  // namespace ad_algorithm
}  // namespace ks
