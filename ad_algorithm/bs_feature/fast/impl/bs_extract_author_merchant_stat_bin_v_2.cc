/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_merchant_stat_bin_v_2.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_sparse_bucket.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

BSExtractAuthorMerchantStatBinV2::BSExtractAuthorMerchantStatBinV2()
    : BSFastFeatureNoPrefix(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5100135);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5100134);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_live_creative_type);
}
void BSExtractAuthorMerchantStatBinV2::Extract(const BSLog& bslog, size_t pos,
                                               std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 =
      get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5100135, pos);
  auto x3 = value_or(x1, -1);
  auto x4 = get_a_item_cnt_bucket(x3);
  auto x5 =
      get_bslog_float(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5100134, pos);
  auto x7 = value_or(x5, -1);
  auto x8 = get_a_item_price_cv_bucket(x7);
  auto x9 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type, pos);
  auto x11 = value_or(x9, -1);
  auto x12 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
  auto x14 = value_or(x12, -1);
  auto x15 = get_bslog_int64_or_default(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_live_creative_type, pos);
  auto x17 = value_or(x15, -1);
  auto x18 = get_author_merchant_stat_bin_hash(x4, x8, x11, x14, x17);
  add_feature_result(x18, result);
}

}  // namespace ad_algorithm
}  // namespace ks
