#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandAdAcountId : public BSFastFeature {
 public:
  BSExtractBrandAdAcountId() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_account_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_value = false;
    bool has_type_value = false;
    auto key_account_id = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_account_id;
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos, &has_type_value);
    int64_t account_id = BSFieldHelper::GetSingular<int64_t>(*bs, key_account_id, pos, &has_value);
    if (!has_type_value || !has_value) {
      return;
    }
    if (item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD ||
        item_type == bs::ItemType::AD_BRAND) {
      AddFeature(GetFeature(FeaturePrefix::PHOTO_ACCOUNT_ID, account_id), 1.0f, result);
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"item.ad_dsp_info.unit.base.account_id"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandAdAcountId);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandAdAcountId);

}  // namespace ad_algorithm
}  // namespace ks
