#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdIndustryGroupIdRr : public BSFastFeature {
 public:
  BSExtractAdIndustryGroupIdRr() : BSFastFeature(FeatureType::ITEM) {}

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    if (bslog.item_size() <= pos) {
      return;
    }
    auto& item = bslog.item(pos);
    if (item.type() == bs::ItemType::AD_BRAND && item.has_ad_dsp_info() &&
        item.ad_dsp_info().common_info_attr_size() > 0) {
      for (int i = 0; i < item.ad_dsp_info().common_info_attr_size(); ++i) {
        if (item.ad_dsp_info().common_info_attr(i).name_value() ==
                ::bs::kuaishou::ad::CommonInfoAttr_Name_INDUSTRY_GROUP_ID &&
            item.ad_dsp_info().common_info_attr(i).type() ==
                ::bs::kuaishou::ad::CommonTypeEnum_AttrType_INT_ATTR) {
          uint64 industry_group_id = item.ad_dsp_info().common_info_attr(i).int_value();
          //                  LOG(INFO) << "rr_industry_group_id:" << industry_group_id;
          AddFeature(GetFeature(FeaturePrefix::AD_BRAND_INDUSTRY_GROUP_ID, industry_group_id), 1.0f, result);
          break;
        }
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdIndustryGroupIdRr);
};

REGISTER_BS_EXTRACTOR(BSExtractAdIndustryGroupIdRr);

}  // namespace ad_algorithm
}  // namespace ks
