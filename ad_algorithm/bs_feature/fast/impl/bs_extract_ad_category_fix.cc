#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_category_fix.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdCategoryFix::BSExtractAdCategoryFix() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_label_info_multi_label_fact);
}

void BSExtractAdCategoryFix::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  int item_type = BSFieldHelper::GetSingular<int, true>(*bs, BSFieldEnum::adlog_item_type, pos);
  auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
  auto key_creative_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_exists;
  bool has_creative = BSFieldHelper::GetSingular<bool>(*bs, key_creative_exists, pos);
  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD ||
       item_type == bs::ItemType::AD_BRAND) &&
      has_ad_dsp_info && has_creative) {
    auto key_base_type = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type;
    int64_t campaign_type = BSFieldHelper::GetSingular<int64_t>(*bs, key_base_type, pos);
    if (bslog.is_train()) {
      auto key_enum = BSFieldEnum::adlog_item_label_info_multi_label_fact;
      BSRepeatedField<int> label_list(*bs, key_enum, pos);
      bool is_conv = false;
      bool is_invoked = false;
      for (int i = 0; i < label_list.size(); ++i) {
        const auto& label = label_list.Get(i);
        if (label == static_cast<int>(::bs::kuaishou::ad::AdCallbackLog::EVENT_CONVERSION)) {
          is_conv = true;
        } else if (label == static_cast<int>(::bs::kuaishou::ad::AdCallbackLog::EVENT_APP_INVOKED)) {
          is_invoked = true;
        }
      }
      int app = 2;          // 拉新
      int app_advance = 7;  // 拉活
      if (is_invoked && campaign_type == app) {
        campaign_type = app_advance;
      } else if (is_conv && campaign_type == app_advance) {
        campaign_type = app;
      }
    }
    AddFeature(GetFeature(FeaturePrefix::PHOTO_USER_AD_TYPE, campaign_type), 1.0f, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
