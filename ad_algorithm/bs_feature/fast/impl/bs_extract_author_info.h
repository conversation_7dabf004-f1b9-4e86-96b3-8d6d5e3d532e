#pragma once

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAuthorInfo : public BSFastFeature {
 public:
  BSExtractAuthorInfo() : BSFastFeature(FeatureType::ITEM) {
    bs_util.BSHasAuthorInfo.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoId.fill_attr_metas(&attr_metas_);
    bs_util.BSHasAuthorInfoAttribute.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoAttributeCountFans.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoAttributeGender.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoAttributeRegTime.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoAttributeCountUpload.fill_attr_metas(&attr_metas_);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorInfo);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorInfo);

}  // namespace ad_algorithm
}  // namespace ks
