#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_log_time.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdLogTime::BSExtractAdLogTime() : BSFastFeatureNoPrefix(FeatureType::USER) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_time);
}
void BSExtractAdLogTime::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_time(*bs);
  add_feature_result(x1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
