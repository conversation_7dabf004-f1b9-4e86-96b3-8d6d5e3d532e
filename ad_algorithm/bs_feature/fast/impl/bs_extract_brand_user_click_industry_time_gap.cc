#include <string>
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_user_click_industry_time_gap.h"

namespace ks {
namespace ad_algorithm {

BSExtractBrandUserClickIndustryTimeGap::
  BSExtractBrandUserClickIndustryTimeGap() : BSFastFeature(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_time);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_dsp_action_detail_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_dsp_action_detail_key_11_list_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_dsp_action_detail_key_11_list_industry_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_dsp_action_detail_key_11_list_action_time);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
}

void BSExtractBrandUserClickIndustryTimeGap::Extract(
  const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  bool has_ad_dsp_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
  bool has_creative =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_exists, pos);
  bool has_creative_base =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists, pos);
  if (!has_ad_dsp_info || !has_creative || !has_creative_base) {
    return;
  }

  bool has_user_info_exists =
      BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
  uint64_t detail_size = BSFieldHelper::GetSingular<uint64_t, true>(
      *bs, BSFieldEnum::adlog_user_info_ad_dsp_action_detail_size, pos);

  if (!has_user_info_exists || detail_size <= 0) return;
  bool key_exists = BSFieldHelper::GetSingular<bool, true>(
      *bs, BSFieldEnum::adlog_user_info_ad_dsp_action_detail_key_11_exists, pos);
  if (!key_exists) return;

  uint64_t new_industry_id = BSFieldHelper::GetSingular<uint64_t>(
           *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id, pos);
  auto key_new_industry_id_list = BSFieldEnum::adlog_user_info_ad_dsp_action_detail_key_11_list_industry_id;
  BSRepeatedField<uint64_t, true> new_industry_id_list(*bs, key_new_industry_id_list, pos);
  auto key_action_time_list = BSFieldEnum::adlog_user_info_ad_dsp_action_detail_key_11_list_action_time;
  BSRepeatedField<uint64_t, true> new_action_time_list(*bs, key_action_time_list, pos);
  uint64_t time = 0;
  for (int i = 0; i < new_industry_id_list.size(); ++i) {
    if ((new_industry_id_list.Get(i) == new_industry_id) && (new_industry_id != 0)) {
      if (new_action_time_list.Get(i) > time) {
        time = new_action_time_list.Get(i);
      }
    }
  }
  if (time > 0) {
    int64_t log_time = BSFieldHelper::GetSingular<int64_t>(*bs, BSFieldEnum::adlog_time, pos);
    uint64_t gap_min = (log_time - time) / 60000;
    if (gap_min < 5) {
      AddFeature(GetFeature(COMBINE_USER_CLICK_INDUSTRY_TIME_GAP, (1ULL << 40 | gap_min)), 1.0, result);
    } else if (gap_min < 30) {
      AddFeature(GetFeature(COMBINE_USER_CLICK_INDUSTRY_TIME_GAP, (2ULL << 40 | gap_min / 5)), 1.0,
                  result);
    } else if (gap_min < 60) {
      AddFeature(GetFeature(COMBINE_USER_CLICK_INDUSTRY_TIME_GAP, (3ULL << 40 | gap_min / 10)), 1.0,
                  result);
    } else if (gap_min < 1440) {
      AddFeature(GetFeature(COMBINE_USER_CLICK_INDUSTRY_TIME_GAP, (4ULL << 40 | gap_min / 120)), 1.0,
                  result);
    } else if (gap_min < 14400) {
      AddFeature(GetFeature(COMBINE_USER_CLICK_INDUSTRY_TIME_GAP, (5ULL << 40 | gap_min / 1440)), 1.0,
                  result);
    } else {
      AddFeature(GetFeature(COMBINE_USER_CLICK_INDUSTRY_TIME_GAP, (6ULL << 40 | gap_min / 7200)), 1.0,
                  result);
    }
  }
}

}  // namespace ad_algorithm
}  // namespace ks
