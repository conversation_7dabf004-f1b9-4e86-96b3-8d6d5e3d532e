#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_duration.h"
namespace ks {
namespace ad_algorithm {

BSExtractBrandDuration::BSExtractBrandDuration() : BSFastFeature(FeatureType::ITEM) {
  bs_util.BSGetPhotoInfoPhotoAttributeDurationMs.fill_attr_metas(&attr_metas_);
}

void BSExtractBrandDuration::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  int duration_ms = bs_util.BSGetPhotoInfoPhotoAttributeDurationMs(bs, pos);
  if (duration_ms != 0) {
    AddFeature(GetFeature(FeaturePrefix::PHOTO_DURATION, duration_ms), 1.0f, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
