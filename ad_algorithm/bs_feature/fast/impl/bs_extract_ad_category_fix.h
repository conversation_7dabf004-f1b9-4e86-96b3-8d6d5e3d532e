#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdCategoryFix : public BSFastFeature {
 public:
  BSExtractAdCategoryFix();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdCategoryFix);
};

REGISTER_BS_EXTRACTOR(BSExtractAdCategoryFix);

}  // namespace ad_algorithm
}  // namespace ks
