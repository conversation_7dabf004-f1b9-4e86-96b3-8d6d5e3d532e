#pragma once
#include <stdlib.h>

#include <algorithm>
#include <set>
#include <string>
#include <vector>

#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"

namespace ks {
namespace ad_algorithm {

template <int field, int max_num>
class BSExtractAdEdgGraphEmb : public BSFastFeatureNoPrefix {
 public:
  BSExtractAdEdgGraphEmb() : BSFastFeatureNoPrefix(FeatureType::DENSE_ITEM) {
    feature_index_ = 0;
    AddAttrMetas();
  }
  explicit BSExtractAdEdgGraphEmb(size_t index) : BSFastFeatureNoPrefix(FeatureType::DENSE_ITEM) {
    feature_index_ = index;
    AddAttrMetas();
  }

  void AddAttrMetas() {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_live_creative_type);
    if (field == 23900) {
      attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_23900);
      attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_23900);
    }
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    auto live_creative_type = BSFieldHelper::GetSingular<int>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_live_creative_type, pos);
    switch (field) {
      case 23900:  //  edg author embed
        int edg_author_emb = 0;
        if (live_creative_type != 1) {
          edg_author_emb = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_23900;
        } else {
          edg_author_emb = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_23900;
        }
        BSRepeatedField<float> author_attrs(*bs, edg_author_emb, pos);
        for (size_t i = 0; author_attrs.size() && i < max_num; ++i) {
          AddFeature(i, author_attrs.Get(i), result);
        }
        break;
    }
  }

 private:
  size_t feature_index_;
};
using BSExtractAdEdgAuthorGraphEmb = BSExtractAdEdgGraphEmb<23900, 64>;
REGISTER_BS_EXTRACTOR(BSExtractAdEdgAuthorGraphEmb);

}  // namespace ad_algorithm
}  // namespace ks
