#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_yellow_car_item_id.dark
class BSExtractAuthorSellHistoryItemIdDense0 : public BSFastFeature {
 public:
  BSExtractAuthorSellHistoryItemIdDense0();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorSellHistoryItemIdDense0);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorSellHistoryItemIdDense0);
}  // namespace ad_algorithm
}  // namespace ks
