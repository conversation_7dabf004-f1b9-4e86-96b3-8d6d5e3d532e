#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdStatisPlayendRatio : public BSFastFeature {
 public:
  BSExtractAdStatisPlayendRatio() : BSFastFeature(FeatureType::DENSE_ITEM) {
    bs_info.kItemAdDspInfoCommonAttr.FillMeta(605, &attr_metas_);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int32_t name_attr_id = 605;
    int playend = 0;
    int impression = 0;
    std::vector<int32_t> attr_ids = bs_info.kItemAdDspInfoCommonAttr.find(name_attr_id);
    if (attr_ids.size() == 2) {
      BSMapField<absl::string_view, int64_t> bs_map(*bs, attr_ids[0], attr_ids[1], pos);
      if (!bs_map.is_empty()) {
        auto res1 = bs_map.Get("playEnd");
        auto res2 = bs_map.Get("impression");
        playend = res1.second ? res1.first : 0;
        impression = res2.second ? res2.first : 0;
      }
      double ratio = 0;
      if (impression >= 200) {
        ratio = 1.0 * playend / impression;
      }
      if (ratio > 1) {
        ratio = 1;
      }
      AddFeature(0, ratio, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdStatisPlayendRatio);
};

REGISTER_BS_EXTRACTOR(BSExtractAdStatisPlayendRatio);

}  // namespace ad_algorithm
}  // namespace ks
