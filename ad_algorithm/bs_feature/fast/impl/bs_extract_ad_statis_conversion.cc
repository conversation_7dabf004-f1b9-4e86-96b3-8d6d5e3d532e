#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_conversion.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdStatisConversion::BSExtractAdStatisConversion() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_value);
  int bounds[9] = {0, 10, 200, 500, 1000, 2000, 10000, 100000, 500000};
  int steps[8] = {1, 2, 4, 10, 25, 50, 200, 1000};
  for (int i = 0; i <= 7; i++) {
      int step = steps[i];
      for (int j = bounds[i]; j < bounds[i+1]; j += step) {
          bucketBound.push_back(j);
      }
  }
  bucketBound.push_back(bounds[8]);
}

void BSExtractAdStatisConversion::Extract(const BSLog& bslog,
                                  size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
  if (has_ad_dsp_info) {
    auto key_column_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_key;
    auto value_column_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_value;
    BSMapField<absl::string_view, int64_t> item_statis_count(*bs, key_column_id, value_column_id, pos);
    if (item_statis_count.size() == 0) {
      return;
    }

    auto iter = item_statis_count.Get("conversion");
    int conversion_bucket = (iter.second) ? getBucketValue(iter.first, 500000) : 0;
    AddFeature(GetFeature(FeaturePrefix::PHOTO_AD_STATIS_CONVERSION,
                          (uint64_t)conversion_bucket), 1.0, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
