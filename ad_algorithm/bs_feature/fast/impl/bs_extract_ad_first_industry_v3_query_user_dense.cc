#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_query_user_dense.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdFirstIndustryV3QueryUserDense::BSExtractAdFirstIndustryV3QueryUserDense()
    : BSFastFeature(FeatureType::DENSE_USER) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_468);
}

void BSExtractAdFirstIndustryV3QueryUserDense::Extract(const BSLog& bslog, size_t pos,
                                                       std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_key_468 = BSFieldEnum::adlog_context_info_common_attr_key_468;
  BSRepeatedField<int64_t> key_468(*bs, enum_key_468);

  if (!key_468.is_empty()) {
    for (size_t idx = 0; idx < key_468.size(); idx++) {
      for (int i = 0; i < 3; ++i) {
        if (i <= (key_468.size() - 1)) {
          AddFeature(i, key_468.Get(i), result);
        } else {
          AddFeature(i, 0, result);
        }
      }
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
