#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_app_developer_name.h"

#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdDeveloperName::BSExtractAdDeveloperName() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_common_info_attr_key_2053);
}

void BSExtractAdDeveloperName::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  auto enum_key_2053 = BSFieldEnum::adlog_item_common_info_attr_key_2053;
  absl::string_view key_2053 = BSFieldHelper::GetSingular<absl::string_view>(*bs, enum_key_2053, pos);

  auto enum_key_2053_exists = BSFieldEnum::adlog_item_common_info_attr_key_2053;
  bool key_2053_exists = BSFieldHelper::HasSingular<absl::string_view>(*bs, enum_key_2053_exists, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD)) {
    if (key_2053_exists) {
      if (!key_2053.empty()) {
        AddFeature(GetFeature(FeaturePrefix::PHOTO_APP_DEVELOPER_NAME, ad_nn::bs::Hash(key_2053)), 1.0f,
                   result);
      }
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
