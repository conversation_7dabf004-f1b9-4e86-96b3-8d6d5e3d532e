#pragma once
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandCombineUserItemImpressionIndustryRealtime : public BSFastFeature {
 public:
  BSExtractBrandCombineUserItemImpressionIndustryRealtime() : BSFastFeature(FeatureType::COMBINE, 26, 26) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_user_info_user_real_time_action_real_time_dsp_action_detail_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(
        BSFieldEnum::
            adlog_user_info_user_real_time_action_real_time_dsp_action_detail_key_1_list_industry_id);
    RealtimeActionSizeHelper::fill_attr_metas(&attr_metas_);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    auto has_base_enum = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists;
    bool has_base = BSFieldHelper::GetSingular<bool>(*bs, has_base_enum, pos);
    if (!has_base) {
      return;
    }

    auto key_enum2 = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id;
    auto key_enum3 = BSFieldEnum::adlog_item_type;
    auto key_enum4 =
        BSFieldEnum::adlog_user_info_user_real_time_action_real_time_dsp_action_detail_key_1_list_industry_id;
    int action_detail_size = RealtimeActionSizeHelper::GetRealtimeActionSize<true>(bs, pos);
    if (action_detail_size > 0) {
      uint64_t ad_industry_id = BSFieldHelper::GetSingular<uint64_t>(*bs, key_enum2, pos);
      uint64_t item_type = BSFieldHelper::GetSingular<uint64_t>(*bs, key_enum3, pos);
      if (item_type == bs::ItemType::AD_BRAND || item_type == bs::ItemType::AD_DSP ||
          item_type == bs::ItemType::NATIVE_AD) {
        //  AD_ITEM_IMPRESSION = 1
        BSRepeatedField<uint64_t, true> industry_ids(*bs, key_enum4, pos);
        for (int i = 0; i < industry_ids.size() && i < 100; ++i) {
          auto industry_id = industry_ids.Get(i);
          if (industry_id != 0 && ad_industry_id != 0) {
            uint64_t id = GetFeature(COMBINE_USER_ITEM_IMPRESSION_INDUSTRY_AD_INDUSTRY_REALTIME, industry_id,
                                     ad_industry_id);
            AddFeature(id, 1.0, result);
          }
        }
      }
    }
  }

 private:
  const std::string USED_FEATURES[2] = {
      "user_info.user_real_time_action.real_time_dsp_action_detail.industry_id",
      "item.ad_dsp_info.creative.base.new_industry_id"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandCombineUserItemImpressionIndustryRealtime);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandCombineUserItemImpressionIndustryRealtime);

}  // namespace ad_algorithm
}  // namespace ks
