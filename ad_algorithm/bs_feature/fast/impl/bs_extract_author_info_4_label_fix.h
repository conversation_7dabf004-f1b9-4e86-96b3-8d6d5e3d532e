#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAuthorInfo4LabelFix : public BSFastFeatureNoPrefix {
 public:
  BSExtractAuthorInfo4LabelFix();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorInfo4LabelFix);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorInfo4LabelFix);

}  // namespace ad_algorithm
}  // namespace ks
