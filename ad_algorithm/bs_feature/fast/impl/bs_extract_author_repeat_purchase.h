#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/proto/bs_common_info_attr_enum.pb.h"
namespace ks {
namespace ad_algorithm {
template <BSFieldEnum author_attr_name>
class BSExtractAuthorRepeatPurchase : public BSFastFeatureNoPrefix {
 public:
  BSExtractAuthorRepeatPurchase() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
    attr_metas_.emplace_back(author_attr_name_);
  }
  virtual void Extract(const BSLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = adlog.GetBS();
    if (bs == nullptr) {
      return;
    }
    uint64_t value = BSFieldHelper::GetSingular<uint64_t>(*bs, author_attr_name_, pos);
    AddFeature(value, 1.0, result);
  }

 private:
  BSFieldEnum author_attr_name_{author_attr_name};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorRepeatPurchase);
};
}  // namespace ad_algorithm
}  // namespace ks
