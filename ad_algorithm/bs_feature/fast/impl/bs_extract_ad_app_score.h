#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "ks/util/json.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdAppScore : public BSFastFeature {
 public:
  BSExtractAdAppScore() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_common_info_attr_key_2050);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);

    if (item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) {
      bool has_value = false;
      auto key_enum = BSFieldEnum::adlog_item_common_info_attr_key_2050;
      int attr = BSFieldHelper::GetSingular<int>(*bs, key_enum, pos, &has_value);
      if (has_value) {
        AddFeature(GetFeature(FeaturePrefix::PHOTO_APP_SCORE, attr), 1.0f, result);
      }
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"item.common_info_attr.APP_SCORE"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdAppScore);
};

REGISTER_BS_EXTRACTOR(BSExtractAdAppScore);

}  // namespace ad_algorithm
}  // namespace ks
