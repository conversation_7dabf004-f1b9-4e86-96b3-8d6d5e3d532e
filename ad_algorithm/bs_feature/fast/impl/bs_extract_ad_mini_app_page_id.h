#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {
class BSExtractAdMiniAppPageId : public BSFastFeature {
 public:
  BSExtractAdMiniAppPageId();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdMiniAppPageId);
};
REGISTER_BS_EXTRACTOR(BSExtractAdMiniAppPageId);
}  // namespace ad_algorithm
}  // namespace ks
