#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdPromotionType : public BSFastFeatureNoPrefix {
 public:
  BSExtractAdPromotionType() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_260001);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  const std::string USED_FEATURES[1] = {"item.ad_dsp_info.common_info_attr.PROMOTION_TYPE"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdPromotionType);
};

REGISTER_BS_EXTRACTOR(BSExtractAdPromotionType);

}  // namespace ad_algorithm
}  // namespace ks
