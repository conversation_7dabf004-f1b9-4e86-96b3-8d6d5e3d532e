#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/hash_function/city.h"
#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdIndustryGroup : public BSFastFeature {
 public:
  BSExtractAdIndustryGroup();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdIndustryGroup);
};

using BSExtractAdIndustryGroup3t = BSExtractAdIndustryGroup;

REGISTER_BS_EXTRACTOR(BSExtractAdIndustryGroup);
REGISTER_BS_EXTRACTOR(BSExtractAdIndustryGroup3t);

}  // namespace ad_algorithm
}  // namespace ks
