#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAuthorWeekGmvMaxBoolMask : public BSFastFeature {
 public:
  BSExtractAuthorWeekGmvMaxBoolMask();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  std::vector<float> segments_ = {
      0.001, 1.0,   8.0,   18.0,  28.0,  38.0,  48.0,  58.0,  68.0,
      78.0,  98.0,  108.0, 168.0, 200.0, 250.0, 300.0, 350.0, 400.0,
      500.0, 600.0, 800.,  1000,  1200,  1400,  1500};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorWeekGmvMaxBoolMask);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorWeekGmvMaxBoolMask);

}  // namespace ad_algorithm
}  // namespace ks
