#pragma once
#include <string>
#include <vector>

#include "base/hash_function/city.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_action_type.pb.h"
using std::string;

namespace ks {
namespace ad_algorithm {

class BSExtractBrandAdUriLps : public BSFastFeature {
 public:
  BSExtractBrandAdUriLps() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_uri);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    auto key_has_dsp_info = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    auto key_has_unit = BSFieldEnum::adlog_item_ad_dsp_info_unit_exists;
    auto key_has_unit_base = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists;
    auto key_has_campaign = BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists;
    auto key_has_campaign_base = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_has_dsp_info, pos);
    bool has_unit = BSFieldHelper::GetSingular<bool>(*bs, key_has_unit, pos);
    bool has_unit_base = BSFieldHelper::GetSingular<bool>(*bs, key_has_unit_base, pos);
    bool has_campaign = BSFieldHelper::GetSingular<bool>(*bs, key_has_campaign, pos);
    bool has_campaign_base = BSFieldHelper::GetSingular<bool>(*bs, key_has_campaign_base, pos);
    if (!has_ad_dsp_info || !has_unit || !has_unit_base || !has_campaign || !has_campaign_base) {
      return;
    }
    bool has_value = false;
    int64_t ocpc_action_type = BSFieldHelper::GetSingular<int64_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type, pos, &has_value);
    if (!has_value) {
      return;
    }
    if (ocpc_action_type == ::bs::kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) {
      absl::string_view uri = BSFieldHelper::GetSingular<absl::string_view>(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_uri, pos);
      if (uri.size() > 0) {
        uint64 uri_hash = base::CityHash64(uri.data(), uri.size());
        AddFeature(GetFeature(FeaturePrefix::PHOTO_BASE_URI, uri_hash), 1.0f, result);
      }
    }
  }

 private:
  const std::string USED_FEATURES[2] = {"item.ad_dsp_info.campaign.base.type",
                                        "item.ad_dsp_info.unit.base.uri"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandAdUriLps);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandAdUriLps);

}  // namespace ad_algorithm
}  // namespace ks
