#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_realtime_gmv_max_bool_mask.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/action.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {
BSExtractAuthorRealtimeGmvMaxBoolMask::BSExtractAuthorRealtimeGmvMaxBoolMask() : BSFastFeature(DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82067);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82005);
}
void BSExtractAuthorRealtimeGmvMaxBoolMask::Extract(const BSLog& bslog, size_t pos,
                                                    std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  auto enum_key_82067 = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82067;
  auto enum_key_82005 = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82005;
  absl::Span<const int64_t> key_82067_opt = get_bslog_int64_list(*bs, enum_key_82067, pos);
  absl::optional<int64_t> key_82005_opt = get_bslog_int64(*bs, enum_key_82005, pos);
  int64_t max = get_max(key_82067_opt);
  std::vector<ExtractResult> result_value =
      find_mask_from_segments_by_target_value(get_max_gmv(max, key_82005_opt), get_gmv_buckets_lining10());
  add_feature_result(result_value, result);
}
}  // namespace ad_algorithm
}  // namespace ks
