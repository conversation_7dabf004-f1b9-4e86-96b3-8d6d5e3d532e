#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_smart_matching_thres_nebula.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdSmartMatchingThresNebula::BSExtractAdSmartMatchingThresNebula()
    : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_4003);
}

void BSExtractAdSmartMatchingThresNebula::Extract(const BSLog& bslog, size_t pos,
                                                  std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size;
  int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);

  auto enum_key_4003 = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_4003;
  float key_4003 = BSFieldHelper::GetSingular<float>(*bs, enum_key_4003, pos);

  auto enum_key_4003_exists = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_4003;
  bool key_4003_exists = BSFieldHelper::HasSingular<float>(*bs, enum_key_4003_exists, pos);

  if (attr_size > 0) {
    if (key_4003_exists) {
      if (key_4003 != -1.0f) {
        AddFeature(0, key_4003, result);
      } else {
        AddFeature(0, -1.0f, result);
      }
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
