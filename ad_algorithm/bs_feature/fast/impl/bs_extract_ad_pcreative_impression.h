#pragma once
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdPcreativeImpression : public BSFastFeature {
 public:
  BSExtractAdPcreativeImpression() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_230);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    bool has_user_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_user_info) {
      auto key_attr = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_230;
      BSRepeatedField<int64_t> attr(*bs, key_attr, pos);
      if (attr.size() != 7) {
        return;
      } else {
        for (int i = 0; i < 7; ++i) {
          AddFeature(GetFeature(FeaturePrefix::PCREATIVE_CREATIVE_IMPRESSION, attr.Get(i)), 1.0f, result);
        }
      }
    }
  }

 private:
  const std::string USED_FEATURES[1] = {
      "item.ad_dsp_info.common_info_attr.PCREATIVE_SELECT_CREATIVE_IMPRESSION"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdPcreativeImpression);
};

REGISTER_BS_EXTRACTOR(BSExtractAdPcreativeImpression);

}  // namespace ad_algorithm
}  // namespace ks
