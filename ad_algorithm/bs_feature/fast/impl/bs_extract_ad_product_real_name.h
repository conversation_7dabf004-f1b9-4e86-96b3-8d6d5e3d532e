#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "ks/util/json.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdProductRealName : public BSFastFeature {
 public:
  BSExtractAdProductRealName() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1213);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info) {
      auto attr_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1213;
      absl::string_view name = ad_nn::BSFieldHelper::GetSingular<absl::string_view>(*bs, attr_id, pos);
      if (!name.empty()) {
        AddFeature(GetFeature(FeaturePrefix::PHOTO_REAL_PRODUCT_NAME, ad_nn::bs::Hash(name)), 1.0f, result);
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdProductRealName);
};

REGISTER_BS_EXTRACTOR(BSExtractAdProductRealName);

}  // namespace ad_algorithm
}  // namespace ks
