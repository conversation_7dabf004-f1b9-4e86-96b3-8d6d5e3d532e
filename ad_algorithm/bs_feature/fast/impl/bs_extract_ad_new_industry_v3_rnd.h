#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdNewIndustryV3Rnd : public BSFastFeature {
 public:
  BSExtractAdNewIndustryV3Rnd() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  const std::string USED_FEATURES[1] = {"item.ad_dsp_info.creative.base.industry_id_v3"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdNewIndustryV3Rnd);
};

REGISTER_BS_EXTRACTOR(BSExtractAdNewIndustryV3Rnd);

}  // namespace ad_algorithm
}  // namespace ks
