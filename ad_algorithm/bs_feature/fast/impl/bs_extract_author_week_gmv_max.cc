#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_week_gmv_max.h"

namespace ks {
namespace ad_algorithm {
BSExtractAuthorWeekGmvMax::BSExtractAuthorWeekGmvMax() : BSFastFeature(DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82084);
}

void BSExtractAuthorWeekGmvMax::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_key_82084 = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82084;
  float key_82084 = BSFieldHelper::GetSingular<float>(*bs, enum_key_82084, pos);

  auto enum_key_82084_exists = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82084;
  bool key_82084_exists = BSFieldHelper::HasSingular<float>(*bs, enum_key_82084_exists, pos);

  if (key_82084_exists) {
    AddFeature(0, key_82084, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
