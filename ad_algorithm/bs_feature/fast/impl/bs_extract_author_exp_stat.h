#pragma once
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAuthorExpStat : public BSFastFeature {
 public:
  BSExtractAuthorExpStat() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_live_info_live_info_author_info_explore_count_click);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_info_photo_info_author_info_explore_count_click);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_live_creative_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_explore_count_click);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_explore_count_click);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_explore_count_click);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_explore_count_click);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_live_info_live_info_author_info_explore_count_unlogin_click);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_info_photo_info_author_info_explore_count_unlogin_click);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_explore_count_unlogin_click);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_explore_count_unlogin_click);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_live_info_live_info_author_info_explore_count_like);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_fans_top_info_photo_info_author_info_explore_count_like);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_explore_count_like);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_explore_count_like);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_live_info_live_info_author_info_explore_count_follow);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_info_photo_info_author_info_explore_count_follow);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_explore_count_follow);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_explore_count_follow);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_live_info_live_info_author_info_explore_count_forward);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_info_photo_info_author_info_explore_count_forward);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_explore_count_forward);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_explore_count_forward);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_live_info_live_info_author_info_explore_count_external_view);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_info_photo_info_author_info_explore_count_external_view);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_explore_count_external_view);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_explore_count_external_view);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_live_info_live_info_author_info_explore_count_negative);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_info_photo_info_author_info_explore_count_negative);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_explore_count_negative);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_explore_count_negative);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_live_info_live_info_author_info_explore_count_long_play);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_info_photo_info_author_info_explore_count_long_play);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_explore_count_long_play);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_explore_count_long_play);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_live_info_live_info_author_info_explore_count_short_play);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_info_photo_info_author_info_explore_count_short_play);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_explore_count_short_play);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_explore_count_short_play);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_live_info_live_info_author_info_explore_count_comment);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_info_photo_info_author_info_explore_count_comment);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_explore_count_comment);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_explore_count_comment);
    bs_util.BSGetAuthorInfoExploreCountNegative.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoExploreCountUnloginClick.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoExploreCountExternalView.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoExploreCountLike.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoExploreCountShortPlay.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoExploreCountLongPlay.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoExploreCountComment.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoExploreCountFollow.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoExploreCountClick.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoExploreCountForward.fill_attr_metas(&attr_metas_);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_value = false;
    int32 explore_click_count = bs_util.BSGetAuthorInfoExploreCountClick(bs, pos, &has_value);
    int32 explore_login_click =
        explore_click_count - bs_util.BSGetAuthorInfoExploreCountUnloginClick(bs, pos, &has_value);
    int32 explore_like_count = bs_util.BSGetAuthorInfoExploreCountLike(bs, pos, &has_value);
    int32 explore_follow_count = bs_util.BSGetAuthorInfoExploreCountFollow(bs, pos, &has_value);
    int32 explore_forward_count = bs_util.BSGetAuthorInfoExploreCountForward(bs, pos, &has_value);
    int32 explore_external_view = bs_util.BSGetAuthorInfoExploreCountExternalView(bs, pos, &has_value);
    int32 explore_negative = bs_util.BSGetAuthorInfoExploreCountNegative(bs, pos, &has_value);
    int32 explore_long_play = bs_util.BSGetAuthorInfoExploreCountLongPlay(bs, pos, &has_value);
    int32 explore_short_play = bs_util.BSGetAuthorInfoExploreCountShortPlay(bs, pos, &has_value);
    int32 explore_comment = bs_util.BSGetAuthorInfoExploreCountComment(bs, pos, &has_value);
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if (!has_value) {
      return;
    }
    if (explore_login_click <= 0 || explore_click_count <= 0) {
      return;
    }
    AddFeature(
        GetFeature(FeaturePrefix::PHOTO_AUTHOR_CLICK_COUNT, static_cast<int>(log(explore_click_count + 2))),
        1.0f, result);
    AddFeature(
        GetFeature(FeaturePrefix::PHOTO_AUTHOR_LIKE_COUNT, static_cast<int>(log(explore_like_count + 2))),
        1.0f, result);
    AddFeature(
        GetFeature(FeaturePrefix::PHOTO_AUTHOR_FOLLOW_COUNT, static_cast<int>(log(explore_follow_count + 2))),
        1.0f, result);
    AddFeature(GetFeature(FeaturePrefix::PHOTO_AUTHOR_FORWARD_COUNT,
                          static_cast<int>(log(explore_forward_count + 2))),
               1.0f, result);
    AddFeature(GetFeature(FeaturePrefix::PHOTO_AUTHOR_EXTEND_VIEW_COUNT,
                          static_cast<int>(log(explore_external_view + 2))),
               1.0f, result);
    AddFeature(
        GetFeature(FeaturePrefix::PHOTO_AUTHOR_NEGATIVE_COUNT, static_cast<int>(log(explore_negative + 2))),
        1.0f, result);
    AddFeature(
        GetFeature(FeaturePrefix::PHOTO_AUTHOR_LONG_PLAY_COUNT, static_cast<int>(log(explore_long_play + 2))),
        1.0f, result);
    AddFeature(GetFeature(FeaturePrefix::PHOTO_AUTHOR_SHORT_PLAY_COUNT,
                          static_cast<int>(log(explore_short_play + 2))),
               1.0f, result);
    AddFeature(
        GetFeature(FeaturePrefix::PHOTO_AUTHOR_COMMENT_COUNT, static_cast<int>(log(explore_comment + 2))),
        1.0f, result);
    ExtractRateFeatureWithMax(FeaturePrefix::PHOTO_AUTHOR_EXP_LIKE_RATE, explore_like_count,
                              explore_login_click, 1000, 0.5, result);
    ExtractRateFeatureWithMax(FeaturePrefix::PHOTO_AUTHOR_EXP_FOLLOW_RATE, explore_follow_count,
                              explore_login_click, 1000, 0.5, result);
    ExtractRateFeatureWithMax(FeaturePrefix::PHOTO_AUTHOR_EXP_FORWARD_RATE, explore_forward_count,
                              explore_login_click, 1000, 0.5, result);
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorExpStat);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorExpStat);

}  // namespace ad_algorithm
}  // namespace ks
