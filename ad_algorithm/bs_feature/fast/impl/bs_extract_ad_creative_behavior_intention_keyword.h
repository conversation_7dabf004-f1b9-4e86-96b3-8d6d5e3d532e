#pragma once
#include <string>
#include <unordered_set>
#include <vector>

#include "base/strings/string_number_conversions.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "base/strings/string_util.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdCreativeBehaviorIntentionKeyword : public BSFastFeature {
 public:
  BSExtractAdCreativeBehaviorIntentionKeyword();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdCreativeBehaviorIntentionKeyword);
};
REGISTER_BS_EXTRACTOR(BSExtractAdCreativeBehaviorIntentionKeyword);
}  // namespace ad_algorithm
}  // namespace ks
