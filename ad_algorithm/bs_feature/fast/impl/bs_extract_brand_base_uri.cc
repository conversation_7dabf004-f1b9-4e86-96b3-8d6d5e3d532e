#include <string>
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_base_uri.h"

namespace ks {
namespace ad_algorithm {

BSExtractBrandBaseUri::BSExtractBrandBaseUri() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_uri);
}

void BSExtractBrandBaseUri::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  bool has_ad_dsp_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
  bool has_unit =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_exists, pos);
  bool has_unit_base =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists, pos);
  if (!has_ad_dsp_info || !has_unit || !has_unit_base) {
    return;
  }
  auto key_ad_unit_url = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_uri;
  absl::string_view ad_unit_url = BSFieldHelper::GetSingular<absl::string_view>(*bs, key_ad_unit_url, pos);
  if (ad_unit_url.size() > 0) {
    std::vector<std::string> tokens;
    base::SplitString(std::string(ad_unit_url), std::string("?"), &tokens);
    if (tokens.size() > 0) {
      uint64 uri_hash = base::CityHash64(tokens[0].c_str(), tokens[0].length());
      AddFeature(GetFeature(FeaturePrefix::PHOTO_BASE_URI, uri_hash), 1.0f, result);
    }
  }
}

}  // namespace ad_algorithm
}  // namespace ks
