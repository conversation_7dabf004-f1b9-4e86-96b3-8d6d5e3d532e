#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_conversion_ratio.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdStatisConversionRatio::
        BSExtractAdStatisConversionRatio() : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_value);
}

void BSExtractAdStatisConversionRatio::Extract(const BSLog& bslog,
                                  size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
  if (has_ad_dsp_info) {
    auto key_column_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_key;
    auto value_column_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_value;
    BSMapField<absl::string_view, int64_t> item_statis_count(*bs, key_column_id, value_column_id, pos);
    if (item_statis_count.size() == 0) {
      return;
    }

    auto conversion_iter = item_statis_count.Get("conversion");
    auto impression_iter = item_statis_count.Get("impression");
    int conversion = (conversion_iter.second) ? conversion_iter.first : 0;
    int impression = (impression_iter.second) ? impression_iter.first : 0;
    double cvr = 0;
    if (impression >= 800) {
      cvr = 1.0 * conversion / impression;
    }
    if (cvr > 1) {
      cvr = 1;
    }
    AddFeature(0, cvr, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
