#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_fans_count.h"

namespace ks {
namespace ad_algorithm {

BSExtractAuthorFansCount::BSExtractAuthorFansCount() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_attribute_fans_count);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_fans_top_info_photo_info_author_info_attribute_fans_count);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_item_nature_photo_info_photo_info_author_info_attribute_fans_count);
  bs_util.BSHasPhotoInfoAuthorInfo.fill_attr_metas(&attr_metas_);
  bs_util.BSHasPhotoInfoAuthorInfoAttribute.fill_attr_metas(&attr_metas_);
  bs_util.BSGetPhotoInfoAuthorInfoAttributeFansCount.fill_attr_metas(&attr_metas_);
}

void BSExtractAuthorFansCount::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  bool has_author_info = bs_util.BSHasPhotoInfoAuthorInfo(bs, pos);
  if (!has_author_info) {
    return;
  }
  bool has_attribute = bs_util.BSHasPhotoInfoAuthorInfoAttribute(bs, pos);
  if (!has_attribute) {
    return;
  }

  int32_t fans_count = bs_util.BSGetPhotoInfoAuthorInfoAttributeFansCount(bs, pos);
  AddFeature(static_cast<int>(log(fans_count + 2)), 1.0f, result);
}

}  // namespace ad_algorithm
}  // namespace ks
