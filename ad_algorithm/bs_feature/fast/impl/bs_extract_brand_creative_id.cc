#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_creative_id.h"

namespace ks {
namespace ad_algorithm {

BSExtractBrandCreativeId::BSExtractBrandCreativeId() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_id);
}

void BSExtractBrandCreativeId::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  bool has_ad_dsp_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
  bool has_creative =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_exists, pos);

  if ((item_type == bs::ItemType::AD_BRAND ||
       item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info &&
      has_creative) {
    auto creative_id = BSFieldHelper::GetSingular<uint64_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_id, pos);
    AddFeature(GetFeature(FeaturePrefix::CREATIVE_ID, creative_id), 1.0f, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
