#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAppStartType : public BSFastFeature {
 public:
  BSExtractAppStartType() : BSFastFeature(FeatureType::USER) {}

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    if (bslog.has_context() && bslog.context().info_common_attr_size() > 0) {
      uint32_t max_loop = 100;
      uint32_t loop_cnt = 0;
      for (auto& attr : bslog.context().info_common_attr()) {
        loop_cnt++;
        if (attr.name_value() == ::bs::kuaishou::ad::ContextInfoCommonAttr_Name_APP_START_TYPE) {
          AddFeature(GetFeature(FeaturePrefix::USER_CONTEXT_APP_START_TYPE, attr.int_value()), 1.0, result);
        }
        if (loop_cnt >= max_loop) {
          break;
        }
      }
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"context.info_common_attr.APP_START_TYPE"};

  DISALLOW_COPY_AND_ASSIGN(BSExtractAppStartType);
};

REGISTER_BS_EXTRACTOR(BSExtractAppStartType);

}  // namespace ad_algorithm
}  // namespace ks
