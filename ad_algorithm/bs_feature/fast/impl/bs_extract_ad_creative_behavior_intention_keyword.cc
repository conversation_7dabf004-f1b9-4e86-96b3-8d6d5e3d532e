#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_creative_behavior_intention_keyword.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdCreativeBehaviorIntentionKeyword::BSExtractAdCreativeBehaviorIntentionKeyword()
    : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5552_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5552_value);
}

void BSExtractAdCreativeBehaviorIntentionKeyword::Extract(const BSLog& bslog, size_t pos,
                                                          std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size;
  int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  auto enum_key_5552_key = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5552_key;
  auto enum_key_5552_value = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5552_value;
  BSMapField<int64_t, float> key_5552(*bs, enum_key_5552_key, enum_key_5552_value, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && info_exists &&
      attr_size > 0) {
    if (!key_5552.is_empty()) {
      for (size_t idx = 0; idx < key_5552.size(); idx++) {
        AddFeature(GetFeature(FeaturePrefix::AD_CREATIVE_BEHAVIOR_INTENTION_KEYWORD, key_5552.GetKey(idx)),
                   1.0f, result);
      }
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
