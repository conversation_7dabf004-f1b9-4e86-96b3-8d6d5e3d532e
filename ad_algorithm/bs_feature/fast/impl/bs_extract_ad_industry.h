#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdIndustry : public BSFastFeature {
 public:
  BSExtractAdIndustry() : BSFastFeature(FeatureType::ITEM) {}

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    if (bslog.item_size() <= pos) {
      return;
    }
    auto& item = bslog.item(pos);
    if ((item.type() == bs::ItemType::AD_DSP || item.type() == bs::ItemType::NATIVE_AD) &&
        item.has_ad_dsp_info() && item.ad_dsp_info().has_creative() &&
        item.ad_dsp_info().creative().has_base()) {
      AddFeature(GetFeature(FeaturePrefix::PHOTO_CREATIVE_INDUSTRY_ID,
                            item.ad_dsp_info().creative().base().industry_id()),
                 1.0f, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdIndustry);
};

REGISTER_BS_EXTRACTOR(BSExtractAdIndustry);

}  // namespace ad_algorithm
}  // namespace ks
