/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pri_msg_day_session_round_dense.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdPriMsgDaySessionRoundDense::BSExtractAdPriMsgDaySessionRoundDense()
    : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_label_info_label_infos_key_20000110);
}
void BSExtractAdPriMsgDaySessionRoundDense::Extract(const BSLog& bslog, size_t pos,
                                                    std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000110, pos);
  auto x2 = cast_to_float(x1);
  add_feature_result(x2, 1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
