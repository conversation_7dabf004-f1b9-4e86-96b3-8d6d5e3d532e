#pragma once
#include <math.h>
#include <time.h>

#include <string>
#include <vector>

#include "base/hash_function/city.h"
#include "base/strings/string_printf.h"
#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
namespace ks {
namespace ad_algorithm {

template <BSFieldEnum item_attr_name>
class BSExtractCombineApplistSdpaInfo : public BSFastFeature {
 public:
  BSExtractCombineApplistSdpaInfo();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  BSFieldEnum item_attr_name_{item_attr_name};
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineApplistSdpaInfo);
};

}  // namespace ad_algorithm
}  // namespace ks
