#pragma once
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdAccountType : public BSFastFeature {
 public:
  BSExtractAdAccountType();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdAccountType);
};

REGISTER_BS_EXTRACTOR(BSExtractAdAccountType);

}  // namespace ad_algorithm
}  // namespace ks
