#pragma once
#include <algorithm>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/strings/string_number_conversions.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {
template <int max_list_len,
          int tag,                               // AuthorId : 0, goods_cluster : 1, goods_cate : 2
          BSFieldEnum no = BSFieldEnum::invalid  // name_value, 取 AuthorId 时不需要, 默认值 invalid = -1
          >
class BSExtractAdMmuCommercePhotoSimActionMatchCnt : public BSFastFeatureNoPrefix {
 public:
  BSExtractAdMmuCommercePhotoSimActionMatchCnt() : BSFastFeatureNoPrefix(FeatureType::DENSE_COMBINE) {
    AddAttrMetas();
  }
  explicit BSExtractAdMmuCommercePhotoSimActionMatchCnt(size_t index)
      : BSFastFeatureNoPrefix(FeatureType::DENSE_COMBINE) {
    feature_index_ = index;
    AddAttrMetas();
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    bool has_user_info =
        BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
    if (!has_user_info) {
      return;
    }
    if (!bs_util.BSHasPhotoInfo(bs, pos)) {
      return;
    }

    int64_t target_item_attr = 0;
    if (tag == 0) {  //  author id
      bool has_live_info = bs_util.BSHasLiveInfo(bs, pos);
      bool has_author_info_id = false;
      if (!has_live_info) {
        target_item_attr = bs_util.BSGetPhotoInfoAuthorInfoId(bs, pos, &has_author_info_id);
      } else {
        target_item_attr = bs_util.BSGetLiveInfoAuthorInfoId(bs, pos, &has_author_info_id);
      }
      if (!has_author_info_id) {
        return;
      }
    } else {  // tag and cluster
      int32_t common_info_attr_size = BSFieldHelper::GetSingular<int32_t>(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size, pos);
      if (common_info_attr_size <= 0) {
        return;
      }
      target_item_attr = BSFieldHelper::GetSingular<int64_t>(*bs, no, pos);
    }
    if (target_item_attr <= 0) {
      return;
    }
    auto p_items = bslog.colossus_reco_photo();
    if (p_items == nullptr) {
      return;
    }

    uint64_t time = BSFieldHelper::GetSingular<uint64_t, true>(*bs, BSFieldEnum::adlog_time, pos) / 1000;
    const auto& items = *(p_items);
    int64_t counter = 0;
    const uint32_t day_unit = 12 * 60 * 60;

    const int bucket_size = 7;
    std::vector<int> match_item_cnt(bucket_size, 0);

    for (int i = items.size() - 1; i >= 0; --i) {
      auto& item = items[i];
      if (item.timestamp > time) {
        continue;
      }

      int id = -1;
      // 获取 id
      switch (tag) {
        case 0:
          id = item.author_id;
          break;
        case 1:
          id = *(reinterpret_cast<const uint32_t*>(&(item.user_longitude))) / 1000000;
          break;
        case 2:
          id = (*(reinterpret_cast<const uint32_t*>(&(item.user_longitude))) % 1000000) / 100;
          break;
      }
      if (target_item_attr != id) {
        continue;
      }

      uint64 time_diff = time - item.timestamp;
      int time_idx = bucket_size - 1;

      if (time_diff < 1 * day_unit) {
        time_idx = 0;
      } else if (time_diff < 7 * day_unit) {
        time_idx = 1;
      } else if (time_diff < 15 * day_unit) {
        time_idx = 2;
      } else if (time_diff < 30 * day_unit) {
        time_idx = 3;
      } else if (time_diff < 60 * day_unit) {
        time_idx = 4;
      } else if (time_diff < 90 * day_unit) {
        time_idx = 5;
      }

      match_item_cnt[time_idx] += 1;
      counter++;
      if (counter >= max_list_len) {
        break;
      }
    }
    for (int i = 1; i < bucket_size; ++i) {
      match_item_cnt[i] += match_item_cnt[i - 1];
    }
    for (int i = 0; i < bucket_size; ++i) {
      AddFeature(i, log(1.0 + match_item_cnt[i]), result);
    }
  }

 private:
  size_t feature_index_;
  const std::string USED_FEATURES[1] = {"time"};
  void AddAttrMetas() {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_time);
    attr_metas_.emplace_back(no);
    bs_util.BSHasPhotoInfo.fill_attr_metas(&attr_metas_);
    bs_util.BSHasLiveInfo.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoId.fill_attr_metas(&attr_metas_);
    bs_util.BSGetLiveInfoAuthorInfoId.fill_attr_metas(&attr_metas_);
  }
};

using BSExtractAdMmuCommercePhotoSimActionMatchAuthorIdCnt =
    BSExtractAdMmuCommercePhotoSimActionMatchCnt<100, 0>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuCommercePhotoSimActionMatchAuthorIdCnt);

using BSExtractAdMmuCommercePhotoSimActionMatchMaxTagCnt = BSExtractAdMmuCommercePhotoSimActionMatchCnt<
    100, 1, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25223>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuCommercePhotoSimActionMatchMaxTagCnt);

using BSExtractAdMmuCommercePhotoSimActionMatchClusterCnt = BSExtractAdMmuCommercePhotoSimActionMatchCnt<
    100, 2, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25225>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuCommercePhotoSimActionMatchClusterCnt);

}  // namespace ad_algorithm
}  // namespace ks
