#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdStatisConversion : public BSFastFeature {
 public:
  BSExtractAdStatisConversion();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);
  inline int getBucketValue(double originValue, int maxValue) {
      int i = 0;
      for (i = 0; i < bucketBound.size(); i++) {
         if (bucketBound[i] >= maxValue)
            return i;
         if (bucketBound[i] >= originValue) {
            return i;
         }
      }
      return i;
  }
 private:
  std::vector<double> bucketBound;
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdStatisConversion);
};

REGISTER_BS_EXTRACTOR(BSExtractAdStatisConversion);

}  // namespace ad_algorithm
}  // namespace ks
