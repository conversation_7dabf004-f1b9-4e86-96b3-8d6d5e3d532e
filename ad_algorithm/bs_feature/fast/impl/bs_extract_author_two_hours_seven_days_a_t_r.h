#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/impl/extract_item_dense_author_two_hours_seven_days_atr.dark
class BSExtractAuthorTwoHoursSevenDaysATR : public BSFastFeature {
 public:
  BSExtractAuthorTwoHoursSevenDaysATR();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorTwoHoursSevenDaysATR);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorTwoHoursSevenDaysATR);
}  // namespace ad_algorithm
}  // namespace ks
