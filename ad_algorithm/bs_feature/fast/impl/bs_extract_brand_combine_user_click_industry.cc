#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_click_industry.h"

namespace ks {
namespace ad_algorithm {

BSExtractBrandCombineUserClickIndustry::
           BSExtractBrandCombineUserClickIndustry() : BSFastFeature(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_dsp_action_detail_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_dsp_action_detail_key_2_list_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_dsp_action_detail_key_2_list_industry_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
}

void BSExtractBrandCombineUserClickIndustry::Extract(
             const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  bool has_ad_dsp_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
  bool has_creative =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_exists, pos);
  bool has_creative_base =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists, pos);
  if (!has_ad_dsp_info || !has_creative || !has_creative_base) {
    return;
  }

  bool has_user_info_exists =
      BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
  uint64_t detail_size = BSFieldHelper::GetSingular<uint64_t, true>(
      *bs, BSFieldEnum::adlog_user_info_ad_dsp_action_detail_size, pos);

  if (!has_user_info_exists || detail_size <= 0) return;
  bool key_exists = BSFieldHelper::GetSingular<bool, true>(
      *bs, BSFieldEnum::adlog_user_info_ad_dsp_action_detail_key_2_exists, pos);
  if (!key_exists) return;

  uint64_t ad_industry_id = BSFieldHelper::GetSingular<uint64_t>(
            *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id, pos);

  auto key_industry_id_list = BSFieldEnum::adlog_user_info_ad_dsp_action_detail_key_2_list_industry_id;
  BSRepeatedField<uint64_t, true> industry_id_list(*bs, key_industry_id_list, pos);
  for (int i = 0; i < industry_id_list.size(); ++i) {
    if (i < max_num_dsp_info) {
      if (industry_id_list.Get(i) != 0 && ad_industry_id != 0) {
        uint64_t id = GetFeature(COMBINE_USER_CLICK_INDUSTRY_AD_INDUSTRY, industry_id_list.Get(i),
                                  ad_industry_id);
        AddFeature(id, 1.0, result);
      }
    }
  }
}

}  // namespace ad_algorithm
}  // namespace ks
