#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdNewIndustry : public BSFastFeature {
 public:
  BSExtractAdNewIndustry() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
    auto key_creative_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_exists;
    bool has_creative = BSFieldHelper::GetSingular<bool>(*bs, key_creative_exists, pos);
    auto key_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists;
    bool has_base = BSFieldHelper::GetSingular<bool>(*bs, key_base_exists, pos);

    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD ||
         item_type == bs::ItemType::AD_BRAND) &&
        has_ad_dsp_info && has_creative && has_base) {
      auto key_industry_id = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id;
      int64_t new_industry_id = BSFieldHelper::GetSingular<int>(*bs, key_industry_id, pos);
      AddFeature(GetFeature(FeaturePrefix::PHOTO_CREATIVE_NEW_INDUSTRY_ID, new_industry_id), 1.0f, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdNewIndustry);
};

REGISTER_BS_EXTRACTOR(BSExtractAdNewIndustry);

}  // namespace ad_algorithm
}  // namespace ks
