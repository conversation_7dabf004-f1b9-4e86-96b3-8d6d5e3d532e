#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_id_slot.h"

namespace ks {
namespace ad_algorithm {
BSExtractAuthorIdSlot::BSExtractAuthorIdSlot() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
  BSGetPhotoInfoAuthorInfoId.fill_attr_metas(&attr_metas_);
  BSHasPhotoInfoAuthorInfo.fill_attr_metas(&attr_metas_);
  BSHasPhotoInfo.fill_attr_metas(&attr_metas_);
}

void BSExtractAuthorIdSlot::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  if (!BSHasPhotoInfo(bs, pos) || !BSHasPhotoInfoAuthorInfo(bs, pos)) {
    return;
  }
  AddFeature(BSGetPhotoInfoAuthorInfoId(bs, pos), 1.0f, result);
}
}  // namespace ad_algorithm
}  // namespace ks
