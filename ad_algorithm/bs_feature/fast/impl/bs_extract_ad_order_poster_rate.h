#pragma once
#include <string>
#include <vector>

#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdOrderPosterRate : public BSFastFeature {
 public:
  BSExtractAdOrderPosterRate();

  void AddRatio(const BSMapField<int64_t, int64_t>& map, int fenzi_index, int fenmu_index,
                std::vector<float>* rate_vec);

  void addCommonFeature(const BSMapField<int64_t, int64_t>& map, std::vector<float>* rate_vec);

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdOrderPosterRate);
};

REGISTER_BS_EXTRACTOR(BSExtractAdOrderPosterRate);

}  // namespace ad_algorithm
}  // namespace ks
