#pragma once
#include <string>
#include <vector>

#include "base/strings/string_number_conversions.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_util.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"
namespace ks {
namespace ad_algorithm {

// 粉丝特征细化, 数据源含直播数据; 数据 ~ 1-2h 延迟
// 1）是否是粉丝；2）是谁的粉丝；3）加粉时间；4）加粉渠道(hold)，action detail 里面的 photo_id 为空
// 多种特征共享 nn weight，目前没有拆分 prefix；
class BSExtractCombineAuthorFansDetail : public BSFastFeatureNoPrefix {
 public:
  BSExtractCombineAuthorFansDetail() : BSFastFeatureNoPrefix(FeatureType::COMBINE) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_action_detail_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_action_detail_follow_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_action_detail_follow_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_action_detail_follow_action_time);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_time);
    bs_util.BSGetAuthorInfoId.fill_attr_metas(&attr_metas_);
    bs_util.BSHasAuthorInfo.fill_attr_metas(&attr_metas_);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    bool has_author_info = bs_util.BSHasAuthorInfo(bs, pos);
    if (!has_author_info) {
      return;
    }

    const int64_t target_author_id = bs_util.BSGetAuthorInfoId(bs, pos);

    // bool has_user_info =BSFieldHelper::GetSingular<bool, true>(*bs,
    //         BSFieldEnum::adlog_user_info_exists, pos);
    // if (!has_user_info) {
    //   return;
    // }

    // bool has_action_detail = BSFieldHelper::GetSingular<bool, true>(*bs,
    //         BSFieldEnum::adlog_user_info_action_detail_exists, pos);
    // if (!has_action_detail) {
    //   return;
    // }

    int follow_num = BSFieldHelper::GetSingular<int64_t, true>(
        *bs, BSFieldEnum::adlog_user_info_action_detail_follow_size, pos);

    BSRepeatedField<int64_t, true> follow_info_ids(*bs, BSFieldEnum::adlog_user_info_action_detail_follow_id,
                                                   pos);
    BSRepeatedField<uint64_t, true> follow_info_time_list(
        *bs, BSFieldEnum::adlog_user_info_action_detail_follow_action_time, pos);

    uint64_t cur_time = BSFieldHelper::GetSingular<uint64_t, true>(*bs, BSFieldEnum::adlog_time, pos);
    int is_followed = 0;
    // 不匹配则时匹配的具体 author_id 为 author_id 的赋值
    int64_t matched_author_id = -1 * target_author_id;
    int gap_hour_dis_rs = 2;
    for (int i = 0; i < follow_num; i++) {
      const int64_t follow_info_id = follow_info_ids.Get(i);
      if (follow_info_id == target_author_id) {
        is_followed = 1;
        matched_author_id = target_author_id;

        double gap_hour = (cur_time - follow_info_time_list.Get(i)) / (3600.0 * 1000);
        if (gap_hour < 0.0) {
          gap_hour = 0.0;
        }
        // 离散化, 1 年约 28 个分桶; 与其他特征的值域分开
        gap_hour_dis_rs = 3 + floor(3 * log(1.0 + gap_hour));
        break;
      }
    }

    AddFeature(is_followed, 1.0, result);
    AddFeature(matched_author_id, 1.0, result);
    AddFeature(gap_hour_dis_rs, 1.0, result);
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineAuthorFansDetail);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineAuthorFansDetail);

}  // namespace ad_algorithm
}  // namespace ks
