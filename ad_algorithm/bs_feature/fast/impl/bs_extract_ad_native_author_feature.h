#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/proto/bs_common_info_attr_enum.pb.h"

namespace ks {
namespace ad_algorithm {
using CommonInfoAttr = ::bs::kuaishou::ad::CommonInfoAttr;

template <int no>
class BSExtractAdNativeTag : public BSFastFeatureNoPrefix {
 public:
  BSExtractAdNativeTag() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);

    BSGetItemAdDspInfoPhotoInfoCommonInfoAttrNo.fill_attr_metas(&attr_metas_);
    BSHasItemAdDspInfoPhotoInfoCommonInfoAttrNo.fill_attr_metas(&attr_metas_);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

    auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size;
    int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);

    auto enum_photo_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists;
    bool photo_info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_photo_info_exists, pos);

    auto enum_item_type = BSFieldEnum::adlog_item_type;
    int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

    int64_t attr_no = BSGetItemAdDspInfoPhotoInfoCommonInfoAttrNo(bs, pos);

    if ((item_type == bs::ItemType::AD_DSP) && info_exists && photo_info_exists && attr_size > 0) {
      if (BSHasItemAdDspInfoPhotoInfoCommonInfoAttrNo(bs, pos)) {
        AddFeature(attr_no, 1.0, result);
      }
    }
  }

 private:
  BSFixedCommonInfo<int64_t> BSGetItemAdDspInfoPhotoInfoCommonInfoAttrNo{
      "adlog.item.ad_dsp_info.photo_info.common_info_attr", no};
  BSHasFixedCommonInfoImpl<int64_t, false> BSHasItemAdDspInfoPhotoInfoCommonInfoAttrNo{
      "adlog.item.ad_dsp_info.photo_info.common_info_attr", no};

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdNativeTag);
};

using BSExtractAdNativeAuthorId = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_ID>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorId);

using BSExtractAdNativeAuthorAge = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_AGE>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorAge);

using BSExtractAdNativeAuthorGender = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_GENDER>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorGender);

using BSExtractAdNativeAuthorClass1 = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_FIRST_CLASS>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorClass1);

using BSExtractAdNativeAuthorClass2 = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_SECOND_CLASS>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorClass2);

using BSExtractAdNativeAuthorCityLevel = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_CITY_LEVEL>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorCityLevel);

using BSExtractAdNativeAuthorProvince = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_PROVINCE>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorProvince);

using BSExtractAdNativeAuthorCity = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_CITY>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorCity);

using BSExtractAdNativeAuthorFansNum = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_FANS_NUM>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorFansNum);

using BSExtractAdNativeAuthorActive = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_ACTIVE_LEVEL>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorActive);

using BSExtractAdNativeAuthorVerType = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_VER_TYPE>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorVerType);

using BSExtractAdNativeAuthorIsSigned = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_IS_SIGNED>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorIsSigned);

using BSExtractAdNativeAuthorAgeIsSeller = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_IS_SELLER>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorAgeIsSeller);

using BSExtractAdNativeAuthorPhoto7d = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_PHOTO_CNT_7D_FRE>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorPhoto7d);

using BSExtractAdNativeAuthorPhoto14d = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_PHOTO_CNT_14D>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorPhoto14d);

using BSExtractAdNativeAuthorPhoto30d = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_PHOTO_CNT_30D>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorPhoto30d);

using BSExtractAdNativeAuthorPhoto90d = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_PHOTO_CNT_90D>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorPhoto90d);

using BSExtractAdNativeAuthorLive90d = BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_LIVE_CNT_30D>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorLive90d);

using BSExtractAdNativeAuthorPoiPhotoCnt =
    BSExtractAdNativeTag<CommonInfoAttr::NATIVE_AUTHOR_POI_PHOTO_CNT_TD>;
REGISTER_BS_EXTRACTOR(BSExtractAdNativeAuthorPoiPhotoCnt);

}  // namespace ad_algorithm
}  // namespace ks
