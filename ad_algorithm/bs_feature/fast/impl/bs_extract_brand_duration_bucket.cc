#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_duration_bucket.h"
namespace ks {
namespace ad_algorithm {

BSExtractBrandDurationBucket::BSExtractBrandDurationBucket() : BSFastFeature(FeatureType::ITEM) {
  bs_util.BSGetPhotoInfoPhotoAttributeDurationMs.fill_attr_metas(&attr_metas_);
  bs_util.BSHasPhotoInfoPhotoAttribute.fill_attr_metas(&attr_metas_);
}

void BSExtractBrandDurationBucket::Extract(
            const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  bool has_attribute = bs_util.BSHasPhotoInfoPhotoAttribute(bs, pos);
  if (!has_attribute) return;
  int duration = bs_util.BSGetPhotoInfoPhotoAttributeDurationMs(bs, pos);
  uint64_t duration_bucket  =  10;
  if (duration < 0) {
    duration_bucket = 0;
  } else if (duration == 0) {
    duration_bucket = 1;
  } else if (duration <= 3000) {
    duration_bucket = 2;
  } else if (duration <= 7500) {
    duration_bucket = 3;
  } else if (duration <= 13000) {
    duration_bucket = 4;
  } else if (duration <= 17000) {
    duration_bucket = 5;
  } else if (duration <= 23000) {
    duration_bucket = 6;
  } else if (duration <= 21000) {
    duration_bucket = 7;
  } else if (duration <= 61000) {
    duration_bucket = 8;
  } else if (duration <= 12000) {
    duration_bucket = 9;
  }
  AddFeature(GetFeature(FeaturePrefix::PHOTO_DURATION, duration_bucket), 1.0f, result);
}

}  // namespace ad_algorithm
}  // namespace ks
