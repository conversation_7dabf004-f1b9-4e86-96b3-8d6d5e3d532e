#pragma once
#include <vector>
#include "base/hash_function/city.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_base/src/hash/hash.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandBaseUri : public BSFastFeature {
 public:
  BSExtractBrandBaseUri();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandBaseUri);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandBaseUri);

}  // namespace ad_algorithm
}  // namespace ks
