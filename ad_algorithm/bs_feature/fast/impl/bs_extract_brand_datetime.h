#pragma once
#include <string>
#include <vector>
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
namespace ks {
namespace ad_algorithm {

class BSExtractBrandDatetime : public BSFastFeature {
 public:
  BSExtractBrandDatetime();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandDatetime);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandDatetime);

}  // namespace ad_algorithm
}  // namespace ks
