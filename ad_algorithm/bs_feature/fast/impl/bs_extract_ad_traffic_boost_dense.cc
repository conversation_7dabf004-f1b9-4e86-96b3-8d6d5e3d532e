#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_traffic_boost_dense.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdTrafficBoostDense::BSExtractAdTrafficBoostDense() : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_53000);
}

void BSExtractAdTrafficBoostDense::Extract(const BSLog& bslog, size_t pos,
                                           std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size;
  int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  int account_type = 0;

  auto enum_key_53000 = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_53000;
  int64_t key_53000 = BSFieldHelper::GetSingular<int64_t>(*bs, enum_key_53000, pos);

  auto enum_key_53000_exists = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_53000;
  bool key_53000_exists = BSFieldHelper::HasSingular<int64_t>(*bs, enum_key_53000_exists, pos);

  if (item_type == bs::ItemType::AD_DSP && info_exists && attr_size > 0) {
    if (key_53000_exists) {
      account_type = key_53000;
      if (key_53000 == 4 || key_53000 == 12) {
        // 1. 流量助推
        AddFeature(0, 1, result);
      } else {
        // 2. 其他
        AddFeature(0, 0, result);
      }
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
