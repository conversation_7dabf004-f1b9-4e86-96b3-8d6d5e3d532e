#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_text_feature_bert_cluster.h"

namespace ks {
namespace ad_algorithm {

BSExtractBrandTextFeatureBertCluster::BSExtractBrandTextFeatureBertCluster() : BSFastFeature(ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_530);
}

void BSExtractBrandTextFeatureBertCluster::Extract(
     const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size;
  int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);
  if (info_exists && attr_size > 0 &&
        (item_type == bs::ItemType::AD_BRAND
         || item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD)) {
    bool has_value = false;
    int32_t cluster = BSFieldHelper::GetSingular<int32_t>(*bs,
                      BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_530, pos, &has_value);
    if (has_value) {
      AddFeature(GetFeature(FeaturePrefix::PHOTO_TEXT_BERT_CLUSTER, cluster), 1.0f, result);
    }
  }
}

}  // namespace ad_algorithm
}  // namespace ks
