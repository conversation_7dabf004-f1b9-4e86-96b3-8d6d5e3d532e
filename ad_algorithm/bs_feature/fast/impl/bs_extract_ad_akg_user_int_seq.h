#pragma once
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdAkgUserIntSeq : public BSFastFeature {
 public:
  BSExtractAdAkgUserIntSeq() : BSFastFeature(USER) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_48001);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    BSRepeatedField<int64_t> attr(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_48001, pos);

    uint32_t num = 0;
    double value = 1.0;
    if (attr.is_empty()) {
      return;
    }
    for (int i = 0; i < attr.size(); ++i) {
      AddFeature(GetFeature(FeaturePrefix::AD_AKG_USER_INT_SEQ, attr.Get(i)), value, result);
      ++num;
      if (num == ID_MAX) {
        break;
      }
    }
  }

 private:
  const uint32_t ID_MAX = 30;
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdAkgUserIntSeq);
};

REGISTER_BS_EXTRACTOR(BSExtractAdAkgUserIntSeq);

}  // namespace ad_algorithm
}  // namespace ks
