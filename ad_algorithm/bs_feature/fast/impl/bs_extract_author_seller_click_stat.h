#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_common_info_attr_feature.h"
namespace ks {
namespace ad_algorithm {

class BSExtractMerchantAuthorClickStat : public BSCommonInfoAttrFeature {
 public:
  BSExtractMerchantAuthorClickStat();

  inline uint64_t get_sign(uint64_t key, uint64_t value) {
    return (key << value_bits_) | (value & value_mask_);
  }

  inline int GetValue(float value, float scale, bool logarithm) {
    value = logarithm ? log(value + 1) : value;
    return (int)(value * scale);
  }

  void ExtractCommonAttr(const ad_nn::SampleInterface& sample, const std::vector<int32_t>& attr_ids,
                         size_t pos, std::vector<ExtractResult>* result) override;

  bool InitInternal(const base::Json& config) override { return true; }

  bool CheckCommonAttr(const ad_nn::SampleInterface& sample, const std::vector<int32_t>& attr_ids, size_t pos,
                       const std::vector<ExtractResult>& result) override {
    return true;
  }

 private:
  int value_bits_;
  uint64_t value_mask_;
  DISALLOW_COPY_AND_ASSIGN(BSExtractMerchantAuthorClickStat);
};

REGISTER_BS_EXTRACTOR(BSExtractMerchantAuthorClickStat);
}  // namespace ad_algorithm
}  // namespace ks
