#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_play5s.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdStatisPlay5s::BSExtractAdStatisPlay5s() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_value);
  int bounds[6] = {0, 1000, 10000, 100000, 1000000, 10000000};
  int steps[5] = {25, 200, 1000, 5000, 100000};
  for (int i = 0; i <= 4; i++) {
      int step = steps[i];
      for (int j = bounds[i]; j < bounds[i+1]; j += step) {
          bucketBound.push_back(j);
      }
  }
  bucketBound.push_back(bounds[5]);
}

void BSExtractAdStatisPlay5s::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
  if (has_ad_dsp_info) {
    auto key_column_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_key;
    auto value_column_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_value;
    BSMapField<absl::string_view, int64_t> item_statis_count(*bs, key_column_id, value_column_id, pos);
    if (item_statis_count.size() == 0) {
      return;
    }

    auto iter = item_statis_count.Get("play5s");
    int play5s_bucket = (iter.second) ? getBucketValue(iter.first, 10000000) : 0;
    AddFeature(GetFeature(FeaturePrefix::PHOTO_AD_STATIS_PLAY5s, (uint64_t)play5s_bucket), 1.0, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
