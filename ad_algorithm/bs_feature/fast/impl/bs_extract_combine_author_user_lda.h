#pragma once
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractCombineAuthorUserLda : public BSFastFeature {
 public:
  BSExtractCombineAuthorUserLda() : BSFastFeature(FeatureType::COMBINE, 26, 16, 10) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_std_topic_id_weight_weight);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_std_topic_id_weight_id);
    bs_util.BSHasPhotoInfoAuthorInfo.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoId.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoAttributeFansCount.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoAttributeUploadCount.fill_attr_metas(&attr_metas_);
    bs_util.BSHasPhotoInfoAuthorInfoAttribute.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoAttributeGender.fill_attr_metas(&attr_metas_);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_user_info =
        BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
    bool has_author_info = bs_util.BSHasPhotoInfoAuthorInfo(bs, pos);
    if (has_author_info) {
      bool has_attribute = bs_util.BSHasPhotoInfoAuthorInfoAttribute(bs, pos);
      if (!has_attribute) {
        return;
      }
      int64_t author_id = bs_util.BSGetPhotoInfoAuthorInfoId(bs, pos);
      uint32 fans_count = bs_util.BSGetPhotoInfoAuthorInfoAttributeFansCount(bs, pos);
      uint32 gender = bs_util.BSGetPhotoInfoAuthorInfoAttributeGender(bs, pos);
      int32 upload_count = bs_util.BSGetPhotoInfoAuthorInfoAttributeUploadCount(bs, pos);

      uint64_t id = 0;
      auto key_weight = BSFieldEnum::adlog_user_info_std_topic_id_weight_weight;
      BSRepeatedField<float, true> topic_weight(*bs, key_weight, pos);
      auto key_id = BSFieldEnum::adlog_user_info_std_topic_id_weight_id;
      BSRepeatedField<int64_t, true> topic_id(*bs, key_id, pos);

      for (int i = 0; i < topic_weight.size(); i++) {
        int val = static_cast<int>(topic_weight.Get(i) * 10);

        id = GetFeature(COMBINE_AUTHOR_ID_USER_LDA, author_id, topic_id.Get(i), val);
        AddFeature(id, 1.0, result);
        id = GetFeature(COMBINE_AUTHOR_FANSCOUNT_USER_LDA, fans_count, topic_id.Get(i), val);
        AddFeature(id, 1.0, result);
        id = GetFeature(COMBINE_AUTHOR_UPLOAD_USER_LDA, upload_count, topic_id.Get(i), val);
        AddFeature(id, 1.0, result);
        id = GetFeature(COMBINE_AUTHOR_GENDER_USER_LDA, gender, topic_id.Get(i), val);
        AddFeature(id, 1.0, result);
      }
    }
  }

 private:
  const std::string USED_FEATURES[11] = {
      "item.ad_dsp_info.photo_info.author_info.attribute.fans_count",
      "item.fans_top_info.photo_info.author_info.attribute.fans_count",
      "item.nature_photo_info.photo_info.author_info.attribute.fans_count",
      "item.ad_dsp_info.photo_info.author_info.attribute.gender",
      "item.fans_top_info.photo_info.author_info.attribute.gender",
      "item.nature_photo_info.photo_info.author_info.attribute.gender",
      "item.ad_dsp_info.photo_info.author_info.attribute.upload_count",
      "item.fans_top_info.photo_info.author_info.attribute.upload_count",
      "item.nature_photo_info.photo_info.author_info.attribute.upload_count",
      "user_info.std_topic.id_weight.id",
      "user_info.std_topic.id_weight.weight"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineAuthorUserLda);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineAuthorUserLda);

}  // namespace ad_algorithm
}  // namespace ks
