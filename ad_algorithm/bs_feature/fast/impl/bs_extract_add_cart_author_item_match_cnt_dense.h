#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_ecom_action_list_v2.dark
class BSExtractAddCartAuthorItemMatchCntDense : public BSFastFeature {
 public:
  BSExtractAddCartAuthorItemMatchCntDense();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAddCartAuthorItemMatchCntDense);
};

REGISTER_BS_EXTRACTOR(BSExtractAddCartAuthorItemMatchCntDense);
}  // namespace ad_algorithm
}  // namespace ks
