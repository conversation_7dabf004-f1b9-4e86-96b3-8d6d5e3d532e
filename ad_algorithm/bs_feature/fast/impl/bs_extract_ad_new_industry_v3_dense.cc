#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_new_industry_v3_dense.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdNewIndustryDense::BSExtractAdNewIndustryDense() : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3);
}

void BSExtractAdNewIndustryDense::Extract(const BSLog& bslog, size_t pos,
                                          std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists;
  bool base_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_base_exists, pos);

  auto enum_creative_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_exists;
  bool creative_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_creative_exists, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  if ((item_type != bs::ItemType::AD_DSP && item_type != bs::ItemType::NATIVE_AD)) {
    return;
  }
  auto enum_id_v3 = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3;
  uint64_t id_v3 = BSFieldHelper::GetSingular<uint64_t>(*bs, enum_id_v3, pos);

  if (info_exists && creative_exists && base_exists) {
    auto industry_id = id_v3 - 1022;
    if (industry_id >= 256) {
      industry_id = 0;
    }

    for (int i = 0; i < 256; i++) {
      if (i == industry_id) {
        AddFeature(i, 1, result);
      } else {
        AddFeature(i, 0, result);
      }
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
