#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_datetime.h"
namespace ks {
namespace ad_algorithm {

BSExtractBrandDatetime::BSExtractBrandDatetime() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_time);
  bs_util.BSGetPhotoInfoPhotoAttributeUploadtime.fill_attr_metas(&attr_metas_);
}

void BSExtractBrandDatetime::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  int upload_time = bs_util.BSGetPhotoInfoPhotoAttributeUploadtime(bs, pos);
  if (upload_time != 0) {
    int64_t now_time = BSFieldHelper::GetSingular<int64_t>(*bs, BSFieldEnum::adlog_time, pos);
    int diff = now_time - upload_time;
    AddFeature(GetFeature(FeaturePrefix::USER_AD_REQUEST_TIMES, diff), 1.0f, result);
    int diff_s = (int)(diff / 1000);
    AddFeature(GetFeature(FeaturePrefix::USER_AD_REQUEST_TIMES, diff_s), 1.0f, result);
    int diff_min = (int)(diff_s / 60);
    AddFeature(GetFeature(FeaturePrefix::USER_AD_REQUEST_TIMES, diff_min), 1.0f, result);
    int diff_hour = (int)(diff_min / 60);
    AddFeature(GetFeature(FeaturePrefix::PHOTO_CREATE_TIME_HOUR, diff_hour), 1.0f, result);
    int diff_day = (int)(diff_hour / 24);
    AddFeature(GetFeature(FeaturePrefix::PHOTO_CREATE_TIME_DAY, diff_day), 1.0f, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
