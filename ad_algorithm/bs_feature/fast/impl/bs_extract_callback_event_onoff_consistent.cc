#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_callback_event_onoff_consistent.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_action_type.pb.h"

namespace ks {
namespace ad_algorithm {
BSExtractCallbackEventOnOffConsistent::BSExtractCallbackEventOnOffConsistent()
    : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_callback_event);
}

void BSExtractCallbackEventOnOffConsistent::Extract(const BSLog& bslog, size_t pos,
                                                    std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  if ((item_type != bs::ItemType::AD_DSP && item_type != bs::ItemType::NATIVE_AD)) {
    return;
  }
  ::bs::kuaishou::ad::AdCallbackLog::EventType callback_event_ =
      ::bs::kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN;
  auto enum_type_exists = BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_exists;
  bool type_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_type_exists, pos);

  auto enum_ocpc_action_type = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type;
  int64_t ocpc_action_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_ocpc_action_type, pos);

  if (bslog.is_train()) {
    auto enum_callback_event = BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_callback_event;
    BSRepeatedField<int64_t> callback_event(*bs, enum_callback_event, pos);

    if (type_exists && callback_event.size() > 0) {
      callback_event_ = static_cast<::bs::kuaishou::ad::AdCallbackLog::EventType>(callback_event.Get(0));
    } else {
      callback_event_ = ::bs::kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN;
    }
  } else {
    /**
    * AD_CONVERSION                     EVENT_CONVERSION
      AD_LANDING_PAGE_FORM_SUBMITTED    EVENT_FORM_SUBMIT
      AD_MERCHANT_FOLLOW                AD_MERCHANT_FOLLOW
      AD_PURCHASE                       EventType_EVENT_PAY
      AD_CREDIT_GRANT                   EVENT_CREDIT_GRANT
    */

    if (ocpc_action_type == ::bs::kuaishou::ad::AD_CONVERSION) {
      callback_event_ = ::bs::kuaishou::ad::AdCallbackLog_EventType_EVENT_CONVERSION;
    } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) {
      callback_event_ = ::bs::kuaishou::ad::AdCallbackLog_EventType_EVENT_FORM_SUBMIT;
    } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_MERCHANT_FOLLOW) {
      callback_event_ = ::bs::kuaishou::ad::AdCallbackLog_EventType_AD_MERCHANT_FOLLOW;
    } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_PURCHASE) {
      callback_event_ = ::bs::kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY;
    } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_CREDIT_GRANT) {
      callback_event_ = ::bs::kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT;
    } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_ITEM_CLICK) {
      callback_event_ = ::bs::kuaishou::ad::AdCallbackLog_EventType_EVENT_ITEM_CLICK;
    } else {
      auto& ocpc_action_type_name =
          ::bs::kuaishou::ad::AdActionType_Name(::bs::kuaishou::ad::AdActionType(ocpc_action_type));
      if (!::bs::kuaishou::ad::AdCallbackLog::EventType_Parse(ocpc_action_type_name, &callback_event_)) {
        callback_event_ = ::bs::kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN;
      }
    }
  }

  for (int i = 0; i < 128; i++) {
    if (i == static_cast<int>(callback_event_)) {
      AddFeature(i, 1, result);
    } else {
      AddFeature(i, 0, result);
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
