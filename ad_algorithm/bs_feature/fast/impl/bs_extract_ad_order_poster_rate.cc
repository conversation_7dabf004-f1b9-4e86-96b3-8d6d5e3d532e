#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_order_poster_rate.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdOrderPosterRate::BSExtractAdOrderPosterRate() : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_action_count_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_action_count_value);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_action_count_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_action_count_value);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_action_count_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_action_count_value);

  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64537);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64538);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64539);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64540);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64541);
}

void BSExtractAdOrderPosterRate::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size;
  int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);

  auto enum_photo_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists;
  bool photo_info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_photo_info_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  std::vector<float> rate_vec;

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD ||
       item_type == bs::ItemType::AD_BRAND) &&
      info_exists) {
    // creative
    auto creative_map_key = BSFieldEnum::adlog_item_ad_dsp_info_creative_action_count_key;
    auto creative_map_value = BSFieldEnum::adlog_item_ad_dsp_info_creative_action_count_value;
    BSMapField<int64_t, int64_t> bs_creative_map(*bs, creative_map_key, creative_map_value, pos);

    if (bs_creative_map.size() > 0) {
      addCommonFeature(bs_creative_map, &rate_vec);
    } else {
      for (int i = 0; i < 2; i++) {
        rate_vec.push_back(0.0);
      }
    }
    // unit
    auto unit_map_key = BSFieldEnum::adlog_item_ad_dsp_info_unit_action_count_key;
    auto unit_map_value = BSFieldEnum::adlog_item_ad_dsp_info_unit_action_count_value;
    BSMapField<int64_t, int64_t> bs_unit_map(*bs, unit_map_key, unit_map_value, pos);

    if (bs_unit_map.size() > 0) {
      addCommonFeature(bs_unit_map, &rate_vec);
    } else {
      for (int i = 0; i < 2; i++) {
        rate_vec.push_back(0.0);
      }
    }
    // campaign
    auto campaign_map_key = BSFieldEnum::adlog_item_ad_dsp_info_campaign_action_count_key;
    auto campaign_map_value = BSFieldEnum::adlog_item_ad_dsp_info_campaign_action_count_value;
    BSMapField<int64_t, int64_t> bs_campaign_map(*bs, campaign_map_key, campaign_map_value, pos);

    if (bs_campaign_map.size() > 0) {
      addCommonFeature(bs_campaign_map, &rate_vec);
    } else {
      for (int i = 0; i < 2; i++) {
        rate_vec.push_back(0.0);
      }
    }
  }

  auto amount = 0;
  auto ctr = 0;
  auto cvr = 0;
  auto pcvr = 0;
  auto diff = 1.0;
  auto enum_key_64537 = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64537;
  float key_64537 = BSFieldHelper::GetSingular<float>(*bs, enum_key_64537, pos);

  auto enum_key_64537_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64537;
  bool key_64537_exists = BSFieldHelper::HasSingular<float>(*bs, enum_key_64537_exists, pos);

  auto enum_key_64538 = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64538;
  float key_64538 = BSFieldHelper::GetSingular<float>(*bs, enum_key_64538, pos);

  auto enum_key_64538_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64538;
  bool key_64538_exists = BSFieldHelper::HasSingular<float>(*bs, enum_key_64538_exists, pos);

  auto enum_key_64539 = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64539;
  float key_64539 = BSFieldHelper::GetSingular<float>(*bs, enum_key_64539, pos);

  auto enum_key_64539_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64539;
  bool key_64539_exists = BSFieldHelper::HasSingular<float>(*bs, enum_key_64539_exists, pos);

  auto enum_key_64540 = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64540;
  float key_64540 = BSFieldHelper::GetSingular<float>(*bs, enum_key_64540, pos);

  auto enum_key_64540_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64540;
  bool key_64540_exists = BSFieldHelper::HasSingular<float>(*bs, enum_key_64540_exists, pos);

  auto enum_key_64541 = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64541;
  float key_64541 = BSFieldHelper::GetSingular<float>(*bs, enum_key_64541, pos);

  auto enum_key_64541_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_64541;
  bool key_64541_exists = BSFieldHelper::HasSingular<float>(*bs, enum_key_64541_exists, pos);

  if ((item_type == bs::ItemType::AD_DSP) && info_exists && photo_info_exists && attr_size > 0) {
    if (key_64537_exists) {
      amount = key_64537;
    }

    if (key_64538_exists) {
      ctr = key_64538;
    }

    if (key_64539_exists) {
      cvr = key_64539;
    }

    if (key_64540_exists) {
      pcvr = key_64540;
    }

    if (key_64541_exists) {
      diff = key_64541;
    }

    rate_vec.push_back(amount);
    rate_vec.push_back(ctr);
    rate_vec.push_back(cvr);
    rate_vec.push_back(pcvr);
    rate_vec.push_back(diff);
  }
  int i = 0;

  for (auto iter = rate_vec.begin(); iter != rate_vec.end(); iter++) {
    AddFeature(i++, *iter, result);
  }
}
void BSExtractAdOrderPosterRate::addCommonFeature(const BSMapField<int64_t, int64_t>& map,
                                                  std::vector<float>* rate_vec) {
  AddRatio(map, 2, 1, rate_vec);
  auto fenzi_iter = map.Get(1);
  if (fenzi_iter.second && fenzi_iter.first > 0) {
    rate_vec->push_back((float)std::log10((1 + fenzi_iter.first)));
  } else {
    rate_vec->push_back((float)std::log10((1)));
  }
}
void BSExtractAdOrderPosterRate::AddRatio(const BSMapField<int64_t, int64_t>& map, int fenzi_index,
                                          int fenmu_index, std::vector<float>* rate_vec) {
  auto fenzi_iter = map.Get(fenzi_index);
  auto fenmu_iter = map.Get(fenmu_index);
  if (fenzi_iter.second && fenmu_iter.second && fenmu_iter.first > 0) {
    float rate = (float)(fenzi_iter.first) / (fenmu_iter.first + 1);
    rate_vec->push_back(rate);
  } else {
    rate_vec->push_back(0.0);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
