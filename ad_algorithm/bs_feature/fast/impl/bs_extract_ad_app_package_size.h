#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "ks/util/json.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdAppPackageSize : public BSFastFeature {
 public:
  BSExtractAdAppPackageSize() : BSFastFeature(FeatureType::ITEM) {}

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    if (bslog.item_size() <= pos) {
      return;
    }
    auto& item = bslog.item(pos);
    if ((item.type() == bs::ItemType::AD_DSP || item.type() == bs::ItemType::NATIVE_AD)) {
      for (const auto& attr : item.common_info_attr()) {
        if (attr.name_value() == ::bs::kuaishou::ad::ItemCommonInfoAttr::APP_PACKAGE_SIZE) {
          AddFeature(GetFeature(FeaturePrefix::PHOTO_APP_PACKAGE_SIZE, attr.int_value()), 1.0f, result);
          break;
        }
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdAppPackageSize);
};

REGISTER_BS_EXTRACTOR(BSExtractAdAppPackageSize);

}  // namespace ad_algorithm
}  // namespace ks
