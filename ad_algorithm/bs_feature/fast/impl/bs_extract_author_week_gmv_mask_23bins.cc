#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_week_gmv_mask_23bins.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/action.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {
BSExtractAuthorWeekGmvMask23Bins::BSExtractAuthorWeekGmvMask23Bins() : BSFastFeature(DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82084);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82002);
}

void BSExtractAuthorWeekGmvMask23Bins::Extract(const BSLog& bslog, size_t pos,
                                               std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  auto enum_key_82084 = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82084;
  absl::optional<float> key_82084_opt = get_bslog_float(*bs, enum_key_82084, pos);

  auto industry_id_v3 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3, pos);

  float author_price_3d = BSFieldHelper::GetSingular<double>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82002, pos);

  float gmv = get_split_gmv(key_82084_opt, industry_id_v3, author_price_3d);

  std::vector<ExtractResult> result_value =
      find_mask_from_segments_by_target_value(gmv, get_gmv_buckets_wanglianhai03());
  add_feature_result(result_value, result);
}
}  // namespace ad_algorithm
}  // namespace ks
