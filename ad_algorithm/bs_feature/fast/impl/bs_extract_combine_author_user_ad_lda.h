#pragma once
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_leaf_util.h"

namespace ks {
namespace ad_algorithm {

class BSExtractCombineAuthorUserAdLda : public BSFastFeature {
 public:
  BSExtractCombineAuthorUserAdLda() : BSFastFeature(FeatureType::COMBINE, 26, 16, 10) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_std_topic_id_weight_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_std_topic_id_weight_weight);
    bs_util.BSHasPhotoInfoAuthorInfo.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoId.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoAttributeFansCount.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoAttributeUploadCount.fill_attr_metas(&attr_metas_);
    bs_util.BSHasPhotoInfoAuthorInfoAttribute.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoAttributeGender.fill_attr_metas(&attr_metas_);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    bool has_user_info = BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
    bool has_author_info = bs_util.BSHasPhotoInfoAuthorInfo(bs, pos);
    if (has_author_info) {
      bool has_attribute = bs_util.BSHasPhotoInfoAuthorInfoAttribute(bs, pos);
      if (!has_attribute) {
        return;
      }
      int64_t author_id = bs_util.BSGetPhotoInfoAuthorInfoId(bs, pos);
      uint32_t fans_count = bs_util.BSGetPhotoInfoAuthorInfoAttributeFansCount(bs, pos);
      uint32_t gender = bs_util.BSGetPhotoInfoAuthorInfoAttributeGender(bs, pos);
      int32_t upload_count = bs_util.BSGetPhotoInfoAuthorInfoAttributeUploadCount(bs, pos);

      uint64_t id = 0;
      auto key_weight = BSFieldEnum::adlog_user_info_ad_std_topic_id_weight_weight;
      BSRepeatedField<float> topic_weight(*bs, key_weight, pos);
      auto key_id = BSFieldEnum::adlog_user_info_ad_std_topic_id_weight_id;
      BSRepeatedField<int64_t> topic_id(*bs, key_id, pos);

      for (int i = 0; i < topic_weight.size(); i++) {
        int val = static_cast<int>(topic_weight.Get(i) * 10);

        id = GetFeature(FeaturePrefix::COMBINE_AUTHOR_ID_USER_AD_LDA, author_id, topic_id.Get(i), val);
        AddFeature(id, 1.0, result);
        id =
            GetFeature(FeaturePrefix::COMBINE_AUTHOR_FANSCOUNT_USER_AD_LDA, fans_count, topic_id.Get(i), val);
        AddFeature(id, 1.0, result);
        id = GetFeature(FeaturePrefix::COMBINE_AUTHOR_UPLOAD_USER_AD_LDA, upload_count, topic_id.Get(i), val);
        AddFeature(id, 1.0, result);
        id = GetFeature(FeaturePrefix::COMBINE_AUTHOR_GENDER_USER_AD_LDA, gender, topic_id.Get(i), val);
        AddFeature(id, 1.0, result);
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineAuthorUserAdLda);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineAuthorUserAdLda);

}  // namespace ad_algorithm
}  // namespace ks
