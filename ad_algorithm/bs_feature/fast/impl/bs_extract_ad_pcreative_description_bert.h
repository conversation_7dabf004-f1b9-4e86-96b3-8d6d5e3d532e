#pragma once
#include <algorithm>
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdPcreativeDescriptionBert : public BSFastFeature {
 public:
  BSExtractAdPcreativeDescriptionBert() : BSFastFeature(FeatureType::DENSE_ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_233);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if (item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) {
      int32_t name_attr_id = 233;

      // PCREATIVE_SELECT_CREATIVE_DESC_BERT
      auto key_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_233;
      BSRepeatedField<float> float_list(*bs, key_id, pos);
      if (float_list.size() != 34) {
        return;
      }

      for (size_t i = 0; i < float_list.size() - 2; i++) {
        AddFeature(i, std::max(std::min(float_list.Get(i), 10.0f), -10.0f), result);
      }
    }
  }

 private:
  const std::string USED_FEATURES[1] = {
      "item.ad_dsp_info.common_info_attr.PCREATIVE_SELECT_CREATIVE_DESC_BERT"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdPcreativeDescriptionBert);
};

REGISTER_BS_EXTRACTOR(BSExtractAdPcreativeDescriptionBert);

}  // namespace ad_algorithm
}  // namespace ks
