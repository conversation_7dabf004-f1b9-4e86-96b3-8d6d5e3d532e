#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdTargetId : public BSFastFeature {
 public:
  BSExtractAdTargetId();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdTargetId);
};

REGISTER_BS_EXTRACTOR(BSExtractAdTargetId);

}  // namespace ad_algorithm
}  // namespace ks
