#pragma once
#include <string>
#include <unordered_map>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdPcreativeImpressionCost12 : public BSFastFeature {
 public:
  BSExtractAdPcreativeImpressionCost12();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdPcreativeImpressionCost12);
};

REGISTER_BS_EXTRACTOR(BSExtractAdPcreativeImpressionCost12);

}  // namespace ad_algorithm
}  // namespace ks
