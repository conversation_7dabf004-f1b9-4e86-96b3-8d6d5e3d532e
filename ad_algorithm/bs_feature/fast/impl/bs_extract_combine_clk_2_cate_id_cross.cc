/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_clk_2_cate_id_cross.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_realtime_cross_feature.h"

namespace ks {
namespace ad_algorithm {

BSExtractCombineClk2CateIdCross::BSExtractCombineClk2CateIdCross()
    : BSFastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5003269);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5100041);
}
void BSExtractCombineClk2CateIdCross::Extract(const BSLog& bslog, size_t pos,
                                              std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5003269, pos);
  auto x2 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5100041, pos);
  auto x3 = get_realtime_target_id_cross_list(x1, x2);
  add_feature_result(x3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
