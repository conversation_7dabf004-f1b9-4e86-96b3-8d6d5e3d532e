#pragma once
#include <string>
#include <unordered_set>
#include <vector>

#include "base/strings/string_number_conversions.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "base/strings/string_util.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdBehaviorIntentionCategoryBacktrace : public BSFastFeature {
 public:
  BSExtractAdBehaviorIntentionCategoryBacktrace() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    bs_info.kItemAdDspInfoCommonAttr.FillMeta(5280, &attr_metas_);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD)) {
      bool has_value = BSFieldHelper::HasSingular<int>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
      if (has_value) {
        int32_t name_attr_id = 5280;
        std::vector<int32_t> attr_ids = bs_info.kItemAdDspInfoCommonAttr.find(name_attr_id);
        if (attr_ids.size() == 2) {
          BSMapField<int64_t, float> bs_map(*bs, attr_ids[0], attr_ids[1], pos);
          std::unordered_set<uint64_t> sub_categorys;
          for (int i = 0; i < bs_map.size(); ++i) {
            auto category = bs_map.GetKey(i);
            while (category > 0) {
              sub_categorys.insert(category);
              category = category / 100;
            }
          }
          for (const auto& sub_category : sub_categorys) {
            AddFeature(
                GetFeature(FeaturePrefix::AD_CREATIVE_BEHAVIOR_INTENTION_CATEGORY_BACKTRACE, sub_category),
                1.0f, result);
          }
        }
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdBehaviorIntentionCategoryBacktrace);
};
REGISTER_BS_EXTRACTOR(BSExtractAdBehaviorIntentionCategoryBacktrace);
}  // namespace ad_algorithm
}  // namespace ks
