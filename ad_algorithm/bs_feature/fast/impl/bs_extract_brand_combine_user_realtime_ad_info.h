#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandCombineUserRealtimeAdInfo : public BSFastFeature {
 public:
  BSExtractBrandCombineUserRealtimeAdInfo() : BSFastFeature(FeatureType::COMBINE, 26, 26) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_user_real_time_action_exists);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_user_info_user_real_time_action_real_time_dsp_action_detail_size);

    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_photo_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_user_info_user_real_time_action_real_time_dsp_action_detail_key_1_list_photo_id);
    RealtimeActionSizeHelper::fill_attr_metas(&attr_metas_);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    int no = 1;  // AD_ITEM_IMPRESSION = 1; // 打开播放页就上报

    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    bool has_user_info =
        BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
    bool has_user_real_time_action = BSFieldHelper::GetSingular<bool, true>(
        *bs, BSFieldEnum::adlog_user_info_user_real_time_action_exists, pos);
    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    int real_time_dsp_action_detail_size = RealtimeActionSizeHelper::GetRealtimeActionSize<true>(bs, pos);

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    bool has_creative =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_exists, pos);
    bool has_base =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists, pos);
    if (!has_ad_dsp_info || !has_creative || !has_base) {
      return;
    }
    if (has_user_info && has_user_real_time_action && real_time_dsp_action_detail_size > 0) {
      // 组合特征
      uint64 photo_id = BSFieldHelper::GetSingular<uint64>(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_photo_id, pos);
      uint64 industry_id = BSFieldHelper::GetSingular<uint64>(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id, pos);
      if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) ||
          item_type == bs::ItemType::AD_BRAND) {
        BSRepeatedField<uint64, true> list_photo_id(
            *bs,
            BSFieldEnum::
                adlog_user_info_user_real_time_action_real_time_dsp_action_detail_key_1_list_photo_id,
            pos);

        for (size_t i = 0; i < list_photo_id.size() && i < 100; i++) {
          uint64_t id_1 =
              GetFeature(FeaturePrefix::COMBINE_USER_REALTIME_PHOTO_ID_PHOTO_ID, list_photo_id[i], photo_id);
          uint64_t id_5 = GetFeature(FeaturePrefix::COMBINE_USER_REALTIME_PHOTO_ID_INDUSTRY, list_photo_id[i],
                                     industry_id);
          AddFeature(id_1, 1.0, result);
          AddFeature(id_5, 1.0, result);
        }
      }
    }
  }

 private:
  const std::string USED_FEATURES[5] = {
      "item.ad_dsp_info.creative.base.id", "item.ad_dsp_info.creative.base.photo_id",
      "item.ad_dsp_info.creative.base.name", "item.ad_dsp_info.creative.base.new_industry_id",
      "user_info.user_real_time_action.real_time_dsp_action_detail.photo_id"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandCombineUserRealtimeAdInfo);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandCombineUserRealtimeAdInfo);

}  // namespace ad_algorithm
}  // namespace ks
