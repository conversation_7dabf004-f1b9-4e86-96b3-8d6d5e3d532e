#pragma once
#include <string>
#include <vector>

#include "base/common/logging.h"
#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#define RANDMAX 100000

namespace ks {
namespace ad_algorithm {

class BSExtractBrandDupCoverId : public BSFastFeature {
 public:
  BSExtractBrandDupCoverId() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_common_info_attr_key_812);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_component_assembly_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_component_assembly_virtual_item_exists);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_component_assembly_virtual_item_virtual_creative_exists);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_component_assembly_virtual_item_virtual_creative_bg_cover_id);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_component_assembly_virtual_item_virtual_creative_bg_cover_id_exists);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if (item_type != bs::ItemType::AD_DSP || item_type != bs::ItemType::AD_BRAND) {
      return;
    }
    ::google::protobuf::uint64 ad_cover_id = 0;
    ::google::protobuf::uint64 virtual_cover_id = 0;
    ::google::protobuf::uint64 cover_id = 0;
    ad_cover_id =
        BSFieldHelper::GetSingular<int64_t>(*bs, BSFieldEnum::adlog_item_common_info_attr_key_812, pos);
    int component_assembly_size = 0;
    component_assembly_size =
        BSFieldHelper::GetSingular<int64_t>(*bs, BSFieldEnum::adlog_item_ad_component_assembly_size, pos);
    if (component_assembly_size == 0) {
      return;
    }
    BSRepeatedField<bool> has_virtual_item(
        *bs, BSFieldEnum::adlog_item_ad_component_assembly_virtual_item_exists, pos);
    BSRepeatedField<bool> has_virtual_creative(
        *bs, BSFieldEnum::adlog_item_ad_component_assembly_virtual_item_virtual_creative_exists, pos);
    BSRepeatedField<bool> has_bg_cover_id(
        *bs, BSFieldEnum::adlog_item_ad_component_assembly_virtual_item_virtual_creative_bg_cover_id_exists,
        pos);
    BSRepeatedField<int64_t> virtual_cover_ids(
        *bs, BSFieldEnum::adlog_item_ad_component_assembly_virtual_item_virtual_creative_bg_cover_id, pos);
    for (int i = 0; i < component_assembly_size; i++) {
      if (has_virtual_item.Get(i) && has_virtual_creative.Get(i) && has_bg_cover_id.Get(i)) {
        virtual_cover_id = virtual_cover_ids.Get(i);
        break;
      }
    }
    if (ad_cover_id != 0 || virtual_cover_id != 0) {
      cover_id = (0 == virtual_cover_id) ? ad_cover_id : virtual_cover_id;
      AddFeature(GetFeature(FeaturePrefix::V2_DUP_COVER_ID, cover_id), 1.0f, result);
    }
  }

 private:
  const std::string USED_FEATURES[2] = {
      "item.common_info_attr.DSP_DUP_COVER_ID",
      "item.ad_component_assembly.virtual_item.virtual_creative.bg_cover_id"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandDupCoverId);
};
REGISTER_BS_EXTRACTOR(BSExtractBrandDupCoverId);
}  // namespace ad_algorithm
}  // namespace ks
