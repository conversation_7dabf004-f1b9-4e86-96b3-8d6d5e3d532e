#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractEcomAndOtherLpsItemType : public BSFastFeature {
 public:
  BSExtractEcomAndOtherLpsItemType();
  bool is_direct_ecom_industry_id(const int& industry_id);

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  const int MIN_INDUSTRY_ID_2 = 147;
  const int MAX_INDUSTRY_ID_2 = 155;
  const int MIN_INDUSTRY_ID__3 = 1256;
  const int MAX_INDUSTRY_ID__3 = 1289;
  DISALLOW_COPY_AND_ASSIGN(BSExtractEcomAndOtherLpsItemType);
};

REGISTER_BS_EXTRACTOR(BSExtractEcomAndOtherLpsItemType);

}  // namespace ad_algorithm
}  // namespace ks
