#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {
class BSExtractCombineClickPhotoSecondIndustrySequence : public BSFastFeature {
 public:
  BSExtractCombineClickPhotoSecondIndustrySequence() : BSFastFeature(COMBINE, 26, 26) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_2_list_size);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_2_list_second_industry_id_hash);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    bool has_user_info = BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
    auto key_has_ad_user_info = BSFieldEnum::adlog_user_info_ad_user_info_exists;
    bool has_ad_user_info = BSFieldHelper::GetSingular<bool>(*bs, key_has_ad_user_info, pos);
    bool has_advertiser_base = BSFieldHelper::GetSingular<bool>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists, pos);
    int64_t long_term_action_size = BSFieldHelper::GetSingular<int64_t>(
        *bs, BSFieldEnum::adlog_user_info_explore_long_term_ad_action_size, pos);
    int64_t key_2_action_size = BSFieldHelper::GetSingular<int64_t>(
        *bs, BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_2_list_size, pos);

    if (!has_user_info || !has_ad_user_info || !has_advertiser_base ||
        !(item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) ||
        long_term_action_size <= 0 || key_2_action_size <= 0) {
      return;
    }
    int64_t industry_id = BSFieldHelper::GetSingular<int64_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_id, pos);
    BSRepeatedField<int64_t> industry_id_hash(
        *bs, BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_2_list_second_industry_id_hash,
        pos);
    size_t lens = key_2_action_size < 100 ? key_2_action_size : 100;
    for (size_t i = 0; i < lens; i++) {
      AddFeature(
          GetFeature(FeaturePrefix::COMBINE_CLICK_SECOND_INDUSTRY_SEQ, industry_id_hash.Get(i), industry_id),
          1.0, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineClickPhotoSecondIndustrySequence);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineClickPhotoSecondIndustrySequence);

}  // namespace ad_algorithm
}  // namespace ks
