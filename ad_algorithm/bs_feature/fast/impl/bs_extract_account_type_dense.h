#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_account_type_dense.dark
class BSExtractAccountTypeDense : public BSFastFeature {
 public:
  BSExtractAccountTypeDense();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAccountTypeDense);
};

REGISTER_BS_EXTRACTOR(BSExtractAccountTypeDense);
}  // namespace ad_algorithm
}  // namespace ks
