#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandCombineUserRealtimePlay5s : public BSFastFeature {
 public:
  BSExtractBrandCombineUserRealtimePlay5s() : BSFastFeature(FeatureType::COMBINE, 26, 26) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_user_real_time_action_exists);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_user_info_user_real_time_action_real_time_dsp_action_detail_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_photo_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_user_info_user_real_time_action_real_time_dsp_action_detail_key_4_list_author_id);
    attr_metas_.emplace_back(
        BSFieldEnum::
            adlog_user_info_user_real_time_action_real_time_dsp_action_detail_key_4_list_industry_id);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_user_info_user_real_time_action_real_time_dsp_action_detail_key_4_list_photo_id);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_user_info_user_real_time_action_real_time_dsp_action_detail_key_4_list_size);
    RealtimeActionSizeHelper::fill_attr_metas(&attr_metas_);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    int no = 4;

    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    bool has_user_info =
        BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
    bool has_user_real_time_action = BSFieldHelper::GetSingular<bool, true>(
        *bs, BSFieldEnum::adlog_user_info_user_real_time_action_exists, pos);
    int real_time_dsp_action_detail_size = RealtimeActionSizeHelper::GetRealtimeActionSize<true>(bs, pos);
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if (has_user_info && has_user_real_time_action && real_time_dsp_action_detail_size > 0) {
      if (item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::AD_BRAND) {
        // 组合特征
        uint64 photo_id = BSFieldHelper::GetSingular<uint64>(
            *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_photo_id, pos);
        uint64 industry_id = BSFieldHelper::GetSingular<uint64>(
            *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id, pos);
        if (item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::AD_BRAND) {
          auto key_auth_id = BSFieldEnum::
              adlog_user_info_user_real_time_action_real_time_dsp_action_detail_key_4_list_author_id;
          auto key_ind_id = BSFieldEnum::
              adlog_user_info_user_real_time_action_real_time_dsp_action_detail_key_4_list_industry_id;
          auto key_photo_id = BSFieldEnum::
              adlog_user_info_user_real_time_action_real_time_dsp_action_detail_key_4_list_photo_id;

          // int64_t list_size = BSFieldHelper::GetSingular<int64_t, true>(*bs,
          // BSFieldEnum::adlog_user_info_user_real_time_action_real_time_dsp_action_detail_key_4_list_size,
          //      pos);
          BSRepeatedField<uint64, true> list_auth_id(*bs, key_auth_id, pos);
          if (list_auth_id.is_empty()) {
            return;
          }
          auto list_size = list_auth_id.size();
          BSRepeatedField<uint64, true> list_ind_id(*bs, key_ind_id, pos);
          if (list_ind_id.is_empty() || list_ind_id.size() != list_size) {
            return;
          }
          BSRepeatedField<uint64, true> list_photo_id(*bs, key_photo_id, pos);
          if (list_photo_id.is_empty() || list_photo_id.size() != list_size) {
            return;
          }

          for (size_t i = 0; i < list_size && i < 30; ++i) {
            uint64_t id_1 = GetFeature(FeaturePrefix::COMBINE_USER_REALTIME_PHOTO_ID_PHOTO_ID_PLAY5S,
                                       list_photo_id[i], photo_id);
            uint64_t id_2 = GetFeature(FeaturePrefix::COMBINE_USER_REALTIME_AUTHOR_ID_PHOTO_ID_PLAY5S,
                                       list_auth_id[i], photo_id);
            uint64_t id_3 = GetFeature(FeaturePrefix::COMBINE_USER_REALTIME_INDUSTRY_ID_PHOTO_ID_PLAY5S,
                                       list_ind_id[i], photo_id);
            uint64_t id_5 = GetFeature(FeaturePrefix::COMBINE_USER_REALTIME_PHOTO_ID_INDUSTRY_ID_PLAY5S,
                                       list_photo_id[i], industry_id);
            uint64_t id_6 = GetFeature(FeaturePrefix::COMBINE_USER_REALTIME_AUTHOR_ID_INDUSTRY_ID_PLAY5S,
                                       list_auth_id[i], industry_id);
            uint64_t id_7 = GetFeature(FeaturePrefix::COMBINE_USER_REALTIME_INDUSTRY_ID_INDUSTRY_ID_PLAY5S,
                                       list_ind_id[i], industry_id);
            AddFeature(id_1, 1.0, result);
            AddFeature(id_2, 1.0, result);
            AddFeature(id_3, 1.0, result);
            AddFeature(id_5, 1.0, result);
            AddFeature(id_6, 1.0, result);
            AddFeature(id_7, 1.0, result);
          }
        }
      }
    }
  }

 private:
  const std::string USED_FEATURES[3] = {
      "item.ad_dsp_info.creative.base.photo_id", "item.ad_dsp_info.creative.base.new_industry_id",
      "user_info.user_real_time_action.real_time_dsp_action_detail.photo_id"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandCombineUserRealtimePlay5s);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandCombineUserRealtimePlay5s);

}  // namespace ad_algorithm
}  // namespace ks
