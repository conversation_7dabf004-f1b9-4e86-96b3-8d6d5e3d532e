#pragma once
#include <math.h>
#include <time.h>

#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
//

namespace ks {
namespace ad_algorithm {
class BSExtractAdNewIndustryDense : public BSFastFeature {
 public:
  BSExtractAdNewIndustryDense();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdNewIndustryDense);
};
REGISTER_BS_EXTRACTOR(BSExtractAdNewIndustryDense);
}  // namespace ad_algorithm
}  // namespace ks
