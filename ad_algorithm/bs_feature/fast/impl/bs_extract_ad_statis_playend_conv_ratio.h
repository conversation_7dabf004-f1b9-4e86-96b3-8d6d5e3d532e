#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdStatisPlayendConvRatio : public BSFastFeature {
 public:
  BSExtractAdStatisPlayendConvRatio() : BSFastFeature(FeatureType::DENSE_ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_value);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    auto key_enum1 = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_enum1, pos);
    if (has_ad_dsp_info) {
      int64_t conversion = 0;
      int64_t playEnd = 0;
      auto key_enum2 = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_key;
      auto key_enum3 = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_value;
      BSRepeatedField<absl::string_view> keys(*bs, key_enum2, pos);
      BSRepeatedField<int> values(*bs, key_enum3, pos);
      for (int i = 0; i < keys.size(); ++i) {
        auto key = keys.Get(i);
        if (key == "conversion") {
          conversion = values.Get(i);
        }
        if (key == "playEnd") {
          playEnd = values.Get(i);
        }
      }
      double ratio = 0;
      if (playEnd >= 500) {
        ratio = 1.0 * conversion / playEnd;
      }
      if (ratio > 1) {
        ratio = 1;
      }
      AddFeature(0, ratio, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdStatisPlayendConvRatio);
};

REGISTER_BS_EXTRACTOR(BSExtractAdStatisPlayendConvRatio);

}  // namespace ad_algorithm
}  // namespace ks
