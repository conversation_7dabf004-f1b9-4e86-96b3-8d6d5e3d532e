#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_sdpa_photo_feature_fix.h"

namespace ks {
namespace ad_algorithm {

template <BSFieldEnum item_attr_name>
BSExtractSdpaPhotoFeatureFix<item_attr_name>::BSExtractSdpaPhotoFeatureFix()
    : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
  attr_metas_.emplace_back(item_attr_name);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
}

template <BSFieldEnum item_attr_name>
void BSExtractSdpaPhotoFeatureFix<item_attr_name>::Extract(const BSLog& bslog, size_t pos,
                                                           std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  bool has_ad_dsp_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
  int32_t common_info_attr_size = BSFieldHelper::GetSingular<int32_t>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size, pos);
  if (item_type == bs::ItemType::AD_DSP && has_ad_dsp_info && common_info_attr_size > 0) {
    auto key_enum = item_attr_name_;
    auto sdpa_feature_value = BSFieldHelper::GetSingular<int64_t>(*bs, key_enum, pos);
    if (sdpa_feature_value != 0) AddFeature(sdpa_feature_value, 1.0f, result);
  }
}

using BSExtractSdpaOuterIdFull =
    BSExtractSdpaPhotoFeatureFix<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90041>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaOuterIdFull);

using BSExtractSdpaProductTitle =
    BSExtractSdpaPhotoFeatureFix<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90042>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductTitle);

using BSExtractSdpaProductName =
    BSExtractSdpaPhotoFeatureFix<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90043>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductName);

using BSExtractSdpaEcomBandIdFull =
    BSExtractSdpaPhotoFeatureFix<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90044>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaEcomBandIdFull);

using BSExtractSdpaEcomShopKeeperIdFull =
    BSExtractSdpaPhotoFeatureFix<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90045>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaEcomShopKeeperIdFull);

using BSExtractSdpaEcomCategoryIdFull =
    BSExtractSdpaPhotoFeatureFix<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90046>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaEcomCategoryIdFull);

using BSExtractSdpaEcomCategory2IdFull =
    BSExtractSdpaPhotoFeatureFix<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90047>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaEcomCategory2IdFull);

using BSExtractSdpaEcomProductPriceFull =
    BSExtractSdpaPhotoFeatureFix<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90048>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaEcomProductPriceFull);

using BSExtractSdpaVideoProductTypeFull =
    BSExtractSdpaPhotoFeatureFix<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90049>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaVideoProductTypeFull);

using BSExtractSdpaProductVideoThemeFull =
    BSExtractSdpaPhotoFeatureFix<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90050>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductVideoThemeFull);

using BSExtractSdpaProductCarBrandFull =
    BSExtractSdpaPhotoFeatureFix<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90051>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductCarBrandFull);

using BSEExtractSdpaProductCarClassificationFull =
    BSExtractSdpaPhotoFeatureFix<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90052>;
REGISTER_BS_EXTRACTOR(BSEExtractSdpaProductCarClassificationFull);

}  // namespace ad_algorithm
}  // namespace ks
