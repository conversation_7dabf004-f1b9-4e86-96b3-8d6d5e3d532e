#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_seller_click_stat.h"

#include <vector>

#include "teams/ad/ad_nn/bs_field_helper/bs_repeated_field.h"

namespace ks {
namespace ad_algorithm {

BSExtractMerchantAuthorClickStat::BSExtractMerchantAuthorClickStat()
    : BSCommonInfoAttrFeature(FeatureType::ITEM,
                              ::bs::kuaishou::ad::CommonInfoAttr_Name_AUTHOR_LAST_30D_CLICK_ITEM_CNT_STAT,
                              GetPrefix(0)) {
  value_bits_ = 22;
  value_mask_ = (1LL << value_bits_) - 1;
}

void BSExtractMerchantAuthorClickStat::ExtractCommonAttr(const ad_nn::SampleInterface& sample,
                                                         const std::vector<int32_t>& attr_ids, size_t pos,
                                                         std::vector<ExtractResult>* result) {
  if (attr_ids.size() != 1) {
    return;
  }
  ad_nn::BSRepeatedField<float> values(sample, attr_ids[0], pos);
  if (values.size() != 4) {
    return;
  }
  auto click_cnt = values[0];
  auto photo_click_cnt = values[1];
  auto live_click_cnt = values[2];
  auto profile_click_cnt = values[3];

  auto photo_click_rate = click_cnt ? photo_click_cnt / click_cnt : 0.0;
  auto live_click_rate = click_cnt ? live_click_cnt / click_cnt : 0.0;
  auto profile_click_rate = click_cnt ? profile_click_cnt / click_cnt : 0.0;

  AddFeature(get_sign(0, GetValue(click_cnt, 100, true)), 1.0, result);
  AddFeature(get_sign(1, GetValue(photo_click_cnt, 100, true)), 1.0, result);
  AddFeature(get_sign(2, GetValue(live_click_cnt, 100, true)), 1.0, result);
  AddFeature(get_sign(3, GetValue(profile_click_cnt, 100, true)), 1.0, result);
  AddFeature(get_sign(4, GetValue(photo_click_rate, 200, false)), 1.0, result);
  AddFeature(get_sign(5, GetValue(live_click_rate, 200, false)), 1.0, result);
  AddFeature(get_sign(6, GetValue(profile_click_rate, 200, false)), 1.0, result);
}

}  // namespace ad_algorithm
}  // namespace ks
