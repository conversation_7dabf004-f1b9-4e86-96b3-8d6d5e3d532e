#pragma once
#include <string>
#include <vector>

#include "base/strings/string_number_conversions.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_util.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAuthorInfoFansRange : public BSFastFeatureNoPrefix {
 public:
  BSExtractAuthorInfoFansRange() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
    bs_util.BSHasAuthorInfoAttribute.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoAttributeCountFans.fill_attr_metas(&attr_metas_);
    bs_util.BSHasAuthorInfo.fill_attr_metas(&attr_metas_);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    bool has_author_info = bs_util.BSHasAuthorInfo(bs, pos);
    if (!has_author_info) {
      return;
    }

    bool has_author_info_attr = bs_util.BSHasAuthorInfoAttribute(bs, pos);
    if (!has_author_info_attr) {
      return;
    }

    uint32 fans_count = bs_util.BSGetAuthorInfoAttributeCountFans(bs, pos);

    uint64 index = 0;
    if (fans_count > 10000000) {  // 大于 1000w
      index = 1;
    } else if (fans_count > 1000000) {  // 大于 100w 小于 1000w
      index = 2;
    } else if (fans_count > 100000) {  // 大于 10w 小于 100w
      index = 3;
    } else {  //  小于 10w
      index = 4;
    }
    double value = 1.0;
    AddFeature(index, value, result);
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorInfoFansRange);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorInfoFansRange);

}  // namespace ad_algorithm
}  // namespace ks
