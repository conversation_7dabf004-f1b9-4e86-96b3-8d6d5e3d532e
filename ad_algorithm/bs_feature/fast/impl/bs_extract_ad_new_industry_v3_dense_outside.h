#pragma once

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdNewIndustryV3DenseOutside : public BSFastFeature {
 public:
  BSExtractAdNewIndustryV3DenseOutside();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdNewIndustryV3DenseOutside);
};

REGISTER_BS_EXTRACTOR(BSExtractAdNewIndustryV3DenseOutside);

}  // namespace ad_algorithm
}  // namespace ks
