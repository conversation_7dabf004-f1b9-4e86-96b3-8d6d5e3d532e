#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_week_gmv_dist.h"

namespace ks {
namespace ad_algorithm {
BSExtractAuthorWeekGmvDist::BSExtractAuthorWeekGmvDist() : BSFastFeature(DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82079);
}

void BSExtractAuthorWeekGmvDist::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_key_82079 = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82079;
  float key_82079 = BSFieldHelper::GetSingular<float>(*bs, enum_key_82079, pos);

  auto enum_key_82079_exists = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82079;
  bool key_82079_exists = BSFieldHelper::HasSingular<float>(*bs, enum_key_82079_exists, pos);

  if (key_82079_exists) {
    AddFeature(0, key_82079, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
