#pragma once
#include <string>
#include <vector>

#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdEcomKuaiCategoryId : public BSFastFeature {
 public:
  BSExtractAdEcomKuaiCategoryId() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ecom_kuai_category_id);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD)) {
      uint64 ekc_id =
          BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_ecom_kuai_category_id, pos);
      AddFeature(GetFeature(FeaturePrefix::PHOTO_ECOM_KUAI_CATEGORY_ID, ekc_id), 1.0f, result);
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"item.ecom_kuai_category_id"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdEcomKuaiCategoryId);
};

REGISTER_BS_EXTRACTOR(BSExtractAdEcomKuaiCategoryId);

}  // namespace ad_algorithm
}  // namespace ks
