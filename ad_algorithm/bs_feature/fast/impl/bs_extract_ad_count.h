#pragma once
#include <algorithm>
#include <cmath>
#include <string>
#include <utility>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdCount : public BSFastFeature {
 public:
  BSExtractAdCount() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_action_count_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_action_count_value);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_action_count_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_action_count_value);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_action_count_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_action_count_value);
  }

  inline void AddRate(const BSMapField<int64_t, int64_t>& map, int fenzi_index, int fenmu_index,
                      FeaturePrefix prefix, std::vector<ExtractResult>* result) {
    auto fenzi_iter = map.Get(fenzi_index);
    auto fenmu_iter = map.Get(fenmu_index);
    if (fenzi_iter.second && fenmu_iter.second) {
      if (fenmu_iter.first > 10000) {
        float rate = (float)(fenzi_iter.first) / (fenmu_iter.first);
        ExtractRateFeatureWithMax(prefix, fenzi_iter.first, fenmu_iter.first, 1000, 0.5, result);
        AddFeature(GetFeature(prefix, 1000000L), rate, result);
      } else {
        AddFeature(GetFeature(prefix, 1000001L), 1.0f, result);
      }
    }
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info) {
      auto creative_map_key = BSFieldEnum::adlog_item_ad_dsp_info_creative_action_count_key;
      auto creative_map_value = BSFieldEnum::adlog_item_ad_dsp_info_creative_action_count_value;
      BSMapField<int64_t, int64_t> bs_creative_map(*bs, creative_map_key, creative_map_value, pos);

      auto unit_map_key = BSFieldEnum::adlog_item_ad_dsp_info_unit_action_count_key;
      auto unit_map_value = BSFieldEnum::adlog_item_ad_dsp_info_unit_action_count_value;
      BSMapField<int64_t, int64_t> bs_unit_map(*bs, unit_map_key, unit_map_value, pos);

      auto campaign_map_key = BSFieldEnum::adlog_item_ad_dsp_info_campaign_action_count_key;
      auto campaign_map_value = BSFieldEnum::adlog_item_ad_dsp_info_campaign_action_count_value;
      BSMapField<int64_t, int64_t> bs_campaign_map(*bs, campaign_map_key, campaign_map_value, pos);

      if (bs_creative_map.size() > 0) {
        AddRate(bs_creative_map, 11, 10, FeaturePrefix::PHOTO_USER_AD_CRE_CTR, result);
        AddRate(bs_creative_map, 18, 10, FeaturePrefix::PHOTO_USER_AD_CRE_NTR, result);
      }

      if (bs_unit_map.size() > 0) {
        AddRate(bs_unit_map, 11, 10, FeaturePrefix::PHOTO_USER_AD_UNIT_CTR, result);
        AddRate(bs_unit_map, 18, 10, FeaturePrefix::PHOTO_USER_AD_UNIT_NTR, result);
      }

      if (bs_campaign_map.size() > 0) {
        AddRate(bs_campaign_map, 11, 10, FeaturePrefix::PHOTO_USER_AD_CAM_CTR, result);
        AddRate(bs_campaign_map, 18, 10, FeaturePrefix::PHOTO_USER_AD_CAM_CTR, result);
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdCount);
};

REGISTER_BS_EXTRACTOR(BSExtractAdCount);

}  // namespace ad_algorithm
}  // namespace ks
