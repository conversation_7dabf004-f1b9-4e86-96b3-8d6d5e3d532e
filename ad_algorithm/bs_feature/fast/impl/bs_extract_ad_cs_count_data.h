#pragma once
#include <cmath>
#include <random>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "third_party/protobuf_v3/src/google/protobuf/map.h"
#include "third_party/protobuf_v3/src/google/protobuf/message.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdCsCountData : public BSFastFeature {
 private:
  std::hash<std::string> hash_fn_;
  std::unordered_map<int, int> name_map_;
  int name_len_ = 0;
  std::vector<BSFieldEnum> key_enums;
  std::vector<BSFieldEnum> value_enums;
  std::vector<BSFieldEnum> size_enums;
  std::vector<uint64_t> count_ad_ids;

 public:
  BSExtractAdCsCountData() : BSFastFeature(FeatureType::ITEM, 16, 18, 18) {
    std::vector<std::string> names{
        "adPhotoImpression",
        "adItemImpression",
        "adItemClick",
        "adPhotoPlayed3s",
        "adConversion",
        "adPurchase",
        "adLandingPageFormSubmitted",
        "adItemDownloadCompleted",
        "adItemDownloadInstalled",
        "adLandingPageEntered",
    };
    name_len_ = names.size();
    for (int i = 0; i < name_len_; i++) {
      auto h = hash_fn_(names[i]);
      name_map_[h] = i;
    }

    key_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1094_key);
    value_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1094_value);
    size_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1094_size);
    count_ad_ids.push_back(1094);

    key_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1098_key);
    value_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1098_value);
    size_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1098_size);
    count_ad_ids.push_back(1098);

    key_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1100_key);
    value_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1100_value);
    size_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1100_size);
    count_ad_ids.push_back(1100);

    key_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1101_key);
    value_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1101_value);
    size_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1101_size);
    count_ad_ids.push_back(1101);

    // key_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1103_key);
    // value_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1103_value);
    // size_enums.push_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1103_size);
    // count_ad_ids.push_back(1103);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1101_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1094_value);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1098_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1100_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1100_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1103_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1101_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1103_value);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1094_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1098_value);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1100_value);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1098_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1101_value);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1103_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_1094_size);
  }

  inline bool get_counters(const BSMapField<absl::string_view, int64_t> &count_map, uint64 *photo_impression,
                           uint64 *item_impression, uint64 *item_click, uint64 *played_3s, uint64 *conversion,
                           uint64 *purchase, uint64 *lps, uint64 *download_completed,
                           uint64 *download_installed, uint64 *lpe) {
    int idx = 0;
    std::vector<uint64> result(name_len_, 0);
    for (size_t i = 0; i < count_map.size(); i++) {
      auto name = count_map.GetKey(i);
      auto val = count_map.GetValue(i);
      auto h = Hash(name);
      auto name_iter = name_map_.find(h);
      if (name_iter != name_map_.end()) {
        int n = name_iter->second;
        if (n < name_len_) result[n] = val;
        if (++idx > name_len_) break;
      }
    }
    *photo_impression = result[0];
    *item_impression = result[1];
    *item_click = result[2];
    *played_3s = result[3];
    *conversion = result[4];
    *purchase = result[5];
    *lps = result[6];
    *download_completed = result[7];
    *download_installed = result[8];
    *lpe = result[9];

    return true;
  }

  virtual void Extract(const BSLog &bslog, size_t pos, std::vector<ExtractResult> *result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info) {
      for (int i = 0; i < key_enums.size(); i++) {
        BSMapField<absl::string_view, int64_t> count_map(*bs, key_enums[i], value_enums[i], pos);
        if (count_map.is_empty() || count_map.size() == 0) {
          continue;
        }

        uint64_t count_ad_id = count_ad_ids[i];

        uint64 ad_photo_impression = 0;
        uint64 ad_item_impression = 0;
        uint64 ad_item_click = 0;
        uint64 ad_photo_played_3s = 0;
        uint64 ad_conversion = 0;
        uint64 ad_purchase = 0;
        uint64 ad_landing_page_form_submitted = 0;
        uint64 ad_item_download_completed = 0;
        uint64 ad_item_download_installed = 0;
        uint64 ad_landing_page_entered = 0;

        get_counters(count_map, &ad_photo_impression, &ad_item_impression, &ad_item_click,
                     &ad_photo_played_3s, &ad_conversion, &ad_purchase, &ad_landing_page_form_submitted,
                     &ad_item_download_completed, &ad_item_download_installed, &ad_landing_page_entered);

        AddFeature(GetFeature(AD_CS_COUNT_PHOTO_IMPRESSION, count_ad_id,
                              static_cast<int>(std::log2(ad_photo_impression + 1))),
                   1.0, result);
        AddFeature(GetFeature(AD_CS_COUNT_ITEM_IMPRESSION, count_ad_id,
                              static_cast<int>(std::log2(ad_item_impression + 1))),
                   1.0, result);
        AddFeature(
            GetFeature(AD_CS_COUNT_ITEM_CLICK, count_ad_id, static_cast<int>(std::log2(ad_item_click + 1))),
            1.0, result);
        AddFeature(
            GetFeature(AD_CS_COUNT_CONVERSION, count_ad_id, static_cast<int>(std::log2(ad_conversion + 1))),
            1.0, result);
        AddFeature(GetFeature(AD_CS_COUNT_FORM_SUBMITTED, count_ad_id,
                              static_cast<int>(std::log2(ad_landing_page_form_submitted + 1))),
                   1.0, result);
        AddFeature(GetFeature(AD_CS_COUNT_DOWNLOAD_COMPLETED, count_ad_id,
                              static_cast<int>(std::log2(ad_item_download_completed + 1))),
                   1.0, result);

        AddFeature(GetFeature(AD_CS_COUNT_SHOW_CLICK, count_ad_id,
                              static_cast<int>(std::log2(ad_photo_impression + 1)),
                              static_cast<int>(std::log2(ad_item_impression + 1))),
                   1.0, result);
        AddFeature(GetFeature(AD_CS_COUNT_CLICK_CLICK2, count_ad_id,
                              static_cast<int>(std::log2(ad_item_impression + 1)),
                              static_cast<int>(std::log2(ad_item_click + 1))),
                   1.0, result);
        AddFeature(GetFeature(AD_CS_COUNT_CLICK_CONV, count_ad_id,
                              static_cast<int>(std::log2(ad_item_impression + 1)),
                              static_cast<int>(std::log2(ad_conversion + 1))),
                   1.0, result);
        AddFeature(GetFeature(AD_CS_COUNT_CLICK_FORMSUB, count_ad_id,
                              static_cast<int>(std::log2(ad_item_impression + 1)),
                              static_cast<int>(std::log2(ad_landing_page_form_submitted + 1))),
                   1.0, result);
        AddFeature(GetFeature(AD_CS_COUNT_CLICK_DOWNLOAD, count_ad_id,
                              static_cast<int>(std::log2(ad_item_impression + 1)),
                              static_cast<int>(std::log2(ad_item_download_completed + 1))),
                   1.0, result);
      }
    }
  }

 private:
  // bool is_not_valid_counter(const ::bs::kuaishou::ad::CommonInfoAttr &commonAttr) {
  //   return !commonAttr.has_name_value() || !commonAttr.has_type() ||
  //          commonAttr.name_value() <
  //              ::bs::kuaishou::ad::CommonInfoAttr_Name::CommonInfoAttr_Name_DSP_PRODUCT_NAME_COUNT_MAP ||
  //          commonAttr.name_value() >
  //              ::bs::kuaishou::ad::CommonInfoAttr_Name::CommonInfoAttr_Name_DSP_INDUSTRY_ID_COUNT_MAP ||
  //          commonAttr.type() != ::bs::kuaishou::ad::CommonTypeEnum::MAP_STRING_INT64_ATTR;
  // }

  uint64 get_counter_val(const ::google::protobuf::Map< ::std::string, int64> &count_map,
                         const std::string &key) {
    const auto &count_res = count_map.find(key);
    if (count_res != count_map.end()) {
      return count_res->second;
    } else {
      return 0;
    }
  }
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdCsCountData);
};

REGISTER_BS_EXTRACTOR(BSExtractAdCsCountData);

}  // namespace ad_algorithm
}  // namespace ks
