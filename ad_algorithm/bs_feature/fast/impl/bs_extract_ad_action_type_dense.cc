#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_action_type_dense.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdActionTypeDense::BSExtractAdActionTypeDense() : BSFastFeature(DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type);
}

void BSExtractAdActionTypeDense::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_action_type = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type;
  int ocpc_action_type = BSFieldHelper::GetSingular<int>(*bs, enum_action_type, pos);

  AddFeature(0, ocpc_action_type, result);
}
}  // namespace ad_algorithm
}  // namespace ks
