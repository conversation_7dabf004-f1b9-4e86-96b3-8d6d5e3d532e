#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_brand_match_account_7d.h"

namespace ks {
namespace ad_algorithm {
BSExtractCombineBrandMatchAccount7D::
  BSExtractCombineBrandMatchAccount7D() : BSFastFeature(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_account_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_56002);
}
// pb 截断逻辑, bs 直接获取
void BSExtractCombineBrandMatchAccount7D::Extract(
  const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  bool has_ad_dsp_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
  bool has_unit =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_exists, pos);
  bool has_unit_base =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists, pos);
  int item_type =
      BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  uint64_t account_id = -1;
  if (has_ad_dsp_info && has_unit &&has_unit_base) {
    account_id = BSFieldHelper::GetSingular<uint64_t>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_account_id, pos);
  }
  int64_t imp_num = -1;
  int64_t clk_num = -1;
  int64_t lps_num = -1;
  int64_t paied_num = -1;
  int64_t dwl_num = -1;
  if (item_type == bs::ItemType::AD_BRAND) {
    BSRepeatedField<int64_t> attr(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_56002, pos);
    if (attr.size() <= 0 || attr.size() % brand_specific_size != 0) return;
    for (int i = 0; i < (attr.size() / brand_specific_size); i++) {
      int64_t account_id_history = attr.Get(i * brand_specific_size + account_offset);
      imp_num = 0;
      clk_num = 0;
      lps_num = 0;
      paied_num = 0;
      dwl_num = 0;
      if (account_id_history == account_id) {
        imp_num = attr.Get(i * brand_specific_size + imp_offset);
        clk_num = attr.Get(i * brand_specific_size + clk_offset);
        lps_num = attr.Get(i * brand_specific_size + lps_offset);
        paied_num = attr.Get(i * brand_specific_size + paied_offset);
        dwl_num = attr.Get(i * brand_specific_size + dwl_offset);
      }
    }
  }
  AddFeature(GetFeature(FeaturePrefix::AD_BRAND_MATCH_ACCOUNT_IMP_NUM_7D, imp_num), 1.0, result);
  AddFeature(GetFeature(FeaturePrefix::AD_BRAND_MATCH_ACCOUNT_CLK_NUM_7D, clk_num), 1.0, result);
  AddFeature(GetFeature(FeaturePrefix::AD_BRAND_MATCH_ACCOUNT_LPS_NUM_7D, lps_num), 1.0, result);
  AddFeature(GetFeature(FeaturePrefix::AD_BRAND_MATCH_ACCOUNT_PAIED_NUM_7D, paied_num), 1.0, result);
  AddFeature(GetFeature(FeaturePrefix::AD_BRAND_MATCH_ACCOUNT_DWL_NUM_7D, dwl_num), 1.0, result);
}

}  // namespace ad_algorithm
}  // namespace ks
