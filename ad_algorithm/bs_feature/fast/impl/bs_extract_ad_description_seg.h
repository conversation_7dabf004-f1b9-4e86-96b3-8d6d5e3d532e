#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "ks/util/json.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdDescriptionSeg : public BSFastFeature {
 public:
  BSExtractAdDescriptionSeg() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_2056);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    int32_t common_info_attr_size = BSFieldHelper::GetSingular<int32_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size, pos);
    auto item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info &&
        common_info_attr_size > 0) {
      auto attr_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_2056;
      BSRepeatedField<int64_t> attr(*bs, attr_id, pos);
      int max_num = attr.size() > 100 ? 100 : attr.size();
      for (int i = 0; i < max_num; ++i) {
        AddFeature(GetFeature(FeaturePrefix::PHOTO_DESCRIPTION, attr.Get(i)), 1.0f, result);
      }
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"item.ad_dsp_info.common_info_attr.DESCRIPTION_INDEX"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdDescriptionSeg);
};

REGISTER_BS_EXTRACTOR(BSExtractAdDescriptionSeg);

}  // namespace ad_algorithm
}  // namespace ks
