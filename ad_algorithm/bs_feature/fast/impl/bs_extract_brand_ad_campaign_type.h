#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandAdCampaignType : public BSFastFeature {
 public:
  BSExtractBrandAdCampaignType() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    bool has_campaign =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists, pos);
    bool has_base =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists, pos);

    if (has_ad_dsp_info && has_campaign && has_base) {
      uint64_t ad_campaign_type = BSFieldHelper::GetSingular<uint64_t>(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
      AddFeature(GetFeature(FeaturePrefix::PHOTO_CAMPAIGN_TYPE, ad_campaign_type), 1.0f, result);
    }
  }

 private:
  const std::string USED_FEATURES[2] = {"item.ad_dsp_info.campaign.base.type", "item.type"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandAdCampaignType);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandAdCampaignType);

}  // namespace ad_algorithm
}  // namespace ks
