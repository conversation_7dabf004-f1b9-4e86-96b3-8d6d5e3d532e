#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_recall_target_info.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdRecallTargetInfo::BSExtractAdRecallTargetInfo() : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_raw_target_info_gender);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_raw_target_info_platform);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_raw_target_info_age_range);
}

void BSExtractAdRecallTargetInfo::Extract(const BSLog& bslog, size_t pos,
                                          std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists;
  bool base_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_base_exists, pos);

  auto enum_unit_exists = BSFieldEnum::adlog_item_ad_dsp_info_unit_exists;
  bool unit_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_unit_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && info_exists &&
      unit_exists && base_exists) {
    int target_arr[16];

    for (int k = 0; k <= 15; k++) target_arr[k] = 0;  // init
    int age_range_arr[10] = {0, 12, 18, 24, 31, 36, 41, 50, 61, 101};

    auto get_age = [&](const auto& age) {
      for (int m = 0; m < 9; m++) {
        if (age >= age_range_arr[m] && age < age_range_arr[m + 1]) {
          return m;
        }
      }
      return -1;
    };
    // age
    auto enum_age_range = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_raw_target_info_age_range;
    BSRepeatedField<int32_t> age_range(*bs, enum_age_range, pos);

    if (age_range.size() == 0) {
      for (int age_k = 6; age_k <= 15; age_k++) target_arr[age_k] = 1;
    } else {
      for (int age_i = 0; age_i < age_range.size() - 1; age_i += 2) {
        int age_min = get_age(age_range.Get(age_i));
        int age_max = get_age(age_range.Get(age_i + 1));
        if (age_min >= 0) {
          for (int m_n = age_min + 6; m_n <= age_max + 6; m_n++) target_arr[m_n] = 1;
        }
      }
    }

    // gender
    auto enum_info_gender = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_raw_target_info_gender;
    BSRepeatedField<absl::string_view> info_gender(*bs, enum_info_gender, pos);

    if (info_gender.size() == 0) {
      target_arr[0] = 1;
      target_arr[1] = 1;
      target_arr[2] = 1;
    } else {
      for (int gender_i = 0; gender_i < info_gender.size(); gender_i++) {
        if (info_gender.Get(gender_i) == "M")
          target_arr[0] = 1;
        else if (info_gender.Get(gender_i) == "F")
          target_arr[1] = 1;
      }
    }

    // platform
    auto enum_info_platform = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_raw_target_info_platform;
    BSRepeatedField<absl::string_view> info_platform(*bs, enum_info_platform, pos);

    if (info_platform.size() == 0) {
      target_arr[3] = 1;
      target_arr[4] = 1;
      target_arr[5] = 1;
    } else {
      for (int platform_i = 0; platform_i < info_platform.size(); platform_i++) {
        if (info_platform.Get(platform_i) == "ios")
          target_arr[3] = 1;
        else if (info_platform.Get(platform_i) == "android")
          target_arr[4] = 1;
      }
    }

    target_arr[2] = 1;
    target_arr[5] = 1;
    target_arr[15] = 1;

    for (int k = 0; k < 16; k++) {
      if (target_arr[k] == 1)
        AddFeature(k, 1, result);
      else
        AddFeature(k, 0, result);
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
