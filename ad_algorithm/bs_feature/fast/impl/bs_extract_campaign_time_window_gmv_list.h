#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_inner_live_item_feature.dark
class BSExtractCampaignTimeWindowGmvList : public BSFastFeature {
 public:
  BSExtractCampaignTimeWindowGmvList();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCampaignTimeWindowGmvList);
};

REGISTER_BS_EXTRACTOR(BSExtractCampaignTimeWindowGmvList);
}  // namespace ad_algorithm
}  // namespace ks
