#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_corporation_name.h"

namespace ks {
namespace ad_algorithm {

BSExtractBrandCorporationName::BSExtractBrandCorporationName() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
  attr_metas_.emplace_back(
    BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_corporation_name);
  attr_metas_.emplace_back(
    BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists);
}

void BSExtractBrandCorporationName::Extract(
           const BSLog &bslog, size_t pos, std::vector<ExtractResult> *result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto key_advertiser_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists;
  bool has_advertiser_base = BSFieldHelper::GetSingular<bool>(*bs, key_advertiser_base_exists, pos);
  if (!has_advertiser_base) return;

  auto corporation_name = BSFieldHelper::GetSingular<absl::string_view>(*bs,
                          BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_corporation_name, pos);
  if (corporation_name.size() > 0) {
    AddFeature(ad_nn::bs::Hash(corporation_name), 1.0f, result);
  }
}


}  // namespace ad_algorithm
}  // namespace ks
