#pragma once
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdPcreativeDescription : public BSFastFeature {
 public:
  BSExtractAdPcreativeDescription() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_233);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    if (!has_ad_dsp_info) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if (item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) {
      BSRepeatedField<float> attr_description(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_233, pos);
      if (attr_description.is_empty() || attr_description.size() != 34) {
        return;
      }
      AddFeature(GetFeature(FeaturePrefix::PHOTO_DESCRIPTION, static_cast<int>(attr_description.Get(32))),
                 1.0f, result);
      AddFeature(GetFeature(FeaturePrefix::PHOTO_ACTIONBAR, static_cast<int>(attr_description.Get(33))), 1.0f,
                 result);
    }
  }

 private:
  const std::string USED_FEATURES[1] = {
      "item.ad_dsp_info.common_info_attr.PCREATIVE_SELECT_CREATIVE_DESC_BERT"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdPcreativeDescription);
};

REGISTER_BS_EXTRACTOR(BSExtractAdPcreativeDescription);

}  // namespace ad_algorithm
}  // namespace ks
