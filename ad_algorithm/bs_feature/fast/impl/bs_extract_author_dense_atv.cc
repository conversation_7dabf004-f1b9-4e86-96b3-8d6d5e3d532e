/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_dense_atv.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/client_industry_checker.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/merge_list.h"

namespace ks {
namespace ad_algorithm {

BSExtractAuthorDenseAtv::BSExtractAuthorDenseAtv() : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5102747);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5102748);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5102749);
}
void BSExtractAuthorDenseAtv::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 =
      get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5102747, pos);
  auto x2 =
      get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5102748, pos);
  auto x3 =
      get_bslog_float(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5102749, pos);
  auto x4 = checkclientindustry(x1, x2, x3);
  auto x6 = merge_float_list_all(x4, x3);
  add_feature_result(x6, 2, result);
}

}  // namespace ad_algorithm
}  // namespace ks
