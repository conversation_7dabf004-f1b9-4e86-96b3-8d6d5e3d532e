#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAuthorFollowCount : public BSFastFeatureNoPrefix {
 public:
  BSExtractAuthorFollowCount();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  const std::string USED_FEATURES[1] = {"item.fans_top_info.photo_info.author_info.follow_count"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorFollowCount);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorFollowCount);

}  // namespace ad_algorithm
}  // namespace ks
