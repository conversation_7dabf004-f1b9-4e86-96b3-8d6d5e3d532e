/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_ad_clk_sim_tier.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/action_list_op.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

BSExtractCombineAdClkSimTier::BSExtractCombineAdClkSimTier()
    : BSFastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_5102165);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5102960);
}
void BSExtractCombineAdClkSimTier::Extract(const BSLog& bslog, size_t pos,
                                           std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_float_list(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_5102165, pos);
  auto x2 = get_bslog_float_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5102960, pos);
  auto x4 = sim_tier(x1, x2, 8);
  add_feature_result(x4, result);
}

}  // namespace ad_algorithm
}  // namespace ks
