#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_industry_group.h"
namespace ks {
namespace ad_algorithm {

BSExtractBrandIndustryGroup::BSExtractBrandIndustryGroup() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_group);
}

void BSExtractBrandIndustryGroup::Extract(
            const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  bool has_advertiser_base = BSFieldHelper::GetSingular<bool>(*bs,
                 BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists, pos);
  if (!has_advertiser_base) return;

  absl::string_view industry_group = BSFieldHelper::GetSingular<absl::string_view>(*bs,
                    BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_group, pos);
  AddFeature(ad_nn::bs::Hash(industry_group), 1.0f, result);
}

}  // namespace ad_algorithm
}  // namespace ks
