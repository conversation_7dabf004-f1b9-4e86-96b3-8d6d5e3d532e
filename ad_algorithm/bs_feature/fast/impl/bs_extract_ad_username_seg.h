#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "ks/util/json.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdUsernameSeg : public BSFastFeature {
 public:
  BSExtractAdUsernameSeg() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_2057);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info) {
      auto key_enum = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_2057;
      BSRepeatedField<int> attr(*bs, key_enum, pos);
      if (attr.is_empty()) {
        return;
      }
      int max_num = attr.size() > 100 ? 100 : attr.size();
      for (int i = 0; i < max_num; ++i) {
        AddFeature(GetFeature(FeaturePrefix::PHOTO_USERNAME, attr.Get(i)), 1.0f, result);
      }
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"item.ad_dsp_info.common_info_attr.USERNAME_INDEX"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdUsernameSeg);
};

REGISTER_BS_EXTRACTOR(BSExtractAdUsernameSeg);

}  // namespace ad_algorithm
}  // namespace ks
