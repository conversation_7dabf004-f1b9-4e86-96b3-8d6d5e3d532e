#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_innerloop_item_inheritees_v3.h"

namespace ks {
namespace ad_algorithm {
using BSExtractPhotoIdStable = BSExtractAdInnerloopItemInheriteesV3<0>;
REGISTER_BS_EXTRACTOR(BSExtractPhotoIdStable);

using BSExtractCreativeIdStable = BSExtractAdInnerloopItemInheriteesV3<1>;
REGISTER_BS_EXTRACTOR(BSExtractCreativeIdStable);

using BSExtractUnitCampaignIdStable = BSExtractAdInnerloopItemInheriteesV3<2>;
REGISTER_BS_EXTRACTOR(BSExtractUnitCampaignIdStable);

using BSExtractCreativeSignatureStable = BSExtractAdInnerloopItemInheriteesV3<3>;
REGISTER_BS_EXTRACTOR(BSExtractCreativeSignatureStable);

using BSExtractAdCreateTimeStable = BSExtractAdInnerloopItemInheriteesV3<4>;
REGISTER_BS_EXTRACTOR(BSExtractAdCreateTimeStable);

}  // namespace ad_algorithm
}  // namespace ks
