#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_delay_label.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdDelayLabel::BSExtractAdDelayLabel() : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_label_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_label_info_label_info_attr_key_1006);
}

void BSExtractAdDelayLabel::Extract(const BSLog &bslog, size_t pos, std::vector<ExtractResult> *result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  auto adlog_item_type = BSFieldEnum::adlog_item_type;
  auto key_1006 = BSFieldEnum::adlog_item_label_info_label_info_attr_key_1006;
  auto item_type = BSFieldHelper::GetSingular<int64_t>(*bs, adlog_item_type, pos);
  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) &&
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_label_info_exists, pos)) {
    bool has_val = false;
    auto target = BSFieldHelper::GetSingular<int64_t>(*bs, key_1006, pos, &has_val);
    if (has_val) {
      AddFeature(0, target, result);
    }
  }
}

}  // namespace ad_algorithm
}  // namespace ks
