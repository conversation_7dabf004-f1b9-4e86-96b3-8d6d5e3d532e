#pragma once
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdPcreativeCost : public BSFastFeature {
 public:
  BSExtractAdPcreativeCost() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_231);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    auto item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    bool has_dsp_info = BSFieldHelper::HasSingular<int>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_dsp_info) {
      auto key_keyword = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_231;
      BSRepeatedField<int64_t> photo_cost(*bs, key_keyword, pos);
      if (photo_cost.size() == 7) {
        for (int32_t i = 0; i < photo_cost.size(); i++) {
          int64_t costVal = photo_cost.Get(i);
          AddFeature(GetFeature(FeaturePrefix::PCREATIVE_CREATIVE_COST, costVal), 1.0f, result);
        }
      }
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"item.ad_dsp_info.common_info_attr.PCREATIVE_SELECT_CREATIVE_COST"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdPcreativeCost);
};

REGISTER_BS_EXTRACTOR(BSExtractAdPcreativeCost);

}  // namespace ad_algorithm
}  // namespace ks
