#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAuthorWeekGmvP90BoolMask : public BSFastFeature {
 public:
  BSExtractAuthorWeekGmvP90BoolMask();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  std::vector<float> segments_ = {0.001, 1.0,   8.0,   18.0,  28.0,  38.0,  48.0,  58.0,  68.0,  78.0,
                                  98.0,  108.0, 168.0, 200.0, 250.0, 300.0, 350.0, 400.0, 500.0, 600.0};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorWeekGmvP90BoolMask);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorWeekGmvP90BoolMask);

}  // namespace ad_algorithm
}  // namespace ks
