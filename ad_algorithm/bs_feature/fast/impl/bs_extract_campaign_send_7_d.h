#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_inner_live_item_feature.dark
class BSExtractCampaignSend7D : public BSFastFeatureNoPrefix {
 public:
  BSExtractCampaignSend7D();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCampaignSend7D);
};

REGISTER_BS_EXTRACTOR(BSExtractCampaignSend7D);
}  // namespace ad_algorithm
}  // namespace ks
