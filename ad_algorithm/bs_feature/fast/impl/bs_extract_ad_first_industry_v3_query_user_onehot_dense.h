#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
namespace ks {
namespace ad_algorithm {

class BSExtractAdFirstIndustryV3QueryUserOnehotDense : public BSFastFeature {
 public:
  BSExtractAdFirstIndustryV3QueryUserOnehotDense();
  std::vector<int> class_type_ = {10101, 10201, 10202, 10203, 10204, 10205, 10206, 10207, 10208,
                                  10209, 10210, 10211, 10212, 10213, 10214, 10301, 10302, 10401,
                                  10501, 10601, 10602, 10701, 10702, 10801, 10901, 10902, 11001,
                                  11101, 11201, 11301, 11401, 11501, 11601, 11602};

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdFirstIndustryV3QueryUserOnehotDense);
};

REGISTER_BS_EXTRACTOR(BSExtractAdFirstIndustryV3QueryUserOnehotDense);

}  // namespace ad_algorithm
}  // namespace ks
