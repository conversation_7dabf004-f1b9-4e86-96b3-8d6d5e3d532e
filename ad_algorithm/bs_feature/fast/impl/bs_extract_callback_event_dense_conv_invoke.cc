#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_callback_event_dense_conv_invoke.h"
namespace ks {
namespace ad_algorithm {
BSExtractCallbackEventDenseConvInvoke::BSExtractCallbackEventDenseConvInvoke()
    : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_callback_event);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_callback_event_size);
}

void BSExtractCallbackEventDenseConvInvoke::Extract(const BSLog& bslog, size_t pos,
                                                    std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  if (item_type != bs::ItemType::AD_DSP) {
    return;
  }

  AdCallbackLog::EventType callback_event;
  auto enum_type_exists = BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_exists;
  bool type_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_type_exists, pos);
  size_t callback_event_size = BSFieldHelper::GetSingular<size_t>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_callback_event_size, pos);

  auto key_enum = BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_callback_event;
  BSRepeatedField<int> event_list(*bs, key_enum, pos);
  if (bslog.is_train()) {
    if (type_exists && callback_event_size > 0) {
      callback_event = static_cast<AdCallbackLog::EventType>(event_list.Get(0));
    } else {
      callback_event = ::bs::kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN;
    }
  } else {
    callback_event = static_cast<AdCallbackLog::EventType>(bslog.get_ad_callback_event(pos));
  }

  if (callback_event == ::bs::kuaishou::ad::AdCallbackLog_EventType_EVENT_CONVERSION) {
    AddFeature(0, 1, result);
    AddFeature(1, 0, result);
  } else if (callback_event == ::bs::kuaishou::ad::AdCallbackLog_EventType_EVENT_APP_INVOKED) {
    AddFeature(0, 0, result);
    AddFeature(1, 1, result);
  } else {
    AddFeature(0, 0, result);
    AddFeature(1, 0, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
