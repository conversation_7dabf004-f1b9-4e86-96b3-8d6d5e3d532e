/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_clk_cate_match_ratio_dense.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_realtime_cross_feature.h"
#include "teams/ad/ad_algorithm/fed/compute/merge_list.h"

namespace ks {
namespace ad_algorithm {

BSExtractClkCateMatchRatioDense::BSExtractClkCateMatchRatioDense()
    : BSFastFeature(FeatureType::DENSE_COMBINE) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5003268);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5100040);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5003269);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5100041);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5003270);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5100039);
}
void BSExtractClkCateMatchRatioDense::Extract(const BSLog& bslog, size_t pos,
                                              std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5003268, pos);
  auto x2 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5100040, pos);
  auto x3 = get_realtime_target_id_match_ratio(x1, x2);
  auto x4 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5003269, pos);
  auto x5 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5100041, pos);
  auto x6 = get_realtime_target_id_match_ratio(x4, x5);
  auto x7 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5003270, pos);
  auto x8 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5100039, pos);
  auto x9 = get_realtime_target_id_match_ratio(x7, x8);
  auto x10 = merge_float_list_all(x3, x6, x9);
  add_feature_result(x10, 3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
