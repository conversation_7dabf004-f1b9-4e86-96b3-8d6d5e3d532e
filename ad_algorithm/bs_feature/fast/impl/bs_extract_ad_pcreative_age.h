#pragma once
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdPcreativeAge : public BSFastFeature {
 public:
  BSExtractAdPcreativeAge() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_232);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);

    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info) {
      auto key_age = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_232;
      bool has_value = false;
      uint64 age = ad_nn::BSFieldHelper::GetSingular<uint64_t>(*bs, key_age, pos, &has_value);
      if (has_value) {
        AddFeature(GetFeature(FeaturePrefix::PCREATIVE_CREATIVE_AGE, age), 1.0f, result);
      }
    }
  }

 private:
  const std::string USED_FEATURES[1] = {
      "item.ad_dsp_info.common_info_attr.PCREATIVE_SELECT_CREATIVE_LIVE_TIME"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdPcreativeAge);
};

REGISTER_BS_EXTRACTOR(BSExtractAdPcreativeAge);

}  // namespace ad_algorithm
}  // namespace ks
