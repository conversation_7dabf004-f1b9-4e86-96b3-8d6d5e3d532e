#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_combine_llm_emb_sim_tier.dark
class BSExtractCombineAdClkSimTier : public BSFastFeatureNoPrefix {
 public:
  BSExtractCombineAdClkSimTier();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineAdClkSimTier);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineAdClkSimTier);
}  // namespace ad_algorithm
}  // namespace ks
