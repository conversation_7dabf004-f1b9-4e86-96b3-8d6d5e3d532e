#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdSmartMatchingThresNebula2 : public BSFastFeature {
 public:
  BSExtractAdSmartMatchingThresNebula2() : BSFastFeature(FeatureType::DENSE_ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_4005);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    auto key_common_info_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size;
    int64_t common_info_attr_size = BSFieldHelper::GetSingular<int64_t>(*bs, key_common_info_attr_size, pos);
    if (common_info_attr_size > 0) {
      auto column_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_4005;
      float smart_matching_creative_thres_nebula2 = BSFieldHelper::GetSingular<float>(*bs, column_id, pos);
      if (smart_matching_creative_thres_nebula2 != -1.0f) {
        AddFeature(0, smart_matching_creative_thres_nebula2, result);
      } else {
        AddFeature(0, -1.0f, result);
      }
    }
  }

 private:
  const std::string USED_FEATURES[1] = {
      "item.ad_dsp_info.common_info_attr.SMART_MATCHING_CREATIVE_THRES_NEBULA2"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdSmartMatchingThresNebula2);
};

REGISTER_BS_EXTRACTOR(BSExtractAdSmartMatchingThresNebula2);

}  // namespace ad_algorithm
}  // namespace ks
