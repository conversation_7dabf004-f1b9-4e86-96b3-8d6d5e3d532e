#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_promotion_type.h"

namespace ks {
namespace ad_algorithm {

void BSExtractAdPromotionType::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  bool has_ad_dsp_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
  if (!has_ad_dsp_info) {
    return;
  }
  size_t attr_size =
      BSFieldHelper::GetSingular<size_t>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size, pos);
  int64_t value = 0;
  if (attr_size > 0) {
    value = BSFieldHelper::GetSingular<int64_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_260001, pos);
  }
  AddFeature(value, 1.0f, result);
}

}  // namespace ad_algorithm
}  // namespace ks
