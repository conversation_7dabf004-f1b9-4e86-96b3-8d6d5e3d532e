#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdStatisPlay5sRatio : public BSFastFeature {
 public:
  BSExtractAdStatisPlay5sRatio() : BSFastFeature(FeatureType::DENSE_ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_value);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    auto key_enum1 = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_enum1, pos);
    if (has_ad_dsp_info) {
      int play5s = 0;
      int impression = 0;
      auto key_enum2 = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_key;
      auto key_enum3 = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_value;
      BSRepeatedField<absl::string_view> keys(*bs, key_enum2, pos);
      BSRepeatedField<int> values(*bs, key_enum3, pos);
      for (int i = 0; i < keys.size(); ++i) {
        auto key = keys.Get(i);
        if (key == "play5s") {
          play5s = values.Get(i);
        } else if (key == "impression") {
          impression = values.Get(i);
        }
      }
      double ratio = 0;
      if (impression >= 200) {
        ratio = 1.0 * play5s / impression;
      }
      if (ratio > 1) {
        ratio = 1;
      }
      AddFeature(0, ratio, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdStatisPlay5sRatio);
};

REGISTER_BS_EXTRACTOR(BSExtractAdStatisPlay5sRatio);

}  // namespace ad_algorithm
}  // namespace ks
