#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/hash_function/city.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_feature.pb.h"
#include "teams/ad/ad_base/src/hash/hash.h"
using std::string;

namespace ks {
namespace ad_algorithm {

class BSExtractBrandBaseUriLps : public BSFastFeature {
 public:
  BSExtractBrandBaseUriLps() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_uri);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    bool has_campaign =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists, pos);
    bool has_campaign_base =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists, pos);
    bool has_unit =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_exists, pos);
    bool has_unit_base =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists, pos);
    if (!has_ad_dsp_info || !has_campaign || !has_campaign_base || !has_unit || !has_unit_base) {
      return;
    }
    auto key_ad_campaign_type = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type;
    uint64_t ad_campaign_type = BSFieldHelper::GetSingular<uint64_t>(*bs, key_ad_campaign_type, pos);
    auto key_ocpc_action_type = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type;
    uint64_t ocpc_action_type = BSFieldHelper::GetSingular<uint64_t>(*bs, key_ocpc_action_type, pos);
    if (ocpc_action_type == ::bs::kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) {
      std::string uri{BSFieldHelper::GetSingular<absl::string_view>(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_uri, pos)};
      if (uri.length() > 0) {
        std::vector<std::string> tokens;
        base::SplitString(uri, std::string("?"), &tokens);
        if (tokens.size() > 0) {
          uint64 uri_hash = base::CityHash64(tokens[0].c_str(), tokens[0].length());
          AddFeature(GetFeature(FeaturePrefix::PHOTO_BASE_URI, uri_hash), 1.0f, result);
        }
      }
    }
  }

 private:
  const std::string USED_FEATURES[2] = {"item.ad_dsp_info.campaign.base.type",
                                        "item.ad_dsp_info.unit.base.uri"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandBaseUriLps);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandBaseUriLps);

}  // namespace ad_algorithm
}  // namespace ks
