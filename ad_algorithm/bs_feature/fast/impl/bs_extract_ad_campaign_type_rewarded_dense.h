#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdCampaignTypeRewardedDense : public BSFastFeature {
 public:
  BSExtractAdCampaignTypeRewardedDense() : BSFastFeature(FeatureType::DENSE_ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if (item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) {
      int num = 0;
      int ad_campaign_type =
          BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
      if (ad_campaign_type == 13 || ad_campaign_type == 3) {
        num = 1;
      }
      bool has_dsp_creative =
          BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_exists, pos);
      bool has_creative_base = BSFieldHelper::GetSingular<bool>(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists, pos);
      if (has_dsp_creative && has_creative_base) {
        auto key_id_v3 = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3;
        uint64_t industry_v3_id = BSFieldHelper::GetSingular<uint64_t>(*bs, key_id_v3, pos);
        if ((ad_campaign_type == 2 || ad_campaign_type == 4 || ad_campaign_type == 5 ||
             ad_campaign_type == 7) &&
            (industry_v3_id <= 1089 && industry_v3_id >= 1032)) {
          num = 1;
        }
      }
      AddFeature(0, num, result);
      AddFeature(1, 1 - num, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdCampaignTypeRewardedDense);
};

REGISTER_BS_EXTRACTOR(BSExtractAdCampaignTypeRewardedDense);

}  // namespace ad_algorithm
}  // namespace ks
