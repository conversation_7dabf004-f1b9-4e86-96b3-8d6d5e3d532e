#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAuthorUploadCount : public BSFastFeatureNoPrefix {
 public:
  BSExtractAuthorUploadCount() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_attribute_upload_count);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_fans_top_info_photo_info_author_info_attribute_upload_count);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_item_nature_photo_info_photo_info_author_info_attribute_upload_count);
    bs_util.BSHasPhotoInfoAuthorInfo.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoAttributeUploadCount.fill_attr_metas(&attr_metas_);
    bs_util.BSHasPhotoInfoAuthorInfoAttribute.fill_attr_metas(&attr_metas_);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_author_info = bs_util.BSHasPhotoInfoAuthorInfo(bs, pos);
    if (!has_author_info) {
      return;
    }
    bool has_attribute = bs_util.BSHasPhotoInfoAuthorInfoAttribute(bs, pos);
    if (!has_attribute) {
      return;
    }
    int64_t upload_count = bs_util.BSGetPhotoInfoAuthorInfoAttributeUploadCount(bs, pos);
    AddFeature(static_cast<int>(log(upload_count + 2)), 1.0f, result);
  }

 private:
  const std::string USED_FEATURES[1] = {"item.fans_top_info.photo_info.author_info.upload_count"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorUploadCount);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorUploadCount);

}  // namespace ad_algorithm
}  // namespace ks
