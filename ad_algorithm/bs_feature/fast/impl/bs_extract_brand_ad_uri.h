#pragma once
#include <string>
#include <vector>

#include "base/hash_function/city.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
using std::string;

namespace ks {
namespace ad_algorithm {

class BSExtractBrandAdUri : public BSFastFeature {
 public:
  BSExtractBrandAdUri();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandAdUri);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandAdUri);

}  // namespace ad_algorithm
}  // namespace ks
