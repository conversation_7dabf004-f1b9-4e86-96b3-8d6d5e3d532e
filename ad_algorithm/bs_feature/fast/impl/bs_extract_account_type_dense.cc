/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_account_type_dense.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_account_type_search_roi.h"

namespace ks {
namespace ad_algorithm {

BSExtractAccountTypeDense::BSExtractAccountTypeDense() : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_53000);
}
void BSExtractAccountTypeDense::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_53000, pos);
  auto x2 = get_account_type_search_roi(x1);
  add_feature_result(x2, 1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
