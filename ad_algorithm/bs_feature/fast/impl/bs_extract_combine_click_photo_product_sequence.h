#pragma once
#include <algorithm>
#include <string>
#include <vector>

#include "base/hash_function/city.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {
class BSExtractCombineClickPhotoProductSequence : public BSFastFeature {
 public:
  BSExtractCombineClickPhotoProductSequence() : BSFastFeature(COMBINE, 26, 26) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_size);
    attr_metas_.emplace_back(
        BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_2_list_product_id_hash);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists);
  }

  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    auto field_u_elta_size = BSFieldEnum::adlog_user_info_explore_long_term_ad_action_size;
    auto field_i_adv_exists = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists;
    auto item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if (BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_exists, pos) &&
        BSFieldHelper::GetSingular<int, true>(*bs, field_u_elta_size, pos) > 0 &&
        (item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) &&
        BSFieldHelper::GetSingular<bool>(*bs, field_i_adv_exists, pos)) {
      auto field_i_prod_name = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name;
      absl::string_view product_name =
          BSFieldHelper::GetSingular<absl::string_view>(*bs, field_i_prod_name, pos);
      auto field_u_elta = BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_2_list_product_id_hash;
      BSRepeatedField<int64_t> product_id_list(*bs, field_u_elta, pos);
      for (int i = 0; i < std::min(100, product_id_list.size()); i++) {
        AddFeature(GetFeature(FeaturePrefix::COMBINE_CLICK_PRODUCT_SEQ, product_id_list.Get(i),
                              base::CityHash64(product_name.data(), product_name.size())),
                   1.0, result);
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineClickPhotoProductSequence);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineClickPhotoProductSequence);

}  // namespace ad_algorithm
}  // namespace ks
