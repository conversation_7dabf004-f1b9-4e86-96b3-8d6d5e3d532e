#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
namespace ks {
namespace ad_algorithm {

template <BSFieldEnum item_attr_name>
class BSExtractAdLocalFeature : public BSFastFeature {
 public:
  BSExtractAdLocalFeature();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  const std::string USED_FEATURES[35] = {"item.ad_dsp_info.common_info_attr.LOCAL_POI_ID",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_POI_NAME",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_POI_CATE1_ID",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_POI_CATE1_ID",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_POI_CATE3_ID",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_POI_PROVINCE_NAME",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_POI_CITY_NAME",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_POI_DISTRICT_NAME",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_SECTION_FIRST_CLASS_NAME",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_SECTION_SECOND_CLASS_NAME",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_FANS_RANGE",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_MANUAL_RELATE_TYPE"
                                         "item.ad_dsp_info.common_info_attr.LOCAL_FIRST_INTEREST_CAT",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_SECOND_INTEREST_CAT",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_HANGING_GOODS_TYPE",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_LOCAL_RULE_TAG_TYPE",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_IS_MMU_ES_PHOTO",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_IS_SUPERIOR_MMU_ES_PHOTO",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_BRAND_NAME",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_HOTVIEW_PHOTO_TYPE",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_SENSE_PHOTO_TYPE",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_POI_CLICK_CNT_7D",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_PAY_ORDER_NUM_3D",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_PAY_ORDER_NUM_7D"
                                         "item.ad_dsp_info.common_info_attr.LOCAL_POI_COLLECT_USER_NUM"
                                         "item.ad_dsp_info.common_info_attr.LOCAL_POI_CTR_3D",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_POI_CTR_7D",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_GOODS_CTR_3D",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_GOODS_CTR_7D",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_PAY_CVR_3D",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_PAY_CVR_7D",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_PAY_ORDER_AMT_3D",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_PAY_ORDER_AMT_7D",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_SENSE_PHOTO_TYPE",
                                         "item.ad_dsp_info.common_info_attr.LOCAL_GPM_7D"};
  BSFieldEnum item_attr_name_{item_attr_name};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdLocalFeature);
};

}  // namespace ad_algorithm
}  // namespace ks
