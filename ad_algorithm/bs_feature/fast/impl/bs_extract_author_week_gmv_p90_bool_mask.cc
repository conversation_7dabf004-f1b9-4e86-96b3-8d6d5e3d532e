#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_week_gmv_p90_bool_mask.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/action.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {
BSExtractAuthorWeekGmvP90BoolMask::BSExtractAuthorWeekGmvP90BoolMask() : BSFastFeature(DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82079);
}
void BSExtractAuthorWeekGmvP90BoolMask::Extract(const BSLog& bslog, size_t pos,
                                                std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  auto enum_key_82079 = BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82079;
  absl::optional<float> key_82079_opt = get_bslog_float(*bs, enum_key_82079, pos);
  float gmv_max = key_82079_opt.value_or(1092.0814);
  std::vector<ExtractResult> result_value = find_mask_from_segments_by_target_value(gmv_max, segments_);
  add_feature_result(result_value, result);
}
}  // namespace ad_algorithm
}  // namespace ks
