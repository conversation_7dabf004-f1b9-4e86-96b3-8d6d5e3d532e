#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/proto/bs_common_info_attr_enum.pb.h"

namespace ks {
namespace ad_algorithm {
using CommonInfoAttr = ::bs::kuaishou::ad::CommonInfoAttr;

template <int no>
class BSExtractAdCarTag : public BSFastFeatureNoPrefix {
 public:
  BSExtractAdCarTag() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);

    BSGetItemAdDspInfoPhotoInfoCommonInfoAttrNo.fill_attr_metas(&attr_metas_);
    BSHasItemAdDspInfoPhotoInfoCommonInfoAttrNo.fill_attr_metas(&attr_metas_);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

    auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size;
    int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);

    auto enum_photo_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists;
    bool photo_info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_photo_info_exists, pos);

    auto enum_item_type = BSFieldEnum::adlog_item_type;
    int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

    int64_t attr_no = BSGetItemAdDspInfoPhotoInfoCommonInfoAttrNo(bs, pos);

    if ((item_type == bs::ItemType::AD_DSP) && info_exists && photo_info_exists && attr_size > 0) {
      if (BSHasItemAdDspInfoPhotoInfoCommonInfoAttrNo(bs, pos)) {
        AddFeature(attr_no, 1.0, result);
      }
    }
  }

 private:
  BSFixedCommonInfo<int64_t> BSGetItemAdDspInfoPhotoInfoCommonInfoAttrNo{
      "adlog.item.ad_dsp_info.photo_info.common_info_attr", no};
  BSHasFixedCommonInfoImpl<int64_t, false> BSHasItemAdDspInfoPhotoInfoCommonInfoAttrNo{
      "adlog.item.ad_dsp_info.photo_info.common_info_attr", no};

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdCarTag);
};

using BSExtractAdCarBrand = BSExtractAdCarTag<CommonInfoAttr::CAR_BRAND_TAG>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarBrand);

using BSExtractAdCarPrice = BSExtractAdCarTag<CommonInfoAttr::CAR_PRICE_TAG>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarPrice);

using BSExtractAdCarPower = BSExtractAdCarTag<CommonInfoAttr::CAR_POWER_TAG>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarPower);

using BSExtractAdCarCountry = BSExtractAdCarTag<CommonInfoAttr::CAR_COUNTRY_TAG>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarCountry);

using BSExtractAdCarOld = BSExtractAdCarTag<CommonInfoAttr::CAR_OLD_TAG>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarOld);

using BSExtractAdCarSeat = BSExtractAdCarTag<CommonInfoAttr::CAR_SEAT_TAG>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarSeat);

using BSExtractAdCarEffect = BSExtractAdCarTag<CommonInfoAttr::CAR_EFFECT_TAG>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarEffect);

using BSExtractAdCarType = BSExtractAdCarTag<CommonInfoAttr::CAR_TYPE_TAG>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarType);

using BSExtractAdCarPart = BSExtractAdCarTag<CommonInfoAttr::CAR_PART_TAG>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarPart);

using BSExtractAdCarMaterail = BSExtractAdCarTag<CommonInfoAttr::CAR_MATERIAL_TAG>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarMaterail);

using BSExtractAdOrderSource = BSExtractAdCarTag<CommonInfoAttr::PHOTO_ORDER_SOURCE>;
REGISTER_BS_EXTRACTOR(BSExtractAdOrderSource);

using BSExtractAdOrderSchema = BSExtractAdCarTag<CommonInfoAttr::PHOTO_ORDER_DEFINE>;
REGISTER_BS_EXTRACTOR(BSExtractAdOrderSchema);

using BSExtractAdCarBrandV2 = BSExtractAdCarTag<CommonInfoAttr::CAR_BRAND_ID_V2>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarBrandV2);

using BSExtractAdCarSubBrandV2 = BSExtractAdCarTag<CommonInfoAttr::CAR_SUB_BRAND_ID_V2>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarSubBrandV2);

using BSExtractAdCarSeriesV2 = BSExtractAdCarTag<CommonInfoAttr::CAR_SERIES_ID_V2>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarSeriesV2);

using BSExtractAdCarPriceV2 = BSExtractAdCarTag<CommonInfoAttr::CAR_PRICE_CATE_V2>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarPriceV2);

using BSExtractAdCarCountryV2 = BSExtractAdCarTag<CommonInfoAttr::CAR_COUNTRY_ID_V2>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarCountryV2);

using BSExtractAdCarEnergyV2 = BSExtractAdCarTag<CommonInfoAttr::CAR_ENERGY_CATE_V2>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarEnergyV2);

using BSExtractAdCarScaleV2 = BSExtractAdCarTag<CommonInfoAttr::CAR_SCALE_CATE_ID_V2>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarScaleV2);

using BSExtractAdCarStructV2 = BSExtractAdCarTag<CommonInfoAttr::CAR_STRUCTURE_V2>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarStructV2);

using BSExtractAdCarSeatV2 = BSExtractAdCarTag<CommonInfoAttr::CAR_SEAT_NUMS_V2>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarSeatV2);

using BSExtractAdCarDisPlaceV2 = BSExtractAdCarTag<CommonInfoAttr::CAR_DISPLACEMENT_V2>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarDisPlaceV2);

using BSExtractAdCarModeV2 = BSExtractAdCarTag<CommonInfoAttr::CAR_DRIVING_MODE_V2>;
REGISTER_BS_EXTRACTOR(BSExtractAdCarModeV2);

}  // namespace ad_algorithm
}  // namespace ks
