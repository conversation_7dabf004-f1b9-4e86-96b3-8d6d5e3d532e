#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_local_feature.h"

namespace ks {
namespace ad_algorithm {

template <BSFieldEnum item_attr_name>
BSExtractAdLocalFeature<item_attr_name>::BSExtractAdLocalFeature() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(item_attr_name);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);
}

template <BSFieldEnum item_attr_name>
void BSExtractAdLocalFeature<item_attr_name>::Extract(const BSLog& bslog, size_t pos,
                                                      std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  bool has_ad_dsp_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
  bool has_ad_dsp_photo_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists, pos);
  int32_t common_info_attr_size = BSFieldHelper::GetSingular<int32_t>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size, pos);
  if (item_type == bs::ItemType::AD_DSP && has_ad_dsp_info && has_ad_dsp_photo_info &&
      common_info_attr_size > 0) {
    auto key_enum = item_attr_name;
    auto local_feature_value = BSFieldHelper::GetSingular<int64_t>(*bs, key_enum, pos);
    if (local_feature_value != 0) AddFeature(local_feature_value, 1.0f, result);
  }
}

using BSExtractLocalPoiId =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420036>;
REGISTER_BS_EXTRACTOR(BSExtractLocalPoiId);
using BSExtractLocalPoiName =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420037>;
REGISTER_BS_EXTRACTOR(BSExtractLocalPoiName);
using BSExtractLocalPoiCate1 =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420038>;
REGISTER_BS_EXTRACTOR(BSExtractLocalPoiCate1);
using BSExtractLocalPoiCate2 =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420039>;
REGISTER_BS_EXTRACTOR(BSExtractLocalPoiCate2);
using BSExtractLocalPoiCate3 =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420040>;
REGISTER_BS_EXTRACTOR(BSExtractLocalPoiCate3);
using BSExtractLocalPoiProvince =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420041>;
REGISTER_BS_EXTRACTOR(BSExtractLocalPoiProvince);
using BSExtractLocalPoiCity =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420042>;
REGISTER_BS_EXTRACTOR(BSExtractLocalPoiCity);
using BSExtractLocalPoiDistrict =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420043>;
REGISTER_BS_EXTRACTOR(BSExtractLocalPoiDistrict);
using BSExtractLocalPoiSectionCate1 =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420044>;
REGISTER_BS_EXTRACTOR(BSExtractLocalPoiSectionCate1);
using BSExtractLocalPoiSectionCate2 =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420045>;
REGISTER_BS_EXTRACTOR(BSExtractLocalPoiSectionCate2);
using BSExtractLocalAuthorFansRange =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420046>;
REGISTER_BS_EXTRACTOR(BSExtractLocalAuthorFansRange);
using BSExtractLocalManualRelateType =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420047>;
REGISTER_BS_EXTRACTOR(BSExtractLocalManualRelateType);
using BSExtractLocalInterestCat1 =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420048>;
REGISTER_BS_EXTRACTOR(BSExtractLocalInterestCat1);
using BSExtractLocalInterestCat2 =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420049>;
REGISTER_BS_EXTRACTOR(BSExtractLocalInterestCat2);
using BSExtractLocalHangingGoodsType =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420050>;
REGISTER_BS_EXTRACTOR(BSExtractLocalHangingGoodsType);
using BSExtractLocalRuleTagType =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420051>;
REGISTER_BS_EXTRACTOR(BSExtractLocalRuleTagType);
using BSExtractLocalIsMmuEsPhoto =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420052>;
REGISTER_BS_EXTRACTOR(BSExtractLocalIsMmuEsPhoto);
using BSExtractLocalIsSuperMmuEsPhoto =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420053>;
REGISTER_BS_EXTRACTOR(BSExtractLocalIsSuperMmuEsPhoto);
using BSExtractLocalBrandName =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420054>;
REGISTER_BS_EXTRACTOR(BSExtractLocalBrandName);
using BSExtractLocalHotviewPhotoType =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420055>;
REGISTER_BS_EXTRACTOR(BSExtractLocalHotviewPhotoType);
using BSExtractLocalSensePhotoType =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420056>;
REGISTER_BS_EXTRACTOR(BSExtractLocalSensePhotoType);
using BSExtractAdLocalPoiClick7d =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420057>;
REGISTER_BS_EXTRACTOR(BSExtractAdLocalPoiClick7d);
using BSExtractAdLocalPoiPay3d =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420058>;
REGISTER_BS_EXTRACTOR(BSExtractAdLocalPoiPay3d);
using BSExtractAdLocalPoiPay7d =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420059>;
REGISTER_BS_EXTRACTOR(BSExtractAdLocalPoiPay7d);
using BSExtractAdLocalPoiCollect =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420060>;
REGISTER_BS_EXTRACTOR(BSExtractAdLocalPoiCollect);
using BSExtractAdLocalPoiCtr3d =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420061>;
REGISTER_BS_EXTRACTOR(BSExtractAdLocalPoiCtr3d);
using BSExtractAdLocalPoiCtr7d =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420062>;
REGISTER_BS_EXTRACTOR(BSExtractAdLocalPoiCtr7d);
using BSExtractAdLocalGoodsCtr3d =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420063>;
REGISTER_BS_EXTRACTOR(BSExtractAdLocalGoodsCtr3d);
using BSExtractAdLocalGoodsCtr7d =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420064>;
REGISTER_BS_EXTRACTOR(BSExtractAdLocalGoodsCtr7d);
using BSExtractAdLocalPayCvr3d =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420065>;
REGISTER_BS_EXTRACTOR(BSExtractAdLocalPayCvr3d);
using BSExtractAdLocalPayCvr7d =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420066>;
REGISTER_BS_EXTRACTOR(BSExtractAdLocalPayCvr7d);
using BSExtractAdLocalPayAmt3d =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420067>;
REGISTER_BS_EXTRACTOR(BSExtractAdLocalPayAmt3d);
using BSExtractAdLocalPayAmt7d =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420068>;
REGISTER_BS_EXTRACTOR(BSExtractAdLocalPayAmt7d);
using BSExtractAdLocalGpm3d =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420069>;
REGISTER_BS_EXTRACTOR(BSExtractAdLocalGpm3d);
using BSExtractAdLocalGpm7d =
    BSExtractAdLocalFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420070>;
REGISTER_BS_EXTRACTOR(BSExtractAdLocalGpm7d);

}  // namespace ad_algorithm
}  // namespace ks
