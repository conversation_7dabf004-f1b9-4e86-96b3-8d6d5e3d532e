#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_advertiser_info.h"

namespace ks {
namespace ad_algorithm {

BSExtractBrandAdvertiserInfo::BSExtractBrandAdvertiserInfo() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_licence_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_corporation_name);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name);
}

void BSExtractBrandAdvertiserInfo::Extract(
             const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  bool has_value_license_id = false;
  absl::string_view licence_id = BSFieldHelper::GetSingular<absl::string_view>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_licence_id, pos, &has_value_license_id);
  bool has_value_corp_name = false;
  absl::string_view corporation_name = BSFieldHelper::GetSingular<absl::string_view>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_corporation_name, pos, &has_value_corp_name);
  bool has_value_product_name = false;
  absl::string_view product_name = BSFieldHelper::GetSingular<absl::string_view>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name, pos, &has_value_product_name);

  if (has_value_license_id && has_value_corp_name && has_value_product_name) {
    if (licence_id.size() > 0) {
      AddFeature(GetFeature(FeaturePrefix::PHOTO_LICENCE_ID, ad_nn::bs::Hash(licence_id)), 1.0f, result);
    }
    if (corporation_name.size() > 0) {
      AddFeature(GetFeature(FeaturePrefix::PHOTO_CORP_NAME, ad_nn::bs::Hash(corporation_name)), 1.0f,
                  result);
    }
    if (product_name.size() > 0) {
      AddFeature(GetFeature(FeaturePrefix::PHOTO_PRODUCT_NAME, ad_nn::bs::Hash(product_name)), 1.0f,
                  result);
    }
  }
}

}  // namespace ad_algorithm
}  // namespace ks
