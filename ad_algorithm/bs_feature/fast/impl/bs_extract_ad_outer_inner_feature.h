#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/proto/bs_common_info_attr_enum.pb.h"

namespace ks {
namespace ad_algorithm {
using CommonInfoAttr = ::bs::kuaishou::ad::CommonInfoAttr;

template <int no>
class BSExtractAdOuterInnerFeature : public BSFastFeatureNoPrefix {
 public:
  BSExtractAdOuterInnerFeature() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);

    BSGetItemAdDspInfoCommonInfoAttrNo.fill_attr_metas(&attr_metas_);
    BSHasItemAdDspInfoCommonInfoAttrNo.fill_attr_metas(&attr_metas_);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size;
    int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);

    auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

    auto enum_item_type = BSFieldEnum::adlog_item_type;
    int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

    int64_t attr_no = BSGetItemAdDspInfoCommonInfoAttrNo(bs, pos);

    if ((item_type == bs::ItemType::AD_DSP) && info_exists && attr_size > 0) {
      if (BSHasItemAdDspInfoCommonInfoAttrNo(bs, pos)) {
        AddFeature(attr_no, 1.0, result);
      }
    }
  }

 private:
  BSFixedCommonInfo<int64_t> BSGetItemAdDspInfoCommonInfoAttrNo{"adlog.item.ad_dsp_info.common_info_attr",
                                                                no};
  BSHasFixedCommonInfoImpl<int64_t, false> BSHasItemAdDspInfoCommonInfoAttrNo{
      "adlog.item.ad_dsp_info.common_info_attr", no};

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdOuterInnerFeature);
};

using BSExtractAdOuterInnerProductId = BSExtractAdOuterInnerFeature<CommonInfoAttr::INOUT_PRODUCT_ID>;
REGISTER_BS_EXTRACTOR(BSExtractAdOuterInnerProductId);

using BSExtractAdOuterInnerSpuId = BSExtractAdOuterInnerFeature<CommonInfoAttr::INOUT_PRODUCT_SPU>;
REGISTER_BS_EXTRACTOR(BSExtractAdOuterInnerSpuId);

}  // namespace ad_algorithm
}  // namespace ks
