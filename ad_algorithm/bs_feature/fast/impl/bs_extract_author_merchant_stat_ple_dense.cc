#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_merchant_stat_ple_dense.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_dense_ple.h"
#include "teams/ad/ad_algorithm/fed/compute/get_realtime_cross_feature.h"
#include "teams/ad/ad_algorithm/fed/compute/merge_list.h"

namespace ks {
namespace ad_algorithm {

BSExtractAuthorMerchantStatPleDense::BSExtractAuthorMerchantStatPleDense()
    : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001103);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001118);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001107);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5002833);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5002834);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5002837);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5002836);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5002839);
}
void BSExtractAuthorMerchantStatPleDense::Extract(const BSLog& bslog, size_t pos,
                                                  std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 =
      get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001103, pos);
  auto x3 = cast_to_float_with_default(x1, -1);
  auto x4 = get_l_type_bins();
  auto x5 = get_dense_ple_helper(x3, x4);
  auto x6 = get_bslog_int64_list(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001118, pos);
  auto x7 = get_list_length_scalar(x6);
  auto x9 = cast_to_float_with_default(x7, 0);
  auto x10 = get_live_yellow_car_len_bins();
  auto x11 = get_dense_ple_helper(x9, x10);
  auto x12 = get_bslog_int64_list(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001107, pos);
  auto x13 = get_list_length_scalar(x12);
  auto x15 = cast_to_float_with_default(x13, 0);
  auto x16 = get_author_history_sell_bins();
  auto x17 = get_dense_ple_helper(x15, x16);
  auto x18 =
      get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5002833, pos);
  auto x20 = cast_to_float_with_default(x18, 0);
  auto x21 = get_author_max_on_car_bins();
  auto x22 = get_dense_ple_helper(x20, x21);
  auto x23 =
      get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5002834, pos);
  auto x25 = cast_to_float_with_default(x23, 0);
  auto x26 = get_author_max_car_len_bins();
  auto x27 = get_dense_ple_helper(x25, x26);
  auto x28 =
      get_bslog_float(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5002837, pos);
  auto x30 = cast_to_float_with_default(x28, 0);
  auto x31 = get_author_item_exp_time_bins();
  auto x32 = get_dense_ple_helper(x30, x31);
  auto x33 =
      get_bslog_float(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5002836, pos);
  auto x35 = cast_to_float_with_default(x33, 0);
  auto x36 = get_author_live_exp_cnt_bins();
  auto x37 = get_dense_ple_helper(x35, x36);
  auto x38 =
      get_bslog_float(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5002839, pos);
  auto x40 = cast_to_float_with_default(x38, -1);
  auto x41 = get_author_exp_ratio_bins();
  auto x42 = get_dense_ple_helper(x40, x41);
  auto x43 = merge_float_list_all(x5, x11, x17, x22, x27, x32, x37, x42);
  add_feature_result(x43, 50, result);
}

}  // namespace ad_algorithm
}  // namespace ks
