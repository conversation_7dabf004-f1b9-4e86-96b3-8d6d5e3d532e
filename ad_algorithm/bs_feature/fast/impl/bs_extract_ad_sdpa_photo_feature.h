#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

template <BSFieldEnum no>
class BSExtractSdpaPhotoFeature : public BSFastFeatureNoPrefix {
 public:
  BSExtractSdpaPhotoFeature() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);
    attr_metas_.emplace_back(no);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    bool has_dsp_photo_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists, pos);
    int32_t common_info_attr_size = BSFieldHelper::GetSingular<int32_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size, pos);
    if (item_type == bs::ItemType::AD_DSP && has_ad_dsp_info && has_dsp_photo_info &&
        common_info_attr_size > 0) {
      auto key_enum = no;
      auto sdpa_feature_value = BSFieldHelper::GetSingular<int64_t>(*bs, key_enum, pos);
      if (sdpa_feature_value > 0) AddFeature(sdpa_feature_value, 1.0f, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractSdpaPhotoFeature);
};

using BSExtractSdpaProductId =
    BSExtractSdpaPhotoFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_90014>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductId);

using BSExtractSdpaOuterId =
    BSExtractSdpaPhotoFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_90015>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaOuterId);

using BSExtractSdpaEcomBandId =
    BSExtractSdpaPhotoFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_90017>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaEcomBandId);

using BSExtractSdpaEcomShopKeeperId =
    BSExtractSdpaPhotoFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_90018>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaEcomShopKeeperId);

using BSExtractSdpaEcomCategoryId =
    BSExtractSdpaPhotoFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_90019>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaEcomCategoryId);

using BSExtractSdpaEcomCategory2Id =
    BSExtractSdpaPhotoFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_90020>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaEcomCategory2Id);

using BSExtractSdpaEcomProductPrice =
    BSExtractSdpaPhotoFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_90021>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaEcomProductPrice);

using BSExtractSdpaVideoProductType =
    BSExtractSdpaPhotoFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_90022>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaVideoProductType);

using BSExtractSdpaProductVideoTheme =
    BSExtractSdpaPhotoFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_90023>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductVideoTheme);

using BSExtractSdpaProductCarBrand =
    BSExtractSdpaPhotoFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_90024>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductCarBrand);

}  // namespace ad_algorithm
}  // namespace ks
