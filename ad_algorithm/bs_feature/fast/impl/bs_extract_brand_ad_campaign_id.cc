#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_campaign_id.h"

namespace ks {
namespace ad_algorithm {
BSExtractBrandAdCampaignId::BSExtractBrandAdCampaignId() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_id);
}

void BSExtractBrandAdCampaignId::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  bool has_ad_dsp_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
  bool has_campaign =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists, pos);
  bool has_base =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists, pos);

  if (has_ad_dsp_info && has_campaign && has_base) {
    uint64_t campaign_id = BSFieldHelper::GetSingular<uint64_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_id, pos);
    AddFeature(GetFeature(FeaturePrefix::CAMPAIGN_ID, campaign_id), 1.0f, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
