#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_dense_ple_v2.dark
class BSExtractAuthorMerchantStatPleDenseV2 : public BSFastFeature {
 public:
  BSExtractAuthorMerchantStatPleDenseV2();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorMerchantStatPleDenseV2);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorMerchantStatPleDenseV2);
}  // namespace ad_algorithm
}  // namespace ks
