#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_target_id.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdTargetId::BSExtractAdTargetId() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_target_id);
}

void BSExtractAdTargetId::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_unit_exists = BSFieldEnum::adlog_item_ad_dsp_info_unit_exists;
  bool unit_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_unit_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  auto enum_target_id = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_target_id;
  uint64_t target_id = BSFieldHelper::GetSingular<uint64_t>(*bs, enum_target_id, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && info_exists &&
      unit_exists) {
    AddFeature(GetFeature(FeaturePrefix::PHOTO_TARGET_ID, target_id), 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
