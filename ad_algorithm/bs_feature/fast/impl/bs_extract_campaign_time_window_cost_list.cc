/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_campaign_time_window_cost_list.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

BSExtractCampaignTimeWindowCostList::BSExtractCampaignTimeWindowCostList()
    : BSFastFeatureNoPrefix(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5003759);
}
void BSExtractCampaignTimeWindowCostList::Extract(const BSLog& bslog, size_t pos,
                                                  std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64_list(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5003759, pos);
  add_feature_result(x1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
