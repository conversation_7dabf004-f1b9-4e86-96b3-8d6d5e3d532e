#pragma once
#include <string>
#include <vector>

// #include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

template <BSFieldEnum no>
class BSExtractAkgIndusUserAdInterestSeq : public BSFastFeatureNoPrefix {
 public:
  BSExtractAkgIndusUserAdInterestSeq() : BSFastFeatureNoPrefix(FeatureType::USER) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_size);
    attr_metas_.emplace_back(no);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    bool has_user_info = BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
    if (!has_user_info) {
      return;
    }

    auto user_common_info_attr_size =
        BSFieldHelper::GetSingular<size_t>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_size, pos);
    if (user_common_info_attr_size <= 0) {
      return;
    }

    auto key_enum = no;
    BSRepeatedField<int> attr(*bs, key_enum, pos);
    const size_t list_size = attr.size();
    for (int i = 0; i < list_size; i++) {
      auto id = attr.Get(i);
      AddFeature(id, 1.0f, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAkgIndusUserAdInterestSeq);
};

using BSExtractAkgIndusUserAdInterestSeqPaySubmit =
    BSExtractAkgIndusUserAdInterestSeq<BSFieldEnum::adlog_user_info_common_info_attr_key_420004>;
REGISTER_BS_EXTRACTOR(BSExtractAkgIndusUserAdInterestSeqPaySubmit);

using BSExtractAkgIndusUserAdInterestSeqItemClick =
    BSExtractAkgIndusUserAdInterestSeq<BSFieldEnum::adlog_user_info_common_info_attr_key_420005>;
REGISTER_BS_EXTRACTOR(BSExtractAkgIndusUserAdInterestSeqItemClick);

using BSExtractAkgIndusUserAdInterestSeqHweightCd =
    BSExtractAkgIndusUserAdInterestSeq<BSFieldEnum::adlog_user_info_common_info_attr_key_420006>;
REGISTER_BS_EXTRACTOR(BSExtractAkgIndusUserAdInterestSeqHweightCd);

using BSExtractAkgIndusUserAdInterestSeqGoodsPurchaseCd =
    BSExtractAkgIndusUserAdInterestSeq<BSFieldEnum::adlog_user_info_common_info_attr_key_420007>;
REGISTER_BS_EXTRACTOR(BSExtractAkgIndusUserAdInterestSeqGoodsPurchaseCd);

}  // namespace ad_algorithm
}  // namespace ks
