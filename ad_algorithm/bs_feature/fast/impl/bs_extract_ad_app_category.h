#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "ks/util/json.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdAppCategory : public BSFastFeature {
 public:
  BSExtractAdAppCategory() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_common_info_attr_key_2054);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if (item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) {
      auto key_enum = BSFieldEnum::adlog_item_common_info_attr_key_2054;
      BSRepeatedField<absl::string_view> attr(*bs, key_enum, pos);
      for (int i = 0; i < attr.size(); i++) {
        absl::string_view category = attr.Get(i);
        if (!category.empty()) {
          AddFeature(GetFeature(FeaturePrefix::PHOTO_APP_CATEGORY, ad_nn::bs::Hash(category)), 1.0f, result);
        }
      }
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"item.common_info_attr.APP_CATEGORY"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdAppCategory);
};

REGISTER_BS_EXTRACTOR(BSExtractAdAppCategory);

}  // namespace ad_algorithm
}  // namespace ks
