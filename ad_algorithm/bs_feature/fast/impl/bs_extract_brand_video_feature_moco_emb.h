#pragma once
#include <algorithm>
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandVideoFeatureMocoEmb : public BSFastFeature {
 public:
  BSExtractBrandVideoFeatureMocoEmb() : BSFastFeature(DENSE_ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_534);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if (item_type == bs::ItemType::AD_BRAND ||
        (item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD)) {
      bool has_value = BSFieldHelper::HasSingular<int>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
      if (has_value) {
        BSRepeatedField<float> attrs(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_534, pos);
        if (!attrs.is_empty() && attrs.size() == 64) {
          for (int i = 0; i < attrs.size(); ++i) {
            AddFeature(i, std::min(attrs.Get(i), 10.0f), result);
          }
        }
      }
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"item.ad_dsp_info.common_info_attr.VIDEO_MOCO_EMBEDDING"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandVideoFeatureMocoEmb);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandVideoFeatureMocoEmb);

}  // namespace ad_algorithm
}  // namespace ks
