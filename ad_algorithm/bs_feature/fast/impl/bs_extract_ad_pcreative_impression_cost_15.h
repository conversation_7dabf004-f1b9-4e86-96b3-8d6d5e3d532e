#pragma once
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdPcreativeImpressionCost15 : public BSFastFeature {
 public:
  BSExtractAdPcreativeImpressionCost15() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_230);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_231);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool adlog_item_ad_dsp_info_exists =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) &&
        adlog_item_ad_dsp_info_exists) {
      ad_nn::BSRepeatedField<uint64_t> attr(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_230,
                                            pos);
      if (attr.size() != 7) {
        return;
      }
      AddFeature(GetFeature(FeaturePrefix::PCREATIVE_CREATIVE_IMPRESSION, attr.Get(4)), 1.0f, result);

      ad_nn::BSRepeatedField<uint64_t> attr2(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_231, pos);
      if (attr2.size() != 7) {
        return;
      }
      AddFeature(GetFeature(FeaturePrefix::PCREATIVE_CREATIVE_COST, attr2.Get(4)), 1.0f, result);
    }
  }

 private:
  const std::string USED_FEATURES[1] = {
      "item.ad_dsp_info.common_info_attr.PCREATIVE_SELECT_CREATIVE_IMPRESSION"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdPcreativeImpressionCost15);
};

REGISTER_BS_EXTRACTOR(BSExtractAdPcreativeImpressionCost15);

}  // namespace ad_algorithm
}  // namespace ks
