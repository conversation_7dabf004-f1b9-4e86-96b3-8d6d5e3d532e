#pragma once
#include <string>
#include <unordered_set>
#include <vector>

#include "base/strings/string_number_conversions.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "base/strings/string_util.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdCreativeBehaviorIntentionCategory : public BSFastFeature {
 public:
  BSExtractAdCreativeBehaviorIntentionCategory() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    bs_info.kItemAdDspInfoCommonAttr.FillMeta(5280, &attr_metas_);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if (item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) {
      // ::bs::kuaishou::ad::CommonInfoAttr_Name_AD_CREATIVE_BEHAVIOR_INTEREST_CATEGORY_ID
      int32_t name_attr_id = 5280;

      std::vector<int32_t> attr_ids = bs_info.kItemAdDspInfoCommonAttr.find(name_attr_id);
      if (attr_ids.size() == 2) {
        BSMapField<int64_t, float> bs_map(*bs, attr_ids[0], attr_ids[1], pos);
        for (int i = 0; i < bs_map.size(); ++i) {
          auto key = bs_map.GetKey(i);
          AddFeature(GetFeature(FeaturePrefix::AD_CREATIVE_BEHAVIOR_INTENTION_CATEGORY, key), 1.0f, result);
        }
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdCreativeBehaviorIntentionCategory);
};
REGISTER_BS_EXTRACTOR(BSExtractAdCreativeBehaviorIntentionCategory);
}  // namespace ad_algorithm
}  // namespace ks
