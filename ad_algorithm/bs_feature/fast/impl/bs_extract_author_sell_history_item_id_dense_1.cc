#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_sell_history_item_id_dense_1.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_info_from_yellow_car_list.h"

namespace ks {
namespace ad_algorithm {

BSExtractAuthorSellHistoryItemIdDense1::BSExtractAuthorSellHistoryItemIdDense1()
    : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001107);
}
void BSExtractAuthorSellHistoryItemIdDense1::Extract(const BSLog& bslog, size_t pos,
                                                     std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64_list(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001107, pos);
  auto x3 = get_item_id_from_yellow_car_list(x1, 1);
  add_feature_result(x3, 4, result);
}

}  // namespace ad_algorithm
}  // namespace ks
