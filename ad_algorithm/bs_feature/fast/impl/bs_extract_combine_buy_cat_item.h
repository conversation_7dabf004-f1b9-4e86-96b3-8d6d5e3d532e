#pragma once
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractCombineBuyCatItem : public BSFastFeature {
 public:
  BSExtractCombineBuyCatItem();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineBuyCatItem);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineBuyCatItem);

}  // namespace ad_algorithm
}  // namespace ks
