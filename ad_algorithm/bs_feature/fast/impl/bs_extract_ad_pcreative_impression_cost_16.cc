#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_pcreative_impression_cost_16.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdPcreativeImpressionCost16::BSExtractAdPcreativeImpressionCost16()
    : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_230);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_231);
}

void BSExtractAdPcreativeImpressionCost16::Extract(const BSLog& bslog, size_t pos,
                                                   std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size;
  int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  auto enum_key_230 = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_230;
  BSRepeatedField<int64_t> key_230(*bs, enum_key_230, pos);

  auto enum_key_231 = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_231;
  BSRepeatedField<int64_t> key_231(*bs, enum_key_231, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && info_exists &&
      attr_size > 0) {
    if (!key_230.is_empty()) {
      if (key_230.size() != 7) {
        return;
      }
      AddFeature(GetFeature(FeaturePrefix::PCREATIVE_CREATIVE_IMPRESSION, key_230[5]), 1.0f, result);
    }

    if (!key_231.is_empty()) {
      if (key_231.size() != 7) {
        return;
      }
      AddFeature(GetFeature(FeaturePrefix::PCREATIVE_CREATIVE_COST, key_231[5]), 1.0f, result);
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
