#pragma once
#include <map>
#include <memory>
#include <string>
#include <unordered_set>
#include <vector>

#include "base/strings/string_number_conversions.h"
#include "base/strings/string_split.h"
#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"
#include "teams/ad/ad_algorithm/log_preprocess/ad_model_kconf_util.h"

namespace ks {
namespace ad_algorithm {
template <GsuFieldType field, int max_list_len,
          BSFieldEnum no,  // name_value
          int tag          // goods_cluster : 0, goods_cate : 1
          >
class BSExtractAdMmuGoodsGsuFeatureNewTopkTarget : public BSFastFeatureNoPrefix {
 public:
  BSExtractAdMmuGoodsGsuFeatureNewTopkTarget() : BSFastFeatureNoPrefix(FeatureType::COMBINE) {
    feature_index_ = 0;
    double thres;
    int end_tag;
    if (tag == 0) {
      config_ = AdModelKconfUtil::adJointLabeledLogMmuCommerceTopkCluster();
      thres = CLUSTER_THRESHOLD;
      end_tag = 2048;
    } else {
      config_ = AdModelKconfUtil::adJointLabeledLogMmuCommerceTopkCate();
      thres = CATE_THRESHOLD;
      end_tag = 2473;
    }
    for (auto c = 0; c < end_tag; c++) {
      std::vector<std::string> key_score_list;
      folly::F14FastSet<std::int64_t> cate_similar_list;
      std::string cate = base::Int64ToString(c);
      auto iter = config_->find(cate);
      if (iter == config_->end()) {
        continue;
      }
      base::SplitString(iter->second, std::string(";"), &key_score_list);
      if (key_score_list.size() <= 0) {
        continue;
      }
      for (size_t i = 0; i < key_score_list.size(); ++i) {
        std::vector<std::string> key_score;
        int64_t key;
        double score;
        base::SplitString(key_score_list[i], std::string(","), &key_score);
        if (key_score.size() < 2) {
          continue;
        }
        base::StringToInt64(key_score[0], &key);
        base::StringToDouble(key_score[1], &score);
        if (score >= thres) {
          cate_similar_list.insert(key);
        }
      }
      if (config_parse.size() != c) {
        return;
      }
      config_parse.push_back(cate_similar_list);
    }
    AddAttrMetas();
  }
  explicit BSExtractAdMmuGoodsGsuFeatureNewTopkTarget(size_t index)
      : BSFastFeatureNoPrefix(FeatureType::COMBINE) {
    feature_index_ = index;
    double thres;
    int end_tag;
    if (tag == 0) {
      config_ = AdModelKconfUtil::adJointLabeledLogMmuCommerceTopkCluster();
      thres = CLUSTER_THRESHOLD;
      end_tag = 2048;
    } else {
      config_ = AdModelKconfUtil::adJointLabeledLogMmuCommerceTopkCate();
      thres = CATE_THRESHOLD;
      end_tag = 2473;
    }
    for (auto c = 0; c < end_tag; c++) {
      std::vector<std::string> key_score_list;
      folly::F14FastSet<std::int64_t> cate_similar_list;
      std::string cate = base::Int64ToString(c);
      auto iter = config_->find(cate);
      if (iter == config_->end()) {
        continue;
      }
      base::SplitString(iter->second, std::string(";"), &key_score_list);
      if (key_score_list.size() <= 0) {
        continue;
      }
      for (size_t i = 0; i < key_score_list.size(); ++i) {
        std::vector<std::string> key_score;
        int64_t key;
        double score;
        base::SplitString(key_score_list[i], std::string(","), &key_score);
        if (key_score.size() < 2) {
          continue;
        }
        base::StringToInt64(key_score[0], &key);
        base::StringToDouble(key_score[1], &score);
        if (score >= thres) {
          cate_similar_list.insert(key);
        }
      }
      if (config_parse.size() != c) {
        return;
      }
      config_parse.push_back(cate_similar_list);
    }
    AddAttrMetas();
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_user_info =
        BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
    if (!has_user_info) {
      return;
    }
    if (!bs_util.BSHasPhotoInfo(bs, pos)) {
      return;
    }

    int32_t common_info_attr_size = BSFieldHelper::GetSingular<int32_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size, pos);
    if (common_info_attr_size <= 0) {
      return;
    }
    auto p_items = bslog.colossus_reco_photo();
    if (p_items == nullptr) {
      return;
    }

    uint64_t user_id = BSFieldHelper::GetSingular<uint64_t, true>(*bs, BSFieldEnum::adlog_user_info_id, pos);

    uint64_t time = BSFieldHelper::GetSingular<uint64_t, true>(*bs, BSFieldEnum::adlog_time, pos) / 1000;

    int64_t target_item_attr = BSFieldHelper::GetSingular<int64_t>(*bs, no, pos);

    if (target_item_attr >= config_parse.size() || target_item_attr < 0) {
      return;
    }
    auto config_parse_target = config_parse[target_item_attr];

    const auto& items = *(p_items);
    int64_t counter = 0;

    for (int i = items.size() - 1; i >= 0; --i) {
      auto& item = items[i];
      if (item.timestamp > time) {
        continue;
      }
      if ((*(reinterpret_cast<const uint32_t*>(&(item.user_longitude))) % 100) < INTENT_SCORE_THRESHOLD) {
        continue;
      }

      int id = -1;
      // 获取 id
      switch (tag) {
        case 0:
          id = *(reinterpret_cast<const uint32_t*>(&(item.user_longitude))) / 1000000;
          break;
        case 1:
          id = (*(reinterpret_cast<const uint32_t*>(&(item.user_longitude))) % 1000000) / 100;
          break;
      }
      auto got = config_parse_target.find(id);
      if (got == config_parse_target.end()) {  // 在 vec 的 set 中 find
        continue;
      }
      int val = -1;
      switch (field) {
        case GsuFieldType::PHOTO_ID:
          val = item.photo_id;
          break;
        case GsuFieldType::AUTHOR_ID:
          val = item.author_id;
          break;
        case GsuFieldType::TAG:
          val = item.tag;
          break;
        case GsuFieldType::GOODS_CLUSTER:
          val = *(reinterpret_cast<const uint32_t*>(&(item.user_longitude))) / 1000000;
          break;
        case GsuFieldType::GOODS_CATE:
          val = (*(reinterpret_cast<const uint32_t*>(&(item.user_longitude))) % 1000000) / 100;
          break;
      }
      AddFeature(val, 1.0f, result);
      counter++;
      if (counter >= max_list_len) {
        break;
      }
    }
  }

 private:
  size_t feature_index_;
  const int INTENT_SCORE_THRESHOLD = 47;
  const double CLUSTER_THRESHOLD = 0.8;
  const double CATE_THRESHOLD = 0.5;
  std::shared_ptr<std::map<std::string, std::string>> config_;
  std::vector<folly::F14FastSet<std::int64_t>> config_parse;
  const std::string USED_FEATURES[1] = {"time"};
  void AddAttrMetas() {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_time);
    attr_metas_.emplace_back(no);
    bs_util.BSHasPhotoInfo.fill_attr_metas(&attr_metas_);
  }
};

using BSExtractAdMmuGoodsGsuFeatureNewPhotoIdCateTopkTarget = BSExtractAdMmuGoodsGsuFeatureNewTopkTarget<
    GsuFieldType::PHOTO_ID, 50, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25223, 1>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewPhotoIdCateTopkTarget);

using BSExtractAdMmuGoodsGsuFeatureNewAuthorIdCateTopkTarget = BSExtractAdMmuGoodsGsuFeatureNewTopkTarget<
    GsuFieldType::AUTHOR_ID, 50, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25223,
    1>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewAuthorIdCateTopkTarget);

using BSExtractAdMmuGoodsGsuFeatureNewTagCateTopkTarget = BSExtractAdMmuGoodsGsuFeatureNewTopkTarget<
    GsuFieldType::TAG, 50, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25223, 1>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewTagCateTopkTarget);

using BSExtractAdMmuGoodsGsuFeatureNewClusterCateTopkTarget = BSExtractAdMmuGoodsGsuFeatureNewTopkTarget<
    GsuFieldType::GOODS_CLUSTER, 50,
    BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25223, 1>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewClusterCateTopkTarget);

using BSExtractAdMmuGoodsGsuFeatureNewPhotoIdClusterTopkTarget = BSExtractAdMmuGoodsGsuFeatureNewTopkTarget<
    GsuFieldType::PHOTO_ID, 50, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25225, 0>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewPhotoIdClusterTopkTarget);

using BSExtractAdMmuGoodsGsuFeatureNewAuthorIdClusterTopkTarget = BSExtractAdMmuGoodsGsuFeatureNewTopkTarget<
    GsuFieldType::AUTHOR_ID, 50, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25225,
    0>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewAuthorIdClusterTopkTarget);

using BSExtractAdMmuGoodsGsuFeatureNewTagClusterTopkTarget = BSExtractAdMmuGoodsGsuFeatureNewTopkTarget<
    GsuFieldType::TAG, 50, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25225, 0>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewTagClusterTopkTarget);

using BSExtractAdMmuGoodsGsuFeatureNewCateClusterTopkTarget = BSExtractAdMmuGoodsGsuFeatureNewTopkTarget<
    GsuFieldType::GOODS_CATE, 50, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25225,
    0>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewCateClusterTopkTarget);

}  // namespace ad_algorithm
}  // namespace ks
