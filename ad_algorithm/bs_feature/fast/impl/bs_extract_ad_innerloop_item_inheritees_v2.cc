#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_innerloop_item_inheritees_v2.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdInnerloopItemInheriteesV2::BSExtractAdInnerloopItemInheriteesV2()
    : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_66301);
  bs_util.BSHasPhotoInfo.fill_attr_metas(&attr_metas_);
  bs_util.BSGetPhotoInfoId.fill_attr_metas(&attr_metas_);
}

void BSExtractAdInnerloopItemInheriteesV2::Extract(const BSLog& bslog, size_t pos,
                                                   std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  if (pos >= bslog.item_size()) {
    return;
  }

  if (!bs_util.BSHasPhotoInfo(bs, pos)) {
    return;
  }
  bool has_value = false;
  uint64_t photo_id = bs_util.BSGetPhotoInfoId(bs, pos, &has_value);

  uint64_t inheritee = 0;
  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);
  auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size;
  int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);
  auto enum_photo_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists;
  bool photo_info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_photo_info_exists, pos);
  auto enum_key_66301 = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_66301;
  int64_t key_66301 = BSFieldHelper::GetSingular<int64_t>(*bs, enum_key_66301, pos);
  auto enum_key_66301_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_66301;
  bool key_66301_exists = BSFieldHelper::HasSingular<int64_t>(*bs, enum_key_66301_exists, pos);

  if (info_exists && photo_info_exists && attr_size > 0) {
    if (key_66301_exists) {
      inheritee = key_66301;
    }
  }
  if (inheritee > 0) {
    photo_id = inheritee;
  }
  AddFeature(GetFeature(FeaturePrefix::PHOTO_ID, photo_id), 1.0f, result);
}
}  // namespace ad_algorithm
}  // namespace ks
