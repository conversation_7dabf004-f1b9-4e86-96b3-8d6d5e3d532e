#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_conversion_time.h"

#include <vector>

namespace ks {
namespace ad_algorithm {

BSExtractAdConversionTime::BSExtractAdConversionTime() : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_time);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_label_info_label_infos_key_10000001_first_occur_timestamp);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_label_info_label_infos_key_10000078_first_occur_timestamp);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_label_info_label_infos_key_10000079_first_occur_timestamp);
}

void BSExtractAdConversionTime::Extract(const BSLog &bslog, size_t pos, std::vector<ExtractResult> *result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  int64_t delivery_time = BSFieldHelper::GetSingular<int64_t>(*bs, BSFieldEnum::adlog_time, pos);
  int64_t conversion_time = delivery_time;  // 默认下发时间

  std::vector<int64_t> enums;
  enums.push_back(BSFieldEnum::adlog_item_label_info_label_infos_key_10000001_first_occur_timestamp);
  enums.push_back(BSFieldEnum::adlog_item_label_info_label_infos_key_10000078_first_occur_timestamp);
  enums.push_back(BSFieldEnum::adlog_item_label_info_label_infos_key_10000079_first_occur_timestamp);

  for (int enum_value : enums) {
    bool has_val = false;
    int64_t tmp = BSFieldHelper::GetSingular<int64_t>(*bs, enum_value, pos, &has_val);
    if (has_val) {
      // 有激活，替换为激活时间
      conversion_time = tmp;
      break;
    }
  }

  AddFeature(0, delivery_time, result);
  AddFeature(1, conversion_time, result);
}

}  // namespace ad_algorithm
}  // namespace ks
