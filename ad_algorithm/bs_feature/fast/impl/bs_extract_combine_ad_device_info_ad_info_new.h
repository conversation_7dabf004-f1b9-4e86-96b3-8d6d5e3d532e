#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"

namespace ks {
namespace ad_algorithm {

template <int Target_type>
class BSExtractCombineAdDeviceInfoAdInfoCluster : public BSFastFeature {
 public:
  BSExtractCombineAdDeviceInfoAdInfoCluster() : BSFastFeature(FeatureType::COMBINE, 16, 26, 10) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists);

    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_licence_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_corporation_name);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_dup_photo_id);

    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_os_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_ip_v4);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_connection_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_operator_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_device_mod);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_brand);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_readable_mod);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_price);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_is_kcard);

    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_os_type_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_ip_v4_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_connection_type_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_operator_type_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_device_mod_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_brand_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_readable_mod_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_price_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_is_kcard_exists);
    bs_util.BSHasPhotoInfo.fill_attr_metas(&attr_metas_);
    bs_util.BSHasPhotoInfoAuthorInfo.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoId.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoAttributeFansCount.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoAttributeUploadCount.fill_attr_metas(&attr_metas_);
    bs_util.BSHasPhotoInfoAuthorInfoAttribute.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoAttributeGender.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoAuthorInfoAttributeRegTime.fill_attr_metas(&attr_metas_);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    auto item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    bool has_ad_dsp_info_advertise_base = BSFieldHelper::GetSingular<bool>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists, pos);
    bool has_ad_dsp_info_creative =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_exists, pos);
    bool has_ad_dsp_info_creative_base =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists, pos);
    bool has_user_info =
        BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
    bool has_user_info_ad_user_info =
        BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_ad_user_info_exists, pos);

    if (has_user_info && has_user_info_ad_user_info) {
      auto key_device_info_size = BSFieldEnum::adlog_user_info_ad_user_info_device_info_size;
      int device_info_size = BSFieldHelper::GetSingular<int, true>(*bs, key_device_info_size, pos);

      auto key_os_type = BSFieldEnum::adlog_user_info_ad_user_info_device_info_os_type;
      BSRepeatedField<int64_t, true> os_type(*bs, key_os_type, pos);

      auto key_ip_v4 = BSFieldEnum::adlog_user_info_ad_user_info_device_info_ip_v4;
      BSRepeatedField<absl::string_view, true> ip_v4(*bs, key_ip_v4, pos);

      auto key_connection_type = BSFieldEnum::adlog_user_info_ad_user_info_device_info_connection_type;
      BSRepeatedField<int64_t, true> connection_type(*bs, key_connection_type, pos);

      auto key_operator_type = BSFieldEnum::adlog_user_info_ad_user_info_device_info_operator_type;
      BSRepeatedField<int64_t, true> operator_type(*bs, key_operator_type, pos);

      auto key_device_mod = BSFieldEnum::adlog_user_info_ad_user_info_device_info_device_mod;
      BSRepeatedField<absl::string_view, true> device_mod(*bs, key_device_mod, pos);

      auto key_brand = BSFieldEnum::adlog_user_info_ad_user_info_device_info_brand;
      BSRepeatedField<absl::string_view, true> brand(*bs, key_brand, pos);

      auto key_readable_mod = BSFieldEnum::adlog_user_info_ad_user_info_device_info_readable_mod;
      BSRepeatedField<absl::string_view, true> readable_mod(*bs, key_readable_mod, pos);

      auto key_price = BSFieldEnum::adlog_user_info_ad_user_info_device_info_price;
      BSRepeatedField<int64_t, true> price(*bs, key_price, pos);

      auto key_is_kcard = BSFieldEnum::adlog_user_info_ad_user_info_device_info_is_kcard;
      BSRepeatedField<int64_t, true> is_kcard(*bs, key_is_kcard, pos);

      auto key_os_type_exists = BSFieldEnum::adlog_user_info_ad_user_info_device_info_os_type_exists;
      BSRepeatedField<bool, true> os_type_exists(*bs, key_os_type_exists, pos);

      auto key_ip_v4_exists = BSFieldEnum::adlog_user_info_ad_user_info_device_info_ip_v4_exists;
      BSRepeatedField<bool, true> ip_v4_exists(*bs, key_ip_v4_exists, pos);

      auto key_connection_type_exists =
          BSFieldEnum::adlog_user_info_ad_user_info_device_info_connection_type_exists;
      BSRepeatedField<bool, true> connection_type_exists(*bs, key_connection_type_exists, pos);

      auto key_operator_type_exists =
          BSFieldEnum::adlog_user_info_ad_user_info_device_info_operator_type_exists;
      BSRepeatedField<bool, true> operator_type_exists(*bs, key_operator_type_exists, pos);

      auto key_device_mod_exists = BSFieldEnum::adlog_user_info_ad_user_info_device_info_device_mod_exists;
      BSRepeatedField<bool, true> device_mod_exists(*bs, key_device_mod_exists, pos);

      auto key_brand_exists = BSFieldEnum::adlog_user_info_ad_user_info_device_info_brand_exists;
      BSRepeatedField<bool, true> brand_exists(*bs, key_brand_exists, pos);

      auto key_readable_mod_exists =
          BSFieldEnum::adlog_user_info_ad_user_info_device_info_readable_mod_exists;
      BSRepeatedField<bool, true> readable_mod_exists(*bs, key_readable_mod_exists, pos);

      auto key_price_exists = BSFieldEnum::adlog_user_info_ad_user_info_device_info_price_exists;
      BSRepeatedField<bool, true> price_exists(*bs, key_price_exists, pos);

      auto key_is_kcard_exists = BSFieldEnum::adlog_user_info_ad_user_info_device_info_is_kcard_exists;
      BSRepeatedField<bool, true> is_kcard_exists(*bs, key_is_kcard_exists, pos);

      std::string device_str = "";
      if (device_info_size > 0) {
        device_info_size = device_info_size < 3 ? device_info_size : 3;
        for (int k = 0; k < device_info_size; k++) {
          if (k < os_type.size() && k < os_type_exists.size() && os_type_exists.Get(k)) {
            device_str = device_str + std::to_string(os_type.Get(k));
          }
          if (k < ip_v4.size() && k < ip_v4_exists.size() && ip_v4_exists.Get(k)) {
            device_str = device_str + std::string(ip_v4.Get(k));
          }
          if (k < connection_type.size() && k < connection_type_exists.size() &&
              connection_type_exists.Get(k)) {
            device_str = device_str + std::to_string(connection_type.Get(k));
          }
          if (k < operator_type.size() && k < operator_type_exists.size() && operator_type_exists.Get(k)) {
            device_str = device_str + std::to_string(operator_type.Get(k));
          }
          if (k < device_mod.size() && k < device_mod_exists.size() && device_mod_exists.Get(k)) {
            device_str = device_str + std::string(device_mod.Get(k));
          }
          if (k < brand.size() && k < brand_exists.size() && brand_exists.Get(k)) {
            device_str = device_str + std::string(brand.Get(k));
          }
          if (k < readable_mod.size() && k < readable_mod_exists.size() && readable_mod_exists.Get(k)) {
            device_str = device_str + std::string(readable_mod.Get(k));
          }
          if (k < price.size() && k < price_exists.size() && price_exists.Get(k)) {
            device_str = device_str + std::to_string(price.Get(k));
          }
          if (k < is_kcard.size() && k < is_kcard_exists.size() && is_kcard_exists.Get(k)) {
            device_str = device_str + std::to_string(is_kcard.Get(k));
          }
        }
      }

      if (Target_type == 1) {
        if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info &&
            has_ad_dsp_info_creative && has_ad_dsp_info_creative_base && has_ad_dsp_info_advertise_base &&
            device_info_size > 0) {
          bool has_value_license_id = false;
          absl::string_view licence_id = BSFieldHelper::GetSingular<absl::string_view>(
              *bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_licence_id, pos,
              &has_value_license_id);
          bool has_value_corp_name = false;
          absl::string_view corporation_name = BSFieldHelper::GetSingular<absl::string_view>(
              *bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_corporation_name, pos,
              &has_value_corp_name);
          bool has_value_product_name = false;
          absl::string_view product_name = BSFieldHelper::GetSingular<absl::string_view>(
              *bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name, pos,
              &has_value_product_name);
          uint64 new_industry_id = BSFieldHelper::GetSingular<int>(
              *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id, pos);
          std::string new_industry_str = std::to_string(new_industry_id);
          uint64 ad_category = BSFieldHelper::GetSingular<int64_t>(
              *bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
          std::string ad_category_str = std::to_string(ad_category);

          if (licence_id.size() == 0 || corporation_name.size() == 0 || product_name.size() == 0) {
            return;
          }
          std::string ad_info = std::string(licence_id) + std::string(corporation_name) +
                                std::string(product_name) + new_industry_str + ad_category_str;
          // LOG(INFO) << "BS ExtractCombineAdDeviceInfoAdInfo" << ad_info
          // << "+" <<  device_str <<  std::endl;
          uint64_t id = GetFeature(FeaturePrefix::COMBINE_DEVICE_INFO_AD_INFO, ad_nn::bs::Hash(device_str),
                                   ad_nn::bs::Hash(ad_info), 0);
          AddFeature(id, 1.0, result);
        }
      } else if (Target_type == 2) {
        bool has_photo_info = bs_util.BSHasPhotoInfo(bs, pos);
        if (!has_photo_info) {
          return;
        }
        bool has_author_info = bs_util.BSHasPhotoInfoAuthorInfo(bs, pos);
        if (has_author_info && device_info_size > 0) {
          bool has_attribute = bs_util.BSHasPhotoInfoAuthorInfoAttribute(bs, pos);
          if (!has_attribute) {
            return;
          }
          std::string author_id = std::to_string(bs_util.BSGetPhotoInfoAuthorInfoId(bs, pos));
          std::string fans_count =
              std::to_string(bs_util.BSGetPhotoInfoAuthorInfoAttributeFansCount(bs, pos));
          std::string gender = std::to_string(bs_util.BSGetPhotoInfoAuthorInfoAttributeGender(bs, pos));
          std::string reg_month = std::to_string(static_cast<int>(
              ceil(bs_util.BSGetPhotoInfoAuthorInfoAttributeRegTime(bs, pos) / 1000.0 / 3600 / 24 / 30)));
          std::string upload_count =
              std::to_string(bs_util.BSGetPhotoInfoAuthorInfoAttributeUploadCount(bs, pos));
          std::string author_info_str = author_id + fans_count + gender + reg_month + upload_count;
          uint64_t id = GetFeature(FeaturePrefix::COMBINE_DEVICE_INFO_AUTHOR_INFO,
                                   ad_nn::bs::Hash(device_str), ad_nn::bs::Hash(author_info_str), 0);
          AddFeature(id, 1.0, result);
        }
      } else if (Target_type == 3) {
        if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info &&
            has_ad_dsp_info_creative && device_info_size > 0) {
          uint64_t dup_photo_id = BSFieldHelper::GetSingular<uint64>(
              *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_dup_photo_id, pos);
          uint64_t id = GetFeature(FeaturePrefix::COMBINE_DEVICE_INFO_DUP_PHOTO, ad_nn::bs::Hash(device_str),
                                   dup_photo_id, 0);
          AddFeature(id, 1.0, result);
        }
      }
    }
  }
};

using BSExtractCombineAdDeviceInfoAdInfo = BSExtractCombineAdDeviceInfoAdInfoCluster<1>;
REGISTER_BS_EXTRACTOR(BSExtractCombineAdDeviceInfoAdInfo);
using BSExtractCombineAdDeviceInfoAuthorInfo = BSExtractCombineAdDeviceInfoAdInfoCluster<2>;
REGISTER_BS_EXTRACTOR(BSExtractCombineAdDeviceInfoAuthorInfo);
using BSExtractCombineAdDeviceInfoDupPhoto = BSExtractCombineAdDeviceInfoAdInfoCluster<3>;
REGISTER_BS_EXTRACTOR(BSExtractCombineAdDeviceInfoDupPhoto);
}  // namespace ad_algorithm
}  // namespace ks
