#pragma once
#include <vector>
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_feature.pb.h"
using ::bs::kuaishou::ad::AdCallbackLog;
namespace ks {
namespace ad_algorithm {
class BSExtractCallbackEventDense : public BSFastFeature {
 public:
  BSExtractCallbackEventDense();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCallbackEventDense);
};
REGISTER_BS_EXTRACTOR(BSExtractCallbackEventDense);
}  // namespace ad_algorithm
}  // namespace ks
