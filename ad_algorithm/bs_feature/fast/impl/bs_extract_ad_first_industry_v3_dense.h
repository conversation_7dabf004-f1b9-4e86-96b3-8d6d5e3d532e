#pragma once

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdFirstIndustryV3Dense : public BSFastFeature {
 public:
  BSExtractAdFirstIndustryV3Dense();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdFirstIndustryV3Dense);
};

REGISTER_BS_EXTRACTOR(BSExtractAdFirstIndustryV3Dense);

}  // namespace ad_algorithm
}  // namespace ks
