#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_uri.h"

namespace ks {
namespace ad_algorithm {
BSExtractBrandAdUri::BSExtractBrandAdUri() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_uri);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists);
}

void BSExtractBrandAdUri::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  auto key_has_dsp_info = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  auto key_has_unit = BSFieldEnum::adlog_item_ad_dsp_info_unit_exists;
  auto key_has_unit_base = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists;
  bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_has_dsp_info, pos);
  bool has_unit = BSFieldHelper::GetSingular<bool>(*bs, key_has_unit, pos);
  bool has_unit_base = BSFieldHelper::GetSingular<bool>(*bs, key_has_unit_base, pos);
  if (!has_ad_dsp_info || !has_unit || !has_unit_base) {
    return;
  }
  std::hash<std::string> hash_fn;
  absl::string_view uri = BSFieldHelper::GetSingular<absl::string_view>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_uri, pos);
  if (uri.size() > 0) {
    uint64_t uri_hash = ad_nn::bs::Hash(uri);
    AddFeature(GetFeature(FeaturePrefix::AD_URI, uri_hash), 1.0f, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
