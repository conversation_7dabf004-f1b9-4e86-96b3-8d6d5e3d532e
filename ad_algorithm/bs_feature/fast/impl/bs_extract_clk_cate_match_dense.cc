/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_clk_cate_match_dense.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_realtime_cross_feature.h"
#include "teams/ad/ad_algorithm/fed/compute/merge_list.h"
#include "teams/ad/ad_algorithm/fed/compute/realtime_ecom_action.h"

namespace ks {
namespace ad_algorithm {

BSExtractClkCateMatchDense::BSExtractClkCateMatchDense() : BSFastFeature(FeatureType::DENSE_COMBINE) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5003268);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5100040);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5003269);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5100041);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5003270);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5100039);
}
void BSExtractClkCateMatchDense::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5003268, pos);
  auto x2 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5100040, pos);
  auto x3 = get_realtime_target_id_match_cnt_v2(x1, x2);
  auto x4 = get_segment_cnt_log_value(x3);
  auto x5 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5003269, pos);
  auto x6 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5100041, pos);
  auto x7 = get_realtime_target_id_match_cnt_v2(x5, x6);
  auto x8 = get_segment_cnt_log_value(x7);
  auto x9 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5003270, pos);
  auto x10 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5100039, pos);
  auto x11 = get_realtime_target_id_match_cnt_v2(x9, x10);
  auto x12 = get_segment_cnt_log_value(x11);
  auto x13 = merge_float_list_all(x4, x8, x12);
  add_feature_result(x13, 3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
