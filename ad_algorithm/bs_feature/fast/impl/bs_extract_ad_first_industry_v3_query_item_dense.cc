#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_query_item_dense.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdFirstIndustryV3QueryDense::BSExtractAdFirstIndustryV3QueryDense() : BSFastFeature(DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3);
}

void BSExtractAdFirstIndustryV3QueryDense::Extract(const BSLog& bslog, size_t pos,
                                                   std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists;
  bool base_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_base_exists, pos);

  auto enum_creative_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_exists;
  bool creative_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_creative_exists, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  auto enum_industry_id = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3;
  uint64_t industry_id = BSFieldHelper::GetSingular<uint64_t>(*bs, enum_industry_id, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && info_exists &&
      creative_exists && base_exists) {
    int tag = 10202;
    if (industry_id >= 1244 && industry_id <= 1255) {
      tag = 10101;
    } else if (industry_id == 1281 || industry_id == 1289 || industry_id == 1089 || industry_id == 1303 ||
               industry_id == 1155 || industry_id == 1159) {
      tag = 10201;
    } else if (industry_id == 1043 || industry_id == 1044 || industry_id == 1064 || industry_id == 1065 ||
               industry_id == 1095 || industry_id == 1096 || industry_id == 1100 || industry_id == 1101 ||
               industry_id == 1263 || industry_id == 1264) {
      tag = 10203;
    } else if (industry_id == 1278) {
      tag = 10204;
    } else if (industry_id == 1082) {
      tag = 10205;
    } else if (industry_id == 1036 || industry_id == 1037 || industry_id == 1057 || industry_id == 1058 ||
               industry_id == 1153 || industry_id == 1154 || industry_id == 1156 || industry_id == 1256 ||
               industry_id == 1257) {
      tag = 10206;
    } else if (industry_id == 1038 || industry_id == 1059 || industry_id == 1160 || industry_id == 1258) {
      tag = 10207;
    } else if (industry_id >= 1049 && industry_id <= 1056 || industry_id >= 1070 && industry_id <= 1077 ||
               industry_id >= 1204 && industry_id <= 1213 || industry_id >= 1269 && industry_id <= 1276) {
      tag = 10208;
    } else if (industry_id == 1282) {
      tag = 10209;
    } else if (industry_id == 1046 || industry_id == 1067 || industry_id == 1098 || industry_id == 1266) {
      tag = 10210;
    } else if (industry_id == 1045 || industry_id == 1066 || industry_id == 1097 || industry_id == 1265) {
      tag = 10211;
    } else if (industry_id == 1048 || industry_id == 1069 || industry_id == 1102 || industry_id == 1268) {
      tag = 10212;
    } else if (industry_id == 1084 || industry_id == 1085 || industry_id == 1284 || industry_id == 1285) {
      tag = 10213;
    } else if (industry_id == 1047 || industry_id == 1068 || industry_id == 1099 || industry_id == 1267) {
      tag = 10214;
    } else if (industry_id == 1115) {
      tag = 10301;
    } else if (industry_id >= 1111 && industry_id <= 1117 && industry_id != 1115 || industry_id == 1277 ||
               industry_id == 1278 || industry_id == 1297 || industry_id == 1087 || industry_id == 1088) {
      tag = 10302;
    } else if (industry_id >= 1118 && industry_id <= 1127 || industry_id == 1086 || industry_id == 1286 ||
               industry_id == 1291) {
      tag = 10401;
    } else if (industry_id >= 1128 && industry_id <= 1143 || industry_id == 1292) {
      tag = 10501;
    } else if (industry_id >= 1194 && industry_id <= 1203 || industry_id == 1039 || industry_id == 1259 ||
               industry_id == 1060 || industry_id == 1293 || industry_id == 1295 || industry_id == 1296 ||
               industry_id == 1157 || industry_id == 1158) {
      tag = 10601;
    } else if (industry_id >= 1032 && industry_id <= 1035) {
      tag = 10602;
    } else if (industry_id >= 1214 && industry_id <= 1221 || industry_id >= 1294 && industry_id <= 1301) {
      tag = 10701;
    } else if (industry_id >= 1214 && industry_id <= 1221 || industry_id == 1290) {
      tag = 10702;
    } else if (industry_id >= 1222 && industry_id <= 1232 || industry_id == 1302) {
      tag = 10801;
    } else if (industry_id == 1237 || industry_id == 1239 || industry_id == 1283 || industry_id == 1083) {
      tag = 10901;
    } else if (industry_id >= 1233 && industry_id <= 1244 || industry_id == 1299) {
      tag = 10902;
    } else if (industry_id >= 1144 && industry_id <= 1152) {
      tag = 11001;
    } else if (industry_id >= 1090 && industry_id <= 1094 || industry_id == 1300) {
      tag = 11101;
    } else if (industry_id >= 1161 && industry_id <= 1172) {
      tag = 11201;
    } else if (industry_id >= 1103 && industry_id <= 1110 || industry_id >= 1277 && industry_id <= 1280 ||
               industry_id >= 1078 && industry_id <= 1081 || industry_id == 1298) {
      tag = 11301;
    } else if (industry_id >= 1173 && industry_id <= 1193) {
      tag = 11401;
    } else if (industry_id >= 1028 && industry_id <= 1031) {
      tag = 11501;
    } else if (industry_id == 1023 || industry_id == 1040 || industry_id == 1061 || industry_id == 1260) {
      tag = 11601;
    } else if (industry_id >= 1024 && industry_id <= 1027 || industry_id == 1041 || industry_id == 1042 ||
               industry_id == 1062 || industry_id == 1063 || industry_id == 1261 || industry_id == 1262) {
      tag = 11602;
    }
    AddFeature(0, tag, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
