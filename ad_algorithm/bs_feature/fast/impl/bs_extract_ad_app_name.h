#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "ks/util/json.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdAppName : public BSFastFeature {
 public:
  BSExtractAdAppName() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_common_info_attr_key_2049);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if (item_type != bs::ItemType::AD_DSP) {
      return;
    }

    auto app_name = BSFieldHelper::GetSingular<absl::string_view>(
        *bs, BSFieldEnum::adlog_item_common_info_attr_key_2049, pos);
    if (!app_name.empty()) {
      std::hash<std::string> hash_fn;
      AddFeature(GetFeature(FeaturePrefix::PHOTO_APP_NAME, hash_fn(app_name.data())), 1.0f, result);
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"item.common_info_attr.APP_NAME"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdAppName);
};

REGISTER_BS_EXTRACTOR(BSExtractAdAppName);

}  // namespace ad_algorithm
}  // namespace ks
