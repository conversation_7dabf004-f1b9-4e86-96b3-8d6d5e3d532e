#pragma once
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "base/common/base.h"
#include "base/common/logging.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdLpsUrlDomainNew : public BSFastFeature {
 public:
  BSExtractAdLpsUrlDomainNew() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);

    auto key_has_advertiser_base = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists;
    bool has_advertiser_base = BSFieldHelper::GetSingular<bool>(*bs, key_has_advertiser_base, pos);

    if (has_ad_dsp_info && has_advertiser_base) {
      auto key_name = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name;
      absl::string_view url_domain = BSFieldHelper::GetSingular<absl::string_view>(*bs, key_name, pos);
      if (url_domain.size() > 0) {
        AddFeature(GetFeature(FeaturePrefix::LPS_AD_URI_DOMAIN, ad_nn::bs::Hash(url_domain)), 1.0f, result);
      }
      absl::string_view product_name = BSFieldHelper::GetSingular<absl::string_view>(*bs, key_name, pos);
      if (product_name.size() > 0) {
        AddFeature(GetFeature(FeaturePrefix::LPS_AD_URI_PRODUCT_NAME, ad_nn::bs::Hash(product_name)), 1.0f,
                   result);
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdLpsUrlDomainNew);
};

REGISTER_BS_EXTRACTOR(BSExtractAdLpsUrlDomainNew);

}  // namespace ad_algorithm
}  // namespace ks
