#pragma once
#include <algorithm>
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

template <BSFieldEnum attr_name, bool is_float>
class BSExtractAdSpuAttr : public BSFastFeatureNoPrefix {
 public:
  BSExtractAdSpuAttr() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(attr_name);
  }

  virtual void Extract(const BSLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = adlog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool adlog_item_ad_dsp_info_exists =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    if (!adlog_item_ad_dsp_info_exists) {
      return;
    }
    bool has_value = false;
    if (is_float) {
      auto value = BSFieldHelper::GetSingular<float>(*bs, attr_name, pos, &has_value);
      if (has_value) AddFeature(ext_ratio_x2y(value), 1.0, result);
    }
    if (!is_float) {
      auto value = BSFieldHelper::GetSingular<int>(*bs, attr_name, pos, &has_value);
      if (has_value) AddFeature(value, 1.0, result);
    }
  }

  double ext_ratio_x2y(const double& x) {
    double y;
    y = std::min(std::max(0.0, x), 10.0);
    y = y < 1 ? 10 * y + 0.5 : 2 * y + 10.5;
    return y;
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdSpuAttr);
};

using BSExtractEcomPhotoMaterialSpuId =
    BSExtractAdSpuAttr<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_11085, false>;
REGISTER_BS_EXTRACTOR(BSExtractEcomPhotoMaterialSpuId);

using BSExtractShopType =
    BSExtractAdSpuAttr<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_26001, false>;
REGISTER_BS_EXTRACTOR(BSExtractShopType);

using BSExtractShopMCId =
    BSExtractAdSpuAttr<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_26002, false>;
REGISTER_BS_EXTRACTOR(BSExtractShopMCId);

using BSExtractShopRSCId =
    BSExtractAdSpuAttr<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_26003, false>;
REGISTER_BS_EXTRACTOR(BSExtractShopRSCId);

using BSExtractShopIsWithoutCheck =
    BSExtractAdSpuAttr<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_26004, false>;
REGISTER_BS_EXTRACTOR(BSExtractShopIsWithoutCheck);

using BSExtractShopRefundT0RateRatio =
    BSExtractAdSpuAttr<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_26005, true>;
REGISTER_BS_EXTRACTOR(BSExtractShopRefundT0RateRatio);

using BSExtractShopRefundT14RateRatio =
    BSExtractAdSpuAttr<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_26006, true>;
REGISTER_BS_EXTRACTOR(BSExtractShopRefundT14RateRatio);

using BSExtractShopRefundT7RateRatio =
    BSExtractAdSpuAttr<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_26007, true>;
REGISTER_BS_EXTRACTOR(BSExtractShopRefundT7RateRatio);

}  // namespace ad_algorithm
}  // namespace ks
