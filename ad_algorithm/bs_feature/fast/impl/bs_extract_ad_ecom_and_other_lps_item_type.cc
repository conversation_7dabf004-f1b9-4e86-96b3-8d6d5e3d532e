#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_ecom_and_other_lps_item_type.h"

namespace ks {
namespace ad_algorithm {
BSExtractEcomAndOtherLpsItemType::BSExtractEcomAndOtherLpsItemType() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_id);
}

void BSExtractEcomAndOtherLpsItemType::Extract(const BSLog& bslog, size_t pos,
                                               std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists;
  bool base_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_base_exists, pos);

  auto enum_campaign_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists;
  bool campaign_base_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_campaign_base_exists, pos);

  auto enum_campaign_exists = BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists;
  bool campaign_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_campaign_exists, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  bool is_zhidian = false;
  bool is_merchant = false;

  if (!info_exists) {
    return;
  }
  auto enum_industry_id = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_id;
  uint64_t industry_id = BSFieldHelper::GetSingular<uint64_t>(*bs, enum_industry_id, pos);

  if (base_exists) {
    is_zhidian = is_direct_ecom_industry_id(industry_id);
  }
  auto enum_campaign_type = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type;
  uint64_t campaign_type = BSFieldHelper::GetSingular<uint64_t>(*bs, enum_campaign_type, pos);

  if (campaign_exists && campaign_base_exists) {
    is_merchant = (campaign_type == 13);
  }
  if (is_zhidian) {
    AddFeature(1, 1.0f, result);
    return;
  }
  if (is_merchant) {
    AddFeature(2, 1.0f, result);
    return;
  }
  AddFeature(3, 1.0f, result);
}
bool BSExtractEcomAndOtherLpsItemType::is_direct_ecom_industry_id(const int& industry_id) {
  // 2.0
  if (industry_id >= MIN_INDUSTRY_ID_2 && industry_id <= MAX_INDUSTRY_ID_2) {
    return true;
  }
  // 3.0
  if (industry_id >= MIN_INDUSTRY_ID__3 && industry_id <= MAX_INDUSTRY_ID__3) {
    return true;
  }
  return false;
}
}  // namespace ad_algorithm
}  // namespace ks
