#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_author_fans.h"

namespace ks {
namespace ad_algorithm {

void BSExtractCombineAuthorFans::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  if (!bs_util.BSHasAuthorInfo(bs, pos)) {
    return;
  }
  BSRepeatedField<uint64_t, true> follow_info_id(*bs, BSFieldEnum::adlog_user_info_action_detail_follow_id,
                                                 pos);
  uint64_t author_info_id = bs_util.BSGetAuthorInfoId(bs, pos);
  int id = 0;
  for (int i = 0; i < follow_info_id.size(); ++i) {
    if (follow_info_id.Get(i) == author_info_id) {
      id = 1;
      break;
    }
  }
  AddFeature(id, 1.0, result);
}

}  // namespace ad_algorithm
}  // namespace ks
