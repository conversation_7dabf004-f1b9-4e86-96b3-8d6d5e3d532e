#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_nn/feature_extract/extract_framework/include/feature_buffer_wrapper.h"
#include "teams/ad/ad_nn/flatten_raw_feature/utils.h"

namespace ks {
namespace ad_algorithm {

class BSExtractNormalizedScore : public BSFastFeature {
 public:
  BSExtractNormalizedScore() : BSFastFeature(DENSE_USER) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_label_info_normalized_attribute_score);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
    uint64_t item_type = BSFieldHelper::GetSingular<uint64_t>(*bs, BSFieldEnum::adlog_item_type, pos);
    if ((item_type != bs::ItemType::AD_DSP && item_type != bs::ItemType::NATIVE_AD) || !has_ad_dsp_info) {
      AddFeature(0, 1.0, result);
      return;
    }
    float score = BSFieldHelper::GetSingular<float>(
        *bs, BSFieldEnum::adlog_item_label_info_normalized_attribute_score, pos);
    AddFeature(0, score, result);
  }

 private:
  const std::string USED_FEATURES[1] = {"label_info.normalized_attribute_score"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractNormalizedScore);
};

REGISTER_BS_EXTRACTOR(BSExtractNormalizedScore);

}  // namespace ad_algorithm
}  // namespace ks
