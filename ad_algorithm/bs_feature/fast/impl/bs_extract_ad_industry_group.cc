#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_industry_group.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdIndustryGroup::BSExtractAdIndustryGroup() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_group);
}

void BSExtractAdIndustryGroup::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists;
  bool base_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_base_exists, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  auto enum_industry_group = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_group;
  absl::string_view industry_group =
      BSFieldHelper::GetSingular<absl::string_view>(*bs, enum_industry_group, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD ||
       item_type == bs::ItemType::AD_BRAND) &&
      info_exists && base_exists) {
    AddFeature(GetFeature(FeaturePrefix::PHOTO_CREATIVE_INDUSTRY_GROUP_ID,
                          base::CityHash64(industry_group.data(), industry_group.length())),
               1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
