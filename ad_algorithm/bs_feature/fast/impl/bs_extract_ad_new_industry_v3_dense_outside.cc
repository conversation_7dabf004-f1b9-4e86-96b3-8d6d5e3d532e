#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_new_industry_v3_dense_outside.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdNewIndustryV3DenseOutside::BSExtractAdNewIndustryV3DenseOutside()
    : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
}

void BSExtractAdNewIndustryV3DenseOutside::Extract(const BSLog& bslog, size_t pos,
                                                   std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists;
  bool base_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_base_exists, pos);

  auto enum_id_v3 = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3;
  int64_t industry_id = BSFieldHelper::GetSingular<int64_t>(*bs, enum_id_v3, pos);

  auto enum_creative_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_exists;
  bool creative_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_creative_exists, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && info_exists &&
      creative_exists && base_exists) {
    int tag = 0;
    if (industry_id >= 1023 && industry_id <= 1056) {
      tag = industry_id - 1022;  // 1~34
    }
    if (industry_id >= 1090 && industry_id <= 1255) {
      tag = industry_id - 1055;  // 35~200
    }
    AddFeature(0, tag, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
