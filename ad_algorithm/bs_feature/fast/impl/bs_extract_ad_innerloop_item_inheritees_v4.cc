#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_innerloop_item_inheritees_v4.h"

namespace ks {
namespace ad_algorithm {
using BSExtractPhotoIdStableV2 = BSExtractAdInnerloopItemInheriteesV4<0, 0>;
REGISTER_BS_EXTRACTOR(BSExtractPhotoIdStableV2);

using BSExtractCreativeIdStableV2 = BSExtractAdInnerloopItemInheriteesV4<1, 0>;
REGISTER_BS_EXTRACTOR(BSExtractCreativeIdStableV2);

using BSExtractUnitCampaignIdStableV2 = BSExtractAdInnerloopItemInheriteesV4<2, 0>;
REGISTER_BS_EXTRACTOR(BSExtractUnitCampaignIdStableV2);

using BSExtractCreativeSignatureStableV2 = BSExtractAdInnerloopItemInheriteesV4<3, 0>;
REGISTER_BS_EXTRACTOR(BSExtractCreativeSignatureStableV2);

using BSExtractAdCreateTimeStableV2 = BSExtractAdInnerloopItemInheriteesV4<4, 0>;
REGISTER_BS_EXTRACTOR(BSExtractAdCreateTimeStableV2);

using BSExtractPhotoIdCold = BSExtractAdInnerloopItemInheriteesV4<0, 1>;
REGISTER_BS_EXTRACTOR(BSExtractPhotoIdCold);

using BSExtractCreativeIdCold = BSExtractAdInnerloopItemInheriteesV4<1, 1>;
REGISTER_BS_EXTRACTOR(BSExtractCreativeIdCold);

using BSExtractCreativeSignatureCold = BSExtractAdInnerloopItemInheriteesV4<3, 1>;
REGISTER_BS_EXTRACTOR(BSExtractCreativeSignatureCold);

}  // namespace ad_algorithm
}  // namespace ks
