#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_callback_event_dense.h"

namespace ks {
namespace ad_algorithm {

BSExtractCallbackEventDense::BSExtractCallbackEventDense() : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type);
}
void BSExtractCallbackEventDense::Extract(
  const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  if (item_type != bs::ItemType::AD_DSP && item_type != bs::ItemType::NATIVE_AD) {
    return;
  }
  ::bs::kuaishou::ad::AdCallbackLog::EventType callback_event =
      ::bs::kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN;
  auto enum_action_type = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type;
  int ocpc_action_type = BSFieldHelper::GetSingular<int>(*bs, enum_action_type, pos);
  if (ocpc_action_type == ::bs::kuaishou::ad::AD_CONVERSION) {
    callback_event = ::bs::kuaishou::ad::AdCallbackLog_EventType_EVENT_CONVERSION;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) {
    callback_event = ::bs::kuaishou::ad::AdCallbackLog_EventType_EVENT_FORM_SUBMIT;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_MERCHANT_FOLLOW) {
    callback_event = ::bs::kuaishou::ad::AdCallbackLog_EventType_AD_MERCHANT_FOLLOW;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_PURCHASE) {
    callback_event = ::bs::kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_CREDIT_GRANT) {
    callback_event = ::bs::kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT;
  } else if (ocpc_action_type == ::bs::kuaishou::ad::AD_ITEM_CLICK) {
    callback_event = ::bs::kuaishou::ad::AdCallbackLog_EventType_EVENT_ITEM_CLICK;
  } else {
    auto& ocpc_action_type_name =
        ::bs::kuaishou::ad::AdActionType_Name(::bs::kuaishou::ad::AdActionType(ocpc_action_type));
    if (!::bs::kuaishou::ad::AdCallbackLog::EventType_Parse(ocpc_action_type_name, &callback_event)) {
      callback_event = ::bs::kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN;
    }
  }
  for (int i = 0; i < 128; i++) {
    if (i == static_cast<int>(callback_event)) {
      AddFeature(i, 1, result);
    } else {
      AddFeature(i, 0, result);
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
