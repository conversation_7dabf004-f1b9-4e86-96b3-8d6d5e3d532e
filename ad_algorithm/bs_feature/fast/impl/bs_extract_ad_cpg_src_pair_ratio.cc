#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_cpg_src_pair_ratio.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdCpgSrcPairRatio::BSExtractAdCpgSrcPairRatio() : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_5000391);
}
void BSExtractAdCpgSrcPairRatio::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_float_list(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_5000391, pos);
  add_feature_result(x1, 50, result);
}

}  // namespace ad_algorithm
}  // namespace ks
