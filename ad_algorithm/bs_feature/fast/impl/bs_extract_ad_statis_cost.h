#pragma once
#include <vector>
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
namespace ks {
namespace ad_algorithm {

class BSExtractAdStatisCost : public BSFastFeature {
 public:
  BSExtractAdStatisCost();

  inline int getBucketValue(double originValue, int maxValue) {
    int i = 0;
    for (i = 0; i < bucketBound.size(); i++) {
      if (bucketBound[i] >= maxValue) return i;
      if (bucketBound[i] >= originValue) {
        return i;
      }
    }
    return i;
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  std::vector<double> bucketBound;
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdStatisCost);
};

REGISTER_BS_EXTRACTOR(BSExtractAdStatisCost);

}  // namespace ad_algorithm
}  // namespace ks
