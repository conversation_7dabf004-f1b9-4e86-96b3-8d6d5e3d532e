#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_buy_cat_industy.h"

namespace ks {
namespace ad_algorithm {
BSExtractCombineBuyCatIndusty::BSExtractCombineBuyCatIndusty() : BSFastFeature(FeatureType::COMBINE) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_170);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id);
}

void BSExtractCombineBuyCatIndusty::Extract(const BSLog& bslog, size_t pos,
                                            std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists;
  bool base_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_base_exists, pos);

  auto enum_creative_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_exists;
  bool creative_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_creative_exists, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  auto enum_attr_size = BSFieldEnum::adlog_user_info_common_info_attr_size;
  int32_t attr_size = BSFieldHelper::GetSingular<int32_t, true>(*bs, enum_attr_size, pos);

  auto enum_user_info_exists = BSFieldEnum::adlog_user_info_exists;
  bool user_info_exists = BSFieldHelper::GetSingular<bool, true>(*bs, enum_user_info_exists, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && info_exists &&
      creative_exists && base_exists) {
    auto enum_industry_id = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id;
    uint64_t industry_id = BSFieldHelper::GetSingular<uint64_t>(*bs, enum_industry_id, pos);

    auto enum_key_170 = BSFieldEnum::adlog_user_info_common_info_attr_key_170;
    BSRepeatedField<int64_t, true> key_170(*bs, enum_key_170, pos);

    if (user_info_exists && attr_size > 0) {
      if (!key_170.is_empty()) {
        for (int i = 0; i < key_170.size() && i < 100; i++) {
          AddFeature(
              GetFeature(FeaturePrefix::COMBINE_BUT_MERCHANT_CAT_INDUSRTY, key_170.Get(i), industry_id), 1.0,
              result);
        }
      }
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
