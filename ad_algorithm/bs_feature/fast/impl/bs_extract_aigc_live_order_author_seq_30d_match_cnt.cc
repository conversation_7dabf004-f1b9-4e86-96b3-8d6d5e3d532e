/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_aigc_live_order_author_seq_30d_match_cnt.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_realtime_cross_feature.h"
#include "teams/ad/ad_algorithm/fed/compute/realtime_ecom_action.h"

namespace ks {
namespace ad_algorithm {

BSExtractAigcLiveOrderAuthorSeq30dMatchCnt::BSExtractAigcLiveOrderAuthorSeq30dMatchCnt()
    : BSFastFeature(FeatureType::DENSE_COMBINE) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5103518);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_id);
}
void BSExtractAigcLiveOrderAuthorSeq30dMatchCnt::Extract(const BSLog& bslog, size_t pos,
                                                         std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5103518, pos);
  auto x2 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_author_info_id, pos);
  auto x3 = get_realtime_target_id_match_cnt_v2(x1, x2);
  auto x4 = get_segment_cnt_log_value(x3);
  add_feature_result(x4, 1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
