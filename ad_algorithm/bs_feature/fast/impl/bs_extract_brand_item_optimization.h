#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandItemOptimization : public BSFastFeature {
 public:
  BSExtractBrandItemOptimization() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_deep_conversion_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    bool has_unit =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_exists, pos);
    bool has_base =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists, pos);
    if (!has_dsp_info || !has_unit || !has_base) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    int deep_conversion_type = BSFieldHelper::GetSingular<int>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_deep_conversion_type, pos);
    if (item_type == bs::ItemType::AD_BRAND) {
      AddFeature(GetFeature(FeaturePrefix::AD_BRAND_OPTIMIZATION_TARGET_ID, deep_conversion_type), 1.0,
                 result);
      return;
    }
    AddFeature(GetFeature(FeaturePrefix::AD_BRAND_DSP_OPTIMIZATION_TARGET_ID, deep_conversion_type), 1.0,
               result);
  }

 private:
  const std::string USED_FEATURES[1] = {"item.ad_dsp_info.unit.base.deep_conversion_type"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandItemOptimization);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandItemOptimization);

}  // namespace ad_algorithm
}  // namespace ks
