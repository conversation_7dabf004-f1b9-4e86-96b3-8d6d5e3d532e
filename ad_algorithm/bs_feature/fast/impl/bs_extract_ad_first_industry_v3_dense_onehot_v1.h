#pragma once

#include <cmath>
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdFirstIndustryV3DenseOnehotV1 : public BSFastFeature {
 public:
  BSExtractAdFirstIndustryV3DenseOnehotV1();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdFirstIndustryV3DenseOnehotV1);
};

REGISTER_BS_EXTRACTOR(BSExtractAdFirstIndustryV3DenseOnehotV1);

}  // namespace ad_algorithm
}  // namespace ks
