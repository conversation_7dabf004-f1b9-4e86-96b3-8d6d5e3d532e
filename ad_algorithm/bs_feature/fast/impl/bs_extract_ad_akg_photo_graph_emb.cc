#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_akg_photo_graph_emb.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdAkgPhotoGraphEmb::BSExtractAdAkgPhotoGraphEmb() : BSFastFeature(DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_48005);
}

void BSExtractAdAkgPhotoGraphEmb::Extract(const BSLog& bslog, size_t pos,
                                          std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size;
  int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  auto enum_key_48005 = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_48005;
  BSRepeatedField<float> key_48005(*bs, enum_key_48005, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && info_exists &&
      attr_size > 0) {
    if (!key_48005.is_empty()) {
      int count = 0;

      for (size_t i = 0; i < key_48005.size(); ++i) {
        if (count < 64) {
          AddFeature(i, std::min(key_48005.Get(i), 10.0f), result);
        }
        count++;
      }
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
