#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAuthorWeekGmvP90BoolMask23Bins : public BSFastFeature {
 public:
  BSExtractAuthorWeekGmvP90BoolMask23Bins();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorWeekGmvP90BoolMask23Bins);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorWeekGmvP90BoolMask23Bins);

}  // namespace ad_algorithm
}  // namespace ks
