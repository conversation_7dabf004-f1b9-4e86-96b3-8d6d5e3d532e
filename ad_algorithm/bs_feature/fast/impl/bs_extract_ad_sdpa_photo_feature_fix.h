#pragma once
#include <math.h>
#include <time.h>

#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
namespace ks {
namespace ad_algorithm {

template <BSFieldEnum item_attr_name>
class BSExtractSdpaPhotoFeatureFix : public BSFastFeature {
 public:
  BSExtractSdpaPhotoFeatureFix();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  const std::string USED_FEATURES[12] = {
      "item.ad_dsp_info.common_info_attr.SDPA_OUTER_ID_FULL",
      "item.ad_dsp_info.common_info_attr.SDPA_TITLE_FULL",
      "item.ad_dsp_info.common_info_attr.SDPA_NAME_FULL",
      "item.ad_dsp_info.common_info_attr.SDPA_ECOM_BRAND_ID_FULL",
      "item.ad_dsp_info.common_info_attr.SDPA_ECOM_SHOP_KEEPER_ID_FULL",
      "item.ad_dsp_info.common_info_attr.SDPA_ECOM_CATEGORY_ID_FULL",
      "item.ad_dsp_info.common_info_attr.SDPA_ECOM_CATEGORY_2_ID_FULL",
      "item.ad_dsp_info.common_info_attr.SDPA_ECOM_PRICE_TAG_FULL",
      "item.ad_dsp_info.common_info_attr.SDPA_VIDEO_PRODUCT_TYPE_FULL",
      "item.ad_dsp_info.common_info_attr.SDPA_VIDEO_PRODUCT_THEME_FULL",
      "item.ad_dsp_info.common_info_attr.SDPA_CAR_BRAND_NAME_FULL",
      "item.ad_dsp_info.common_info_attr.SDPA_CAR_BRAND_CLASSIFICATION_FULL"};
  BSFieldEnum item_attr_name_{item_attr_name};
  DISALLOW_COPY_AND_ASSIGN(BSExtractSdpaPhotoFeatureFix);
};

}  // namespace ad_algorithm
}  // namespace ks
