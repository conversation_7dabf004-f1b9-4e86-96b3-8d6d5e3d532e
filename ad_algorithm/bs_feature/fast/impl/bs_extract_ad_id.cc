#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_id.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdId::BSExtractAdId() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_id);
}

void BSExtractAdId::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_creative_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_exists;
  bool creative_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_creative_exists, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  auto enum_campaign_id = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_id;
  uint64_t campaign_id = BSFieldHelper::GetSingular<uint64_t>(*bs, enum_campaign_id, pos);

  auto enum_creative_id = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_id;
  uint64_t creative_id = BSFieldHelper::GetSingular<uint64_t>(*bs, enum_creative_id, pos);

  auto enum_unit_id = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_id;
  uint64_t unit_id = BSFieldHelper::GetSingular<uint64_t>(*bs, enum_unit_id, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && info_exists &&
      creative_exists) {
    // std::string& literal = base::StringPrintf("1_User_Ad_Cre_%lu",
    // creative_id);
    //      const std::string &literal1 = concat("1_User_Ad_Cre_",creative_id);
    //      addFeature(literal1, 1.0, result);
    AddFeature(GetFeature(FeaturePrefix::CREATIVE_ID, creative_id), 1.0f, result);

    // literal = base::StringPrintf("1_User_Ad_Unit_%lu", unit_id);
    //      const std::string &literal2 = concat("1_User_Ad_Unit_",unit_id);
    //      addFeature(literal2, 1.0, result);
    AddFeature(GetFeature(FeaturePrefix::UNIT_ID, unit_id), 1.0f, result);

    // literal = base::StringPrintf("1_User_Ad_Cam_%lu", campaign_id);
    //      const std::string &literal3 = concat("1_User_Ad_Cam_",campaign_id);
    //      addFeature(literal3, 1.0, result);
    AddFeature(GetFeature(FeaturePrefix::CAMPAIGN_ID, campaign_id), 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
