#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_item_dense_author_price.dark
class BSExtractAuthorPrice80 : public BSFastFeature {
 public:
  BSExtractAuthorPrice80();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorPrice80);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorPrice80);
}  // namespace ad_algorithm
}  // namespace ks
