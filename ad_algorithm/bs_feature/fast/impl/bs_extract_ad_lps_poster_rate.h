#pragma once
#include <string>
#include <vector>

#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdLpsPosterRate : public BSFastFeature {
 public:
  BSExtractAdLpsPosterRate() : BSFastFeature(FeatureType::DENSE_ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_action_count_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_action_count_value);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_action_count_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_action_count_value);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_action_count_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_action_count_value);
  }

  inline void AddRatio(const BSMapField<int64_t, int64_t>& map, int fenzi_index, int fenmu_index,
                       std::vector<float>* rate_vec) {
    auto fenzi_iter = map.Get(fenzi_index);
    auto fenmu_iter = map.Get(fenmu_index);
    if (fenzi_iter.second && fenmu_iter.second && fenmu_iter.first > 0) {
      float rate = (float)(fenzi_iter.first) / (fenmu_iter.first + 1);
      rate_vec->push_back(rate);
    } else {
      rate_vec->push_back(0.0);
    }
  }

  inline void addCommonFeature(const BSMapField<int64_t, int64_t>& map, std::vector<float>* rate_vec) {
    AddRatio(map, 2, 1, rate_vec);
    AddRatio(map, 53, 2, rate_vec);
    AddRatio(map, 53, 1, rate_vec);

    auto fenzi_iter = map.Get(1);
    if (fenzi_iter.second && fenzi_iter.first > 0) {
      rate_vec->push_back((float)std::log10((1 + fenzi_iter.first)));
    } else {
      rate_vec->push_back((float)std::log10((1)));
    }
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    std::vector<float> rate_vec;
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD ||
         item_type == bs::ItemType::AD_BRAND) &&
        has_ad_dsp_info) {
      auto creative_map_key = BSFieldEnum::adlog_item_ad_dsp_info_creative_action_count_key;
      auto creative_map_value = BSFieldEnum::adlog_item_ad_dsp_info_creative_action_count_value;
      BSMapField<int64_t, int64_t> bs_creative_map(*bs, creative_map_key, creative_map_value, pos);

      auto unit_map_key = BSFieldEnum::adlog_item_ad_dsp_info_unit_action_count_key;
      auto unit_map_value = BSFieldEnum::adlog_item_ad_dsp_info_unit_action_count_value;
      BSMapField<int64_t, int64_t> bs_unit_map(*bs, unit_map_key, unit_map_value, pos);

      auto campaign_map_key = BSFieldEnum::adlog_item_ad_dsp_info_campaign_action_count_key;
      auto campaign_map_value = BSFieldEnum::adlog_item_ad_dsp_info_campaign_action_count_value;
      BSMapField<int64_t, int64_t> bs_campaign_map(*bs, campaign_map_key, campaign_map_value, pos);

      if (bs_creative_map.size() > 0) {
        addCommonFeature(bs_creative_map, &rate_vec);
      } else {
        for (int i = 0; i < 4; i++) {
          rate_vec.push_back(0.0);
        }
      }
      if (bs_unit_map.size() > 0) {
        addCommonFeature(bs_unit_map, &rate_vec);
      } else {
        for (int i = 0; i < 4; i++) {
          rate_vec.push_back(0.0);
        }
      }
      if (bs_campaign_map.size() > 0) {
        addCommonFeature(bs_campaign_map, &rate_vec);
      } else {
        for (int i = 0; i < 4; i++) {
          rate_vec.push_back(0.0);
        }
      }
      int i = 0;
      for (auto iter = rate_vec.begin(); iter != rate_vec.end(); iter++) {
        AddFeature(i++, *iter, result);
      }
    }
  }

 private:
  const std::string USED_FEATURES[3] = {"item.ad_dsp_info.creative.action_count",
                                        "item.ad_dsp_info.unit.action_count",
                                        "item.ad_dsp_info.campaign.action_count"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdLpsPosterRate);
};

REGISTER_BS_EXTRACTOR(BSExtractAdLpsPosterRate);

}  // namespace ad_algorithm
}  // namespace ks
