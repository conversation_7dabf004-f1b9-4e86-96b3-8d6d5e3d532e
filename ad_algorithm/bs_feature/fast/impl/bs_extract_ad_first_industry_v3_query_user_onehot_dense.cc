#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_query_user_onehot_dense.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdFirstIndustryV3QueryUserOnehotDense::BSExtractAdFirstIndustryV3QueryUserOnehotDense()
    : BSFastFeature(FeatureType::DENSE_USER) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_468);
}

void BSExtractAdFirstIndustryV3QueryUserOnehotDense::Extract(const BSLog& bslog, size_t pos,
                                                             std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_key_468 = BSFieldEnum::adlog_context_info_common_attr_key_468;
  BSRepeatedField<int64_t> key_468(*bs, enum_key_468);

  if (!key_468.is_empty()) {
    for (size_t i = 0; i < class_type_.size(); i++) {
      if (class_type_[i] == key_468.Get(0)) {
        AddFeature(i, 3, result);
      } else {
        AddFeature(i, 0, result);
      }
    }
  }
}

}  // namespace ad_algorithm
}  // namespace ks
