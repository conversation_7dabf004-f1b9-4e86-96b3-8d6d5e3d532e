#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_create_time.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdCreateTime::BSExtractAdCreateTime() : BSFastFeature(ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_create_time);
}

void BSExtractAdCreateTime::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists;
  bool base_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_base_exists, pos);

  auto enum_creative_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_exists;
  bool creative_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_creative_exists, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  auto enum_create_time = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_create_time;
  uint64_t create_time = BSFieldHelper::GetSingular<uint64_t>(*bs, enum_create_time, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && info_exists &&
      creative_exists && base_exists) {
    uint64_t cur_time = base::GetTimestamp() / 1000;
    uint64_t hour = (cur_time - create_time) / 1000 / 60 / 60;
    uint64_t day = hour / 24;
    AddFeature(GetFeature(FeaturePrefix::PHOTO_CREATE_TIME_HOUR, hour), 1.0f, result);
    AddFeature(GetFeature(FeaturePrefix::PHOTO_CREATE_TIME_DAY, day), 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
