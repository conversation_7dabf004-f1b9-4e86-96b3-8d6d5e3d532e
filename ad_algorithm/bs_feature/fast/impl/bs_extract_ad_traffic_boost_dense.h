#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdTrafficBoostDense : public BSFastFeature {
 public:
  BSExtractAdTrafficBoostDense();
  virtual void Extract(const BSLog &bslog, size_t pos, std::vector<ExtractResult> *result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdTrafficBoostDense);
};

REGISTER_BS_EXTRACTOR(BSExtractAdTrafficBoostDense);

}  // namespace ad_algorithm
}  // namespace ks
