#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_delivery_type.h"

namespace ks {
namespace ad_algorithm {
BSExtractBrandAdDeliveryType::BSExtractBrandAdDeliveryType() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_unit_type);
}

void BSExtractBrandAdDeliveryType::Extract(
            const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  int item_type =
      BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  bool has_ad_dsp_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
  bool has_unit =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_exists, pos);
  bool has_base =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists, pos);

  if (item_type == bs::ItemType::AD_BRAND && has_ad_dsp_info && has_unit && has_base) {
    uint64_t unit_type = BSFieldHelper::GetSingular<uint64_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_unit_type, pos);
    AddFeature(GetFeature(FeaturePrefix::PHOTO_BID_TYPE, unit_type), 1.0f, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
