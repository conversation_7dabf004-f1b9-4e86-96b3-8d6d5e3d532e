#pragma once
#include <string>
#include <vector>
#include <unordered_map>
#include "base/hash_function/city.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandCombineUserClickMatchNum : public BSFastFeature {
 public:
  BSExtractBrandCombineUserClickMatchNum();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  std::unordered_map<uint64, std::string> transfer;
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandCombineUserClickMatchNum);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandCombineUserClickMatchNum);
}  // namespace ad_algorithm
}  // namespace ks
