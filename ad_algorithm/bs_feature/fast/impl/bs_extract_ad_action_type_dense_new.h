#pragma once

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdActionTypeDenseNew : public BSFastFeature {
 public:
  BSExtractAdActionTypeDenseNew();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdActionTypeDenseNew);
};

REGISTER_BS_EXTRACTOR(BSExtractAdActionTypeDenseNew);
}  // namespace ad_algorithm
}  // namespace ks
