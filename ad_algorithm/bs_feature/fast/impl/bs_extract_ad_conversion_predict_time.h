#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdConversionPredictTime : public BSFastFeature {
 public:
  BSExtractAdConversionPredictTime();

  virtual void Extract(const BSLog &bslog, size_t pos, std::vector<ExtractResult> *result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdConversionPredictTime);
};

REGISTER_BS_EXTRACTOR(BSExtractAdConversionPredictTime);

}  // namespace ad_algorithm
}  // namespace ks
