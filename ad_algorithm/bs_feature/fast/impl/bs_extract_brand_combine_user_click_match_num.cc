#include <string>
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_combine_user_click_match_num.h"

namespace ks {
namespace ad_algorithm {

BSExtractBrandCombineUserClickMatchNum::
         BSExtractBrandCombineUserClickMatchNum() : BSFastFeature(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_2_list_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_3_list_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_4_list_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_5_list_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_6_list_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_8_list_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_19_list_size);

  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_product_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_2_list_product_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_3_list_product_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_4_list_product_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_5_list_product_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_6_list_product_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_8_list_product_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_19_list_product_id_hash);

  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_photo_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_2_list_photo_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_3_list_photo_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_4_list_photo_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_5_list_photo_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_6_list_photo_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_8_list_photo_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_19_list_photo_id);

  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_second_industry_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_2_list_second_industry_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_3_list_second_industry_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_4_list_second_industry_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_5_list_second_industry_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_6_list_second_industry_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_8_list_second_industry_id_hash);
  attr_metas_.emplace_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_19_list_second_industry_id_hash);

  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_photo_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);

  transfer = {{21, "角色扮演"},
              {22, "射击游戏"},
              {23, "棋牌捕鱼"},
              {24, "体育游戏"},
              {25, "养成游戏"},
              {26, "综合游戏平台"},
              {27, "休闲益智"},
              {28, "策略游戏"},
              {29, "综合电商"},
              {30, "母婴"},
              {31, "IT消电"},
              {32, "医疗"},
              {33, "服饰"},
              {34, "房产家居"},
              {35, "跨境"},
              {36, "快消"},
              {37, "日化"},
              {38, "电商商家"},
              {39, "商业零售"},
              {40, "其他电商零售类"},
              {41, "新闻"},
              {42, "影音娱乐"},
              {43, "团购/折扣"},
              {44, "生活/健康"},
              {45, "婚恋/交友"},
              {46, "招聘"},
              {47, "工具"},
              {48, "其他资讯类"},
              {49, "主机厂"},
              {50, "机动车销售与服务"},
              {51, "车类配件及周边"},
              {52, "汽车资讯与服务平台"},
              {53, "出行服务(含物流)"},
              {54, "其他(含非机动)"},
              {55, "小额贷款"},
              {56, "P2P网贷平台"},
              {57, "金融综合线上平台"},
              {58, "银行服务"},
              {59, "信用卡"},
              {60, "证券"},
              {61, "保险"},
              {63, "第三方支付"},
              {64, "其他金融服务类"},
              {65, "饮料/茶/酒水"},
              {66, "食品零食"},
              {67, "其他食品饮料类"},
              {68, "护肤品/化妆品"},
              {69, "卫浴/口腔用品"},
              {70, "洗涤/卫生用品"},
              {71, "奢侈品"},
              {72, "其他美妆日化类"},
              {73, "3C"},
              {74, "家用电器"},
              {75, "办公设备"},
              {76, "运营商"},
              {77, "其他IT消电类"},
              {78, "交易平台"},
              {79, "房地产"},
              {80, "物业管理"},
              {81, "其他房产类"},
              {82, "运动服饰"},
              {83, "休闲装/正装"},
              {84, "内衣"},
              {85, "鞋/箱包"},
              {86, "饰品/配饰"},
              {87, "其他服饰类"},
              {88, "留学/语言培训"},
              {89, "高等教育"},
              {90, "民办学校"},
              {91, "儿童/学生培训"},
              {92, "职业技能培训"},
              {93, "教育类平台"},
              {94, "其他教育类"},
              {95, "在线旅游服务平台"},
              {96, "传统旅行社"},
              {98, "航空公司"},
              {99, "旅游景点"},
              {100, "酒店"},
              {101, "其他旅游类"},
              {102, "药品/保健品"},
              {103, "医疗机构"},
              {104, "医疗美容"},
              {105, "医疗器械"},
              {106, "医疗平台"},
              {107, "其他医疗类"},
              {108, "社会组织类"},
              {109, "政府组织类"},
              {110, "家装建材"},
              {111, "装修服务"},
              {112, "家具灯饰"},
              {113, "家具家装综合"},
              {114, "家纺家饰"},
              {115, "其他家居建材类"},
              {116, "机械设备"},
              {117, "出版传媒"},
              {118, "招商加盟"},
              {119, "节能环保"},
              {120, "工农业"},
              {121, "安全安保"},
              {122, "商务服务"},
              {123, "其他面向企业类"},
              {124, "乐器"},
              {125, "娱乐票务"},
              {126, "宠物服务"},
              {127, "体育器械"},
              {128, "运势测算"},
              {129, "休闲活动"},
              {131, "图书音像"},
              {132, "礼品收藏"},
              {133, "展览"},
              {134, "其他运动娱乐休闲类"},
              {135, "母婴用品"},
              {136, "幼儿食品"},
              {137, "母婴服务"},
              {138, "其他母婴护理类"},
              {139, "婚庆交友"},
              {140, "保姆家政"},
              {141, "居民服务"},
              {142, "冲印摄影"},
              {143, "家电维修"},
              {144, "餐饮服务"},
              {145, "丽人美发"},
              {146, "其他生活服务类"},
              {147, "服装"},
              {148, "配饰"},
              {149, "箱包"},
              {150, "生活用品"},
              {151, "数码产品"},
              {152, "化妆品"},
              {154, "食品"},
              {155, "其他直营电商"},
              {1023, "3C"},
              {1024, "家用电器"},
              {1025, "其他IT消电类"},
              {1028, "运营商"},
              {1032, "综合电商"},
              {1033, "快消"},
              {1034, "商业零售"},
              {1036, "电商商家"},
              {1037, "电商商家"},
              {1038, "电商商家"},
              {1039, "电商商家"},
              {1057, "电商商家"},
              {1058, "电商商家"},
              {1059, "电商商家"},
              {1090, "交易平台"},
              {1091, "房地产"},
              {1092, "物业管理"},
              {1093, "其他房产类"},
              {1094, "其他房产类"},
              {1103, "家装建材"},
              {1104, "装修服务"},
              {1105, "家具灯饰"},
              {1106, "家具家装综合"},
              {1107, "家纺家饰"},
              {1108, "其他家居建材类"},
              {1109, "其他家居建材类"},
              {1110, "其他家居建材类"},
              {1111, "主机厂"},
              {1112, "机动车销售与服务"},
              {1113, "车类配件及周边"},
              {1114, "汽车资讯与服务平台"},
              {1115, "出行服务(含物流"},
              {1116, "其他(含非机动)"},
              {1117, "主机厂"},
              {1128, "银行服务"},
              {1129, "证券"},
              {1130, "小额贷款"},
              {1131, "基金"},
              {1135, "保险"},
              {1136, "保险"},
              {1153, "护肤品/化妆品"},
              {1154, "卫浴/口腔用品"},
              {1155, "其他美妆日化类"},
              {1156, "其他美妆日化类"},
              {1160, "母婴用品"},
              {1256, "化妆品"},
              {1257, "化妆品"},
              {1258, "其他直营电商"},
              {1259, "其他直营电商"}};
}

void BSExtractBrandCombineUserClickMatchNum::Extract(
                const BSLog &bslog, size_t pos, std::vector<ExtractResult> *result) {
  // 0:imp; 1:click1; 2:click2 3:play3s 4:play5s 5:playend 6:conv, 8:lps, 19:
  // union_lps
  std::vector<int> urb_type_array = {1, 2, 3, 4, 5, 6, 8, 19};
  int urb_size = *std::max_element(urb_type_array.begin(), urb_type_array.end()) + 1;
  std::vector<BSFieldEnum> urb_enums;
  std::vector<BSFieldEnum> product_enums;
  std::vector<BSFieldEnum> photo_id_enums;
  std::vector<BSFieldEnum> second_industry_enums;

  urb_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_size);
  urb_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_2_list_size);
  urb_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_3_list_size);
  urb_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_4_list_size);
  urb_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_5_list_size);
  urb_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_6_list_size);
  urb_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_8_list_size);
  urb_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_19_list_size);

  product_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_product_id_hash);
  product_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_2_list_product_id_hash);
  product_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_3_list_product_id_hash);
  product_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_4_list_product_id_hash);
  product_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_5_list_product_id_hash);
  product_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_6_list_product_id_hash);
  product_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_8_list_product_id_hash);
  product_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_19_list_product_id_hash);

  photo_id_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_photo_id);
  photo_id_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_2_list_photo_id);
  photo_id_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_3_list_photo_id);
  photo_id_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_4_list_photo_id);
  photo_id_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_5_list_photo_id);
  photo_id_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_6_list_photo_id);
  photo_id_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_8_list_photo_id);
  photo_id_enums.push_back(BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_19_list_photo_id);

  second_industry_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_1_list_second_industry_id_hash);
  second_industry_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_2_list_second_industry_id_hash);
  second_industry_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_3_list_second_industry_id_hash);
  second_industry_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_4_list_second_industry_id_hash);
  second_industry_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_5_list_second_industry_id_hash);
  second_industry_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_6_list_second_industry_id_hash);
  second_industry_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_8_list_second_industry_id_hash);
  second_industry_enums.push_back(
      BSFieldEnum::adlog_user_info_explore_long_term_ad_action_key_19_list_second_industry_id_hash);

  std::vector<int> photo_id_match_num(urb_size, 0);
  std::vector<int> industry_id_hash_match_num(urb_size, 0);
  std::vector<int> product_id_match_num(urb_size, 0);
  std::vector<int> total_num(urb_size, 0);

  std::vector<std::vector<ks::ad_algorithm::FeaturePrefix>> feature_prefix(
      urb_size, std::vector<ks::ad_algorithm::FeaturePrefix>(3, USER_ID));
  feature_prefix[1][2] = COMBINE_USER_LONG_CLICK1_AD_PRODUCT_ID_MATCH_NUM;

  feature_prefix[2][0] = COMBINE_USER_LONG_CLICK2_AD_PHOTO_ID_MATCH_NUM;
  feature_prefix[2][1] = COMBINE_USER_LONG_CLICK2_AD_INDUSTRY_ID_MATCH_NUM;
  feature_prefix[2][2] = COMBINE_USER_LONG_CLICK2_AD_PRODUCT_ID_MATCH_NUM;

  feature_prefix[3][2] = COMBINE_USER_LONG_PLAY3S_AD_PRODUCT_ID_MATCH_NUM;

  feature_prefix[4][0] = COMBINE_USER_LONG_PLAY5S_AD_PHOTO_ID_MATCH_NUM;
  feature_prefix[4][1] = COMBINE_USER_LONG_PLAY5S_AD_INDUSTRY_ID_MATCH_NUM;
  feature_prefix[4][2] = COMBINE_USER_LONG_PLAY5S_AD_PRODUCT_ID_MATCH_NUM;

  feature_prefix[5][0] = COMBINE_USER_LONG_PLAYEND_AD_PHOTO_ID_MATCH_NUM;
  feature_prefix[5][1] = COMBINE_USER_LONG_PLAYEND_AD_INDUSTRY_ID_MATCH_NUM;
  feature_prefix[5][2] = COMBINE_USER_LONG_PLAYEND_AD_PRODUCT_ID_MATCH_NUM;

  feature_prefix[6][0] = COMBINE_USER_LONG_CONV_AD_PHOTO_ID_MATCH_NUM;
  feature_prefix[6][1] = COMBINE_USER_LONG_CONV_AD_INDUSTRY_ID_MATCH_NUM;
  feature_prefix[6][2] = COMBINE_USER_LONG_CONV_AD_PRODUCT_ID_MATCH_NUM;

  feature_prefix[8][0] = COMBINE_USER_LONG_LPS_AD_PHOTO_ID_MATCH_NUM;
  feature_prefix[8][1] = COMBINE_USER_LONG_LPS_AD_INDUSTRY_ID_MATCH_NUM;
  feature_prefix[8][2] = COMBINE_USER_LONG_LPS_AD_PRODUCT_ID_MATCH_NUM;

  feature_prefix[19][0] = COMBINE_USER_LONG_UNION_LPS_AD_PHOTO_ID_MATCH_NUM;
  feature_prefix[19][1] = COMBINE_USER_LONG_UNION_LPS_AD_INDUSTRY_ID_MATCH_NUM;
  feature_prefix[19][2] = COMBINE_USER_LONG_UNION_LPS_AD_PRODUCT_ID_MATCH_NUM;

  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto key_user_info_exists = BSFieldEnum::adlog_user_info_exists;
  bool has_user_info = BSFieldHelper::GetSingular<bool, true>(*bs, key_user_info_exists, pos);

  auto key_map_size = BSFieldEnum::adlog_user_info_explore_long_term_ad_action_size;
  int map_size = BSFieldHelper::GetSingular<int, true>(*bs, key_map_size, pos);

  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);

  if (has_user_info && map_size > 0 &&
      (item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::AD_BRAND)) {
    auto key_photo_id = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_photo_id;
    uint64_t photo_id = BSFieldHelper::GetSingular<uint64_t>(*bs, key_photo_id, pos);
    uint64_t second_industry_id_hash = UINT64_MAX;
    uint64_t product_id_hash = UINT64_MAX;

    auto key_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists;
    bool has_base = BSFieldHelper::GetSingular<bool>(*bs, key_base_exists, pos);
    if (has_base) {
      bool has_industry_id = false;
      auto key_industry_id = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_id;
      int industry_id = BSFieldHelper::GetSingular<int>(*bs, key_industry_id, pos, &has_industry_id);
      if (has_industry_id) {
        if (transfer.find(industry_id) != transfer.end()) {
          std::string &industry_name = transfer[industry_id];
          if (industry_name.size() > 0) {
            second_industry_id_hash = base::CityHash64(industry_name.c_str(), industry_name.size());
          }
        }
      }

      auto key_name = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name;
      absl::string_view product_name = BSFieldHelper::GetSingular<absl::string_view>(*bs, key_name, pos);
      product_id_hash = base::CityHash64(product_name.data(), product_name.size());
    }

    for (int i = 0; i < urb_type_array.size(); i++) {
      int urb_type = urb_type_array[i];
      bool has_list_size = false;
      int list_size = BSFieldHelper::GetSingular<int, true>(*bs, urb_enums[i], pos, &has_list_size);
      if (!has_list_size) {
        photo_id_match_num[urb_type] = -1;
        industry_id_hash_match_num[urb_type] = -1;
        product_id_match_num[urb_type] = -1;
      } else {
        BSRepeatedField<uint64_t, true> product_id_hash_list(*bs, product_enums[i], pos);
        BSRepeatedField<int64_t, true> photo_id_list(*bs, photo_id_enums[i], pos);
        BSRepeatedField<int64_t, true> second_industry_id_hash_list(*bs, second_industry_enums[i], pos);
        for (int idx = 0; idx < list_size; idx++) {
          if (total_num[urb_type] > 1000) {
            break;
          }
          if (idx < product_id_hash_list.size() && product_id_hash_list.Get(idx) == product_id_hash) {
            product_id_match_num[urb_type]++;
          }
          if (urb_type == 1 || urb_type == 3) {
            total_num[urb_type]++;
            continue;
          }
          if (idx < photo_id_list.size() && photo_id_list.Get(idx) == photo_id) {
            photo_id_match_num[urb_type]++;
          }
          if (idx < second_industry_id_hash_list.size() &&
              second_industry_id_hash_list.Get(idx) == second_industry_id_hash) {
            industry_id_hash_match_num[urb_type]++;
          }
          total_num[urb_type]++;
        }
      }
    }
  } else {
    for (int i = 0; i < urb_type_array.size(); i++) {
      photo_id_match_num[urb_type_array[i]] = -1;
      industry_id_hash_match_num[urb_type_array[i]] = -1;
      product_id_match_num[urb_type_array[i]] = -1;
    }
  }

  for (int i = 0; i < urb_type_array.size(); i++) {
    AddFeature(GetFeature(feature_prefix[urb_type_array[i]][2], product_id_match_num[urb_type_array[i]]),
                1.0, result);
    if (urb_type_array[i] == 1 || urb_type_array[i] == 3) {
      continue;
    }
    AddFeature(GetFeature(feature_prefix[urb_type_array[i]][0], photo_id_match_num[urb_type_array[i]]), 1.0,
                result);
    AddFeature(
        GetFeature(feature_prefix[urb_type_array[i]][1], industry_id_hash_match_num[urb_type_array[i]]),
        1.0, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
