#pragma once
#include <string>
#include <vector>

#include "base/hash_function/city.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
using std::string;

namespace ks {
namespace ad_algorithm {

class BSExtractAdUriLps : public BSFastFeature {
 public:
  BSExtractAdUriLps() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_uri);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_dsp_info_exists =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    bool has_unit_exists =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_exists, pos);
    bool has_campaign_exists =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists, pos);
    bool has_campaign_base_exists =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists, pos);

    std::hash<std::string> hash_fn;

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD ||
         item_type == bs::ItemType::AD_BRAND) &&
        has_dsp_info_exists && has_unit_exists && has_campaign_exists && has_campaign_base_exists) {
      uint64_t ad_campaign_type = BSFieldHelper::GetSingular<uint64_t>(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
      if (ad_campaign_type == 3 || ad_campaign_type == 4 || ad_campaign_type == 5) {
        bool has_unit_base_exists =
            BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists, pos);
        if (has_unit_base_exists) {
          std::string uri{BSFieldHelper::GetSingular<absl::string_view>(
              *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_uri, pos)};
          if (uri.length() > 0) {
            uint64 uri_hash = base::CityHash64(uri.c_str(), uri.length());
            AddFeature(GetFeature(FeaturePrefix::PHOTO_BASE_URI, uri_hash), 1.0f, result);
          }
        }
      }
    }
  }

 private:
  const std::string USED_FEATURES[2] = {"item.ad_dsp_info.campaign.base.type",
                                        "item.ad_dsp_info.unit.base.uri"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdUriLps);
};

REGISTER_BS_EXTRACTOR(BSExtractAdUriLps);

}  // namespace ad_algorithm
}  // namespace ks
