#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_applist_sdpa_info.h"

#include "base/hash_function/city.h"

namespace ks {
namespace ad_algorithm {

template <BSFieldEnum item_attr_name>
BSExtractCombineApplistSdpaInfo<item_attr_name>::BSExtractCombineApplistSdpaInfo()
    : BSFastFeature(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(item_attr_name_);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_app_package);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90043);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90042);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90041);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90047);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90048);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_24002);
}

template <BSFieldEnum item_attr_name>
void BSExtractCombineApplistSdpaInfo<item_attr_name>::Extract(const BSLog& bslog, size_t pos,
                                                              std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  // 1. FeaturePrefix
  auto feature_prefix = FeaturePrefix::COMBINE_USER_APP_LIST_SDPA_PID;
  switch (item_attr_name) {
    case BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_24002:
      feature_prefix = FeaturePrefix::COMBINE_USER_APP_LIST_SDPA_PID;
      break;
    case BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90041:
      feature_prefix = FeaturePrefix::COMBINE_USER_APP_LIST_SDPA_OUTERID;
      break;
    case BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90043:
      feature_prefix = FeaturePrefix::COMBINE_USER_APP_LIST_SDPA_PRODUCT_NAME;
      break;
    case BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90042:
      feature_prefix = FeaturePrefix::COMBINE_USER_APP_LIST_SDPA_PRODUCT_TITLE;
      break;
    case BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90048:
      feature_prefix = FeaturePrefix::COMBINE_USER_APP_LIST_SDPA_ECOM_PRICE;
      break;
    case BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90047:
      feature_prefix = FeaturePrefix::COMBINE_USER_APP_LIST_SDPA_ECOM_CATE3;
  }
  // 2. 得到 SDPA info ID
  int64_t sdpa_id = 0;
  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  bool has_ad_dsp_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
  int32_t common_info_attr_size = BSFieldHelper::GetSingular<int32_t>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size, pos);
  if (item_type == bs::ItemType::AD_DSP && has_ad_dsp_info && common_info_attr_size > 0) {
    sdpa_id = BSFieldHelper::GetSingular<int64_t>(*bs, item_attr_name_, pos);
  }
  // 3. 得到 Applist
  auto key_user_info_exists = BSFieldEnum::adlog_user_info_exists;
  bool has_user_info = BSFieldHelper::GetSingular<bool, true>(*bs, key_user_info_exists, pos);
  auto key_has_ad_user_info = BSFieldEnum::adlog_user_info_ad_user_info_exists;
  bool has_ad_user_info = BSFieldHelper::GetSingular<bool, true>(*bs, key_has_ad_user_info, pos);

  if (has_user_info && has_ad_user_info && sdpa_id != 0) {
    auto key_app_package = BSFieldEnum::adlog_user_info_ad_user_info_device_info_app_package;
    BSRepeatedField<absl::string_view, true> app_package(*bs, key_app_package, pos);
    for (int i = 0; i < app_package.size(); i++) {
      AddFeature(GetFeature(feature_prefix,
                            base::CityHash64(app_package.Get(i).data(), app_package.Get(i).size()), sdpa_id),
                 1.0, result);
    }
  }
}

using BSExtractCombineApplistSdpaPid = BSExtractCombineApplistSdpaInfo<
    BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_24002>;
REGISTER_BS_EXTRACTOR(BSExtractCombineApplistSdpaPid);

using BSExtractCombineApplistSdpaOuterid =
    BSExtractCombineApplistSdpaInfo<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90041>;
REGISTER_BS_EXTRACTOR(BSExtractCombineApplistSdpaOuterid);

using BSExtractCombineApplistSdpaProductTitle =
    BSExtractCombineApplistSdpaInfo<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90042>;
REGISTER_BS_EXTRACTOR(BSExtractCombineApplistSdpaProductTitle);

using BSExtractCombineApplistSdpaProductName =
    BSExtractCombineApplistSdpaInfo<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90043>;
REGISTER_BS_EXTRACTOR(BSExtractCombineApplistSdpaProductName);

using BSExtractCombineApplistSdpaEcomPrice =
    BSExtractCombineApplistSdpaInfo<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90048>;
REGISTER_BS_EXTRACTOR(BSExtractCombineApplistSdpaEcomPrice);

using BSExtractCombineApplistSdpaEcomCate3 =
    BSExtractCombineApplistSdpaInfo<BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_90047>;
REGISTER_BS_EXTRACTOR(BSExtractCombineApplistSdpaEcomCate3);

}  // namespace ad_algorithm
}  // namespace ks
