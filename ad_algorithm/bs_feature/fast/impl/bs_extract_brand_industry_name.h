#pragma once
#include <string>
#include <unordered_map>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandIndustryName : public BSFastFeatureNoPrefix {
 public:
  BSExtractBrandIndustryName() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_first_id);
  }

  std::unordered_map<int, absl::string_view> industry_map = {
      {1000, "IT消电类"},   {1001, "通信类"},     {1002, "电商零售类"}, {1003, "电商商家"},
      {1004, "快手小店"},   {1005, "房地产"},     {1006, "服装配饰"},   {1007, "家居建材类"},
      {1008, "交通类"},     {1009, "教育培训类"}, {1010, "金融服务类"}, {1011, "旅游类"},
      {1012, "快速消费品"}, {1013, "商务服务"},   {1014, "招商加盟"},   {1015, "生活服务类"},
      {1016, "食品饮料类"}, {1017, "医疗类"},     {1018, "游戏类"},     {1019, "运动娱乐休闲类"},
      {1020, "政府组织类"}, {1021, "传媒资讯类"}, {1022, "直营电商"}};

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    bool has_advertiser_base = BSFieldHelper::GetSingular<bool>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists, pos);
    if (!has_dsp_info || !has_advertiser_base) {
      return;
    }
    int64_t industry_id = BSFieldHelper::GetSingular<int64_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_first_id, pos);
    auto industry_iter = industry_map.find(industry_id);
    if (industry_iter != industry_map.end()) {
      absl::string_view industry_name = industry_iter->second;
      AddFeature(ad_nn::bs::Hash(industry_name), 1.0f, result);
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"item.ad_dsp_info.advertiser_base.industry_first_id"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandIndustryName);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandIndustryName);

}  // namespace ad_algorithm
}  // namespace ks
