#pragma once
#include <math.h>
#include <time.h>

#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_feature.pb.h"

// using ::bs::kuaishou::ad::AdCallbackLog;
namespace ks {
namespace ad_algorithm {
class BSExtractAdQueueType : public BSFastFeatureNoPrefix {
 public:
  BSExtractAdQueueType() : BSFastFeatureNoPrefix(FeatureType::COMBINE) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_431_key);
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_431_value);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    uint64 item_id = BSFieldHelper::GetSingular<uint64_t>(*bs, BSFieldEnum::adlog_item_id, pos);
    bool is_soft = false;
    uint64 index = 0;
    bool has_value = BSFieldHelper::HasSingular<int64_t, true>(*bs, BSFieldEnum::adlog_context_exists, pos);
    if (has_value) {
      auto key_enum = BSFieldEnum::adlog_context_info_common_attr_key_431_key;
      auto value_enum = BSFieldEnum::adlog_context_info_common_attr_key_431_value;
      BSMapField<int64_t, bool, true> ad_queue_type_map(*bs, key_enum, value_enum, pos);
      auto iter = ad_queue_type_map.Get(item_id);
      if (iter.second) {
        is_soft = iter.second;
      }
    }

    double value = 1.0;
    if (is_soft) {
      index = 1;
    }
    AddFeature(index, value, result);
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdQueueType);
};
REGISTER_BS_EXTRACTOR(BSExtractAdQueueType);
}  // namespace ad_algorithm
}  // namespace ks
