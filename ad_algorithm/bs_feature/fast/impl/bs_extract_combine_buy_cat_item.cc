#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_buy_cat_item.h"

namespace ks {
namespace ad_algorithm {
BSExtractCombineBuyCatItem::BSExtractCombineBuyCatItem() : BSFastFeature(COMBINE) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_170);
}

void BSExtractCombineBuyCatItem::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  auto enum_attr_size = BSFieldEnum::adlog_user_info_common_info_attr_size;
  int32_t attr_size = BSFieldHelper::GetSingular<int32_t, true>(*bs, enum_attr_size, pos);

  auto enum_info_exists = BSFieldEnum::adlog_user_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool, true>(*bs, enum_info_exists, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD)) {
    auto enum_item_id = BSFieldEnum::adlog_item_id;
    uint64_t item_id = BSFieldHelper::GetSingular<uint64_t>(*bs, enum_item_id, pos);

    auto enum_key_170 = BSFieldEnum::adlog_user_info_common_info_attr_key_170;
    BSRepeatedField<int64_t, true> key_170(*bs, enum_key_170, pos);

    if (info_exists && attr_size > 0) {
      if (!key_170.is_empty()) {
        for (int i = 0; i < key_170.size() && i < 100; i++) {
          AddFeature(GetFeature(FeaturePrefix::COMBINE_BUT_MERCHANT_CAT_ITEM, key_170.Get(i), item_id), 1.0,
                     result);
        }
      }
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
