#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
namespace ks {
namespace ad_algorithm {

template <BSFieldEnum item_attr_name>
class BSExtractSdpaPhotoCntFeature : public BSFastFeature {
 public:
  BSExtractSdpaPhotoCntFeature() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(item_attr_name);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    bool has_ad_dsp_photo_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists, pos);
    int32_t common_info_attr_size = BSFieldHelper::GetSingular<int32_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size, pos);
    if (item_type == bs::ItemType::AD_DSP && has_ad_dsp_info && has_ad_dsp_photo_info &&
        common_info_attr_size > 0) {
      auto key_enum = item_attr_name;
      auto sdpa_feature_value = BSFieldHelper::GetSingular<int64_t>(*bs, key_enum, pos);
      if (sdpa_feature_value != 0) AddFeature(sdpa_feature_value, 1.0f, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractSdpaPhotoCntFeature);
};

using BSExtractSdpaProductImpCntSlot3D =
    BSExtractSdpaPhotoCntFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420071>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductImpCntSlot3D);
using BSExtractSdpaProductImpCntSlot7D =
    BSExtractSdpaPhotoCntFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420072>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductImpCntSlot7D);
using BSExtractSdpaProductClickCntSlot3D =
    BSExtractSdpaPhotoCntFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420073>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductClickCntSlot3D);
using BSExtractSdpaProductClickCntSlot7D =
    BSExtractSdpaPhotoCntFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420074>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductClickCntSlot7D);
using BSExtractSdpaProductP3sCntSlot3D =
    BSExtractSdpaPhotoCntFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420075>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductP3sCntSlot3D);
using BSExtractSdpaProductP3sCntSlot7D =
    BSExtractSdpaPhotoCntFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420076>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductP3sCntSlot7D);
using BSExtractSdpaProductConvCntSlot3D =
    BSExtractSdpaPhotoCntFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420077>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductConvCntSlot3D);
using BSExtractSdpaProductConvCntSlot7D =
    BSExtractSdpaPhotoCntFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420078>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductConvCntSlot7D);
using BSExtractSdpaProductCtrSlot3D =
    BSExtractSdpaPhotoCntFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420079>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductCtrSlot3D);
using BSExtractSdpaProductCtrSlot7D =
    BSExtractSdpaPhotoCntFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420080>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductCtrSlot7D);
using BSExtractSdpaProductCvrSlot3D =
    BSExtractSdpaPhotoCntFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420081>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductCvrSlot3D);
using BSExtractSdpaProductCvrSlot7D =
    BSExtractSdpaPhotoCntFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_420082>;
REGISTER_BS_EXTRACTOR(BSExtractSdpaProductCvrSlot7D);

}  // namespace ad_algorithm
}  // namespace ks
