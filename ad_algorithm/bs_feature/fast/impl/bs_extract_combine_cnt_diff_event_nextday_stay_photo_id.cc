#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cnt_diff_event_nextday_stay_photo_id.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/offline_user_seq_pooling_global_merge.h"

namespace ks {
namespace ad_algorithm {

BSExtractCombineCntDiffEventNextdayStayPhotoId::BSExtractCombineCntDiffEventNextdayStayPhotoId()
    : BSFastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5002442);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5002443);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_time);
}
void BSExtractCombineCntDiffEventNextdayStayPhotoId::Extract(const BSLog& bslog, size_t pos,
                                                             std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5002442, pos);
  auto x2 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5002443, pos);
  auto x3 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_id, pos);
  auto x4 = get_bslog_int64_or_default<true>(*bs, BSFieldEnum::adlog_time, pos);
  auto x5 = get_cnt_diff_with_target_hit_combine(x1, x2, x3, x4);
  add_feature_result(x5, result);
}

}  // namespace ad_algorithm
}  // namespace ks
