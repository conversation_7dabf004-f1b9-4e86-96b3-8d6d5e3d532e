#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/hash_function/city.h"
#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandCombineUserAppListAdId : public BSFastFeature {
 public:
  BSExtractBrandCombineUserAppListAdId() : BSFastFeature(FeatureType::COMBINE, 26, 26) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_device_info_app_package);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_photo_id);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);

    if (item_type != bs::ItemType::AD_BRAND) {
      return;
    }

    auto key_has_ad_dsp_info = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_has_ad_dsp_info, pos);

    auto key_has_creative = BSFieldEnum::adlog_item_ad_dsp_info_creative_exists;
    bool has_creative = BSFieldHelper::GetSingular<bool>(*bs, key_has_creative, pos);

    auto key_has_base = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists;
    bool has_base = BSFieldHelper::GetSingular<bool>(*bs, key_has_base, pos);

    if (!has_ad_dsp_info || !has_creative || !has_base) {
      return;
    }

    auto Enum_creative_id = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_id;
    auto Enum_photo_id = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_photo_id;
    auto Enum_industry_id = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id;

    uint64 creative_id = BSFieldHelper::GetSingular<int64_t>(*bs, Enum_creative_id, pos);
    uint64 photo_id = BSFieldHelper::GetSingular<int64_t>(*bs, Enum_photo_id, pos);
    uint64 industry_id = BSFieldHelper::GetSingular<int64_t>(*bs, Enum_industry_id, pos);

    bool has_user_info = BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
    bool has_ad_user_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_user_info_ad_user_info_exists, pos);
    if (has_user_info && has_ad_user_info) {
      auto key_app_package = BSFieldEnum::adlog_user_info_ad_user_info_device_info_app_package;
      BSRepeatedField<absl::string_view> app_package(*bs, key_app_package, pos);
      for (int i = 0; i < app_package.size(); i++) {
        AddFeature(
            GetFeature(FeaturePrefix::COMBINE_USER_APP_LIST_PHOTOID,
                       base::CityHash64(app_package.Get(i).data(), app_package.Get(i).size()), photo_id),
            1.0, result);
        AddFeature(
            GetFeature(FeaturePrefix::COMBINE_USER_APP_LIST_INDUSTRYID,
                       base::CityHash64(app_package.Get(i).data(), app_package.Get(i).size()), industry_id),
            1.0, result);
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandCombineUserAppListAdId);
};
REGISTER_BS_EXTRACTOR(BSExtractBrandCombineUserAppListAdId);
}  // namespace ad_algorithm
}  // namespace ks
