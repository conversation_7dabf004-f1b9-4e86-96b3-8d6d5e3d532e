#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractUserCmtHetuTag : public BSFastFeature {
 public:
  BSExtractUserCmtHetuTag() : BSFastFeature(FeatureType::USER) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_575);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_user_info = BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
    if (!has_user_info) {
      return;
    }
    BSRepeatedField<int64_t> attr(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_575, pos);
    for (int i = 0; i < attr.size(); ++i) {
      uint64_t id = GetFeature(FeaturePrefix::USER_CMT_PHOTO_HETU_TAG, attr.Get(i));
      AddFeature(id, 1.0, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractUserCmtHetuTag);
};

REGISTER_BS_EXTRACTOR(BSExtractUserCmtHetuTag);

}  // namespace ad_algorithm
}  // namespace ks
