#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/impl/extract_combine_sparse_offline_merge_pooling_global.dark
class BSExtractCombineCntDiffEventConversionPhotoId : public BSFastFeatureNoPrefix {
 public:
  BSExtractCombineCntDiffEventConversionPhotoId();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineCntDiffEventConversionPhotoId);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineCntDiffEventConversionPhotoId);
}  // namespace ad_algorithm
}  // namespace ks
