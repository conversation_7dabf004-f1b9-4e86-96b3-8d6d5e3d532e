#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_new_industry_v3_rnd.h"

namespace ks {
namespace ad_algorithm {
void BSExtractAdNewIndustryV3Rnd::Extract(const BSLog& bslog, size_t pos,
                                          std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  bool has_value = false;
  int64_t new_industry_id = BSFieldHelper::GetSingular<int>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3, pos, &has_value);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_value) {
    AddFeature(GetFeature(FeaturePrefix::PHOTO_CREATIVE_NEW_INDUSTRY_ID, new_industry_id), 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
