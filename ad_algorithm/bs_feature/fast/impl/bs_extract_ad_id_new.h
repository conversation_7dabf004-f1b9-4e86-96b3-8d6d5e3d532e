#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdIdNew : public BSFastFeature {
 public:
  BSExtractAdIdNew() : BSFastFeature(FeatureType::ITEM) {}

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  }

 private:
  const std::string USED_FEATURES[0] = {};

  DISALLOW_COPY_AND_ASSIGN(BSExtractAdIdNew);
};

REGISTER_BS_EXTRACTOR(BSExtractAdIdNew);

}  // namespace ad_algorithm
}  // namespace ks
