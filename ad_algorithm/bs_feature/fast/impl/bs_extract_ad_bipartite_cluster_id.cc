#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_bipartite_cluster_id.h"

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdBipartiteClusterId::BSExtractAdBipartiteClusterId() : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_61033);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists);
}

void BSExtractAdBipartiteClusterId::Extract(const BSLog &bslog, size_t pos,
                                            std::vector<ExtractResult> *result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  bool has_ad_dsp_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
  bool has_dsp_photo_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists, pos);
  bool has_value;
  auto value = ad_nn::BSFieldHelper::GetSingular<int64_t>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_61033, pos, &has_value);
  if (has_ad_dsp_info && has_dsp_photo_info && has_value) {
    AddFeature(0, (uint32_t)value, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
