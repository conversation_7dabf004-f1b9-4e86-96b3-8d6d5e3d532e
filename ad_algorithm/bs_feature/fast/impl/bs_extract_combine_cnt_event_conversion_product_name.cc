#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_cnt_event_conversion_product_name.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/offline_user_seq_pooling_global_merge.h"

namespace ks {
namespace ad_algorithm {

BSExtractCombineCntEventConversionProductName::BSExtractCombineCntEventConversionProductName()
    : BSFastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5002438);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5002441);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_5002461);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5002464);
  attr_metas_.emplace_back(BSFieldEnum::adlog_time);
}
void BSExtractCombineCntEventConversionProductName::Extract(const BSLog& bslog, size_t pos,
                                                            std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5002438, pos);
  auto x2 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5002441, pos);
  auto x3 =
      get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_5002461, pos);
  auto x4 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5002464, pos);
  auto x5 = get_bslog_int64_or_default<true>(*bs, BSFieldEnum::adlog_time, pos);
  auto x6 = get_cnt_with_target_hit_combine_v2(x1, x2, x3, x4, x5);
  add_feature_result(x6, result);
}

}  // namespace ad_algorithm
}  // namespace ks
