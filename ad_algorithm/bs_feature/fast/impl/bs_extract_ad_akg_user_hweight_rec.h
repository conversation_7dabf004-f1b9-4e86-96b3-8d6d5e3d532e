#pragma once
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdAkgUserHweightRec : public BSFastFeature {
 public:
  BSExtractAdAkgUserHweightRec() : BSFastFeature(USER) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_48051);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    BSRepeatedField<int64_t> attr(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_48051, pos);

    uint32_t num = 0;
    double value = 1.0;
    if (attr.is_empty()) {
      return;
    }
    std::vector<ks::ad_algorithm::FeaturePrefix> fea_prefix(50);
    fea_prefix[0] = AD_AKG_USER_CONV_ALL_PHOTO;
    fea_prefix[1] = AD_AKG_USER_CONV_ALL_ACCOUNT;
    fea_prefix[2] = AD_AKG_USER_CONV_ALL_CAMPAIGN;
    fea_prefix[3] = AD_AKG_USER_CONV_ALL_UNIT;
    fea_prefix[4] = AD_AKG_USER_CONV_ALL_PRODUCT_NAME;
    fea_prefix[5] = AD_AKG_USER_CONV_ALL_INDUSTRY_CLUSTER;
    fea_prefix[6] = AD_AKG_USER_CONV_ALL_INDUSTRY_FIRST;
    fea_prefix[7] = AD_AKG_USER_CONV_ALL_INDUSTRY_SECOND;
    fea_prefix[8] = AD_AKG_USER_LPS_ALL_PHOTO;
    fea_prefix[9] = AD_AKG_USER_LPS_ALL_ACCOUNT;
    fea_prefix[10] = AD_AKG_USER_LPS_ALL_CAMPAIGN;
    fea_prefix[11] = AD_AKG_USER_LPS_ALL_UNIT;
    fea_prefix[12] = AD_AKG_USER_LPS_ALL_PRODUCT_NAME;
    fea_prefix[13] = AD_AKG_USER_LPS_ALL_INDUSTRY_CLUSTER;
    fea_prefix[14] = AD_AKG_USER_LPS_ALL_INDUSTRY_FIRST;
    fea_prefix[15] = AD_AKG_USER_LPS_ALL_INDUSTRY_SECOND;
    fea_prefix[16] = AD_AKG_USER_HWEIGHT_OUTER_USER;
    fea_prefix[17] = AD_AKG_USER_HWEIGHT_OUTER_PHOTO;
    fea_prefix[18] = AD_AKG_USER_HWEIGHT_OUTER_ACCOUNT;
    fea_prefix[19] = AD_AKG_USER_HWEIGHT_OUTER_CAMPAIGN;
    fea_prefix[20] = AD_AKG_USER_HWEIGHT_OUTER_UNIT;
    fea_prefix[21] = AD_AKG_USER_HWEIGHT_OUTER_PRODUCT_NAME;
    fea_prefix[22] = AD_AKG_USER_HWEIGHT_OUTER_INDUSTRY_CLUSTER;
    fea_prefix[23] = AD_AKG_USER_HWEIGHT_OUTER_INDUSTRY_FIRST;
    fea_prefix[24] = AD_AKG_USER_HWEIGHT_OUTER_INDUSTRY_SECOND;
    fea_prefix[25] = AD_AKG_USER_HWEIGHT_WITH_OUTER_USER;
    fea_prefix[26] = AD_AKG_USER_HWEIGHT_WITH_OUTER_PHOTO;
    fea_prefix[27] = AD_AKG_USER_HWEIGHT_WITH_OUTER_ACCOUNT;
    fea_prefix[28] = AD_AKG_USER_HWEIGHT_WITH_OUTER_CAMPAIGN;
    fea_prefix[29] = AD_AKG_USER_HWEIGHT_WITH_OUTER_UNIT;
    fea_prefix[30] = AD_AKG_USER_HWEIGHT_WITH_OUTER_PRODUCT_NAME;
    fea_prefix[31] = AD_AKG_USER_HWEIGHT_WITH_OUTER_INDUSTRY_CLUSTER;
    fea_prefix[32] = AD_AKG_USER_HWEIGHT_WITH_OUTER_INDUSTRY_FIRST;
    fea_prefix[33] = AD_AKG_USER_HWEIGHT_WITH_OUTER_INDUSTRY_SECOND;
    fea_prefix[34] = AD_AKG_USER_HWEIGHT_ALL_ACCOUNT;
    fea_prefix[35] = AD_AKG_USER_HWEIGHT_ALL_CAMPAIGN;
    fea_prefix[36] = AD_AKG_USER_HWEIGHT_ALL_UNIT;
    fea_prefix[37] = AD_AKG_USER_HWEIGHT_ALL_PRODUCT_NAME;
    fea_prefix[38] = AD_AKG_USER_HWEIGHT_ALL_INDUSTRY_CLUSTER;
    fea_prefix[39] = AD_AKG_USER_HWEIGHT_ALL_INDUSTRY_FIRST;
    fea_prefix[40] = AD_AKG_USER_HWEIGHT_ALL_INDUSTRY_SECOND;
    fea_prefix[41] = AD_AKG_USER_OCPC_ACTION_TYPE;
    fea_prefix[42] = AD_AKG_USER_MULTI_RETRIVEL_TAG;
    fea_prefix[43] = AD_AKG_USER_HWEIGHT_REC;
    int count = 0;
    for (int i = 0; i < attr.size(); ++i) {
      if (count / 20 > 43) {
        break;
      }
      uint64_t id;
      int32_t hash_value = attr.Get(i);
      if (attr.Get(i) < 0) {
        AddFeature(GetFeature(fea_prefix[count / 20], hash_value), value, result);
      } else {
        AddFeature(GetFeature(fea_prefix[count / 20], attr.Get(i)), value, result);
      }
      count++;
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdAkgUserHweightRec);
};

REGISTER_BS_EXTRACTOR(BSExtractAdAkgUserHweightRec);

}  // namespace ad_algorithm
}  // namespace ks
