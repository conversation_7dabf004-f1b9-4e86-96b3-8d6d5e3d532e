#pragma once
#include <math.h>
#include <time.h>

#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_feature.pb.h"
using ::bs::kuaishou::ad::AdCallbackLog;
namespace ks {
namespace ad_algorithm {
class BSExtractCallbackEventNew : public BSFastFeature {
 public:
  BSExtractCallbackEventNew() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_callback_event);
    attr_metas_.emplace_back(BSFieldEnum::adlog_is_train);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_get_ad_callback_event);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    if (item_type != bs::ItemType::AD_DSP && item_type != bs::ItemType::NATIVE_AD) return;
    ::bs::kuaishou::ad::AdCallbackLog::EventType callback_event_ =
        ::bs::kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN;

    bool is_train = BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_is_train, pos);
    if (is_train) {
      bool has_call_back_type = BSFieldHelper::GetSingular<bool>(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_exists, pos);
      auto key_enum = BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_callback_event;
      BSRepeatedField<uint64_t> id_list(*bs, key_enum, pos);
      if (has_call_back_type && id_list.size() > 0) {
        auto callback_event_ = id_list.Get(0);
      }
    } else {
      auto key_enum = BSFieldEnum::adlog_get_ad_callback_event;
      BSRepeatedField<uint64_t> id_list(*bs, key_enum, pos);
      auto callback_event_ = id_list.Get(pos);
    }
    AddFeature(static_cast<uint64_t>(callback_event_), 1.0f, result);
  }

 private:
  const std::string USED_FEATURES[1] = {"item.ad_dsp_info.call_back_type.callback_event"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractCallbackEventNew);
};
REGISTER_BS_EXTRACTOR(BSExtractCallbackEventNew);
}  // namespace ad_algorithm
}  // namespace ks
