#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_statis_impression.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdStatisImpression::BSExtractAdStatisImpression() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_value);
  double bounds[7] = {0, 1000, 10000, 100000, 1000000, 10000000, 50000000};
  int steps[6] = {25, 200, 1000, 5000, 100000, 10000000};
  for (int i = 0; i <= 5; i++) {
      int step = steps[i];
      for (double j = bounds[i]; j < bounds[i+1]; j += step) {
          bucketBound.push_back(j);
      }
  }
  bucketBound.push_back(bounds[6]);
}

void BSExtractAdStatisImpression::Extract(const BSLog& bslog,
                                          size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
  if (has_ad_dsp_info) {
    auto key_column_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_key;
    auto value_column_id = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_605_value;
    BSMapField<absl::string_view, int64_t> item_statis_count(*bs, key_column_id, value_column_id, pos);
    if (item_statis_count.size() == 0) {
      return;
    }

    auto iter = item_statis_count.Get("impression");
    int impression_bucket = (iter.second) ? getBucketValue(iter.first, 50000000) : 0;
    AddFeature(GetFeature(FeaturePrefix::PHOTO_AD_STATIS_IMPRESSION,
                          (uint64_t)impression_bucket), 1.0, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
