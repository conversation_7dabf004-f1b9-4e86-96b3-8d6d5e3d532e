#pragma once
#include <string>
#include <vector>

#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {
class BSExtractAdCreateTime : public BSFastFeature {
 public:
  BSExtractAdCreateTime();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdCreateTime);
};
REGISTER_BS_EXTRACTOR(BSExtractAdCreateTime);
}  // namespace ad_algorithm
}  // namespace ks
