#pragma once
#include <string>
#include <unordered_map>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_common_info_attr_feature.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_repeated_field.h"

namespace ks {
namespace ad_algorithm {

class BSExtractMerchantAuthorLiveTrolleyStat : public BSCommonInfoAttrFeature {
 public:
  BSExtractMerchantAuthorLiveTrolleyStat()
      : BSCommonInfoAttrFeature(FeatureType::ITEM,
                                ::bs::kuaishou::ad::CommonInfoAttr_Name_AUTHOR_LAST_30D_TROLLEY_STAT,
                                GetPrefix(0)) {
    value_bits_ = 22;
    value_mask_ = (1LL << value_bits_) - 1;
  }

  inline uint64_t get_sign(uint64_t key, uint64_t value) {
    return (key << value_bits_) | (value & value_mask_);
  }

  inline int GetValue(float value, float scale, bool logarithm) {
    value = logarithm ? log(value + 1) : value;
    return (int)(value * scale);
  }

  void ExtractCommonAttr(const ad_nn::SampleInterface& sample, const std::vector<int32_t>& attr_ids,
                         size_t pos, std::vector<ExtractResult>* result) override {
    if (attr_ids.size() != 1) {
      return;
    }
    ad_nn::BSRepeatedField<float> values(sample, attr_ids[0], pos);
    if (values.size() != 3) {
      return;
    }
    auto live_show_trolley_cnt = values[0];
    auto live_click_trolley_cnt = values[1];
    auto live_trolley_ctr = values[2];
    AddFeature(get_sign(0, GetValue(live_show_trolley_cnt, 100, true)), 1.0, result);
    AddFeature(get_sign(1, GetValue(live_click_trolley_cnt, 100, true)), 1.0, result);
    AddFeature(get_sign(2, GetValue(live_trolley_ctr, 200, false)), 1.0, result);
  }

  bool InitInternal(const base::Json& config) override { return true; }

  bool CheckCommonAttr(const ad_nn::SampleInterface& sample, const std::vector<int32_t>& attr_ids, size_t pos,
                       const std::vector<ExtractResult>& result) override {
    return true;
  }

 private:
  int value_bits_;
  uint64_t value_mask_;

  DISALLOW_COPY_AND_ASSIGN(BSExtractMerchantAuthorLiveTrolleyStat);
};

REGISTER_BS_EXTRACTOR(BSExtractMerchantAuthorLiveTrolleyStat);

}  // namespace ad_algorithm
}  // namespace ks
