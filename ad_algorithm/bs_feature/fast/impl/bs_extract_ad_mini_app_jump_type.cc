#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_mini_app_jump_type.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdMiniAppJumpType::BSExtractAdMiniAppJumpType() : BSFastFeature(FeatureType::COMBINE) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_70_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_70_value);
}

void BSExtractAdMiniAppJumpType::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  uint64 item_id = BSFieldHelper::GetSingular<uint64_t>(*bs, BSFieldEnum::adlog_item_id, pos);
  bool is_direct_jump = false;
  uint64 index = 0;
  bool has_value = BSFieldHelper::HasSingular<int64_t, true>(*bs, BSFieldEnum::adlog_context_exists, pos);
  if (has_value) {
    auto key_enum = BSFieldEnum::adlog_context_info_common_attr_key_70_key;
    auto value_enum = BSFieldEnum::adlog_context_info_common_attr_key_70_value;
    BSMapField<int64_t, bool, true> direct_call_map(*bs, key_enum, value_enum, pos);
    auto iter = direct_call_map.Get(item_id);
    if (iter.second) {
      is_direct_jump = iter.second;
    }
  }
  double value = 1.0;
  if (is_direct_jump) {
    index = 1;
  }
  AddFeature(index, value, result);
}
}  // namespace ad_algorithm
}  // namespace ks
