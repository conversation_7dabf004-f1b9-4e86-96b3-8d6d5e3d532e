#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_ad_new_industry.h"

namespace ks {
namespace ad_algorithm {
BSExtractBrandAdNewIndustry::BSExtractBrandAdNewIndustry() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id);
}

void BSExtractBrandAdNewIndustry::Extract(
          const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  int item_type =
      BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  bool has_ad_dsp_info =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
  bool has_creative =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_exists, pos);
  bool has_base =
      BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD ||
         item_type == bs::ItemType::AD_BRAND) && has_ad_dsp_info && has_creative && has_base) {
    uint64_t new_industry_id = BSFieldHelper::GetSingular<uint64_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_new_industry_id, pos);
    AddFeature(GetFeature(FeaturePrefix::PHOTO_CREATIVE_NEW_INDUSTRY_ID, new_industry_id), 1.0f, result);
  }
}

}  // namespace ad_algorithm
}  // namespace ks
