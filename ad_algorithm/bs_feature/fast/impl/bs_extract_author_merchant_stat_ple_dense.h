#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_dense_ple.dark
class BSExtractAuthorMerchantStatPleDense : public BSFastFeature {
 public:
  BSExtractAuthorMerchantStatPleDense();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorMerchantStatPleDense);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorMerchantStatPleDense);
}  // namespace ad_algorithm
}  // namespace ks
