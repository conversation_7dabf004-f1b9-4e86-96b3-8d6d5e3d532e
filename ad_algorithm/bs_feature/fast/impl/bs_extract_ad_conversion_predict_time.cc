#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_conversion_predict_time.h"

#include <string>
#include <vector>

#include "base/strings/string_split.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdConversionPredictTime::BSExtractAdConversionPredictTime()
    : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_label_info_label_infos_key_20000073_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_label_info_label_infos_key_20000073_value);
}

void BSExtractAdConversionPredictTime::Extract(const BSLog &bslog, size_t pos,
                                               std::vector<ExtractResult> *result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  uint64_t EVENT_CONVERSION_ID = 10000001;
  uint64_t EVENT_APPOINT_FORM_ID = 10000078;
  uint64_t EVENT_APPOINT_JUMP_CLICK_ID = 10000079;

  BSMapField<int64_t, absl::string_view> attr_map(
      *bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000073_key,
      BSFieldEnum::adlog_item_label_info_label_infos_key_20000073_value, pos);

  if (!attr_map.is_empty()) {
    std::string predict_time_str = "";
    auto last_call_back_label = LONG_MAX;
    for (int i = 0; i < attr_map.size(); ++i) {
      const auto &call_back_label = attr_map.GetKey(i);
      if (call_back_label == EVENT_CONVERSION_ID || call_back_label == EVENT_APPOINT_FORM_ID ||
          call_back_label == EVENT_APPOINT_JUMP_CLICK_ID) {
        if (call_back_label < last_call_back_label) {
          last_call_back_label = call_back_label;
          predict_time_str = std::string(attr_map.GetValue(i));
        }
      }
    }
    if (predict_time_str != "") {
      std::vector<std::string> time_list;
      base::SplitString(predict_time_str, std::string("#"), &time_list);
      if (time_list.size() != 9) {
        LOG_EVERY_N(ERROR, 1000) << "predict_time_str format error:" << predict_time_str;
        return;
      }
      for (int i = 0; i < time_list.size(); i++) {
        auto predcit_time = std::atol(time_list[i].c_str());
        AddFeature(i, predcit_time, result);
      }
    }
  }
}

}  // namespace ad_algorithm
}  // namespace ks
