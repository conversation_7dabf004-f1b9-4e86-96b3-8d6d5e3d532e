#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_recall_target_info_v2.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdRecallTargetInfoV2::BSExtractAdRecallTargetInfoV2() : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_raw_target_info_age_range);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_raw_target_info_gender);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_raw_target_info_platform);
}

void BSExtractAdRecallTargetInfoV2::Extract(const BSLog& bslog, size_t pos,
                                            std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  if (item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) {
    int target_arr[16];
    for (int k = 0; k <= 15; k++) {
      target_arr[k] = 0;  // init
    }
    int age_range_arr[10] = {0, 12, 18, 24, 31, 36, 41, 50, 61, 101};
    auto get_age = [&](const auto& age) {
      for (int m = 0; m < 9; m++) {
        if (age >= age_range_arr[m] && age < age_range_arr[m + 1]) {
          return m;
        }
      }
      return -1;
    };

    BSRepeatedField<int32_t> age_range(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_raw_target_info_age_range, pos);
    if (age_range.size() == 0) {
      for (int age_k = 6; age_k <= 15; age_k++) {
        target_arr[age_k] = 1;
      }
    } else {
      for (int age_i = 0; age_i < age_range.size() - 1; age_i += 2) {
        int age_min = get_age(age_range.Get(age_i));
        int age_max = get_age(age_range.Get(age_i + 1));
        if (age_min >= 0) {
          for (int m_n = age_min + 6; m_n <= age_max + 6; m_n++) {
            target_arr[m_n] = 1;
          }
        }
      }
    }

    BSRepeatedField<absl::string_view> gender(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_raw_target_info_gender, pos);
    if (gender.size() == 0) {
      target_arr[0] = 1;
      target_arr[1] = 1;
      target_arr[2] = 1;
    } else {
      for (int gender_i = 0; gender_i < gender.size(); gender_i++) {
        if (gender.Get(gender_i) == "M") {
          target_arr[0] = 1;
        } else if (gender.Get(gender_i) == "F") {
          target_arr[1] = 1;
        }
      }
    }

    BSRepeatedField<absl::string_view> platform(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_raw_target_info_platform, pos);
    if (platform.size() == 0) {  // 通投广告
      target_arr[3] = 1;
      target_arr[4] = 1;
      target_arr[5] = 1;
    } else {
      for (int platform_i = 0; platform_i < platform.size(); platform_i++) {
        if (platform.Get(platform_i) == "ios") {
          target_arr[3] = 1;
        } else if (platform.Get(platform_i) == "android") {
          target_arr[4] = 1;
        }
      }
    }

    if (target_arr[3] == 1 && target_arr[4] == 1) {  // 通投广告
      target_arr[3] = 1;
      target_arr[4] = 1;
      target_arr[5] = 1;
    } else if (target_arr[3] == 1) {  // 定投 ios 广告
      target_arr[3] = 2;
    } else if (target_arr[4] == 1) {  // 定投安卓广告
      target_arr[4] = 2;
    }

    target_arr[2] = 1;
    target_arr[5] = 1;
    target_arr[15] = 1;

    for (int k = 0; k < 16; k++) {
      if (target_arr[k] == 1) {
        AddFeature(k, 1, result);
      } else if (target_arr[k] == 2) {
        AddFeature(k, 2, result);
      } else {
        AddFeature(k, 0, result);
      }
    }
  }
}

}  // namespace ad_algorithm
}  // namespace ks
