#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_info_4_label_new.h"

namespace ks {
namespace ad_algorithm {

BSExtractAuthorInfo4LabelNew::BSExtractAuthorInfo4LabelNew()
    : BSFastFeatureNoPrefix(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82001);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82002);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82003);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82004);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82005);
}

void BSExtractAuthorInfo4LabelNew::Extract(const BSLog& bslog, size_t pos,
                                           std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  float author_price_1d = 0.0;
  float author_price_3d = 0.0;
  float author_price_14d = 0.0;
  int64 gmv_total = 0;
  int64 pay_cnt_total = 0;
  author_price_1d = BSFieldHelper::GetSingular<double>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82001, pos);
  author_price_3d = BSFieldHelper::GetSingular<double>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82002, pos);
  author_price_14d = BSFieldHelper::GetSingular<double>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82003, pos);
  gmv_total = BSFieldHelper::GetSingular<int64>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82004, pos);
  pay_cnt_total = BSFieldHelper::GetSingular<int64>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82005, pos);
  float res = 0.0;
  if (pay_cnt_total > 20 && gmv_total > 0) {
    res = gmv_total * 0.01 / pay_cnt_total;
  } else if (author_price_1d > 0.0) {
    res = author_price_1d;
  } else if (author_price_3d > 0.0) {
    res = author_price_3d;
  } else if (author_price_14d > 0.0) {
    res = author_price_14d;
  } else {
    res = 50.0;
  }
  AddFeature(0, res, result);
  return;
}

}  // namespace ad_algorithm
}  // namespace ks
