#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_adsim_photo_info.h"

#include <string>

namespace ks {
namespace ad_algorithm {

BSExtractAdsimPhotoInfo::BSExtractAdsimPhotoInfo() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_photo_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_account_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_author_info_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  bs_util.BSHasPhotoInfoAuthorInfo.fill_attr_metas(&attr_metas_);
  bs_util.BSGetPhotoInfoAuthorInfoId.fill_attr_metas(&attr_metas_);
}

void BSExtractAdsimPhotoInfo::Extract(const BSLog &bslog, size_t pos, std::vector<ExtractResult> *result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
  uint64_t item_type = BSFieldHelper::GetSingular<uint64_t>(*bs, BSFieldEnum::adlog_item_type, pos);
  if ((item_type != bs::ItemType::AD_DSP && item_type != bs::ItemType::NATIVE_AD) || !has_ad_dsp_info) {
    return;
  }

  auto key_id_v3 = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3;
  uint64_t industry_id_v3 = BSFieldHelper::GetSingular<uint64_t>(*bs, key_id_v3, pos);

  uint64_t ad_product_name_id = UINT64_MAX;
  auto key_advertiser_exists = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_exists;
  bool has_advertiser = BSFieldHelper::GetSingular<bool>(*bs, key_advertiser_exists, pos);
  if (has_advertiser) {
    auto key_product_name = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name;
    auto product_name = BSFieldHelper::GetSingular<absl::string_view>(*bs, key_product_name, pos);
    ad_product_name_id = ks::ad_nn::bs::Hash(product_name);
  }

  uint64_t ad_photo_id = BSFieldHelper::GetSingular<uint64_t>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_photo_id, pos);
  uint64_t ad_account_id = BSFieldHelper::GetSingular<uint64_t>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_account_id, pos);

  bool has_author_id = false;
  uint64_t ad_author_id = UINT64_MAX;
  bool has_author_info = bs_util.BSHasPhotoInfoAuthorInfo(bs, pos);
  if (has_author_info) {
    ad_author_id = bs_util.BSGetPhotoInfoAuthorInfoId(bs, pos);
  }
  AddFeature(GetFeature(FeaturePrefix::AD_ADSIM_PHOTO_INFO_PHOTO_ID, ad_photo_id), 1.0, result);
  AddFeature(GetFeature(FeaturePrefix::AD_ADSIM_PHOTO_INFO_PRODUCT_NAME, ad_product_name_id), 1.0, result);
  AddFeature(GetFeature(FeaturePrefix::AD_ADSIM_PHOTO_INFO_AUTHOR_ID, ad_author_id), 1.0, result);
  AddFeature(GetFeature(FeaturePrefix::AD_ADSIM_PHOTO_INFO_INDUSTRY_ID, industry_id_v3), 1.0, result);
  AddFeature(GetFeature(FeaturePrefix::AD_ADSIM_PHOTO_INFO_ACCOUNT_ID, ad_account_id), 1.0, result);
}

}  // namespace ad_algorithm
}  // namespace ks
