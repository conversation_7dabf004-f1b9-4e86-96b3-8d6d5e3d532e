#pragma once

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {
class BSExtractAdItemSize : public BSFastFeature {
 public:
  BSExtractAdItemSize();
  virtual void Extract(const BSLog &bslog, size_t pos, std::vector<ExtractResult> *result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdItemSize);
};

REGISTER_BS_EXTRACTOR(BSExtractAdItemSize);

}  // namespace ad_algorithm
}  // namespace ks
