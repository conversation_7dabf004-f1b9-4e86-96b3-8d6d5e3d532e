/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_account_dense_cid_offsite_order_cate_1_id_180d.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

BSExtractAccountDenseCidOffsiteOrderCate1Id180d::BSExtractAccountDenseCidOffsiteOrderCate1Id180d()
    : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5102957);
}
void BSExtractAccountDenseCidOffsiteOrderCate1Id180d::Extract(const BSLog& bslog, size_t pos,
                                                              std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_float_list(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5102957, pos);
  add_feature_result(x1, 116, result);
}

}  // namespace ad_algorithm
}  // namespace ks
