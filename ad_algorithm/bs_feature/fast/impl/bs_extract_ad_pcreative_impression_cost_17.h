#pragma once
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdPcreativeImpressionCost17 : public BSFastFeature {
 public:
  BSExtractAdPcreativeImpressionCost17() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_230);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_231);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
    uint64_t item_type = BSFieldHelper::GetSingular<uint64_t>(*bs, BSFieldEnum::adlog_item_type, pos);
    if ((item_type != bs::ItemType::AD_DSP && item_type != bs::ItemType::NATIVE_AD) || !has_ad_dsp_info) {
      return;
    }
    auto key_impression_list = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_230;
    BSRepeatedField<int64_t> impression_list(*bs, key_impression_list, pos);
    if (impression_list.size() != 7) {
      return;
    }
    AddFeature(GetFeature(FeaturePrefix::PCREATIVE_CREATIVE_IMPRESSION, impression_list.Get(6)), 1.0f,
               result);
    auto key_cost_list = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_231;
    BSRepeatedField<int64_t> cost_list(*bs, key_cost_list, pos);
    if (cost_list.size() != 7) {
      return;
    }
    AddFeature(GetFeature(FeaturePrefix::PCREATIVE_CREATIVE_COST, cost_list.Get(6)), 1.0f, result);
  }

 private:
  const std::string USED_FEATURES[1] = {
      "item.ad_dsp_info.common_info_attr.PCREATIVE_SELECT_CREATIVE_IMPRESSION"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdPcreativeImpressionCost17);
};

REGISTER_BS_EXTRACTOR(BSExtractAdPcreativeImpressionCost17);

}  // namespace ad_algorithm
}  // namespace ks
