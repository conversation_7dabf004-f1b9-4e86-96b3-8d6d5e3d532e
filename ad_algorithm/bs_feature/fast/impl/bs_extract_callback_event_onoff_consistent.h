#pragma once
#include <math.h>
#include <time.h>

#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

using ::bs::kuaishou::ad::AdCallbackLog;
namespace ks {
namespace ad_algorithm {
class BSExtractCallbackEventOnOffConsistent : public BSFastFeature {
 public:
  BSExtractCallbackEventOnOffConsistent();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCallbackEventOnOffConsistent);
};
REGISTER_BS_EXTRACTOR(BSExtractCallbackEventOnOffConsistent);
}  // namespace ad_algorithm
}  // namespace ks
