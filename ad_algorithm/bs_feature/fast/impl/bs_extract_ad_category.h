#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdCategory : public BSFastFeature {
 public:
  BSExtractAdCategory() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
    auto key_creative_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_exists;
    bool has_creative = BSFieldHelper::GetSingular<bool>(*bs, key_creative_exists, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD ||
         item_type == bs::ItemType::AD_BRAND) &&
        has_ad_dsp_info && has_creative) {
      auto key_base_type = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type;
      int64_t base = BSFieldHelper::GetSingular<int64_t>(*bs, key_base_type, pos);
      AddFeature(GetFeature(FeaturePrefix::PHOTO_USER_AD_TYPE, base), 1.0f, result);
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdCategory);
};

REGISTER_BS_EXTRACTOR(BSExtractAdCategory);

}  // namespace ad_algorithm
}  // namespace ks
