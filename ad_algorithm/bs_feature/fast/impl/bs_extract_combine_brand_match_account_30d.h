#pragma once
#include <vector>
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractCombineBrandMatchAccount30D : public BSFastFeature {
 public:
  BSExtractCombineBrandMatchAccount30D();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  const int brand_specific_size = 6;  // account_id, imp, clk, lps, paied, dwl, 品牌特征列表固定大小
  const int account_offset = 0;
  const int imp_offset = 1;
  const int clk_offset = 2;
  const int lps_offset = 3;
  const int paied_offset = 4;
  const int dwl_offset = 5;
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineBrandMatchAccount30D);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineBrandMatchAccount30D);

}  // namespace ad_algorithm
}  // namespace ks
