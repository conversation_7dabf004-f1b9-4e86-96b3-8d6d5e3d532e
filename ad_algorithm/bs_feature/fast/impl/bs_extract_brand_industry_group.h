#pragma once
#include <string>
#include <vector>
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"
namespace ks {
namespace ad_algorithm {

class BSExtractBrandIndustryGroup : public BSFastFeatureNoPrefix {
 public:
  BSExtractBrandIndustryGroup();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandIndustryGroup);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandIndustryGroup);

}  // namespace ad_algorithm
}  // namespace ks
