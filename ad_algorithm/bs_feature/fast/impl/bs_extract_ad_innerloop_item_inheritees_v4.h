#pragma once
#include <string>
#include <vector>

#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

template <int feat_idx, int feat_type>
class BSExtractAdInnerloopItemInheriteesV4 : public BSFastFeature {
 public:
  BSExtractAdInnerloopItemInheriteesV4() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_66300);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_target_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_photo_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_dup_cover_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_name);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_display_info);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_create_time);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_66301);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_66302);
    bs_util.BSHasPhotoInfo.fill_attr_metas(&attr_metas_);
    bs_util.BSGetPhotoInfoId.fill_attr_metas(&attr_metas_);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    if (pos >= bslog.item_size()) {
      return;
    }

    int64 status = 0;
    uint64 inheritee_photo = 0;
    uint64 inheritee_creative = 0;
    auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);
    auto enum_photo_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists;
    bool photo_info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_photo_info_exists, pos);
    // 66300 label
    auto enum_key_66300 = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_66300;
    int64_t key_66300 = BSFieldHelper::GetSingular<int64_t>(*bs, enum_key_66300, pos);
    bool key_66300_exists = BSFieldHelper::HasSingular<int64_t>(*bs, enum_key_66300, pos);
    // 66301 photo
    auto enum_key_66301 = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_66301;
    int64_t key_66301 = BSFieldHelper::GetSingular<int64_t>(*bs, enum_key_66301, pos);
    bool key_66301_exists = BSFieldHelper::HasSingular<int64_t>(*bs, enum_key_66301, pos);
    // 66302 creative
    auto enum_key_66302 = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_66302;
    int64_t key_66302 = BSFieldHelper::GetSingular<int64_t>(*bs, enum_key_66302, pos);
    bool key_66302_exists = BSFieldHelper::HasSingular<int64_t>(*bs, enum_key_66302, pos);

    if (info_exists) {
      if (photo_info_exists && key_66300_exists) {
        status = key_66300;
      }
      if (key_66301_exists) {
        inheritee_photo = key_66301;
      }
      if (key_66302_exists) {
        inheritee_creative = key_66302;
      }
    }
    std::vector<uint64_t> feat_list;
    if (feat_idx == 0) {
      // ExtractPhotoId
      if (bs_util.BSHasPhotoInfo(bs, pos)) {
        bool has_value = false;
        uint64_t photo_id = bs_util.BSGetPhotoInfoId(bs, pos, &has_value);
        if (status + feat_type_ > 0 && inheritee_photo > 0) {
          photo_id = inheritee_photo;
        }
        feat_list.emplace_back(GetFeature(FeaturePrefix::PHOTO_ID, photo_id));
      }
    } else {
      int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
      bool has_creative =
          BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_exists, pos);
      if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && enum_info_exists &&
          has_creative) {
        if (feat_idx == 1) {
          // ExtractCreativeId
          auto creative_id = BSFieldHelper::GetSingular<uint64_t>(
              *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_id, pos);
          if (status + feat_type_ > 0 && inheritee_creative > 0) {
            creative_id = inheritee_creative;
          }
          feat_list.emplace_back(GetFeature(FeaturePrefix::CREATIVE_ID, creative_id));
        } else if (feat_idx == 2) {
          // ExtractUnitCampaignId
          uint64 unit_id =
              BSFieldHelper::GetSingular<uint64>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_id, pos);
          uint64 campaign_id = BSFieldHelper::GetSingular<uint64>(
              *bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_id, pos);
          feat_list.emplace_back(GetFeature(FeaturePrefix::UNIT_ID, unit_id + status * 1000));
          feat_list.emplace_back(GetFeature(FeaturePrefix::CAMPAIGN_ID, campaign_id + status * 1000));
        } else if (feat_idx == 3) {
          // ExtractCreativeSignature
          uint64 target_id = BSFieldHelper::GetSingular<uint64>(
              *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_target_id, pos);
          uint64 photo_id = BSFieldHelper::GetSingular<uint64>(
              *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_photo_id, pos);
          if (status + feat_type_ > 0 && inheritee_photo > 0) {
            photo_id = inheritee_photo;
          }
          uint64 dup_cover_id = BSFieldHelper::GetSingular<uint64>(
              *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_dup_cover_id, pos);
          auto name = BSFieldHelper::GetSingular<absl::string_view>(
              *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_name, pos);
          auto display_info = BSFieldHelper::GetSingular<absl::string_view>(
              *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_display_info, pos);
          feat_list.emplace_back(GetFeature(FeaturePrefix::PHOTO_TARGET_ID, target_id));
          feat_list.emplace_back(GetFeature(FeaturePrefix::PHOTO_ID, photo_id));
          feat_list.emplace_back(GetFeature(FeaturePrefix::PHOTO_DUP_COVER_ID, dup_cover_id));
          feat_list.emplace_back(GetFeature(FeaturePrefix::PHOTO_CREATIVE_NAME, ad_nn::bs::Hash(name)));
          feat_list.emplace_back(
              GetFeature(FeaturePrefix::PHOTO_CREATIVE_DISPLAY_INFO, ad_nn::bs::Hash(display_info)));
        } else if (feat_idx == 4) {
          // ExtractAdCreateTime
          auto enum_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists;
          bool base_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_base_exists, pos);
          if (base_exists) {
            auto enum_create_time = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_create_time;
            uint64_t create_time = BSFieldHelper::GetSingular<uint64_t>(*bs, enum_create_time, pos);
            uint64_t cur_time = base::GetTimestamp() / 1000;
            uint64_t hour = (cur_time - create_time) / 1000 / 60 / 60;
            uint64_t day = hour / 24;
            if (status == 0) {
              feat_list.emplace_back(GetFeature(FeaturePrefix::PHOTO_CREATE_TIME_HOUR, hour));
              feat_list.emplace_back(GetFeature(FeaturePrefix::PHOTO_CREATE_TIME_DAY, day));
            } else {
              feat_list.emplace_back(GetFeature(FeaturePrefix::PHOTO_CREATE_TIME_HOUR, 0));
              feat_list.emplace_back(GetFeature(FeaturePrefix::PHOTO_CREATE_TIME_DAY, 0));
            }
          }
        }
      }
    }
    for (auto feat : feat_list) {
      AddFeature(feat, 1.0f, result);
    }
  }

 private:
  int feat_idx_{feat_idx};
  int feat_type_{feat_type};

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdInnerloopItemInheriteesV4);
};

}  // namespace ad_algorithm
}  // namespace ks
