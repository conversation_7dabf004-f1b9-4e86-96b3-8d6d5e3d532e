#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_feature.pb.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdsimPhotoInfo : public BSFastFeature {
 public:
  BSExtractAdsimPhotoInfo();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdsimPhotoInfo);
};

REGISTER_BS_EXTRACTOR(BSExtractAdsimPhotoInfo);

}  // namespace ad_algorithm
}  // namespace ks
