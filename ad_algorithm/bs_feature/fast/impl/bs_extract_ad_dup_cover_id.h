#pragma once
#include <stdlib.h>
#include <time.h>

#include <iostream>
#include <string>
#include <vector>

#include "base/common/logging.h"
#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#define RANDMAX 100000

namespace ks {
namespace ad_algorithm {

class BSExtractAdDupCoverId : public BSFastFeature {
 public:
  BSExtractAdDupCoverId() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_common_info_attr_key_812);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);

    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);

    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD ||
         item_type == bs::ItemType::AD_BRAND) &&
        has_ad_dsp_info) {
      bool has_value = false;
      auto key_cover_id = BSFieldEnum::adlog_item_common_info_attr_key_812;
      auto ad_dup_cover_id = BSFieldHelper::GetSingular<uint64_t>(*bs, key_cover_id, pos, &has_value);
      if (has_value) {
        AddFeature(GetFeature(FeaturePrefix::PHOTO_DUP_COVER_ID, ad_dup_cover_id), 1.0f, result);
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdDupCoverId);
};

REGISTER_BS_EXTRACTOR(BSExtractAdDupCoverId);

}  // namespace ad_algorithm
}  // namespace ks
