/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_merchant_stat_ple_dense_v_2.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_dense_ple.h"
#include "teams/ad/ad_algorithm/fed/compute/merge_list.h"

namespace ks {
namespace ad_algorithm {

BSExtractAuthorMerchantStatPleDenseV2::BSExtractAuthorMerchantStatPleDenseV2()
    : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5100135);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5100134);
}
void BSExtractAuthorMerchantStatPleDenseV2::Extract(const BSLog& bslog, size_t pos,
                                                    std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 =
      get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5100135, pos);
  auto x3 = cast_to_float_with_default(x1, -1);
  const auto& x4 = get_a_item_cnt_bins();
  auto x5 = get_dense_ple_helper(x3, x4);
  auto x6 =
      get_bslog_float(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5100134, pos);
  auto x8 = cast_to_float_with_default(x6, -1);
  const auto& x9 = get_a_item_price_cv_bins();
  auto x10 = get_dense_ple_helper(x8, x9);
  auto x11 = merge_float_list_all(x5, x10);
  add_feature_result(x11, 12, result);
}

}  // namespace ad_algorithm
}  // namespace ks
