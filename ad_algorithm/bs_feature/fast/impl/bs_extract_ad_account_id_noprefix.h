#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdAccountIdNoPrefix : public BSFastFeatureNoPrefix {
 public:
  BSExtractAdAccountIdNoPrefix();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdAccountIdNoPrefix);
};

REGISTER_BS_EXTRACTOR(BSExtractAdAccountIdNoPrefix);

}  // namespace ad_algorithm
}  // namespace ks
