#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "ks/util/json.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdDeveloperName : public BSFastFeature {
 public:
  BSExtractAdDeveloperName();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdDeveloperName);
};

REGISTER_BS_EXTRACTOR(BSExtractAdDeveloperName);

}  // namespace ad_algorithm
}  // namespace ks
