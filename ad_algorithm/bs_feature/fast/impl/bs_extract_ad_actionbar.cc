#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_actionbar.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdActionbar::BSExtractAdActionbar() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_2055);
}

void BSExtractAdActionbar::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size;
  int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  bool has_value = false;
  auto enum_key_2055 = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_2055;
  int64_t key_2055 = BSFieldHelper::GetSingular<int64_t>(*bs, enum_key_2055, pos, &has_value);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && info_exists &&
      attr_size > 0) {
    if (has_value) {
      AddFeature(GetFeature(FeaturePrefix::PHOTO_ACTIONBAR, key_2055), 1.0f, result);
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
