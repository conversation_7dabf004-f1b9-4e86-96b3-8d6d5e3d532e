#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAuthorInfo4LabelNew : public BSFastFeatureNoPrefix {
 public:
  BSExtractAuthorInfo4LabelNew();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorInfo4LabelNew);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorInfo4LabelNew);

}  // namespace ad_algorithm
}  // namespace ks
