#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_add_cart_seller_id.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/realtime_ecom_action.h"

namespace ks {
namespace ad_algorithm {

BSExtractAddCartSellerId::BSExtractAddCartSellerId() : BSFastFeatureNoPrefix(FeatureType::USER) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_66147);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_66141);
  attr_metas_.emplace_back(BSFieldEnum::adlog_time);
}
void BSExtractAddCartSellerId::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64_list(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_66147, pos);
  auto x2 = get_bslog_int64_list(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_66141, pos);
  auto x4 = get_bslog_time(*bs);
  auto x7 = filter_action_by_time_diff_thresh(x1, x2, 100, x4, 3000, false);
  add_feature_result(x7, result);
}

}  // namespace ad_algorithm
}  // namespace ks
