#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

template <BSFieldEnum no>
class BSExtractAdEcomBrandFeature : public BSFastFeatureNoPrefix {
 public:
  BSExtractAdEcomBrandFeature() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);
    attr_metas_.emplace_back(no);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    bool has_dsp_photo_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists, pos);
    int32_t common_info_attr_size = BSFieldHelper::GetSingular<int32_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size, pos);
    if (item_type == bs::ItemType::AD_DSP && has_ad_dsp_info && has_dsp_photo_info &&
        common_info_attr_size > 0) {
      auto key_enum = no;
      BSRepeatedField<int> attr(*bs, key_enum, pos);
      double value = 1.0;
      const size_t list_size = attr.size();
      for (int i = 0; i < list_size; ++i) {
        auto id = attr.Get(i);
        AddFeature(id, value, result);
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdEcomBrandFeature);
};

using BSExtractAdEcomBrandId =
    BSExtractAdEcomBrandFeature<BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_90001>;
REGISTER_BS_EXTRACTOR(BSExtractAdEcomBrandId);

}  // namespace ad_algorithm
}  // namespace ks
