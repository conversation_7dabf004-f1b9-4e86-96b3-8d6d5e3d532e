#include <algorithm>
#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_brand_vision_feature_cover_ctr.h"

namespace ks {
namespace ad_algorithm {

BSExtractBrandVisionFeatureCoverCTR::BSExtractBrandVisionFeatureCoverCTR() : BSFastFeature(DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_3073);
}

void BSExtractBrandVisionFeatureCoverCTR::Extract(
  const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }
  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size;
  int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);
  if (info_exists && attr_size > 0 && item_type == bs::ItemType::AD_BRAND) {
    BSRepeatedField<float> embedding(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_3073, pos);
    if (embedding.size() != 32) return;
    for (size_t i = 0; i < embedding.size(); ++i) {
      AddFeature(i, std::min(embedding.Get(i), 10.0f), result);
    }
  } else if (attr_size > 0) {
    BSRepeatedField<float> embedding(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_3073, pos);
    if (embedding.size() != 32) return;
    for (size_t i = 0; i < embedding.size(); ++i) {
      AddFeature(i, std::min(embedding.Get(i), 10.0f), result);
    }
  }
}

}  // namespace ad_algorithm
}  // namespace ks
