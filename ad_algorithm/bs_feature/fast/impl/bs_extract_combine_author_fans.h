#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

class BSExtractCombineAuthorFans : public BSFastFeatureNoPrefix {
 public:
  BSExtractCombineAuthorFans() : BSFastFeatureNoPrefix(FeatureType::COMBINE) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_action_detail_follow_id);
    bs_util.BSHasAuthorInfo.fill_attr_metas(&attr_metas_);
    bs_util.BSGetAuthorInfoId.fill_attr_metas(&attr_metas_);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineAuthorFans);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineAuthorFans);

}  // namespace ad_algorithm
}  // namespace ks
