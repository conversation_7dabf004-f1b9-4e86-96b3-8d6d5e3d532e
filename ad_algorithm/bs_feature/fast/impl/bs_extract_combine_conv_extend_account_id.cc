/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_conv_extend_account_id.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/offline_user_seq_pooling_global_merge.h"

namespace ks {
namespace ad_algorithm {

BSExtractCombineConvExtendAccountId::BSExtractCombineConvExtendAccountId()
    : BSFastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5101995);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_5101997);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_5102003);
}
void BSExtractCombineConvExtendAccountId::Extract(const BSLog& bslog, size_t pos,
                                                  std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5101995, pos);
  auto x2 = get_bslog_int64_list<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_5101997, pos);
  auto x3 =
      get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_5102003, pos);
  auto x4 = get_user_seq_reverse_search(x1, x2, x3);
  add_feature_result(x4, result);
}

}  // namespace ad_algorithm
}  // namespace ks
