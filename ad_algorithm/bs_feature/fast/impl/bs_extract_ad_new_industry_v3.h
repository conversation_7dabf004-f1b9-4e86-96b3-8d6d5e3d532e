#pragma once
#include <cmath>
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdNewIndustryV3 : public BSFastFeature {
 public:
  BSExtractAdNewIndustryV3() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    bool has_value = false;
    int64_t new_industry_id = BSFieldHelper::GetSingular<int>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3, pos, &has_value);

    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_value) {
      AddFeature(GetFeature(FeaturePrefix::PHOTO_CREATIVE_NEW_INDUSTRY_ID, new_industry_id), 1.0f, result);
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"item.ad_dsp_info.creative.base.industry_id_v3"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdNewIndustryV3);
};

REGISTER_BS_EXTRACTOR(BSExtractAdNewIndustryV3);

}  // namespace ad_algorithm
}  // namespace ks
