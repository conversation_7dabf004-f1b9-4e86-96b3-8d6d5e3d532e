#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_item_dense_author_price.dark
class BSExtractAuthorPrice60 : public BSFastFeature {
 public:
  BSExtractAuthorPrice60();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorPrice60);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorPrice60);
}  // namespace ad_algorithm
}  // namespace ks
