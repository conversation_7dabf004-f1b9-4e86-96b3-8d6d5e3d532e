#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_akg_user_graph_emb.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdAkgUserGraphEmb::BSExtractAdAkgUserGraphEmb() : BSFastFeature(DENSE_USER) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_size);
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_common_info_attr_key_48000);
}

void BSExtractAdAkgUserGraphEmb::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_attr_size = BSFieldEnum::adlog_user_info_common_info_attr_size;
  int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size);

  auto enum_info_exists = BSFieldEnum::adlog_user_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists);

  auto enum_key_48000 = BSFieldEnum::adlog_user_info_common_info_attr_key_48000;
  BSRepeatedField<float> key_48000(*bs, enum_key_48000);

  if (info_exists && attr_size > 0) {
    if (!key_48000.is_empty()) {
      int count = 0;

      for (size_t idx = 0; idx < key_48000.size(); idx++) {
        auto akg_value = key_48000.Get(idx);
        if (count < 64) {
          AddFeature(count, akg_value, result);
        }
        count++;
      }
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
