#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_campaign_id.h"

namespace ks {
namespace ad_algorithm {
BSExtractAdCampaignId::BSExtractAdCampaignId() : BSFastFeature(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_id);
}

void BSExtractAdCampaignId::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

  auto enum_campaign_id = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_id;
  uint64_t campaign_id = BSFieldHelper::GetSingular<uint64_t>(*bs, enum_campaign_id, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && info_exists) {
    AddFeature(GetFeature(FeaturePrefix::CAMPAIGN_ID, campaign_id), 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
