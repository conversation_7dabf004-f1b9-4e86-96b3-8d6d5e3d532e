#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_account_dense_cid_offsite.dark
class BSExtractAccountDenseCidOffsiteOrderCate1Id180d : public BSFastFeature {
 public:
  BSExtractAccountDenseCidOffsiteOrderCate1Id180d();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAccountDenseCidOffsiteOrderCate1Id180d);
};

REGISTER_BS_EXTRACTOR(BSExtractAccountDenseCidOffsiteOrderCate1Id180d);
}  // namespace ad_algorithm
}  // namespace ks
