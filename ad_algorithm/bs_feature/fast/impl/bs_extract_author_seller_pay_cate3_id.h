#pragma once
#include <string>
#include <unordered_map>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_common_info_attr_feature.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_repeated_field.h"

namespace ks {
namespace ad_algorithm {

class BSExtractMerchantAuthorSoldCate3Id : public BSCommonInfoAttrFeature {
 public:
  BSExtractMerchantAuthorSoldCate3Id()
      : BSCommonInfoAttrFeature(FeatureType::ITEM,
                                ::bs::kuaishou::ad::CommonInfoAttr_Name_AUTHOR_LAST_30D_PAY_CATE_LEVEL3_LIST,
                                GetPrefix(0)) {
    value_bits_ = 32;
    value_mask_ = (1LL << value_bits_) - 1;
  }

  inline uint64_t get_sign(uint64_t key, uint64_t value) {
    return (key << value_bits_) | (value & value_mask_);
  }

  void ExtractCommonAttr(const ad_nn::SampleInterface& sample, const std::vector<int32_t>& attr_ids,
                         size_t pos, std::vector<ExtractResult>* result) override {
    if (attr_ids.size() != 1) {
      return;
    }
    ad_nn::BSRepeatedField<int64_t> values(sample, attr_ids[0], pos);
    for (int i = 0; i < values.size(); ++i) {
      AddFeature(get_sign(0, values[i]), 1.0, result);
    }
    AddFeature(get_sign(1, values.size()), 1.0, result);
  }

  bool InitInternal(const base::Json& config) override { return true; }

  bool CheckCommonAttr(const ad_nn::SampleInterface& sample, const std::vector<int32_t>& attr_ids, size_t pos,
                       const std::vector<ExtractResult>& result) override {
    return true;
  }

 private:
  int value_bits_;
  uint64_t value_mask_;

  DISALLOW_COPY_AND_ASSIGN(BSExtractMerchantAuthorSoldCate3Id);
};

REGISTER_BS_EXTRACTOR(BSExtractMerchantAuthorSoldCate3Id);

}  // namespace ad_algorithm
}  // namespace ks
