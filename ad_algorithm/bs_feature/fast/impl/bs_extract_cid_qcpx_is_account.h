#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_cid_qcpx_is_account.dark
class BSExtractCidQcpxIsAccount : public BSFastFeature {
 public:
  BSExtractCidQcpxIsAccount();
  void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractCidQcpxIsAccount);
};

REGISTER_BS_EXTRACTOR(BSExtractCidQcpxIsAccount);
}  // namespace ad_algorithm
}  // namespace ks
