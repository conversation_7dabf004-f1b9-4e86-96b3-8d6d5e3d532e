#pragma once
#include <math.h>
#include <time.h>

#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/time/timestamp.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_feature.pb.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
namespace ks {
namespace ad_algorithm {
class BSExtractCombineContextInfoDupPhoto : public BSFastFeature {
 public:
  BSExtractCombineContextInfoDupPhoto() : BSFastFeature(FeatureType::COMBINE, 16, 26, 10) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_age_segment);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_gender);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_platform);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_network);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_region);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_language);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_phone_price);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_consumption_level);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_has_house);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_is_work);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_dislike_ad);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_ad_user_info_live_days);
    attr_metas_.emplace_back(BSFieldEnum::adlog_time);
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_dup_photo_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_page_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_sub_page_id);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    auto item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    bool has_ad_dsp_info_creative =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_exists, pos);
    bool has_user_info =
        BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info &&
        has_ad_dsp_info_creative) {
      auto adlog_time = BSFieldHelper::GetSingular<int64_t, true>(*bs, BSFieldEnum::adlog_time, pos);
      char datehour[100];
      time_t tick = (time_t)(adlog_time / 1000);
      struct tm tm = {0};
      struct tm* tm1 = localtime_r(&tick, &tm);
      if (tm1 == NULL) {
        return;
      }
      strftime(datehour, sizeof(datehour), "%H", &tm);
      uint64_t dup_photo_id = BSFieldHelper::GetSingular<uint64>(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_dup_photo_id, pos);

      bool has_context = BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_context_exists, pos);
      if (has_context) {
        uint64 page_id =
            BSFieldHelper::GetSingular<uint64, true>(*bs, BSFieldEnum::adlog_context_page_id, pos);
        uint64 sub_page_id =
            BSFieldHelper::GetSingular<uint64, true>(*bs, BSFieldEnum::adlog_context_sub_page_id, pos);
        std::string context_info = std::to_string(page_id) + std::to_string(sub_page_id) + datehour;
        uint64_t id = GetFeature(FeaturePrefix::COMBINE_CONTEXT_INFO_DUP_PHOTO, ad_nn::bs::Hash(context_info),
                                 dup_photo_id, 0);
        AddFeature(id, 1.0, result);
      }
    }
  }

 private:
  const std::string USED_FEATURES[4] = {"item.ad_dsp_info.creative.base.dup_photo_id", "time",
                                        "context.sub_page_id", "context.pos_id"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineContextInfoDupPhoto);
};
REGISTER_BS_EXTRACTOR(BSExtractCombineContextInfoDupPhoto);
}  // namespace ad_algorithm
}  // namespace ks
