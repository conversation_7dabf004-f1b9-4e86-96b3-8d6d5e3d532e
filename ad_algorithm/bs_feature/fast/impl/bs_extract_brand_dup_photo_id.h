#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
namespace ks {
namespace ad_algorithm {

class BSExtractBrandDupPhotoId : public BSFastFeature {
 public:
  BSExtractBrandDupPhotoId() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_dup_photo_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);

    bool has_ad_dsp_info_creative =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_exists, pos);

    bool has_ad_dsp_info_creative_base =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists, pos);

    if (has_ad_dsp_info && has_ad_dsp_info_creative) {
      if (!has_ad_dsp_info_creative_base) {
        return;
      }
      uint64_t dup_photo_id = BSFieldHelper::GetSingular<uint64_t>(
          *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_dup_photo_id, pos);
      AddFeature(GetFeature(FeaturePrefix::PHOTO_DUP_PHOTO_ID, dup_photo_id), 1.0f, result);
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"item.ad_dsp_info.creative.base.dup_photo_id"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandDupPhotoId);
};

REGISTER_BS_EXTRACTOR(BSExtractBrandDupPhotoId);

}  // namespace ad_algorithm
}  // namespace ks
