#pragma once
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdPcreativeImpressionCost14 : public BSFastFeature {
 public:
  BSExtractAdPcreativeImpressionCost14() : BSFastFeature(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_230);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_231);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_ad_dsp_info =
        BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_ad_dsp_info_exists, pos);
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    size_t common_info_size = BSFieldHelper::GetSingular<size_t, true>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info &&
        common_info_size > 0) {
      auto key_impression_list = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_230;
      BSRepeatedField<int64_t> impression_list(*bs, key_impression_list, pos);
      if (impression_list.size() != 7) {
        return;
      }
      AddFeature(GetFeature(FeaturePrefix::PCREATIVE_CREATIVE_IMPRESSION, impression_list.Get(3)), 1.0f,
                 result);
      auto key_cost_list = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_231;
      BSRepeatedField<int64_t> cost_list(*bs, key_cost_list, pos);
      if (cost_list.size() != 7) {
        return;
      }
      AddFeature(GetFeature(FeaturePrefix::PCREATIVE_CREATIVE_COST, cost_list.Get(3)), 1.0f, result);
    }
  }

 private:
  const std::string USED_FEATURES[1] = {
      "item.ad_dsp_info.common_info_attr.PCREATIVE_SELECT_CREATIVE_IMPRESSION"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdPcreativeImpressionCost14);
};

REGISTER_BS_EXTRACTOR(BSExtractAdPcreativeImpressionCost14);

}  // namespace ad_algorithm
}  // namespace ks
