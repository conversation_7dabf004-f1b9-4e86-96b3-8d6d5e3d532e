#pragma once
#include <algorithm>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/strings/string_number_conversions.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {
template <GsuFieldType field, int max_list_len,
          BSFieldEnum no,  // name_value
          int tag,         // goods_cluster : 0, goods_cate : 1
          int start_bit = 0, int bits = 0>
class BSExtractAdMmuGoodsGsuFeatureNewV1 : public BSFastFeatureNoPrefix {
 public:
  BSExtractAdMmuGoodsGsuFeatureNewV1() : BSFastFeatureNoPrefix(FeatureType::COMBINE) { AddAttrMetas(); }
  explicit BSExtractAdMmuGoodsGsuFeatureNewV1(size_t index) : BSFastFeatureNoPrefix(FeatureType::COMBINE) {
    feature_index_ = index;
    AddAttrMetas();
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    bool has_user_info =
        BSFieldHelper::GetSingular<bool, true>(*bs, BSFieldEnum::adlog_user_info_exists, pos);
    if (!has_user_info) {
      return;
    }
    if (!bs_util.BSHasPhotoInfo(bs, pos)) {
      return;
    }

    int32_t common_info_attr_size = BSFieldHelper::GetSingular<int32_t>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size, pos);
    if (common_info_attr_size <= 0) {
      return;
    }
    auto p_items = bslog.colossus_reco_photo();
    if (p_items == nullptr) {
      return;
    }

    uint64_t user_id = BSFieldHelper::GetSingular<uint64_t, true>(*bs, BSFieldEnum::adlog_user_info_id, pos);
    uint64_t time = BSFieldHelper::GetSingular<uint64_t, true>(*bs, BSFieldEnum::adlog_time, pos) / 1000;

    int64_t target_item_attr = BSFieldHelper::GetSingular<int64_t>(*bs, no, pos);
    const auto& items = *(p_items);
    int64_t counter = 0;

    for (int i = items.size() - 1; i >= 0; --i) {
      auto& item = items[i];
      if (item.timestamp > time) {
        continue;
      }
      if ((*(reinterpret_cast<const uint32_t*>(&(item.user_longitude))) % 100) < THRESHOLD) {
        continue;
      }
      int id = -1;
      // 获取 id
      switch (tag) {
        case 0:
          id = *(reinterpret_cast<const uint32_t*>(&(item.user_longitude))) / 1000000;
          break;
        case 1:
          id = (*(reinterpret_cast<const uint32_t*>(&(item.user_longitude))) % 1000000) / 100;
          break;
      }
      if (target_item_attr == id) {
        int val = -1;
        switch (field) {
          case GsuFieldType::PHOTO_ID:
            val = item.photo_id;
            break;
          case GsuFieldType::AUTHOR_ID:
            val = item.author_id;
            break;
          case GsuFieldType::TAG:
            val = item.tag;
            break;
          case GsuFieldType::GOODS_CLUSTER:
            val = *(reinterpret_cast<const uint32_t*>(&(item.user_longitude))) / 1000000;
            break;
          case GsuFieldType::GOODS_CATE:
            val = (*(reinterpret_cast<const uint32_t*>(&(item.user_longitude))) % 1000000) / 100;
            break;
        }
        AddFeature(val, 1.0f, result);
        counter++;
        if (counter >= max_list_len) {
          break;
        }
      }
    }
  }

 private:
  size_t feature_index_;
  const int THRESHOLD = 47;
  const std::string USED_FEATURES[1] = {"time"};
  void AddAttrMetas() {
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_time);
    attr_metas_.emplace_back(no);
    bs_util.BSHasPhotoInfo.fill_attr_metas(&attr_metas_);
  }
};

using BSExtractAdMmuGoodsGsuFeatureNewPhotoIdCateV1 = BSExtractAdMmuGoodsGsuFeatureNewV1<
    GsuFieldType::PHOTO_ID, 100, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25223,
    1>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewPhotoIdCateV1);

using BSExtractAdMmuGoodsGsuFeatureNewAuthorIdCateV1 = BSExtractAdMmuGoodsGsuFeatureNewV1<
    GsuFieldType::AUTHOR_ID, 100, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25223,
    1>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewAuthorIdCateV1);

using BSExtractAdMmuGoodsGsuFeatureNewTagCateV1 = BSExtractAdMmuGoodsGsuFeatureNewV1<
    GsuFieldType::TAG, 100, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25223, 1>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewTagCateV1);

using BSExtractAdMmuGoodsGsuFeatureNewClusterCateV1 = BSExtractAdMmuGoodsGsuFeatureNewV1<
    GsuFieldType::GOODS_CLUSTER, 100,
    BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25223, 1>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewClusterCateV1);

using BSExtractAdMmuGoodsGsuFeatureNewPhotoIdClusterV1 = BSExtractAdMmuGoodsGsuFeatureNewV1<
    GsuFieldType::PHOTO_ID, 100, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25225,
    0>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewPhotoIdClusterV1);

using BSExtractAdMmuGoodsGsuFeatureNewAuthorIdClusterV1 = BSExtractAdMmuGoodsGsuFeatureNewV1<
    GsuFieldType::AUTHOR_ID, 100, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25225,
    0>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewAuthorIdClusterV1);

using BSExtractAdMmuGoodsGsuFeatureNewTagClusterV1 = BSExtractAdMmuGoodsGsuFeatureNewV1<
    GsuFieldType::TAG, 100, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25225, 0>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewTagClusterV1);

using BSExtractAdMmuGoodsGsuFeatureNewCateClusterV1 = BSExtractAdMmuGoodsGsuFeatureNewV1<
    GsuFieldType::GOODS_CATE, 100, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_key_25225,
    0>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsGsuFeatureNewCateClusterV1);

}  // namespace ad_algorithm
}  // namespace ks
