#pragma once
#include <math.h>

#include <string>
#include <vector>

#include "base/hash_function/city.h"
#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractCombineAdAppIdMedia : public BSFastFeature {
 public:
  BSExtractCombineAdAppIdMedia() : BSFastFeature(FeatureType::COMBINE, 26, 26) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_app_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_unit_base_app_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  }

  virtual void Extract(const BSLog &bslog, size_t pos, std::vector<ExtractResult> *result) {
    auto bs = bslog.GetBS();
    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);

    if (bs == nullptr || (item_type != bs::ItemType::AD_DSP && item_type != bs::ItemType::NATIVE_AD) ||
        !has_ad_dsp_info) {
      return;
    }

    auto key_context_exists = BSFieldEnum::adlog_context_exists;
    bool has_context = BSFieldHelper::GetSingular<bool, true>(*bs, key_context_exists, pos);

    auto key_unit_exists = BSFieldEnum::adlog_item_ad_dsp_info_unit_exists;
    bool has_unit = BSFieldHelper::GetSingular<bool>(*bs, key_unit_exists, pos);
    auto key_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists;
    bool has_base = BSFieldHelper::GetSingular<bool>(*bs, key_base_exists, pos);

    if (has_context) {
      absl::string_view app_id_str =
          BSFieldHelper::GetSingular<absl::string_view, true>(*bs, BSFieldEnum::adlog_context_app_id, pos);
      int64 app_id = ad_nn::bs::Hash(app_id_str);
      if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info &&
          has_unit && has_base) {
        uint64 app = BSFieldHelper::GetSingular<uint64_t>(
            *bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_app_id, pos);
        AddFeature(GetFeature(FeaturePrefix::COMBINE_AD_APP_ID_MEDIA, app_id, app), 1.0, result);
      }
    }
  }

 private:
  const std::string USED_FEATURES[2] = {"context.app_id", "item.ad_dsp_info.unit.base.app_id"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineAdAppIdMedia);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineAdAppIdMedia);

}  // namespace ad_algorithm
}  // namespace ks
