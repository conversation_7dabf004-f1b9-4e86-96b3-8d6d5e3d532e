#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_info.h"

#include <cmath>

namespace ks {
namespace ad_algorithm {

void BSExtractAuthorInfo::Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  if (!bs_util.BSHasAuthorInfo(bs, pos)) {
    return;
  }

  int64_t author_id = bs_util.BSGetAuthorInfoId(bs, pos);
  AddFeature(GetFeature(FeaturePrefix::PHOTO_AUTHOR_ID, author_id), 1.0f, result);
  if (!bs_util.BSHasAuthorInfoAttribute(bs, pos)) {
    return;
  }
  uint32_t fans_count = bs_util.BSGetAuthorInfoAttributeCountFans(bs, pos);
  AddFeature(GetFeature(FeaturePrefix::PHOTO_AUTHOR_FANS_COUNT, static_cast<int>(log(fans_count + 1))), 1.0f,
             result);
  uint32_t gender = bs_util.BSGetAuthorInfoAttributeGender(bs, pos);
  AddFeature(GetFeature(FeaturePrefix::PHPTO_AUTHOR_GENDER, gender), 1.0f, result);
  int64_t reg_time = bs_util.BSGetAuthorInfoAttributeRegTime(bs, pos);
  int reg_month = static_cast<int>(std::ceil(reg_time / 1000.0 / 3600 / 24 / 30));
  AddFeature(GetFeature(FeaturePrefix::PHOTO_REGTIME, reg_month), 1.0f, result);

  int32_t upload_count = bs_util.BSGetAuthorInfoAttributeCountUpload(bs, pos);
  AddFeature(GetFeature(FeaturePrefix::PHOTO_UPLOAD_COUNT, upload_count), 1.0f, result);
}

}  // namespace ad_algorithm
}  // namespace ks
