#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdIsSmartMatching : public BSFastFeature {
 public:
  BSExtractAdIsSmartMatching() : BSFastFeature(FeatureType::DENSE_ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_4000);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }
    auto key_common_info_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size;
    int64_t common_info_attr_size = BSFieldHelper::GetSingular<int64_t>(*bs, key_common_info_attr_size, pos);
    if (common_info_attr_size > 0) {
      auto key_smart_matching_unit_label = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_4000;
      int64_t smart_matching_unit_label =
          BSFieldHelper::GetSingular<int64_t>(*bs, key_smart_matching_unit_label, pos);
      if (smart_matching_unit_label == 1) {
        AddFeature(0, 1, result);
      } else {
        AddFeature(0, 0, result);
      }
    }
  }

 private:
  const std::string USED_FEATURES[1] = {"item.ad_dsp_info.common_info_attr.SMART_MATCHING_UNIT_LABEL"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdIsSmartMatching);
};

REGISTER_BS_EXTRACTOR(BSExtractAdIsSmartMatching);

}  // namespace ad_algorithm
}  // namespace ks
