#pragma once
#include <algorithm>
#include <cmath>
#include <string>
#include <utility>
#include <vector>

#include "base/strings/string_printf.h"
#include "ks/util/json.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/proto/bs_common_info_attr_enum.pb.h"

namespace ks {
namespace ad_algorithm {
template <int no>
class BSExtractLiveAuthorMediumIndustryUnivExplore : public BSFastFeatureNoPrefix {
 public:
  BSExtractLiveAuthorMediumIndustryUnivExplore() : BSFastFeatureNoPrefix(FeatureType::COMBINE) {
    if (no ==
        ::bs::kuaishou::ad::
            CommonInfoAttr_NameExtendTwo_UNIV_LIVE_AUTHOR_X_NEW_INDUSTRY_X_AD_STYLE_7D_EXPLORE_COVERAGE_LIST) {  // NOLINT
      buck_value_ = {0.,         0.07177346, 0.14869836, 0.23114441, 0.31950791, 0.41421356,
                     0.51571657, 0.6245048,  0.74110112, 0.86606598, 1.};
    }

    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_6);
    attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_39);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_80020);

    BSGetItemAdDspInfoCommonInfoAttrNo.fill_attr_metas(&attr_metas_);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    auto enum_context_exists = BSFieldEnum::adlog_context_exists;
    bool context_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_context_exists, pos);

    auto enum_attr_size = BSFieldEnum::adlog_context_info_common_attr_size;
    int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);

    auto enum_info_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_size;
    int32_t info_attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_info_attr_size, pos);

    auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

    auto enum_item_type = BSFieldEnum::adlog_item_type;
    int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

    int ad_style = -1;
    int industry_id = -1;
    auto enum_key_39 = BSFieldEnum::adlog_context_info_common_attr_key_39;
    int64_t key_39 = BSFieldHelper::GetSingular<int64_t>(*bs, enum_key_39, pos);

    auto enum_key_39_exists = BSFieldEnum::adlog_context_info_common_attr_key_39;
    bool key_39_exists = BSFieldHelper::HasSingular<int64_t>(*bs, enum_key_39_exists, pos);

    auto enum_key_6 = BSFieldEnum::adlog_context_info_common_attr_key_6;
    int64_t key_6 = BSFieldHelper::GetSingular<int64_t>(*bs, enum_key_6, pos);

    auto enum_key_6_exists = BSFieldEnum::adlog_context_info_common_attr_key_6;
    bool key_6_exists = BSFieldHelper::HasSingular<int64_t>(*bs, enum_key_6_exists, pos);

    if (context_exists && attr_size > 0) {
      if (key_6_exists) {
        ad_style = key_6;
      }

      if (key_39_exists) {
        industry_id = key_39;
      }
    }

    if (ad_style < 0 || industry_id < 0) {
      return;
    }

    int medium_key = industry_id * 1000 + ad_style;

    auto enum_key_80020 = BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_80020;
    BSRepeatedField<int64_t> key_80020(*bs, enum_key_80020, pos);

    BSRepeatedField<float> attr_no = std::move(BSGetItemAdDspInfoCommonInfoAttrNo(bs, pos));

    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && info_exists &&
        info_attr_size > 0) {
      int index = -1;

      if (!key_80020.is_empty()) {
        for (int i = 0; i < key_80020.size(); i++) {
          if (key_80020.Get(i) == medium_key) {
            index = i;
            break;
          }
        }
      }

      if (index <= 0) {
        return;
      }

      if (!attr_no.is_empty()) {
        if (attr_no.size() <= index) {
          return;
        }
        int res = 12;
        float temp = attr_no.Get(index);

        for (int i = 0; i < 11; i++) {
          if (temp <= buck_value_[i]) {
            res = i + 1;
            break;
          }
        }
        AddFeature(res, 1.0f, result);
      }
    }
  }

 private:
  std::vector<double> buck_value_ = {1.,           2.4669495,    5.00986941,  9.41795677,
                                     17.05926501,  30.30527989,  53.26691223, 93.07032116,
                                     162.06852643, 281.67517305, 489.01027487};

 private:
  BSFixedCommonInfo<BSRepeatedField<float>> BSGetItemAdDspInfoCommonInfoAttrNo{
      "adlog.item.ad_dsp_info.common_info_attr", no};
  DISALLOW_COPY_AND_ASSIGN(BSExtractLiveAuthorMediumIndustryUnivExplore);
};
using BSExtractLiveAuthorMediumIndustryUnivExploreRatio7D = BSExtractLiveAuthorMediumIndustryUnivExplore<
    ::bs::kuaishou::ad::
        CommonInfoAttr_NameExtendTwo_UNIV_LIVE_AUTHOR_X_NEW_INDUSTRY_X_AD_STYLE_7D_EXPLORE_RATIO_LIST>;
REGISTER_BS_EXTRACTOR(BSExtractLiveAuthorMediumIndustryUnivExploreRatio7D);

using BSExtractLiveAuthorMediumIndustryUnivExploreCoverage7D = BSExtractLiveAuthorMediumIndustryUnivExplore<
    ::bs::kuaishou::ad::
        CommonInfoAttr_NameExtendTwo_UNIV_LIVE_AUTHOR_X_NEW_INDUSTRY_X_AD_STYLE_7D_EXPLORE_COVERAGE_LIST>;
REGISTER_BS_EXTRACTOR(BSExtractLiveAuthorMediumIndustryUnivExploreCoverage7D);

}  // namespace ad_algorithm
}  // namespace ks
