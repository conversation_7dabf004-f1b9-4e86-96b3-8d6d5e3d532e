#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"
namespace ks {
namespace ad_algorithm {

class BSExtractAuthorIdSlot : public BSFastFeatureNoPrefix {
 public:
  BSExtractAuthorIdSlot();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
 private:
  BSPhotoInfo<uint64_t> BSGetPhotoInfoAuthorInfoId{"author_info.id"};
  BSHasPhotoInfoImpl BSHasPhotoInfoAuthorInfo{"author_info"};
  BSHasPhotoInfoImpl BSHasPhotoInfo{""};
  DISALLOW_COPY_AND_ASSIGN(BSExtractAuthorIdSlot);
};

REGISTER_BS_EXTRACTOR(BSExtractAuthorIdSlot);

}  // namespace ad_algorithm
}  // namespace ks
