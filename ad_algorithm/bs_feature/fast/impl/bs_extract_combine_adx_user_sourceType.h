#pragma once
#include <string>
#include <vector>

#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractCombineAdxUserSourceType : public BSFastFeature {
 public:
  BSExtractCombineAdxUserSourceType() : BSFastFeature(FeatureType::COMBINE, 26, 26) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_adx_source_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_id);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_level);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_large_amount_creative);
    attr_metas_.emplace_back(BSFieldEnum::adlog_user_info_exists);
  }

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    auto key_user_info_exists = BSFieldEnum::adlog_user_info_exists;
    bool has_user_info = BSFieldHelper::GetSingular<bool, true>(*bs, key_user_info_exists, pos);
    if (!has_user_info) {
      return;
    }

    int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
    int adx_source_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_adx_source_type, pos);
    uint64_t user_id = BSFieldHelper::GetSingular<uint64_t, true>(*bs, BSFieldEnum::adlog_user_info_id, pos);
    int64_t level = BSFieldHelper::GetSingular<int64_t, true>(*bs, BSFieldEnum::adlog_user_info_level, pos);
    if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && adx_source_type > 0) {
      AddFeature(GetFeature(FeaturePrefix::COMBINE_ADX_USER_ID_SOURCETYPE, user_id, adx_source_type), 1.0,
                 result);

      AddFeature(GetFeature(FeaturePrefix::COMBINE_ADX_USER_LEVEL_SOURCETYPE, level, adx_source_type), 1.0,
                 result);
    }

    if (item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) {
      auto key_large = BSFieldEnum::adlog_item_large_amount_creative;
      int64_t large_amount_creative = BSFieldHelper::GetSingular<int64_t>(*bs, key_large, pos);
      AddFeature(GetFeature(FeaturePrefix::COMBINE_ADX_USER_ID_AMOUNT, user_id, large_amount_creative), 1.0,
                 result);

      AddFeature(GetFeature(FeaturePrefix::COMBINE_ADX_USER_LEVEL_AMOUNT, level, large_amount_creative), 1.0,
                 result);
    }
  }

 private:
  const std::string USED_FEATURES[4] = {"item.adx_source_type", "user_info.id", "user_info.level",
                                        "item.large_amount_creative"};
  DISALLOW_COPY_AND_ASSIGN(BSExtractCombineAdxUserSourceType);
};

REGISTER_BS_EXTRACTOR(BSExtractCombineAdxUserSourceType);

}  // namespace ad_algorithm
}  // namespace ks
