#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_mechant_history_item_pay_cnt_dense.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_realtime_cross_feature.h"

namespace ks {
namespace ad_algorithm {

BSExtractAuthorMechantHistoryItemPayCntDense::BSExtractAuthorMechantHistoryItemPayCntDense()
    : BSFastFeature(FeatureType::DENSE_COMBINE) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001107);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_label_info_label_infos_key_20000038_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_label_info_label_infos_key_20000038_value);
}
void BSExtractAuthorMechantHistoryItemPayCntDense::Extract(const BSLog& bslog, size_t pos,
                                                           std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64_list(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5001107, pos);
  auto x2 = get_bslog_map_int64_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000038_key,
                                      BSFieldEnum::adlog_item_label_info_label_infos_key_20000038_value, pos);
  auto x3 = get_list_map_match_dense_helper(x1, x2);
  auto x4 = cast_to_float_list(x3);
  add_feature_result(x4, 1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
