#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extrac_item_crm_third_industry.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"

namespace ks {
namespace ad_algorithm {

BSExtracItemCrmThirdIndustry::BSExtracItemCrmThirdIndustry() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_2900);
}
void BSExtracItemCrmThirdIndustry::Extract(const BSLog& bslog, size_t pos,
                                           std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_str(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_2900, pos);
  auto x2 = city_hash64(x1);
  add_feature_result(x2, result);
}

}  // namespace ad_algorithm
}  // namespace ks
