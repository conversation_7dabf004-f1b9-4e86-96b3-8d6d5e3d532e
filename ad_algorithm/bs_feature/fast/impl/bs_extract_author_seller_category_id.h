#pragma once
#include <string>
#include <unordered_map>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_common_info_attr_feature.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_repeated_field.h"

namespace ks {
namespace ad_algorithm {

class BSExtractMerchantAuthorCategoryId : public BSCommonInfoAttrFeature {
 public:
  BSExtractMerchantAuthorCategoryId()
      : BSCommonInfoAttrFeature(FeatureType::ITEM, ::bs::kuaishou::ad::CommonInfoAttr_Name_AUTHOR_CATEGORY_ID,
                                GetPrefix(0)) {
    value_bits_ = 22;
    value_mask_ = (1LL << value_bits_) - 1;
  }

  inline uint64_t get_sign(uint64_t key, uint64_t value) {
    return (key << value_bits_) | (value & value_mask_);
  }

  void ExtractCommonAttr(const ad_nn::SampleInterface& sample, const std::vector<int32_t>& attr_ids,
                         size_t pos, std::vector<ExtractResult>* result) override {
    if (attr_ids.size() != 1) {
      return;
    }

    ad_nn::BSRepeatedField<int64_t> values(sample, attr_ids[0], pos);
    for (size_t i = 0; i < values.size(); ++i) {
      if (values[i] > 0) {
        AddFeature(get_sign(i, values[i]), 1.0, result);
      }
    }
  }

  bool InitInternal(const base::Json& config) override { return true; }

  bool CheckCommonAttr(const ad_nn::SampleInterface& sample, const std::vector<int32_t>& attr_ids, size_t pos,
                       const std::vector<ExtractResult>& result) override {
    return true;
  }

 private:
  int value_bits_;
  uint64_t value_mask_;

  DISALLOW_COPY_AND_ASSIGN(BSExtractMerchantAuthorCategoryId);
};

REGISTER_BS_EXTRACTOR(BSExtractMerchantAuthorCategoryId);

}  // namespace ad_algorithm
}  // namespace ks
