#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_first_industry_v3_dense_onehot_v1.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdFirstIndustryV3DenseOnehotV1 ::BSExtractAdFirstIndustryV3DenseOnehotV1()
    : BSFastFeature(DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_creative_exists);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
}

void BSExtractAdFirstIndustryV3DenseOnehotV1::Extract(const BSLog& bslog, size_t pos,
                                                      std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);
  auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);
  auto key_creative_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_exists;
  bool has_creative = BSFieldHelper::GetSingular<bool>(*bs, key_creative_exists, pos);
  auto key_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_exists;
  bool has_base = BSFieldHelper::GetSingular<bool>(*bs, key_base_exists, pos);

  if ((item_type == bs::ItemType::AD_DSP || item_type == bs::ItemType::NATIVE_AD) && has_ad_dsp_info &&
      has_creative && has_base) {
    auto key_industry_id = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_industry_id_v3;
    int64_t industry_id = BSFieldHelper::GetSingular<int>(*bs, key_industry_id, pos);
    int tag = 23;
    // industry_id_v3 map table:
    // https://docs.corp.kuaishou.com/s/home/<USER>
    if (industry_id >= 1023 && industry_id <= 1027) {
      tag = 0;
    } else if (industry_id > 1027 && industry_id <= 1031) {
      tag = 1;
    } else if (industry_id > 1031 && industry_id <= 1035) {
      tag = 2;
    } else if (industry_id > 1035 && industry_id <= 1056) {
      tag = 3;
    } else if ((industry_id > 1056 && industry_id <= 1089) || industry_id == 1290) {
      tag = 4;
    } else if (industry_id > 1089 && industry_id <= 1094) {
      tag = 5;
    } else if (industry_id > 1094 && industry_id <= 1102) {
      tag = 6;
    } else if (industry_id > 1102 && industry_id <= 1110) {
      tag = 7;
    } else if (industry_id > 1110 && industry_id <= 1117) {
      tag = 8;
    } else if (industry_id > 1117 && industry_id <= 1127) {
      tag = 9;
    } else if (industry_id > 1127 && industry_id <= 1143) {
      tag = 10;
    } else if (industry_id > 1143 && industry_id <= 1152) {
      tag = 11;
    } else if (industry_id > 1152 && industry_id <= 1160) {
      tag = 12;
    } else if (industry_id > 1160 && industry_id <= 1172) {
      tag = 13;
    } else if (industry_id > 1172 && industry_id <= 1193) {
      tag = 14;
    } else if (industry_id > 1193 && industry_id <= 1203) {
      tag = 15;
    } else if (industry_id > 1203 && industry_id <= 1213) {
      tag = 16;
    } else if (industry_id > 1213 && industry_id <= 1221) {
      tag = 17;
    } else if (industry_id > 1221 && industry_id <= 1232) {
      tag = 18;
    } else if (industry_id > 1232 && industry_id <= 1241) {
      tag = 19;
    } else if (industry_id > 1241 && industry_id <= 1244) {
      tag = 20;
    } else if (industry_id > 1244 && industry_id <= 1255) {
      tag = 21;
    } else if (industry_id > 1255 && industry_id <= 1289) {
      tag = 22;
    }
    for (size_t i = 0; i < 24; i++) {
      if (tag == i) {
        AddFeature(i, 1, result);
      } else {
        AddFeature(i, 0, result);
      }
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
