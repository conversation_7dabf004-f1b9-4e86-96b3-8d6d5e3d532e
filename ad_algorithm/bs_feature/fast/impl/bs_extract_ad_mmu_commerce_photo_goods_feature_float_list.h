#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdMmuGoodsFeatureNewEmbed64 : public BSFastFeatureNoPrefix {
 public:
  BSExtractAdMmuGoodsFeatureNewEmbed64();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdMmuGoodsFeatureNewEmbed64);
};

REGISTER_BS_EXTRACTOR(BSExtractAdMmuGoodsFeatureNewEmbed64);

}  // namespace ad_algorithm
}  // namespace ks
