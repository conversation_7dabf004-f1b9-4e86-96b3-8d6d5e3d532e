#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_repeat_purchase.h"
namespace ks {
namespace ad_algorithm {
using BSExtractAuthorRepeatPurchaseAvgTime =
    BSExtractAuthorRepeatPurchase<BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82008>;
REGISTER_BS_EXTRACTOR(BSExtractAuthorRepeatPurchaseAvgTime);
using BSExtractAuthorRepeatPurchaseAvgCnt =
    BSExtractAuthorRepeatPurchase<BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_82009>;
REGISTER_BS_EXTRACTOR(BSExtractAuthorRepeatPurchaseAvgCnt);
}  // namespace ad_algorithm
}  // namespace ks
