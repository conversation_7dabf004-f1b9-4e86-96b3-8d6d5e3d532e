#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature_no_prefix.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/proto/bs_common_info_attr_enum.pb.h"

namespace ks {
namespace ad_algorithm {
using CommonInfoAttr = ::bs::kuaishou::ad::CommonInfoAttr;

template <int no>
class BSExtractAdMmuGoodsFeatureNewInt : public BSFastFeatureNoPrefix {
 public:
  BSExtractAdMmuGoodsFeatureNewInt() : BSFastFeatureNoPrefix(FeatureType::ITEM) {
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_type);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists);
    attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size);

    BSGetItemAdDspInfoPhotoInfoCommonInfoAttrNo.fill_attr_metas(&attr_metas_);
    BSHasItemAdDspInfoPhotoInfoCommonInfoAttrNo.fill_attr_metas(&attr_metas_);
  }
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result) {
    auto bs = bslog.GetBS();
    if (bs == nullptr) {
      return;
    }

    auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    bool info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

    auto enum_attr_size = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_common_info_attr_size;
    int32_t attr_size = BSFieldHelper::GetSingular<int32_t>(*bs, enum_attr_size, pos);

    auto enum_photo_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists;
    bool photo_info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_photo_info_exists, pos);

    auto enum_item_type = BSFieldEnum::adlog_item_type;
    int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);

    int64_t attr_no = BSGetItemAdDspInfoPhotoInfoCommonInfoAttrNo(bs, pos);

    if ((item_type == bs::ItemType::AD_DSP) && info_exists && photo_info_exists && attr_size > 0) {
      if (BSHasItemAdDspInfoPhotoInfoCommonInfoAttrNo(bs, pos)) {
        AddFeature(attr_no, 1.0, result);
      }
    }
  }

 private:
  BSFixedCommonInfo<int64_t> BSGetItemAdDspInfoPhotoInfoCommonInfoAttrNo{
      "adlog.item.ad_dsp_info.photo_info.common_info_attr", no};
  BSHasFixedCommonInfoImpl<int64_t, false> BSHasItemAdDspInfoPhotoInfoCommonInfoAttrNo{
      "adlog.item.ad_dsp_info.photo_info.common_info_attr", no};

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdMmuGoodsFeatureNewInt);
};

using BSExtractAdMmuCommercePhotoMaxTagId = BSExtractAdMmuGoodsFeatureNewInt<
    ::bs::kuaishou::ad::CommonInfoAttr_NameExtendOne_AD_MMU_COMMERCE_PHOTO_GOODS_CATEGORY_MAX_TAG_ID_LIST>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuCommercePhotoMaxTagId);

using BSExtractAdMmuCommercePhotoLeafTagId = BSExtractAdMmuGoodsFeatureNewInt<
    ::bs::kuaishou::ad::CommonInfoAttr_NameExtendOne_AD_MMU_COMMERCE_PHOTO_GOODS_CATEGORY_LEAF_TAG_ID_LIST>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuCommercePhotoLeafTagId);

using BSExtractAdMmuCommercePhotoClusterId = BSExtractAdMmuGoodsFeatureNewInt<
    ::bs::kuaishou::ad::CommonInfoAttr_NameExtendOne_AD_MMU_COMMERCE_PHOTO_GOODS_CATEGORY_CLUSTER_ID_LIST>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuCommercePhotoClusterId);

using BSExtractAdMmuCommercePhotoIntentScore = BSExtractAdMmuGoodsFeatureNewInt<
    ::bs::kuaishou::ad::CommonInfoAttr_NameExtendOne_AD_MMU_COMMERCE_PHOTO_GOODS_ECOM_INTENT_SCORE>;
REGISTER_BS_EXTRACTOR(BSExtractAdMmuCommercePhotoIntentScore);
}  // namespace ad_algorithm
}  // namespace ks
