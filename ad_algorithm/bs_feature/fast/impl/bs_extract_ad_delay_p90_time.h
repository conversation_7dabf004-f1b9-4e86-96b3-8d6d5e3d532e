#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractAdDelayP90Time : public BSFastFeature {
 public:
  BSExtractAdDelayP90Time();

  virtual void Extract(const BSLog &bslog, size_t pos, std::vector<ExtractResult> *result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractAdDelayP90Time);
};

REGISTER_BS_EXTRACTOR(BSExtractAdDelayP90Time);

}  // namespace ad_algorithm
}  // namespace ks
