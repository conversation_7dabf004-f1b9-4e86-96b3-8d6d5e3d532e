#pragma once
#include <string>
#include <vector>
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractBrandCombineUserClickIndustry : public BSFastFeature {
  const int max_num_dsp_info = 100;

 public:
  BSExtractBrandCombineUserClickIndustry();

  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(BSExtractBrandCombineUserClickIndustry);
};
REGISTER_BS_EXTRACTOR(BSExtractBrandCombineUserClickIndustry);
}  // namespace ad_algorithm
}  // namespace ks
