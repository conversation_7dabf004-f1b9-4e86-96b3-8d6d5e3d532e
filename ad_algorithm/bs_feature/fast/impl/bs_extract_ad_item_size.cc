#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_ad_item_size.h"

namespace ks {
namespace ad_algorithm {

BSExtractAdItemSize::BSExtractAdItemSize() : BSFastFeature(DENSE_ITEM) {}

void BSExtractAdItemSize::Extract(const BSLog &bslog, size_t pos, std::vector<ExtractResult> *result) {
  if (bslog.item_size() <= pos) {
    return;
  }
  // PV 的候选 item 数量
  AddFeature(0, bslog.item_size(), result);
}

}  // namespace ad_algorithm
}  // namespace ks
