#pragma once
#include <google/protobuf/arena.h>
#include <future>
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include "base/common/gflags.h"
// ::bs::kuaishou::ad::AdCallbackLog::EventType
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_meta.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_callback_log_event_type.pb.h"
#include "teams/ad/ad_algorithm/feature_interface/ad_live_sim_result.h"
#include "teams/ad/ad_algorithm/feature_interface/ad_log_interface.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/feature_extract/extract_framework/include/sample_interface.h"
#include "teams/ad/ad_nn/item_union/item_info_type.h"
#include "teams/ad/picasso/sdk/common/common_item_types.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

// NOTE(Jzh) : Do NOT include PB header in this file!!!

// Forward declaration for protobuf definitions.
namespace bs {
namespace kuaishou {
namespace ad {
namespace algorithm {
class AllGoods;
class AdUserAdActionMap;
}  // namespace algorithm
}  // namespace ad
}  // namespace kuaishou
}  // namespace bs

namespace xlib {
namespace picasso {
namespace pb {
namespace kv {
class HMapListAppendReq;
}
}  // namespace pb
}  // namespace picasso
}  // namespace xlib

namespace ks {
namespace reco {
class UserInfo;
}
namespace ad_algorithm {

class BSLog : public AdLogInterface {
 public:
  BSLog();
  explicit BSLog(const ad_nn::SampleInterface* sample);
  virtual ~BSLog();

  const ::bs::kuaishou::ad::AdCallbackLog::EventType get_ad_callback_event(int pos) const;
  void add_ad_callback_event(::bs::kuaishou::ad::AdCallbackLog::EventType callback_event);
  void reserve_ad_callback_event(size_t size);
  int ad_callback_events_size() const;

  uint64_t llsid() const override;
  LogType log_type() const override { return LogType::LT_BS; }
  uint64_t timestamp() const override;
  uint64_t item_id(int index) const override;
  int item_size() const override;
  bool has_user_info() const override;
  bool has_context() const override;
  bool has_reco_user_info() const { return reco_user_info_ != nullptr; }
  const ks::reco::UserInfo* reco_user_info() const;
  ks::reco::UserInfo* mutable_reco_user_info();
  void reset_reco_user_info() { reco_user_info_ = nullptr; }

  const ad_nn::SampleInterface* GetBS() const { return sample_; }
  void SetBS(const ad_nn::SampleInterface* sample) { sample_ = sample; }
  // ad live sim 相关
  std::unordered_map<uint32_t, AdLiveSimResult>* mutable_ad_live_sim_result() {
    return &ad_live_sim_result_map;
  }
  const std::unordered_map<uint32_t, AdLiveSimResult>* get_ad_live_sim_result() const {
    return &ad_live_sim_result_map;
  }
  bool ad_live_sim_ready() const { return ad_live_sim_ready_; }
  void set_ad_live_sim_ready(bool ready) { ad_live_sim_ready_ = ready; }
  void set_item_main_type(ks::ad_nn::ItemInfoType main_type) { main_type_ = main_type; }
  std::shared_ptr<Arena> GetArena() { return pb_arena_; }

 protected:
  template <typename T>
  T* CreateMessage() {
    return Arena::CreateMessage<T>(pb_arena_.get());
  }

 private:
  mutable std::shared_ptr<Arena> pb_arena_ = nullptr;
  ks::reco::UserInfo* reco_user_info_ = nullptr;
  const ad_nn::SampleInterface* sample_ = nullptr;
  std::vector<::bs::kuaishou::ad::AdCallbackLog::EventType> ad_callback_events_;
  // ad live sim 字段
  std::unordered_map<uint32_t, AdLiveSimResult> ad_live_sim_result_map;
  bool ad_live_sim_ready_ = false;
  ks::ad_nn::ItemInfoType main_type_ = ks::ad_nn::ItemInfoType::FULL;
  DECLARE_PROCESSOR(::bs::kuaishou::ad::algorithm::AdUserAdActionMap,
                    ad_user_ad_action_map);
  DECLARE_PROCESSOR(::bs::kuaishou::ad::algorithm::AllGoods, ad_user_all_goods_action);
  DECLARE_PROCESSOR(xlib::picasso::pb::kv::HMapListAppendReq, h_maplist_append_req);
  DECLARE_PROCESSOR(std::vector<colossus::AdLiveItemT>, colossus_ad_live_item);
  DECLARE_PROCESSOR(std::vector<colossus::LiveItemV2T>, colossus_reco_live_item);
  DECLARE_PROCESSOR(std::vector<colossus::LiveItemT>, colossus_reco_live_v1_item);
  DECLARE_PROCESSOR(std::vector<colossus::AdGoodsItemT>, colossus_ad_goods_item);
  DECLARE_PROCESSOR(std::vector<colossus::AdGoodsItemNewT>, colossus_ad_goods_item_new);
  DECLARE_PROCESSOR(std::vector<colossus::AdGoodsItemNewT>,
                    colossus_ad_goods_item_new_pdn_v0);
  DECLARE_PROCESSOR(std::vector<colossus::SimItemV2T>, colossus_reco_photo);
  DECLARE_PROCESSOR(std::vector<colossus::SimItemV3T>, colossus_reco_photo_v3);
  DECLARE_PROCESSOR(std::vector<colossus::SimItemV3T>, colossus_reco_photo_v3_pdn_v0);
  DECLARE_PROCESSOR(StringIntMap, colossus_ad_live_spu);
  DECLARE_PROCESSOR(StringIntMap, colossus_ad_live_cid3);
  DECLARE_PROCESSOR(IntIntMap, author_cluster_map);
  DECLARE_PROCESSOR(IntIntMap, live_cluster_map);
  DECLARE_PROCESSOR(IntIntListMap, ad_live_global_gsu_result);
  DECLARE_PROCESSOR(IntIntListMap, ad_live_ecomm_gsu_result);
  DECLARE_PROCESSOR(IntIntListMap, ad_live_author_gsu_result);
  DECLARE_PROCESSOR(IntIntListMap, ad_live_author_cluster_gsu_result);
  DECLARE_PROCESSOR(IntIntListMap, ad_live_spu_gsu_result);
  DECLARE_PROCESSOR(IntIntListMap, ad_live_cid3_gsu_result);
  DECLARE_PROCESSOR(IntIntListMap, ad_live_remote_cluster_gsu_result);
  DECLARE_PROCESSOR(IntIntListMap, reco_live_global_gsu_result);
  DECLARE_PROCESSOR(IntIntListMap, reco_live_author_gsu_result);
  DECLARE_PROCESSOR(IntIntListMap, reco_live_author_cluster_gsu_result);
  DECLARE_PROCESSOR(IntIntListMap, reco_live_remote_cluster_gsu_result);
  DECLARE_PROCESSOR(IntIntListMap, reco_live_v1_remote_cluster_gsu_result);
  DECLARE_PROCESSOR(std::vector<int>, ad_live_colossus_idx_filtered_by_playtime);
  DECLARE_PROCESSOR(std::vector<int>, ad_live_colossus_idx_filtered_by_label);
  DECLARE_PROCESSOR(std::vector<int>, reco_live_colossus_idx_filtered_by_playtime);
  DECLARE_PROCESSOR(std::vector<std::vector<float>>, ad_user_history_photo_embedding);
  DECLARE_PROCESSOR(std::vector<std::vector<float>>, ad_user_history_pdct_embedding);
  DECLARE_PROCESSOR(std::vector<int64_t>, ad_user_histactpdct_pdct);
  DECLARE_PROCESSOR(std::vector<int64_t>, ad_user_histactpdct_firstid);
  DECLARE_PROCESSOR(std::vector<int64_t>, ad_user_histactpdct_secondid);
  DECLARE_PROCESSOR(std::vector<float>, ad_user_histactpdct_u2uemb);
  DECLARE_PROCESSOR(std::string, ad_user_histact_weight);
  DECLARE_PROCESSOR(std::vector<int64_t>, ad_user_histact_type);
  DECLARE_PROCESSOR(std::vector<ks::ad_picasso::sdk::AdGoodsItemT>,
                    picasso_ad_goods_item);
  // ad_live_offline_sample_set 仅用于离线样本过滤
  DECLARE_PROCESSOR(std::unordered_set<std::string>, ad_live_offline_sample_set);
};

}  // namespace ad_algorithm
}  // namespace ks
