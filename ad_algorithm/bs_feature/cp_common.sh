all_values=(
# "teams/ad/ad_algorithm/feature/fast/impl/extract_common_int_sign_value.h"
# "teams/ad/ad_algorithm/feature/fast/impl/extract_common_int_list_sign_value.h"
# "teams/ad/ad_algorithm/feature/fast/impl/extract_common_number_list_sign_value.h"
# "teams/ad/ad_algorithm/feature/fast/impl/extract_common_int_map_sign_value.h"
# "teams/ad/ad_algorithm/feature/fast/impl/extract_common_number_seg_norm.h"
# "teams/ad/ad_algorithm/feature/fast/impl/extract_common_number_value.h"
"teams/ad/ad_algorithm/feature/fast/impl/extract_author_seller_category_id.h"
"teams/ad/ad_algorithm/feature/fast/impl/extract_author_seller_click_stat.h"
"teams/ad/ad_algorithm/feature/fast/impl/extract_author_seller_live_trolley_stat.h"
"teams/ad/ad_algorithm/feature/fast/impl/extract_author_seller_pay_cate3_id.h"
)

path_prefix=$1
for entry in ${all_values[@]}
do
  echo $entry
  full_source_path=$path_prefix/$entry
  source_file_name=$(echo "$entry" | awk -F "/" '{print $NF}')
  class_name=$(grep "class" $full_source_path | awk '{print $2}' | awk -F ':' '{print $1}')
  echo $class_name
  full_target_path="$path_prefix/teams/ad/ad_algorithm/bs_feature/fast/impl/bs_${source_file_name}"

  echo "Full source path: $full_source_path"
  echo "Full target path: $full_target_path"
  # cp source to target
  cp $full_source_path $full_target_path
  echo "cp $full_source_path $full_target_path" 
  sed -i "s/ad_algorithm\/feature\/fast\/frame\/common_info_attr_feature_factory.h/ad_algorithm\/bs_feature\/fast\/frame\/bs_common_info_attr_feature_factory.h/g" $full_target_path
  sed -i "s/${class_name}/BS${class_name}/g" $full_target_path
  sed -i "s/CommonInfoAttrFeature/BSCommonInfoAttrFeature/g" $full_target_path
  sed -i "s/public FastFeature/public BSFastFeature/g" $full_target_path
  sed -i "s/REGISTER_COMMON_INFO_ATTR_EXTRACTOR/REGISTER_BS_COMMON_INFO_ATTR_EXTRACTOR/g" $full_target_path
done
