#!/bin/sh

feature_info=$1
path_prefix=$2
bs_feature_list_path="$path_prefix/teams/ad/ad_algorithm/bs_feature/fast/impl/bs_feature_list.h"
# clear the content
> $bs_feature_list_path
echo "#pragma once" >> $bs_feature_list_path
echo "#include \"teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_id.h\"" >> $bs_feature_list_path
total_count=$(wc -l $feature_info | awk '{print $1}')
total_count=$(($total_count - 1))
cur_count=0
while read -r line
do
  if [[ $line = *":" ]];then
    cmd=$(echo $line | awk -F ":" '{print $1}')
    echo "cmd:$cmd"
  else
    extractor_class_name=$(echo $line | awk '{print $1}')
    extractor_path=$(echo $line | awk '{print $2}')
    echo "Extractor class name: $extractor_class_name"
    echo "Extractor path: $extractor_path"
    source_file_name=$(echo $line | awk -F "/" '{print $NF}')
    echo "Extractor source file name: $source_file_name"
    full_source_path="$path_prefix/teams/ad/ad_algorithm/feature/fast/impl/${source_file_name}"
    full_target_path="$path_prefix/teams/ad/ad_algorithm/bs_feature/fast/impl/bs_${source_file_name}"
    rm $full_target_path
    grep "CommonInfoAttrFeature" $full_source_path > /dev/null 2>&1
    if [[ $? -eq 0 ]];then
      echo "CommonInfoAttrFeature related, skip $extractor_class_name $full_source_path"
      cur_count=$(($cur_count + 1))
      echo "progress: $cur_count/$total_count"
      continue
    fi

    echo $line | grep "reco_feature_list.h" > /dev/null 2>&1
    if [[ $? -eq 0 ]];then
      echo "has reco feature list.h, skip $extractor_class_name $full_source_path"
      cur_count=$(($cur_count + 1))
      echo "progress: $cur_count/$total_count"
      continue
    fi

    grep "template<" $full_source_path > /dev/null 2>&1
    if [[ $? -eq 0 ]];then
      echo "has template feature, skip $extractor_class_name $full_source_path"
      cur_count=$(($cur_count + 1))
      echo "progress: $cur_count/$total_count"
      continue
    fi

    echo "Full source path: $full_source_path"
    echo "Full target path: $full_target_path"
    # cp source to target
    cp $full_source_path $full_target_path
    echo "cp $full_source_path $full_target_path" 

    # change the content
    sed -i "s/ad_algorithm\/feature\/fast\/frame\/fast_feature_no_prefix.h/ad_algorithm\/bs_feature\/fast\/frame\/bs_fast_feature_no_prefix.h/g" $full_target_path
    sed -i "s/ad_algorithm\/feature\/fast\/frame\/fast_feature.h/ad_algorithm\/bs_feature\/fast\/frame\/bs_fast_feature.h/g" $full_target_path
    sed -i "s/${extractor_class_name}/BS${extractor_class_name}/g" $full_target_path
    #sed -i "s/public FastFeature/public BSFastFeature/g" $full_target_path
    sed -i "s/REGISTER_EXTRACTOR/REGISTER_BS_EXTRACTOR/g" $full_target_path
    #sed -i "s/FastFeature(/BSFastFeature(/g" $full_target_path
    sed -i "s/FastFeature/BSFastFeature/g" $full_target_path
    # change the bs_feature_list.h
    grep "bs_${source_file_name}" $bs_feature_list_path
    if [[ $? -eq 0 ]];then
      echo "A duplicated file"
      cur_count=$(($cur_count + 1))
      echo "progress: $cur_count/$total_count"
      continue
    fi

    echo "#include \"teams/ad/ad_algorithm/bs_feature/fast/impl/bs_${source_file_name}\"" >> $bs_feature_list_path
    cur_count=$(($cur_count + 1))
    echo "progress: $cur_count/$total_count"
  fi
done < "$feature_info"
