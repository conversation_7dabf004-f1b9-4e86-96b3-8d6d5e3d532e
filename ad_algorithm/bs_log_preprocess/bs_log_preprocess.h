#pragma once

#include <iostream>
#include <fstream>
#include <string>
#include <random>
#include <atomic>
#include <memory>
#include <vector>
#include <unordered_set>
#include "base/common/base.h"
#include "base/common/logging.h"
#include "base/time/timestamp.h"
#include "base/strings/string_split.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_number_conversions.h"
#include "base/encoding/base64.h"
#include "base/encoding/line_escape.h"
#include "teams/ad/ad_base/src/common/common.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/bs_log.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

class BSItemFilterBase;
class BSLabelExtractorBase;

class BSLogPreprocess {
 public:
  BSLogPreprocess(const std::string& item_filter, const std::string& label_extractor);

  bool init(const std::string& item_filter, const std::string& label_extractor);
  bool init_success() const { return init_success_; }

  bool filter(const BSLog& bslog, size_t pos);
  std::vector<int64_t> get_label(const BSLog& bslog, size_t pos);

  std::atomic<int64_t> count_total{0};
  std::atomic<int64_t> count_after_log_filter{0};
  std::atomic<int64_t> count_after_item_filter{0};
  std::atomic<int64_t> count_after_label_extractor{0};
  std::atomic<int64_t> count_empty_labels{0};
  std::atomic<int64_t> count_negative{0};
  std::atomic<int64_t> count_positive{0};
  std::atomic<int64_t> count_of_item_size_equal_one{0};
  std::atomic<int64_t> count_of_item_size_equal_two{0};
  std::atomic<int64_t> count_of_item_size_equal_three{0};
  std::vector<int> count_label;

 private:
  std::vector<std::shared_ptr<BSItemFilterBase>> item_filters_;
  std::shared_ptr<BSLabelExtractorBase> label_extractor_ = nullptr;
  std::string tab_filter_name_;
  std::string label_extractor_full_name_;
  std::vector<std::string> item_filter_names_;
  bool init_success_ = false;
};

}  // namespace ad_algorithm
}  // namespace ks
