import os

cc_library(name = "bs_log_preprocess",
            srcs = ["*.cc", "bs_item_filter/*.cc", "bs_label_extractor/*.cc"],
            deps = [
                "//base/strings/BUILD:strings",
                "//ks/util/BUILD:util",
                "//base/strings/BUILD:strings",
                "//teams/ad/ad_algorithm/bs_feature/BUILD:bs_log",
                "//base/common/BUILD:base",
                "//infra/redis_proxy_client/BUILD:redis_client",
                "//teams/ad/ad_nn/bs_field_helper/BUILD:bs_field_helper",
                "//teams/ad/ad_algorithm/bs_feature/BUILD:bs_meta"
                ],
            cppflags=[
                "-D GOOGLE_LOGGING=1",
                "-Ithird_party/flatbuffers/include/"
            ]
)
           
