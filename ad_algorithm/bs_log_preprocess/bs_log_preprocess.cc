#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_log_preprocess.h"

namespace ks {
namespace ad_algorithm {

BSLogPreprocess::BSLogPreprocess(const std::string& item_filter, const std::string& label_extractor) {
  init(item_filter, label_extractor);
}

// 建议不要用 | 作为参数中的分隔符，从命令行传参数时候会被当做管道，导致参数传递错误。如果已经用了 |，
// 建议参数用 @ 替换，| 和 @ 都会被当做分隔符。shell 中的特殊字符都不应该当做分隔符，如分号 (;),
// 竖线 (|), 等号 (=)，斜线 (\/)，否则很容易出问题。 插件自己的参数 conf 里也不能用逗号 (,) 和冒号 (:)，
// 逗号是用来分隔多个插件的，冒号是用来分隔插件名和插件参数的。
bool BSLogPreprocess::init(const std::string& item_filter, const std::string& label_extractor) {
  init_success_ = false;

  PluginMgr<BSItemFilterBase>* item_filter_mgr = nullptr;
  PluginMgr<BSItemFilterBase>::GetInstance(&item_filter_mgr);

  std::vector<std::string> filters;
  base::SplitString(item_filter, std::string(",", 1), &filters);
  std::vector<std::string> seg;
  std::string filter_name = "";
  std::string filter_args = "";
  for (const auto& filter : filters) {
    seg.clear();
    bool found = false;
    if (filter == "") {
      continue;
    }
    item_filter_names_.emplace_back(filter);
    base::SplitString(filter, std::string(":", 1), &seg);
    filter_name = seg[0];
    if (seg.size() >= 2) {
      filter_args = seg[1];
    } else {
      std::string filter_args = "";
    }

    auto cur_item_filter = item_filter_mgr->BuildPlugin(filter_name, filter_args);
    if (cur_item_filter != nullptr) {
      item_filters_.push_back(cur_item_filter);
      found = true;
    }
    if (!found) {
      init_success_ = false;
      return false;
    }
  }

  if (label_extractor.size() == 0) {
    LOG(ERROR) << "must provide label extractor!";
    init_success_ = false;
    return false;
  }

  seg.clear();
  base::SplitString(label_extractor, std::string(":", 1), &seg);
  auto label_extractor_name = seg[0];
  std::string label_extractor_args = "";
  if (seg.size() > 1) {
    label_extractor_args = seg[1];
  }
  LOG(INFO) << "init LogPreprocess, label_extractor: " << label_extractor
            << ", name: " << label_extractor_name
            << ", args: " << label_extractor_args;
  PluginMgr<BSLabelExtractorBase>* label_extractor_mgr = nullptr;
  PluginMgr<BSLabelExtractorBase>::GetInstance(&label_extractor_mgr);
  label_extractor_ = label_extractor_mgr->BuildPlugin(label_extractor_name, label_extractor_args);
  if (label_extractor_ == nullptr) {
    LOG(ERROR) <<"!!!!! no found label extractor : "<< label_extractor_name;
    init_success_ = false;
    return false;
  }

  init_success_ = true;
  return init_success_;
}

bool BSLogPreprocess::filter(const BSLog& bslog, size_t pos) {
  // 多个 item_filter 必须同时满足
  for (const auto& item_filter : item_filters_) {
    if (!(*item_filter)(bslog, pos)) {
      return false;
    }
  }

  return true;
}

std::vector<int64_t> BSLogPreprocess::get_label(const BSLog& bslog, size_t pos) {
  return (*label_extractor_)(bslog, pos);
}

}  // namespace ad_algorithm
}  // namespace ks
