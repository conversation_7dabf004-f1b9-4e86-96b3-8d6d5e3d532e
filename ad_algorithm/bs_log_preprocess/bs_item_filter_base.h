#pragma once

#include "base/common/base.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/bs_log.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_map_field.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_repeated_field.h"
#include "teams/ad/ad_algorithm/log_preprocess/plugin_mgr.h"
#include "teams/ad/ad_algorithm/log_preprocess/plugin_base.h"

namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSRepeatedField;
using ks::ad_nn::BSMapField;
using ks::ad_nn::BSFieldHelper;

class BSItemFilterBase : public PluginBase {
 public:
  virtual ~BSItemFilterBase() {}
  virtual bool operator()(const BSLog& bslog, size_t pos) const = 0;
};

}  // namespace ad_algorithm
}  // namespace ks
