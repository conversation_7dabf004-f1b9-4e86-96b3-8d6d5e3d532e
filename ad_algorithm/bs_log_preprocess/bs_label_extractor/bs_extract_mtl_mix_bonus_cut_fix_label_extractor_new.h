#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/label_extractor/mtl_mix_bonus_cut_fix_label_extractor_new.dark
class BSExtractMtlMixBonusCutFixLabelExtractorNew : public BSLabelExtractorBase {
 public:
  explicit BSExtractMtlMixBonusCutFixLabelExtractorNew(const std::string&) {}
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSExtractMtlMixBonusCutFixLabelExtractorNew,
                "bs_extract_mtl_mix_bonus_cut_fix_label_extractor_new");
}  // namespace ad_algorithm
}  // namespace ks
