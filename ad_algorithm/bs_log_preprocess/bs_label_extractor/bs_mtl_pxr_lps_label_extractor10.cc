#include <string>
#include <set>
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_mtl_pxr_lps_label_extractor10.h"

namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSMtlPxrLpsLabelExtractor10::BSMtlPxrLpsLabelExtractor10(const std::string& conf)
    : bs_mtl_pxr_lps_label_extractor41_(conf) {}

std::vector<int64_t> BSMtlPxrLpsLabelExtractor10::operator()(const BSLog& bslog,
                                                            size_t pos) const {
  std::vector<int64_t> old_res = bs_mtl_pxr_lps_label_extractor41_(bslog, pos);
  if (old_res.size() == 0) {
    return old_res;
  }
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return old_res;
  }
  std::vector<int64_t> res;
  auto key_num = BSFieldEnum::adlog_item_label_info_label_infos_key_20000062;
  uint64_t lps_num = BSFieldHelper::GetSingular<uint64_t>(*bs, key_num, pos);
  std::set<int> indus_set;
  auto key_industry_id = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_id;
  auto label_list_size = bslog.item_size();
  auto key_lps = BSFieldEnum::adlog_item_label_info_landing_page_form_submitted;  // bslog->total_sampe_len();
  for (unsigned int target_pos = 0; target_pos < label_list_size; ++target_pos) {
    int industry_id = BSFieldHelper::GetSingular<int>(*bs, key_industry_id, target_pos);
    bool lps = BSFieldHelper::GetSingular<bool>(*bs, key_lps, target_pos);
    if (lps) {
      indus_set.insert(industry_id);
    }
  }
  int cur_industry_id = BSFieldHelper::GetSingular<int>(*bs, key_industry_id, pos);
  int label_same_cate_lps = 0;
  if (indus_set.find(cur_industry_id) != indus_set.end()) {
     label_same_cate_lps = 1;
  }
  int64_t label = old_res[0];

  label |= static_cast<int64_t>(label_same_cate_lps << 16);
  res.push_back(label);
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
