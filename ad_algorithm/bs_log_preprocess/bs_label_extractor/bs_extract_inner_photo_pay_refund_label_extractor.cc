/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_extract_inner_photo_pay_refund_label_extractor.h"

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/label_extractor/inner_photo_pay_refund_label_extractor.h"

namespace ks {
namespace ad_algorithm {

std::vector<int64_t> BSExtractInnerPhotoPayRefundLabelExtractor::operator()(const BSLog& bslog,
                                                                            size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return {};
  }
  auto x0 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000222, pos);
  auto x1 = inner_photo_pay_refund_label_extractor(x0);

  std::vector<int64_t> res = {static_cast<int64_t>(x1)};
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
