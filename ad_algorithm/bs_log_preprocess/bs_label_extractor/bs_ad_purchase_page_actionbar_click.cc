/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_ad_purchase_page_actionbar_click.h"

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/label_extractor/ad_purchase_page_actionbar_click.h"

namespace ks {
namespace ad_algorithm {

std::vector<int64_t> BSAdPurchasePageActionbarClick::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return {};
  }
  auto x0 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_977, pos);
  auto x1 = ad_purchase_page_actionbar_click(x0);

  std::vector<int64_t> res = {static_cast<int64_t>(x1)};
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
