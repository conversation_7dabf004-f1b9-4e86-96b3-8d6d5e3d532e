/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_inner_live_pay_refund_label_extractor.h"

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/label_extractor/inner_live_pay_refund_label_extractor.h"

namespace ks {
namespace ad_algorithm {

std::vector<int64_t> BSInnerLivePayRefundLabelExtractor::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return {};
  }
  auto x0 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
  auto x1 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000059, pos);
  auto x2 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_72, pos);
  auto x3 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_572, pos);
  auto x4 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000012, pos);
  auto x5 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000015, pos);
  auto x6 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000014, pos);
  auto x7 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000027, pos);
  auto x8 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000047, pos);
  auto x9 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000078, pos);
  auto x10 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_301, pos);
  auto x11 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_307, pos);
  auto x12 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_308, pos);
  auto x13 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000001, pos);
  auto x14 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_68, pos);
  auto x15 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_type, pos);
  auto x16 = get_bslog_int64_or_default(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_live_creative_type, pos);
  auto x17 = get_bslog_bool_or_default(
      *bs, BSFieldEnum::adlog_item_fans_top_info_photo_info_retrieval_photo_info_is_living, pos);
  auto x18 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_context_sub_page_id, pos);
  auto x19 = get_bslog_str_or_default(*bs, BSFieldEnum::adlog_context_app_id, pos);
  auto x20 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10001004, pos);
  auto x21 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_728, pos);
  auto x22 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000049, pos);
  auto x23 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_902, pos);
  auto x24 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_context_pos_id, pos);
  auto x25 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_901, pos);
  auto x26 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_958, pos);
  auto x27 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10001060, pos);
  auto x28 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_303, pos);
  auto x29 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000012, pos);
  auto x30 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_65, pos);
  auto x31 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000222, pos);
  auto x32 = inner_live_pay_refund_label_extractor(x0, x1, x2, x3, x4, x5, x6, x7, x8, x9, x10, x11, x12, x13,
                                                   x14, x15, x16, x17, x18, x19, x20, x21, x22, x23, x24, x25,
                                                   x26, x27, x28, x29, x30, x31);

  std::vector<int64_t> res = {static_cast<int64_t>(x32)};
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
