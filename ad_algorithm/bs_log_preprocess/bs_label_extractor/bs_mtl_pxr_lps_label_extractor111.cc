#include <string>
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_mtl_pxr_lps_label_extractor111.h"

namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSMtlPxrLpsLabelExtractor111::BSMtlPxrLpsLabelExtractor111(const std::string& conf)
    : bs_mtl_pxr_lps_label_extractor4_(conf) {}

std::vector<int64_t> BSMtlPxrLpsLabelExtractor111::operator()(const BSLog& bslog,
                                                            size_t pos) const {
  std::vector<int64_t> old_res = bs_mtl_pxr_lps_label_extractor4_(bslog, pos);
  if (old_res.size() == 0) {
    return old_res;
  }
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return old_res;
  }
  std::vector<int64_t> res;
  auto key_num = BSFieldEnum::adlog_item_label_info_label_infos_key_20000062;
  uint64_t lps_num = BSFieldHelper::GetSingular<uint64_t>(*bs, key_num, pos);

  int64_t label = old_res[0];
  // label 的 13 14 15 位置给到 lps_num, 值 > 8 截断
  if (lps_num >= 8) {
    lps_num = 7;
  }
  label |= static_cast<int64_t>(lps_num << 13);

  // label 的 16 17 位置给到:interactive_form & ocpc_action_type  53(lps) 190(pay) 383(grant)
  // 2&53 : 0 单列表单
  // !2&53 : 1 双列+联盟表单
  // 190 : 2 付费中表单
  // 383 : 3 授信中表单
  auto key_interactive_form = BSFieldEnum::adlog_context_info_common_attr_key_14;
  bool has_value = false;
  int interactive_form = BSFieldHelper::GetSingular<int, true>(*bs, key_interactive_form, pos, &has_value);
  auto key_action_type = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type;
  int ocpc_action_type = BSFieldHelper::GetSingular<int>(*bs, key_action_type, pos);
  int inter_ocpc = 0;
  if (interactive_form == 2 && ocpc_action_type == 53) {
    inter_ocpc = 0;
  } else if (interactive_form != 2 && ocpc_action_type == 53) {
    inter_ocpc = 1;
  } else if (ocpc_action_type == 190) {
    inter_ocpc = 2;
  } else if (ocpc_action_type == 383) {
    inter_ocpc = 3;
  }
  label |= static_cast<int64_t>(inter_ocpc << 16);

  res.push_back(label);
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
