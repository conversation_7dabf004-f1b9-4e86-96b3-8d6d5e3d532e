#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/label_extractor/inner_live_pay_fastemit_label_extractor.dark
class BSInnerLivePayFastemitLabelExtractor : public BSLabelExtractorBase {
 public:
  explicit BSInnerLivePayFastemitLabelExtractor(const std::string&) {}
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSInnerLivePayFastemitLabelExtractor,
                "bs_inner_live_pay_fastemit_label_extractor");
}  // namespace ad_algorithm
}  // namespace ks
