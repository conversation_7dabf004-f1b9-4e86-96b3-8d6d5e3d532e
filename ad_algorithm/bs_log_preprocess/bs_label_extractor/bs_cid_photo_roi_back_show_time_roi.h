#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/label_extractor/cid_photo_roi_back_show_time_roi.dark
class BSCidPhotoRoiBackShowTimeRoi : public BSLabelExtractorBase {
 public:
  explicit BSCidPhotoRoiBackShowTimeRoi(const std::string&) {}
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSCidPhotoRoiBackShowTimeRoi, "bs_cid_photo_roi_back_show_time_roi");
}  // namespace ad_algorithm
}  // namespace ks
