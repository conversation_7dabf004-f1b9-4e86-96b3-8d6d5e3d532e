#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/label_extractor/mtl_ecom_lps_order_label_extractor_v34_qcpx.dark
class BSMtlEcomLpsOrderLabelExtractorV34Qcpx : public BSLabelExtractorBase {
 public:
  explicit BSMtlEcomLpsOrderLabelExtractorV34Qcpx(const std::string&) {}
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSMtlEcomLpsOrderLabelExtractorV34Qcpx,
                "bs_mtl_ecom_lps_order_label_extractor_v_34_qcpx");
}  // namespace ad_algorithm
}  // namespace ks
