#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/label_extractor/ad_purchase_page_actionbar_click.dark
class BSAdPurchasePageActionbarClick : public BSLabelExtractorBase {
 public:
  explicit BSAdPurchasePageActionbarClick(const std::string&) {}
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSAdPurchasePageActionbarClick, "bs_ad_purchase_page_actionbar_click");
}  // namespace ad_algorithm
}  // namespace ks
