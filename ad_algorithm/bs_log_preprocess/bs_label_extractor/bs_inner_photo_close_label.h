#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/label_extractor/inner_photo_close_label.dark
class BSInnerPhotoCloseLabel : public BSLabelExtractorBase {
 public:
  explicit BSInnerPhotoCloseLabel(const std::string&) {}
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSInnerPhotoCloseLabel, "bs_inner_photo_close_label");
}  // namespace ad_algorithm
}  // namespace ks
