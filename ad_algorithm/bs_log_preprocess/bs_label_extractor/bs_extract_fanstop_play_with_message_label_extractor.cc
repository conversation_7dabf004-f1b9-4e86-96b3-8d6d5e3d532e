/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_extract_fanstop_play_with_message_label_extractor.h"

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/label_extractor/fanstop_play_with_message_label_extractor.h"

namespace ks {
namespace ad_algorithm {

std::vector<int64_t> BSExtractFanstopPlayWithMessageLabelExtractor::operator()(const BSLog& bslog,
                                                                               size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return {};
  }
  auto x0 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_type, pos);
  auto x1 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
  auto x2 = get_bslog_int64_or_default(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_photo_attribute_duration_ms, pos);
  auto x3 = get_bslog_int64_or_default(
      *bs, BSFieldEnum::adlog_item_fans_top_info_photo_info_photo_attribute_duration_ms, pos);
  auto x4 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_feedback_negative, pos);
  auto x5 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_72, pos);
  auto x6 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_572, pos);
  auto x7 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000006, pos);
  auto x8 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_13, pos);
  auto x9 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000525, pos);
  auto x10 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000064, pos);
  auto x11 = fanstop_play_with_message_label_extractor(x0, x1, x2, x3, x4, x5, x6, x7, x8, x9, x10);

  std::vector<int64_t> res = {static_cast<int64_t>(x11)};
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
