#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/label_extractor/mtl_compatible_live_paycnt_label_extractor15_account_live.dark
class BSExtractCompatMTLLivePay15LabelExtractorV2 : public BSLabelExtractorBase {
 public:
  explicit BSExtractCompatMTLLivePay15LabelExtractorV2(const std::string&) {}
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSExtractCompatMTLLivePay15LabelExtractorV2,
                "bs_extract_compat_m_t_l_live_pay_15_label_extractor_v_2");
}  // namespace ad_algorithm
}  // namespace ks
