#pragma once

#include <string>
#include <vector>
#include <utility>
#include<unordered_map>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

class BSFanstopWithNegLabelExtractor : public BSLabelExtractorBase {
 public:
  struct EnumClassHash {
    template <typename T>
    std::size_t operator()(T t) const {
      return static_cast<std::size_t>(t);
    }
  };

  explicit BSFanstopWithNegLabelExtractor(const std::string& conf);
  virtual std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const;

 private:
  std::unordered_map<int, int, EnumClassHash> label_value_;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSFanstopWithNegLabelExtractor, "bs_fanstop_with_neg_label_extractor");

}  // namespace ad_algorithm
}  // namespace ks
