#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/label_extractor/mtl_ecom_lps_order_label_extractor_v32_gmv.dark
class BSMtlEcomLpsOrderLabelExtractorV32Gmv : public BSLabelExtractorBase {
 public:
  explicit BSMtlEcomLpsOrderLabelExtractorV32Gmv(const std::string&) {}
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSMtlEcomLpsOrderLabelExtractorV32Gmv,
                "bs_mtl_ecom_lps_order_label_extractor_v_32_gmv");
}  // namespace ad_algorithm
}  // namespace ks
