#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/label_extractor/extract_prms_mtl_label_v2_extractor.dark
class BSExtractPrmsMtlLabelV2Extractor : public BSLabelExtractorBase {
 public:
  explicit BSExtractPrmsMtlLabelV2Extractor(const std::string&) {}
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSExtractPrmsMtlLabelV2Extractor,
                "bs_extract_prms_mtl_label_v_2_extractor");
}  // namespace ad_algorithm
}  // namespace ks
