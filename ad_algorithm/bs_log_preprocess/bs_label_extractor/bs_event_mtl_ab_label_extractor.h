#pragma once

#include<unordered_map>
#include <string>
#include <vector>
#include <utility>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

class BSEventMtlAbLabelExtractor : public BSLabelExtractorBase {
 public:
  struct EnumClassHash {
    template <typename T>
    std::size_t operator()(T t) const {
      return static_cast<std::size_t>(t);
    }
  };

  explicit BSEventMtlAbLabelExtractor(const std::string& conf);
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;

 private:
  std::unordered_map<int, std::vector<int>, EnumClassHash> label_map;
  int neg_sample_rate = 0;
  bool is_neg_sample = false;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSEventMtlAbLabelExtractor, "bs_event_mtl_ab_label_extractor");

}  // namespace ad_algorithm
}  // namespace ks
