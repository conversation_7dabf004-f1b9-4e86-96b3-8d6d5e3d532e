#include <utility>
#include <string>
#include "base/strings/string_split.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/log_preprocess/ad_model_kconf_util.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_mtl_compatible_live_paycnt_label_extractor3_kaiworks.h"


namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSMTLCompatibleLivePaycntLabelExtractor3Kaiwork::BSMTLCompatibleLivePaycntLabelExtractor3Kaiwork(
    const std::string& conf) {}

std::vector<int64_t> BSMTLCompatibleLivePaycntLabelExtractor3Kaiwork::operator()(const BSLog& bslog,
                                                                                 size_t pos) const {
  std::vector<int64_t> res;
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return res;
  }

  auto key_label_ad_merchat_follow = BSFieldEnum::adlog_item_label_info_label_infos_key_72;
  bool has_ad_merchat_follow = BSFieldHelper::GetSingular<bool>(*bs, key_label_ad_merchat_follow, pos);

  auto key_label_ad_fans_top_follow = BSFieldEnum::adlog_item_label_info_label_infos_key_572;
  bool has_ad_fans_top_follow = BSFieldHelper::GetSingular<bool>(*bs, key_label_ad_fans_top_follow, pos);

  auto key_label_goods_view = BSFieldEnum::adlog_item_label_info_label_infos_key_10000012;
  bool has_goods_view = BSFieldHelper::GetSingular<bool>(*bs, key_label_goods_view, pos);

  auto key_label_event_order_paied = BSFieldEnum::adlog_item_label_info_label_infos_key_10000015;
  bool has_event_order_paied = BSFieldHelper::GetSingular<bool>(*bs, key_label_event_order_paied, pos);

  auto key_label_mix_front_order_paid_cnt = BSFieldEnum::adlog_item_label_info_label_infos_key_20000027;
  bool has_mix_front_order_paid_cnt =
      BSFieldHelper::GetSingular<bool>(*bs, key_label_mix_front_order_paid_cnt, pos);

  auto key_label_mix_front_purchase_amount = BSFieldEnum::adlog_item_label_info_label_infos_key_20000026;
  bool has_mix_front_purchase_amount =
      BSFieldHelper::GetSingular<bool>(*bs, key_label_mix_front_purchase_amount, pos);

  auto key_label_order_paid_cnt = BSFieldEnum::adlog_item_label_info_label_infos_key_20000014;
  bool has_order_paid_cnt = BSFieldHelper::GetSingular<bool>(*bs, key_label_order_paid_cnt, pos);

  auto key_label_purchase_amount = BSFieldEnum::adlog_item_label_info_label_infos_key_20000015;
  bool has_purchase_amount = BSFieldHelper::GetSingular<bool>(*bs, key_label_purchase_amount, pos);

  auto key_label_live_like = BSFieldEnum::adlog_item_label_info_label_infos_key_301;
  bool has_live_like = BSFieldHelper::GetSingular<bool>(*bs, key_label_live_like, pos);

  auto key_label_live_shop_cart_click = BSFieldEnum::adlog_item_label_info_label_infos_key_307;
  bool has_live_shop_cart_click = BSFieldHelper::GetSingular<bool>(*bs, key_label_live_shop_cart_click, pos);

  auto key_label_live_shop_link_jump = BSFieldEnum::adlog_item_label_info_label_infos_key_308;
  bool has_live_shop_link_jump = BSFieldHelper::GetSingular<bool>(*bs, key_label_live_shop_link_jump, pos);

  auto key_label_standard_live_play_started = BSFieldEnum::adlog_item_label_info_label_infos_key_20000001;
  bool has_standard_live_play_started =
      BSFieldHelper::GetSingular<bool>(*bs, key_label_standard_live_play_started, pos);

  auto key_label_live_play_started = BSFieldEnum::adlog_item_label_info_label_infos_key_68;
  bool has_live_play_started = BSFieldHelper::GetSingular<bool>(*bs, key_label_live_play_started, pos);

  auto key_label_mix_flag = BSFieldEnum::adlog_item_label_info_label_infos_key_20000059;
  bool has_mix_flag = BSFieldHelper::GetSingular<bool>(*bs, key_label_mix_flag, pos);

  int label = 0;
  if (has_mix_flag) {
    uint64 mix_flag_val = BSFieldHelper::GetSingular<int>(*bs, key_label_mix_flag, pos);
    if (mix_flag_val == 1) {
      label |= 1;
    }
  }

  if (has_ad_merchat_follow || has_ad_fans_top_follow) {
    label |= 2;
  }

  if (has_goods_view) {
    label |= 4;
  }

  if (has_event_order_paied) {
    label |= 8;
  }

  if (has_order_paid_cnt) {
    uint64 order_paid_cnt_val = BSFieldHelper::GetSingular<int>(*bs, key_label_order_paid_cnt, pos);
    if (order_paid_cnt_val > 31) {
      order_paid_cnt_val = 31;
    }

    label |= (order_paid_cnt_val << 4);
    if (order_paid_cnt_val >= 1) {
      label |= 8;
    }
  }

  if (has_mix_front_order_paid_cnt) {
    uint64 order_paid_cnt_val = BSFieldHelper::GetSingular<int>(*bs, key_label_mix_front_order_paid_cnt, pos);
    if (order_paid_cnt_val > 31) {
      order_paid_cnt_val = 31;
    }
    label |= (order_paid_cnt_val << 9);
    if (order_paid_cnt_val >= 1) {
      label |= 8;
    }
  }

  if (has_live_like) {
    label |= (1 << 14);
  }

  if (has_live_shop_cart_click) {
    label |= (1 << 15);
  }

  if (has_live_shop_link_jump) {
    label |= (1 << 16);
  }

  if (has_standard_live_play_started) {
    label |= (1 << 17);
  }

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);
  if (item_type == bs::ItemType::AD_BRAND) {
    if (has_standard_live_play_started || has_live_play_started) {
      label |= (1 << 17);
    }
  }

  auto enum_campaign_base_exists =
      BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists;
  bool has_campaign_base =
      BSFieldHelper::GetSingular<bool>(*bs, enum_campaign_base_exists, pos);
  bool has_creative = BSFieldHelper::GetSingular<bool>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_exists, pos);
  if (item_type == bs::ItemType::AD_DSP && has_campaign_base && has_creative) {
    int64_t creative_type = BSFieldHelper::GetSingular<int64_t>(*bs,
        BSFieldEnum::adlog_item_ad_dsp_info_creative_base_live_creative_type, pos);

    if (creative_type != 1) {
      if (has_live_play_started) {
        label |= (1 << 17);
      }
    }
  }

  auto enum_sub_page_id = BSFieldEnum::adlog_context_sub_page_id;
  int64_t sub_page_id =
      BSFieldHelper::GetSingular<int64_t>(*bs, enum_sub_page_id);
  if (sub_page_id == 100011292 || sub_page_id == 100011503 || sub_page_id == 100012061 ||
      AdModelKconfUtil::inspireLiveSubpageConf()->count(sub_page_id)) {
    label |= (1 << 18);
  }

  res.push_back(label);

  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
