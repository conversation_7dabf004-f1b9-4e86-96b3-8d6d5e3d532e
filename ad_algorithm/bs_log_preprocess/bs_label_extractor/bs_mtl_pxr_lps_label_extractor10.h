#pragma once

#include <string>
#include <vector>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_mtl_pxr_lps_label_extractor41.h"

namespace ks {
namespace ad_algorithm {

class BSMtlPxrLpsLabelExtractor10 : public BSLabelExtractorBase {
 public:
  explicit BSMtlPxrLpsLabelExtractor10(const std::string& conf);
  virtual std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const;

 private:
  BSMtlPxrLpsLabelExtractor41 bs_mtl_pxr_lps_label_extractor41_;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSMtlPxrLpsLabelExtractor10, "bs_mtl_pxr_lps_label_extractor10");

}  // namespace ad_algorithm
}  // namespace ks
