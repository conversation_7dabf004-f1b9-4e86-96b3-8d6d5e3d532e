#pragma once

#include <string>
#include <vector>
#include <utility>
#include<unordered_map>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

class BSMtlNewPxrLpsLabelExtractor42 : public BSLabelExtractorBase {
 public:
  explicit BSMtlNewPxrLpsLabelExtractor42(const std::string& conf);
  virtual std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSMtlNewPxrLpsLabelExtractor42, "bs_mtl_new_pxr_lps_label_extractor42");

}  // namespace ad_algorithm
}  // namespace ks



