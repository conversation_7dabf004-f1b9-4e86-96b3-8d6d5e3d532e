/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_inner_loop_short_video_ssb_label_extractor.h"

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/label_extractor/inner_loop_short_video_ssb_label_extractor.h"

namespace ks {
namespace ad_algorithm {

std::vector<int64_t> BSInnerLoopShortVideoSsbLabelExtractor::operator()(const BSLog& bslog,
                                                                        size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return {};
  }
  auto x0 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000046, pos);
  auto x1 = get_bslog_float(*bs, BSFieldEnum::adlog_item_label_info_label_info_attr_key_1105, pos);
  auto x2 = inner_loop_short_video_ssb_label_extractor(x0, x1);

  std::vector<int64_t> res = {static_cast<int64_t>(x2)};
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
