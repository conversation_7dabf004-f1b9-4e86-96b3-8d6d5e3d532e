/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_mtl_pxr_invoke_label_extractor_new.h"

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/label_extractor/mtl_pxr_invoke_label_extractor_new.h"

namespace ks {
namespace ad_algorithm {

std::vector<int64_t> BSMtlPxrInvokeLabelExtractorNew::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return {};
  }
  auto x0 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_1, pos);
  auto x1 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_2, pos);
  auto x2 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000006, pos);
  auto x3 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_13, pos);
  auto x4 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_30000001, pos);
  auto x5 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_15, pos);
  auto x6 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_918, pos);
  auto x7 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000020, pos);
  auto x8 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000002, pos);
  auto x9 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000011, pos);
  auto x10 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_804, pos);
  auto x11 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000061, pos);
  auto x12 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_958, pos);
  auto x13 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_320, pos);
  auto x14 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_30000004, pos);
  auto x15 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000084, pos);
  auto x16 = mtl_pxr_invoke_label_extractor_new(x0, x1, x2, x3, x4, x5, x6, x7, x8, x9, x10, x11, x12, x13,
                                                x14, x15);

  std::vector<int64_t> res = {static_cast<int64_t>(x16)};
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
