/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_extract_mtl_feed_ctr_photo_ts_label_extractor.h"

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/label_extractor/mtl_feed_ctr_photo_imp_ts_label_extractor_v1.h"

namespace ks {
namespace ad_algorithm {

std::vector<int64_t> BSExtractMtlFeedCtrPhotoTsLabelExtractor::operator()(const BSLog& bslog,
                                                                          size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return {};
  }
  auto x0 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_played_5s, pos);
  auto x1 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_click, pos);
  auto x2 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000104, pos);
  auto x3 = get_bslog_int64_or_default<true>(*bs, BSFieldEnum::adlog_context_pos_id, pos);
  auto x4 = get_bslog_time(*bs);
  auto x5 = mtl_feed_ctr_photo_imp_ts_label_extractor_v1(x0, x1, x2, x3, x4);

  std::vector<int64_t> res = {static_cast<int64_t>(x5)};
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
