#pragma once
/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"
#include <vector>
#include <string>

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/label_extractor/extract_prms_mtl_label_extractor.dark
class BSExtractPrmsMtlLabelExtractor : public BSLabelExtractorBase {
 public:
  explicit BSExtractPrmsMtlLabelExtractor(const std::string&) {}
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSExtractPrmsMtlLabelExtractor, "bs_extract_prms_mtl_label_extractor");
}  // namespace ad_algorithm
}  // namespace ks
