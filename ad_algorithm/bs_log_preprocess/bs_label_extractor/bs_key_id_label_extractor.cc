#include <utility>
#include <string>
#include "base/strings/string_split.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_key_id_label_extractor.h"


namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSKeyIdLabelExtractor::BSKeyIdLabelExtractor(const std::string& conf) {
  std::vector<std::string> results;
  base::SplitString(conf, std::string(",", 1), &results);
  std::vector<std::string> seg;
  for (const auto& str : results) {
    seg.clear();
    base::SplitString(str, std::string("|@"), &seg);
    if (seg.size() != 2) {
      LOG(FATAL) << "label config error in " << conf << ", error string " << str;
    }
    // 具体的 id 值可以参考 bs_fanstop_with_neg_label_extractor.cc,
    // 或者参考 https://kconf.corp.kuaishou.com/#/ad/algorithm_feature/featureConfigMap
    int id = atoi(seg[0].c_str());
    int val = atoi(seg[1].c_str());
    label_value_.push_back(std::make_pair(id, val));
    LOG(INFO) << "add: id=" << id << ", val=" << val;
  }
}

std::vector<int64_t> BSKeyIdLabelExtractor::operator()(const BSLog& bslog, size_t pos) const {
  std::vector<int64_t> res;
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return res;
  }

  int label = 0;
  for (int i = label_value_.size() - 1; i >= 0; i--) {
    int id = label_value_[i].first;
    int val = label_value_[i].second;
    bool checked = BSFieldHelper::GetSingular<bool>(*bs, id, pos);
    LOG_EVERY_N(INFO, 1000000) << "i:" << i << ", id: " << id << ", val:" << val
              << ", checked:" << checked;
    if (checked) {
      label = val;
      LOG_EVERY_N(INFO, 10000) << "id: " << id << ", val:" << val
                             << ", label: " << label;
      break;
    }
  }
  LOG_EVERY_N(INFO, 1000000) << "label:" << label;
  res.push_back(label);
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
