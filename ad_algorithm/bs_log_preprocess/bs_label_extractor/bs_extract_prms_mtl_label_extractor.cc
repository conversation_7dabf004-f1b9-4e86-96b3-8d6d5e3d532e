/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_extract_prms_mtl_label_extractor.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/label_extractor/prms_mtl_label_extractor.h"

namespace ks {
namespace ad_algorithm {

std::vector<int64_t> BSExtractPrmsMtlLabelExtractor::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return {};
  }
  auto x0 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_item_impression, pos);
  auto x1 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_item_click, pos);
  auto x2 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_786, pos);
  auto x3 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000525, pos);
  auto x4 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_played_3s, pos);
  auto x5 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_played_5s, pos);
  auto x6 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_played_end, pos);
  auto x7 = prms_mtl_label_extractor(x0, x1, x2, x3, x4, x5, x6);

  std::vector<int64_t> res = { static_cast<int64_t>(x7) };
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
