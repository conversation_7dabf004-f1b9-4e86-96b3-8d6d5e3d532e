#pragma once
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

class BSMTLCompatibleLivePaycntLabelExtractor3Kaiwork : public BSLabelExtractorBase {
 public:
  struct EnumClassHash {
    template <typename T>
    std::size_t operator()(T t) const {
      return static_cast<std::size_t>(t);
    }
  };

  explicit BSMTLCompatibleLivePaycntLabelExtractor3Kaiwork(const std::string &conf);
  virtual std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSMTLCompatibleLivePaycntLabelExtractor3Kaiwork,
                "bs_mtl_compatible_live_paycnt_label_extractor3_kaiworks");
}  // namespace ad_algorithm
}  // namespace ks
