/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_mtl_new_pxr_lps_label_extractor_12.h"

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/label_extractor/mtl_pxr_lps_label_extractor_v2.h"

namespace ks {
namespace ad_algorithm {

std::vector<int64_t> BSMtlNewPxrLpsLabelExtractor12::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return {};
  }
  auto x0 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_item_impression, pos);
  auto x1 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_item_click, pos);
  auto x2 =
      get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_landing_page_form_submitted, pos);
  auto x3 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_played_3s, pos);
  auto x4 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_played_5s, pos);
  auto x5 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_played_end, pos);
  auto x6 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_approximate_purchase, pos);
  auto x7 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
  auto x8 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type, pos);
  auto x9 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000183, pos);
  auto x10 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000022, pos);
  auto x11 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_id, pos);
  auto x12 = get_bslog_int64_or_default(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_first_id, pos);
  auto x13 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_786, pos);
  auto x14 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000525, pos);
  auto x15 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000118, pos);
  auto x16 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000062, pos);
  auto x17 = get_bslog_int64<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_14, pos);
  auto x18 = mtl_new_pxr_lps_label_extractor10(x0, x1, x2, x3, x4, x5, x6, x7, x8, x9, x10, x11, x12, x13,
                                               x14, x15, x16, x17);

  std::vector<int64_t> res = {static_cast<int64_t>(x18)};
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
