#include <utility>
#include <string>
#include "base/strings/string_split.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_fanstop_with_neg_label_extractor.h"


namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSFanstopWithNegLabelExtractor::BSFanstopWithNegLabelExtractor(const std::string& conf) {
}

std::vector<int64_t> BSFanstopWithNegLabelExtractor::operator()(const BSLog& bslog, size_t pos) const {
  std::vector<int64_t> res;
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return res;
  }

  auto key_feedback_negative = BSFieldEnum::adlog_item_label_info_feedback_negative;
  bool feedback_negative = BSFieldHelper::GetSingular<bool>(*bs, key_feedback_negative, pos);

  auto key_like = BSFieldEnum::adlog_item_label_info_like;
  bool like = BSFieldHelper::GetSingular<bool>(*bs, key_like, pos);

  auto key_follow = BSFieldEnum::adlog_item_label_info_follow;
  bool follow = BSFieldHelper::GetSingular<bool>(*bs, key_follow, pos);

  auto key_played_3s = BSFieldEnum::adlog_item_label_info_played_3s;
  bool played_3s = BSFieldHelper::GetSingular<bool>(*bs, key_played_3s, pos);

  auto key_played_5s = BSFieldEnum::adlog_item_label_info_played_5s;
  bool played_5s = BSFieldHelper::GetSingular<bool>(*bs, key_played_5s, pos);

  auto key_click_to_live = BSFieldEnum::adlog_item_label_info_click_to_live;
  bool click_to_live = BSFieldHelper::GetSingular<bool>(*bs, key_click_to_live, pos);

  auto key_live_shop_cart_click = BSFieldEnum::adlog_item_label_info_live_shop_cart_click;
  bool live_shop_cart_click = BSFieldHelper::GetSingular<bool>(*bs, key_live_shop_cart_click, pos);

  auto key_live_shop_link_jump = BSFieldEnum::adlog_item_label_info_live_shop_link_jump;
  bool live_shop_link_jump = BSFieldHelper::GetSingular<bool>(*bs, key_live_shop_link_jump, pos);

  auto key_standard_live_played_started = BSFieldEnum::adlog_item_label_info_standard_live_played_started;
  bool standard_live_played_started = BSFieldHelper::GetSingular<bool>(*bs,
    key_standard_live_played_started, pos);

  auto key_live_played_started = BSFieldEnum::adlog_item_label_info_live_played_started;
  bool live_played_started = BSFieldHelper::GetSingular<bool>(*bs, key_live_played_started, pos);

  auto key_order_paid = BSFieldEnum::adlog_item_label_info_order_paid;
  bool order_paid = BSFieldHelper::GetSingular<bool>(*bs, key_order_paid, pos);

  auto key_delivery = BSFieldEnum::adlog_item_label_info_delivery;
  bool delivery = BSFieldHelper::GetSingular<bool>(*bs, key_delivery, pos);

  auto key_item_impression = BSFieldEnum::adlog_item_label_info_item_impression;
  bool item_impression = BSFieldHelper::GetSingular<bool>(*bs, key_item_impression, pos);

  auto key_feed_impression = BSFieldEnum::adlog_item_label_info_feed_impression;
  bool feed_impression = BSFieldHelper::GetSingular<bool>(*bs, key_feed_impression, pos);

  auto key_shop_cart_click = BSFieldEnum::adlog_item_label_info_shop_cart_click;
  bool shop_cart_click = BSFieldHelper::GetSingular<bool>(*bs, key_shop_cart_click, pos);

  // 额外 label 信息应该从 label_infos 读取了
  auto key_label_infos_merchant_follow = BSFieldEnum::adlog_item_label_info_label_infos_key_72;
  follow |= BSFieldHelper::GetSingular<bool>(*bs, key_label_infos_merchant_follow, pos);
  auto key_label_infos_goods_view = BSFieldEnum::adlog_item_label_info_label_infos_key_10000012;
  shop_cart_click |= BSFieldHelper::GetSingular<bool>(*bs, key_label_infos_goods_view, pos);
  auto key_label_infos_photo_p5s = BSFieldEnum::adlog_item_label_info_label_infos_key_22;
  played_5s |= BSFieldHelper::GetSingular<bool>(*bs, key_label_infos_photo_p5s, pos);
  auto key_label_infos_direct_follow = BSFieldEnum::adlog_item_label_info_label_infos_key_30000001;
  follow |= BSFieldHelper::GetSingular<bool>(*bs, key_label_infos_direct_follow, pos);
  auto key_label_infos_item_impression = BSFieldEnum::adlog_item_label_info_label_infos_key_1;
  item_impression |= BSFieldHelper::GetSingular<bool>(*bs, key_label_infos_item_impression, pos);
  auto key_label_infos_feed_impression = BSFieldEnum::adlog_item_label_info_label_infos_key_20000004;
  feed_impression |= BSFieldHelper::GetSingular<bool>(*bs, key_label_infos_feed_impression, pos);
  auto key_label_infos_played_5s = BSFieldEnum::adlog_item_label_info_label_infos_key_20000002;
  played_5s |= BSFieldHelper::GetSingular<bool>(*bs, key_label_infos_played_5s, pos);
  auto key_label_infos_click_to_live = BSFieldEnum::adlog_item_label_info_label_infos_key_20000003;
  click_to_live |= BSFieldHelper::GetSingular<bool>(*bs, key_label_infos_click_to_live, pos);
  auto key_label_infos_shop_item_click = BSFieldEnum::adlog_item_label_info_label_infos_key_307;
  shop_cart_click |= BSFieldHelper::GetSingular<bool>(*bs, key_label_infos_shop_item_click, pos);
  auto key_label_infos_photo_to_profile = BSFieldEnum::adlog_item_label_info_label_infos_key_120;
  click_to_live |= BSFieldHelper::GetSingular<bool>(*bs, key_label_infos_photo_to_profile, pos);
  auto key_label_infos_live_shop_link_jump = BSFieldEnum::adlog_item_label_info_label_infos_key_308;
  live_shop_link_jump |= BSFieldHelper::GetSingular<bool>(*bs, key_label_infos_live_shop_link_jump, pos);
  auto key_label_infos_follow = BSFieldEnum::adlog_item_label_info_label_infos_key_572;
  follow |= BSFieldHelper::GetSingular<bool>(*bs, key_label_infos_follow, pos);
  auto key_label_infos_order_paid = BSFieldEnum::adlog_item_label_info_label_infos_key_10000015;
  order_paid |= BSFieldHelper::GetSingular<bool>(*bs, key_label_infos_order_paid, pos);
  auto key_label_infos_live_played_started = BSFieldEnum::adlog_item_label_info_label_infos_key_68;
  live_played_started |= BSFieldHelper::GetSingular<bool>(*bs, key_label_infos_live_played_started, pos);
  auto key_label_infos_standard_live_played_started =
    BSFieldEnum::adlog_item_label_info_label_infos_key_20000001;
  standard_live_played_started |= BSFieldHelper::GetSingular<bool>(*bs,
    key_label_infos_standard_live_played_started, pos);

  int label = 0;

  if (!feedback_negative) {
    if (delivery) {
      label |= (1<<21);
    }
    if (shop_cart_click) {
      label |= 1;
    }
    if (item_impression) {
      label |= (1<<6);
    }
    if (feed_impression) {
      label |= (1<<7);
    }
    if (like) {
      label |= (1<<8);
    }
    if (follow) {
      label |= (1<<9);
    }
    if (played_3s) {
      label |= (1<<10);
    }
    if (played_5s) {
      label |= (1<<11);
    }
    if (click_to_live) {
      label |= (1<<12);
    }
    if (live_shop_cart_click) {
      label |= (1<<13);
    }
    if (live_shop_link_jump) {
      label |= (1<<14);
    }
    if (standard_live_played_started) {
       label |= (1<<15);
    }

    if (live_played_started) {
       label |= (1<<16);
    }

    if (order_paid) {
       label |= (1<<17);
    }
  }
  LOG_EVERY_N(INFO, 100000) << "feedback_negative=" << feedback_negative
                            << ", delivery=" << delivery
                            << ", shop_cart_click=" << shop_cart_click
                            << ", item_impression=" << item_impression
                            << ", feed_impression=" << feed_impression
                            << ", like=" << like
                            << ", follow=" << follow
                            << ", played_3s=" << played_3s
                            << ", played_5s=" << played_5s
                            << ", click_to_live=" << click_to_live
                            << ", live_shop_cart_click=" << live_shop_cart_click
                            << ", live_shop_link_jump=" << live_shop_link_jump
                            << ", standard_live_played_started=" << standard_live_played_started
                            << ", live_played_started=" << live_played_started
                            << ", order_paid=" << order_paid;


  res.push_back(label);

  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
