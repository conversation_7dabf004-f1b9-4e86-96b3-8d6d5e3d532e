#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/label_extractor/inner_shelf_merchant_pos_label_extractor_v0.dark
class BSInnerShelfMerchantPosLabelExtractorV0 : public BSLabelExtractorBase {
 public:
  explicit BSInnerShelfMerchantPosLabelExtractorV0(const std::string&) {}
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSInnerShelfMerchantPosLabelExtractorV0,
                "bs_inner_shelf_merchant_pos_label_extractor_v_0");
}  // namespace ad_algorithm
}  // namespace ks
