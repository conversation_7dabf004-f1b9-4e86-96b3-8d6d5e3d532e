#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/label_extractor/extract_prms_follow_mtl_label_extractor.dark
class BSExtractPrmsFollowMtlLabelExtractor : public BSLabelExtractorBase {
 public:
  explicit BSExtractPrmsFollowMtlLabelExtractor(const std::string&) {}
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSExtractPrmsFollowMtlLabelExtractor,
                "bs_extract_prms_follow_mtl_label_extractor");
}  // namespace ad_algorithm
}  // namespace ks
