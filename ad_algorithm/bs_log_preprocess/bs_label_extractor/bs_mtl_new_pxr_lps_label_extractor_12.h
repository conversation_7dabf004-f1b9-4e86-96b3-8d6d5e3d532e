#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/label_extractor/mtl_pxr_lps_label_extractor_v4.dark
class BSMtlNewPxrLpsLabelExtractor12 : public BSLabelExtractorBase {
 public:
  explicit BSMtlNewPxrLpsLabelExtractor12(const std::string&) {}
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSMtlNewPxrLpsLabelExtractor12,
                "bs_mtl_new_pxr_lps_label_extractor_12");
}  // namespace ad_algorithm
}  // namespace ks
