#include <utility>
#include <string>
#include "base/strings/string_split.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_mtl_new_pxr_lps_label_extractor42.h"

namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSMtlNewPxrLpsLabelExtractor42::BSMtlNewPxrLpsLabelExtractor42(const std::string& conf) {
}

std::vector<int64_t> BSMtlNewPxrLpsLabelExtractor42::operator()(const BSLog& bslog, size_t pos) const {
  std::vector<int64_t> res;
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return res;
  }

  auto key_item_impression = BSFieldEnum::adlog_item_label_info_item_impression;
  bool item_impression = BSFieldHelper::GetSingular<bool>(*bs, key_item_impression, pos);

  auto key_item_click = BSFieldEnum::adlog_item_label_info_item_click;
  bool item_click = BSFieldHelper::GetSingular<bool>(*bs, key_item_click, pos);

  auto key_lps = BSFieldEnum::adlog_item_label_info_landing_page_form_submitted;
  bool lps = BSFieldHelper::GetSingular<bool>(*bs, key_lps, pos);

  auto key_played_3s = BSFieldEnum::adlog_item_label_info_played_3s;
  bool played_3s = BSFieldHelper::GetSingular<bool>(*bs, key_played_3s, pos);

  auto key_played_5s = BSFieldEnum::adlog_item_label_info_played_5s;
  bool played_5s = BSFieldHelper::GetSingular<bool>(*bs, key_played_5s, pos);

  auto key_played_end = BSFieldEnum::adlog_item_label_info_played_end;
  bool played_end = BSFieldHelper::GetSingular<bool>(*bs, key_played_end, pos);

  auto key_approximate_purchase = BSFieldEnum::adlog_item_label_info_approximate_purchase;
  bool approximate_purchase = BSFieldHelper::GetSingular<bool>(*bs, key_approximate_purchase, pos);

  auto key_campaign_type = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type;
  uint64_t ad_campaign_type = BSFieldHelper::GetSingular<uint64_t>(*bs, key_campaign_type, pos);

  auto key_action_type = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type;
  int ocpc_action_type = BSFieldHelper::GetSingular<int>(*bs, key_action_type, pos);

  auto key_label_submit_fill = BSFieldEnum::adlog_item_label_info_label_infos_key_10000183;
  bool submit_fill = BSFieldHelper::GetSingular<bool>(*bs, key_label_submit_fill, pos);

  auto key_label_leave_time = BSFieldEnum::adlog_item_label_info_label_infos_key_20000022;
  uint64_t leave_time = BSFieldHelper::GetSingular<uint64_t>(*bs, key_label_leave_time, pos);

  auto pm_integration_type_key = BSFieldEnum::adlog_item_label_info_label_info_attr_key_1109;
  int pm_integration_type = BSFieldHelper::GetSingular<int>(*bs, pm_integration_type_key, pos);
  // LOG_EVERY_N(INFO, 1) << "wrz debug pm_integration_type=" << pm_integration_type;

  // kuaishou::ad::AD_PURCHASE: 190
  bool if_ocpc_pay = (ocpc_action_type == 190)
                      &&(ad_campaign_type == 3 || ad_campaign_type == 4 || ad_campaign_type == 5);

  auto key_industry_id = BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_id;

  int label = 0;
  //   per  pxr   lps item_click item_impression
  //   0    0      0      0           0
  if (leave_time > 10000) {
    label |= 4096;
  }

  if (submit_fill) {
    label |= 2048;
  }

  if (approximate_purchase) {
    label |= 1024;
  }

  if (if_ocpc_pay) {
     label |= 256;
  }

  if (played_end) {
    label |= 32;
  }

  if (played_5s) {
    label |= 16;
  }

  if (played_3s) {
    label |= 8;
  }

  // leads_submit 也当做表单
  auto key = BSFieldEnum::adlog_item_label_info_label_infos_key_786;
  bool LEADS_SUBMIT = BSFieldHelper::GetSingular<bool>(*bs, key, pos);
  key = BSFieldEnum::adlog_item_label_info_label_infos_key_10000525;
  bool EVENT_PRIVATE_MESSAGE_SENT = BSFieldHelper::GetSingular<bool>(*bs, key, pos);
  if (lps) {
    label |= 4;
  }

  if (item_click) {
    label |= 2;
  }

  if (item_impression) {
    label |= 1;
  }

  int prm = 1;
  if (EVENT_PRIVATE_MESSAGE_SENT && (ocpc_action_type == 926)) {
    label |= static_cast<int64_t>(prm << 18);
  }

  int leads = 1;
  if (LEADS_SUBMIT && ((ocpc_action_type == 786) || (ocpc_action_type == 948 && ad_campaign_type == 24))) {
    label |= static_cast<int64_t>(leads << 19);
  }

  int pre_prm = 1;
  if (EVENT_PRIVATE_MESSAGE_SENT) {
    label |= static_cast<int64_t>(pre_prm << 13);
  }
  int leads_post = 1;
  if (LEADS_SUBMIT) {
    label |= static_cast<int64_t>(leads_post << 14);
  }

  int integrarion_type = 0;
  if (pm_integration_type == 1) {
    integrarion_type = 1;
  } else if (pm_integration_type == 2) {
    integrarion_type = 2;
  } else if (pm_integration_type == 99) {
    integrarion_type = 3;
  }
  label |= static_cast<int64_t>(integrarion_type << 6);
  res.push_back(label);

  return res;
}

}  // namespace ad_algorithm
}  // namespace ks


