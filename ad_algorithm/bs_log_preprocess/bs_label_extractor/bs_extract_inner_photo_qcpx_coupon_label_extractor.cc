/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_extract_inner_photo_qcpx_coupon_label_extractor.h"

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/label_extractor/inner_photo_qcpx_coupon_label_extractor.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

std::vector<int64_t> BSExtractInnerPhotoQcpxCouponLabelExtractor::operator()(const BSLog& bslog,
                                                                             size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return {};
  }
  auto x0 = get_bslog_map_int32_int64(*bs, BSFieldEnum::adlog_item_rank_params_key,
                                      BSFieldEnum::adlog_item_rank_params_value, pos);
  auto x2 = get_value_from_map(x0, 1108);
  auto x3 = get_bslog_map_int32_int64(*bs, BSFieldEnum::adlog_item_rank_params_key,
                                      BSFieldEnum::adlog_item_rank_params_value, pos);
  auto x5 = get_value_from_map(x3, 1224);
  auto x6 = inner_photo_qcpx_coupon_label_extractor(x2, x5);

  std::vector<int64_t> res = {static_cast<int64_t>(x6)};
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
