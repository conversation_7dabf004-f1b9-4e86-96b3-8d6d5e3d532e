/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_inner_photo_close_label.h"

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/label_extractor/inner_photo_close_label.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

std::vector<int64_t> BSInnerPhotoCloseLabel::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return {};
  }
  auto x0 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_1, pos);
  auto x1 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000005, pos);
  auto x2 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000015, pos);
  auto x3 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_21, pos);
  auto x4 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_22, pos);
  auto x5 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000011, pos);
  auto x6 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000014, pos);
  auto x7 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_72, pos);
  auto x8 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_13, pos);
  auto x9 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000006, pos);
  auto x10 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_15, pos);
  auto x11 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_918, pos);
  auto x12 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_102, pos);
  auto x13 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_2, pos);
  auto x14 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_type, pos);
  auto x15 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000064, pos);
  auto x16 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000046, pos);
  auto x17 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type, pos);
  auto x18 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
  auto x19 = get_bslog_int64_or_default(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_live_creative_type, pos);
  auto x20 = get_bslog_int64_or_default(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_photo_info_photo_attribute_duration_ms, pos);
  auto x21 = get_bslog_str<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_1011, pos);
  auto x22 =
      get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_global_gmv_label_infos_key_50000001, pos);
  auto x23 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000105, pos);
  auto x24 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000222, pos);
  auto x25 = get_bslog_map_int32_int64(*bs, BSFieldEnum::adlog_item_rank_params_key,
                                       BSFieldEnum::adlog_item_rank_params_value, pos);
  auto x27 = get_value_from_map(x25, 1108);
  auto x28 = cast_to_float(x27);
  auto x31 = get_value_from_map(x25, 1224);
  auto x32 = cast_to_float(x31);
  auto x33 = inner_photo_close_label(x0, x1, x2, x3, x4, x5, x6, x7, x8, x9, x10, x11, x12, x13, x14, x15,
                                     x16, x17, x18, x19, x20, x21, x22, x23, x24, x28, x32);

  std::vector<int64_t> res = {static_cast<int64_t>(x33)};
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
