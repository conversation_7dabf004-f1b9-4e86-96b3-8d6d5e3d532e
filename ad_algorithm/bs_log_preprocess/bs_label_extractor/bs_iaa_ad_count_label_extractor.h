#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/label_extractor/general_value_label_extractor.dark
class BSIaaAdCountLabelExtractor : public BSLabelExtractorBase {
 public:
  explicit BSIaaAdCountLabelExtractor(const std::string&) {}
  std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSIaaAdCountLabelExtractor, "bs_iaa_ad_count_label_extractor");
}  // namespace ad_algorithm
}  // namespace ks
