#include <utility>
#include <string>
#include "base/strings/string_split.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_meta.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_event_mtl_label_extractor.h"

namespace ks {
namespace ad_algorithm {

BSEventMtlLabelExtractor::BSEventMtlLabelExtractor(const std::string& conf) {
  std::vector<std::string> conf_part;
  std::vector<std::string> event_label_list;
  std::vector<std::string> event_label;
  std::vector<std::string> label;
  base::SplitString(conf, std::string("="), &conf_part);
  if (conf_part.size() > 1) {
    neg_sample_rate = atoi(conf_part[1].c_str());
    is_neg_sample = true;
  }

  base::SplitString(conf_part[0], std::string("+"), &event_label_list);
  for (const auto & str : event_label_list) {
    event_label.clear();
    base::SplitString(str, std::string("|@"), &event_label);
    CHECK(event_label.size() == 2) << "label config error in "<< conf << ", error string " << str;
    label.clear();
    base::SplitString(event_label[1], std::string("-"), &label);
    CHECK(label.size() == 4) << "label config error in " << conf << ", error string " << event_label[1];
    int l100 = atoi(label[0].c_str());
    int l101 = atoi(label[1].c_str());
    int l110 = atoi(label[2].c_str());
    int l111 = atoi(label[3].c_str());
    bs::AdCallbackLog::EventType action_type = bs::AdCallbackLog::EventType::EVENT_UNKNOWN;
    auto it = bs::AdCallbackLog::str_to_event_type.find(event_label[0]);
    if (it != bs::AdCallbackLog::str_to_event_type.end()) {
      std::vector<int> labels;
      labels.push_back(l100);
      labels.push_back(l101);
      labels.push_back(l110);
      labels.push_back(l111);
      label_map.insert({action_type, labels});
      LOG(INFO) << "label " << action_type << ":" << event_label[1];
    }
  }
}

std::vector<int64_t> BSEventMtlLabelExtractor::operator()(const BSLog& bslog, size_t pos) const {
  std::vector<int64_t> res;
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return res;
  }

  auto key_callback_event = BSFieldEnum::adlog_item_label_info_callback_event;
  int call_back_label = BSFieldHelper::GetSingular<int>(*bs, key_callback_event, pos);

  int l100 = 0;
  int l101 = 0;
  int l110 = 0;
  int l111 = 0;
  bool event = false;

  bool item_click = BSFieldHelper::GetSingular<bool>(*bs, BSFieldEnum::adlog_item_label_info_item_click, pos);

  const auto it = label_map.find(call_back_label);
  if (it != label_map.end()) {
    event = true;
    l100 = it->second[0];
    l101 = it->second[1];
    l110 = it->second[2];
    l111 = it->second[3];
  }

  if (!event) {
    auto key_action_types = BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_callback_event;
    BSRepeatedField<int> action_types(*bs, key_action_types, pos);
    for (size_t i = 0; i < action_types.size(); i++) {
      auto type = action_types.Get(i);
      const auto iit = label_map.find(type);
      if (iit != label_map.end()) {
        l100 = iit->second[0];
        l101 = iit->second[1];
        l110 = iit->second[2];
        l111 = iit->second[3];
        break;
      }
    }
  }

  res.resize(1);
  if (!item_click && !event) {
    if (is_neg_sample && ad_base::AdRandom::GetInt(0, 99) < neg_sample_rate) {
      return std::vector<int64_t>();
    }
    res[0] = l100;
  }

  if (!item_click && event) {
    res[0] = l101;
  }
  if (item_click && !event) {
    res[0] = l110;
  }
  if (item_click && event) {
    res[0] = l111;
  }

  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
