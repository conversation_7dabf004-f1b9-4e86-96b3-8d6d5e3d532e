#pragma once

#include <string>
#include <vector>
#include <utility>
#include<unordered_map>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor_base.h"

namespace ks {
namespace ad_algorithm {

class BSKeyIdLabelExtractor : public BSLabelExtractorBase {
 public:
  explicit BSKeyIdLabelExtractor(const std::string& conf);
  virtual std::vector<int64_t> operator()(const BSLog& bslog, size_t pos) const;

 private:
  std::vector<std::pair<int, int>> label_value_;
};

REGISTER_PLUGIN(BSLabelExtractorBase, BSKeyIdLabelExtractor, "bs_key_id_label_extractor");

}  // namespace ad_algorithm
}  // namespace ks
