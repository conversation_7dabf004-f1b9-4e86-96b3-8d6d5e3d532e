/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_label_extractor/bs_cid_photo_roi_back_show_time_roi.h"

#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/label_extractor/cid_photo_roi_back_show_time_roi.h"

namespace ks {
namespace ad_algorithm {

std::vector<int64_t> BSCidPhotoRoiBackShowTimeRoi::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return {};
  }
  auto x0 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_1, pos);
  auto x1 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000020, pos);
  auto x2 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000002, pos);
  auto x3 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000011, pos);
  auto x4 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000005, pos);
  auto x5 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_403, pos);
  auto x6 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000047, pos);
  auto x7 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000046, pos);
  auto x8 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000090, pos);
  auto x9 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000091, pos);
  auto x10 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_907, pos);
  auto x11 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000255, pos);
  auto x12 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000256, pos);
  auto x13 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000036, pos);
  auto x14 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000101, pos);
  auto x15 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000102, pos);
  auto x16 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000100, pos);
  auto x17 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type, pos);
  auto x18 = cid_photo_roi_back_show_time_roi(x0, x1, x2, x3, x4, x5, x6, x7, x8, x9, x10, x11, x12, x13, x14,
                                              x15, x16, x17);

  std::vector<int64_t> res = {static_cast<int64_t>(x18)};
  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
