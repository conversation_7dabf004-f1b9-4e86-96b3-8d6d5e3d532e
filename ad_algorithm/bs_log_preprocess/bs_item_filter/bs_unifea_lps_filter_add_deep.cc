#include <vector>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_unifea_lps_filter_add_deep.h"

namespace ks {
namespace ad_algorithm {

BSUnifeaLpsFilterAddDeep::BSUnifeaLpsFilterAddDeep(const std::string& conf):
  bs_item_type_filter_("AD_DSP"),
  bs_lps_new_union_deep_filter_("item_impression"),
  bs_interactive_form_filter_("1+2+0"),
  bs_page_subpage_id_filter_("100012268-100012271@100012269-100012270#0") {
}

BSUnifeaLpsFilterAddDeep::BSUnifeaLpsFilterAddDeep(const std::string& conf1,
                                     const std::string& conf2,
                                     const std::string& conf3,
                                     const std::string& conf4) :
  bs_item_type_filter_(conf1),
  bs_lps_new_union_deep_filter_(conf2),
  bs_interactive_form_filter_(conf3),
  bs_page_subpage_id_filter_(conf4) {
}

bool BSUnifeaLpsFilterAddDeep::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  size_t total_sample_len = bs->total_sample_len();
  std::vector<bool> res(total_sample_len, true);

  int total_click = 0;
  auto key_click = BSFieldEnum::adlog_item_label_info_item_impression;
  for (size_t i = 0; i < total_sample_len; i++) {
    bool click = BSFieldHelper::GetSingular<bool>(*bs, key_click, i);
    bool item_type_res = bs_item_type_filter_(bslog, i);
    bool union_pay_res = bs_lps_new_union_deep_filter_(bslog, i);
    bool interactive_form_res = bs_interactive_form_filter_(bslog, i);
    bool page_res = bs_page_subpage_id_filter_(bslog, i);

    res[i] = (item_type_res && union_pay_res && interactive_form_res && page_res);
    if (click && res[i]) {
      total_click += 1;
    }
  }

  /* 取消判断：无2跳归因
  if (total_click == 0) {
    for (size_t i = 0; i < total_sample_len; i++) {
      res[i] = false;
    }
  }
  */

  return res[pos];
}

}  // namespace ad_algorithm
}  // namespace ks
