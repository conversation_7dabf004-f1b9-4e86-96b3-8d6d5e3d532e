#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/inner_inspire_filter.dark
class BSInnerRmInspireFilter5 : public BSItemFilterBase {
 public:
  explicit BSInnerRmInspireFilter5(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerRmInspireFilter5, "bs_inner_rm_inspire_filter_5");
}  // namespace ad_algorithm
}  // namespace ks
