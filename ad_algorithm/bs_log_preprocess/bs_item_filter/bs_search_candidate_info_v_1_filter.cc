/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_search_candidate_info_v_1_filter.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/search_candidate_info_type_filter.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

bool BSSearchCandidateInfoV1Filter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_map_int64_int64<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_1405_key,
                                            BSFieldEnum::adlog_context_info_common_attr_key_1405_value, pos);
  auto x1 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_id, pos);
  auto x2 = get_value_from_map(x0, x1);
  auto x4 = search_candidate_info_type_filter(x2, 1);
  return x4;
}

}  // namespace ad_algorithm
}  // namespace ks
