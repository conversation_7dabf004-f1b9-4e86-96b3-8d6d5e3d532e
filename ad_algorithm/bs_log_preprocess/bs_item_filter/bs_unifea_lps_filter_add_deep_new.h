#pragma once
#include <string>
#include <unordered_set>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_item_type_ocpx_filter.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_lps_union_deep_filter.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_interactive_form_filter.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_page_subpage_id_filter.h"

namespace ks {
namespace ad_algorithm {

// 拷贝自 BSUnifeaLpsFilterAddDeep
// 修改 item_filter 类型 BsItemTypeFilter -> BSItemTypeOcpxFilter

class BSUnifeaLpsFilterAddDeepNew : public BSItemFilterBase {
 public:
  explicit BSUnifeaLpsFilterAddDeepNew(const std::string& conf);
  explicit BSUnifeaLpsFilterAddDeepNew(const std::string& conf1,
                             const std::string& conf2,
                             const std::string& conf3,
                             const std::string& conf4);
  bool operator()(const BSLog& bslog, size_t pos) const;

 protected:
  BSItemTypeOcpxFilter bs_item_type_ocpx_filter_;
  BSLpsUnionDeepFilter bs_lps_union_deep_filter_;
  BSInteractiveFormFilter bs_interactive_form_filter_;
  BSPageSubPageIdFilter bs_page_subpage_id_filter_;
};

REGISTER_PLUGIN(BSItemFilterBase, BSUnifeaLpsFilterAddDeepNew, "bs_unifea_lps_filter_add_deep_new");

}  // namespace ad_algorithm
}  // namespace ks
