#include <vector>
#include <utility>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_meta.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_item_type_ocpx_filter.h"

namespace ks {
namespace ad_algorithm {

BSItemTypeOcpxFilter::BSItemTypeOcpxFilter(const std::string& conf) {
  // conf 格式：AD_DSP@FANS_TOP+926
  // 代表 AD_DSP 全部放进来
  //     FANS_TOP 只要 ocpx_action_type = 926 (EVENT_PRIVATE_MESSAGE_SENT)
  std::vector<std::string> results;
  base::SplitString(conf, std::string("@", 1), &results);
  for (const auto& type_element : results) {
    std::vector<std::string> vct_type_ocpxs;
    base::SplitString(type_element, std::string("+", 1), &vct_type_ocpxs);
    if (vct_type_ocpxs.size() == 0) {
      continue;
    }

    // 解析 type
    const auto& type_str = vct_type_ocpxs[0];
    auto it = bs::StrToEnumValue::str_to_item_type.find(type_str);
    if (it == bs::StrToEnumValue::str_to_item_type.end()) {
      continue;
    }
    int type = it->second;
    // 解析 ocpx 数据
    std::set<int> ocpx_set;
    for (int i = 1; i < vct_type_ocpxs.size(); ++i) {
      int ocpx_int_value = -1;
      if (base::StringToInt(vct_type_ocpxs[i], &ocpx_int_value)) {
        ocpx_set.insert(ocpx_int_value);
      }
    }
    type_ocpx_map_.insert(std::make_pair(type, std::move(ocpx_set)));
  }
}

bool BSItemTypeOcpxFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);

  auto key_ocpc = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type;
  int ocpc_action_type = BSFieldHelper::GetSingular<int>(*bs, key_ocpc, pos);

  auto it_ocpx_whitelist = type_ocpx_map_.find(item_type);
  if (it_ocpx_whitelist != type_ocpx_map_.end()) {
    const auto& ocpx_set = it_ocpx_whitelist->second;
    bool all_type = (ocpx_set.size() == 0);
    // 如果没有 ocpx 限定，则认为是所有都放过
    // 如果有 ocpx 限定，则按照限定条件过滤
    return all_type || (ocpx_set.find(ocpc_action_type) != ocpx_set.end());
  }

  return false;
}

}  // namespace ad_algorithm
}  // namespace ks
