#pragma once
#include <string>
#include <unordered_set>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_unifea_lps_filter_add_deep_outer.h"

namespace ks {
namespace ad_algorithm {

class BSUnifeaNewLpsFilterAddDeepSampleOuter2 : public BSItemFilterBase {
 public:
  explicit BSUnifeaNewLpsFilterAddDeepSampleOuter2(const std::string& conf);
  bool operator()(const BSLog& bslog, size_t pos) const;

 protected:
  BSUnifeaLpsFilterAddDeepOuter bs_unifea_lps_filter_add_deep_outer_;
  double neg_sample_rate_;
};

REGISTER_PLUGIN(BSItemFilterBase, BSUnifeaNewLpsFilterAddDeepSampleOuter2,
                                   "bs_unifea_new_lps_filter_add_deep_sample_outer_2");

}  // namespace ad_algorithm
}  // namespace ks

