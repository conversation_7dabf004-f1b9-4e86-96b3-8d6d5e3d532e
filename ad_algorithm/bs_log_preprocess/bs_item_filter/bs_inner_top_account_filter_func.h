#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/inner_top_account_filter_func.dark
class BSInnerTopAccountFilterFunc : public BSItemFilterBase {
 public:
  explicit BSInnerTopAccountFilterFunc(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerTopAccountFilterFunc, "bs_inner_top_account_filter_func");
}  // namespace ad_algorithm
}  // namespace ks
