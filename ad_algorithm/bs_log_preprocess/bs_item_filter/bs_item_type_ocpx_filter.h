#pragma once

#include <string>
#include <unordered_map>
#include <set>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// 根据 item_type + ocpx_action_type 进行过滤
class BSItemTypeOcpxFilter : public BSItemFilterBase {
 public:
  explicit BSItemTypeOcpxFilter(const std::string& conf);
  bool operator()(const BSLog& bslog, size_t pos) const;

 private:
  std::unordered_map<int, std::set<int>> type_ocpx_map_;
};

REGISTER_PLUGIN(BSItemFilterBase, BSItemTypeOcpxFilter, "bs_item_type_ocpx_filter");

}  // namespace ad_algorithm
}  // namespace ks
