#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/inner_photo_order_item_filter_p2l_cid.dark
class BSInnerPhotoOrderFilterP2lCid : public BSItemFilterBase {
 public:
  explicit BSInnerPhotoOrderFilterP2lCid(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerPhotoOrderFilterP2lCid, "bs_inner_photo_order_filter_p_2l_cid");
}  // namespace ad_algorithm
}  // namespace ks
