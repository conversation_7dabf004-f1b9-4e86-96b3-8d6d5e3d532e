#include <vector>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_meta.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_campaign_type_filter.h"

namespace ks {
namespace ad_algorithm {

BSCampaignTypeFilter::BSCampaignTypeFilter(const std::string& conf) {
  if (conf != "") {
    std::vector<std::string> tokens;
    base::SplitString(conf, std::string("@", 1), &tokens);
    for (const auto& token : tokens) {
      int type = -1;
      if (base::StringToInt(token, &type)) {
        bs_campaign_type_set_.insert(type);
      }
    }
  }
}

bool BSCampaignTypeFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  auto enum_campaign_type = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type;
  uint64_t campaign_type = BSFieldHelper::GetSingular<uint64_t>(*bs, enum_campaign_type, pos);

  if (bs_campaign_type_set_.find(campaign_type) != bs_campaign_type_set_.end()) {
    return true;
  }
  return false;
}

}  // namespace ad_algorithm
}  // namespace ks
