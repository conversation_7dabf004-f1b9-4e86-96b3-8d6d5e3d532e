#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/inner_guess_interactive_form_need_double.dark
class BSInnerGuessInteractiveFormNeedDouble : public BSItemFilterBase {
 public:
  explicit BSInnerGuessInteractiveFormNeedDouble(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerGuessInteractiveFormNeedDouble,
                "bs_inner_guess_interactive_form_need_double");
}  // namespace ad_algorithm
}  // namespace ks
