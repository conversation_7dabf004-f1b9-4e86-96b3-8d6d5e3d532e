#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/inner_cid_gmv_only_filter.dark
class BSInnerCidGmvOnlyFilter : public BSItemFilterBase {
 public:
  explicit BSInnerCidGmvOnlyFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerCidGmvOnlyFilter, "bs_inner_cid_gmv_only_filter");
}  // namespace ad_algorithm
}  // namespace ks
