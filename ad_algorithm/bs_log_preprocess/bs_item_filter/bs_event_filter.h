#pragma once

#include <string>
#include <unordered_set>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// 保留从点击开始的激活样本，通过参数指定激活类型，如 click_event_filter:EVENT_CONVERSION
class BSEventFilter : public BSItemFilterBase {
 public:
  explicit BSEventFilter(const std::string& conf);
  bool operator()(const BSLog& bslog, size_t pos) const;

 private:
  std::unordered_set<int> ad_action_set_;
};

REGISTER_PLUGIN(BSItemFilterBase, BSEventFilter, "bs_event_filter");

}  // namespace ad_algorithm
}  // namespace ks
