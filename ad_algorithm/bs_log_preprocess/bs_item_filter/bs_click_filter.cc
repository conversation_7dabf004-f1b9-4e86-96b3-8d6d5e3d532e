#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_click_filter.h"

namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSClickFilter::BSClickFilter(const std::string& conf) {
}

bool BSClickFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  auto key_click = BSFieldEnum::adlog_item_label_info_item_impression;
  return BSFieldHelper::GetSingular<bool>(*bs, key_click, pos);
}

}  // namespace ad_algorithm
}  // namespace ks

