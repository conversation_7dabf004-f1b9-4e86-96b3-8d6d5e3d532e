/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_inner_top_account_filter_func.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/inner_top_account_filter_func.h"

namespace ks {
namespace ad_algorithm {

bool BSInnerTopAccountFilterFunc::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_str_or_default(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_corporation_name, pos);
  auto x1 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type, pos);
  auto x2 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
  auto x3 = inner_top_account_filter_func(x0, x1, x2);
  return x3;
}

}  // namespace ad_algorithm
}  // namespace ks
