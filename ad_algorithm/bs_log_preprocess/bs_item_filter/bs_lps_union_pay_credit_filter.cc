#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_lps_union_pay_credit_filter.h"

namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSLpsUnionPayCreditFilter::BSLpsUnionPayCreditFilter(const std::string& conf) {
}

// 和 pipeline_mtl.py 保持一致
bool BSLpsUnionPayCreditFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  if (is_landsub_item(bslog, pos)) {
    return true;
  } else {
    return false;
  }
}

bool BSLpsUnionPayCreditFilter::is_landsub_item(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);

  auto key_unit_exists = BSFieldEnum::adlog_item_ad_dsp_info_unit_exists;
  bool has_unit = BSFieldHelper::GetSingular<bool>(*bs, key_unit_exists, pos);

  auto key_base_exists = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_exists;
  bool has_base = BSFieldHelper::GetSingular<bool>(*bs, key_base_exists, pos);

  if (!has_ad_dsp_info || !has_unit || !has_base) {
    return false;
  }

  auto key_ocpc = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type;
  int ocpc_action_type = BSFieldHelper::GetSingular<int>(*bs, key_ocpc, pos);

  auto key_campaign_type = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type;
  uint64_t ad_campaign_type = BSFieldHelper::GetSingular<uint64_t>(*bs, key_campaign_type, pos);

  bool if_ocpc_pay = (ocpc_action_type ==  190)
                      && (ad_campaign_type == 3 || ad_campaign_type == 4 || ad_campaign_type == 5);

  bool if_ocpc_credit = (ocpc_action_type ==  383)
                      && (ad_campaign_type == 3 || ad_campaign_type == 4 || ad_campaign_type == 5);

  //  kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED : 53
  if (ocpc_action_type == 53 || if_ocpc_pay || if_ocpc_credit) {
    return true;
  } else {
    return false;
  }
}

}  // namespace ad_algorithm
}  // namespace ks

