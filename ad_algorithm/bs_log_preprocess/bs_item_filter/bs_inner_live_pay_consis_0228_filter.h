#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/live_pay_consis_0228_filter.dark
class BSInnerLivePayConsis0228Filter : public BSItemFilterBase {
 public:
  explicit BSInnerLivePayConsis0228Filter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerLivePayConsis0228Filter, "bs_inner_live_pay_consis_0228_filter");
}  // namespace ad_algorithm
}  // namespace ks
