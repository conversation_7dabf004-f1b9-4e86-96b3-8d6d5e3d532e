#pragma once
#include <string>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_unifea_lps_filter.h"

namespace ks {
namespace ad_algorithm {

class BSUnifeaLpsFilterAddUniv : public BSUnifeaLpsFilter {
 public:
  explicit BSUnifeaLpsFilterAddUniv(const std::string& conf);
};

REGISTER_PLUGIN(BSItemFilterBase, BSUnifeaLpsFilterAddUniv, "bs_unifea_lps_filter_add_univ");

}  // namespace ad_algorithm
}  // namespace ks
