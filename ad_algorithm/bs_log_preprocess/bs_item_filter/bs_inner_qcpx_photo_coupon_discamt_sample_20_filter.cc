/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_inner_qcpx_photo_coupon_discamt_sample_20_filter.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/inner_qcpx_photo_coupon_discamt_sample_20_filter.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

bool BSInnerQcpxPhotoCouponDiscamtSample20Filter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_int64_or_default<true>(*bs, BSFieldEnum::adlog_context_sub_page_id, pos);
  auto x1 = get_bslog_int64_or_default<true>(*bs, BSFieldEnum::adlog_context_page_id, pos);
  auto x2 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_type, pos);
  auto x3 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
  auto x4 = get_bslog_int64_or_default(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_live_creative_type, pos);
  auto x5 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type, pos);
  auto x6 = get_bslog_map_int32_int64(*bs, BSFieldEnum::adlog_item_rank_params_key,
                                      BSFieldEnum::adlog_item_rank_params_value, pos);
  auto x8 = get_value_from_map(x6, 1108);
  auto x9 = cast_to_float(x8);
  auto x12 = get_value_from_map(x6, 1107);
  auto x13 = cast_to_float(x12);
  auto x16 = get_value_from_map(x6, 1224);
  auto x17 = cast_to_float(x16);
  auto x20 = get_value_from_map(x6, 1225);
  auto x21 = cast_to_float(x20);
  auto x24 = get_value_from_map(x6, 1127);
  auto x25 = cast_to_int64(x24);
  auto x26 = inner_qcpx_photo_coupon_discamt_sample_20_filter(x0, x1, x2, x3, x4, x5, x9, x13, x17, x21, x25);
  return x26;
}

}  // namespace ad_algorithm
}  // namespace ks
