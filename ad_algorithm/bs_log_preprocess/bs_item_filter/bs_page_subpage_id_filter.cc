#include <vector>
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_page_subpage_id_filter.h"

namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSPageSubPageIdFilter::BSPageSubPageIdFilter(const std::string& conf) {
  if (conf != "") {
    std::vector<std::string> tuple;
    std::vector<std::string> tokens;
    base::SplitString(conf, std::string("%#"), &tuple);
    if (tuple.size() > 1 && atoi(tuple[1].c_str()) == 1) {
      LOG(INFO) << "set filterFlag = true";
      filterFlag = true;
    }
    base::SplitString(tuple[0], std::string("@"), &tokens);
    for (const auto& token : tokens) {
      types_.insert(token);
      LOG(INFO) << "insert token: " << token;
    }
  }
}

bool BSPageSubPageIdFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  auto key_context_exists = BSFieldEnum::adlog_context_exists;
  bool has_context = BSFieldHelper::GetSingular<bool>(*bs, key_context_exists, 0);
  if (has_context) {
    int page_id = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_context_page_id, 0);
    int sub_page_id = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_context_sub_page_id, 0);
    std::string key = std::to_string(page_id) + "-" + std::to_string(sub_page_id);

    if (types_.find(key) != types_.end()) {
      if (filterFlag) {
        return true;
      } else {
        return false;
      }
    }
  }

  if (filterFlag) {
    return false;
  }
  return true;
}

}  // namespace ad_algorithm
}  // namespace ks
