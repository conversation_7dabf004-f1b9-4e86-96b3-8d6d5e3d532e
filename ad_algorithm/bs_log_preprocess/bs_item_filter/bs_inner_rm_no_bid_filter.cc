/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_inner_rm_no_bid_filter.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/check_label_infos.h"

namespace ks {
namespace ad_algorithm {

bool BSInnerRmNoBidFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_speed, pos);
  auto x2 = rm_equal_int(x0, 5);
  auto x3 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5003758, pos);
  auto x5 = rm_equal_int(x3, 2);
  auto x6 = logic_and(x2, x5);
  return x6;
}

}  // namespace ad_algorithm
}  // namespace ks
