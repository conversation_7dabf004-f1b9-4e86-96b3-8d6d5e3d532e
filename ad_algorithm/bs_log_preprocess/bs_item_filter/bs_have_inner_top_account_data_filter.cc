/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_have_inner_top_account_data_filter.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/have_inner_top_account_data_filter.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

bool BSHaveInnerTopAccountDataFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_map_int32_int64(*bs, BSFieldEnum::adlog_item_rank_params_key,
                                      BSFieldEnum::adlog_item_rank_params_value, pos);
  auto x2 = get_value_from_map(x0, 1307);
  auto x3 = get_bslog_str_or_default(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_corporation_name, pos);
  auto x4 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type, pos);
  auto x5 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
  auto x6 = have_inner_top_account_data_filter(x2, x3, x4, x5);
  return x6;
}

}  // namespace ad_algorithm
}  // namespace ks
