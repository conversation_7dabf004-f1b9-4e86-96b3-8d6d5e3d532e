#include <vector>
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_impression_sample.h"

namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSImpressionSample::BSImpressionSample(const std::string& conf) {
  if (conf != "") {
    std::vector<std::string> tokens;
    base::SplitString(conf, std::string(";+"), &tokens);
    for (const auto& token : tokens) {
      double type = 0.0;
      if (base::StringToDouble(token, &type)) {
        neg_sample_rate = type;
      }
    }
  }
}

bool BSImpressionSample::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

    auto key_item_impression = BSFieldEnum::adlog_item_label_info_item_impression;
    bool is_impression = BSFieldHelper::GetSingular<bool>(*bs, key_item_impression, pos);
    // LOG_EVERY_N(INFO, 1) << "zz debug neg_sample_rate=" << neg_sample_rate;
    if (is_impression && ad_base::AdRandom::GetDouble() > neg_sample_rate) {
      return false;
    }
    return true;
}

}  // namespace ad_algorithm
}  // namespace ks

