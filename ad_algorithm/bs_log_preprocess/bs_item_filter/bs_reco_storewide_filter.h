#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/reco_storewide_filter.dark
class BSRecoStorewideFilter : public BSItemFilterBase {
 public:
  explicit BSRecoStorewideFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSRecoStorewideFilter, "bs_reco_storewide_filter");
}  // namespace ad_algorithm
}  // namespace ks
