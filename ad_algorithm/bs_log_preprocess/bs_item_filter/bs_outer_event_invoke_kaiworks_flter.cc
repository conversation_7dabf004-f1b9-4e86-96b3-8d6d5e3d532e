/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_outer_event_invoke_kaiworks_flter.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/outer_event_invoke_kaiworks_filter.h"

namespace ks {
namespace ad_algorithm {

bool BSOuterEventInvokeKaiworksFlter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_1, pos);
  auto x1 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000084, pos);
  auto x2 = get_bslog_str_list(*bs, BSFieldEnum::adlog_item_label_space, pos);
  auto x3 = event_invoke_kaiworks_filter(x0, x1, x2);
  return x3;
}

}  // namespace ad_algorithm
}  // namespace ks
