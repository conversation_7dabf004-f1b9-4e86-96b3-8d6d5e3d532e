/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_live_pay_inspire_five_percent_filter.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/live_pay_inspire_filter.h"

namespace ks {
namespace ad_algorithm {

bool BSLivePayInspireFivePercentFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_int64_or_default<true>(*bs, BSFieldEnum::adlog_context_page_id, pos);
  auto x1 = live_pay_inspire_five_percent_filter(x0);
  return x1;
}

}  // namespace ad_algorithm
}  // namespace ks
