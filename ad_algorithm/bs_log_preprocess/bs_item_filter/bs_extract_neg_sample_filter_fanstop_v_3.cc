/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_extract_neg_sample_filter_fanstop_v_3.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/neg_sample_filter_fanstop_v3.h"

namespace ks {
namespace ad_algorithm {

bool BSExtractNegSampleFilterFanstopV3::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_786, pos);
  auto x1 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000525, pos);
  auto x2 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_item_impression, pos);
  auto x3 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_item_click, pos);
  auto x4 = neg_sample_filter_fanstop_v3(x0, x1, x2, x3);
  return x4;
}

}  // namespace ad_algorithm
}  // namespace ks
