#pragma once

#include <string>
#include <vector>
#include <unordered_set>
#include "base/strings/string_split.h"
#include "base/strings/string_number_conversions.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

class BSInteractiveFormFilter : public BSItemFilterBase {
 public:
  explicit BSInteractiveFormFilter(const std::string& conf);
  bool operator()(const BSLog& bslog, size_t pos) const;

 private:
  std::unordered_set<int> types_;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInteractiveFormFilter, "bs_interactive_form_filter");

}  // namespace ad_algorithm
}  // namespace ks
