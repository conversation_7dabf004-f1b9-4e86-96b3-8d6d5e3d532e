#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/inner_shelf_qcpx_photo_data_filter_v1.dark
class BSInnerShelfQcpxPhotoDataFilterV1 : public BSItemFilterBase {
 public:
  explicit BSInnerShelfQcpxPhotoDataFilterV1(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerShelfQcpxPhotoDataFilterV1,
                "bs_inner_shelf_qcpx_photo_data_filter_v_1");
}  // namespace ad_algorithm
}  // namespace ks
