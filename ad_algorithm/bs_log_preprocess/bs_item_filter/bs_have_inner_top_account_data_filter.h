#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/have_inner_top_account_data_filter.dark
class BSHaveInnerTopAccountDataFilter : public BSItemFilterBase {
 public:
  explicit BSHaveInnerTopAccountDataFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSHaveInnerTopAccountDataFilter, "bs_have_inner_top_account_data_filter");
}  // namespace ad_algorithm
}  // namespace ks
