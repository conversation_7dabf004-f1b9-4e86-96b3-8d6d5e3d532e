/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_drama_novel_filter.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/ad_roas_item_filter.h"

namespace ks {
namespace ad_algorithm {

bool BSDramaNovelFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5002328, pos);
  auto x1 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
  auto x2 = drama_novel_filter(x0, x1);
  return x2;
}

}  // namespace ad_algorithm
}  // namespace ks
