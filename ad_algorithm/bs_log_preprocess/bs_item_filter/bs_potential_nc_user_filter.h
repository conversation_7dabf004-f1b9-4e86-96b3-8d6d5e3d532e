#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/potential_nc_user_filter.dark
class BSPotentialNcUserFilter : public BSItemFilterBase {
 public:
  explicit BSPotentialNcUserFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSPotentialNcUserFilter, "bs_potential_nc_user_filter");
}  // namespace ad_algorithm
}  // namespace ks
