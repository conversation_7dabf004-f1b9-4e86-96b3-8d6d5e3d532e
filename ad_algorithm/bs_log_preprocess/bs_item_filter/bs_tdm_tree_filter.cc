#include <vector>
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_tdm_tree_filter.h"

namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSTDMTreeFilter::BSTDMTreeFilter(const std::string& conf) {
  std::vector<std::string> results;
  base::SplitString(conf, std::string("@", 1), &results);
  for (const auto& tree_name : results) {
    LOG(INFO) << "add tdm_tree_name=" << tree_name;
    tree_set_.emplace(hash_fn_(tree_name));
  }
  all_type_ = tree_set_.size() == 0;
}

bool BSTDMTreeFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }
  if (all_type_) return true;
  // BFV("100009", "tdm.tree_name", std::string, tree_name_vec);
  auto key_tdm_tree_name = 100009;
  auto tdm_tree_name = BSFieldHelper::GetSingular<absl::string_view>(*bs, key_tdm_tree_name, pos);
  auto tree_id = hash_fn_(std::string(tdm_tree_name));
  bool ret = tree_set_.find(tree_id) != tree_set_.end();
  LOG_EVERY_N(INFO, 100000) << "tdm_tree_name=" << tdm_tree_name << ", ret=" << ret;
  return ret;
}


}  // namespace ad_algorithm
}  // namespace ks

