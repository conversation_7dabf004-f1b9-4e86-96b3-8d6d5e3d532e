#include <vector>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_meta.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_item_type_filter.h"

namespace ks {
namespace ad_algorithm {

BSItemTypeFilter::BSItemTypeFilter(const std::string& conf) {
  all_type_ = conf.size() == 0;
  std::vector<std::string> results;
  base::SplitString(conf, std::string("@", 1), &results);
  for (const auto& type : results) {
    auto it = bs::StrToEnumValue::str_to_item_type.find(type);
    if (it != bs::StrToEnumValue::str_to_item_type.end()) {
      type_set_.insert(it->second);
    }
  }
}

bool BSItemTypeFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);

  return all_type_ || (type_set_.find(item_type) != type_set_.end());
}

}  // namespace ad_algorithm
}  // namespace ks
