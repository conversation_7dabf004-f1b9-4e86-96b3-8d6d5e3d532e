#include <string>
#include <vector>
#include <unordered_set>
#include "base/strings/string_split.h"
#include "base/strings/string_number_conversions.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_repeated_field.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_interactive_form_filter.h"

namespace ks {
namespace ad_algorithm {

BSInteractiveFormFilter::BSInteractiveFormFilter(const std::string& conf) {
  if (conf != "") {
    std::vector<std::string> tokens;
    base::SplitString(conf, std::string(";+"), &tokens);
    for (const auto& token : tokens) {
      int type = -1;
      if (base::StringToInt(token, &type)) {
        types_.insert(type);
      }
    }
  }
}

bool BSInteractiveFormFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  auto key_interactive_form = BSFieldEnum::adlog_context_info_common_attr_key_14;
  bool has_value = false;
  int interactive_form = BSFieldHelper::GetSingular<int, true>(*bs, key_interactive_form, pos, &has_value);
  if (has_value) {
    if (types_.find(interactive_form) != types_.end()) {
      return true;
    }
  }

  return false;
}

}  // namespace ad_algorithm
}  // namespace ks
