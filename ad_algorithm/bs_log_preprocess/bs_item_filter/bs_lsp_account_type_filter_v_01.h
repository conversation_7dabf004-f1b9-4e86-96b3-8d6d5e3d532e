#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/lsp_account_type_filter.dark
class BSLspAccountTypeFilterV01 : public BSItemFilterBase {
 public:
  explicit BSLspAccountTypeFilterV01(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSLspAccountTypeFilterV01, "bs_lsp_account_type_filter_v_01");
}  // namespace ad_algorithm
}  // namespace ks
