#pragma once

#include <string>
#include <set>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// 根据 item_type 过滤
class BSStandardLivePlayStartedFilterCompatible2 : public BSItemFilterBase {
 public:
  explicit BSStandardLivePlayStartedFilterCompatible2(const std::string& conf);
  bool operator()(const BSLog& bslog, size_t pos) const;

 private:
  int inspire_ns_percent_ = 100;
  bool inspire_negative_sample(
      const BSLog& bslog,
      size_t pos,
      bool is_inspire_live_request) const;
};

REGISTER_PLUGIN(BSItemFilterBase,
                BSStandardLivePlayStartedFilterCompatible2,
                "bs_standard_live_played_started_filter_compatible2");

}  // namespace ad_algorithm
}  // namespace ks
