#include <vector>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_meta.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_direct_and_photo_live_filter_compatible.h"

namespace ks {
namespace ad_algorithm {

BSDirectANDPHOTOLiveFilterCompatible::BSDirectANDPHOTOLiveFilterCompatible(const std::string& conf) {}

bool BSDirectANDPHOTOLiveFilterCompatible::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  const int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);
  if (item_type == bs::ItemType::FANS_TOP_LIVE) {
    return true;
  }

  auto enum_campaign_exists =
      BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists;
  bool has_campaign =
      BSFieldHelper::GetSingular<bool>(*bs, enum_campaign_exists, pos);
  if ((item_type == bs::ItemType::AD_DSP ||
       item_type == bs::ItemType::NATIVE_AD) &&
      has_campaign) {
    auto enum_ad_campaign_type =
        BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type;
    uint64_t campaign_type =
        BSFieldHelper::GetSingular<uint64_t>(*bs, enum_ad_campaign_type, pos);
    if (campaign_type == 14 || campaign_type == 21 || campaign_type == 22 || campaign_type == 23) {
      return true;
    }
  }

  if (item_type == bs::ItemType::AD_BRAND) {
    auto enum_photo_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_exists;
    bool photo_info_exists = BSFieldHelper::GetSingular<bool>(*bs, enum_photo_info_exists, pos);
    if (photo_info_exists) {
      auto retrieval_photo_info_exists =
          BSFieldEnum::adlog_item_ad_dsp_info_photo_info_retrieval_photo_info_exists;
      bool has_retrieval_photo_info = BSFieldHelper::GetSingular<bool>(*bs, retrieval_photo_info_exists, pos);
      if (has_retrieval_photo_info) {
        auto bs_is_living = BSFieldEnum::adlog_item_ad_dsp_info_photo_info_retrieval_photo_info_is_living;
        return BSFieldHelper::GetSingular<bool>(*bs, bs_is_living, pos);
      }
    }

    bool has_creative = BSFieldHelper::GetSingular<bool>(
        *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_exists, pos);
    if (has_creative) {
      int64_t live_creative_type = BSFieldHelper::GetSingular<int64_t>(*bs,
          BSFieldEnum::adlog_item_ad_dsp_info_creative_base_live_creative_type, pos);
      if (live_creative_type == 1 || live_creative_type == 2) {
        return true;
      }
    }
  }

  const auto key_fans_photo_info_exists = BSFieldEnum::adlog_item_fans_top_info_photo_info_exists;
  bool has_fans_photo_info = BSFieldHelper::GetSingular<bool>(*bs, key_fans_photo_info_exists, pos);
  if (item_type == bs::ItemType::FANS_TOP && has_fans_photo_info) {
    auto fans_retrieval_photo_info =
        BSFieldEnum::adlog_item_fans_top_info_photo_info_retrieval_photo_info_exists;
    bool has_retrieval_photo_info = BSFieldHelper::GetSingular<bool>(*bs, fans_retrieval_photo_info, pos);
    if (has_retrieval_photo_info) {
      auto fans_is_living = BSFieldEnum::adlog_item_fans_top_info_photo_info_retrieval_photo_info_is_living;
      return BSFieldHelper::GetSingular<bool>(*bs, fans_is_living, pos);
    }
  }

  return false;
}

}  // namespace ad_algorithm
}  // namespace ks
