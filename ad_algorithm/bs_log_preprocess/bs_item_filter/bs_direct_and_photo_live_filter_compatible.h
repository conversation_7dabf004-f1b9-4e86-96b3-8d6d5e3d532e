#pragma once

#include <string>
#include <set>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// 根据 item_type 过滤
class BSDirectANDPHOTOLiveFilterCompatible : public BSItemFilterBase {
 public:
  explicit BSDirectANDPHOTOLiveFilterCompatible(const std::string& conf);
  bool operator()(const BSLog& bslog, size_t pos) const;
};

REGISTER_PLUGIN(BSItemFilterBase,
                BSDirectANDPHOTOLiveFilterCompatible,
                "bs_direct_and_photo_live_filter_compatible");

}  // namespace ad_algorithm
}  // namespace ks
