#include <fstream>
#include <string>
#include <vector>
#include "absl/strings/string_view.h"
#include "absl/strings/str_split.h"
#include "absl/strings/numbers.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_key_filter.h"

namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSKeyFilter::BSKeyFilter(const std::string& conf) {
  std::ifstream infile(conf);

  if (infile.is_open()) {
    std::string line;
    int64_t llsid;
    int64_t item_id;
    while (std::getline(infile, line)) {
      std::vector<absl::string_view> arr = absl::StrSplit(line, "\t");
      if (arr.size() == 2) {
        if (absl::SimpleAtoi(arr[0], &llsid) && absl::SimpleAtoi(arr[1], &item_id)) {
          llsid_item_ids_[llsid].push_back(item_id);
        } else {
          LOG(ERROR) << "parse llsid or item_id failed! line: " << line;
        }
      } else {
        LOG(ERROR) << "wrong format of line, should be llsid and item_id seperate by \t, but is: "
                   << line;
      }
    }

    LOG(INFO) << "read llsid, item_id done, size: " << llsid_item_ids_.size();
    infile.close();
  } else {
    LOG(ERROR) << "open file failed: " << conf;
  }
}

bool BSKeyFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  auto key_llsid = BSFieldEnum::adlog_llsid;
  int64_t llsid = BSFieldHelper::GetSingular<int64_t>(*bs, key_llsid);

  auto key_item_id = BSFieldEnum::adlog_item_id;
  int64_t item_id = BSFieldHelper::GetSingular<int64_t>(*bs, key_item_id, pos);

  auto it = llsid_item_ids_.find(llsid);
  if (it != llsid_item_ids_.end()) {
    const auto& item_ids = it->second;
    for (size_t i = 0; i < item_ids.size(); i++) {
      if (item_ids[i] == item_id) {
        return true;
      }
    }
  }

  return false;
}

}  // namespace ad_algorithm
}  // namespace ks

