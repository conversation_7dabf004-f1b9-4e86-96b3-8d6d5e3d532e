/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_inner_qcpx_live_unify_ctr_filter.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/inner_qcpx_live_unify_ctr_filter.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

bool BSInnerQcpxLiveUnifyCtrFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_map_int32_int64(*bs, BSFieldEnum::adlog_item_rank_params_key,
                                      BSFieldEnum::adlog_item_rank_params_value, pos);
  auto x2 = get_value_from_map(x0, 1242);
  auto x3 = cast_to_float(x2);
  auto x4 = inner_qcpx_live_unify_ctr_filter(x3);
  return x4;
}

}  // namespace ad_algorithm
}  // namespace ks
