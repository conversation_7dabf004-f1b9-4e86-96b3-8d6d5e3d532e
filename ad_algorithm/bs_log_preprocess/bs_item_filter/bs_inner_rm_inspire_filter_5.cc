/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_inner_rm_inspire_filter_5.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/check_label_infos.h"

namespace ks {
namespace ad_algorithm {

bool BSInnerRmInspireFilter5::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_int64_or_default<true>(*bs, BSFieldEnum::adlog_context_page_id, pos);
  auto x1 = page_id_is_inner_inspire(x0);
  auto x2 = logic_not(x1);
  auto x4 = random_less(0.05);
  auto x5 = logic_or(x2, x4);
  return x5;
}

}  // namespace ad_algorithm
}  // namespace ks
