/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_inner_guess_interactive_form_need_single.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/inner_guess_interactive_form_filter.h"

namespace ks {
namespace ad_algorithm {

bool BSInnerGuessInteractiveFormNeedSingle::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_int64_or_default<true>(*bs, BSFieldEnum::adlog_context_page_id, pos);
  auto x1 = get_bslog_int64<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_14, pos);
  auto x3 = inner_guess_interactive_form_filter(x0, x1, 2);
  return x3;
}

}  // namespace ad_algorithm
}  // namespace ks
