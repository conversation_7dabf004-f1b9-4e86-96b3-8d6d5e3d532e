/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_history_pay_filter.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/history_pay_filter.h"

namespace ks {
namespace ad_algorithm {

bool BSHistoryPayFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_int64<true>(*bs, BSFieldEnum::adlog_user_info_common_info_attr_key_61096, pos);
  auto x1 = history_pay_filter(x0);
  return x1;
}

}  // namespace ad_algorithm
}  // namespace ks
