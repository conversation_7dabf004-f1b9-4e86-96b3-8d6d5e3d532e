#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/order_roas_selftrain_filter.dark
class BSInnerQcpxPhotoPaidIsSelfTrainFilter : public BSItemFilterBase {
 public:
  explicit BSInnerQcpxPhotoPaidIsSelfTrainFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerQcpxPhotoPaidIsSelfTrainFilter,
                "bs_inner_qcpx_photo_paid_is_self_train_filter");
}  // namespace ad_algorithm
}  // namespace ks
