#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/inner_unifyui_random_rm_neg.dark
class BSInnerUnifyUIRandomRmNeg50Filter : public BSItemFilterBase {
 public:
  explicit BSInnerUnifyUIRandomRmNeg50Filter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerUnifyUIRandomRmNeg50Filter,
                "bs_inner_unify_u_i_random_rm_neg_50_filter");
}  // namespace ad_algorithm
}  // namespace ks
