#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_tube_show_filter.h"

namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSTubeShowFilter::BSTubeShowFilter(const std::string& conf) {
}

bool BSTubeShowFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  auto key_show = BSFieldEnum::item_tube_item_info_label_info_key_60000001;
  return BSFieldHelper::GetSingular<bool>(*bs, key_show, pos);
}

}  // namespace ad_algorithm
}  // namespace ks

