/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_inner_shelf_qcpx_photo_data_filter_v_3.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/inner_shelf_qcpx_photo_data_filter_v1.h"

namespace ks {
namespace ad_algorithm {

bool BSInnerShelfQcpxPhotoDataFilterV3::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_int64_or_default<true>(*bs, BSFieldEnum::adlog_context_sub_page_id, pos);
  auto x2 = inner_shelf_qcpx_photo_data_filter_v1(x0, 0.025);
  return x2;
}

}  // namespace ad_algorithm
}  // namespace ks
