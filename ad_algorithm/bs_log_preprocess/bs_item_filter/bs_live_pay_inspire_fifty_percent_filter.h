#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/live_pay_inspire_filter.dark
class BSLivePayInspireFiftyPercentFilter : public BSItemFilterBase {
 public:
  explicit BSLivePayInspireFiftyPercentFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSLivePayInspireFiftyPercentFilter,
                "bs_live_pay_inspire_fifty_percent_filter");
}  // namespace ad_algorithm
}  // namespace ks
