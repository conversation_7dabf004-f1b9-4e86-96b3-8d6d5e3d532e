#pragma once
#include <string>
#include <unordered_set>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_unifea_lps_filter_only_fanstop.h"
namespace ks {
namespace ad_algorithm {

// 拷贝自 bs_unifea_lps_filter_add_deep_sample2.h
// 更改 conf 准入条件为 bs_unifea_lps_filter_only_fanstop_sample_

class BSUnifeaLpsFilterOnlyFanstopSample : public BSItemFilterBase {
 public:
  explicit BSUnifeaLpsFilterOnlyFanstopSample(const std::string& conf);
  bool operator()(const BSLog& bslog, size_t pos) const;
 protected:
  BSUnifeaLpsFilterOnlyFanstop bs_unifea_lps_filter_only_fanstop_sample_;
  double neg_sample_rate_;
};

REGISTER_PLUGIN(BSItemFilterBase,
                BSUnifeaLpsFilterOnlyFanstopSample,
                "bs_unifea_lps_filter_only_fanstop_sample");
}  // namespace ad_algorithm
}  // namespace ks
