#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/search_train_data_sample.dark
class BSSearchTrainDataSample : public BSItemFilterBase {
 public:
  explicit BSSearchTrainDataSample(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSSearchTrainDataSample, "bs_search_train_data_sample");
}  // namespace ad_algorithm
}  // namespace ks
