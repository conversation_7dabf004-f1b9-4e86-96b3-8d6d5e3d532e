#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/inner_hign_cost_hign_user_filter.dark
class BSInnerHignCostHignUserFilter5000 : public BSItemFilterBase {
 public:
  explicit BSInnerHignCostHignUserFilter5000(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerHignCostHignUserFilter5000,
                "bs_inner_hign_cost_hign_user_filter_5000");
}  // namespace ad_algorithm
}  // namespace ks
