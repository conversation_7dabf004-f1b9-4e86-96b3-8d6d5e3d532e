#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_meta.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_item_live_filter_fix.h"

namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;
using ks::ad_nn::SampleInterface;

BSItemLiveFilterFix::BSItemLiveFilterFix(const std::string& conf) {
}

bool BSItemLiveFilterFix::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  int item_type = BSFieldHelper::GetSingular<int>(*bs, BSFieldEnum::adlog_item_type, pos);

  if (item_type == bs::ItemType::FANS_TOP_LIVE) {
    return true;
  }

  auto key_ad_dsp_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool has_ad_dsp_info = BSFieldHelper::GetSingular<bool>(*bs, key_ad_dsp_info_exists, pos);

  auto key_campaign_exists = BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists;
  bool has_campaign = BSFieldHelper::GetSingular<bool>(*bs, key_campaign_exists, pos);

  auto key_creative_exists = BSFieldEnum::adlog_item_ad_dsp_info_creative_exists;
  bool has_creative = BSFieldHelper::GetSingular<bool>(*bs, key_creative_exists, pos);

  if (item_type == bs::ItemType::AD_DSP && has_campaign && has_creative) {
    auto key_campaign_type = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type;
    int campaign_type = BSFieldHelper::GetSingular<int>(*bs, key_campaign_type, pos);
    auto key_creative_type = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_live_creative_type;
    int creative_type = BSFieldHelper::GetSingular<int>(*bs, key_creative_type, pos);
    if (creative_type == 1 && (campaign_type == 21 || campaign_type == 22 || campaign_type == 23)) {
      return true;
    }
  }

  if (has_ad_dsp_info && has_campaign && has_creative) {
    auto key_campaign_type = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type;
    int campaign_type = BSFieldHelper::GetSingular<int>(*bs, key_campaign_type, pos);
    auto key_creative_type = BSFieldEnum::adlog_item_ad_dsp_info_creative_base_live_creative_type;
    int creative_type = BSFieldHelper::GetSingular<int>(*bs, key_creative_type, pos);
    if (creative_type == 1 && campaign_type == 14) {
      return true;
    }
  }
  return false;
}

}  // namespace ad_algorithm
}  // namespace ks
