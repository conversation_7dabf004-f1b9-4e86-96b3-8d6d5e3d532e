#pragma once

#include <vector>
#include <string>
#include <unordered_map>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// 根据 llsid, item_id 为 key 进行过滤。
// llsid, item_id 保存在本地文件中，以 \t 分隔。
// 读取后以 llsid 为 key 保存，value 为 item_id 列表。
class BSKeyFilter : public BSItemFilterBase {
 public:
  explicit BSKeyFilter(const std::string& conf);
  bool operator()(const BSLog& bslog, size_t pos) const;

 private:
  std::unordered_map<int64_t, std::vector<int64_t>> llsid_item_ids_;
};

REGISTER_PLUGIN(BSItemFilterBase, BSKeyFilter, "bs_key_filter");

}  // namespace ad_algorithm
}  // namespace ks
