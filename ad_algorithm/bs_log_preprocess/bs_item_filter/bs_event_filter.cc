#include <vector>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_meta.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_event_filter.h"

namespace ks {
namespace ad_algorithm {

BSEventFilter::BSEventFilter(const std::string& conf) {
  std::vector<std::string> results;
  base::SplitString(conf, std::string(",+"), &results);
  bs::AdCallbackLog::EventType action_type = bs::AdCallbackLog::EventType::EVENT_UNKNOWN;
  for (const auto & str : results) {
    auto it = bs::AdCallbackLog::str_to_event_type.find(str);
    if (str.size() > 0 && it != bs::AdCallbackLog::str_to_event_type.end()) {
      ad_action_set_.insert(it->second);
    }
  }

  if (ad_action_set_.size() == 0) {
    LOG(FATAL) << "event_filter must have parametes, for example: event_filter:EVENT_CONVERSION";
  }
}

bool BSEventFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  auto key_callback_event = BSFieldEnum::adlog_item_label_info_callback_event;
  int callback_event = BSFieldHelper::GetSingular<int>(*bs, key_callback_event, pos);
  if (ad_action_set_.find(callback_event) != ad_action_set_.end()) {
    return true;
  }

  if (callback_event == bs::AdCallbackLog::EventType::EVENT_UNKNOWN) {
    auto key_action_types = BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_callback_event;
    BSRepeatedField<int> action_types(*bs, key_action_types, pos);
    for (size_t i = 0; i < action_types.size(); i++) {
      int type = action_types.Get(i);
      if (ad_action_set_.find(type) != ad_action_set_.end())
        return true;
    }
  }

  return false;
}

}  // namespace ad_algorithm
}  // namespace ks
