#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/inner_shelf_merchant_orderpay_filter_v2.dark
class BSInnerShelfMerchantOrderpayFilterV2 : public BSItemFilterBase {
 public:
  explicit BSInnerShelfMerchantOrderpayFilterV2(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerShelfMerchantOrderpayFilterV2,
                "bs_inner_shelf_merchant_orderpay_filter_v_2");
}  // namespace ad_algorithm
}  // namespace ks
