#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/inner_unifyui_random_rm_neg.dark
class BSInnerUnifyUIRandomRmNeg20Filter : public BSItemFilterBase {
 public:
  explicit BSInnerUnifyUIRandomRmNeg20Filter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerUnifyUIRandomRmNeg20Filter,
                "bs_inner_unify_u_i_random_rm_neg_20_filter");
}  // namespace ad_algorithm
}  // namespace ks
