#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/search_candidate_info_type_filter.dark
class BSSearchCandidateInfoV0Filter : public BSItemFilterBase {
 public:
  explicit BSSearchCandidateInfoV0Filter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSSearchCandidateInfoV0Filter, "bs_search_candidate_info_v_0_filter");
}  // namespace ad_algorithm
}  // namespace ks
