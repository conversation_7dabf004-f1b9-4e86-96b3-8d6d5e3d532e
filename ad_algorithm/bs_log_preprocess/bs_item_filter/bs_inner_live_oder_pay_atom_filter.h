#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/live_orderpay_atom_filter.dark
class BSInnerLiveOderPayAtomFilter : public BSItemFilterBase {
 public:
  explicit BSInnerLiveOderPayAtomFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerLiveOderPayAtomFilter, "bs_inner_live_oder_pay_atom_filter");
}  // namespace ad_algorithm
}  // namespace ks
