#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/outer_loop_left_slide_filter.dark
class BSOuterLoopLeftSlideFilter : public BSItemFilterBase {
 public:
  explicit BSOuterLoopLeftSlideFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSOuterLoopLeftSlideFilter, "bs_outer_loop_left_slide_filter");
}  // namespace ad_algorithm
}  // namespace ks
