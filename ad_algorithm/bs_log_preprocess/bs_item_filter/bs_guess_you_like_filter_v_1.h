#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/guess_you_like_filter_v0.dark
class BSGuessYouLikeFilterV1 : public BSItemFilterBase {
 public:
  explicit BSGuessYouLikeFilterV1(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSGuessYouLikeFilterV1, "bs_guess_you_like_filter_v_1");
}  // namespace ad_algorithm
}  // namespace ks
