#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/item_filter/inner_shelf_merchant_orderpay_filter_new_v0.dark
class BSInnerShelfMerchantOrderpayFilterNewV0 : public BSItemFilterBase {
 public:
  explicit BSInnerShelfMerchantOrderpayFilterNewV0(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerShelfMerchantOrderpayFilterNewV0,
                "bs_inner_shelf_merchant_orderpay_filter_new_v_0");
}  // namespace ad_algorithm
}  // namespace ks
