#pragma once

#include <string>
#include <set>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// 根据 item_type 过滤
class BSItemTypeFilter : public BSItemFilterBase {
 public:
  explicit BSItemTypeFilter(const std::string& conf);
  bool operator()(const BSLog& bslog, size_t pos) const;

 private:
  bool all_type_ = false;
  std::set<int> type_set_;
};

REGISTER_PLUGIN(BSItemFilterBase, BSItemTypeFilter, "bs_item_type_filter");

}  // namespace ad_algorithm
}  // namespace ks
