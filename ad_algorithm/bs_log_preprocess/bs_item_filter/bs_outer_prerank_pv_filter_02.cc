/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_outer_prerank_pv_filter_02.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/outer_prerank_pv_filter.h"

namespace ks {
namespace ad_algorithm {

bool BSOuterPrerankPvFilter02::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_llsid, pos);
  auto x1 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_info_attr_key_103, pos);
  auto x2 = outer_prerank_pv_filter_02(x0, x1);
  return x2;
}

}  // namespace ad_algorithm
}  // namespace ks
