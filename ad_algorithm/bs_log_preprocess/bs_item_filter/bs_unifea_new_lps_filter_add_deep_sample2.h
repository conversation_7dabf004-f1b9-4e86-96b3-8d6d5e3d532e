#pragma once
#include <string>
#include <unordered_set>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_unifea_lps_filter_add_deep.h"

namespace ks {
namespace ad_algorithm {

class BSUnifeaNewLpsFilterAddDeepSample2 : public BSItemFilterBase {
 public:
  explicit BSUnifeaNewLpsFilterAddDeepSample2(const std::string& conf);
  bool operator()(const BSLog& bslog, size_t pos) const;

 protected:
  BSUnifeaLpsFilterAddDeep bs_unifea_lps_filter_add_deep_;
  double neg_sample_rate_;
};

REGISTER_PLUGIN(BSItemFilterBase, BSUnifeaNewLpsFilterAddDeepSample2,
                                   "bs_unifea_new_lps_filter_add_deep_sample2");

}  // namespace ad_algorithm
}  // namespace ks

