#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/neg_sample_filter_brand_order_pay.dark
class BSExtractNegSampleFilterBrandOrderPay : public BSItemFilterBase {
 public:
  explicit BSExtractNegSampleFilterBrandOrderPay(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSExtractNegSampleFilterBrandOrderPay,
                "bs_extract_neg_sample_filter_brand_order_pay");
}  // namespace ad_algorithm
}  // namespace ks
