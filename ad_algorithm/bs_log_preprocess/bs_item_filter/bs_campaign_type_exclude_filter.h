#pragma once

#include <string>
#include <set>
#include <unordered_set>
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

class BSCampaignTypeExcludeFilter : public BSItemFilterBase {
 public:
  explicit BSCampaignTypeExcludeFilter(const std::string& conf);
  bool operator()(const BSLog& bslog, size_t pos) const;

 private:
  std::unordered_set<uint64_t> campaign_type_set_;
};

REGISTER_PLUGIN(BSItemFilterBase, BSCampaignTypeExcludeFilter, "bs_campaign_type_exclude_filter");

}  // namespace ad_algorithm
}  // namespace ks
