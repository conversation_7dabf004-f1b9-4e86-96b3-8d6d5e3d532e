#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/search_conv_filter.dark
class BSAdSearchConvFilterV1 : public BSItemFilterBase {
 public:
  explicit BSAdSearchConvFilterV1(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSAdSearchConvFilterV1, "bs_ad_search_conv_filter_v_1");
}  // namespace ad_algorithm
}  // namespace ks
