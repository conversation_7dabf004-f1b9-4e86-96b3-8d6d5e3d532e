#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/is_not_buyer_home_and_mall_filter.dark
class BSIsNotBuyerHomeAndMallFilter : public BSItemFilterBase {
 public:
  explicit BSIsNotBuyerHomeAndMallFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSIsNotBuyerHomeAndMallFilter, "bs_is_not_buyer_home_and_mall_filter");
}  // namespace ad_algorithm
}  // namespace ks
