#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/neg_sample_filter_fanstop_recall_base.dark
class BSExtractNegSampleFilterFanstopRecallBase : public BSItemFilterBase {
 public:
  explicit BSExtractNegSampleFilterFanstopRecallBase(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSExtractNegSampleFilterFanstopRecallBase,
                "bs_extract_neg_sample_filter_fanstop_recall_base");
}  // namespace ad_algorithm
}  // namespace ks
