#include <vector>
#include "base/strings/string_number_conversions.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_meta.h"
#include "teams/ad/ad_algorithm/log_preprocess/ad_model_kconf_util.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_standard_live_played_started_filter_compatible2.h"

namespace ks {
namespace ad_algorithm {

BSStandardLivePlayStartedFilterCompatible2::BSStandardLivePlayStartedFilterCompatible2(
    const std::string& conf) {
  if (conf.empty()) {
    return;
  }

  std::vector<std::string> results;
  base::SplitString(conf, std::string("+"), &results);
  std::vector<std::string> seg;
  for (const auto& str : results) {
    if (str.empty()) {
      continue;
    }
    seg.clear();
    base::SplitString(str, std::string("@"), &seg);
    if (seg.size() != 2) {
      continue;
    }
    if (seg[0] == "inspire_ns" && base::StringToInt(seg[1], &inspire_ns_percent_)) {
      LOG(INFO) << "StandardLivePlayStartedFilterCompatible2 inspire_ns_percent: " << inspire_ns_percent_;
    }
  }
}

bool BSStandardLivePlayStartedFilterCompatible2::inspire_negative_sample(
    const BSLog& bslog, size_t pos, bool is_inspire_live_request) const {
  auto bs = bslog.GetBS();

  auto key_label_event_order_paied = BSFieldEnum::adlog_item_label_info_label_infos_key_10000015;
  bool has_event_order_paied = BSFieldHelper::GetSingular<bool>(*bs, key_label_event_order_paied, pos);

  auto key_label_order_paid_cnt = BSFieldEnum::adlog_item_label_info_label_infos_key_20000014;
  bool has_order_paid_cnt = BSFieldHelper::GetSingular<bool>(*bs, key_label_order_paid_cnt, pos);
  int order_paid_cnt = BSFieldHelper::GetSingular<int>(*bs, key_label_order_paid_cnt, pos);

  auto key_label_mix_front_order_paid_cnt = BSFieldEnum::adlog_item_label_info_label_infos_key_20000027;
  bool has_mix_front_order_paid_cnt =
      BSFieldHelper::GetSingular<bool>(*bs, key_label_mix_front_order_paid_cnt, pos);
  int mix_front_order_paid_cnt =
      BSFieldHelper::GetSingular<int>(*bs, key_label_mix_front_order_paid_cnt, pos);

  if (has_event_order_paied) { return true;}
  if (has_order_paid_cnt && order_paid_cnt >= 1) {return true;}
  if (has_mix_front_order_paid_cnt && mix_front_order_paid_cnt >= 1) {return true;}

  if (is_inspire_live_request) {
    double random_number = ad_base::AdRandom::GetDouble();
    if (inspire_ns_percent_ < 100 && random_number * 100 > inspire_ns_percent_) {
      return false;
    }
  }

  return true;
}

bool BSStandardLivePlayStartedFilterCompatible2::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  bool live_played_started = false;
  bool standard_live_played_started = false;

  auto key_label_live_play_started = BSFieldEnum::adlog_item_label_info_label_infos_key_68;
  bool has_live_play_started = BSFieldHelper::GetSingular<bool>(*bs, key_label_live_play_started, pos);
  if (has_live_play_started) {
    live_played_started = true;
  }

  auto key_label_standard_live_play_started = BSFieldEnum::adlog_item_label_info_label_infos_key_20000001;
  bool has_standard_live_play_started =
      BSFieldHelper::GetSingular<bool>(*bs, key_label_standard_live_play_started, pos);
  if (has_standard_live_play_started) {
    standard_live_played_started = true;
  }

  bool is_inspire_live_request = false;
  auto enum_sub_page_id = BSFieldEnum::adlog_context_sub_page_id;
  int64_t sub_page_id =
      BSFieldHelper::GetSingular<int64_t>(*bs, enum_sub_page_id);
  if (sub_page_id == 100011292 || sub_page_id == 100011503 || sub_page_id == 100012061 ||
      AdModelKconfUtil::inspireLiveSubpageConf()->count(sub_page_id)) {
    is_inspire_live_request = true;
  }

  auto enum_item_type = BSFieldEnum::adlog_item_type;
  const int64_t item_type = BSFieldHelper::GetSingular<int64_t>(*bs, enum_item_type, pos);
  const auto key_fans_photo_info_exists = BSFieldEnum::adlog_item_fans_top_info_photo_info_exists;
  bool has_fans_photo_info = BSFieldHelper::GetSingular<bool>(*bs, key_fans_photo_info_exists, pos);
  if (item_type == bs::ItemType::FANS_TOP && has_fans_photo_info) {
    auto fans_retrieval_photo_info =
        BSFieldEnum::adlog_item_fans_top_info_photo_info_retrieval_photo_info_exists;
    bool has_retrieval_photo_info = BSFieldHelper::GetSingular<bool>(*bs, fans_retrieval_photo_info, pos);
    if (has_retrieval_photo_info) {
      auto fans_is_living = BSFieldEnum::adlog_item_fans_top_info_photo_info_retrieval_photo_info_is_living;
      return BSFieldHelper::GetSingular<bool>(*bs, fans_is_living, pos) &&
          live_played_started && inspire_negative_sample(bslog, pos, is_inspire_live_request);
    }
  }

  if (item_type == bs::ItemType::FANS_TOP_LIVE) {
    return standard_live_played_started && inspire_negative_sample(bslog, pos, is_inspire_live_request);
  }

  bool has_campaign_base = BSFieldHelper::GetSingular<bool>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists, pos);
  bool has_creative = BSFieldHelper::GetSingular<bool>(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_exists, pos);
  if (item_type == bs::ItemType::AD_DSP && has_campaign_base && has_creative) {
    int64_t live_creative_type = BSFieldHelper::GetSingular<int64_t>(*bs,
        BSFieldEnum::adlog_item_ad_dsp_info_creative_base_live_creative_type, pos);
    if (live_creative_type != 1) {
      return live_played_started && inspire_negative_sample(bslog, pos, is_inspire_live_request);
    } else {
      return standard_live_played_started && inspire_negative_sample(bslog, pos, is_inspire_live_request);
    }
  }

  if (item_type == bs::ItemType::AD_BRAND && (standard_live_played_started || live_played_started)) {
    return inspire_negative_sample(bslog, pos, is_inspire_live_request);
  }

  return false;
}

}  // namespace ad_algorithm
}  // namespace ks
