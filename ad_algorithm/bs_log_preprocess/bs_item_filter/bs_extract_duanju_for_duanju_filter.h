#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/outer_loop_conv_for_duanju_filter.dark
class BSExtractDuanjuForDuanjuFilter : public BSItemFilterBase {
 public:
  explicit BSExtractDuanjuForDuanjuFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSExtractDuanjuForDuanjuFilter, "bs_extract_duanju_for_duanju_filter");
}  // namespace ad_algorithm
}  // namespace ks
