#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/outer_event_invoke_kaiworks_filter.dark
class BSOuterEventInvokeKaiworksFlter : public BSItemFilterBase {
 public:
  explicit BSOuterEventInvokeKaiworksFlter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSOuterEventInvokeKaiworksFlter, "bs_outer_event_invoke_kaiworks_flter");
}  // namespace ad_algorithm
}  // namespace ks
