/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_inner_hign_cost_hign_user_filter_5000.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/buyer_effective_type.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/inner_high_cost_hign_user_filter.h"

namespace ks {
namespace ad_algorithm {

bool BSInnerHignCostHignUserFilter5000::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_str<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_1011, pos);
  auto x1 = get_user_buyer_effective_type(x0);
  auto x2 = get_bslog_int64_list(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_10316, pos);
  auto x4 = inner_high_cost_high_user_filter(x1, x2, 5000);
  return x4;
}

}  // namespace ad_algorithm
}  // namespace ks
