#pragma once

#include <string>
#include <unordered_set>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

class BSPageSubPageIdFilter : public BSItemFilterBase {
 public:
  explicit BSPageSubPageIdFilter(const std::string& conf);

  virtual bool operator()(const BSLog& bslog, size_t pos) const;

 private:
  std::unordered_set<std::string> types_;
  bool filterFlag = false;
};

REGISTER_PLUGIN(BSItemFilterBase, BSPageSubPageIdFilter, "bs_page_subpage_id_filter");

}  // namespace ad_algorithm
}  // namespace ks
