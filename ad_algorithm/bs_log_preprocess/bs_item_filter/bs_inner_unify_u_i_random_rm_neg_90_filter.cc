/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_inner_unify_u_i_random_rm_neg_90_filter.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/check_label_infos.h"

namespace ks {
namespace ad_algorithm {

bool BSInnerUnifyUIRandomRmNeg90Filter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000015, pos);
  auto x1 = keep_label_infos_bool(x0);
  auto x2 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_307, pos);
  auto x3 = keep_label_infos_bool(x2);
  auto x4 = logic_or(x1, x3);
  auto x5 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_308, pos);
  auto x6 = keep_label_infos_bool(x5);
  auto x7 = logic_or(x4, x6);
  auto x8 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000005, pos);
  auto x9 = keep_label_infos_bool(x8);
  auto x10 = logic_or(x7, x9);
  auto x11 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_68, pos);
  auto x12 = keep_label_infos_bool(x11);
  auto x13 = logic_or(x10, x12);
  auto x14 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000001, pos);
  auto x15 = keep_label_infos_bool(x14);
  auto x16 = logic_or(x13, x15);
  auto x17 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_22, pos);
  auto x18 = keep_label_infos_bool(x17);
  auto x19 = logic_or(x16, x18);
  auto x21 = random_less(0.1);
  auto x22 = logic_or(x19, x21);
  return x22;
}

}  // namespace ad_algorithm
}  // namespace ks
