/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_industry_game_filter.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/industry_game_filter.h"

namespace ks {
namespace ad_algorithm {

bool BSIndustryGameFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5002328, pos);
  auto x1 = industry_game_filter(x0);
  return x1;
}

}  // namespace ad_algorithm
}  // namespace ks
