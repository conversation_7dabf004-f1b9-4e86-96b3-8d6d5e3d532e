#pragma once
#include <vector>
#include <set>
#include <string>
#include <unordered_set>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

class BSTDMTreeFilter : public BSItemFilterBase {
 public:
  explicit BSTDMTreeFilter(const std::string&);
  bool operator()(const BSLog& bslog, size_t pos) const;

 private:
  bool all_type_ = false;
  std::set<int> tree_set_;
  std::hash<std::string> hash_fn_;
};

REGISTER_PLUGIN(BSItemFilterBase, BSTDMTreeFilter, "bs_tdm_tree_filter");

}  // namespace ad_algorithm
}  // namespace ks
