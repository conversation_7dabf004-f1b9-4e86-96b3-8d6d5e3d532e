#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_feed_impression_filter.h"

namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSFeedImpressionFilter::BSFeedImpressionFilter(const std::string& conf) {
}

bool BSFeedImpressionFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  auto key_feed_impression = BSFieldEnum::adlog_item_label_info_feed_impression;
  bool feed_impression = BSFieldHelper::GetSingular<bool>(*bs, key_feed_impression, pos);

  if (feed_impression) {
    return true;
  }
  return false;
}


}  // namespace ad_algorithm
}  // namespace ks

