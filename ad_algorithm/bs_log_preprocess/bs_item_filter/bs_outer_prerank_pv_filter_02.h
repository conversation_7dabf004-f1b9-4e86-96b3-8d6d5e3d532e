#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/outer_prerank_pv_filter.dark
class BSOuterPrerankPvFilter02 : public BSItemFilterBase {
 public:
  explicit BSOuterPrerankPvFilter02(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSOuterPrerankPvFilter02, "bs_outer_prerank_pv_filter_02");
}  // namespace ad_algorithm
}  // namespace ks
