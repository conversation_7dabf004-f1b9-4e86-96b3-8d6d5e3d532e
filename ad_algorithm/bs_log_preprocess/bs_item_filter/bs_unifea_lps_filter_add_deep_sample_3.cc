/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_unifea_lps_filter_add_deep_sample_3.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/unifea_lps_filter_add_deep_sample.h"

namespace ks {
namespace ad_algorithm {

bool BSUnifeaLpsFilterAddDeepSample3::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_item_impression, pos);
  auto x1 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_item_click, pos);
  auto x2 =
      get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_landing_page_form_submitted, pos);
  auto x3 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_approximate_purchase, pos);
  auto x4 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
  auto x5 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type, pos);
  auto x6 = get_bslog_int64(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000022, pos);
  auto x7 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_786, pos);
  auto x8 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000525, pos);
  auto x9 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_type, pos);
  auto x10 = get_bslog_int64<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_14, pos);
  auto x11 = get_bslog_int64_or_default<true>(*bs, BSFieldEnum::adlog_context_page_id, pos);
  auto x12 = get_bslog_int64_or_default<true>(*bs, BSFieldEnum::adlog_context_sub_page_id, pos);
  auto x13 = unifea_lps_filter_add_deep_sample3(x0, x1, x2, x3, x4, x5, x6, x7, x8, x9, x10, x11, x12);
  return x13;
}

}  // namespace ad_algorithm
}  // namespace ks
