/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_inner_live_pay_consis_0228_filter.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/live_pay_consis_filter_0228.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

bool BSInnerLivePayConsis0228Filter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_map_int64_str<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_437_key,
                                          BSFieldEnum::adlog_context_info_common_attr_key_437_value, pos);
  auto x1 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_id, pos);
  auto x2 = get_value_from_map(x0, x1);
  auto x3 = live_pay_consis_0228_filter(x2);
  return x3;
}

}  // namespace ad_algorithm
}  // namespace ks
