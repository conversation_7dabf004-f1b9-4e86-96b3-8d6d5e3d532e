#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/inner_qcpx_live_coupon_full_link_filter.dark
class BSInnerQcpxLiveCouponAmountOrderFullLinkFilter : public BSItemFilterBase {
 public:
  explicit BSInnerQcpxLiveCouponAmountOrderFullLinkFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerQcpxLiveCouponAmountOrderFullLinkFilter,
                "bs_inner_qcpx_live_coupon_amount_order_full_link_filter");
}  // namespace ad_algorithm
}  // namespace ks
