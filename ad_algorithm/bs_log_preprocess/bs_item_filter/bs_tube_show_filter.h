#pragma once
#include <string>
#include <unordered_set>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

class BSTubeShowFilter : public BSItemFilterBase {
 public:
  explicit BSTubeShowFilter(const std::string&);
  bool operator()(const BSLog& bslog, size_t pos) const;
};

REGISTER_PLUGIN(BSItemFilterBase, BSTubeShowFilter, "bs_tube_show_filter");

}  // namespace ad_algorithm
}  // namespace ks
