#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/outer_prerank_pv_filter.dark
class BSOuterPrerankPvFilter22 : public BSItemFilterBase {
 public:
  explicit BSOuterPrerankPvFilter22(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSOuterPrerankPvFilter22, "bs_outer_prerank_pv_filter_22");
}  // namespace ad_algorithm
}  // namespace ks
