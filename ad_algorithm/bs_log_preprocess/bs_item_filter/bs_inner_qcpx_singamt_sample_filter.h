#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/inner_loop_live_singleamt_coupon.dark
class BSInnerQcpxSingamtSampleFilter : public BSItemFilterBase {
 public:
  explicit BSInnerQcpxSingamtSampleFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSInnerQcpxSingamtSampleFilter, "bs_inner_qcpx_singamt_sample_filter");
}  // namespace ad_algorithm
}  // namespace ks
