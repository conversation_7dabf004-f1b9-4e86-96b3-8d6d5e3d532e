/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_extract_neg_sample_filter_brand_order_pay.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/neg_sample_filter_brand_order_pay.h"

namespace ks {
namespace ad_algorithm {

bool BSExtractNegSampleFilterBrandOrderPay::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000108, pos);
  auto x1 = neg_sample_filter_brand_order_pay(x0);
  return x1;
}

}  // namespace ad_algorithm
}  // namespace ks
