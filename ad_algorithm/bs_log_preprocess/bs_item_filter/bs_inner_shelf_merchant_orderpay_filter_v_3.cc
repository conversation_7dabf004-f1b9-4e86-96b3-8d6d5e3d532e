/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_inner_shelf_merchant_orderpay_filter_v_3.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/inner_shelf_merchant_orderpay_filter_v0.h"

namespace ks {
namespace ad_algorithm {

bool BSInnerShelfMerchantOrderpayFilterV3::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
  auto x1 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type, pos);
  auto x2 = get_bslog_int64_or_default<true>(*bs, BSFieldEnum::adlog_context_page_id, pos);
  auto x3 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_1, pos);
  auto x5 = inner_shelf_merchant_orderpay_filter_v0(x0, x1, x2, x3, 0.5);
  return x5;
}

}  // namespace ad_algorithm
}  // namespace ks
