#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_unifea_lps_filter_add_deep_leads_submit.h"
#include <vector>
#include "base/strings/string_number_conversions.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_base/src/math/random/random.h"

namespace ks {
namespace ad_algorithm {

BSUnifeaLpsFilterAddDeepLeadsSubmit::BSUnifeaLpsFilterAddDeepLeadsSubmit(
    const std::string& conf)
    : bs_unifea_lps_filter_add_deep_(conf) {
  neg_sample_rate_ = 1.0;
  if (conf != "") {
    base::StringToDouble(conf, &neg_sample_rate_);
  }
}

bool BSUnifeaLpsFilterAddDeepLeadsSubmit::operator()(const BSLog& bslog,
                                                     size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }
  bool res = bs_unifea_lps_filter_add_deep_(bslog, pos);
  auto key_imp = BSFieldEnum::adlog_item_label_info_item_impression;
  // leads_submit 也当做表单
  auto key_lps = BSFieldEnum::adlog_item_label_info_landing_page_form_submitted;
  auto key_leads_submit = BSFieldEnum::adlog_item_label_info_label_infos_key_786;
  bool imp = BSFieldHelper::GetSingular<bool>(*bs, key_imp, pos);
  bool lps = BSFieldHelper::GetSingular<bool>(*bs, key_lps, pos);
  bool leads_submit = BSFieldHelper::GetSingular<bool>(*bs, key_leads_submit, pos);
  // aux label 也不采样
  auto key_label_leave_time = BSFieldEnum::adlog_item_label_info_label_infos_key_20000022;
  bool leave =
      BSFieldHelper::GetSingular<uint64_t>(*bs, key_label_leave_time, pos) > 10000;
  auto key_approximate_purchase = BSFieldEnum::adlog_item_label_info_approximate_purchase;
  bool pur = BSFieldHelper::GetSingular<bool>(*bs, key_approximate_purchase, pos);
  auto key_item_click = BSFieldEnum::adlog_item_label_info_item_click;
  bool clk = BSFieldHelper::GetSingular<bool>(*bs, key_item_click, pos);
  auto key_ocpc = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type;
  int ocpc_action_type = BSFieldHelper::GetSingular<int>(*bs, key_ocpc, pos);
  bool if_ocpc_lead_submit = (ocpc_action_type == 786);
  bool keep_item = if_ocpc_lead_submit;
  keep_item = (keep_item && imp);
  // 表单负样本采样
  double r = ad_base::AdRandom::GetDouble();
  bool t = r > neg_sample_rate_;
  if (keep_item && !leads_submit && !leave && !pur && !clk && t) {
    keep_item = false;
  }
  return keep_item;
}

}  // namespace ad_algorithm
}  // namespace ks
