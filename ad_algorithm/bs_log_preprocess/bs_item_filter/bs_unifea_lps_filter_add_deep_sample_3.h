#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/unifea_lps_filter_add_deep_sample.dark
class BSUnifeaLpsFilterAddDeepSample3 : public BSItemFilterBase {
 public:
  explicit BSUnifeaLpsFilterAddDeepSample3(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSUnifeaLpsFilterAddDeepSample3, "bs_unifea_lps_filter_add_deep_sample_3");
}  // namespace ad_algorithm
}  // namespace ks
