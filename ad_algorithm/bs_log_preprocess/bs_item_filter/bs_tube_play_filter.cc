#include <iostream>
#include <cstdlib>
#include <ctime>
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_field_helper.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_tube_play_filter.h"

namespace ks {
namespace ad_algorithm {

using ks::ad_nn::BSFieldEnum;
using ks::ad_nn::BSFieldHelper;

BSTubePlayFilter::BSTubePlayFilter(const std::string& conf) {
}

bool BSTubePlayFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }
  std::srand(std::time(0));
  bool condition = (std::rand() % 100) < 30;
  auto key_show_play = BSFieldEnum::item_tube_item_info_label_info_key_60000021;
  bool play = BSFieldHelper::GetSingular<bool>(*bs, key_show_play, pos);
  auto key_show = BSFieldEnum::item_tube_item_info_label_info_key_60000001;
  bool show = BSFieldHelper::GetSingular<bool>(*bs, key_show, pos);
  return  show && (play || (!play && condition));
}

}  // namespace ad_algorithm
}  // namespace ks


