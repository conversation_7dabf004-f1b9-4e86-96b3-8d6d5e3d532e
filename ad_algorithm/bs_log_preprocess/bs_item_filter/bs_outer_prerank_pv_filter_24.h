#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/outer_prerank_pv_filter.dark
class BSOuterPrerankPvFilter24 : public BSItemFilterBase {
 public:
  explicit BSOuterPrerankPvFilter24(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSOuterPrerankPvFilter24, "bs_outer_prerank_pv_filter_24");
}  // namespace ad_algorithm
}  // namespace ks
