#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/is_not_guess_you_like_filter.dark
class BSIsNotGuessYouLikeFilter : public BSItemFilterBase {
 public:
  explicit BSIsNotGuessYouLikeFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSIsNotGuessYouLikeFilter, "bs_is_not_guess_you_like_filter");
}  // namespace ad_algorithm
}  // namespace ks
