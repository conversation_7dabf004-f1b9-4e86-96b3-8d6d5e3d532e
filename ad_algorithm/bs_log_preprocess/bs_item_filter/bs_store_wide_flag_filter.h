#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/store_wide_flag_filter.dark
class BSStoreWideFlagFilter : public BSItemFilterBase {
 public:
  explicit BSStoreWideFlagFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSStoreWideFlagFilter, "bs_store_wide_flag_filter");
}  // namespace ad_algorithm
}  // namespace ks
