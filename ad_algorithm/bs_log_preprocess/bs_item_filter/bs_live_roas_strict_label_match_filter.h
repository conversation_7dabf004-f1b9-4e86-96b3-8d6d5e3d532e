#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/live_roas_strict_label_match_filter.dark
class BSLiveRoasStrictLabelMatchFilter : public BSItemFilterBase {
 public:
  explicit BSLiveRoasStrictLabelMatchFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSLiveRoasStrictLabelMatchFilter, "bs_live_roas_strict_label_match_filter");
}  // namespace ad_algorithm
}  // namespace ks
