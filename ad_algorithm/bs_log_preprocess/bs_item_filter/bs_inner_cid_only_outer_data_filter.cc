/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_inner_cid_only_outer_data_filter.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/cid_filter.h"

namespace ks {
namespace ad_algorithm {

bool BSInnerCidOnlyOuterDataFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_id, pos);
  auto x1 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
  auto x2 = get_bslog_int64_or_default<true>(*bs, BSFieldEnum::adlog_context_page_id, pos);
  auto x3 = get_bslog_int64_or_default<true>(*bs, BSFieldEnum::adlog_context_sub_page_id, pos);
  auto x4 = get_bslog_bool_or_default(*bs, BSFieldEnum::adlog_item_label_info_item_impression, pos);
  auto x5 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type, pos);
  auto x6 = cid_filter(x0, x1, x2, x3, x4, x5);
  return x6;
}

}  // namespace ad_algorithm
}  // namespace ks
