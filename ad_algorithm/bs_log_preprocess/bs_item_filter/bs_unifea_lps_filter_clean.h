#pragma once
#include <string>
#include <unordered_set>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_item_type_filter.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_lps_union_pay_filter.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_interactive_form_filter.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_page_subpage_id_filter.h"

namespace ks {
namespace ad_algorithm {

// 对齐 unifea 表单过滤逻辑。这个逻辑有点奇怪，当前 item 的结果会依赖其他几个 item 的结果。
// 依赖如下几个 item_filter:
// ["bs_item_type_filter:AD_DSP",
// "bs_lps_union_pay_filter:item_impression",
// "bs_interactive_form_filter:1+2",
// "bs_page_subpage_id_filter:100012268-100012271@100012269-100012270#0"]
//
// 具体逻辑如下:
//    1. 初始化计数 cnt = 0
//    2. 遍历 item 下标，当其他几个 item_filter 结果求交集为 true 的时候，判断当前 item 的
//       item_impression 是否为 true , 如果为 true 则将 cnt += 1, 为 false 时候不做处理
//    3. 如果最终 cnt == 0 , 则认为当前样的的所有 item 都是负样本，将每个 item 的结果都
//       设置为 false 。如果 cnt > 0 , 则以其他几个 item_filter 的结果求交为最终结果。
class BSUnifeaLpsFilterClean : public BSItemFilterBase {
 public:
  explicit BSUnifeaLpsFilterClean(const std::string& conf);
  explicit BSUnifeaLpsFilterClean(const std::string& conf1,
                             const std::string& conf2,
                             const std::string& conf3,
                             const std::string& conf4);
  bool operator()(const BSLog& bslog, size_t pos) const;

 protected:
  BSItemTypeFilter bs_item_type_filter_;
  BSLpsUnionPayFilter bs_lps_union_pay_filter_;
  BSInteractiveFormFilter bs_interactive_form_filter_;
  BSPageSubPageIdFilter bs_page_subpage_id_filter_;
};

REGISTER_PLUGIN(BSItemFilterBase, BSUnifeaLpsFilterClean, "bs_unifea_lps_filter_clean");

}  // namespace ad_algorithm
}  // namespace ks
