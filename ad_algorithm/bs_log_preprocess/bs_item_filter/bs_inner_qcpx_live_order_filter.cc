/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_inner_qcpx_live_order_filter.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/inner_qcpx_live_order_filter.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

bool BSInnerQcpxLiveOrderFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_type, pos);
  auto x1 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type, pos);
  auto x2 = get_bslog_int64_or_default(
      *bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_live_creative_type, pos);
  auto x3 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type, pos);
  auto x4 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000001, pos);
  auto x5 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_68, pos);
  auto x6 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_10000015, pos);
  auto x7 = get_bslog_bool(*bs, BSFieldEnum::adlog_item_label_info_label_infos_key_20000014, pos);
  auto x8 = get_bslog_map_int32_int64(*bs, BSFieldEnum::adlog_item_rank_params_key,
                                      BSFieldEnum::adlog_item_rank_params_value, pos);
  auto x10 = get_value_from_map(x8, 1108);
  auto x11 = cast_to_float(x10);
  auto x14 = get_value_from_map(x8, 1107);
  auto x15 = cast_to_float(x14);
  auto x18 = get_value_from_map(x8, 1224);
  auto x19 = cast_to_float(x18);
  auto x22 = get_value_from_map(x8, 1225);
  auto x23 = cast_to_float(x22);
  auto x26 = get_value_from_map(x8, 1127);
  auto x27 = cast_to_int64(x26);
  auto x28 = inner_qcpx_live_order_filter(x0, x1, x2, x3, x4, x5, x6, x7, x11, x15, x19, x23, x27);
  return x28;
}

}  // namespace ad_algorithm
}  // namespace ks
