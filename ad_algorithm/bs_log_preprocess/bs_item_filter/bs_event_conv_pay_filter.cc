#include <string>
#include <vector>
#include <unordered_set>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_event_conv_pay_filter.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/sim_proto/bs_ad_callback_log_event_type.pb.h"

namespace ks {
namespace ad_algorithm {

// 保留从点击开始的激活样本，通过参数指定激活类型，如 click_event_filter:EVENT_CONVERSION
BSEventConvPayFilter::BSEventConvPayFilter(const std::string& conf) {
  std::vector<std::string> results;
  base::SplitString(conf, std::string("+", 1), &results);
  for (const auto& str : results) {
    LOG(INFO) << "campaign type " << std::stoull(str.c_str());
    ad_campaign_type_set_.insert(std::stoull(str.c_str()));
  }
}

bool BSEventConvPayFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }
  auto key_callback_event = BSFieldEnum::adlog_item_label_info_callback_event;
  auto val = BSFieldHelper::GetSingular<int>(*bs, key_callback_event, pos);
  if (val == ::bs::kuaishou::ad::AdCallbackLog::EVENT_CONVERSION) {
    return true;
  }
  if (val == ::bs::kuaishou::ad::AdCallbackLog::EVENT_PAY) {
    auto k1 = BSFieldEnum::adlog_item_ad_dsp_info_exists;
    auto v1 = BSFieldHelper::GetSingular<int>(*bs, k1, pos);
    auto k2 = BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists;
    auto v2 = BSFieldHelper::GetSingular<int>(*bs, k2, pos);
    auto k3 = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists;
    auto v3 = BSFieldHelper::GetSingular<int>(*bs, k3, pos);
    if (v1 && v2 && v3) {
      auto key_campaign_base_type = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type;
      uint64_t val = BSFieldHelper::GetSingular<uint64_t>(*bs, key_callback_event, pos);
      if (ad_campaign_type_set_.find(val) != ad_campaign_type_set_.end()) {
        return true;
      }
    }
    return false;
  }
  if (val == ::bs::kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN) {
    auto action_types = BSFieldEnum::adlog_item_ad_dsp_info_call_back_type_callback_event;
    BSRepeatedField<uint64_t> attr_key(*bs, action_types, pos);
    for (int i = 0; i < attr_key.size(); i++) {
      auto type = attr_key.Get(i);
      if (type == ::bs::kuaishou::ad::AdCallbackLog::EVENT_CONVERSION) {
        return true;
      } else if (type == ::bs::kuaishou::ad::AdCallbackLog::EVENT_PAY) {
        auto k1 = BSFieldEnum::adlog_item_ad_dsp_info_exists;
        auto v1 = BSFieldHelper::GetSingular<int>(*bs, k1, pos);
        auto k2 = BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists;
        auto v2 = BSFieldHelper::GetSingular<int>(*bs, k2, pos);
        auto k3 = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists;
        auto v3 = BSFieldHelper::GetSingular<int>(*bs, k3, pos);
        if (v1 && v2 && v3) {
          auto key_campaign_base_type = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type;
          uint64_t val = BSFieldHelper::GetSingular<uint64_t>(*bs, key_campaign_base_type, pos);
          if (ad_campaign_type_set_.find(val) != ad_campaign_type_set_.end()) {
            return true;
          }
        }
        return false;
      }
    }
  }
  return false;
}

}  // namespace ad_algorithm
}  // namespace ks

