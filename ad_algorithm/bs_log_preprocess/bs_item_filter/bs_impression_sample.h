#pragma once
#include <string>
#include <unordered_set>
#include "base/strings/string_split.h"
#include "base/strings/string_number_conversions.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"
#include "teams/ad/ad_base/src/math/random/random.h"

namespace ks {
namespace ad_algorithm {

class BSImpressionSample : public BSItemFilterBase {
 public:
  explicit BSImpressionSample(const std::string&);
  bool operator()(const BSLog& bslog, size_t pos) const;

  bool is_landsub_item(const BSLog& bslog, size_t pos) const;
 private:
  float neg_sample_rate;
};

REGISTER_PLUGIN(BSItem<PERSON>ilterBase, BSImpressionSample, "bs_impression_sample");

}  // namespace ad_algorithm
}  // namespace ks

