#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/live_roas_gmv_filter.dark
class BSLiveRoasGmvFilter : public BSItemFilterBase {
 public:
  explicit BSLiveRoasGmvFilter(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSLiveRoasGmvFilter, "bs_live_roas_gmv_filter");
}  // namespace ad_algorithm
}  // namespace ks
