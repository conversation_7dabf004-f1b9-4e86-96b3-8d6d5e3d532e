#pragma once

#include <string>
#include <unordered_set>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// 保留从点击开始的激活样本，通过参数指定激活类型，如 click_event_filter:EVENT_CONVERSION
class BSEventConvPayFilter : public BSItemFilterBase {
 public:
  explicit BSEventConvPayFilter(const std::string&);
  virtual bool operator()(const BSLog& bslog, size_t pos) const;

 private:
  std::unordered_set<uint64_t> ad_campaign_type_set_;
};

REGISTER_PLUGIN(BSItemFilterBase, BSEventConvPayFilter, "bs_event_conv_pay_filter");

}  // namespace ad_algorithm
}  // namespace ks
