#include <vector>
#include "base/strings/string_split.h"
#include "base/strings/string_number_conversions.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_unifea_new_lps_filter_add_deep_sample21.h"

namespace ks {
namespace ad_algorithm {

BSUnifeaNewLpsFilterAddDeepSample21::BSUnifeaNewLpsFilterAddDeepSample21(const std::string& conf):
  bs_unifea_lps_filter_add_deep_(conf) {
  neg_sample_rate_ = 1.0;
  if (conf != "") {
    base::StringToDouble(conf, &neg_sample_rate_);
  }
}

bool BSUnifeaNewLpsFilterAddDeepSample21::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }
  bool res = bs_unifea_lps_filter_add_deep_(bslog, pos);
  auto key_imp = BSFieldEnum::adlog_item_label_info_item_impression;
  // leads_submit 也当做表单
  // EVENT_PRIVATE_MESSAGE_SENT 也当做表单
  auto key_lps = BSFieldEnum::adlog_item_label_info_landing_page_form_submitted;
  auto key_leads_submit = BSFieldEnum::adlog_item_label_info_label_infos_key_786;
  auto key_message = BSFieldEnum::adlog_item_label_info_label_infos_key_10000525;
  bool imp = BSFieldHelper::GetSingular<bool>(*bs, key_imp, pos);
  bool lps = BSFieldHelper::GetSingular<bool>(*bs, key_lps, pos);
  bool LEADS_SUBMIT = BSFieldHelper::GetSingular<bool>(*bs, key_leads_submit, pos);
  bool EVENT_PRIVATE_MESSAGE_SENT = BSFieldHelper::GetSingular<bool>(*bs, key_message, pos);
  // aux label 也不采样
  auto key_label_leave_time = BSFieldEnum::adlog_item_label_info_label_infos_key_20000022;
  bool leave = BSFieldHelper::GetSingular<uint64_t>(*bs, key_label_leave_time, pos) > 10000;
  auto key_approximate_purchase = BSFieldEnum::adlog_item_label_info_approximate_purchase;
  bool pur = BSFieldHelper::GetSingular<bool>(*bs, key_approximate_purchase, pos);
  auto key_item_click = BSFieldEnum::adlog_item_label_info_item_click;
  bool clk = BSFieldHelper::GetSingular<bool>(*bs, key_item_click, pos);

  auto key_campaign_type = BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type;
  uint64_t campaign_type = BSFieldHelper::GetSingular<uint64_t>(*bs, key_campaign_type, pos);

  res = (res && imp);
  // 表单负样本采样
  // acttion_type 对齐 label
  auto key_action_type = BSFieldEnum::adlog_item_ad_dsp_info_unit_base_ocpc_action_type;
  int ocpc_action_type = BSFieldHelper::GetSingular<int>(*bs, key_action_type, pos);
  if (ocpc_action_type == 926) {
    lps |= EVENT_PRIVATE_MESSAGE_SENT;
  } else if (ocpc_action_type == 786 || (ocpc_action_type == 948 && campaign_type == 24)) {
    lps |= LEADS_SUBMIT;
  }
  double r = ad_base::AdRandom::GetDouble();
  bool t = r > neg_sample_rate_;
  if (res && !lps && !leave && !pur && !clk && t) {
    res = false;
  }

  return res;
}

}  // namespace ad_algorithm
}  // namespace ks
