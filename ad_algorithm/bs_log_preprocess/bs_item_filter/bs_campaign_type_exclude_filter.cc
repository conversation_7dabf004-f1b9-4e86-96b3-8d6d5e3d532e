#include <vector>
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_meta.h"
#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_campaign_type_exclude_filter.h"

namespace ks {
namespace ad_algorithm {

BSCampaignTypeExcludeFilter::BSCampaignTypeExcludeFilter(const std::string& conf) {
  std::vector<std::string> results;
  base::SplitString(conf, std::string("@", 1), &results);
  for (const auto& str : results) {
    LOG(INFO) << "campaign type " << std::stoull(str.c_str());
    campaign_type_set_.insert(std::stoull(str.c_str()));
  }
}

bool BSCampaignTypeExcludeFilter::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return false;
  }

  auto enum_info_exists = BSFieldEnum::adlog_item_ad_dsp_info_exists;
  bool info_exists =
      BSFieldHelper::GetSingular<bool>(*bs, enum_info_exists, pos);

  auto enum_campaign_exists =
      BSFieldEnum::adlog_item_ad_dsp_info_campaign_exists;
  bool campaign_exists =
      BSFieldHelper::GetSingular<bool>(*bs, enum_campaign_exists, pos);

  auto enum_campaign_base_exists =
      BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_exists;
  bool campaign_base_exists =
      BSFieldHelper::GetSingular<bool>(*bs, enum_campaign_base_exists, pos);

  if (info_exists && campaign_exists && campaign_base_exists) {
    auto enum_campaign_type =
        BSFieldEnum::adlog_item_ad_dsp_info_campaign_base_type;
    uint64_t campaign_type =
        BSFieldHelper::GetSingular<uint64_t>(*bs, enum_campaign_type, pos);
    if (campaign_type_set_.find(campaign_type) != campaign_type_set_.end()) {
      return false;
    }
  }

  return true;
}

}  // namespace ad_algorithm
}  // namespace ks
