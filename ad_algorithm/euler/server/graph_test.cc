#include <chrono>
#include <iostream>
#include <vector>
#include <thread>
#include "euler/client/graph.h"

int main() {
  euler::client::GraphConfig config;
  config.Add("mode", "Local");
  config.Add("directory", ".");  // 图数据目录
  auto graph = euler::client::Graph::NewGraph(config);

  graph->GetFullNeighbor({0, 1}, {0, 1}, [](const euler::client::IDWeightPairVec& result) {
    for (const auto& neighbors : result) {
      for (const auto& tuple : neighbors) {
        euler::client::NodeID target;
        float weight;
        int32_t edge_type;
        std::tie(target, weight, edge_type) = tuple;
        std::cout << "(" << target << ", " << weight << ", " << edge_type << ") ";
      }
      std::cout << std::endl;
    }
  });
  std::this_thread::sleep_for(std::chrono::milliseconds(100));

  graph->GetNodeBinaryFeature({1, 2}, {0}, [](const euler::client::BinaryFatureVec& result) {
    for (const auto& features : result) {
      for (const std::string& feature : features) {
        std::cout << feature;
      }
      std::cout << std::endl;
    }
  });
  std::this_thread::sleep_for(std::chrono::milliseconds(100));
}
