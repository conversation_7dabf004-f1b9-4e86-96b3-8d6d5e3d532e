cc_binary(
  name = "graph_test",
  srcs = [
    "graph_test.cc"
  ],
  deps = [
    "//teams/ad/ad_algorithm/euler/euler/client/BUILD:client",
    "//base/common/BUILD:base",
  ],
  cppflags = [
    "-I teams/ad/ad_algorithm/euler",
    "-I third_party/zookeeper/include/zookeeper",
    "-I third_party/grpc-v1100-dev/include",
  ]
)

cc_binary(
  name = "my_graph",
  srcs = [
    "my_graph.cc"
  ],
  deps = [
    "//teams/ad/ad_algorithm/euler/euler/client/BUILD:client",
    "//base/common/BUILD:base",
  ],
  cppflags = [
    "-I teams/ad/ad_algorithm/euler",
    "-I third_party/zookeeper/include/zookeeper",
    "-I third_party/grpc-v1100-dev/include",
  ]
)

