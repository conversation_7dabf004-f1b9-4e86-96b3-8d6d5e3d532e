#pragma once

#include <string>
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/common_info_attr_feature.h"
#include "teams/ad/ad_algorithm/feature_interface/common_info_attr_feature_factory_tmpl.h"

namespace ks {
namespace ad_algorithm {

bool PbNameParseFunc(const std::string& name, ::kuaishou::ad::CommonInfoAttr_Name* name_value);

using CommonAttrFeatureCreator = CommonAttrFeatureCreatorTmpl<
  CommonInfoAttrFeature, ::kuaishou::ad::CommonInfoAttr_Name>;

using CommonInfoAttrFeatureFactory = CommonInfoAttrFeatureFactoryTmpl<
  CommonInfoAttrFeature, ::kuaishou::ad::CommonInfoAttr_Name>;

template<>
CommonInfoAttrFeatureFactory::CommonInfoAttrFeatureFactoryTmpl();

#define REGISTER_COMMON_INFO_ATTR_EXTRACTOR(cls, attr_type, op)      \
  static CommonInfoAttrFeatureFactory::Register<cls> register_##cls(attr_type, op);

}  // namespace ad_algorithm
}  // namespace ks
