#pragma once
#include <map>
#include <vector>
#include "teams/ad/ad_algorithm/feature_interface/industry_v5_wrapper.h"
#include "teams/ad/ad_algorithm/feature/fast/ad_log.h"
#include "absl/strings/str_join.h"
#include "teams/ad/ad_algorithm/label_centre/label_centre_wrapper.h"
namespace ks {
namespace ad_algorithm {

class PbAccountIdVisitorManager {
 public:
  static PbAccountIdVisitorManager& Instance() {
    static PbAccountIdVisitorManager instance;
    return instance;
  }

  bool RegisterVisitor(KeyPosType pos_type, MappingType type);
  bool RegisterLabelCentreVisitor(KeyPosType pos_type, MappingType type);
  void RegisterAllAccountVisitors();
  void RegisterAllProductNameVisitors();
  void RegisterAllLabelCentreVisitors();

  template <typename Seq, typename T>
  Seq FindCommonInfoIntList(const T& attrs, size_t pos, const std::vector<int64_t>& ids) {
    Seq accounts;

    for (const auto& attr : attrs) {
      for (size_t i = 0; i < ids.size(); i++) {
        if (attr.name_value() == ids[i]) {
          for (const auto x : attr.int_list_value()) {
            accounts.Append(x);
          }
        }
      }
    }

    return accounts;
  }

  template <typename Seq, typename T, typename U>
  Seq FindCommonInfoIntList(const T& attrs, size_t pos, U id) {
    std::vector<int64_t> ids = { static_cast<int64_t>(id) };
    return FindCommonInfoIntList<Seq>(attrs, pos, ids);
  }

  template <typename T>
  void RegisterUserCommonInfoAccountVisitor(KeyPosType pos_type, T enum_name);

  template <typename T>
  void RegisterUserCommonInfoProductNameVisitor(KeyPosType pos_type, T enum_name);

  template <typename T>
  void RegisterUserCommonInfoKeysVisitor(KeyPosType pos_type, T enum_name);

  template <typename T>
  void RegisterItemRealTimeCommonAttr(KeyPosType pos_type, T enum_name);

  void DebugVisitor(const AdLog& adlog, size_t pos, const std::vector<KeyPosType>& p_types);

 private:
  PbAccountIdVisitorManager();
  std::map<KeyPosType, AccountIdVisitor> account_visitors_;
  std::map<KeyPosType, ProductNameVisitor> product_name_visitors_;
  std::map<KeyPosType, KeysVisitor> keys_visitors_;
};

}  // namespace ad_algorithm
}  // namespace ks
