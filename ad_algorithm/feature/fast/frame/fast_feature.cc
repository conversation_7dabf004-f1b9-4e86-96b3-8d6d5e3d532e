#include <algorithm>
#include <utility>
#include <sstream>
#include <string>
#include "base/common/logging.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.pb.h"
// RecoPhotoInfo
#include "ks/reco_proto/proto/reco.pb.h"

namespace ks {
namespace ad_algorithm {

void FastFeature::ExtractRecoPhotoFea(const AdLogInterface & adlog,
                      // const vector<RecoPhotoInfo> & reco_photo_info,
                      const RecoPhotoInfoVector & reco_photo_info,
                      size_t pos,
                      std::vector<ExtractResult>* result) {
  return ExtractRecoPhoto(reinterpret_cast<const AdLog&>(adlog),
                          reco_photo_info,
                          pos,
                          result);
}

void FastFeature::ExtractRecoPhoto(const AdLog & adlog, const std::vector<ks::reco::RecoPhotoInfo> & reco_photo_info,
                              size_t pos, std::vector<ExtractResult>* result) {
  return;
}

namespace internal {

}  //  namespace internal

void FastFeature::GetUserCallbackActionFeature(const AdLog& adlog, const int64_t target_action_type,
                                               const ::kuaishou::ad::CommonInfoAttr_Name cia_name,
                                               std::vector<ExtractResult>* result) {
  int64_t max_loop = 100;
  if (adlog.has_user_info() && adlog.user_info().common_info_attr_size() > 0) {
    for (const ::kuaishou::ad::CommonInfoAttr& userAttr : adlog.user_info().common_info_attr()) {
      if (userAttr.name_value() == cia_name) {
        // key 的存储形式是 industry_id(or hash(product_name)) * 10000 + action_type_id，
        // val 是用户过去 30 天在该 industry_id(or product_name) 下有过该 action 的次数
        int64_t cur_loop = 0;
        int64_t divisor = 10000;
        for (const auto& kv : userAttr.map_int64_int64_value()) {
          if (cur_loop > max_loop) {
            break;
          }
          int64_t cur_action_type = kv.first % divisor;
          if (cur_action_type != target_action_type) {
            continue;
          }
          int64_t id = kv.first / divisor;
          // id 是二级行业 id 或 hash(product_name)，不会冲突，因此没有使用 prefix
          AddFeature(id, 1.0, result);
          cur_loop++;
        }
        break;
      }
    }
  }
}

const ::google::protobuf::Map< ::std::string, float >*
FastFeature::GetQueryToken(const AdLog & adlog) {
  if (adlog.has_context() && adlog.context().info_common_attr_size() > 0) {
    for (auto & attr : adlog.context().info_common_attr()) {
      if (attr.name_value() == ::kuaishou::ad::ContextInfoCommonAttr_Name_SEARCH_QUERY_TOKEN) {
        return &attr.map_string_float_value();
      }
    }
  }
  return nullptr;
}

const ::google::protobuf::Map< ::std::string, float >*
FastFeature::GetPhotoText(const AdLog & adlog, size_t pos,
             ::google::protobuf::int64 common_info_attr_enum) {
  if (adlog.item(pos).has_ad_dsp_info() && adlog.item(pos).ad_dsp_info().common_info_attr_size() >0) {
    for (auto &attr : adlog.item(pos).ad_dsp_info().common_info_attr()) {
      if (attr.name_value() == common_info_attr_enum) {
        return &(attr.map_string_float_value());
      }
    }
  }
  return nullptr;
}

const ::google::protobuf::RepeatedPtrField<CommonInfoAttr>*
FastFeature::GetCommonInfoAttr(const ItemAdaptorBase& item) {
  const PhotoInfo* photo_info = GetPhotoInfo(item);
  if (photo_info != nullptr) {
    return &photo_info->common_info_attr();
  }
  const LiveInfo* live_info = GetLiveInfo(item);
  if (live_info != nullptr) {
    return &live_info->common_info_attr();
  }
  return nullptr;
}

const ::google::protobuf::RepeatedPtrField<CommonInfoAttr>*
FastFeature::GetCommonInfoAttrNew(const ItemAdaptorBase& item) {
  if (item.ad_dsp_info().has_campaign() && item.ad_dsp_info().has_creative()) {
    auto& campaign = item.ad_dsp_info().campaign().base();
    auto& creative = item.ad_dsp_info().creative().base();
    //  21/22/23 && live_creative_type=2 是粉条作品引流物料
    if ((campaign.type() == 21 || campaign.type() == 22 || campaign.type() == 23) &&
        creative.live_creative_type() == 2) {
      return &item.ad_dsp_info().common_info_attr();
    }
    //  17/18/20 是粉条作品物料
    if ((campaign.type() == 17 || campaign.type() == 18 || campaign.type() == 20)) {
      return &item.ad_dsp_info().common_info_attr();
    }
  }
  const PhotoInfo* photo_info = GetPhotoInfoNew(item);
  if (photo_info != nullptr) {
    return &photo_info->common_info_attr();
  }
  const LiveInfo* live_info = GetLiveInfo(item);
  if (live_info != nullptr) {
    return &live_info->common_info_attr();
  }
  return nullptr;
}

const PhotoInfo* FastFeature::GetPhotoInfo(const ItemAdaptorBase& item) {
  if (item.type() == ItemType::AD_DSP && item.has_ad_dsp_info()) {
    return &item.ad_dsp_info().photo_info();
  } else if (item.type() == ItemType::FANS_TOP && item.has_fans_top_info()) {
    return &item.fans_top_info().photo_info();
  } else if (item.type() == ItemType::NATURE_PHOTO &&
             item.has_nature_photo_info()) {
    return &item.nature_photo_info().photo_info();
  } else if (item.type() == ItemType::AD_BRAND && item.has_ad_dsp_info()) {
    return &item.ad_dsp_info().photo_info();
  } else if (item.type() == ItemType::NATIVE_AD && item.has_ad_dsp_info()) {
    return &item.ad_dsp_info().photo_info();
  }
  return nullptr;
}

const PhotoInfo* FastFeature::GetPhotoInfoNew(const ItemAdaptorBase& item) {
  if (item.type() == ItemType::AD_DSP && item.has_ad_dsp_info()) {
    if (item.ad_dsp_info().has_campaign() && item.ad_dsp_info().has_creative()) {
      auto& campaign = item.ad_dsp_info().campaign().base();
      auto& creative = item.ad_dsp_info().creative().base();
      //  21/22/23 && live_creative_type=1 是粉条直播直投物料
      if ((campaign.type() == 21 || campaign.type() == 22 || campaign.type() == 23) &&
          creative.live_creative_type() == 1) {
        return nullptr;
      }
    }
    return &item.ad_dsp_info().photo_info();
  } else if (item.type() == ItemType::FANS_TOP && item.has_fans_top_info()) {
    return &item.fans_top_info().photo_info();
  } else if (item.type() == ItemType::NATURE_PHOTO &&
              item.has_nature_photo_info()) {
    return &item.nature_photo_info().photo_info();
  } else if (item.type() == ItemType::AD_BRAND && item.has_ad_dsp_info()) {
    return &item.ad_dsp_info().photo_info();
  } else if (item.type() == ItemType::NATIVE_AD && item.has_ad_dsp_info()) {
    return &item.ad_dsp_info().photo_info();
  }
  return nullptr;
}

const AdDspMmuInfo* FastFeature::GetAdDspMmuInfo(const ItemAdaptorBase& item) {
  if (item.type() == ItemType::AD_DSP && item.has_ad_dsp_info()) {
    return &item.ad_dsp_info().ad_dsp_mmu_info();
  } else if (item.type() == ItemType::NATIVE_AD && item.has_ad_dsp_info()) {
    return &item.ad_dsp_info().ad_dsp_mmu_info();
  }
  return nullptr;
}

const LiveInfo* FastFeature::GetLiveInfo(const ItemAdaptorBase& item) {
  if (item.type() == ItemType::FANS_TOP_LIVE &&
      item.has_fans_top_live_info()) {
    return &item.fans_top_live_info().live_info();
  }
  if (item.type() == ItemType::FANS_TOP_LIVE_MERCHANT &&
      item.has_fans_top_live_info()) {
    return &item.fans_top_live_info().live_info();
  }
  if (item.type() == ItemType::FANS_TOP && item.has_fans_top_info() &&
      item.fans_top_info().has_live_info()) {
    return &item.fans_top_info().live_info();
  }
  if (item.type() == ItemType::AD_DSP && item.has_ad_dsp_info()) {
    return &item.ad_dsp_info().live_info();
  }
  if (item.type() == ItemType::NATIVE_AD && item.has_ad_dsp_info()) {
    return &item.ad_dsp_info().live_info();
  }
  if (item.type() == ItemType::AD_BRAND && item.has_ad_dsp_info()
      && item.ad_dsp_info().has_live_info()) {
    return &item.ad_dsp_info().live_info();
  }
  return nullptr;
}

const AuthorInfo* FastFeature::GetAuthorInfo(const ItemAdaptorBase& item) {
  if (item.type() == ItemType::FANS_TOP_LIVE && item.has_fans_top_live_info() &&
      item.fans_top_live_info().has_live_info()) {
    return &item.fans_top_live_info().live_info().author_info();
  }
  if (item.type() == ItemType::FANS_TOP && item.has_fans_top_info() &&
      item.fans_top_info().has_photo_info()) {
    return &item.fans_top_info().photo_info().author_info();
  }
  if ((item.type() == ItemType::AD_DSP || item.type() == ItemType::NATIVE_AD) &&
      item.has_ad_dsp_info()) {
    if (item.ad_dsp_info().has_campaign() && item.ad_dsp_info().has_creative()) {
      auto& campaign = item.ad_dsp_info().campaign().base();
      auto& creative = item.ad_dsp_info().creative().base();
      //  21/22/23 是粉条物料
      if ((campaign.type() == 14 || campaign.type() == 21 || campaign.type() == 22 ||
           campaign.type() == 23) && creative.live_creative_type() == 1) {
        // 只有直播直投的取 live_info 下的，作品引流和作品都取 photo_info 下的
        if (item.ad_dsp_info().has_live_info()) {
          return &item.ad_dsp_info().live_info().author_info();
        }
      } else {
        if (item.ad_dsp_info().has_photo_info()) {
          return &item.ad_dsp_info().photo_info().author_info();
        }
      }
    }
  }
  if (item.type() == ItemType::AD_BRAND && item.has_ad_dsp_info() &&
      item.ad_dsp_info().has_live_info()) {
    return &item.ad_dsp_info().live_info().author_info();
  }
  return nullptr;
}

const FansTopOrderInfo* FastFeature::GetFansTopOrderInfo(const ItemAdaptorBase& item) {
  if (item.type() == ItemType::NATIVE_AD && item.has_ad_dsp_info() &&
      item.ad_dsp_info().has_order_info()) {
    return &item.ad_dsp_info().order_info();
  }
  if (item.type() == ItemType::FANS_TOP && item.has_fans_top_info() &&
      item.fans_top_info().has_order_info()) {
    return &item.fans_top_info().order_info();
  }
  return nullptr;
}

const FansTopOrderInfo* FastFeature::GetFansTopOrderInfoFix(const ItemAdaptorBase& item) {
  if ((item.type() == ItemType::NATIVE_AD || item.type() == ItemType::AD_DSP) && item.has_ad_dsp_info() &&
      item.ad_dsp_info().has_order_info()) {
    return &item.ad_dsp_info().order_info();
  }
  if (item.type() == ItemType::FANS_TOP && item.has_fans_top_info() &&
      item.fans_top_info().has_order_info()) {
    return &item.fans_top_info().order_info();
  }
  return nullptr;
}

const FansTopOrderInfo* FastFeature::GetNewFlowFansTopOrderInfo(const ItemAdaptorBase& item) {
  if (item.type() == ItemType::NATIVE_AD && item.has_ad_dsp_info() &&
      item.ad_dsp_info().has_order_info()) {
    return &item.ad_dsp_info().order_info();
  }
  if (item.type() == ItemType::AD_DSP && item.has_ad_dsp_info() &&
      item.ad_dsp_info().has_order_info()) {
    return &item.ad_dsp_info().order_info();
  }
  if (item.type() == ItemType::FANS_TOP && item.has_fans_top_info() &&
      item.fans_top_info().has_order_info()) {
    return &item.fans_top_info().order_info();
  }
  return nullptr;
}

void FastFeature::GetNormQuery(const AdLog & adlog, std::string* query) {
  std::string tab_keyword;
  for (auto & attr : adlog.context().info_common_attr()) {
    if (attr.name_value() == ::kuaishou::ad::ContextInfoCommonAttr_Name_QUERY) {
      (*query) = attr.string_value();
    }
    if (attr.name_value() == ::kuaishou::ad::ContextInfoCommonAttr_Name_TAB_KEYWORD) {
      tab_keyword = attr.string_value();
    }
  }
  (*query) += tab_keyword;

  // query 归一化: 大写转小写，全角转半角，繁体转简体
  base::TrimWhitespaces(query);
  if (query->length() > 0) {
    ad_target_search::QueryBase::
      strNormalizeUtf8((*query),
                       ad_target_search::QueryBase::SNO_TO_LOWER |
                       ad_target_search::QueryBase::SNO_TO_HALF |
                       ad_target_search::QueryBase::SNO_TO_SIMPLIFIED);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
