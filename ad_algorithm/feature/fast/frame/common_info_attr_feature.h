#pragma once

#include <vector>
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {
// CommonInfoAttr feature 脚本化相关逻辑
// 假定如下:
// 1. 只处理 User 和 Item 特征，不支持 Combine 特征
// 2. 每个 Feature 只处理一个 CommonInfoAttr 的 name，且只返回一个 prefix 的特征
//    (后续计划废弃 prefix，返回裸值)
class CommonInfoAttrFeature : public FastFeatureNoPrefix {
 public:
  CommonInfoAttrFeature(FeatureType type,
                        ::kuaishou::ad::CommonInfoAttr::Name attr_name,
                        FeaturePrefix prefix);
  virtual ~CommonInfoAttrFeature() {}

  // Feature 抽取逻辑校验接口, 校验 result 是否符合预期
  bool CheckInternal(const AdLog &adlog, size_t pos, const std::vector<ExtractResult> &result) override;
  virtual bool Init(const base::Json& config);
  static void Reset(uint32_t item_count);

 protected:
  void Extract(const AdLog & adlog, size_t pos, std::vector<ExtractResult>* result) override;
  virtual bool InitInternal(const base::Json& config) = 0;
  virtual void ExtractCommonAttr(const ::kuaishou::ad::CommonInfoAttr* attr,
                                 std::vector<ExtractResult>* result) = 0;
  virtual bool CheckCommonAttr(const ::kuaishou::ad::CommonInfoAttr* attr,
                               const std::vector<ExtractResult>& result) = 0;

  const ::google::protobuf::RepeatedPtrField<::kuaishou::ad::CommonInfoAttr>*
  GetCommonInfoAttrList(const AdLog &adlog, size_t pos);
  const ::kuaishou::ad::CommonInfoAttr* GetCommonInfoAttr(const AdLog &adlog, size_t pos);

 protected:
  ::kuaishou::ad::CommonInfoAttr::Name attr_name_;
  FeaturePrefix feature_prefix_;
  bool is_photo_feature_ = false;
  bool is_item_feature_ = false;
  bool is_live_feature_ = false;
  bool is_ecom_realtime_feature_ = false;

 private:
  // 下面两个游标分别用于标记 user/item feature 抽取 common_info_attr 所达到的下标位置
  // 前提假设为:
  //   1. user/item 中 common_info_attr 按照 name_value 升序排列
  //   2. user/item feature 在 feature file 定义中按照 name_value 升序排列
  static thread_local uint32_t user_common_info_cursor_;  // user common_info_attr 目前特征抽取到的游标
  // 每个 item 的 common_info_attr 目前特征抽取到的游标
  static thread_local std::vector<uint32_t> item_common_info_cursor_;

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonInfoAttrFeature);
};

}  // namespace ad_algorithm
}  // namespace ks
