#include <vector>
#include "absl/strings/numbers.h"
#include "teams/ad/ad_algorithm/feature_interface/tool.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/common_info_attr_feature_factory.h"
// CommonInfoAttr 相关特征抽象实现类
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_common_int_sign_value.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_common_int_list_sign_value.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_common_number_list_sign_value.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_common_int_map_sign_value.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_common_number_seg_norm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_common_number_value.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_common_dense_float_value.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_common_dense_float_list_value.h"

namespace ks {
namespace ad_algorithm {

bool PbNameParseFunc(const std::string& name,
                     ::kuaishou::ad::CommonInfoAttr_Name* name_value) {
  if (name_value == nullptr) {
    LOG(WARNING) << "name_value is a nullptr";
    return false;
  }

  if (name.size() == 0) {
    LOG(ERROR) << "name is empty!";
    return false;
  }

  if (is_str_all_integer(name)) {
    int32_t v;

    if (!absl::SimpleAtoi(name, &v)) {
      LOG(ERROR) << "parse int failed! str: " << name;
      return false;
    }

    *name_value = static_cast<::kuaishou::ad::CommonInfoAttr_Name>(v);

    return true;
  }

  bool succ = false;
  ::kuaishou::ad::CommonInfoAttr_NameExtendOne extend_one_value;
  ::kuaishou::ad::CommonInfoAttr_NameExtendTwo extend_two_value;
  ::kuaishou::ad::CommonInfoAttr_NameExtendThree extend_three_value;
  ::kuaishou::ad::CommonInfoAttr_NameExtendFour extend_four_value;

  succ |= ::kuaishou::ad::CommonInfoAttr_Name_Parse(name, name_value);
  if (succ) {
    LOG(INFO) << "Name value: " << *name_value;
    return true;
  }

  succ |= ::kuaishou::ad::CommonInfoAttr_NameExtendOne_Parse(name, &extend_one_value);
  if (succ) {
    *name_value = static_cast<::kuaishou::ad::CommonInfoAttr_Name>(static_cast<int32_t>(extend_one_value));
    LOG(INFO) << "ExtendOne value: " << *name_value;
    return true;
  }

  succ |= ::kuaishou::ad::CommonInfoAttr_NameExtendTwo_Parse(name, &extend_two_value);
  if (succ) {
    *name_value = static_cast<::kuaishou::ad::CommonInfoAttr_Name>(static_cast<int32_t>(extend_two_value));
    LOG(INFO) << "ExtendTwo value: " << *name_value;
    return true;
  }

  succ |= ::kuaishou::ad::CommonInfoAttr_NameExtendThree_Parse(name, &extend_three_value);
  if (succ) {
    *name_value = static_cast<::kuaishou::ad::CommonInfoAttr_Name>(static_cast<int32_t>(extend_three_value));
    LOG(INFO) << "ExtendThree value: " << *name_value;
    return true;
  }

  succ |= ::kuaishou::ad::CommonInfoAttr_NameExtendFour_Parse(name, &extend_four_value);
  if (succ) {
    *name_value = static_cast<::kuaishou::ad::CommonInfoAttr_Name>(static_cast<int32_t>(extend_four_value));
    LOG(INFO) << "ExtendFour value: " << *name_value;
    return true;
  }

  LOG(WARNING) << "Parse common info attr name enum failed";
  return false;
}

template<>
CommonInfoAttrFeatureFactory::CommonInfoAttrFeatureFactoryTmpl() :
  name_parse_func_(PbNameParseFunc) {
}

}  // namespace ad_algorithm
}  // namespace ks
