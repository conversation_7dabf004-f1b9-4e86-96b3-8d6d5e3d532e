#pragma once

#include <vector>
#include <string>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

using SampleAttrs = kuiba::PredictItem;
class SampleAttrFastFeature;

class SampleAttrExtractOp {
 public:
  virtual void init(const std::string& config) = 0;
  virtual kuiba::CommonSampleEnum_AttrType type() = 0;
  virtual void extract(SampleAttrFastFeature* extract,
                       const kuiba::SampleAttr*, std::vector<ExtractResult>* result) = 0;
};

class SampleAttrFastFeature: public FastFeatureNoPrefix {
 public:
  SampleAttrFastFeature(const std::string& feature_name,
                        SampleAttrExtractOp* op,
                        FeatureType type,
                        const std::string& config,
                        int left_bits = 52): FastFeatureNoPrefix(type, left_bits) {
    feature_name_ = feature_name;
    op_ptr_ = op;
    op_ptr_->init(config);
  }

  virtual ~SampleAttrFastFeature() {}

  const SampleAttrs* get_sample_attrs(const AdLog& adlog, size_t pos) {
    switch (GetType()) {
      case FeatureType::USER:
      case FeatureType::DENSE_USER:
        if (adlog.has_merchant_user_info()) {
          return adlog.merchant_user_attrs().get();
        }
        break;
      case FeatureType::ITEM:
      case FeatureType::DENSE_ITEM:
        if (adlog.has_merchant_item_attrs(pos)) {
          return adlog.merchant_item_attrs(pos).get();
        }
        break;
      default:
        LOG(ERROR) << "SampleAttrFastFeature not support FeatureType: " << GetType();
        break;
    }
    return nullptr;
  }

  const kuiba::SampleAttr* get_sample_attr(const AdLog& adlog, size_t pos) {
    const SampleAttrs* sample_attrs = get_sample_attrs(adlog, pos);
    if (sample_attrs) {
      for (const ::kuiba::SampleAttr& attr : sample_attrs->attr()) {
        if (feature_name_.compare(attr.name()) == 0) {
          return &attr;
        }
      }
    }
    return nullptr;
  }

 protected:
  virtual void Extract(const AdLog& adlog, size_t pos,
                       std::vector<ExtractResult>* result) {
    if (pos >= adlog.item_size()) {
      return;
    }
    const kuiba::SampleAttr* attr = get_sample_attr(adlog, pos);
    if (attr == nullptr) {
      return;
    }
    CHECK_EQ(attr->type(), op_ptr_->type())
        << ". SampleAttrExtractOp type error: feature name is " << feature_name_;
    op_ptr_->extract(this, attr, result);
  }

 protected:
    std::string feature_name_;
    SampleAttrExtractOp* op_ptr_;
};

namespace internal {
template <class Op, FeatureType F>
class SampleAttrFeaFactory : public Factory {
 public:
    explicit SampleAttrFeaFactory(const std::string feature_name, const std::string& config) {
      feature_name_ = feature_name;
      config_ = config;
    }

    virtual FastFeature* Create() {
      SampleAttrExtractOp* op = new Op();
      return new SampleAttrFastFeature(feature_name_, op, F, config_);
    }
 private:
    std::string config_;
    std::string feature_name_;
};

}  // namespace internal

}  // namespace ad_algorithm
}  // namespace ks
