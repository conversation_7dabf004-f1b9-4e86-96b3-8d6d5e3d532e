#pragma once

#include <math.h>
#include <iostream>
#include <map>
#include <string>
#include <unordered_map>
#include <vector>
#include <algorithm>

// NOTE(Jzh) : Do NOT include PB header in this file!!! Please add it in cc file.
#include "base/common/basic_types.h"
#include "base/common/logging.h"
#include "base/hash_function/city.h"
#include "base/strings/string_split.h"
#include "base/strings/string_util.h"
#include "teams/ad/ad_algorithm/feature/fast/ad_log.h"
#include "teams/ad/ad_algorithm/feature/fast/ad_log_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/ad_log_wrapper.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/pb_account_id_visitor_manager.h"
#include "teams/ad/ad_algorithm/feature_interface/ad_log_interface.h"
#include "teams/ad/ad_algorithm/feature_interface/factory_tmpl.h"
#include "teams/ad/ad_algorithm/feature_interface/fast_feature_interface.h"
#include "teams/ad/ad_target_search/util/string/query_string_normalize.h"
#include "teams/ad/ad_algorithm/feature_interface/industry_v5_wrapper.h"
#include "teams/ad/ad_algorithm/feature_interface/industry_v5_getter.h"
#include "teams/ad/ad_algorithm/log_preprocess/ad_model_kconf_util.h"
#include "teams/ad/ad_algorithm/pb_adaptor/item_adaptor_base.h"

#ifdef ENABLE_KUIBA
#include "teams/reco-model/base/feature/sign/sign_feature.h"
#endif
#ifdef REGISTER_EXTRACTOR
#undef REGISTER_EXTRACTOR
#endif

using UserInfo = kuaishou::ad::algorithm::UserInfo;
using PhotoInfo = kuaishou::ad::algorithm::PhotoInfo;
using AuthorInfo = kuaishou::ad::algorithm::AuthorInfo;
using FansTopOrderInfo = kuaishou::ad::algorithm::FansTopOrderInfo;
using LiveInfo = kuaishou::ad::algorithm::LiveInfo;
using AdDspMmuInfo = kuaishou::ad::algorithm::AdDspMmuInfo;
using Item = kuaishou::ad::algorithm::Item;
using ItemType = kuaishou::ad::algorithm::ItemType;
using SimpleAdDspInfos = ::kuaishou::ad::algorithm::SimpleAdDspInfos;
using SimpleAdDspInfo = ::kuaishou::ad::algorithm::SimpleAdDspInfo;
using CommonInfoAttr = kuaishou::ad::CommonInfoAttr;
using ContextInfoCommonAttr = kuaishou::ad::ContextInfoCommonAttr;

namespace kuaishou {
namespace ad {
namespace algorithm {


}  // namespace algorithm
}  // namespace ad
}  // namespace kuaishou

namespace ks {
namespace reco {

class RecoPhotoInfo;

}  // namespace reco
}  // namespace ks

namespace ks {

namespace ad_algorithm {

// Alias for forward declaration.
using RecoPhotoInfoVector = std::vector<ks::reco::RecoPhotoInfo>;

class FastFeature : public FastFeatureInterface {
 public:
  explicit FastFeature(FeatureType type, size_t left_bits = 52) :
    FastFeatureInterface(type, left_bits) {}

  FastFeature(FeatureType type, size_t left_bits, size_t right_bits,
              CombineVersion combine_version = DEFAULT) :
    FastFeatureInterface(type, left_bits, right_bits, combine_version) {}

  FastFeature(FeatureType type, size_t left_bits, size_t mid_bits,
              size_t right_bits) :
    FastFeatureInterface(type, left_bits, mid_bits, right_bits) {}

  virtual ~FastFeature() {}

  void ExtractFea(const AdLogInterface& adlog, size_t pos,
                  std::vector<ExtractResult>* result) {
    return Extract(reinterpret_cast<const AdLog&>(adlog), pos, result);
  }

  void ExtractRecoPhotoFea(const AdLogInterface & adlog,
                        // const vector<RecoPhotoInfo> & reco_photo_info,
                        const RecoPhotoInfoVector & reco_photo_info,
                        size_t pos, std::vector<ExtractResult>* result);

  // Feature 抽取逻辑校验接口，已有老 feature 默认不支持校验，直接返回
  bool CheckFea(const AdLogInterface& adlog, size_t pos,
                const std::vector<ExtractResult> &result) {
    return Check(reinterpret_cast<const AdLog&>(adlog),
                 pos,
                 result);
  }

  static const PhotoInfo* GetPhotoInfoNew(const ItemAdaptorBase& item);
  static const PhotoInfo* GetPhotoInfo(const ItemAdaptorBase& item);
  static const LiveInfo* GetLiveInfo(const ItemAdaptorBase& item);
  // 兼容获取 author info
  static const AuthorInfo* GetAuthorInfo(const ItemAdaptorBase& item);

 protected:
  virtual void Extract(const AdLog& adlog, size_t pos,
                       std::vector<ExtractResult>* result) = 0;
  virtual void ExtractRecoPhoto(const AdLog & adlog, const RecoPhotoInfoVector & reco_photo_info,
                                size_t pos, std::vector<ExtractResult>* result);
  virtual bool Check(const AdLog& adlog, size_t pos,
                     const std::vector<ExtractResult> &result) {
    return true;
  }

 protected:
  const ::google::protobuf::RepeatedPtrField<CommonInfoAttr>* GetCommonInfoAttr(const ItemAdaptorBase& item);
  // 使用 GetCommonInfoAttrNew，否则直播类物料返回的 common_info_attr 将为空
  const ::google::protobuf::RepeatedPtrField<CommonInfoAttr>* GetCommonInfoAttrNew(
      const ItemAdaptorBase& item);

  // 用于兼容粉条新旧物料
  const AdDspMmuInfo* GetAdDspMmuInfo(const ItemAdaptorBase& item);

  const FansTopOrderInfo* GetFansTopOrderInfo(const ItemAdaptorBase& item);

  const FansTopOrderInfo* GetFansTopOrderInfoFix(const ItemAdaptorBase& item);

  const FansTopOrderInfo* GetNewFlowFansTopOrderInfo(const ItemAdaptorBase& item);

  void GetUserCallbackActionFeature(const AdLog& adlog, int64_t target_action_type,
                                    const ::kuaishou::ad::CommonInfoAttr_Name cia_name,
                                    std::vector<ExtractResult>* result);
  void GetNormQuery(const AdLog & adlog, std::string* query);

  const ::google::protobuf::Map< ::std::string, float >*
  GetQueryToken(const AdLog & adlog);

  const ::google::protobuf::Map< ::std::string, float >*
  GetPhotoText(const AdLog & adlog, size_t pos,
               ::google::protobuf::int64 common_info_attr_enum);
};

namespace internal {
using Factory = FactoryTmpl<FastFeature>;
using FactoryBundle = FactoryBundleTmpl<FastFeature>;

template <class T>
class FeaFactory : public Factory {
 public:
  virtual FastFeature* Create() { return new T(); }
};

template <class T>
class SeqFeaFactory : public Factory {
 public:
    explicit SeqFeaFactory(size_t seq_index) {
      seq_index_ = seq_index;
    }
    virtual FastFeature* Create() { return new T(seq_index_);}
 private:
    size_t seq_index_;
};

#ifdef ENABLE_KUIBA
template <class T, FeatureType F>
class RecoFastFeature : public FastFeature {
 public:
  RecoFastFeature() : FastFeature(F) {}

 protected:
  void Extract(const AdLog & adlog, size_t pos, std::vector<ExtractResult>* result) {
    ks::reco::SampleInfo sample_info;
    sample_info.user = adlog.reco_user_info();
    // sample_info.tab_id = reco_log.tab();
    std::vector<uint64> reco_result;
    reco_feature_.Extract(sample_info, &reco_result);
    for (int idx = 0; idx < reco_result.size(); ++idx) {
      AddFeatureSparse(reco_result[idx], result);
    }
  }

  void ExtractRecoPhoto(const AdLog & adlog, const RecoPhotoInfoVector & reco_photo_info,
               size_t pos, std::vector<ExtractResult>* result) {
    ks::reco::SampleInfo sample_info;
    sample_info.user = adlog.reco_user_info();
    if (pos > 0 && pos < reco_photo_info.size()) {
      sample_info.photo = &(reco_photo_info[pos].photo());
      sample_info.context = &(reco_photo_info[pos].context_info());
    }
    // sample_info.tab_id = reco_log.tab();
    std::vector<uint64> reco_result;
    reco_feature_.Extract(sample_info, &reco_result);
    for (int idx = 0; idx < reco_result.size(); ++idx) {
      AddFeatureSparse(reco_result[idx], result);
    }
  }

 private:
  T reco_feature_;
  DISALLOW_COPY_AND_ASSIGN(RecoFastFeature);
};

template <class T, FeatureType F>
class RecoFeaFactory : public Factory {
 public:
  virtual FastFeature* Create() {
    return new RecoFastFeature<T, F>();
  }
};

#endif
}  // namespace internal

#define REGISTER_EXTRACTOR(cls)                                                \
  static ::ks::ad_algorithm::internal::FactoryBundle::Register register_##cls( \
      #cls, new ::ks::ad_algorithm::internal::FeaFactory<cls>())

// 序列特征抽取类注册函数，max_seq_len 指定最大的序列长度
#define REGISTER_SEQUENCE_EXTRACTOR(cls, max_seq_len)                              \
    static ::ks::ad_algorithm::internal::FactoryBundle::Register register_##cls(   \
      #cls, max_seq_len, [](size_t idx) {return new ::ks::ad_algorithm::internal::SeqFeaFactory<cls>(idx);})

}  // namespace ad_algorithm
}  // namespace ks
