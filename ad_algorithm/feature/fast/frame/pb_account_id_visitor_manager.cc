#include "absl/strings/str_join.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/pb_account_id_visitor_manager.h"
#include "teams/ad/ad_algorithm/feature/fast/ad_log.h"
#include "teams/ad/ad_algorithm/fed/compute/tag_center.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"

namespace ks {
namespace ad_algorithm {

using internal::ToInteger;
using ks::ad_algorithm::label_centre::LabelCentreMapping;

template <typename T>
void PbAccountIdVisitorManager::RegisterUserCommonInfoAccountVisitor(KeyPosType pos_type, T enum_name) {
  account_visitors_[pos_type] = [this, enum_name](const AdLogInterface& log, size_t pos) {
    const auto& adlog = static_cast<const AdLog&>(log);
    return FindCommonInfoIntList<AccountIdSeq>(adlog.user_info().common_info_attr(), pos, enum_name);
  };
}

template <typename T>
void PbAccountIdVisitorManager::RegisterUserCommonInfoProductNameVisitor(KeyPosType pos_type, T enum_name) {
  product_name_visitors_[pos_type] = [this, enum_name](const AdLogInterface& log, size_t pos) {
    const auto& adlog = static_cast<const AdLog&>(log);
    return FindCommonInfoIntList<ProductNameSeq>(adlog.user_info().common_info_attr(), pos, enum_name);
  };
}

template <typename T>
void PbAccountIdVisitorManager::RegisterUserCommonInfoKeysVisitor(KeyPosType pos_type, T enum_name) {
  keys_visitors_[pos_type] = [this, enum_name](const AdLogInterface& log, size_t pos) {
    const auto& adlog = static_cast<const AdLog&>(log);
    return FindCommonInfoIntList<KeySeq>(adlog.user_info().common_info_attr(), pos, enum_name);
  };
}

template <typename T>
void PbAccountIdVisitorManager::RegisterItemRealTimeCommonAttr(KeyPosType pos_type, T enum_name) {
  keys_visitors_[pos_type] = [this, enum_name](const AdLogInterface& log, size_t pos) {
    const auto& adlog = static_cast<const AdLog&>(log);
    return FindCommonInfoIntList<KeySeq>(adlog.item(pos).item_common_attr(), pos, enum_name);
  };
}

bool PbAccountIdVisitorManager::RegisterVisitor(KeyPosType pos_type, MappingType type) {
  bool register_success = false;
  switch (type) {
    case MappingType::kIndustryMapping: {
      auto iter = account_visitors_.find(pos_type);
      if (iter != account_visitors_.end()) {
        IndustryMapping::Instance().RegisterAccountIdVisitor(LogType::LT_PB, pos_type, iter->second);
        register_success = true;
      }
      break;
    }
    case MappingType::kIdIndustryMapping: {
      auto iter = account_visitors_.find(pos_type);
      if (iter != account_visitors_.end()) {
        IdIndustryMapping::Instance().RegisterAccountIdVisitor(LogType::LT_PB, pos_type, iter->second);
        register_success = true;
      }
      break;
    }
    case MappingType::kGameProductNameMapping: {
      auto iter_product_name = product_name_visitors_.find(pos_type);
      if (iter_product_name != product_name_visitors_.end()) {
        GameProductNameMapping::Instance().RegisterAccountIdVisitor(LogType::LT_PB,
                                           pos_type, iter_product_name->second);
        register_success = true;
      }
      break;
    }
    default:
      break;
  }
  if (!register_success) {
    LOG(FATAL) << "register fail for pos type: " << ToInteger(pos_type)
               << ", mapping type: " << type;
  }
  return true;
}

PbAccountIdVisitorManager::PbAccountIdVisitorManager() {
  RegisterAllAccountVisitors();
  RegisterAllProductNameVisitors();
  RegisterAllLabelCentreVisitors();
}

void PbAccountIdVisitorManager::RegisterAllAccountVisitors() {
  static AccountIdSeq default_v;

  account_visitors_[KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID] =
      [](const AdLogInterface& log, size_t pos) {
        if (pos >= log.item_size()) {
          return default_v;
        }
        const auto& adlog = reinterpret_cast<const AdLog&>(log);
        const auto& item = adlog.item(pos);
        if (item.has_ad_dsp_info()) {
          const auto& ad_dsp_info = item.ad_dsp_info();
          if (ad_dsp_info.has_unit()) {
            const auto& unit = ad_dsp_info.unit();
            if (unit.has_base()) {
              return AccountIdSeq(unit.base().account_id());
            }
          }
        }
        return default_v;
      };

  RegisterUserCommonInfoAccountVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_PLAYED_5S_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_PLAYED_5S_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoAccountVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_PLAYED_END_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_PLAYED_END_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoAccountVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CLICK_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_CLICK_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoAccountVisitor(
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoAccountVisitor(
    KeyPosType::USER_ADLOGFULL_EVENT_CONVERSION_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_EVENT_CONVERSION_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoAccountVisitor(
    KeyPosType::USER_ADLOGFULL_EVENT_PAY_UNION_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_EVENT_PAY_UNION_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoAccountVisitor(
    KeyPosType::USER_ADLOGFULL_EVENT_NEXTDAY_STAY_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_EVENT_NEXTDAY_STAY_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoAccountVisitor(
    KeyPosType::USER_ADLOGFULL_AD_ITEM_DOWNLOAD_COMPLETED_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_ITEM_DOWNLOAD_COMPLETED_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoAccountVisitor(
      KeyPosType::USER_ADLOGFULL_AD_ITEM_DOWNLOAD_INSTALLED_ACCOUNT_ID_LIST,
      CommonInfoAttr::ADLOGFULL_AD_ITEM_DOWNLOAD_INSTALLED_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoAccountVisitor(
      KeyPosType::USER_ADLOGFULL_AD_APPROXIMATE_PURCHASE_ACCOUNT_ID_LIST,
      CommonInfoAttr::ADLOGFULL_AD_APPROXIMATE_PURCHASE_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoAccountVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CANCEL_LIKE_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_CANCEL_LIKE_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoAccountVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_NEGATIVE_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoAccountVisitor(
    KeyPosType::USER_ADLOGFULL_AD_LIVE_CANCEL_FOLLOW_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_LIVE_CANCEL_FOLLOW_ACCOUNT_ID_LIST);
}

void PbAccountIdVisitorManager::RegisterAllProductNameVisitors() {
  static ProductNameSeq default_v;

  product_name_visitors_[KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_ADVERTISER_BASE_PRODUCT_NAME] =
    [](const AdLogInterface& log, size_t pos) {
      if (pos >= log.item_size()) {
        return default_v;
      }
      const auto& adlog = reinterpret_cast<const AdLog&>(log);
      const auto& item = adlog.item(pos);
      if (item.has_ad_dsp_info()) {
        const auto& ad_dsp_info = item.ad_dsp_info();
        if (ad_dsp_info.has_advertiser_base()) {
          auto& product_name = item.ad_dsp_info().advertiser_base().product_name();
          return ProductNameSeq(java_hash_code(product_name));
        }
      }
      return default_v;
    };

  RegisterUserCommonInfoProductNameVisitor(
    KeyPosType::USER_AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST,
    CommonInfoAttr::AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoProductNameVisitor(
    KeyPosType::USER_AD_PHOTO_PLAYED_END_PRODUCT_NAME_LIST,
    CommonInfoAttr::AD_PHOTO_PLAYED_END_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoProductNameVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CLICK_PRODUCT_NAME_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_CLICK_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoProductNameVisitor(
    KeyPosType::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST,
    CommonInfoAttr::AD_ITEM_CLICK_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoProductNameVisitor(
    KeyPosType::USER_EVENT_CONVERSION_PRODUCT_NAME_LIST,
    CommonInfoAttr::EVENT_CONVERSION_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoProductNameVisitor(
    KeyPosType::USER_EVENT_PAY_PRODUCT_NAME_LIST,
    CommonInfoAttr::EVENT_PAY_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoProductNameVisitor(
    KeyPosType::USER_ADLOGFULL_EVENT_NEXTDAY_STAY_PRODUCT_NAME_LIST,
    CommonInfoAttr::ADLOGFULL_EVENT_NEXTDAY_STAY_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoProductNameVisitor(
    KeyPosType::USER_AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST,
    CommonInfoAttr::AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoProductNameVisitor(
    KeyPosType::USER_AD_ITEM_DOWNLOAD_INSTALLED_PRODUCT_NAME_LIST,
    CommonInfoAttr::AD_ITEM_DOWNLOAD_INSTALLED_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoProductNameVisitor(
    KeyPosType::USER_AD_APPROXIMATE_PURCHASE_PRODUCT_NAME_LIST,
    CommonInfoAttr::AD_APPROXIMATE_PURCHASE_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoProductNameVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CANCEL_LIKE_PRODUCT_NAME_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_CANCEL_LIKE_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoProductNameVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_PRODUCT_NAME_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_NEGATIVE_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoProductNameVisitor(
    KeyPosType::USER_ADLOGFULL_AD_LIVE_CANCEL_FOLLOW_PRODUCT_NAME_LIST,
    CommonInfoAttr::ADLOGFULL_AD_LIVE_CANCEL_FOLLOW_PRODUCT_NAME_LIST);
}

void PbAccountIdVisitorManager::DebugVisitor(const AdLog& adlog,
                                             size_t pos,
                                             const std::vector<KeyPosType>& p_types) {
  if (pos >= adlog.item_size()) {
    return;
  }

  std::vector<uint64_t> account_ids;
  for (size_t i = 0; i < p_types.size(); i++) {
    auto it = account_visitors_.find(p_types[i]);
    if (it != account_visitors_.end()) {
      AccountIdSeq id_seq = it->second(adlog, pos);
      for (size_t j = 0; j < id_seq.size(); j++) {
        account_ids.push_back(id_seq[j]);
      }
    } else {
      LOG(ERROR) << "cannot find visitor, p_type: " << static_cast<int>(p_types[i]);
    }
  }

  auto views = IndustryMapping::Instance().GetMultiIndustryV5(adlog, p_types, pos);
  LOG(INFO) << "DebugVisitor, llsid: " << adlog.llsid()
            << ", item_id: " << adlog.item(pos).id()
            << ", account_ids: " << absl::StrJoin(account_ids, ",")
            << ", info size; " << views.size();
}

bool PbAccountIdVisitorManager::RegisterLabelCentreVisitor(KeyPosType pos_type, MappingType type) {
  auto iter = keys_visitors_.find(pos_type);
  if (iter == keys_visitors_.end()) {
    LOG(FATAL) << "register fail for pos type: " << ToInteger(pos_type)
               << ", mapping type: " << type;
  }
  return ks::ad_algorithm::label_centre::LabelCentreMapping::Instance().RegisterKeysVisitor(
                     LogType::LT_PB, pos_type, type, iter->second);
}

void PbAccountIdVisitorManager::RegisterAllLabelCentreVisitors() {
  static KeySeq default_v;
  keys_visitors_[KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID] =
      [](const AdLogInterface& log, size_t pos) {
        if (pos >= log.item_size()) {
          return default_v;
        }
        const auto& adlog = reinterpret_cast<const AdLog&>(log);
        const auto& item = adlog.item(pos);
        if (item.has_ad_dsp_info()) {
          const auto& ad_dsp_info = item.ad_dsp_info();
          if (ad_dsp_info.has_unit()) {
            const auto& unit = ad_dsp_info.unit();
            if (unit.has_base()) {
              return KeySeq(unit.base().account_id());
            }
          }
        }
        return default_v;
      };
  RegisterItemRealTimeCommonAttr(
    KeyPosType::ITEM_INNER_LOOP_AUTO_UNIT_LIST,
    kuaishou::ad::ContextInfoCommonAttr::INNER_LOOP_AUTO_UNIT_LIST);
  RegisterItemRealTimeCommonAttr(
    KeyPosType::ITEM_INNER_LOOP_AUTO_PHOTO_LIST,
    kuaishou::ad::ContextInfoCommonAttr::INNER_LOOP_AUTO_PHOTO_LIST);
  RegisterItemRealTimeCommonAttr(
    KeyPosType::ITEM_INNER_LOOP_AUTO_AUTHOR_ID,
    kuaishou::ad::ContextInfoCommonAttr::INNER_LOOP_AUTO_AUTHOR_ID);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_PLAYED_5S_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_PLAYED_5S_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_PLAYED_END_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_PLAYED_END_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CLICK_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_CLICK_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_EVENT_CONVERSION_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_EVENT_CONVERSION_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_EVENT_PAY_UNION_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_EVENT_PAY_UNION_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_EVENT_NEXTDAY_STAY_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_EVENT_NEXTDAY_STAY_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_AD_ITEM_DOWNLOAD_COMPLETED_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_ITEM_DOWNLOAD_COMPLETED_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoKeysVisitor(
      KeyPosType::USER_ADLOGFULL_AD_ITEM_DOWNLOAD_INSTALLED_ACCOUNT_ID_LIST,
      CommonInfoAttr::ADLOGFULL_AD_ITEM_DOWNLOAD_INSTALLED_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoKeysVisitor(
      KeyPosType::USER_ADLOGFULL_AD_APPROXIMATE_PURCHASE_ACCOUNT_ID_LIST,
      CommonInfoAttr::ADLOGFULL_AD_APPROXIMATE_PURCHASE_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CANCEL_LIKE_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_CANCEL_LIKE_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_NEGATIVE_ACCOUNT_ID_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_AD_LIVE_CANCEL_FOLLOW_ACCOUNT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_LIVE_CANCEL_FOLLOW_ACCOUNT_ID_LIST);
  keys_visitors_[KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_ADVERTISER_BASE_PRODUCT_NAME] =
    [](const AdLogInterface& log, size_t pos) {
      if (pos >= log.item_size()) {
        return default_v;
      }
      const auto& adlog = reinterpret_cast<const AdLog&>(log);
      const auto& item = adlog.item(pos);
      if (item.has_ad_dsp_info()) {
        const auto& ad_dsp_info = item.ad_dsp_info();
        if (ad_dsp_info.has_advertiser_base()) {
          auto& product_name = item.ad_dsp_info().advertiser_base().product_name();
          return KeySeq(java_hash_code(product_name));
        }
      }
      return default_v;
  };
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST,
    CommonInfoAttr::AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_AD_PHOTO_PLAYED_END_PRODUCT_NAME_LIST,
    CommonInfoAttr::AD_PHOTO_PLAYED_END_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CLICK_PRODUCT_NAME_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_CLICK_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST,
    CommonInfoAttr::AD_ITEM_CLICK_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_EVENT_CONVERSION_PRODUCT_NAME_LIST,
    CommonInfoAttr::EVENT_CONVERSION_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_EVENT_PAY_PRODUCT_NAME_LIST,
    CommonInfoAttr::EVENT_PAY_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_EVENT_NEXTDAY_STAY_PRODUCT_NAME_LIST,
    CommonInfoAttr::ADLOGFULL_EVENT_NEXTDAY_STAY_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST,
    CommonInfoAttr::AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_AD_ITEM_DOWNLOAD_INSTALLED_PRODUCT_NAME_LIST,
    CommonInfoAttr::AD_ITEM_DOWNLOAD_INSTALLED_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_AD_APPROXIMATE_PURCHASE_PRODUCT_NAME_LIST,
    CommonInfoAttr::AD_APPROXIMATE_PURCHASE_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CANCEL_LIKE_PRODUCT_NAME_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_CANCEL_LIKE_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_PRODUCT_NAME_LIST,
    CommonInfoAttr::ADLOGFULL_AD_PHOTO_NEGATIVE_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_AD_LIVE_CANCEL_FOLLOW_PRODUCT_NAME_LIST,
    CommonInfoAttr::ADLOGFULL_AD_LIVE_CANCEL_FOLLOW_PRODUCT_NAME_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::E2E_STAT_LIST,
    CommonInfoAttr::E2E_STAT_LIST);
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::E2E_ACTIVE_LIST,
    CommonInfoAttr::E2E_ACTIVE_LIST);
  keys_visitors_[KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_PHOTO_ID] =
    [](const AdLogInterface& log, size_t pos) {
      if (pos >= log.item_size()) {
        return default_v;
      }
      const auto& adlog = reinterpret_cast<const AdLog&>(log);
      const auto& item = adlog.item(pos);
      if (item.has_ad_dsp_info() && item.ad_dsp_info().has_photo_info()) {
        return KeySeq(item.ad_dsp_info().photo_info().id());
      }
      return default_v;
  };
  keys_visitors_[KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ID] =
      [](const AdLogInterface& log, size_t pos) {
        if (pos >= log.item_size()) {
          return default_v;
        }
        const auto& adlog = reinterpret_cast<const AdLog&>(log);
        const auto& item = adlog.item(pos);
        if (item.has_ad_dsp_info()) {
          const auto& ad_dsp_info = item.ad_dsp_info();
          if (ad_dsp_info.has_unit()) {
            const auto& unit = ad_dsp_info.unit();
            if (unit.has_base()) {
              return KeySeq(unit.base().id());
            }
          }
        }
        return default_v;
      };
  RegisterUserCommonInfoKeysVisitor(
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_UNIT_ID_LIST,
    CommonInfoAttr::ADLOGFULL_AD_ITEM_CLICK_UNIT_ID_LIST);
}

}  // namespace ad_algorithm
}  // namespace ks
