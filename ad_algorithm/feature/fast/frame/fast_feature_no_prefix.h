#pragma once
#include <vector>
#include "teams/ad/ad_algorithm/feature/fast/ad_log.h"
#include "teams/ad/ad_algorithm/log_preprocess/ad_model_kconf_util.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/feature_interface/fast_feature_interface.h"

namespace ks {
namespace ad_algorithm {

class FastFeatureNoPrefix : public FastFeature {
 public:
  explicit FastFeatureNoPrefix(FeatureType type, size_t left_bits = 52) :
    FastFeature(type, left_bits) {}

  FastFeatureNoPrefix(FeatureType type, size_t left_bits, size_t right_bits,
                      CombineVersion combine_version = DEFAULT) :
    FastFeature(type, left_bits, right_bits, combine_version) {}

  FastFeatureNoPrefix(FeatureType type, size_t left_bits, size_t mid_bits,
                      size_t right_bits):
    FastFeature(type, left_bits, mid_bits, right_bits) {}

  virtual ~FastFeatureNoPrefix() {}

  // 新 feature 抽取必须配置 slot
  bool NeedSlot() override {
    return true;
  }

 protected:
  // Feature 抽取逻辑校验接口, 校验 result 是否符合预期
  virtual bool Check(const AdLog &adlog, size_t pos, const std::vector<ExtractResult> &result) {
    // sign 的高位预留为 slot 的位置，检查特征没有误设置预留位
    if (!FastFeatureInterface::IsDense()) {
      for (auto &res : result) {
        if (FastFeatureInterface::GetPrefix(res.sign) > 0) {
          LOG(WARNING) << " llsid " << adlog.llsid()
                       << " invalid sign " << res.sign << " with prefix != 0";
          return false;
        }
      }
    }
    return CheckInternal(adlog, pos, result);
  }

  virtual bool CheckInternal(const AdLog &adlog, size_t pos, const std::vector<ExtractResult> &result) {
    // 默认不进行其他校验
    return true;
  }
};

}  // namespace ad_algorithm
}  // namespace ks
