#include <vector>
#include "base/common/logging.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/common_info_attr_feature.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.pb.h"

namespace ks {
namespace ad_algorithm {

thread_local uint32_t CommonInfoAttrFeature::user_common_info_cursor_ = 0;
thread_local std::vector<uint32_t> CommonInfoAttrFeature::item_common_info_cursor_;

CommonInfoAttrFeature::CommonInfoAttrFeature(FeatureType type,
                                             ::kuaishou::ad::CommonInfoAttr::Name attr_name,
                                             FeaturePrefix prefix)
  : FastFeatureNoPrefix(type), attr_name_(attr_name), feature_prefix_(prefix),
    is_item_feature_(!IsUserType(type)) {
  if (type == FeatureType::COMBINE || type == FeatureType::DENSE_COMBINE) {
    LOG(FATAL) << "combine feature is not supported";
  }
  LOG(INFO) << "feature type " << type << " attr name " << attr_name
            << " prefix " << prefix << " is_item " << is_item_feature_;
}

// Feature 抽取逻辑校验接口, 校验 result 是否符合预期
bool CommonInfoAttrFeature::CheckInternal(const AdLog &adlog,
                                          size_t pos,
                                          const std::vector<ExtractResult> &result) {
  // 此处用最简单的 CommonAttr 获取逻辑，以尽量避免逻辑错误
  auto common_attr = GetCommonInfoAttrList(adlog, pos);
  const ::kuaishou::ad::CommonInfoAttr* target_attr = nullptr;
  if (common_attr != nullptr) {
    for (const auto &attr : *common_attr) {
      if (attr.name_value() == attr_name_) {
        target_attr = &attr;
        break;
      }
    }
  }

  if (target_attr == nullptr) {
    if (result.empty()) {
      // common attr 为空，results 为空，符合预期
      return true;
    }

    // common attr 为空，results 非空，不符合预期
    LOG(WARNING) << "attr " << attr_name_ << " llsid " << adlog.llsid() << " item pos " << pos
                  << " attr not found with non-empty results size " << result.size();
    return false;
  }

  return CheckCommonAttr(target_attr, result);
}

bool CommonInfoAttrFeature::Init(const base::Json& config) {
  is_photo_feature_ = config.GetInt("is_photo_feature", false);
  is_live_feature_ = config.GetInt("is_live_feature", false);
  is_ecom_realtime_feature_ = config.GetInt("is_ecom_realtime_feature", false);
  return InitInternal(config);
}

void CommonInfoAttrFeature::Reset(uint32_t item_count) {
  user_common_info_cursor_ = 0;
  item_common_info_cursor_.clear();
  item_common_info_cursor_.resize(item_count, 0);
}

void CommonInfoAttrFeature::Extract(const AdLog & adlog,
                                    size_t pos,
                                    std::vector<ExtractResult>* result) {
  auto attr = GetCommonInfoAttr(adlog, pos);
  if (attr == nullptr) {
    return;
  }
  ExtractCommonAttr(attr, result);
}

const ::google::protobuf::RepeatedPtrField<::kuaishou::ad::CommonInfoAttr>*
CommonInfoAttrFeature::GetCommonInfoAttrList(const AdLog &adlog, size_t pos) {
  auto common_attr = &adlog.user_info().common_info_attr();
  if (is_item_feature_) {
    const auto &item = adlog.item(pos);
    common_attr = nullptr;
    switch (item.type()) {
      case ItemType::AD_DSP:
      case ItemType::NATIVE_AD:
      case ItemType::AD_BRAND:
        if (item.has_ad_dsp_info()) {
          if (is_live_feature_) {
            if (item.ad_dsp_info().has_live_info() &&
                item.ad_dsp_info().live_info().common_info_attr_size() > 0) {
              common_attr = &item.ad_dsp_info().live_info().common_info_attr();
            }
          } else if (is_photo_feature_) {
            if (item.ad_dsp_info().has_photo_info() &&
                item.ad_dsp_info().photo_info().common_info_attr_size() > 0) {
              common_attr = &item.ad_dsp_info().photo_info().common_info_attr();
            }
          } else if (item.ad_dsp_info().common_info_attr_size() > 0) {
            common_attr = &item.ad_dsp_info().common_info_attr();
          }
        }
        break;
      case ItemType::FANS_TOP:
        if (item.has_fans_top_info()) {
          if (is_live_feature_) {
            if (item.fans_top_info().has_live_info() &&
                item.fans_top_info().live_info().common_info_attr_size() > 0) {
              common_attr = &item.fans_top_info().live_info().common_info_attr();
            }
          } else if (item.fans_top_info().has_photo_info() &&
                      item.fans_top_info().photo_info().common_info_attr_size() > 0) {
            common_attr = &item.fans_top_info().photo_info().common_info_attr();
          }
        }
        break;
      case ItemType::FANS_TOP_LIVE:
      case ItemType::FANS_TOP_LIVE_MERCHANT:
        if (item.has_fans_top_live_info() && item.fans_top_live_info().has_live_info() &&
            item.fans_top_live_info().live_info().common_info_attr_size() > 0) {
          common_attr = &item.fans_top_live_info().live_info().common_info_attr();
        }
        break;
      default:
        break;
    }
  } else if (is_ecom_realtime_feature_) {
    common_attr = nullptr;
    if (adlog.user_info().has_user_real_time_action()) {
      auto &real_time_action = adlog.user_info().user_real_time_action();
      if (real_time_action.ad_ecom_realtime_user_common_attr_size() > 0) {
        common_attr = &real_time_action.ad_ecom_realtime_user_common_attr();
      }
    }
  }
  return common_attr;
}

const ::kuaishou::ad::CommonInfoAttr*
CommonInfoAttrFeature::GetCommonInfoAttr(const AdLog &adlog, size_t pos) {
  auto common_attr = GetCommonInfoAttrList(adlog, pos);
  if (common_attr == nullptr) {
    return nullptr;
  }
  uint32_t *cursor = &user_common_info_cursor_;

  // TODO(yangjialin) 待 feature 排序后去掉重置 cursor 逻辑，改到 bundle 中
  *cursor = 0;

  while (*cursor < common_attr->size()) {
    auto &attr = (*common_attr)[*cursor];
    /* TODO(yangjialin) 待 feature 排序后加回来
    if (attr.name_value() > attr_name_) {
      // 没找到对应的 attr，直接返回
      return nullptr;
    }*/

    ++(*cursor);
    if (attr.name_value() == attr_name_) {
      return &attr;
    }
  }

  return nullptr;
}

}  // namespace ad_algorithm
}  // namespace ks
