
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group17.h"
#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_huoke_product_name_ratio_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_live_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_live_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_creative_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_photo_or_live_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_photo_or_live_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_creative_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_user_id_creative_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_photo_or_live_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_user_id_photo_or_live_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_photo_and_live_author_id_no_prefix_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_unify_inner.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_smart_offer_multi_random_sample.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_combine_smart_offer_multi_random_sample.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_realtime_time_diff.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_item_price_mode_dense_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_item_price_mode_sparse_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_query_hot_sequence_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_account_type_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ocpc_action_type_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_qcpx_discount_rate.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_qcpx_discount_rate_cap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_item_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_item_chain_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_no_bid.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_item_align_rqvae_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_item_align_similar.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_item_align_rqvae_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_item_align_similar.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shelf_merchant_ecom_goods_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shelf_merchant_photo_play_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shelf_merchant_photo_click_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shelf_merchant_live_play_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shelf_merchant_live_click_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shelf_merchant_live_goods_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_item_impression_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_item_click_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_play_3s_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_play_5s_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_play_end_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_order_paied_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_order_submit_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_goods_view_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_search_item_impre_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_search_item_click_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_search_play_3s_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_search_play_5s_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_search_play_end_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_search_order_paied_goods_item_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_author_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_account_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_product_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_second_industry_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_cluster_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_photo_min_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_author_min_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_account_min_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_product_min_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_second_industry_min_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_cluster_min_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_ecom_paycnt_label.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_threshold_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_ocpx_action_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_goods_sid_matched_cnt_goods_view_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_goods_sid_matched_cnt_goods_view_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_goods_sid_matched_cnt_goods_view_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_goods_sid_matched_cnt_order_submitl_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_goods_sid_matched_cnt_order_submitl_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_goods_sid_matched_cnt_order_submitl_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_photo_sid_matched_cnt_play_10s_l_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_photo_sid_matched_cnt_play_10s_l_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_photo_sid_matched_cnt_play_10s_l_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_photo_sid_matched_cnt_play_end_l_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_photo_sid_matched_cnt_play_end_l_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_photo_sid_matched_cnt_play_end_l_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_title_select_tokens_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_title_select_tokens_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_title_select_tokens_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_title_select_tokens_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_title_select_tokens_5.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_title_select_tokens_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_title_select_tokens_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_title_select_tokens_8.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_title_select_tokens_9.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_title_select_tokens_10.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_title_select_tokens_11.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_title_select_tokens_12.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_sdpa_name_fix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_global_pay_cnt_gmv.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_time_window_cost_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_time_window_gmv_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_time_window_ctr_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_time_window_cvr_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_time_window_send_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_time_window_convert_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sdpa_product_id_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_account_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_conv_extend_second_industry_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_uni_rank_size.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_uni_recall_size.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_uni_rank_size.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_uni_recall_size.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_cost_one_hour.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_photo_send_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_photo_convert_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_photo_gmv_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_photo_ctr_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_photo_cvr_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_cvr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_unify_ctr_info_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_audience.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_server_show_ctr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_ctr_model_unify_live_audience_rate.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_title_select_tokens_13.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_title_select_tokens_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_dense_p_2l_soft_pec_top_bar_status.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_sparse_p_2l_soft_pec_top_bar_status.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_photo_send_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_photo_send_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_author_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_advertiser_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_photo_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_live_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_yellow_cart_cate_2_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_yellow_cart_cate_3_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_u_type_author_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_l_type_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_u_type_l_type_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_qcpx_photo_self_train_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inner_photo_qcpx_coupon.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_paycnt_indirect.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_qcpx_live_pec_style_abtest_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_qcpx_live_pec_style_abtest_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_qcpx_p_2l_soft_pec_style_abtest_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_qcpx_p_2l_soft_pec_style_abtest_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_qcpx_p_2l_hard_pec_style_abtest_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_qcpx_p_2l_hard_pec_style_abtest_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_photo_cost_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_unit_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_unit_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_photo_creative_num_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_photo_gmv_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_unit_gmv_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_cost_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_cost_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_convert_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_convert_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_gmv_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_gmv_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_send_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_send_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_photo_cost_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_photo_cost_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_photo_convert_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_photo_convert_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_photo_gmv_24_h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_photo_gmv_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_creative_num_cur.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_p_2l_photo_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_qcpx_live_pec_style_abtest_hash_id_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_qcpx_live_pec_style_abtest_hash_id_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_qcpx_p_2l_soft_pec_style_abtest_hash_id_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_qcpx_p_2l_soft_pec_style_abtest_hash_id_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_qcpx_p_2l_hard_pec_style_abtest_hash_id_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_qcpx_p_2l_hard_pec_style_abtest_hash_id_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_industry_14d_paycnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_industry_14d_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_industry_14d_gmv.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_industry_7d_paycnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_industry_7d_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_industry_7d_gmv.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_impression_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_imp_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_play_end_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_click_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_good_view_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_order_pay_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_order_submit_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_order_pay_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_good_view_cnt_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_pos_comment_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_neg_comment_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_neu_comment_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_comment_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_comment_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_unify_inner_behavior.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_item_align_res_kmeans.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_item_align_pq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_align_oc_tree.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_item_align_ann.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_item_align_ann_hot.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_qcpx_photo_self_train_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_all_merchant_author_id_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_imp_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_imp_industry_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_imp_account_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_imp_author_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_play_end_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_play_end_industry_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_play_end_account_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_play_end_author_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_click_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_click_industry_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_click_account_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_click_author_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_good_view_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_good_view_industry_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_good_view_account_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_good_view_author_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_orderpay_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_orderpay_industry_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_orderpay_account_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_t_0_orderpay_author_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_order_submit_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_order_submit_x_7_cat_2_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_order_submit_x_7_cat_3_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_order_submit_x_7_cat_4_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_orderpay_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_orderpay_x_7_cat_2_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_orderpay_x_7_cat_3_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_orderpay_x_7_cat_4_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_a.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_b.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_5.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_8.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_9.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_10.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_11.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_12.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_13.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_14.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_15.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_16.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_17.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_18.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_19.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_20.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_adcu_text_g_21.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_good_view_spu_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_good_view_x_7_cat_2_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_good_view_x_7_cat_3_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_t_0_good_view_x_7_cat_4_match_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_campaign_use_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_qcpx_photo_self_train_tag_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_yellow_car_avg_price_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_yellow_car_max_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_yellow_car_min_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_price_1h.cc"   // NOLINT

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_cnt_1h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_mean.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_max.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_min.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_ordercnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_price_3h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_cnt_3h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_slot_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_slot_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_chain_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_roi_2_storewide.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_dense_atv.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_feed_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_feed_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_single_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_single_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_his_feed_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_his_feed_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_his_single_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_his_single_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_feed_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_feed_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_single_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_single_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_item_his_feed_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_item_his_feed_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_item_his_single_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_item_his_single_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_item_card_pay_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_item_card_pay_amount.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_lps_20_in_sample_weight.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_crosspv_time.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_conversion_delivery_diff_whether_le_20_min.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lc_rec_sid_match_first.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lc_rec_sid_match_second.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lc_rec_sid_match_three.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_item_align_rqvae_id_p_2l.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_item_align_similar_p_2l.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_lable_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_no_ltv_bid.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_outer_photo_is_aigc.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_pred_cpm.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_pred_fill_rate.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_dnc_score.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_is_live_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_more_author_all_merchant_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_more_author_id_user_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_chain_expand_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_gmv_static.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_gmv_avg_bucket.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_gmv_sum_bucket.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_order_spu_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_spu_price_std.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_spu_price_avg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_spu_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_store_wide.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_biz_center_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_order_biz_center_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_click_biz_center_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_photo_campaign_type_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sku_id_top_20_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sku_price_top_20_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pctr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_plvtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_psvr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pvtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pwatchtime_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pwtd_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pcpr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pltr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pwtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pftr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pcmtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_phtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pclick_live_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_p_effective_watch_live_time_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pptr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pepstr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_plstr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_petcm_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pcmef_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pctr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_psvr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_plvtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pltr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pwtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pcmtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_phtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pin_lvtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pin_etr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_petr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pctetr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pctlvtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pfanstr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pgtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pnegtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pptr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_preportr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pshtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pvtr_itemcontext_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_inner_merge_top_layer_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_pctr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_plvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_psvr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_pvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_pwatchtime_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_pwtd_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_pcpr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_pltr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_pwtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_pftr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_pcmtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_phtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_pclick_live_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_p_effective_watch_live_time_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_pptr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_pepstr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_plstr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_petcm_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_frrank_uescore_pcmef_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_mean_15m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_mean_30m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_mean_60m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_std_30m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_std_15m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_rate_15m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_rate_30m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_llm_cid_l_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_llm_cid_l_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_llm_cid_l_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_llm_predict_top_cid_l_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_llm_predict_top_cid_l_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_llm_predict_top_cid_l_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_calibrated_cpm_score.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pctr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_psvr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_plvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pltr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pwtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pcmtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_phtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pin_lvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pin_etr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_petr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pctetr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pctlvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pfanstr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pgtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pnegtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pptr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_preportr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pshtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_live_pvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_account_dense_cid_offsite_order_cate_1_id_180d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_x_7_level_1_mmub.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_x_7_level_2_mmub.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_x_7_level_3_mmub.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_x_7_level_4_mmub.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_has_frrank_uescore.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_has_frrank_uescore_live.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_has_frrank_uescore_itemcontext.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_has_frrank_uescore_live_itemcontext.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_natural_share_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_natural_like_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_natural_collect_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_natural_download_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_natural_long_play_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_natural_comment_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_natural_follow_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_ad_wechat_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_ad_clk_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_ad_eca_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_5.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_8.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_9.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_10.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_11.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_12.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_13.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_14.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_15.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_16.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_17.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_18.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_19.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_20.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_21.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_22.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_23.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_24.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_25.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_26.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_28.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_29.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_b_30.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_5.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_8.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_9.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_10.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_11.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_12.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_13.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_14.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_15.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_16.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_17.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_18.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_19.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_20.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_21.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_22.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_23.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_24.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_25.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_26.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_27.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_28.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_29.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_adcu_text_g_c_30.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_ad_ls_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_ad_lps_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_ad_pm_sim_tier.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pri_msg_day_session_round_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pri_msg_first_message_type_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pri_msg_day_session_round.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pri_msg_first_message_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pri_msg_current_session_round_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pri_msg_current_session_round.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pri_msg_item_click_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_price_label.cc"   // NOLINT

void register_group_17() {}

#endif
