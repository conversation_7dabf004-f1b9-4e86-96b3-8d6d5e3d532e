#include "teams/ad/ad_algorithm/feature/fast/impl/group/group7.h"
#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_dense_hash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sdpa_ecom_user_interact_cate_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_bert.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_photo_bid_type_new.h"

// 搜索广告用户行为序列
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_action_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_action_timestamp_list_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_realtime_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_realtime_v2.h"

// gsu
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_longterm_gsu_live_realtime_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_longterm_gsu_live_top.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_longterm_gsu_photo_realtime_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_longterm_item_long.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_longterm_live.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_longterm_live_long.h"

// 多域 sim
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_hetu_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_multi_domain_sim_generate_hetu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_multi_domain_sim_gsu_hetu.h"

// sdpa photo 特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_sdpa_photo_feature.h"

// ad goods
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_goods_gsu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_goods_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sim_seq_ad_goods_v0.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sim_seq_ad_goods_v0_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sim_seq_ad_goods_v0_num.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_gsu_deep_ret.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_longterm_item_v3_tp.h"
// unify_cxr
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_unify_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_new_unify_sctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_unify_cvr.h"

// ad mix
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_cat_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_cat_info_merge.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_cur_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_pos_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_id_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_pred_info_log.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_pred_info_log_merge.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_seq_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_session_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_cur_ad_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_cur_ad_info_match.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_cvr_end.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_cvr_start.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_infer_cur_ad_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_unify_sctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_pre_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_pre_info_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_pos_dense_onehot_infer.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_pos_dense_onehot_train.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_pv_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_is_first_session.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_page_id_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sub_page_id_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_longterm_item_v3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_longterm_top_item_v3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_union_ecpm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_seq_mask.h"

// 侯德榜搜索域特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_field_id_list.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_bid.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_user_buy180d_Item_cat3_sequence.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_id_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_longterm_goods_tp.h"
// realtime
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_action_backflow.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_action_cross.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_action_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action.h"

void register_group_7() {}

#endif
