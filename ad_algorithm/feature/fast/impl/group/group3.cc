#include "teams/ad/ad_algorithm/feature/fast/impl/group/group3.h"

#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

// 用户行为序列
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_no_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_no_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_impression_no_play3s_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_no_conv_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_no_item_click_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_no_conv_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_no_conv_ecom.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_ecom_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_ecom_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_ecom_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_ecom_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conv_extend_ecom_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_impression_extend_ecom_days.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_num.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universal_embedding_dense.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_second_industry_share.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_event_credit_grant_account.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_event_jinjian_account.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_credit_jinjian_grant_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_loan_grant_product.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_loan_jinjian_product.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rank_unify_cxr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rank_unify_cxr_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rank_unify_cvr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rank_unify_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_target_flag.h"
//  联盟
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_smart_target_calibrator.h"

// zd add sequence
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_itemclick_photo_product_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_lps_photo_product_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_photo_product_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_itemclick_photo_second_industry_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_lps_photo_second_industry_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_photo_second_industry_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_second_industry_share.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_id_no_prefix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_spu_v3_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_apm_goods_action_seq_picasso.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_apm_goods_action_sample_info.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_product_share_v2.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_product_share_v3.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_itemclick_photo_id_sequence.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_lps_photo_id_sequence.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ordersubmit_photo_id_sequence.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ordersubmit_photo_product_sequence.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_photo_id_sequence.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_universe_photo_conv_medium_industry_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_universe_photo_conv_medium_industry_cvr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_universe_photo_conv_medium_industry_dr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_universe_photo_conv_medium_sub_industry_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_universe_photo_conv_medium_sub_industry_cvr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_universe_photo_conv_medium_sub_industry_dr.h"

// user photo enhance
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_enhance_real_play_rate.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_age.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_author_fusion.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_avg_play_rate_explore.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_avg_play_rate_global.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_avg_play_rate_thanos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_duration.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_enter_profile_count_explore.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_enter_profile_count_explore_rate.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_enter_profile_count_global.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_enter_profile_count_global_rate.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_enter_profile_count_thanos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_enter_profile_count_thanos_rate.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_photo_fusion_explore.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_photo_fusion.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_photo_fusion_thanos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_enhance_tag_favor.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_enhance_upload_gap.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_htag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_htag_uniq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_htag0.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_htag1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_htag2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_enhance_follow_htag0_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_enhance_follow_htag1_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_enhance_follow_htag2_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_enhance_follow_htag_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_enhance_follow_htag_seq_uniq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_enhance_follow_htag_all.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_enhance_follow_author_htag_lv1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_enhance_follow_author_htag_lv2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_enhance_follow_author_htag_lv3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_follow_htag_match.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_bid_type.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_style_t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_union_dense_adstyle.h"

// 本地生活
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_local_life_sku_order_action.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_local_life_pay_order_buyer_id_shop_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_support_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_crm_define_industry_info.h"
// 搜索广告
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_account_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_ad_campaign_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_ad_campaign_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_ad_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_ad_unit_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_dup_cover_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_page_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_query.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_src.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_product_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_product_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_pos_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_pos_predict.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recal_strategy.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recal_relevance.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recal_matchtype.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_pos_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_photo_slogan.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_photo_slogan_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_photo_pname.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_photo_pname_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_photo_ocr_title.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_photo_ocr_title_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_photo_ocr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_photo_ocr_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_photo_description.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_photo_description_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_photo_cname.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_photo_cname_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_photo_asr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_photo_asr_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_from_page.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_time_advertiser.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_loc_advertiser.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_photo_video_feature_moco_cluster.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_photo_text_feature_bert_cluster.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_corporation.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_ad_username.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_ad_actionbar.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_new_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_search_pos_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_search_pos_product_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_search_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_search_pos_dup_cover_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_search_pos_account_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_search_pos_user_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recall_strategy_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recall_extend_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recall_qr_score.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recall_rewrite_query.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_kbox_type.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_query_source.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_query_catgory_class2.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_query_catgory_class3.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_akg_query_embedding.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_refer_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recall_rewrite_query_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_enter_source.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_bidword.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recall_strategy.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recall_relevance.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recall_matchtype.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_query_token.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_parser_text_v1.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_parser_text_token_v1.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_query_combine_match_num.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_productname_token.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_productname_token_reverse.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_querytoken_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_querytoken_ad_unit_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_querytoken_ad_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_querytoken_ad_campaign_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_querytoken_ad_campaign_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_querytoken_account_id.h"

// 快看点用户行为特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_knews_app_uid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_knews_user_creation_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_knews_user_coin_level.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_knews_user_ad_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_knews_user_ad_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_knews_user_ad_play.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_knews_user_ad_pgc.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_knews_user_ad_dislike.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_knews_user_ad_share.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_knews_user_ad_favorite.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_knews_user_ad_cmt.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_accountflag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_unit_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_refresh_direction.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_register_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_last_item_impression_gap.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_xdt_prehot_timestamp.h"

// crm 行业特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_crm_is_wangzhuan.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_crm_info_product_feature.h"

// wangxin25 add feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sequence_length.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_app_list_sequence_share.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_installed_app_sequence_share.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_conversion_photo_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_conversion_product_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_conversion_second_industry_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_impression_photo_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_impression_product_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_impression_second_industry_sequence.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_cpm_and_bonus.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_app_list_ad_app_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_app_list_ad_cate_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_app_list_ad_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/sample_attr_feature_list.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_follow_time_sequence_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_follow_side_info_sequence_v1.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_cnt_p3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_cnt_p5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_cnt_form_submit.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_cnt_item_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_cnt_item_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_cnt_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_detail_item_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_detail_item_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_detail_p3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_detail_p5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_detail_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_detail_form_submit.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eds_merchant_seller_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eds_merchant_creative_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eds_merchant_secind_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eds_merchant_photo_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eds_direct_photo_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eds_direct_seller_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_put_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_xdt_mmu_embedding.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_online_retail_info_pay_type.h"



#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_merchant_seller_goodsview.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_add_fans_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_pay_buyer_uv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_item_click_uv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_watch_uv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_total_watch_uv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_total_order_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_submit_buyer_uv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_cate3_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_cate3_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_total_order_gmv.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_impression_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_impression_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_item_click_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_item_click_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_play3s_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_play3s_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_play5s_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_play5s_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_playend_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_playend_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_conversion_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_conversion_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_lps_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_lps_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_pay_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_retention_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_retention_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_jinjian_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_credit_grant_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_click1_no_click2_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_click1_no_play3s_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_click2_no_conv_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_item_impression.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_universe_long_item_impression.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_app_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_followlist_liveauthor_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_id_list_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_buy_cat_new_seq.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_event_type_product_name_no_pdd.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_media_wangzhuan.h"

// 联盟媒体特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_wangzhuan.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_photo_media_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_photo_media_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_photo_media_uid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_photo_media_ad_style.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_photo_media_wangzhuan.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_media_wangzhuan.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_media_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_wangzhuan_media_wangzhuan.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_media_id_ad_style.h"

// 用户直播行为特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_hate_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_impression_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_playend_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_share_author.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_media_ad_style_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_media_pos_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_media_uid_fix.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_holiday_next.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_conv_next_stay.h"


// 用户 corss 序列
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_merchant_seller_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_merchant_seller_pay.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_merchant_seller_follow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_longterm_photo_form_submit.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_longterm_photo_item_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_longterm_photo_item_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_longterm_photo_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_longterm_photo_p3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_longterm_photo_p5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_realtime_seller_p3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_realtime_seller_p5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_realtime_seller_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_realtime_seller_item_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_realtime_seller_item_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_realtime_seller_form_submit.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_realtime_photo_p3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_realtime_photo_p5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_realtime_photo_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_realtime_photo_item_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_realtime_photo_item_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_realtime_photo_form_submit.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_action_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_action_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_photo_action_seq_feature.h"

// 软广对粉条模型提供兼容版 context 特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_abtest_hash_id_native.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_interactive_form_native.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_new_context_info_native.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rt_browse_type_native.h"

//   小贷行业特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_jinjian_photo_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_jinjian_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_lps_photo_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_lps_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_shouxin_photo_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_shouxin_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_impression_no_click_photo_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_impression_no_click_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_impression_no_play3s_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_jinjian_extend_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_jinjian_no_shouxin_photo_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_jinjian_no_shouxin_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_no_jinjian_photo_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_no_jinjian_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_no_shouxin_photo_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_no_shouxin_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_shouxin_extend_xiaodai.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reco_slide_fm_re.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_dense_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info_playend_more_combine.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info_play5s_more_combine.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info_item_click_more_combine.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info_playend_more_combine_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info_play5s_more_combine_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info_item_click_more_combine_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_xiaodai_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_xiaodai_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_xiaodai_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_xiaodai_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_jinjian_extend_xiaodai_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_jinjian_extend_xiaodai_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_xiaodai_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_xiaodai_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_xiaodai_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_xiaodai_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_shouxin_extend_xiaodai_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_shouxin_extend_xiaodai_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_social_price_info.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_creative_material_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sub_page_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_server_no_item_click_flag_no_userid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_selling_goods_embedding.h"



#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_2_user_up_emb.cc"    // NOLINT



// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_2_user_up_emb.cc"    // NOLINT
// 粉丝 & 直播实时行为细化
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_author_fans_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_realtime_action_list_match_n_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inner_ad_creative_type.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_recall_product_name_embedding_dense.h"
#endif

void register_group_3() {}
