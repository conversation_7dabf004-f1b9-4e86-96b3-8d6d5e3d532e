#include "teams/ad/ad_algorithm/feature/fast/impl/group/group5.h"
#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_callback_event_dense_conv_invoke.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ecom_photo_spu_emb.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mgi_e2e_photo_emb_dim8.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_pre_info_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recall_extend_type_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recall_matchtype_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recall_qr_score_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recall_relevance_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recall_strategy_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_recall_strategy_type_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_realtime_v3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_goods_gsu_picasso.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_realtime_v3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_realtime_action_list_sim_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_history_pdct_emb_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_history_photo_emb_dim8.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_view_component_type.h"

void register_group_5() {}

#endif
