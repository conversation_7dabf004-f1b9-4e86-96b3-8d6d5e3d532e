#include "teams/ad/ad_algorithm/feature/fast/impl/group/group4.h"

#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

// 直播交叉特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eshop_click_cate1_match_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eshop_click_cate2_match_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eshop_click_cate3_match_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eshop_buy_cate1_match_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eshop_buy_cate2_match_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eshop_buy_cate3_match_num.h"
// 用户观看直播间隔
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_live_ave_gap.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_live_last_gap.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_time_gap_cross_ped.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_time_gap_ped.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_time_gap_cross_ped.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_time_gap_ped.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_time_gap_p3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_time_gap_p5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_time_gap_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_time_gap_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_time_gap_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_time_gap_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_time_gap_cross_p3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_time_gap_cross_p5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_time_gap_cross_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_time_gap_cross_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_time_gap_cross_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_time_gap_cross_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_time_gap_p3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_time_gap_p5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_time_gap_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_time_gap_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_time_gap_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_time_gap_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_time_gap_cross_p3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_time_gap_cross_p5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_time_gap_cross_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_time_gap_cross_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_time_gap_cross_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_time_gap_cross_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_jinniu_xiaodian_product_id.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_cnt_form_submit_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_cnt_item_clk_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_form_submit.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_cnt_ts_form_submit.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_list_ts_form_submit.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_item_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_cnt_ts_item_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_list_ts_item_clk.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_order_by_cate.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_order_by_live.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_author_p2l_count.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_click_author_id_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_click_item_id_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_click_cate1_id_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_click_cate2_id_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_click_cate3_id_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_author_id_match_num_sparse_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_author_id_match_num_dense_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_author_id_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate1_id_match_num_sparse_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate1_id_match_num_dense_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate2_id_match_num_sparse_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate2_id_match_num_dense_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate3_id_match_num_sparse_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate3_id_match_num_dense_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate3_id_gsu_item_id_list_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate3_id_gsu_author_id_list_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate3_id_gsu_cate1_id_list_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate3_id_gsu_cate2_id_list_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate3_id_gsu_item_id_seq_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate3_id_gsu_author_id_seq_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate3_id_gsu_cate1_id_seq_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate3_id_gsu_cate2_id_seq_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_mmu_cate1_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_mmu_cate2_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_mmu_cate3_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate2_id_gsu_item_id_list_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate2_id_gsu_author_id_list_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate2_id_gsu_cate1_id_list_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate2_id_gsu_cate3_id_list_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate2_id_gsu_item_id_seq_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate2_id_gsu_author_id_seq_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate2_id_gsu_cate1_id_seq_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate2_id_gsu_cate3_id_seq_longterm.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_click_author_id_longterm_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_click_cate1_id_longterm_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_click_cate2_id_longterm_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_click_cate3_id_longterm_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_click_author_id_seq_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate3_id_gsu_item_id_list_longterm_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_cate3_id_gsu_author_id_list_longterm_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_click_cate3_id_gsu_item_id_list_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_click_cate3_id_gsu_author_id_list_longterm.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reward_distbt_coin_no_ad_cnt_sd.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reward_distbt_coin_watch_photo_cnt_sd.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_callback_event_onoff_consistent.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_ecom_conv_guiyin_type.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_collect_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_collect_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_collect_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_enter_profile_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_enter_profile_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_enter_profile_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_hate_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_hate_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_hate_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reid_user_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reid_photo_tag.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_server_no_item_click_flag_fix_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_server_no_item_click_flag_no_userid_fix_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_ecom_lps_item_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_server_no_item_click_flag_fix_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_server_no_item_click_flag_no_userid_fix_v2.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top1_interact_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top2_interact_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top3_interact_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top4_interact_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top5_interact_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top6_interact_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_new_industry_v3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_new_industry_v3_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_new_industry_v3_select.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_id_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_live_action_seq_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_exp_model.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reward_page_id_dense.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_show_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_show_side_info_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_show_gap_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_hot_real_show_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_hot_real_show_side_info_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_hot_real_show_gap_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_show_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_hot_real_show_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_video_play_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_id_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_author_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_author_side_info_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_video_play_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_video_play_gap_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_id_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_id_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_id_dense_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_id_dense_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_ecom_max_min_price_log.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_component_click_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_num_extend_ecom_dense_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_num_extend_ecom_dense_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_edu_course.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_cate3_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_cate3_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_rewarded_pageid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_fanstop_context.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_detail_pe.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_detail_conv.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_mcda_unify_user_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mcda_action_list.h"

// new fix feature of match and cross v1
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_product_name_clk_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_product_name_pe_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_product_name_conv_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_product_name_invoke_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eds_comp_product_name_cnt_fix.h"
// new fix feature of match and cross v2
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_p3s_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_p5s_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_ped_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_item_imp_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_item_clk_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_detail_item_clk_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_detail_item_imp_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_detail_p3s_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_detail_p5s_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_detail_like_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_ecom_brand_id_feature_new.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_detail_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_context_info_dup_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_datetime_info_dup_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_pageid_dup_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_ad_device_info_context_dup_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_interactive_form_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_industry_item_click_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_photo_id_item_click_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_product_name_dense_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_ad_device_info_ad_info_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_detail_info_ad_info_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_detail_info_context_ad_info_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_location_ad_info_new.h"

// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_ocpc_itemclick_dense.h"

#endif

void register_group_4() {}
