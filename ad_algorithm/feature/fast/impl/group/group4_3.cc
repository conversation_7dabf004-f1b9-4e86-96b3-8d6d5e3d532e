#include "teams/ad/ad_algorithm/feature/fast/impl/group/group4_3.h"
#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"


// dragon 框架的特征抽取配置也放置到 model feature 文件中，使用一个空的特征抽取类兼容
#include "teams/ad/ad_algorithm/feature/fast/impl/empty_combine_dragon.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_cooperation_mode_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reward_page_id_dense_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_item_global_gsu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_item_ecomm_gsu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_item_ecomm_match_category.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_item_u2u_gsu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_item_author_cluster_gsu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_item_spu_gsu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_item_cid3_gsu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_item_ecomm_gsu_sep.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_item_remote_cluster_gsu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_live_item_cluster_gsu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_live_item_a2a_gsu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_live_item_author_cluster_gsu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_live_item_remote_cluster_gsu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_live_item_v1_remote_cluster_gsu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_univ_app_package_std_list.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_account_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_goods_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_goods_cate2_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_goods_price_4_label.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_creative_type_fix.h"

// pdn feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_pdn_realtime_action_list_mask.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_pdn_realtime_action_list_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_pdn_realtime_action_list.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_campaign_type_only_dsp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_quality_emb.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_tag_category_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_author_level_id_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_user_v5.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_htag_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sdpa_user_interact_pid_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_is_connected_live_new_flow_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_photo_mmu_hetu_tag_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_topic_photo_topic_new.h"

// fanstop domain feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_item_type.h"

// deep
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_deep_label_info.h"

// ali
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_ali_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_ali_feature_float.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_ecom_brand_id_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_longterm_top_item.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_longterm_item.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_external_prerank_quota.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_external_rank_quota.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_rank_quota.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_mmu_author_cluster_id.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_goods_attr_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_goods_id_list_size.h"

#endif

void register_group_4_3() {}
