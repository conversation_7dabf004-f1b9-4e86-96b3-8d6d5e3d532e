#include "teams/ad/ad_algorithm/feature/fast/impl/group/init_all_group.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/group/group1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group1_1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group2_1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group3_1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group4.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group4_1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group4_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group4_3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group5.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group6.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group8.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group9.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group10.h"

bool register_all_group() {
  register_group_1();
  register_group_1_1();
  register_group_2();
  register_group_2_1();
  register_group_3();
  register_group_3_1();
  register_group_4();
  register_group_4_1();
  register_group_4_2();
  register_group_4_3();
  register_group_5();
  register_group_6();
  register_group_7();
  register_group_8();
  register_group_9();
  register_group_10();
  return true;
}
