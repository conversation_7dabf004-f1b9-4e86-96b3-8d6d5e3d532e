#include "teams/ad/ad_algorithm/feature/fast/impl/group/group1.h"

#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_tab.h"

// USER
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_follow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_like_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_attribute.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_gender.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_deviceid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_count_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_region_from_ip.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_region.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ipc.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_region_from_ipv6.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_negative.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_negative_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_loc.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_negative.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_report.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_block.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_download.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_download_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_like_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_device_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_device_info_ad_user.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top_short_topic.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_app_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_browsed_ad.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_interest.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_interest_category.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_behavior_category.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_audience.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_life_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_sum_online_dense_combine.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_sum_online_dense_combine_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_sum_online_dense_combine_new_lookalike.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_sum_dense_combine_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_game_label_stat_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_ids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_with_action_and_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_auto_cpa_bid_smart.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_auto_bid_smart_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_ids_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_clk2_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_seq.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dislike_topic.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_hate_topic.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top_like_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dl_click_cluster.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_gender_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_year_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_region_from_gps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_classification_143.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_cf.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_like_mmu_classification_143.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_like_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_recent_follow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_all_follow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_share.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_follow.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_gender_n.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_age.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dislike_ad.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_hate_ad_rate.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_phone_price.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_consumption_level.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_datetime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dowload_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dowload_industry_new.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_new.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_negative_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_new.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_device_info_kcard.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_author_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_large.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_large_new.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_unitAction.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_adload_feature_duration_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_adload_feature_duration_online_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_duration_pv_eliminate_bias.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_duration_pv_eliminate_ocpc_bias.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_adload_category_online.h"
// #include
// "ks/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_classification_623.h"
// #include
// "ks/ad/ad_algorithm/feature/fast/impl/extract_user_like_mmu_classification_623.h"
// #include
// "ks/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_classification_2w_cluster.h"
// #include
// "ks/ad/ad_algorithm/feature/fast/impl/extract_user_like_mmu_classification_2w_cluster.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_cvr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_app_conversion.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_app_download.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c2_lps_cmd.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_c2conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_name.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_city_village.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_embedding_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_follow_photo.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_realtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_realtime.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top3_impression_realtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_avegap_realtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_last_click_mingap_realtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_impression_avegap_realtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top3_click_realtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_impression_noclick_realtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_last_impression_mingap_realtime.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_business_interest.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_offline_retailer_keyword.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_orientation_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_context_info_app.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_new_context_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_new_context_info_rp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_context_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rt_browse_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_combine_channel.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_to_live_author_near_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_follow_author_time_dense.h"

// add user retention stats feas
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_retention_feas.h"

// add thanos user emp statis feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_thanos_emp_statis_ft.h"
// add ks item emp statis feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_ks_emp_statis_ft.h"
// add nebula item emp statis feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_nebula_emp_statis_ft.h"
// add thanos item emp statis feature
// 将 上面两个特征: nebula item 和 ks item 整合为 thanos item 一个特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_thanos_emp_statis_ft.h"
// add combine rank play3s feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_play3s.h"
// add combine rank server_show_ctr feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_server_show_ctr.h"
// add combine rank ntr feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_ntr.h"
// add combine rank ad_playtime feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_ad_playtime.h"
// add combine rank unify_ctr feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_unify_ctr.h"
// add combine rank unify_cvr feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_unify_cvr.h"
// add combine context rank play3s feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_context_rank_play3s.h"
// add combine context rank ntr feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_context_rank_ntr.h"
// add combine context rank playtime_ad feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_context_rank_playtime_ad.h"
// add combine context rank server_show_ctr feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_context_rank_server_show_ctr.h"
// add combine context rank server_show_cvr feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_context_rank_server_show_cvr.h"
// add context dense user thanos emp statis feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_user_thanos_emp_statis.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_detail_context_pattern_type.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_id_unlogin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_gift_game_tag_online_picasso.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_nebula_reco_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_nebula_reco_comment.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_industry_impression_click_realtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_detail_universe_context_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_upload_material.h"

// pcreative select model
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pcreative_age.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pcreative_cost.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pcreative_impression.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pcreative_impression_cost_11.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pcreative_impression_cost_12.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pcreative_impression_cost_13.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pcreative_impression_cost_14.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pcreative_impression_cost_15.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pcreative_impression_cost_16.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pcreative_impression_cost_17.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pcreative_description.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pcreative_description_bert.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pcreative_photo_impr_cost.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pcreative_photo_audio_emb.h"

// add user multi touch back feat
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_multi_touch_back_product_name.h"

//// PHOTO
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dup_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dedup_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_author_no_author_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_region_from_ip.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_explore_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_explore_stat_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_upload_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_from_client.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_border.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_filter.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_light.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_llsid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_rank_index.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_music.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_template.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_camera.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_is_ad.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_count.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_misc_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_category.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_width_height.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_new_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_persia_resnet_cover_embedding.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_show_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_label_delay_ts.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_click_label.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_user_click_industry_time_gap.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_sec_industry_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_industry_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_item_optimization.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_feed_user_group.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_combine_user_realtime_play5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_combine_user_realtime_playend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_topic.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_follow_topic.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_like_topic.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dl_cluster.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dl_feature_embedding.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_deep_conv_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_creative_signature.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_mmu_hetu_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_photo_mmu_hetu_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_photo_mmu_hetu_tag_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_mmu_quality_score.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_mmu_quality_score_embedding.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_cmt_hetu_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_neg_hetu_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_like_hetu_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_like_hetu_tag_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play_end_hetu_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_hetu_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_hetu_tag_l1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_hetu_tag_l2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_hetu_tag_l3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_ecomm_hetu_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_ecomm_hetu_tag_l1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_ecomm_hetu_tag_l2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_ecomm_hetu_tag_l3.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_search_feature_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_emb_query1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_emb_query2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_emb_query3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_emb_query4.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_emb_query5.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_emb_query6.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_order_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_order_stat_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_order_stat_ftr.h"  // 不要用在 ctr 预估

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_unit_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_campaign_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_campaign_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_ad_campaign_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_account_id.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_account_id_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_app_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_app_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_app_category.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_app_developer_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_app_package_size.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_app_score.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_base_uri.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_uri_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_base_uri_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_create_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_creative_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_universe_new_creative.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_detail_ads_union_special.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_is_smart_matching.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_smart_matching_purchase_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_photo_bid_price.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_new_fanstop_photo_bid_price.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_photo_bid_type_ocpx_action_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_new_fanstop_photo_bid_type_ocpx_action_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_unit_avg_conv2retention.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rank_cpm_div_bid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rank_cpm_div_bid_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_live_bid_price.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_live_bid_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_dense_has_yello_trolley.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_mmu_item_attr.h"

// BRAND
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_level.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_device_price.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_device_os.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_device_model.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_device_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_device_network.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_brand_account_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_brand_account_30d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_brand_match_account_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_brand_match_account_30d.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_ad_campaign_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_account_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_dup_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_creative_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_ad_delivery_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_industry_group.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_unit_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_duration_bucket.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_creative_stat.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_ad_new_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_ad_campaign_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_text_feature_bert_cluster.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_text_feature_bert_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_video_feature_moco_cluster.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_video_feature_moco_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_vision_feature_cover_ctr.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_combine_user_item_impression_industry_realtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_combine_user_realtime_ad_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_photo_bid_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_user_click_action_nebula.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_advertiser_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_base_uri_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_photo_app_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_base_uri.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_ad_uri.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_dup_cover_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_combine_account_media_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_ad_uri_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_combine_user_app_list_product.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_photo_20wspeech_ec_idxs.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_user_combine_channel.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_combine_user_app_list_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_combine_user_click_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_combine_user_click_ad.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_combine_user_id_dup_cover_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_photo_dsp_embedding.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_combine_user_click_match_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_posid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_posidv2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_unit_type.h"

// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_order_info.h"
// //没有提升
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_title.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_title.h"

// 没有提升

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_gender_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_year_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_region_from_gps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_mmu_classification_143.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_norm_all_click_topic.h"
// photo fanstop wtr embedding
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_fanstop_embedding_wtr.h"

// photo universal emb
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_photo_universal_emb.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_creative_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_unit_campaign_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_creative_id_dense.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_ad_component_assembly_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_ad_component_info.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_ad_component_sticker.h"

// photo material creator
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_material_tag_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_material_tag_gender.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_material_tag_cat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_material_tag_benefit.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_material_tag_style.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_material_tag_age.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_material_tag_actor.h"

// ad creative text info
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_actionbar.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_description_seg.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_username_seg.h"

// 兼容程序化创意
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_v2_dup_cover_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_virtual_creative_sticker_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_virtual_creative_title_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_virtual_creative_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_virtual_creative_cover_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_virtual_creative_creative_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_color.h"  // TODO(txc)
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_sound.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_explore_stat_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_is_filter_by_feedback.h"
// #include
// "ks/ad/ad_algorithm/feature/fast/impl/extract_photo_mmu_classification_623.h"
// #include
// "ks/ad/ad_algorithm/feature/fast/impl/extract_photo_mmu_cluster_2w.h"

// adxinfo
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_adx_info.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_adx_title.h"

//
//// COMBINE
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_region.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_poi_author_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_distance.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_follow_author_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_like_author_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_level_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_level_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_level_photo_region_from_ip.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_attribute_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_attribute_dup_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_device_applist.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_device_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_device_dup_photo.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_block_ad.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_click_ad.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_like_ad.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_negative_ad.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_report_ad.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_impression_ad.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_download_ad.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_click_author_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_photo_id_user_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_photo_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_lda_photo_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_negative_author_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_negative_photo_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_author_sim_action_match_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_ecomm_sim_action_match_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_author_sim_action_match_cnt_fix.h"


#endif

void register_group_1() {}
