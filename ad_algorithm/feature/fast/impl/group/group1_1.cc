#include "teams/ad/ad_algorithm/feature/fast/impl/group/group1_1.h"

#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_author_user_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_poi_photo_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_author_attr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_cluser_photo_cluster.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_photo_id_user_topic.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_photo_topic.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_topic_photo_topic.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_no_prefix.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_click.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_p3s.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_allconv.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_others.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_inc.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_inc_cut.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_inc_cut_new.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_inc_cut_opt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_normalized_attribute_score_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_realtime_action_list_sim.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_realtime_action_list_sim_picasso.h"

// reco gsu sim feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sim_gsu_feature.h"

// game tag sim.
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_game_tag_sim.h"

// sdk feature.
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sdk_action_feature.h"

// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_click_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_click_author_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_click_product_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_cpxr_author_no_click_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_cpxr_product_no_click_seq.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_target_author_id_share.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_target_author_id_share1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_target_product_name_share.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_target_product_name_share1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top1_action_list_sim_inc_cut.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top2_action_list_sim_inc_cut.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top3_action_list_sim_inc_cut.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top4_action_list_sim_inc_cut.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top5_action_list_sim_inc_cut.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top6_action_list_sim_inc_cut.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top1_action_list_sim_inc_cut_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top2_action_list_sim_inc_cut_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top3_action_list_sim_inc_cut_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top4_action_list_sim_inc_cut_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top5_action_list_sim_inc_cut_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top6_action_list_sim_inc_cut_v2.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_current_distance.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_region_gps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_photo_gender.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_photo_year.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_photo_agegender.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_gender_year_region.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_photo_id_item_cf.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_topic_photo_norm_all_topic.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_mmu_classification_143_ad.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_gender_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_age_segment_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_days_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_phone_price_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_consumption_level_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_datetime_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_click_industry_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_click_industry_ad_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_click_industry_time_gap.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_industry_clicked.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_negative_industry_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_negative_industry_ad_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_games_label1_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_games_label2_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_item_click_product_name_ad_product_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_download_start_product_name_ad_product_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_conversion_product_name_ad_product_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_lgbm_all_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_lgbm_prediction.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_lgbm_prediction_rf.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_retrieval_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_retrieval_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_embedding_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_test_sparse_data.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_test_sparse_data.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_test_category_data.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_test_category_data.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_test_dense_data.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_test_dense_data.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_finance_state_online.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_feature2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_embedding_feature2.h"

// #include
// "ks/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_feature_test.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_photo_dl_feature_embedding.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_dl_feature_embedding.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_dl_feature_embedding.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_is_follow_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_is_follow_live_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_is_follow_detail_live_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_callback_event.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_callback_event128.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_callback_event_train.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_callback_event_train_all.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sm_dense_train_callback_event.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sm_dense_infer_callback_event.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_union_dense_callback_event_train.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_union_dense_callback_event_infer.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_union_target_labels.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_callback_event_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_callback_event.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_flow_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_label_match_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_label_match_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_callback_event128_lsp2appoint.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_app_list_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_browsed_ad_and_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_interest_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_audience_and_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_device_photo_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_app_list_product.h"

// adx id 类特征 暂时没有效果 平均每个 id 曝光 1.5 次  不足以训练
// #include
// "ks/ad/ad_algorithm/feature/fast/impl/extract_combine_user_device_adx_info.h"
// #include
// "ks/ad/ad_algorithm/feature/fast/impl/extract_combine_click_adx_info.h"
// #include
// "ks/ad/ad_algorithm/feature/fast/impl/extract_combine_follow_adx_info.h"
// #include
// "ks/ad/ad_algorithm/feature/fast/impl/extract_combine_like_adx_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_apps_games_label.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_view_like_photo_label.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dsp_embedding.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dsp_embedding.h"

// 添加粉条模型图片 embedding 特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_photo_embedding_ctr.h"

// 测试 user unfollow
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_unfollow.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_attribute_photo_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_author_attr_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_level_photo_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_level_dup_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_download_installed.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_width_height_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_attribute_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_device_info_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_deviceid_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_dislike_topic.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top_short_topic.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_hate_topic.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_p3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_embedding_p3s.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_view_photo_tags.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_view_fanstop_id.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_view_tags.h"
// #include
// "ks/ad/ad_algorithm/feature/fast/impl/extract_user_device_info_kcard.h"
// test  -----   nearby_fanstop_live
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_userlevel_liveauthor.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_userlevel_live.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_cover.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_video_quality.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_userid_livecoverurl.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_clicklist_followlive.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_clicklist_live.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_clickliveauthorlist_hot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_clickliveauthorlist_near.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_followlist_live.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_followlist_liveauthor.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_forwardlist_live.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_forwardlist_liveauthor.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_giftlist_live.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_giftlist_liveauthor.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_likelist_live.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_likelist_liveauthor.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_mmu_class_623.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_mmu_cluster_2w.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_mmu_cluster_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_mmu_global_cluster_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_mmu_class623.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_mmu_cluster2w.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_frame_last_mmu_clase_623.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_high_quality_author_list.h"

// test ----- reco_live_ctr_embedding
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_reco_live_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_reco_emp_xtr_dis.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_his_stat_dis.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_embedding_reco_live_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_reco_live_wtr.h"

// test ----- conversion_user_embedding
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_conversion.h"
// user fanstop wtr embedding
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_embedding_wtr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_ad_follow_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_ad_like_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_ad_lda.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_photo_id_user_ad_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_photo_ad_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_lda_photo_ad_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_topic_photo_ad_topic.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_negative_photo_ad_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_author_user_ad_lda.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_null_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_photo_id.h"
// detail
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_detail_context_author_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_detail_context_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_detail_pic_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_detail_pic_shape.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_impression_noclick.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_impression_noclick_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_impression_avegap.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_avegap.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top3_impression.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top3_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_last_click_mingap.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_last_click_mingap_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_last_impression_mingap.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_last_impression_mingap_new.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_click_bow_3d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_click_bow_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_click_bow_3d_words.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_click_bow_7d_words.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_photo_impression_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_item_impression_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_photo_click_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_item_click_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_new_detail_page_click_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_photo_like_list.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_landing_jd_brand_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_landing_jd_cate1_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_landing_jd_cate2_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_landing_jd_cate3_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_landing_tb_brand_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_landing_tb_cate1_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_landing_tb_cate2_list.h"

// Long term MMU feature
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_lt_mmu_class623.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_lt_mmu_emb128.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_app_bert_emb.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_buy_intents.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_app_bert_emb.h"

// Material Feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_vision_feature_cover_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_visual_emb_vision_feature_cover_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_vision_feature_cover_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_vision_feature_cover_ctr_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_vision_feature_cover_ctr_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_text_feature_bert_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_text_feature_bert_cluster.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_text_feature_bert_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_text_feature_bert_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_text_feature_bert_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_text_feature_bert_click_feed.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_text_feature_bert_conv_feed.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_text_feature_bert_imp_feed.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_landingpage_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_video_feature_moco_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_video_feature_moco_cluster.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_video_feature_moco_click_thanos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_video_feature_moco_conv_thanos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_video_feature_moco_imp_thanos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_reco_mat_embedding.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_click_author_mat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_click_mat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_follow_mat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_like_mat.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_adx_bow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_adx_bow_words.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_adx_user_sourceType.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_adx_device_sourceType.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_adx_soureType.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_adx_mmu_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_adx_embedding.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_bclick_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_interest_imp_keywords.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_interest_click_keywords.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_interest_click2_keywords.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ss_interest_keywords.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ss_interest_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_adx_keywords.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_interest_categorys.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_zy_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_zy_fine_info.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_click2_second_category_30d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_imp_second_category_30d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_imp_second_category_14d.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_bow_7d_words.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_bow_3d_words.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_bow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cover_star_cluster.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cover_mmu_cluster.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cover_mmu_embedding.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_adx_user_creative_7d_bow.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_latlon.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_latlon2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_latlon4.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_user_distance_tenkm.h"

//用户时序信息
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_hour.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_diff_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_weekhour_ad_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_weektime_ad_id.h"


// 用户广告历史行为
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_impression.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_photo_comment.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_photo_follow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_download_completed.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_download_installed.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conversion.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_nextday_stay.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_week_stay.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_zhx_item_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_zhx_item_user_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_zhx_item_price.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_zhx_item_source_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_zhx_item_cate_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_zhx_live_author_gender.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_zhx_live_author_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_zhx_live_like_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_zhx_live_online_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_zhx_live_stream_id.h"

// live action detail
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_follow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_follow_no_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_played_1m.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_played_1m_no_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_played_3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_played_3s_no_author.h"

// live
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_mmu_class_623_all.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_attribute_live.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_device_live.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_click_author_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_played3s_author_id.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_combine_merchant_watched_author_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_combine_merchant_followed_author_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_combine_merchant_to_detail_author_list.h"

// author related
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_retarget_same_auhor.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_retarget_same_auhor_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_retarget_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_retarget_author_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_same_author_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_same_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_only_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_advertiser_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_photo_ad_component_cover_mmu.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_product_name_item_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_product_name_download_started.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_product_name_conversion.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_installed_app.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_browse_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_product_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_bid_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_bid_type.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_device_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_product_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_embedding_product_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_device_info_imei.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_device_info_noip.h"


// live 0521
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_behav_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_watch_tag_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_mmu_tag_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_watch_tag_live_mmu_tag_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_merchant_list_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_merchant_list_online_author_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_merchant_list_online_item.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_impression_avegap.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_impression_avegap_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_impression_noclick.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_impression_noclick_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_creative_type.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_behav_stat_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_weekly_behav_author_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_to_detail_merchant_author_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_to_detail_merchant_author_list_no_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_watched_merchant_author_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_watched_merchant_author_list_no_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_followed_merchant_author_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_followed_merchant_author_list_no_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_to_detail_merchant_item_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_buy_price_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_buy_cat_new.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_datetime_hour_week.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_datetime_hour_week_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_photo_vcreative_cover.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_cate_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_online_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_price.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_comment_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_creative_style_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_flash_duration.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_ks_map_cate_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_library_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_mark_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_product_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_product_labels.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_shop_keeper_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_sold_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_spu_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_age.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_gender.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_province.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dpa_city.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_advertiser_product_name.h"

// 用户不同周期的行为序列特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_industry_id_deep_action_30d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_industry_id_deep_action_90d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_industry_id_shallow_action_3d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_industry_id_shallow_action_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_industry_id_shallow_action_30d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_photo_id_deep_action_30d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_photo_id_deep_action_90d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_photo_id_shallow_action_3d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_photo_id_shallow_action_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_photo_id_shallow_action_30d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_shallow_act_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_product_name_deep_action_30d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_product_name_deep_action_90d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_product_name_shallow_action_3d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_product_name_shallow_action_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtl_product_name_shallow_action_30d.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_request_times.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_build_times.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_build_time2.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_shallow_action_industry_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_deep_action_industry_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_shallow_action_product_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_deep_action_product_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_label.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_purchase_amount.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_nebula_reco_play3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_extend_info.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_gift_game_tag_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_choose_tag_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_watch_photo_tag_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_watch_live_tag_online.h"

// NEW ADD FEATURE
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_dense_num_30d_dis.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_num_dis.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_ad_item_click_num_dis.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_dense_num_30d.h"

// dsp count service feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_cs_count_data.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_cs_count_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_cs_count_data_dis_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_tag_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dmp_game_product.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_like_tags.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_like_tags_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_caption_segment.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_type_count_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_adx_count_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_small_shop_title_words.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_label_online.h"

#endif

void register_group_1_1() {}
