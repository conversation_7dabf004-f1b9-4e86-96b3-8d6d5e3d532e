#include "teams/ad/ad_algorithm/feature/fast/impl/group/group4_2.h"
#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

// USER MULTI-ACTION
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_1d_cat2_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_1d_cat3_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_15d_cat2_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_15d_cat3_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_buy_1d_cat2_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_buy_1d_cat3_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_buy_30d_cat2_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_buy_30d_cat3_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_1d_cat2_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_1d_cat3_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_10d_cat2_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_10d_cat3_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play0s_1d_pvalue_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play7s_1d_pvalue_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_1d_pvalue_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play0s_1d_first_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play0s_1d_second_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play0s_1d_third_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play7s_1d_first_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play7s_1d_second_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play7s_1d_third_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_1d_first_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_1d_second_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_1d_third_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play0s_7d_second_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play0s_7d_third_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play7s_7d_second_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play7s_7d_third_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_7d_first_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_7d_second_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_7d_third_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_15d_second_cat_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_15d_third_cat_list.h"

// USER MULTI-ACTION MULTI-SCALE
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_buy_1d_cat2_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_buy_30d_cat2_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_1d_cat2_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_15d_cat2_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play7s_1d_pvalue_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_1d_pvalue_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play0s_1d_second_cat_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play0s_1d_third_cat_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play7s_1d_second_cat_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play7s_1d_third_cat_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_1d_second_cat_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_1d_third_cat_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play0s_7d_second_cat_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play0s_7d_third_cat_list_scale.h"

// ALL-DOMAIN USER ACTION
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_like_6m_cat3_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_like_6m_food_pid_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_coll_6m_cat2_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_coll_6m_merch_pid_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_comm_6m_cat3_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_comm_6m_bea_pid_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_rew_6m_cat3_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_rew_6m_cloth_pid_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_buy_item_attr_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_click_item_attr_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_click_1d_cat2_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_click_5d_cat3_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_live_1d_cat2_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_live_1d_cat3_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_live_10d_cat2_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_live_10d_cat3_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_market_cate_1d_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_market_cate_7d_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_market_kw_1d_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_market_kw_7d_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_play_1d_brand_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_play_7d_brand_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_commerce_play_15d_brand_list.h"

// 在线延迟建模特征字段
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_delay_pctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_delay_pcvr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_delay_delivery_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_delay_conversion_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_delay_p90_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_delay_label.h"

// 新粉条直播 ECPM 新增特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_live_bid_type_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_live_yellow_trolley_new.h"

// 直播开播时间间隔
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_start_time_gap.h"
// UNIVERSE_AD_ACT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_unit_ad_act_category.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_unit_ad_act_threshold.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_unit_ad_act_times.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_unit_ad_act_gamepackage.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_unit_ad_act_category_times.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_unit_ad_act_threshold_times.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_unit_ad_act_category_threshold.h"
// Repair_univ_fea
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_ad_style_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_app_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_cooperation_mode_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_game_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_industry_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_pos_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_uid_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_media_cooperation_mode_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_media_industry_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_element_type_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_item_click_type_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_conv_medium_industry_ctr_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_conv_medium_industry_cvr_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_conv_medium_industry_dr_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_conv_medium_sub_industry_ctr_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_conv_medium_sub_industry_cvr_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_conv_medium_sub_industry_dr_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_ad_style_sub_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_unit_ad_act_threshold_adstyle.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_unit_ad_act_category_adstyle.h"

// universe feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_p_author_avg_roi_ratio_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_p_author_avg_cpa_bid_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_p_author_post_ctr_7d.h"

// AKG Feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_akg_user_graph_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_akg_user_int_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_akg_user_simi_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_akg_user_side_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_akg_user_tag_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_akg_photo_graph_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_akg_user_ann_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_akg_user_hweight_rec.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_akg_user_int_seq_v1.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_augment_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_id_augment_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_new_industry_v3_dense_outside.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_first_industry_v3_query_item_dense.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_first_industry_v3_query_user_dense.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_first_industry_v3_query_user_onehot_dense.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_first_industry_v3_query_item_onehot_dense.h"

// user_rank_stat
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_stat_action_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_stat_first_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_first_industry_v3_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_action_type_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_action_type_dense_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_predict_stat.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_like_cat28_entertainment_photo_list_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_like_cat28_service_photo_list_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_like_cat28_stationery_photo_list_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_like_cat28_game_photo_list_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_collect_cat28_entertainment_photo_list_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_collect_cat28_service_photo_list_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_collect_game_photo_list_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_comment_cat28_entertainment_photo_list_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_comment_cat28_service_photo_list_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_comment_cat28_stationery_photo_list_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_comment_game_photo_list_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_search_query_game_cate_1d_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_search_query_game_ip_cate_3d_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_search_query_gamecate_7d_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_search_query_game_ip_cate_7d_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_search_query_game_cate_15d_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_search_query_game_ip_cate_15d_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_search_query_game_cate_30d_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_play_7s_game_cate_1d_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_play_end_game_cate_1d_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_play_game_cate_7d_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_play_game_cate_15d_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_collect_financial_photo_list_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_comment_financial_photo_list_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_search_financial_query_cate2_taglist_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_search_financial_query_cate3_taglist_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_comment_financial_cate3_taglist_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_comment_financial_cate2_taglist_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_pageid_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_rewarded_pageid_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_user_ad_action_list_sim_for_reco_match.h"

// ad live item realtime feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_embedding_template.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_list_value_template.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_list_value_template_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_list_value_seq_template.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_value_template.h"

// union ad style dense one-hot
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_universe_ad_style_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_action_match_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_match_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_mmu_item_click_match_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_mmu_p5s_match_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_mmu_ped_match_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_direct_ecom_click2_match_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_direct_ecom_lps_match_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_live_user_fans_flag_sub_page_id.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_ecom_action_industry_v2.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_category_can.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_new_industry_can.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_advertiser_info_can.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_global_stat_can.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_mmu_hetu_tag_can.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_mmu_item_attr_can.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_play_stat_can.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_title_can.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_request_times_can.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_context_info_can.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_datetime_can.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_device_info_ad_user_can.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_click_industry_can.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_last_item_impression_gap_can.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_new_context_info_can.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_stay_stastical_cxr_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_stay_stastical_cxr_num_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_datetime_week.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_product_name_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_order_info_new_flow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_is_connected_live_new_flow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_order_stat_new_flow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_order_stat_ctr_new_flow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_order_stat_ftr_new_flow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_slot_new_flow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_new_flow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_photo_mmu_hetu_tag_new_flow.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_realtime_new_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_realtime_new_extend_ict3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_click_realtime_new_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_merchant_follow_realtime_new_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_order_info_new_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_order_stat_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_advertiser_info_replace.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_app_list_product_replace.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_server_no_item_click_flag_fix_v1_prelace.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_scene.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_high_light.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_special_item.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_special_spu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_detail_item.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_detail_spu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_yellowtrolley_item.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_yellowtrolley_spu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_volume_price.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_volume.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_price.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_face_attribute.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_face_ids.h"
// mix context info
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_ad_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_reco_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_item_combine_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_unify_cvr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_unify_cvr_sparse.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_product_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_first_industry_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_product_name_match.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_first_industry_id_match.h"

#endif

void register_group_4_2() {}
