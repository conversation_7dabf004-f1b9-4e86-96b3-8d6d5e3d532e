#include "teams/ad/ad_algorithm/feature/fast/impl/group/group4_1.h"
#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

// new comp eds feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_click2_pn_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_click2_mmu1_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_click2_mmu2_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_click2_mmu3_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_pe_pn_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_pe_mmu1_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_pe_mmu2_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_pe_mmu3_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_iv_pn_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_iv_mmu1_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_iv_mmu2_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_iv_mmu3_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_cv_pn_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_cv_mmu1_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_cv_mmu2_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_cv_mmu3_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_click2_app_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_pe_app_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_iv_app_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_cv_app_with_time.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_form_submit_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_detail_form_submit_fix.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_jinniu_xiaodian_product_id_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_jinniu_xiaodian_product_id_v2_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reco_user_click_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reco_user_click_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_user_extend_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_live_goods_impression_seller_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_live_p3s_live_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_live_p3s_seller_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_live_standard_live_room_live_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_live_standard_live_room_seller_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_live_yellow_cart_click_live_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_live_yellow_cart_click_seller_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_live_yellow_cart_show_live_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_live_yellow_cart_show_seller_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_native_live_goods_view_cate3_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_native_live_yellow_carts_click_seller_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_native_photo_goods_view_cate3_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_native_photo_goods_view_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_native_photo_item_impression_photo_id.h"

// offline user fanstop pdn seq extractor
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_user_pdn_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_user_pdn_seq_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_serving_pdn_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_room_author_with_cart_item_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_id_list_with_length.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_click_item_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_buy_item_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_click_item_seq.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_fanstop_live_author_fans_count.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_live_user_fans_flag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_live_user_fans_flag_dense.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_fans_flag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_fans_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p2l_clickitem_authorid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p2l_clickitem_authortag3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p2l_clickitem_authortag4.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p2l_purchase_authorid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p2l_purchase_authortag3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p2l_purchase_authortag4.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_photo_product_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_photo_second_industry_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_product_click_share.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_second_industry_click_share.h"

// 区分是否开屏流量
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rtb_flag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_rtb_flag.h"

// mmu photo embedding
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_ecom_mmu_dense.h"

// dmp feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_hetu_second.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_hetu_second.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_hetu_third.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_search_second_score.h"

// rewarded cas landing_page_submit_rate
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_lps_Q_off_train.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_lps_Q_bucket_off_train.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_lps_pred_val_bucket_infer.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_media_caimai_industry.h"

// order type 特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_ecom_order_type_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_ecom_cate2_7d_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_ecom_cate2_7d_seq_num.h"

// dense age && gender
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_gender_n_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_age_dense.h"
// face && face_two
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_live_face_two_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_live_face_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_photo_face_fix.h"
// combine(seq, target)
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_click_photo_product_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_click_photo_second_industry_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_playend_photo_product_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_playend_photo_second_industry_sequence.h"

// 用户购买 / 浏览商品序列 tag 特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_buy_merchant_tag_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_browse_merchant_tag_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_buy_merchant_emb_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_browse_merchant_emb_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reward_raw_cpm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_live_merchant_iamge_emb.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_request_times_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rt_browse_type_1.h"

// 数据采买 安装列表 emb
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_applist_pr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_applist_td.h"

// reco seq for fanstop retrieval
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_reco_like_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_reco_like_seq_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_reco_like_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_reco_like_num_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_reco_like_mask.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_reco_seq_sparse.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_reco_seq_sparse_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_reco_seq_mask.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_reco_seq_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_reco_click_live_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_reco_click_live_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_action_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_action_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_seq_sparse.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_seq_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_id_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_top28_seq_sparse.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_top28_seq_sparse_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_top28_seq_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_seq_item_hash_id.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_last_first_screen_ad_time_stamp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_last_pv_time_stamp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_last_pv_page_size.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_last_pv_ad_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_gsu_v1.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_long_term_ecom_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_long_term_ecom_cate1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_long_term_ecom_cate2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_long_term_ecom_cate3.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_ecom_and_other_lps_item_type.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play_ecom_photo_v1_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play_ecom_cate1_v1_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play_ecom_cate2_v1_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play_ecom_cate3_v1_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play_ecom_photo_v1_seq_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play_ecom_cate1_v1_seq_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play_ecom_cate2_v1_seq_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play_ecom_cate3_v1_seq_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reward_distbt_coin_no_ad_cnt_sd_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reward_distbt_coin_watch_photo_cnt_sd_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_buy_category_tag_sum.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_buy_brand_tag_sum.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_buy_entity_tag_sum.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_buy_tag_sum.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_browse_category_tag_sum.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_browse_brand_tag_sum.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_browse_entity_tag_sum.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_browse_tag_sum.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_new_industry_v3_dense_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_click2_mmu1_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_click2_pn_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eds_comp_realtime_click2_time_sequence_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_prodtct_name_ug_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_cart_item_cate3_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_cart_item_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_native_live_click_cate3_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_native_live_click_item_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_native_live_click_seller_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_install_app_flag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_install_pdd_flag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_retention_product_1d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_retention_product_3d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_retention_product_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_retention_product_14d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_retention_media_id_product.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_retention_media_id_product_is_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_ocpx_action_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_ocpx_action_type_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_ecom_and_other_lps_item_ocpx_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_selling_goods_embedding_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_merchant_behavior_intention_cate_level_one.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_merchant_behavior_intention_cate_level_two.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_has_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_is_soft.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_merchant_brand_tag_one.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_merchant_brand_tag_two.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_merchant_entity_tag_one.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_merchant_entity_tag_two.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_weekday.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_predict_scvr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_like_impr_count_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_like_click_count_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_like_photo_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_like_author_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_like_duration_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_real_show_impr_count_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_real_show_click_count_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_real_show_photo_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_real_show_author_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_real_show_duration_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_click_live_impr_count_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_click_live_click_count_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_click_live_photo_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_click_live_author_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_click_live_duration_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_promotion_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_quality_filter_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_quality_visit_freq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_quality_ecpm_thres.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_quality_ecpm_thres_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_buy_category_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_cate.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_next_stay_vacation.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_age_segment_vacation.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_gender_vacation.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_holiday_vacation.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_product_name_vacation.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_medium_vacation.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_label_elapsed_hour.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_age_segment_vacation_holiday.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_gender_vacation_holiday.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_product_name_vacation_holiday.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_medium_vacation_holiday.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_user_ad_action_list_sim_for_reco.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_nxh_flow_type.h"

// AD-ALL MERCHANT USER ACTION
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_merchant_cate1_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_merchant_cate2_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_merchant_cate3_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_merchant_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_merchant_industry_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_merchant_product_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_merchant_cate1_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_merchant_cate2_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_merchant_cate3_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_merchant_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_merchant_industry_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_merchant_product_name.h"

// AD-ALL MERCHANT USER ACTION V2
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_merchant_cate1_id_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_merchant_cate2_id_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_merchant_cate3_id_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_merchant_photo_id_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_merchant_industry_id_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_merchant_product_name_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_merchant_cate1_id_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_merchant_cate2_id_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_merchant_cate3_id_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_merchant_photo_id_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_merchant_industry_id_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_merchant_product_name_v2.h"

#endif

void register_group_4_1() {}
