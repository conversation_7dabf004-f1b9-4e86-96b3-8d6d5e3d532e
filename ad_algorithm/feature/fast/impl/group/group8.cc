#include "teams/ad/ad_algorithm/feature/fast/impl/group/group8.h"
#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_dsp_last_gap_live_played.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_ad_splash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_fans_top_last_gap_live_played.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_play_lt1s_last_gap.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_play_lt1s_stat_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_play_lt1s_time_stamp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_action_play_lt1s_match_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_stat_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_time_stamp.h"
// sp
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_action_sp_list.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_action_sp_match_cnt.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_extend_action_list_match_stat_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_extend_action_list_stat_dense.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_invoke_product_name_dense.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_match.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_colossus_filtered_by_playtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_longterm_item_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_sdpa_product_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_with_action_and_id_given_len.h"
// kai no hash
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_installed_app_newhash_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_download_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_download_new_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_datetime_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_installed_app_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_device_info_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_refresh_direction_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_datetime_hour_week_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_device_info_ad_user_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conversion_extend_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_short_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_short_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_short_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_short_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conversion_extend_short_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_short_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_like_new_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_id_list_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_click_item_id_list_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_click_cart_seller_id_list_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_click_card_seller_id_list_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo_buy_item_seller_id_list_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_behav_online_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_merchant_list_online_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_followed_merchant_author_list_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_to_detail_merchant_author_list_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_weekly_behav_author_list_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_ecom_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_ecom_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_deviceid_new_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_like_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_attribute_new_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_device_info_new_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_author_nohash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_only_author_nohash.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_action_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_action_ts_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_action_len.h"

// debug
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_debug_info_llsid_cid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_first_industry_v3_dense_onehot.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_queue_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_goods_gsu_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_goods_gsu_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_goods_gsu_swing.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_goods_gsu_for_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_goods_gsu_tag_prior.h"

// ITEM PHOTO/LIVE AUTHOR
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_common_enhance_author_fusion.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_common_author_industry_level.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_common_author_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_common_author_id.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_user_v5_time_sort.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_user_buy270d_time_sort_Item_cat3_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_commerce_user_click180d_time_sort_Item_cat3_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dense_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_seq_app_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_seq_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_seq_product_id_hash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_seq_second_industry_id_hash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_id_dense_v3.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_fans_valued_label.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_valued_followed_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_info_fans_range.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_rank_cpm_info_v2.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_next_stay_day_state.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_next_stay_minute_of_day.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_next_stay_bn_state_dense.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_audience_count_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_edg_graph_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_product_name_id_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reward_page_id_sparse.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ocpc_action_type_encode_dense.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_ocpc_action_type_encode_dense_new.h"

// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_play_time.h"
// live start time gap
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_start_time_gap_plain_dis_src2.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_start_time_gap_plain_dis_n_aid_src2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_lsp_segment_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_lsp_segment_info_v2.h"

// creative uplift
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_creative_uplift_label_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_creative_id.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_outer_inner_feature_float.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_outer_inner_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_photo_item_global_gsu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_univ_rank_cxr_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_invoke_product_name_each_dense.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_invoke_product_name_each_dense_v2.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_invoke_scene_flag.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_conv_scene_flag.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_newlands_click_top_emb.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_newlands_click_tail_emb.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_invoke_product_name_each_dense_v3.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_traffic_boost_dense.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_plc_biz_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_is_follow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_is_follow_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top_action_list_sim_inc_cut_v4.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top_action_list_sim_inc_cut_v2.h"

void register_group_8() {}

#endif
