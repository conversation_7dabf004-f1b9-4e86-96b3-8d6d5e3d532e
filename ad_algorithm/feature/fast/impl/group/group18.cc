
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group18.h"
#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_prm_leads_num_segment.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_wechat_num_segment.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_order_sku_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_order_gmv_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_rec_item_g_v_fir.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_rec_item_g_v_sec.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_rec_item_g_v_thr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_rec_sid_fir.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_rec_sid_sec.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_rec_sid_thr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_is_new_product_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_product_stage.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_is_new_creative_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_creative_stage.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_is_new_sdpa_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_sdpa_stage.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_is_new_corp_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_valid_message_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_llm_dpsk_valid_user.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_llm_exp_1_user.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_photo_llm_dpsk_photo_top_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_inner_cid_top_account.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_item_live_top_layer_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_live_action_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_product_name_discount_ratio.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mini_game_fix_roi_ratio.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_msg_clk_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_msg_clk_type_x_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_msg_clk_type_x_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_msg_cot.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_regular_place_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_home_place_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_work_place_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_residence_place_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_query_quant_seq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_uplift_ctr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_mean_15m_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_mean_30m_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_std_30m_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_rate_30m_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_yellow_car_avg_price_v_2_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_aigc_live_order_author_seq_30d_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_explore_sample_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_use_main_ctr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_p_2l_audience_elastic_c_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_p_2l_audience_elastic_c_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_msg_chat_stay_time.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_recl_2_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_recl_2l_3_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_recl_3_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_live_action_real_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mixup_dense_photo_label_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_profile_label.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_account_maa_cost_level_90d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_account_is_first_cost_in_cur_m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_account_is_first_cost_in_cur_q.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_ctr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_etr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_lvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_in_etr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_in_lvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_ltr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_wtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_htr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_inner_trigger_photo_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_inner_trigger_page.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_inner_trigger_position.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sku_price_top_20_list_rm_dup.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_pos_imp_rate_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_imp_rate_5pp_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_imp_rate_10pp_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_imp_rate_15pp_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_cpm_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_cpm_rate_5pp_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_cpm_rate_10pp_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_cpm_rate_15pp_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inspire_search_query.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_dense_live_cvr_onemodel_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_sparse_live_cvr_onemodel_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_callback_item_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_callback_event_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_visitor_lp_leave_time_7_day.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sku_price_top_20_list_rm_dup_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_dense_abtest_param_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_sparse_abtest_param_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_query_to_ad_product_aggr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_action_semantic_id_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_action_semantic_id_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_action_semantic_id_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_action_semantic_id_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_whether_ks_or_fake_user.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_p_2l_audience_elastic_d_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_p_2l_audience_elastic_d_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_playcard_sequence_share_combine.cc" // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_qcpx_dense_live_cvr_onemodel_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_qcpx_sparse_live_cvr_onemodel_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_low_cpm_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_cpm_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_cot_matched_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_search_order_hc.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_valid_action_semantic_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_valid_action_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_action_sequence_semantic_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_action_sequence_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_search_pos_v_1.cc"   // NOLINT

void register_group_18() {}

#endif
