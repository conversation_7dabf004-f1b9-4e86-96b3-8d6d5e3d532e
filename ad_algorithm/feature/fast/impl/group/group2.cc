#include "teams/ad/ad_algorithm/feature/fast/impl/group/group2.h"

#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

// zhanfen tag & ad action list extend
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_imp_zhanfen_tag_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_clk_zhanfen_tag_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_lik_zhanfen_tag_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reyun_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_new_install_app_all.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_new_install_app_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_new_install_app_old.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_adx_ocpc_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_app_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click1_no_play3s_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click1_no_click2_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click2_no_conv_extend.h"


// insurance
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_insurance_action_product.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_credit_profile_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_insurance_third_tag_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_attribute_insurance_tag_online.h"

// nebula action extend
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_nebula_ad_play3s_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_nebula_ad_play5s_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_nebula_ad_playend_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_nebula_ad_click_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_nebula_ad_item_click_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_action_nebula.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_action_nebula_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_itemclick_action_nebula.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_action_nebula.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play3s_action_nebula.h"

// detail ad action list extend
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_detail_item_click_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_detail_item_impression_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_detail_context_photo_info.h"

// detail pre-photo feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_detail_ads_resource_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_product_name_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_detail_context_photo_mmu_class_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_detail_context_photo_mmu_game_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_detail_context_photo_strategy_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_detail_context_photo_mmu_text_cluster.h"

// detail photo_id related feature wrapper
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_photo_id_user_lda_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_download_installed_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_filter_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_music_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_width_height_new_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_level_photo_new_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_attribute_photo_new_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_device_photo_new_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_click_ad_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_impression_ad_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_embedding_feature2_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_embedding_p3s_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_holidays.h"

// item_impression related feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_item_impression_ad_realtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_item_impression_industry_ad_industry_realtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_item_impression_industry_time_gap_realtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_impression_avegap_realtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_last_item_impression_mingap_realtime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top3_item_impression_realtime.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_num_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_name_bow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_embedding_node2vec.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_top_neighbours.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/etract_photo_unit_stat.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_product_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_product_name_played3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_product_name_played5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_product_name_playedend.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_abtest_hash_id.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_ad_app_id_media.h"
// reco
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_action_ext.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_action_click_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_action_search_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_action_upload_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_follow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_action_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_cluster.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_multi_play.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_embedding_multi_play.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_play_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_embedding_play_time.h"


// universe
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_dense_getui_data_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_sparse_getui_data_extend.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_position_status.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_universe_flow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_ad_style.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_cooperation_mode.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_medium_attribute.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_medium_uid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_app_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_pos_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_id_ab.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_id_ac.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_medium_industry_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_medium_sub_industry_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_medium_game_category_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_style.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_cooperation_mode.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_attribute.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_uid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_app_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_industry_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_sub_industry_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_game_category_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_creative_style_material_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_creative_style_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_creative_resource_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_creative_template_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_creative_material_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_creative_playcard_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_creative_endcard_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_creative_video_pic.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_creative_hori_vert.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_creative_rule_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_item_dynamic_style_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_dynamic_style_ann_recall_key.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_target_hash.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_conv_medium_industry_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_conv_medium_industry_cvr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_conv_medium_industry_dr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_conv_medium_sub_industry_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_conv_medium_sub_industry_cvr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_conv_medium_sub_industry_dr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_industry_stats_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_stats_all.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_universe_creative_stat_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_universe_unit_stat_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_universe_account_stat_online.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_universe_campaign_stat_online.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_apps_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_available_disk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_total_disk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_available_disk_percent.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_available_memory.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_total_memory.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_available_memory_percent.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_cpu_count.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_baterry_percent.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_active_apps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_wide_context_feature_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_wide_item_feature_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_rerank_pos.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_uid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_app.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_ad_style.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_cooperation_mode.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_media_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_media_uid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_media_ad_style.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_media_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_media_cooperation_mode.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_media_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lps_account_stats_15d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_lps_account_stats_30d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lps_user_stats_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lps_user_stats_15d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lps_user_stats_30d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_lps_user_stats_15d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_lps_user_stats_30d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_lps_user_stats_60d.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_age_ad_style.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_age_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_age_product.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_gender_ad_style.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_gender_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_gender_product.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_pos_id_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_ad_style_sub.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_element_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_item_click_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_app_installed_cate.h"
//
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rta_accepted_tag.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_dup_cover_id.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_level_loc.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_click_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_click_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_follow_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_like_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_like_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_follow_photo.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cpa_bid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cpa_bid_fix_roas.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cpa_bid_fix_roas2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cpa_bid_fix_roas3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_deep_conversion_bid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_roi_reciprocal.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_scvr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_dup_cover_id.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_nocnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_nocnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_nocnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_nocnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_nocnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_attr_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_adx_cvr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_adx_account_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_adx_campaign_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_adx_second_category.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_stat_dense.h"

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_download_ad_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_download_new1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_unitAction_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_week_stay_new.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play3s_action_nebula_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_action_nebula_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_lgbm_all_feature_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_fake_info.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_id_hashtwo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_id_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_like_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_follow_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_negative_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_deviceid_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_imporession_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_device_info_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_like_author_id_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_education_action.h"

// 作品推广小店商品信息
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_item_category_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_item_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_item_price.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_buy_cat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_buy_price.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_buy_source.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sjh_p3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sjh_30_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sjh_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sjh_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sjh_follow.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_buy_cat_item.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_buy_cat_industy.h"
// 利用 item_extend 提取特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inner_purchase_period.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_click_industry.h"

// 定向相关特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_target_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_target_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_auto_cpa_bid_feed.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_auto_cpa_bid_nebula.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_auto_cpa_bid_union.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_target.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_embedding_target.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_target_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_embedding_target_v2.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_app_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_c1_retention_new_pay_stat_3days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_c1_retention_new_pay_stat_7days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_c2_retention_new_pay_stat_3days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_c2_retention_new_pay_stat_7days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_rt_browse_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_ocpc_action_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_shallow_conv_ocpc_action_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_smart_matching_thres_feed.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_smart_matching_thres_nebula.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_smart_matching_thres_feed2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_smart_matching_thres_nebula2.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_universe_flag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_universe_flag_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_debug_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_test_bert_cluster.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_width_height_new_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_category_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_advertiser_info_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_new_industry_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_caption_segment_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cover_star_cluster_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_medium_attribute_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_ad_style_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_cooperation_mode_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_universe_flow_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_medium_uid_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_pos_id_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_app_id_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_account_id_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_ad_lda_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_bid_type_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_deep_conv_type_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_template_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_app_id_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_dup_cover_id_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dup_photo_id_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_virtual_creative_sticker_id_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_virtual_creative_title_id_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_media_uid_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_medium_industry_id_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_medium_sub_industry_id_3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_medium_game_category_id_3t.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_info1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_embedding_uembed.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_more_info_for_pred.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_more_info_for_pred_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_more_info_for_pred_3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dmp_id_fea.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_event_type_product_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_info.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_age_sex_city_device_region.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_reco_user_realtime_action_and_creative.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_reco_user_realtime_action_and_mmu_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longtermaction_and_industryid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_reco_user_realtime_action_and_product_id.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_id.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_recall_target_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_live_goods_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_recall_target_info_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_recall_target_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_scene.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_action_product_name_online.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_llsid_dense_info.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_rank_cpm_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_rank_ctcvr_info.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_20wconv_ec_kwd_idxs.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_20wspeech_ec_kwd_idxs.h"
// 京快相关特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_jk_samples_type.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_paid_ids.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_industry_stats_merge.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_appcontextproduct.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_uidcontextproduct.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_subcontextproduct.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_appcontextproduct_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_uidcontextproduct_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_subcontextproduct_fix.h"

// 平台电商相关特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_diff_app_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_past_app_list.h"

// extend 特征补全
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conversion_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_purchase_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_jinjian_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_credit_extend.h"

// short extend 特征补全
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_purchase_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_credit_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conversion_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_jinjian_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conversion_extend_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_product_name_lps_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_product_name_conversion_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_product_name_item_click_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_purchase_extend_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_slot.h"

// ecom extend 特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_ecom.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_ecom_kuai_category_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_poster_rate.h"

// edu extend 特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_edu.h"

// 小贷相关特征，覆盖率低，用来筛选安装 714 高利贷和赌博类 app 用户
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_714_app_installed_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_gambling_app_installed_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_crm_product_type.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_uri.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_ecommerce_product_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_ecommerce_product_id.h"

// fanstop feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_fanstop_userinfo_and_photoinfo.h"


// 负反馈特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_server_no_item_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_played5s_no_item_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_pay.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_server_no_item_click_flag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_played5s_no_item_click_flag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_cnt.h"

// click larger photo id and author id
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_larger_author_photo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_has_yello_trolley.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_has_yello_trolley_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_has_yello_trolley.h"

// 电商 profile
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_click_item_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo_click_card_seller_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo_click_cart_seller_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo_buy_item_seller_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_click_cart_seller_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_click_card_seller_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_click_item_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_buy_item_id_list.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_device_industry_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_imei_industry_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_author_exp_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_author_exp_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_music_global_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_orig_music_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_global_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_negative_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_play_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_short_play_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_thanos_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dnn_cluster_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_thanos_play_rate_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_level_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_level_author_stat.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_exp_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_follow_author_id_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_click_author_id_longterm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_and_itemclick.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_and_play3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_and_playend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_play5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_replayed.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_combine_merchant_followed_author_list_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_combine_merchant_to_detail_author_list_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_combine_merchant_watched_author_list_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_and_lps.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_is_connected_live.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_is_connected_live_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_author_is_living.h"

// reco 用户小店行为 list 特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_cate1_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_cate2_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_cate3_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_channel_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_seller_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_source_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_click_item_cate1_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_click_item_cate2_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_click_item_cate3_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_click_item_seller_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_click_item_source_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_ctr_test.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_interactive_form.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_interactive_form.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_live_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_hit_follow_count_limit.h"

// 直营电商用户行为特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_click_cate_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_click_product_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_lps_cate_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_lps_product_list.h"
//
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_extend_data_zxl.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_callback_event_sparse.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ocpc_action_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_callback_event_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_unit_bid_sparse.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_shallow_label_target_union.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_creative_tag_dense.h"

// 搜索电商关键词特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_query_keywordcnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_target_info.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_first_cate_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_item_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_kuai_cate_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_old_first_cate_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_old_second_cate_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_pastday_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_price_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_second_cate_list.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_inner_delivery.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_delayed_time.h"

// 用户 30 天内有过转化行为的行业和 product
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_callback_action_product_feature_jinjian.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_callback_action_product_feature_credit.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_callback_action_industry_feature_jinjian.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_callback_action_industry_feature_credit.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_ecom_fix.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_week_gmv_dist.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_first_cate_with_passed_day.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_second_cate_with_passed_day.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_old_first_cate_with_passed_day.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_old_second_cate_with_passed_day.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_first_cate_rfm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_second_cate_rfm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_old_first_cate_rfm.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_order_old_second_cate_rfm.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_ecom_with_time.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_bid_bucket.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_week_gmv_max.h"

#endif

void register_group_2() {}
