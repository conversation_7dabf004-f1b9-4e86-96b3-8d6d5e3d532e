#include "teams/ad/ad_algorithm/feature/fast/impl/group/group6.h"
#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_action_timestamp_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_action_list_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_context_dense_pred_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_action_match_cnt_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_play_lt1s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_action_play_lt1s_match.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_longterm_item_v2_tp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_seq_item_hash_id_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_goods_graphsim.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_goods_gsu_picasso_count.h"

void register_group_6() {}

#endif
