#include "teams/ad/ad_algorithm/feature/fast/impl/group/group2_1.h"

#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

// no prefix with slot 和序列特征抽取类示例
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_follow_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_follow_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_follow_large_slot.h"

// zd add 1013
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_no_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_ecom_short_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_ecom_short_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_ecom_short_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_played3s_no_item_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_played3s_no_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_played5s_no_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_played_fix5s_no_item_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playedend_no_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_author_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_follow_num.h"
// creative cat tag
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_creative_support_cat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_creative_support_tag.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_num_extend_ecom_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_num_extend_ecom_dense.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_label_dense_info.h"

// 粉条人工标注类目特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_author_level_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_tag_category.h"

// 小店 long short 类型特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_cate1_id_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_cate2_id_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_click_cart_seller_id_list_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_click_item_id_list_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_buy_item_id_list_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_id_list_short.h"

// 三方电商长期行为 list 特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_third_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_third_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_third_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_third_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_third_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_third_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_third_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_order_submit_extend.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_comp_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conversion_extend_comp_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_comp_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_comp_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_comp_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_photo_comment_comp_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_photo_comment_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_photo_follow_comp_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_photo_follow_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_comp_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_comp_ecom_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_comp_ecom_with_time.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_ecom_order_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_lps_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_played5s_no_lps_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playedend_no_lps_ecom.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_order_paied_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_ecom_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_order_paied_extend_ecom_days.h"

// 手机磁盘、电量、内存及真实产品名特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_device_battery_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_device_memory_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_device_disk_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_product_real_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_device_left_battery.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_device_left_memory.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_device_left_disk.h"

// 转化事件中真实的 callback event
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_true_callback_event_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_conv_callback.h"

// 作品粉条订单侧信息，包含订单类型、内外部等
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_order_info_new.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_click_product_sequence_share.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_product_share.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_click_photo_sequence_share.h"
// extract_photo_id_share.h  using  extract_photo_id_slot.h
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_click_second_industry_sequence_share.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_industry_id_share.h"

// 品牌广告
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_account_id_rr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_primary_industry_id_rr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_secondary_industry_id_rr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_industry_group_id_rr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_app_start_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_corporation_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_datetime.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_brand_duration.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_author_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_global_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_negative_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_play_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_avegap_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_avegap_realtime_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_datetime_hour_week_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_imp_avegap_realtime_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_impression_avegap_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_impression_noclick_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top3_click_realtime_brand.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_top3_impression_realtime_brand.h"


// web parse features
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_domain_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_form_new1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_form_new3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_diff.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_11.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_12.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_13.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_21.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_22.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_23.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_31.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_32.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_33.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_41.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_42.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_43.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_51.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_52.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_53.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_61.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_62.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_lps_url_price_new_63.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_product_name.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_item_impression_industry_ad_industry_realtime_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_with_time_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_ecom_short_with_time_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_played5s_no_lps_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playedend_no_lps_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_business_interest_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_installed_app_newhash.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_view_like_photo_label_newhash.h"

// no prefix with slot 特征抽取类
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_follow_photo_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_follow_photo_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_like_new_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_negative_new_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_new_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_like_author_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_like_photo_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_click_author_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_click_photo_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_follow_author_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_nebula_follow_photo_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_author_new_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_photo_new_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_follow_photo_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_unfollow_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dsp_action_detail_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_id_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_sequence_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_imp_rt_pid_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_clk_rt_pid_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_time_diff_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_purchase_extend_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_purchase_extend_sequence_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_purchase_time_diff_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_ad_purchase_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conversion_extend_sequence_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_ad_conversion_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_sequence_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_ad_item_click_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_sequence_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_ad_lps_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_imp_rt_pid_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_clk_rt_pid_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_hetu_tag_extend_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_photo_extend_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_hetu_tag_extend_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_hetu_tag_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_hetu_tag_extend_seq.h"

// 小店通 ocpx_action_type 特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_ocpx_action_type_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_ocpx_action_type_sparse.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_xdt_ocpx_action_type_dense.h"

// 电商的商品文本和封面特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ecom_product_cover_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ecom_product_title_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_cover_feature_ctr_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_cover_feature_ctr_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_cover_feature_ctr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_text_feature_ctr_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_text_feature_ctr_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_text_feature_ctr.h"


// zed add realtime / longterm  photo cartesian
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_and_ad_info_itemclick.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_and_ad_info_playend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_and_ad_info_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_and_ad_info_play3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_and_ad_info_play5s.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info_play3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info_play5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info_click_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info_playend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info_negtive.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_ad_info_replayed.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_extend_dense_feature.h"

// 联盟排查 api 切换 sdk 后, cpm 更低,现排查方案为将两个新的 pos 进行替换,进行 ab 实验
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_wide_context_feature_dense_ab.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_wide_context_feature_dense_ac.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_new_context_info_ab.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_new_context_info_ac.h"

// 小店作者特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_seller_category_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_seller_click_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_seller_live_trolley_stat.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_seller_pay_cate3_id.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_universe_ad_style.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_datetime.h"

// 小店预估品类特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_xdt_item_prediction_cate1_meizhuan.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_xdt_item_prediction_cate2_meizhuan.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_xdt_item_prediction_cate3_meizhuan.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_xdt_item_prediction_cate1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_xdt_item_prediction_cate2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_xdt_item_prediction_cate3.h"

// fanstop live action feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_live_action_feature.h"

// fanstop photo action feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_fanstop_photo_action_feature.h"

// live realtime feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_audience_count.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_coin_count.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_coin_count_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_comment_count.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_comment_count_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_follow_count.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_gift_count.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_like_count.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_like_count_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_order_amount.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_order_count.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_order_count_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_report_count.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_share_count.h"

// Online Retail Info
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_online_retail_info_category.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_online_retail_info_item_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_online_retail_info_product_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_online_retail_info_spu_id.h"

// Reco User Action List
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reco_user_like_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reco_user_click_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_app_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_app_dense.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_fanstop_userinfo_and_photoinfo_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_id_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_disliketopic_photolda.h"

// 付费能力特征
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_pay_info.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_pay_info_dense.h"

// 教育金融落地页特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_education_class_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_education_class_info2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_education_class_info3.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_financial_product_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_game_play_method.h"
// 教育二级行业拆分广告特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_edu_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conv_extend_edu_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conv_extend_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conv_extend_edu_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_edu_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_edu_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_edu_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_conv_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_lps_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_pay_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_impression_no_click_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_impression_no_play3s_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_edu_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_edu_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_pay_extend_edu_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_pay_extend_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_pay_extend_edu_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_edu_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_edu_with_time.h"
// 金融二级行业拆分广告特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_fin_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conv_extend_fin_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conv_extend_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conv_extend_fin_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_fin_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_fin_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_fin_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_conv_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_lps_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_pay_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_impression_no_click_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_impression_no_play3s_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_fin_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_fin_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_pay_extend_fin_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_pay_extend_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_pay_extend_fin_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_fin_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_fin_with_time.h"

// 资讯类二级行业拆分广告特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_info_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conv_extend_info_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conv_extend_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conv_extend_info_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_info_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_info_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_info_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_conv_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_lps_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_pay_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_impression_no_click_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_impression_no_play3s_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_info_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_info_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_pay_extend_info_days.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_pay_extend_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_pay_extend_info_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_info_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_info_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_info.h"
// 游戏类二级行业拆分广告特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_purchase_extend_short_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conversion_extend_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_purchase_extend_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play3s_extend_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_download_installed_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_conversion_extend_short_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_download_completed_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_photo_comment_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_photo_follow_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_short_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_game.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_short_game.h"

// 喜欢、评论、分享特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_comment_extend_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_comment_extend_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_comment_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_comment_no_item_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_comment_no_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_comment_no_pay.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_like_extend_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_like_extend_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_like_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_like_no_item_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_like_no_lps.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_like_no_pay.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_played3s_no_comment.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_played3s_no_like.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_played3s_no_share.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_share_extend_edu.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_share_extend_fin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_share_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_share_no_item_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_share_no_pay.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_share_no_lps.h"

// 特征当 label 传入
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_act_label.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_device_realtime_os.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_device_realtime_network.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_lps_cate_list_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_click_item_cate3_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo_click_cart_seller_id_list_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_seller_id_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo_buy_item_seller_id_list_short.h"

// 联盟用户行为统计特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_click_industry_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_click_industry_d30.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_click_product_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_click_product_d30.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_conversion_industry_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_conversion_industry_d30.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_conversion_product_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_conversion_product_d30.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_deep_conversion_industry_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_deep_conversion_industry_d30.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_deep_conversion_product_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_deep_conversion_product_d30.h"

// 联盟 item 侧统计特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_account_conv_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_material_feature_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_is_playable.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_contextinfo.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_iteminfo.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_account_conv_medium.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_account_deep_conv_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_account_deep_conv_medium.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_account_target_cpm_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_account_target_cpm_medium.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_product_conv_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_product_conv_medium.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_product_deep_conv_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_product_deep_conv_medium.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_product_target_cpm_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_product_target_cpm_medium.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_photo_conv_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_photo_conv_medium.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_photo_deep_conv_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_photo_deep_conv_medium.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_photo_target_cpm_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_photo_target_cpm_medium.h"

// 联盟探索特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_is_explore.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_context_posid_cluster.h"

// 联盟媒体侧统计特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_uid_target_cost_account_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_uid_target_cost_product_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_uid_target_cost_sec_industry_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_uid_target_cost_unit_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_uid_target_cpm_account_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_uid_target_cpm_product_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_uid_target_cpm_sec_industry_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_uid_target_cpm_unit_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_id_target_cost_account_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_id_target_cost_product_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_id_target_cost_sec_industry_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_id_target_cost_unit_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_id_target_cpm_account_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_id_target_cpm_product_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_id_target_cpm_sec_industry_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_id_target_cpm_unit_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_creative_material_dense_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_medium_first_industry_dense_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_creative_material_dense_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_first_industry_dense_d7.h"


// 联盟媒体侧特征
// 原始数据:游戏 游戏(类型) 休闲益智 答题 免费 一起来答题 ********* com.together.answer https://apps.bytesfield.c
// hash 之后: *********
// -*********,*********,-*********,*********,-*********,-*********,-**********,**********
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_app_category_app_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_app_category_download_link.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_app_category_first_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_app_category_online_property.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_app_category_package_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_app_category_purchase_property.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_app_category_second_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_app_category_third_class.h"

// 联盟画像 app
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_effect_conversion_dense_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_effect_submit_dense_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_effect_purchase_dense_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_effect_roi_dense_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_age_sex_distribution_dense_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_action_uv_dense_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_label_uv_dense_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_effect_conversion_cpm_pos_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_effect_submit_cpm_pos_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_effect_purchase_cpm_pos_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_effect_roi_cpm_pos_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_action_uv_conversion_cpm_pos_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_action_uv_submit_cpm_pos_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_action_uv_purchase_cpm_pos_d7.h"
// 联盟画像 V2
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_user_price_level_distribution_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_ad_render_type_distribution_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_interests_label_query_uv_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_interests_label_response_uv_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_action_uv_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_activate_applist_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_install_applist_d7.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_flow_profile_apps_pos_install_applist_d7.h"

// fanstop user click_to_live seq feature
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_to_live_author_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_to_live_photo_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_to_live_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_to_live_author_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_to_live_photo_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo2live_author_have_shop_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo2live_author_id_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo2live_author_lv1_id_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo2live_author_lv2_id_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo2live_author_lv3_id_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo2live_author_sub2_tag_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo2live_author_sub3_tag_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo2live_author_sub4_tag_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo2live_photo_htag0_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo2live_photo_htag1_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo2live_photo_id_seq.h"

// smb 相关特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_simple_product.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_simple_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_simple_product.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_simple_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_simple_industry.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_smb_with_time.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_industry_group.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lpc_extend_with_time_real.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_smb_with_time.h"

// itemcf 相关特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_itemcf_mock_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_itemcf_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_itemcf_product_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_itemcf_industry_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_itemcf_sec_industry_name.h"

// 金牛用户行为特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_click2_itemids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_click2_cate1ids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_click2_cate2ids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_click2_cate3ids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_click2_productIds.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_click2_spuids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_click2_sellerids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_lps_temids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_lps_cate1ids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_lps_cate2Ids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_lps_cate3ids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_lps_productids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_lps_spuids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_lps_sellerids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecomad_action_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_online_retail_info_item_id_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_online_retail_info_spu_id_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_online_retail_info_category_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_online_retail_info_product_id_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_direct_creative_cluster.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_seller_category_id_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_seller_pay_cate3_id_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_seller_click_stat_2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_seller_live_trolley_stat_2.h"

// 三方电商唤端特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_no_invoke.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_no_invoke.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playedend_no_invoke.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_app_invoke_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_invoke_extend_days.h"

#endif

void register_group_2_1() {}
