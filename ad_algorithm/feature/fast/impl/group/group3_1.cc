#include "teams/ad/ad_algorithm/feature/fast/impl/group/group3_1.h"

#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

//小说属性特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_novel_plot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_novel_role.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_novel_theme.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_novel_author.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_novel_name.h"
// 行为意向类特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_creative_behavior_intention_category_backtrace.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_creative_behavior_intention_category.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_behavior_intention_category_ad_degree_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_behavior_intention_category_ad_degree_180d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_behavior_intention_category_app_degree_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_behavior_intention_category_app_degree_180d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_behavior_intention_category_app_install.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_behavior_intention_keyword_ad_degree_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_creative_behavior_intention_keyword.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_behavior_intention_keyword_ad_degree_180d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_behavior_intention_keyword_app_degree_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_behavior_intention_keyword_app_degree_180d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_active_app_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_car_tag_list.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_media_sub_industry_id_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_media_industry_id_v2.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_large_new_author_slot.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_large_new_photo_slot.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_media_first_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_media_second_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_media_third_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_media_online_property.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_media_purchase_property.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_universe_material_tag.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_id_media_first_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_id_media_second_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_id_media_third_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_id_media_online_property.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_account_id_media_purchase_property.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_photo_id_media_first_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_photo_id_media_second_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_photo_id_media_third_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_photo_id_media_online_property.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_photo_id_media_purchase_property.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_name_media_first_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_name_media_second_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_name_media_third_class.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_name_media_online_property.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_product_name_media_purchase_property.h"

// 金牛商品最大/最小价格
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_ecom_max_min_price.h"
// 直播相关特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_live_info_play5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_and_live_info_replayed.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_and_live_info_itemclick.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_and_live_info_playend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_and_live_info_play3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_and_live_info_lps.h"

// 直播商品特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_stream_item_info.h"

//匹配特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_click_match_num_long.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_click_match_num.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_applist_cate_match_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_click_match_num_long_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_applist_cate_match_num_dense.h"

//用户活跃度
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_active_level_from_comminfo.h"



#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_action_nebula_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_region_from_ip_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_device_photo_new_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_attribute_photo_new_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_follow_author_id_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_bid_type_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_combine_channel_new.h"

//游戏类特征
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_dur_14d.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_dur_30d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_dur_7d.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_duration_14d.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_duration_14d_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_duration_1d.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_duration_1d_v1.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_duration_30d.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_duration_30d_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_dur.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_dur_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_view_14d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_view_30d.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_view_30d_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_view_7d.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_avg_view_7d_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_complete_14d.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_complete_14d_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_complete_1d.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_complete_1d_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_conplete_30d.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_conplete_30d_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_game_aud.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_game_fullcsr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_game_live_cmt.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_game_live_cmt_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_game_live_share_1d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_game_photo_num.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_game_photo_num_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_game_streamer.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_live_14d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_live_30d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_live_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_nongame_live_share.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_photo_num.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_photo_num_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_ungame_7d.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_ungame_7d_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_ungame_live_cmt.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_ungame_live_cmt_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_unvideo_share_7d.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_unvideo_share_7d_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_valid_game_live_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_valid_live_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_video_duration_14d.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_video_duration_7d.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_video_share_1d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_video_share_7d.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_consume_video_share_7d_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_game_wide_gamecenter_live.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_live_author_fans.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_author_fans.h"

// new cross feature of user
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_seller_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_seller_pe.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_seller_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_seller_invoke.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_photo_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_photo_pe.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_photo_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_photo_invoke.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_creative_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_creative_pe.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_creative_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_creative_invoke.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_product_name_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_product_name_pe.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_product_name_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_eds_comp_product_name_invoke.h"
// new match feature of user
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_cnt_pe.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_longterm_action_cnt_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eds_comp_seller_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eds_comp_photo_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eds_comp_product_name_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_eds_comp_creative_cnt.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_reserve_field.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reserve_field.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_reserve_field.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_item_impr_7d_dense.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_rewarded_flag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_merchant_price.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_live_author_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_live_photo_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_photo_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_author_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_duration_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_click_count_sequence.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_impression_count_sequence.h"
// 联盟交叉特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_age_media_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_gender_media_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_gender_media_uid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_app_list_media_app.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_app_list_media_pos.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_app_list_media_uid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_app_list_media_ad_style.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_app_list_media_industry.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_app_list_sequence_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_click_product_sequence_num.h"

//多触点
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_multi_touch_user_extend.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_cnt_ts_order_pay.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_cnt_ts_item_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_item_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_item_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_cnt_ts_ped.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_eds_direct_item_click_list_ts.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_list_ts_ped.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_list_ts_order_pay.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_list_ts_item_imp.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_p5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_p5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_order_pay.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_p3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_ped.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_form_submit.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_item_clk.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_ped.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_list_ts_p3s.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_invoke_extend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_invoke_extend_short.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ad_click2_no_invoke_extend.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_statis_click.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_statis_click_ratio.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_statis_conversion.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_statis_conversion_ratio.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_statis_cost.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_statis_impression.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_statis_play3s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_statis_play3s_ratio.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_statis_play5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_statis_play5s_ratio.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_statis_playend.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_statis_playend_conv_ratio.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_statis_playend_ratio.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_fake_feature.h"

// ug roi
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ug_dense_photo_roi_ltv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ug_photo_real_product_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ug_combine_product_media_wangzhuan.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ug_combine_product_media_uid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ug_combine_product_pos_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ug_combine_product_ad_style.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ug_combine_account_media_uid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ug_combine_account_pos_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ug_combine_account_ad_style.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ug_user_label_type.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ug_user_label_type_mock.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_day_of_week.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_day_hour_of_week.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_conversion_rewarded.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_nextday_stay_rewarded.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_download_rewarded.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_download_complete_rewarded.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_impression_rewarded.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_click_rewarded.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_download_install_rewarded.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_rewarded.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_str_pred_val_train.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_str_pred_val_infer.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_conv_pred_val_train.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_conv_pred_val_infer.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_lps_pred_val_infer.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_candidate_queue_size.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_is_show_conv_inspire.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_is_show_order_paied_inspire.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_last_time_coin_amount.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_view_count_di.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_combine_di_view_count_coin.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_combine_is_deep_inspire.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_combine_dense_wangzhuan_tag.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rewarded_combine_wangzhuan_tag.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_fans_count.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_follow_count.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_upload_count.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_caption_segment_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_subtitle_segment.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_dense_num_14d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_dense_num_14d_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_campaign_type_rewarded_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ocpc_action_type_rewarded_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_server_no_item_click_flag_no_uid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_server_no_item_click_flag_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_photo_author_fans.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_combine_merchant_followed_author_list_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_combine_merchant_to_detail_author_list_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_combine_merchant_watched_author_list_new.h"

// sim gsu

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_1t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_2t.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_4t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_5t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_6t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_7t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_only_author1t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_only_author2t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_only_author3t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_only_author4t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_only_author5t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_only_author6t.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_only_author7t.h"

// 综合电商 match 特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_invoke.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_invoke.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_list_ts_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_list_ts_invoke.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_cnt_ts_conv.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_shortterm_action_cnt_ts_invoke.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_p3s_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_p5s_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_ped_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_order_submit.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_item_imp_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_item_clk_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_eds_comp_item_click_list_ts.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_eds_comp_item_conv_list_ts.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_eds_comp_item_invoke_list_ts.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_eds_comp_item_ped_list_ts.h"

// 综合电商 match 特征修复
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_conv_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_invoke_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_p3s_v2_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_p5s_v2_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_ped_v2_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_order_submit_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_item_imp_v2_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_list_ts_item_clk_v2_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_conv_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_longterm_action_cnt_ts_invoke_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_eds_comp_item_click_list_ts_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_eds_comp_item_conv_list_ts_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_eds_comp_item_invoke_list_ts_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_eds_comp_item_ped_list_ts_fix.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reward_live_play_cnt_seven_day.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reward_jlrw_show_pv_cnt_seven_day.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reward_jlrw_task_click_pv_cnt_seven_day.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reward_money_pv_cnt_seven_day.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_reward_impression_cnt_seven_day.h"

// 粉条简易直播间用户特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_simple_to_standard_cnt_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_simple_to_standard_cnt_7d_1m.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_simple_to_standard_cnt_7d_5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_simple_to_standard_cnt_7d_10s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_simple_to_standard_rate_7d.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_simple_to_standard_rate_7d_1m.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_simple_to_standard_rate_7d_5s.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_simple_to_standard_rate_7d_10s.h"

// multi-scale 特征
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_click_item_cate1_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_click_item_cate2_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_click_item_cate3_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_click_item_seller_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_cate1_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_cate2_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_cate3_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_eshop_buy_item_seller_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_author_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_user_watched_merchant_author_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_goods_view_seller_id_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_paid_seller_id_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_click_item_id_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_follow_photo_id_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_reco_live_info_new.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_comp_ecom_photo_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_comp_ecom_product_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_comp_ecom_photo_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_comp_ecom_product_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_comp_ecom_photo_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play5s_extend_comp_ecom_product_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_comp_ecom_photo_id_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_comp_ecom_product_id_scale.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_comp_click2_photo_id_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_comp_click2_app_id_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_comp_click2_product_id_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_comp_playend_photo_id_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_comp_playend_app_id_list_scale.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_comp_playend_product_id_list_scale.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_user_comp_ad_click_extend_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_user_comp_ad_click_extend_product_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_user_comp_ad_click2_extend_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_user_comp_ad_click2_extend_product_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_user_comp_ad_play5s_extend_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_user_comp_ad_play5s_extend_product_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_user_comp_ad_playend_extend_photo_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cross_user_comp_ad_playend_extend_product_id.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_merchant_price_v2.h"

#endif

void register_group_3_1() {}
