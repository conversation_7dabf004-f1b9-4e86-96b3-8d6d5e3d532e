
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group16.h"
#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_7days_avg.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_7days_max.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_7days_min.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_7days_sum.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_7days_times.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_23days_avg.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_23days_max.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_23days_min.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_23days_sum.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_23days_times.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_60days_avg.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_60days_max.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_60days_min.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_60days_sum.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_recent_60days_times.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_pay_list_raw_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_inner_ecpm_raw_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_mix_rank_unify_cpm_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_mix_rank_unify_gpm_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_mix_rank_unify_ue_score_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_short_video_and_live_order_paid_model_rank_name_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_product_name_hash_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_huoke_real_clue_type_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_progress_extra_mission.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_progress_extra_reward.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_task_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_req_scene_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_daily_mission_limit.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_same_day_common_reward_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_same_day_again_reward_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_same_day_common_reward_amount.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_same_day_again_reward_amount.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_same_day_common_avg_reward.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_same_day_again_avg_reward.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_last_req_seconds_interval.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_last_req_scene_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_last_three_avg_reward.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_last_task_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_meta_item_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_meta_item_id_list_clip.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_meta_photo_id_list_sample.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_meta_author_id_list_sample.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_meta_account_id_list_sample.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_meta_item_id_list_sample.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_meta_label_embed_sample.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_meta_label_sample.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_merchant_stat_bin_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_bid_info_bin.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_author_gpm_for_mix_rank.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_clear_click_action_long_term_account_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_clear_click_action_long_term_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cover_mmu_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_search_data_source_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_stay_duration_buckets_tidanye.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_stay_duration_buckets_shangxiangye.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_stay_duration_buckets_tidanye.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_stay_duration_buckets_shangxiangye.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_playlet_theme.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_playlet_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_playlet_plot.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_playlet_channel.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_clear_click_action_one_day_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_clear_click_action_long_term_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_clear_click_action_one_day_package_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_clear_click_action_long_term_package_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_clear_click_action_long_term_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_clear_click_action_long_term_industry_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_clear_click_action_one_day_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_clear_click_action_one_day_account_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_clear_click_action_one_day_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_clear_click_action_one_day_industry_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_week.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_rq_vae_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_rq_vae_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_rq_vae_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_rq_vae_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_rq_vae_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_rq_vae_i_2_u.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_rq_vae_i_2_i.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rq_vae_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rq_vae_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rq_vae_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rq_vae_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rq_vae_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rq_vae_u_2_u.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rq_vae_u_2_i.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_search_photo_id_dpa_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_smart_offer_value_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_smart_offer_result_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_smart_offer_lessons_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_smart_offer_ms_coin_amount_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_smart_offer_one_lessons_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_mix_rank_last_pv_ad_max_score_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_mix_rank_last_pv_not_ad_max_score_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_llm_coldstart_new_item_id_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_llm_coldstart_new_item_id_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_llm_coldstart_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sharkv_2_content_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_sharkv_2_content_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_l_l_m_pdct_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_phone_plc_click_num_7d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_phone_plc_click_num_30d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_phone_call_num_30d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_phone_call_num_7d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_phone_plc_click_num_15d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_phone_call_num_15d_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_product_recall_with_user_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_recall_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sharkv_2_photo_2_item.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sharkv_2_item_2_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_learn_u_2_u.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_video_clip_u_2_u.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_coldstart_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_sub_context_product_coldstart_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_uid_context_product_coldstart_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_combine_app_context_product_coldstart_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_click_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_click_item_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ped_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ped_item_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_goods_shark_v_2_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_goods_baichuan_p_q_id_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_goods_baichuan_p_q_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_goods_baichuan_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_p_3s_cate_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_p_5s_cate_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_pend_cate_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_clk_cate_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lps_cate_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_p_3s_1_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_p_3s_2_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_p_3s_3_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_p_5s_1_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_p_5s_2_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_p_5s_3_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_pend_1_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_pend_2_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_pend_3_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_clk_1_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_clk_2_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_clk_3_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_lps_1_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_lps_2_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_lps_3_cate_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_p_3s_cate_match_ratio_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_p_5s_cate_match_ratio_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_pend_cate_match_ratio_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_clk_cate_match_ratio_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lps_cate_match_ratio_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_date_time_encoder_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_gender_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dense_cold_start_cost_total.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dense_cold_start_click_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dense_cold_start_lps_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dense_cold_start_creative_create_time.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_recall_embedding_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_product_recall_with_user_embedding_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_label_deep_event.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_query_hot_sequence_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_query_hot_sequence_photo_imp_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_query_hot_sequence_photo_clk_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_query_hot_sequence_photo_src_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_query_hot_sequence_photo_type_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_event_conversion_photo_id_rec_20.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_event_conversion_photo_id_rec_50.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dense_new_photo_conv.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_click_cnt_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_goods_cate_2nd_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_create_order_num_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_city_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_pay_order_amt_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_min_discount_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_max_original_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_cate_1_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_goods_cate_3rd_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_cate_3_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_cate_2_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_create_order_num_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_goods_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_min_original_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_create_order_num_3d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_click_cnt_3d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_click_cnt_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_poi_latitude.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_mac_purchase_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_province_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_poi_longtitude.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_order_amt_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_pay_order_num_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_goods_cate_1st_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_poi_pay_order_num_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_search_page_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_sku_max_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_sku_median_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_sku_min_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_long_ih_creative_uids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_long_ih_author_uids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_input_page_avg_item_time.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_input_session_seq_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_input_is_fresh_req.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_input_page_pos.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_input_ecom_photo_item_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_input_ecom_live_item_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_input_ecom_photo_hetu_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_input_ecom_live_hetu_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_input_ecom_photo_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_input_ecom_live_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_input_ecom_photo_first_industry_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_input_ecom_live_first_industry_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_input_ecom_photo_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_input_ecom_live_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_real_ih_author_uids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_query_hot_sequence_length_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_pay_lock_lesson_index_list_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_pay_lock_lesson_index_list_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_click_min_charge_list_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_pay_min_charge_list_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_click_min_charge_list_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_pay_min_charge_list_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_play_end_spu_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_item_click_spu_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_order_paied_spu_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_goods_view_spu_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_search_dsp_kbox.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_edu_deep_photo_lps_cnt_fix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_model_pred.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_real_ih_author_uids_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_real_ih_creative_uids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_mix_rank_native_quality_status_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_is_live.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_qcpx_coupon_cause_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_realtime_meta_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_realtime_meta_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_realtime_meta_account_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_realtime_meta_label_embed.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_realtime_meta_label.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_cart_item_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_collect_item_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_cart_item_list_ad.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_collect_item_list_ad.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_meta_rqcode_0_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_meta_rqcode_1_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_meta_rqcode_2_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_meta_rqcode_3_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_meta_rqcode_4_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_meta_rqcode_5_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_meta_rqcode_label_embed.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_meta_rqcode_label.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_credit_dense_domain_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_explore_trigger_hetu_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_qcpx_coupon_delivery.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_qcpx_coupon_thre_amt_comb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_inherit_list_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_photo_and_live_author_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_author_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_photo_and_live_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_user_id_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_ad_log_time.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_unclick_action_one_day_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_unclick_action_one_day_package_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_unclick_action_one_day_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_unclick_action_one_day_account_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_unclick_action_one_day_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_unclick_action_one_day_industry_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_unclick_action_longterm_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_unclick_action_longterm_package_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_unclick_action_longterm_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_unclick_action_longterm_account_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_unclick_action_longterm_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_unclick_action_longterm_industry_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inner_live_roas.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_pred_ad_roas_end.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_pred_ad_roas_front.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_pred_ad_roas_gmv_end.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_pred_ad_roas_gmv_front.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_pred_ad_roas_pay_end.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_pred_ad_roas_pay_front.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_dense_id_split.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inner_loop_unify_ltv.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_dense_id_split_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_goods_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_huoke_real_clue_type_dense_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_cost_total_segment.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_click_num_segment.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_lps_num_segment.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dense_cold_start_is_new_product_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dense_cold_start_is_new_creative_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dense_cold_start_is_new_corp_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_creative_id_last_14d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_creative_id_last_1d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_creative_id_last_30d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_14d_avg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_14d_avg_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_14d_max.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_14d_max_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_14d_min.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_times_last_14d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_14d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_14d_raw_list_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_1d_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_1d_count_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_1d_log_sum.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_1d_log_sum_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_1d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_1d_raw_list_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_30d_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_30d_count_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_30d_log_sum.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_30d_log_sum_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_30d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_30d_raw_list_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_product_name_last_14d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_product_name_last_1d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_product_name_last_30d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_times_creative_id_last_1d_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_times_last_14d_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_times_last_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_times_last_1d_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_times_last_30d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_times_last_30d_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_times_last_3d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_times_last_3d_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_times_last_7d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_times_last_7d_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_14d_min_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_14d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_14d_raw_list_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_14d_sum.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_14d_sum_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_1d_avg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_1d_avg_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_1d_max.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_1d_max_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_1d_min.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_1d_min_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_1d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_1d_raw_list_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_1d_sum.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_1d_sum_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_30d_avg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_30d_avg_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_30d_max.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_30d_max_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_30d_min.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_30d_min_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_30d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_30d_raw_list_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_30d_sum.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_30d_sum_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_3d_avg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_3d_avg_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_3d_max.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_3d_max_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_3d_min.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_3d_min_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_3d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_3d_raw_list_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_3d_sum.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_3d_sum_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_7d_avg.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_7d_avg_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_7d_max.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_7d_max_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_7d_min.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_7d_min_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_7d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_7d_raw_list_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_7d_sum.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_ltv_log_last_7d_sum_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_14d_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_14d_count_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_14d_log_sum.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_14d_log_sum_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_14d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_14d_raw_list_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_1d_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_1d_count_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_1d_log_sum.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_1d_log_sum_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_1d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_1d_raw_list_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_30d_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_30d_count_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_30d_log_sum.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_30d_log_sum_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_30d_raw_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_creative_id_last_30d_raw_list_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_14d_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_14d_count_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_14d_log_sum.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_game_dsp_realize_match_product_name_last_14d_log_sum_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_real_time_combine_order_paied_author_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_long_term_combine_order_paied_author_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_real_time_combine_goods_view_author_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_long_term_combine_goods_view_author_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_utype_smkv.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_ad_cpa_bid_fix_roas.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_target_cpm.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_inner_gpm_raw.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_same_day_common_view_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_same_day_again_view_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_same_day_deep_reward_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_same_day_deep_reward_amount.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_last_task_coin.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_last_ad_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_last_ad_cpm.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_same_day_task_coin_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_same_day_task_type_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_storewide_uplift_prob_with_ad.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_storewide_uplift_prob_without_ad.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_sparse_author_attraction_by_c_2_rank.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_sparse_author_attraction_by_c_3_rank.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_sparse_author_shop_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_sparse_author_score.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_id_no_prefix_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_creative_sparse_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_creative_sparse_id_no_prefix_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_author_id_no_prefix_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_photo_and_live_author_id_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_live_merge_ad_live_posid_sim_jump_click_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_live_merge_ad_live_posid_sim_played_mean_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_live_merge_ad_live_posid_sim_to_profile_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_posid_sim_order_paied_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_posid_sim_order_paied_item_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_posid_sim_goods_view_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_posid_sim_goods_view_item_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_posid_sim_goods_view_item_brand_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_posid_sim_goods_view_item_x_7_category_3_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_realtime_meta_product_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_realtime_meta_industry_id_v_3_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_live_merge_posid_combine_jump_click_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_live_merge_posid_combine_played_mean_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_live_merge_posid_combine_to_profile_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_order_paied_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_order_paied_item_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_goods_view_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_goods_view_item_brand_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_goods_view_item_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_goods_view_item_x_7_category_3_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_live_merge_posid_combine_jump_click_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_live_merge_posid_combine_played_mean_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_live_merge_posid_combine_to_profile_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_live_merge_posid_combine_live_click_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_live_merge_posid_combine_played_median_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_live_merge_posid_combine_live_like_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_live_merge_posid_combine_live_comment_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_live_merge_posid_combine_order_submit_click_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_order_impression_item_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_order_impression_item_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_order_submit_click_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_order_submit_click_item_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inner_live_pay_cnt_front.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inner_live_pay_cnt_end.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_industry_id_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_product_name_hash.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pctr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_plvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_psvr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pwatchtime_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pwtd_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pcpr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pltr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pwtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pftr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pcmtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_phtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pclick_live_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_p_effective_watch_live_time_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pptr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pepstr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_plstr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_petcm_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_frrank_uescore_pcmef_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_posid_sim_order_paied_distributor_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_order_submit_distributor_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_goods_view_user_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_order_impression_user_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_product_buy_click_user_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_real_time_merchant_posid_combine_shop_click_user_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_u_2_u_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_invoke_hack_label_mark.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_buyer_effective_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_request_freq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_creative_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_creative_sparse_id_no_prefix_new_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_id_no_prefix_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_id_no_prefix_new_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_id_author_id_no_prefix_new_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_photo_and_live_author_id_new_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_left_slide_stats.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_left_slide_gesture_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_realtime_meta_ecom_spu_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_ecom_spu_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_cid_qcpx_allow_dispense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_cid_qcpx_gear_position.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_cid_qcpx_is_account.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_unify_ctr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_action_author_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_author_action_merchant_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_action_target_merchant_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_author_sim_action_match_cnt_aid_32_fix.h"   // NOLINT

void register_group_16() {}

#endif
