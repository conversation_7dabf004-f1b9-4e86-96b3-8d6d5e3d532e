#include "teams/ad/ad_algorithm/feature/fast/impl/group/group11.h"

#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_follow_ocpx_action_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_single_product_price.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rl_pv_feature_ob1.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_wanhe_creator_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rl_pv_feature_ob2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_wanhe_creator_cross_section_first_class_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_wanhe_creator_first_category_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_wanhe_creator_cross_section_second_class_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_query_history_list_sequence.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ad_impression_extend_car.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_wanhe_creator_fans_user_num.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_client_volume.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_client_volume_cross.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_gesture_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_gesture_type_cross.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_universe_query_type_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_hd_ocpc_itemclick_full.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_hd_ocpc_p_3s_full.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_hd_ocpc_ped_full.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_kp_invoke_full.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_kp_itemclick_full.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_kp_p_3s_full.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_kp_ped_full.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_wanhe_creator_gender.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_wanhe_creator_fre_city_level.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_wanhe_creator_is_busi_author.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_wanhe_creator_is_community_good_author.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_wanhe_creator_is_op_author.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_wanhe_creator_photo_active_status.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_wanhe_creator_quality_first_key.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_wanhe_creator_second_category_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_wanhe_is_photo_mcn_user.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_account_id_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_account_id_noprefix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_bipartite_cluster_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_category_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_conversion_predict_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_conversion_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_cpg_buy_ratio.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_cpg_src_pair_ratio.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_cpg_src_ratio.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_cpg_tgt_pair_ratio.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_cpg_tgt_ratio.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_first_industry_v3_query_item_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_first_industry_v3_query_item_onehot_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_first_industry_v3_query_user_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_first_industry_v3_query_user_onehot_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_innerloop_item_inheritees.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_innerloop_item_inheritees_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_innerloop_item_inheritees_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_innerloop_item_inheritees_v3.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_innerloop_item_inheritees_v4.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_item_size.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_local_feature.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_mini_app_jump_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_mini_app_page_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_mmu_commerce_photo_goods_feature_float_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_new_industry_v3_rnd.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_order_poster_rate.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_recall_target_info_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_sdpa_photo_feature_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_traffic_boost_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_adsim_photo_info.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_info_4_label.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_info_4_label_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_info_4_label_new.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_realtime_gmv_max_bool_mask.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_realtime_gmv_max_bool_mask_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_realtime_gmv_min_bool_mask_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_realtime_gmv_p10_bool_mask.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_repeat_purchase.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_goods_view_carrier_id_cross.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_goods_view_merchant_attr_match_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_goods_view_tag_list_author_id_cross.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_goods_view_user_id_cross.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_inner_user_fans_tag.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_live_user_fans_Fix_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_live_user_fans_Fix_sparse.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_order_paied_3_category_prop_value_id_author_id_cross.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_order_paied_author_id_cross.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_order_paied_carrier_id_cross.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_order_paied_merchant_attr_match_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_order_paied_tag_list_author_id_cross.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_order_paied_user_id_cross.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_productname_token.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_query_productname_token_reverse.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_action_cross.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_action_match_cnt_live.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_action_sp_match_cnt.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_live_pay_labels_end.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_live_pay_labels_front.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_universe_query_type_product.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_inc_cut_nio.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_inc_cut_nio_opt.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_lps.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_no_filter.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_no_filter_ad_action.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_no_filter_align.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_no_filter_callback.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_no_filter_gmv.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_no_filter_meta.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_window_sample.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_age_product_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_age_second_industry.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_app_list_product_cold_start_lps.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_app_list_product_coldstart.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_attr_current_item_category_prop_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_attr_current_item_category_prop_value_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_attr_current_item_promise_delivery_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_attr_current_item_tag_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_attr_current_item_user_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_click_product_id_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_click_second_industry.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_click_second_industry_product_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_extend_info.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_industry_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_info_sdpa_info.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_interest_list_product_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_interest_list_second_industry.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_level_product_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_level_second_industry.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_life_stage_product_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_life_stage_second_industry.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_live_reservation_r3_flag.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_os_type_product_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_os_type_second_industry.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_phone_price_product_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_phone_price_second_industry.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_list_cross_aid.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_list_cross_aid_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_match.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_match_add.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_match_add_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_region_product_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_region_second_industry.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_account_ids.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_author_ids.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_auto_cpa_bids.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_campaign_ids.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_campaign_types.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_cpa_bids.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_creative_ids.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_creative_types.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_live_stream_ids.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_ocpx_action_types.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_photo_ids.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_prerank_ecpms.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_prerank_pctrs.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_prerank_pcvrs.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_unit_ids.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_conversion_secondindustry_name_each_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_creative_kol_user_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_creative_outer_loop_native.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_debug_info_llsid_cid_int.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_ad_queue_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_author_fans.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_realtime_ecom_action_match_cnt_v_2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_delivery.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_inspire_live_sub_page_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_is_author_followed.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_click.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_click_auc.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_impression.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_live_realtime_top_5_price.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_local_ocpx_action_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_merchant_item_put_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_promotion_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_mmu_cates.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_sdpa_ecom_pid_action_cnt.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_sdpa_list_cnt.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_sdpa_novel_info.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fake_conversion_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_follow_ocpc_action_type_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_follow_ocpc_action_type_dense_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_follow_ocpc_action_type_dense_four.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_gauss_dropout_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_i_purchase_action_timegap_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_i_realtime_purchase_action_timegap_list_by_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_merge_recall_context_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_v5_second_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_invoke_product_name_each_dense_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_invoke_product_name_each_dense_v3.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_author_gmv_bucket_count.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_author_gmv_bucket_dist.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_author_gmv_bucket_mask.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_click_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cpm.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cpm_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_creative_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_account_orient_selection.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_adcu_token.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_deepconvtype.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_ecom_campaign_flag.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_ecom_ocpx_action_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_gmv_round.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_industry.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_last_3d_author_pirce.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_lps_ocpx_action_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_mmu_ad_time_space_content_express_segment_end.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_mmu_ad_time_space_content_express_segment_start.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_mmu_ad_time_space_type_sit_drama_segment_end.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_mmu_ad_time_space_type_sit_drama_segment_start.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_photo_lps_p_90.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_tag_center_e_2_e_photo_embedding.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_univ_int_photo_emb.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_univ_int_photo_emb_exp.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_eshop_embedding.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_eshop_embedding_spu.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_eshop_embedding_v3.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_gmv_label.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_goods_basic_attr.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_goods_cnt_label.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_goods_each_attr.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_pc_profile.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_play_seconds_label.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_price_combine.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_price_combine_sku.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_repeat_purchase_label.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_shark_embedding.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_account_orient_user_age_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_account_orient_user_city_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_account_orient_user_gender_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_account_orient_user_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_account_orient_user_phone_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_account_orient_user_province_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_account_orient_user_register_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_author_live_avg_price_paycnt.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_live_highlight.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_live_id_dynamic.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_live_play_duration.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_llm_photo_keywords_no_prefix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_content_express_segment_10_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_content_express_segment_1_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_content_express_segment_2_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_content_express_segment_3_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_content_express_segment_4_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_content_express_segment_5_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_content_express_segment_6_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_content_express_segment_7_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_content_express_segment_8_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_content_express_segment_9_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_content_express_segment_last_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_first_5s_content_express_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_first_5s_detect_element_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_first_5s_visual_display_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_step_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_type_sitdrame_segment_1_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_type_sitdrame_segment_2_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_type_sitdrame_segment_3_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_type_sitdrame_segment_4_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_type_sitdrame_segment_5_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_type_sitdrame_segment_last_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_visual_display_segment_1_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_visual_display_segment_2_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_visual_display_segment_3_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_visual_display_segment_4_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_visual_display_segment_5_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_ad_time_space_visual_display_segment_last_seq_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_msta_visual_pattern_10_tag_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_msta_visual_pattern_1_tag_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_msta_visual_pattern_2_tag_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_msta_visual_pattern_3_tag_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_msta_visual_pattern_4_tag_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_msta_visual_pattern_5_tag_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_msta_visual_pattern_6_tag_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_msta_visual_pattern_7_tag_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_msta_visual_pattern_8_tag_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_mmu_msta_visual_pattern_9_tag_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_photo_lps_p_90_cvr.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_photo_lps_p_90_formtype.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_photo_lps_p_90_funcid.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_photo_lps_p_90_material.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_photo_lps_p_90_p_90.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_photo_lps_p_90_siteid.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_confront_mode_one.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_confront_mode_two.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_first_level_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_game_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_game_ip.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_game_play.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_game_screen_one.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_game_screen_three.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_game_screen_two.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_game_type_one.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_game_type_two.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_incentive.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_second_level_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_theme.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_prt_name_2_third_level_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_annual_vip_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_carrier_first.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_carrier_second.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_core_gameplay_first.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_core_gameplay_second.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_episode_duration.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_episodes.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_guild.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_max_charge.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_min_charge.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_paid_episodes.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_payment_threshold.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_profit_model_first.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_profit_model_second.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_recommend_charge.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_semiannual_vip_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_socializing_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_tag_center_unit_price.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_yellow_car_goods_merchant_product_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_yellow_car_goods_spu_cluster_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_yellow_car_goods_spu_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_yellow_car_goods_x_7_cate_1.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_yellow_car_goods_x_7_cate_2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_yellow_car_goods_x_7_cate_3.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_yellow_car_goods_x_7_cate_4.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_yellow_car_goods_x_7_cate_leaf_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_target_industry_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_label_match_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_latest_live_time_interval.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_tag_category_new_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_coin_count_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_comment_count_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_like_count_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_realtime_order_count_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_start_time_gap_plain_dis_n_aid_src2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_start_time_gap_plain_dis_n_aid_src2_dis_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_start_time_gap_plain_dis_src2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_union_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_activity_discount_hundreds.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_activity_discount_ones.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_activity_discount_tens.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_activity_discount_tenths.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_activity_price_hundreds.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_activity_price_ones.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_activity_price_tens.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_activity_price_tenths.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_list_merge.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_log_activity_discount.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_log_activity_price.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_log_price.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_price_hundreds.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_price_ones.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_price_tens.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_price_tenths.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_attr_single_merge.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_keyword.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_price.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_item_prop.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_dense_spuid.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_cluster_id_id_dark.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_dense_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_info_feature.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_info_feature_no_prefix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_spu_id_dark.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_spu_id_product_id_dark.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_x_7_cate_1_id_dark.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_x_7_cate_2_id_dark.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_x_7_cate_3_id_dark.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_x_7_cate_4_id_dark.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_x_7_cate_leaf_id_dark.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_x_7_cate_score_dark.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_live_goods_type_dark.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_user_interact_feature.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mixup_dense_callback_event.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mixup_dense_callback_event_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mixup_dense_ocpc_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_live_newlands_sparse_feat.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mmu_live_newlands_tag_idx_emb_feat.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_model_explore_tag.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_active.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_age.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_age_is_seller.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_city.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_city_level.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_class_1.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_class_2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_fans_num.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_gender.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_is_signed.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_live_90d.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_photo_14d.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_photo_30d.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_photo_7d.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_photo_90d.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_poi_photo_cnt.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_province.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_native_author_ver_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_next_stay_bn_state_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_next_stay_conv_day_state_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_next_stay_everyday_state.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ocpc_action_type_encode_dense_new.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ocpc_itemclick_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_ad_field_emb_e2e.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_ad_field_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_ad_mmu_newlands_tag_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_advertiser_info_coldstart.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_advertiser_info_coldstart_order.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_akg_hop5_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_akg_hop5_reverse_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cid_interest_emb.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_enhance_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_eshop_embedding_v3.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_for_pdct_ltr.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_x7_cate3.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_id_x7_level.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_keyword_id_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_md5.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_mmu_top28_cate1.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_mmu_top28_cate2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_mmu_top28_cate3.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_phdid.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_play_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_plc_biz_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_product_name_cold_start_lps.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_product_name_coldstart.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_product_name_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_product_name_tiny_game.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_realtime_purchase_action_list_by_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_relevanve_emb.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_second_industry_dense_268.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_shark_embedding.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_upmodel_first_industry_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_upmodel_idv3.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_upmodel_product_name.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_privileged_2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_privileged_2_auc.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_product_name_id_dense_new.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rank_ctcvr.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rank_ctcvr_scale.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rank_ctcvr_scale_fix.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rank_quota_cpm_list_inner_photo.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rank_quota_cpm_list_inner_soft_live.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rank_quota_with_top_cpm_outer.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_recall_product_name_embedding_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sdpa_cross_product_name_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sdpa_cross_product_name_dense_v2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sdpa_ecom_product_kwds.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sdpa_ecom_user_action_kwds.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_akg_query_embedding.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_kbox_type.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_parser_text_token_v1.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_parser_text_v1.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_pos_v_2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_qin_query_embedding.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_query_catgory_class2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_query_catgory_class3.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_query_combine_match_num.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_query_source.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_query_statistic.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_refer_photo_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_source.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_shop_item_detail_info_v1.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_combine_live_c_2_price_comm.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_combine_live_c_3_price_comm.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_combine_live_price_comm.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_combine_live_realtime_price_comm.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_combine_user_author_avail_coupons_num.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_discount_ratio.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_history_avg_price.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_avg_price.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_coupon_reduce_stat.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_coupon_threshold_stat.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_diff_coupons_num.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_live_avg_price.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_live_discount_rate.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_live_discount_ratio.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_user_live_total_cost.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_style_server_lp_conv_data_sequence_share.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_style_server_lp_impr_data_sequence_share.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_style_server_lp_resource_type_sequence_share.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_style_server_lp_style_material_id_sequence_share.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_style_server_lp_style_type_sequence_share.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_tag_center_product_name_2_game_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_purchase_action_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_action_follow_author_id_match_detail.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_action_gift_price_author_id_match_detail.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_action_gift_price_author_id_match_detail_with_price.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_action_join_fans_group_author_id_match_detail.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_ad_photo_click_author_id_match_detail.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_ad_photo_click_photo_id_match_detail.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_action_sp_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_purchase_action_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_purchase_action_list_by_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_purchase_action_list_by_time_author_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_purchase_action_list_by_time_live_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_uni_user_merchant_flag.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_univ_combine_rank_cxr.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_creative_rule_id_sequence_share.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_creative_rule_id_sequence_share_combine.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_author_weight.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_ocpc_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_ocpc_dense2.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_ocpc_dense3.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_ocpc_dense4.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_rtb_bid.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_up_model_pdct_character_cut.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_up_model_pdct_character_cut_inf.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_up_model_pdct_name_emb.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_up_model_pdct_name_emb_inf.cc"    // NOLINT

void register_group_11() {}

#endif
