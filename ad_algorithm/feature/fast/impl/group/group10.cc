#include "teams/ad/ad_algorithm/feature/fast/impl/group/group10.h"

#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_up_model_pdct_character_cut.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_up_model_pdct_name_emb.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_up_model_pdct_word_cut.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_upmodel_pdct_similar_pdct.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_histaction_firstidv3_seq.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_histaction_pdct_seq.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_histaction_secondidv3_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_search_query_statistical.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_search_query_statistical_prefix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_search_query_ids_data.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_follow_ocpc_action_type_dense_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mgi_e2e_photo_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_histaction_firstid_V3_seq_inf.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_histaction_pdct_V3_seq_inf.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_histaction_secondid_V3_seq_inf.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_up_model_pdct_character_cut_inf.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_up_model_pdct_name_emb_inf.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_up_model_pdct_word_cut_inf.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_upmodel_pdct_similar_pdct_inf.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_upmodel_user_u2u_emb_inf.h"

// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_appPackage.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_interest.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_device_brand.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_device_readablemod.h"

// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_device_ostype.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_app_purchase.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_appl1label.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_appl2label.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_info_4_label.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_info_4_label.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_app_active_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dmp_finance_tag.h"

// live reco embedding
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_reco_embedding_feed_uid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_reco_embedding_single_uid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_reco_embedding_feed_aid.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_reco_embedding_single_aid.h"

// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_upmodel_pdct_mmuemb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_history_pdct_mask.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_history_photo_mask.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_history_pdct_actweight.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_upmodel_photo_short_play_num.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action1_no_action2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_order_industry_price.cc"    // NOLINT

// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ks_flow_data_dense.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_akg_hop5_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_sdpa_photo_cnt_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_rank_ctcvr_scale_fix_v1.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_goods_sim_picasso.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_realtime_new_extend_ict.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_car_tag_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_ad_realtime_action_full.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_live_merge.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_live_merge_match_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_action_live_merge_dense.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_second_industry_pay.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_merchant_action_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_merchant_action_match_detail.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_repeat_purchase_cate_v1.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_native_author_feature.h"
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_info_feature.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_v5_third_category_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_week_gmv_max_bool_mask.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_week_gmv_p90_bool_mask.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_week_gmv_p90_bool_mask_23bins.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_week_gmv_mask_23bins.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_purchase_action_timegap_list.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_purchase_action_timegap_list_by_time.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_ecom_action_match_detail_author_id.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_order_paied_match_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_order_submit_match_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_goods_view_match_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_shop_click_match_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_order_submit_click_match_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_product_buy_click_match_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_order_impression_match_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_order_submit_whole_pay_stat_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_order_submit_photo_pay_stat_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_order_submit_live_pay_stat_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_order_paied_whole_pay_stat_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_order_paied_photo_pay_stat_dense.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_merchant_order_paied_live_pay_stat_dense.cc"    // NOLINT
void register_group_10() {}

#endif
