
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group14.h"
#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_realtime_rta_delivery_photo_list.cc" // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_realtime_rta_delivery_spu_list.cc" // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_realtime_rta_impression_photo_list.cc" // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_realtime_rta_impression_spu_list.cc" // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_realtime_rta_item_click_photo_list.cc" // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_realtime_rta_item_click_spu_list.cc" // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_action_list_with_multi_key_hard_search.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_rank_name_combine.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_rank_name_ocpx_combine.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_rank_name_form_combine.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_rank_name_form_ocpx_combine.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_rank_name_queue_combine.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_live_avg_gmv.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_action_list_with_multi_key_hard_search.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_context_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_yellow_car_item_id_dense_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_v_5_third_id_rnd.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_v_5_first_id_rnd.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_product_name_rnd.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_v_5_first_id_rnd_dup.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_v_5_second_id_rnd.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_v_5_second_id_rnd_dup.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_v_5_third_id_rnd_dup.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_product_name_rnd_dup.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_real_time_goods_view_author_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_real_time_order_paied_author_id_order_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_long_term_goods_view_author_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_long_term_order_paied_author_id_cross.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_seq_length_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_goods_seq_length_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_live_pay_long_term_user_author_cvr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_pay_long_term_positive_author.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_pay_long_term_negative_author.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_yellow_car_item_id_dense_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_yellow_car_item_id_dense_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_yellow_car_item_id_dense_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_yellow_car_item_id_dense_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_yellow_car_item_id_dense_5.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_live_pay_long_term_length_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mobile_plan_telecommunication.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_explaining_item_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_conv_chain_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_v_3_first_id_rnd.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_v_3_first_id_rnd_dup.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_gmv_indirect.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_outer_sample.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_unify_cvr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_gpm_pay_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_realtime_adx_outer_photo_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_realtime_adx_outer_product_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_sell_history_item_id_dense_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_sell_history_item_id_dense_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_sell_history_item_id_dense_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_sell_history_item_id_dense_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_sell_history_item_id_dense_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_itemimp_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_item_click_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_like_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_comment_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_played_3s_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_played_5s_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_playedend_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_invoke_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_download_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_landing_page_load_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_come_back_main_app_full.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_like_status_after_play.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_hight_light.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_realtime_orderclick_no_order_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_realtime_order_appr_no_order_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_realtime_order_view_no_order_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_realtime_order_cart_no_order_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_realtime_order_replay_no_order_no_prefix.cc"   // NOLINT

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_click_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_click_product.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_click_industry.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_p_5s_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_p_5s_product.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_p_5s_industry.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_lps_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_lps_product.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_lps_industry.cc"   // NOLINT

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_click_photo_x_product_industry.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_click_industry_x_product_photo.cc"   // NOLINT

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_p5s_photo_x_product_industry.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_p5s_industry_x_product_photo.cc"   // NOLINT

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_lps_photo_x_product_industry.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_edu_realtime_user_lps_industry_x_product_photo.cc"   // NOLINT

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_cus_acq_industry.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_cus_acq_product.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_cus_acq_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_last_5_deal_spu_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_last_10_deal_spu_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_last_30_deal_spu_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_last_5_deal_category_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_last_10_deal_category_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_last_30_deal_category_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_last_5_deal_category_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_last_10_deal_category_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_last_30_deal_category_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_conv_chain_type_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_lls_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_itemimp_full_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_item_click_full_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_like_full_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_comment_full_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_played_3s_full_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_played_5s_full_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_playedend_full_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_invoke_full_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_download_full_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_landing_page_load_full_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_jx_off_come_back_main_app_full_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_sug_prefix_input.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_refer_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_is_commerical.cc"   // NOLINT

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_edu_realtime_user_click_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_edu_realtime_user_click_industry_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_edu_realtime_user_p_5s_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_edu_realtime_user_p_5s_industry_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_edu_realtime_user_form_sub_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_edu_realtime_user_form_sub_industry_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_edu_realtime_user_cus_acq_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_edu_realtime_user_cus_acq_industry_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_edu_realtime_user_click_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_edu_realtime_user_click_industry_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_edu_realtime_user_p_5s_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_edu_realtime_user_p_5s_industry_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_edu_realtime_user_form_sub_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_edu_realtime_user_form_sub_industry_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_edu_realtime_user_cus_acq_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_edu_realtime_user_cus_acq_industry_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rank_rta_account_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rank_rta_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rank_rta_industry_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rank_rta_product_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rank_rta_ocpx_action_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rank_rta_bid_max_level.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rank_rta_bid_min_level.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_add_wechat_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_add_wechat_account.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_add_wechat_product.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_add_wechat_industry.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_im_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_im_account.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_im_product.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_im_industry.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ug_flag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_submit_clue_item_type_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_new_account_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_day_7_stay_duration_by_industry_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_day_7_stay_cnt_by_industry_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_day_30_stay_duration_by_industry_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_day_30_stay_cnt_by_industry_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_order_price_range.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cot_close_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cot_close_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cot_close_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cot_close_4.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cot_close_5.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cot_close_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cot_close_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cot_close_8.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cot_close_9.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cot_close_10.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dense_cot_open_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_dense_cot_close_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_click_cot_open_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_click_cot_close_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_order_cot_open_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_order_cot_close_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_creative_material_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_sku_max_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_sku_min_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_sku_median_price.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_purchase_degree.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_ad_click_30d_keyword_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_ad_click_60d_keyword_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_orderpay_7d_keyword_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_orderpay_30d_keyword_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_orderpay_60d_keyword_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_concat_keyword_video_clip.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_user_ks_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_is_item_card.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_search_pos.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_search_page.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_search_page_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_search_page_size.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_ad_click_7d_videol_clip_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_ad_click_30d_videol_clip_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_ad_click_60d_videol_clip_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_orderpay_7d_videol_clip_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_orderpay_30d_videol_clip_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_orderpay_60d_videol_clip_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_first_industry_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_second_industry_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_sell_history_llm_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_prerank_candidates_list_hard_account_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_prerank_candidates_list_hard_photo_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_prerank_candidates_list_hard_industry_id_v_3_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_prerank_candidates_list_hard_city_product_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_prerank_candidates_list_hard_ocpx_action_type_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_prerank_candidates_list_soft_account_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_prerank_candidates_list_soft_photo_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_prerank_candidates_list_soft_industry_id_v_3_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_prerank_candidates_list_soft_city_product_id_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_prerank_candidates_list_soft_ocpx_action_type_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_pinnerformer_embedding_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_pinnerformer_embedding_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_pinnerformer_embedding_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_pinnerformer_embedding_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_inner_live_prerank_candidate_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_inner_live_prerank_candidate_first_industry_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_inner_live_prerank_candidate_industry_id_v_3_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_inner_live_prerank_candidate_account_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_realtime_rta_delivery_spu_list_ecom.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_realtime_rta_impression_spu_list_ecom.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_realtime_rta_item_click_spu_list_ecom.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_realtime_adx_spu_list_ecom.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_product_goods_spu_id_ecom.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_i_m_wechat_ped_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_i_m_wechat_ped_account.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_i_m_wechat_ped_product.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_i_m_wechat_clk_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_i_m_wechat_clk_account.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_i_m_wechat_clk_product.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_i_m_wechat_apr_photo.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_i_m_wechat_apr_account.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_i_m_wechat_apr_product.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_form_sub_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_form_sub_product_name_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_form_sub_industry_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_eff_cus_acq_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_eff_cus_acq_product_name_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_eff_cus_acq_industry_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_eff_cus_acq_photo_ratio.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_eff_cus_acq_product_name_ratio.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_eff_cus_acq_industry_ratio.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_realtime_form_sub_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_realtime_form_sub_product_name_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_realtime_form_sub_industry_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_realtime_cus_acq_photo_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_realtime_cus_acq_product_name_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_realtime_cus_acq_industry_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_realtime_cus_acq_photo_ratio.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_realtime_eff_cus_acq_product_name_ratio.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_realtime_eff_cus_acq_industry_ratio.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_live_emb_rnd.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_i_m_ocpc_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_live_emb_rnd_dup.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_page_avg_item_time.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_session_seq_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_is_fresh_req.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_page_item_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_page_pos.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_page_hetu_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_page_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_page_first_industry_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_page_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_page_item_id_all.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_page_pos_all.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_page_hetu_tag_all.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_page_product_name_all.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_page_first_industry_id_all.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_page_author_id_all.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_live_realtime_acc_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_inner_ecpm_raw_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_live_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_photo_reco_emb_feed_uid.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_universe_cpm_ad_style.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_live_realtime_acc_cnt_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_hottest_intention_list_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_newest_intention_list_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_hottest_intention_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_newest_intention_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_cart_item_add_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_cart_item_add_succ_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_cart_item_add_cur_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_cart_item_update_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_cart_item_update_succ_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_cart_item_update_cur_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_cart_item_delete_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_cart_item_delete_succ_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_cart_item_delete_cur_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_item_comment_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_item_comment_succ_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_item_comment_cur_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_item_comment_update_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_item_comment_update_succ_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_item_comment_update_cur_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_comment_credit_score.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_comment_service_score.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_comment_quality_score.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_comment_logistics_score.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_comment_performance_score.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_comment_cost_performance_star_score.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_im_message_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_im_message_succ_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_im_message_cur_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_refund_event_count.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_refund_event_succ_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_refund_event_cur_time_gap.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_cart_item_match_target_item.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ods_item_comment_match_target_item.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_merchant_im_msg_refund_event_match_target_item.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_is_new_item.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_adcu_reco_b_token.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_adcu_reco_c_token.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_extend_tmpl.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_extend_ecom_tmpl.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_extend_ecom_with_time_tmpl.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_extend_ecom_short_with_time_tmpl.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_id_dense_split.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_concat_event_page_impression_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_concat_event_page_impression_second_industry_name_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_concat_event_conversion_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_concat_event_conversion_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_concat_event_conversion_account_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_concat_event_conversion_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_concat_event_conversion_second_industry_name_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_concat_event_conversion_package_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_concat_event_pay_second_industry_name_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_hit_event_conversion_second_industry_name_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_gap_event_conversion_second_industry_name_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cnt_event_conversion_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cnt_event_conversion_second_industry_name_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_min_gap_event_app_invoked_second_industry_name_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_min_gap_event_page_impression_second_industry_name_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_min_gap_event_conversion_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_min_gap_event_conversion_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_min_gap_event_conversion_second_industry_name_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_min_gap_event_register_second_industry_name_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_min_gap_event_pay_second_industry_name_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_min_gap_event_landingpage_started_download_click_second_industry_name_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_min_gap_event_key_inapp_action_second_industry_name_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cnt_diff_event_conversion_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cnt_diff_event_conversion_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cnt_diff_event_conversion_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cnt_diff_event_conversion_second_industry_name_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cnt_diff_event_nextday_stay_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cnt_diff_event_wechat_game_click_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cnt_diff_event_key_inapp_action_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cnt_diff_event_cid_landing_page_show_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cnt_diff_event_game_upgrade_role_account_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_cnt_diff_event_cid_landing_page_click_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_physical_pos_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_1_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_2_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_4_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_13_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_0_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_1_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_2_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_4_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_13_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_0_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_1_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_2_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_4_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_13_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_win_cpm_style_0_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_1_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_2_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_4_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_13_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_0_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_1_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_2_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_4_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_13_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_0_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_1_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_2_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_4_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_13_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_lose_cpm_style_0_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_1_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_2_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_4_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_13_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_1_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_2_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_4_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_13_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_1_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_2_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_4_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_13_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_0_day_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_0_day_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_rta_style_0_day_7.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_live_audience.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_live_realtime_acc_cnt_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_rt_acc_fea.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_stat_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_merchant_stat_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_merchant_author_match_ratio_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_ksmp_match_detal.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_ecom_order_pay_match_detal.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_ecom_order_pay_with_coupon_match_detal.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_local_order_pay_match_detal.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_local_order_pay_verify_match_detal.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_local_life_trade_verify_author_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_local_life_trade_author_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ksmp_order_pay_author_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ecom_order_pay_author_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_ecom_order_pay_with_coupon_author_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_local_order_pay.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_long_term_merchant_author_id_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_real_time_combine_order_paied_yellow_car_attr_match_dense_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_real_time_combine_goods_view_yellow_car_attr_match_dense_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_long_term_combine_order_paied_yellow_car_attr_match_dense_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_long_term_combine_goods_view_yellow_car_attr_match_dense_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_real_time_combine_order_paied_history_sell_attr_match_dense_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_real_time_combine_goods_view_history_sell_attr_match_dense_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_long_term_combine_order_paied_history_sell_attr_match_dense_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_long_term_combine_goods_view_history_sell_attr_match_dense_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_add_cart_author_item_match_cnt_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_update_cart_author_item_match_cnt_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_delete_cart_author_item_match_cnt_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_comment_match_score_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_comment_update_match_score_sparse.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_im_message_author_match_cnt_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_refund_author_match_cnt_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_ecom_photo_item_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_ecom_live_item_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_ecom_photo_hetu_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_ecom_live_hetu_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_ecom_photo_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_ecom_live_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_ecom_photo_first_industry_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_ecom_live_first_industry_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_ecom_photo_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_ecom_live_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_seq_id_cross_pos_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mix_rank_context_match_target_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_sell_history_llm_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_turn_cnt_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_is_c_send_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_c_2b_send_msg_cnt_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_is_b_reply_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_b_2c_after_send_msg_cnt_list_30_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_turn_cnt_list_15_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_is_c_send_list_15_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_c_2b_send_msg_cnt_list_15_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_is_b_reply_list_15_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_b_2c_after_send_msg_cnt_list_15_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_turn_cnt_list_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_is_c_send_list_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_c_2b_send_msg_cnt_list_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_is_b_reply_list_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_b_2c_after_send_msg_cnt_list_7_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_turn_cnt_list_1_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_is_c_send_list_1_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_c_2b_send_msg_cnt_list_1_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_is_b_reply_list_1_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_im_indirect_b_2c_after_send_msg_cnt_list_1_d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_industry_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_ad_style.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_is_playable.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_medium_cooperation_mode.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_material_feature_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_medium_app_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_first_industry_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_second_industry_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_endcard_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_playcard_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_hour.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_date_interval.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_ad_style_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_is_playable_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_industry_tag_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_medium_cooperation_mode_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_click_action_seq_material_feature_type_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_product_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_industry_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_is_playable.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_ad_style.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_medium_cooperation_mode.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_material_feature_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_medium_app_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_first_industry_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_second_industry_name.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_endcard_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_playcard_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_hour.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_date_interval.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_ad_style_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_is_playable_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_industry_tag_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_medium_cooperation_mode_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_material_feature_type_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_novel_search_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_keyword_intent_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_novel_interactive_times_segment.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_card_scene_flag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_sim_account_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_sim_action_label_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_sim_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_sim_industry_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_sim_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_sim_product_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_action_sim_timestamp_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_action_list_with_multi_key_hard_search_pooling.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_action_list_with_multi_key_pooling.cc"   // NOLINT
void register_group_14() {}

#endif
