#include "teams/ad/ad_algorithm/feature/fast/impl/group/group9.h"
#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_pay_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_slp_cnt.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_model_rank_name.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_latest_live_time_interval.h"
// live real time feature
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_realtime_action_match_cnt_live.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_colossus_filtered_by_play_1m.h"
// adsim2.0
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_inc_cut_nio.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_no_filter.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_window_sample.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_no_filter_meta.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_price_combine.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_shop_item_detail_info_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_merchant_sku_price.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_price_combine_sku.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_adsim_photo_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_adsim_photo_info_field.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_ad_action_list_sim_no_filter_field.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_pdn_sim_seq_num_v0.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_pdn_sim_seq_v0.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_pdn_sim_seq_v1.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_ecom_action_list.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_univ_combine_rank_cxr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_goods_action_picasso.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_second_industry_dense_268.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_second_industry_dense_268.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_top_action_list_sim_inc_cut_v5.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_sdpa_cross_product_name_dense.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_sdpa_cross_product_name_dense_v2.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_merge_recall_context_dense.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_sdpa_ecom_product_kwds.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_sdpa_ecom_user_action_kwds.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_spu_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_tag_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_tag_match_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_rank_cpm_info_v3.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_outer_inner_spu_feature_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_action_spu_list_fix.h"

// universe feature
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_creative_rule_id_sequence_share.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_p_author_univ.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_p_author_avg_explore_ratio.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_author_media_app_avg_explore_ratio.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_author_medium_industry_avg_explore_ratio.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_p_medi_style_univ.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_spu_attr.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_spu_action_list.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_goods_cnt_label.h"

// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_mixup_dense_ocpc_type.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_mixup_dense_callback_event.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_mixup_dense_callback_event_fix.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_photo_field_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_photo_field_id_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_goods_4_label.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_goods_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_recruit_live_jobs_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_is_recruit_live.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_recruit_live_author_feature.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_delay_info_unify.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_click_match_num_long_merge.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_recall_target_info_v2.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_rank_ctcvr_scale_fix.h"
// mmu kge feature
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mmu_kge.h"

// upmodel features
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_age.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_current_city_id.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_current_ip.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_current_poi_type.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_current_province_id.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_device_app_ver.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_device_id.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_device_platform.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_device_visit_mode.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_device_visit_net.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_fans_count.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_follow_count.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_gender.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_has_car.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_has_house.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_is_work.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_life_stage.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_long_city_id.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_long_ip.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_long_poi_type.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_long_province_id.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_network.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_phone_price.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_platform.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_region.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_upload_count.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_upmodel_idv3.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_upmodel_first_industry_id.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_upmodel_product_name.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_goods_gsu_for_photo_v1.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_field_id_list_new.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_mmu_commerce_photo_goods_feature_int.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_mmu_commerce_photo_goods_feature_int_list.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_mmu_commerce_photo_goods_feature_float_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_mmu_commerce_photo_goods_gsu_feature.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_mmu_cates.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_sdpa_novel_info.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_action_tag_list_order_paid_combine.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_author_tag_category_new_fix.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_goods_gsu_new_neg.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_akg_indus_user_ad_interest_seq.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_history_photo_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_history_pdct_emb.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_history_pdct_emb_48.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_outside_mmu_ids.h"

// 存在单独的 cc 文件
// #include
// "teams/ad/ad_algorithm/feature/fast/impl/extract_live_start_time_gap_plain_dis_n_aid_src2_dis_v2.h"
// dragon feature
#include "teams/ad/ad_algorithm/feature/fast/impl/empty_dense_combine_dragon.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/empty_dense_item_dragon.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/empty_dense_user_dragon.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/empty_item_dragon.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/empty_reco_item_dragon.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/empty_reco_user_dragon.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/empty_user_dragon.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_mmu_commerce_photo_sim_action_match_cnt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_mmu_commerce_photo_goods_gsu_feature_v1.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dsplive_realtime_action_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_realtime_action_list_add.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_match_add.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_realtime_action_match_add_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_style_server_lp_sequence_share_v2.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_style_server_card_material_feature.h"

// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_category_fix.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_datetime_hour_till_natural_day_share.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_datetime_natural_day_event_pay_dense.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_datetime_dense.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_user_extend_info.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_mmu_commerce_photo_goods_gsu_feature_topk_target.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_realtime_seq_lp_no_dup.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_realtime_seq_no_dup_share_opt.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_phd_play_ids.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_actions_prefix.h"


#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_goods_attr_list_fix.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_follow_ocpc_action_type_dense.h"

// order
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_order_poster_rate.h"

// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_author_weight.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_ocpc_dense.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_ocpc_dense2.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_ocpc_dense3.h"
// 存在单独的 cc 文件
// #include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_photo_ocpc_dense4.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_landingpage_realtime_action_list.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_item_u2u_match.h"
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_live_item_u2u_match_category.h"

void register_group_9() {}

#endif
