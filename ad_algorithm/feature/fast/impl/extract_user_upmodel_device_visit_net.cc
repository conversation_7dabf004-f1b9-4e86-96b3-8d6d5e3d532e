#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_device_visit_net.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelDeviceVisitNet::Extract(const AdLog& adlog, size_t pos,
                                               std::vector<ExtractResult>* result) {
  std::hash<std::string> hash_fn;
  if (adlog.has_user_info() && adlog.user_info().has_device_info()) {
    const auto& device_info_visit_net =
        GetFeature(FeaturePrefix::UP_USER_DEVICE_INFO_VISIT_NET,
                   hash_fn(adlog.user_info().device_info().visit_net()));
    AddFeature(device_info_visit_net, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
