#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_user_universe_convert_action_sequences.dark
class ExtractUserUniverseConvertActionSeqSecondIndustryName : public FastFeatureNoPrefix {
 public:
  ExtractUserUniverseConvertActionSeqSecondIndustryName();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserUniverseConvertActionSeqSecondIndustryName);
};

REGISTER_EXTRACTOR(ExtractUserUniverseConvertActionSeqSecondIndustryName);
}  // namespace ad_algorithm
}  // namespace ks
