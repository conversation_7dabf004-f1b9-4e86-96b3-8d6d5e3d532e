#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_user_tim.dark
class ExtractUserMerchantLongTermOrderPaiedTsPleDense : public FastFeature {
 public:
  ExtractUserMerchantLongTermOrderPaiedTsPleDense();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserMerchantLongTermOrderPaiedTsPleDense);
};

REGISTER_EXTRACTOR(ExtractUserMerchantLongTermOrderPaiedTsPleDense);
}  // namespace ad_algorithm
}  // namespace ks
