#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_dense_ple.dark
class ExtractUserMerchantStatPleDense : public FastFeature {
 public:
  ExtractUserMerchantStatPleDense();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserMerchantStatPleDense);
};

REGISTER_EXTRACTOR(ExtractUserMerchantStatPleDense);
}  // namespace ad_algorithm
}  // namespace ks
