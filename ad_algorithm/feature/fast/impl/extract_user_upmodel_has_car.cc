#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_has_car.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelHasCar::Extract(const AdLog& adlog, size_t pos,
                                       std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_long_term_loc()) {
    const auto& ad_user_info_has_car = GetFeature(
        FeaturePrefix::UP_USER_HAS_CAR, adlog.user_info().ad_user_info().has_car());
    AddFeature(ad_user_info_has_car, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
