#pragma once
#include <vector>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {
class ExtractUserUniverseStatsAll : public FastFeature {
 public:
  ExtractUserUniverseStatsAll() : FastFeature(FeatureType::DENSE_USER) {}

    virtual void Extract(const AdLog& adlog, size_t pos,
                         std::vector<ExtractResult>* result) {
        if (adlog.has_user_info() && adlog.user_info().common_info_attr_size() > 0) {
            for (const ::kuaishou::ad::CommonInfoAttr& userAttr : adlog.user_info().common_info_attr()) {
                if (userAttr.name_value() == ::kuaishou::ad::CommonInfoAttr_Name_USER_UNIVERSE_ALL_TAG) {
                    size_t c = 0;
                    for (const float &cxr : userAttr.float_list_value()) {
                         if(std::isnan(cxr) || cxr < 0){
                           AddFeature(c++, 0, result);
                         }else{
                           AddFeature(c++, cxr, result);
                         }
                    }
                    break;
                }
            }
        }
    }

 private:
//    const uint32_t ID_MAX = 100;
    DISALLOW_COPY_AND_ASSIGN(ExtractUserUniverseStatsAll);
};

REGISTER_EXTRACTOR(ExtractUserUniverseStatsAll);

}  // namespace ad_algorithm
}  // namespace ks
