#pragma once
#include <vector>
#include <string>
#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/log_preprocess/ad_model_kconf_util.h"

namespace ks {
namespace ad_algorithm {

class ExtractUserUniverseMediaFirstClass : public FastFeature {
 public:
  ExtractUserUniverseMediaFirstClass()
    : FastFeature(FeatureType::USER) {
  }

  bool getMediaAttr(const AdLog& adlog, int index, vector<std::string>& res) {
    auto adUnMediaClassMap = AdModelKconfUtil::adUnMediaClass();
    std::string app_id = adlog.context().app_id();
    auto iter = adUnMediaClassMap->find(app_id);
    if (iter == adUnMediaClassMap->end()) {
      return false;
    }
    std::string attr = iter->second;
    vector<std::string> attr_list;
    base::SplitString(attr, ";", &attr_list);
    if (attr_list.size() < index + 1) {
      return false;
    }
    base::SplitString(attr_list[index], ",", &res);
    return true;
  }

  virtual void Extract(const AdLog& adlog,
                       size_t pos,
                       std::vector<ExtractResult>* result) {
    vector<std::string> attr_res;
    int attr_index = 1;
    std::hash<std::string> hash_fn;
    if (getMediaAttr(adlog, attr_index, attr_res)) {
      for (std::string attr : attr_res) {
        //cout << "ydj_debug " << attr << endl;
        AddFeature(GetFeature(FeaturePrefix::USER_UN_MEDIA_FIRST_CLASS, hash_fn(attr)), 1.0, result);
      }
    } 
  }
 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserUniverseMediaFirstClass);
};

REGISTER_EXTRACTOR(ExtractUserUniverseMediaFirstClass);

}  // namespace ad_algorithm
}  // namespace ks

