#pragma once
#include <string>
#include <vector>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
namespace ks {
namespace ad_algorithm {
class ExtractUserUPModelLongCityId : public FastFeature {
 public:
  ExtractUserUPModelLongCityId():FastFeature(FeatureType::USER) {}
  virtual void Extract(const AdLog& adlog,
                       size_t pos,
                       std::vector<ExtractResult>* result);
 private:
  const std::string  USED_FEATURES[1] = {
    "user_info.long_term_loc.city_id"
  };
  DISALLOW_COPY_AND_ASSIGN(ExtractUserUPModelLongCityId);
};
REGISTER_EXTRACTOR(ExtractUserUPModelLongCityId);
}  // namespace ad_algorithm
}  // namespace ks
