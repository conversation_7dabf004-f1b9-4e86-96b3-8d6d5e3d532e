#pragma once
#include <string>
#include <vector>
#include <unordered_map>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"
namespace ks {
namespace ad_algorithm {
class ExtractUserValuedFollowAuthor
    : public FastFeatureNoPrefix {
 public:
  explicit ExtractUserValuedFollowAuthor(int interval)
      : FastFeatureNoPrefix(FeatureType::USER) { interval_ = interval; }
  virtual void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
    if (!adlog.has_user_info()) return;

    int info_size = 5;  // is_valued_fan is_t7_interact_fan is_t7_gmv_fan t1_t7_gmv is_merchant
    std::vector<int> valued_flag;
    // interval 7
    if (interval_ == 7) {
      if (adlog.has_user_info() && adlog.user_info().common_info_attr_size() > 0) {
        for (const ::kuaishou::ad::CommonInfoAttr& user_attr : adlog.user_info().common_info_attr()) {
          if (user_attr.name_value() ==
                ::kuaishou::ad::CommonInfoAttr_NameExtendOne_FANSTOP_VALUED_FOLLOW_INFO_T7) {
            for (int idx = 0; idx * info_size < user_attr.int_list_value_size(); ++idx) {
              if (user_attr.int_list_value(idx * info_size) > 0) {
                valued_flag.emplace_back(idx);
              }
            }
          }
        }
        for (const ::kuaishou::ad::CommonInfoAttr& user_attr : adlog.user_info().common_info_attr()) {
          if (user_attr.name_value() ==
                ::kuaishou::ad::CommonInfoAttr_NameExtendOne_FANSTOP_VALUED_FOLLOW_ID_T7) {
            for (int i = 0; i < valued_flag.size(); ++i) {
              AddFeature(user_attr.int_list_value(valued_flag[i]), 1.0f, result);
            }
          }
        }
      }
    }
  }

 private:
  int interval_;
  const std::string USED_FEATURES[1] = {
      "item.ad_dsp_info.photo_info.author_info.id"};
  DISALLOW_COPY_AND_ASSIGN(ExtractUserValuedFollowAuthor);
};
// t7
class ExtractUserValuedFollowAuthorT7: public ExtractUserValuedFollowAuthor {
 public:
    ExtractUserValuedFollowAuthorT7():ExtractUserValuedFollowAuthor(7) {}
 private:
    DISALLOW_COPY_AND_ASSIGN(ExtractUserValuedFollowAuthorT7);
};
REGISTER_EXTRACTOR(ExtractUserValuedFollowAuthorT7);

}  // namespace ad_algorithm
}  // namespace ks
