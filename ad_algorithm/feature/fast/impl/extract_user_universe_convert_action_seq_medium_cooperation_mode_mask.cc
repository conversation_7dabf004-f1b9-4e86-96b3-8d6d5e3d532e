#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_medium_cooperation_mode_mask.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/uv_action_seq_util.h"

namespace ks {
namespace ad_algorithm {

ExtractUserUniverseConvertActionSeqMediumCooperationModeMask::
    ExtractUserUniverseConvertActionSeqMediumCooperationModeMask()
    : FastFeature(FeatureType::DENSE_COMBINE) {}
void ExtractUserUniverseConvertActionSeqMediumCooperationModeMask::Extract(
    const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.user_info().common_info_attr(),
                                 CommonInfoAttr::UNIVERSE_AD_CONVERT_ACTION_SEQ_MEDIUM_COOPERATION_MODE);
  auto x2 = get_adlog_int64(adlog.context().info_common_attr(), ContextInfoCommonAttr::COOPERATION_MODE);
  auto x4 = hard_search_seq_with_single_key(x1, x2, 30);
  add_feature_result(x4, 30, result);
}

}  // namespace ad_algorithm
}  // namespace ks
