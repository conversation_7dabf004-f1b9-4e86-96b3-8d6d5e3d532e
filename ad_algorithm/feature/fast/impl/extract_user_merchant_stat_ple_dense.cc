#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_stat_ple_dense.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_multi_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_dense_ple.h"
#include "teams/ad/ad_algorithm/fed/compute/merge_list.h"

namespace ks {
namespace ad_algorithm {

ExtractUserMerchantStatPleDense::ExtractUserMerchantStatPleDense() : FastFeature(FeatureType::DENSE_USER) {}
void ExtractUserMerchantStatPleDense::Extract(const AdLog& adlog, size_t pos,
                                              std::vector<ExtractResult>* result) {
  auto multi_attr1 =
      get_adlog_multi<absl::optional<int64_t>, absl::optional<int64_t>, absl::optional<int64_t>,
                      absl::optional<int64_t>, absl::optional<int64_t>, absl::optional<int64_t>,
                      absl::optional<int64_t>>(
          adlog.user_info().common_info_attr(),
          std::make_tuple(CommonInfoAttr::U_TYPE_INT, CommonInfoAttr::USER_VALUE_W_LEVEL,
                          CommonInfoAttr::USER_PAY_ORDER_DAYS_30D, CommonInfoAttr::USER_LAST_ORDER_DATE_DIFF,
                          CommonInfoAttr::USER_UNRISK_PAY_ORDER_CNT_30D,
                          CommonInfoAttr::USER_UNRISK_PAY_ORDER_CNT_365D,
                          CommonInfoAttr::USER_AUTHOR_UNIQUE_CNT));
  auto x1 = std::get<0>(multi_attr1);
  auto x6 = std::get<1>(multi_attr1);
  auto x11 = std::get<2>(multi_attr1);
  auto x16 = std::get<3>(multi_attr1);
  auto x21 = std::get<4>(multi_attr1);
  auto x26 = std::get<5>(multi_attr1);
  auto x31 = std::get<6>(multi_attr1);

  auto x3 = cast_to_float_with_default(x1, 0);
  auto x4 = get_u_type_bins();
  auto x5 = get_dense_ple_helper(x3, x4);
  auto x8 = cast_to_float_with_default(x6, 0);
  auto x9 = get_w_level_bins();
  auto x10 = get_dense_ple_helper(x8, x9);
  auto x13 = cast_to_float_with_default(x11, -1);
  auto x14 = get_user_active_30d_bins();
  auto x15 = get_dense_ple_helper(x13, x14);
  auto x18 = cast_to_float_with_default(x16, 720);
  auto x19 = get_user_last_order_date_diff_bins();
  auto x20 = get_dense_ple_helper(x18, x19);
  auto x23 = cast_to_float_with_default(x21, -1);
  auto x24 = get_user_pay_30d_bins();
  auto x25 = get_dense_ple_helper(x23, x24);
  auto x28 = cast_to_float_with_default(x26, -1);
  auto x29 = get_user_pay_365d_bins();
  auto x30 = get_dense_ple_helper(x28, x29);
  auto x33 = cast_to_float_with_default(x31, 0);
  auto x34 = get_user_author_unique_bins();
  auto x35 = get_dense_ple_helper(x33, x34);
  auto x36 = merge_float_list_all(x5, x10, x15, x20, x25, x30, x35);
  add_feature_result(x36, 48, result);
}

}  // namespace ad_algorithm
}  // namespace ks
