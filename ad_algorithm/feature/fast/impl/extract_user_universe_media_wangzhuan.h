#pragma once
#include <vector>
#include <string>
#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

class ExtractUserUniverseMediaWangzhuan : public FastFeature {
 public:
  ExtractUserUniverseMediaWangzhuan()
    : FastFeature(FeatureType::USER) {
  }

  bool isWangZhuan(const AdLog& adlog) {
    auto wangzhuanIds = AdModelKconfUtil::adUnMediaIsWangZhuan();
    std::string app_id = adlog.context().app_id();
    if (wangzhuanIds->find(app_id) != wangzhuanIds->end()) {
      return true;
    }
    return false;
  }

  virtual void Extract(const AdLog& adlog,
                       size_t pos,
                       std::vector<ExtractResult>* result) {
    if (isWangZhuan(adlog)) {
      AddFeature(GetFeature(FeaturePrefix::USER_UN_MEDIA_WANGZHUAN,1), 1.0, result);
    } else {
      AddFeature(GetFeature(FeaturePrefix::USER_UN_MEDIA_WANGZHUAN,0), 1.0, result);
    }
  }
 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserUniverseMediaWangzhuan);
};

REGISTER_EXTRACTOR(ExtractUserUniverseMediaWangzhuan);

}  // namespace ad_algorithm
}  // namespace ks

