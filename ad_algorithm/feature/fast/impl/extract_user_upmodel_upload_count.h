#pragma once
#include <string>
#include <vector>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
namespace ks {
namespace ad_algorithm {
class ExtractUserUPModelUploadCount : public FastFeature {
 public:
  ExtractUserUPModelUploadCount():FastFeature(FeatureType::USER) {}
  virtual void Extract(const AdLog& adlog,
                       size_t pos,
                       std::vector<ExtractResult>* result);
 private:
  const std::string  USED_FEATURES[1] = {
    "user_info.attribute.fans_count"
  };
  DISALLOW_COPY_AND_ASSIGN(ExtractUserUPModelUploadCount);
};
REGISTER_EXTRACTOR(ExtractUserUPModelUploadCount);
}  // namespace ad_algorithm
}  // namespace ks
