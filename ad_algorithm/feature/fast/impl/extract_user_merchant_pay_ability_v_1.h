#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_user_merchant_pay_ability.dark
class ExtractUserMerchantPayAbilityV1 : public FastFeature {
 public:
  ExtractUserMerchantPayAbilityV1();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserMerchantPayAbilityV1);
};

REGISTER_EXTRACTOR(ExtractUserMerchantPayAbilityV1);
}  // namespace ad_algorithm
}  // namespace ks
