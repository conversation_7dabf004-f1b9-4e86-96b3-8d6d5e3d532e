#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/impl/extract_user_universe_convert_action_sequences_extend_mask.dark
class ExtractUserUniverseConvertActionSeqMaterialFeatureTypeExtendMask : public FastFeature {
 public:
  ExtractUserUniverseConvertActionSeqMaterialFeatureTypeExtendMask();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserUniverseConvertActionSeqMaterialFeatureTypeExtendMask);
};

REGISTER_EXTRACTOR(ExtractUserUniverseConvertActionSeqMaterialFeatureTypeExtendMask);
}  // namespace ad_algorithm
}  // namespace ks
