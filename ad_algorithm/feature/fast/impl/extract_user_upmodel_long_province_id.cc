#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_long_province_id.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelLongProvinceId::Extract(const AdLog& adlog, size_t pos,
                                               std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_long_term_loc()) {
    const auto& long_term_province_id =
        GetFeature(FeaturePrefix::USER_LOC_PROVINCE,
                   adlog.user_info().long_term_loc().province_id());
    AddFeature(long_term_province_id, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
