#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_gender.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelGender::Extract(const AdLog& adlog, size_t pos,
                                       std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_attribute()) {
    const auto& attribute_gender =
        GetFeature(FeaturePrefix::UP_USER_GENDER, adlog.user_info().attribute().gender());
    AddFeature(attribute_gender, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
