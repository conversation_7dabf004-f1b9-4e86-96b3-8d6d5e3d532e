#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_native_hard_rank_candidates_account_id_sort_by_ensemble_no_prefix.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_multi_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/rank_candidates_list_info.h"

namespace ks {
namespace ad_algorithm {

ExtractUserSparseNativeHardRankCandidatesAccountIdSortByEnsembleNoPrefix::
    ExtractUserSparseNativeHardRankCandidatesAccountIdSortByEnsembleNoPrefix()
    : FastFeatureNoPrefix(FeatureType::USER) {}
void ExtractUserSparseNativeHardRankCandidatesAccountIdSortByEnsembleNoPrefix::Extract(
    const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto multi_attr1 =
      get_adlog_multi<absl::Span<const int64_t>, absl::Span<const int64_t>, absl::Span<const int64_t>>(
          adlog.context().info_common_attr(),
          std::make_tuple(
              ContextInfoCommonAttr::RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST,
              ContextInfoCommonAttr::RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST,
              ContextInfoCommonAttr::RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST));
  auto x1 = std::get<0>(multi_attr1);
  auto x2 = std::get<1>(multi_attr1);
  auto x3 = std::get<2>(multi_attr1);

  auto x4 = get_adlog_int64(adlog.llsid());
  auto x11 = generate_rank_candidates_list_feature(x1, x2, x3, x4, 0, 32, 0, 0, 1, 50);
  add_feature_result(x11, result);
}

}  // namespace ad_algorithm
}  // namespace ks
