#pragma once
#include <string>
#include <vector>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"
#include "ks/reco_proto/proto/reco.pb.h"
namespace ks {
namespace ad_algorithm {

class ExtractUserVideoPlayGapSeq: public FastFeatureNoPrefix {
 public:
  ExtractUserVideoPlayGapSeq(): FastFeatureNoPrefix(FeatureType::USER) {
    feature_index_ = 0;
  }

  explicit ExtractUserVideoPlayGapSeq(size_t index): FastFeatureNoPrefix(FeatureType::USER) {
    feature_index_ = index;
  }

  virtual void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
    if (adlog.has_reco_user_info()
       && adlog.reco_user_info()->has_user_profile_v1()) {
      const auto& user_profile = adlog.reco_user_info()->user_profile_v1();
      int video_play_num = user_profile.video_playing_stat_size();
      if (video_play_num > 0 && feature_index_ < video_play_num) {
        double value = 1.0;
        const auto& video_play = user_profile.video_playing_stat(feature_index_);
        AddFeature(CombineValue(1, video_play.author_id()), value, result);
        AddFeature(CombineValue(2, video_play.photo_id()), value, result);
        uint64 play_time = (video_play.playing_time() / 1000) << 24;
        uint64 duration = video_play.video_duration() / 1000;
        AddFeature(CombineValue(3, play_time | duration), value, result);
        AddFeature(CombineValue(15, IntLogTimeMs(adlog.Get().time() - video_play.server_timestamp())), value, result);
      }
    }
  }

 private:
  size_t feature_index_;

  uint64_t CombineValue(uint64_t val1, uint64_t val2) {
    uint64_t feature = (val2 & ((1ULL << 48) - 1));
    return ((uint64_t)(val1) << 48) | feature;
  }
  int IntLogTimeMs(uint64 time_gap_ms) {
    double time_gap = 1.0 * (time_gap_ms + 1.0) / 1000.0;
    return static_cast<int>(log(time_gap));
  }
  const std::string  USED_FEATURES[6] = {
    "time",
    "reco_user_info.user_profile_v1.video_playing_stat.author_id",
    "reco_user_info.user_profile_v1.video_playing_stat.photo_id",
    "reco_user_info.user_profile_v1.video_playing_stat.playing_time",
    "reco_user_info.user_profile_v1.video_playing_stat.video_duration",
    "reco_user_info.user_profile_v1.video_playing_stat.server_timestamp"
  };
  DISALLOW_COPY_AND_ASSIGN(ExtractUserVideoPlayGapSeq);
};

REGISTER_SEQUENCE_EXTRACTOR(ExtractUserVideoPlayGapSeq, 100);

}  // namespace ad_algorithm
}  // namespace ks
