#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_pay_ability_v_1.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_multi_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"
#include "teams/ad/ad_algorithm/fed/compute/merge_list.h"

namespace ks {
namespace ad_algorithm {

ExtractUserMerchantPayAbilityV1::ExtractUserMerchantPayAbilityV1() : FastFeature(FeatureType::DENSE_USER) {}
void ExtractUserMerchantPayAbilityV1::Extract(const AdLog& adlog, size_t pos,
                                              std::vector<ExtractResult>* result) {
  auto multi_attr1 = get_adlog_multi<absl::optional<int64>, absl::optional<int64>, absl::optional<int64>,
                                     absl::optional<int64>, absl::optional<int64>, absl::optional<int64>,
                                     absl::optional<int64>, absl::optional<int64>, absl::optional<int64>,
                                     absl::optional<int64>, absl::optional<int64>, absl::optional<int64>>(
      adlog.user_info().common_info_attr(),
      std::make_tuple(CommonInfoAttr::USER_MERCHANT_PAY_ABILITY_ORDER_TIMES_30D,
                      CommonInfoAttr::USER_MERCHANT_PAY_ABILITY_ORDER_TIMES_90D,
                      CommonInfoAttr::USER_MERCHANT_PAY_ABILITY_ORDER_TIMES_360D,
                      CommonInfoAttr::USER_MERCHANT_PAY_ABILITY_TOTAL_FEE_30D,
                      CommonInfoAttr::USER_MERCHANT_PAY_ABILITY_TOTAL_FEE_90D,
                      CommonInfoAttr::USER_MERCHANT_PAY_ABILITY_TOTAL_FEE_360D,
                      CommonInfoAttr::USER_MERCHANT_PAY_ABILITY_ORDER_PRICE_MEDIAN_30D,
                      CommonInfoAttr::USER_MERCHANT_PAY_ABILITY_ORDER_PRICE_MEDIAN_90D,
                      CommonInfoAttr::USER_MERCHANT_PAY_ABILITY_ORDER_PRICE_MEDIAN_360D,
                      CommonInfoAttr::USER_MERCHANT_PAY_ABILITY_ORDER_PRICE_AVG_30D,
                      CommonInfoAttr::USER_MERCHANT_PAY_ABILITY_ORDER_PRICE_AVG_90D,
                      CommonInfoAttr::USER_MERCHANT_PAY_ABILITY_ORDER_PRICE_AVG_360D));
  auto x1 = std::get<0>(multi_attr1);
  auto x5 = std::get<1>(multi_attr1);
  auto x9 = std::get<2>(multi_attr1);
  auto x13 = std::get<3>(multi_attr1);
  auto x17 = std::get<4>(multi_attr1);
  auto x21 = std::get<5>(multi_attr1);
  auto x25 = std::get<6>(multi_attr1);
  auto x29 = std::get<7>(multi_attr1);
  auto x33 = std::get<8>(multi_attr1);
  auto x37 = std::get<9>(multi_attr1);
  auto x41 = std::get<10>(multi_attr1);
  auto x45 = std::get<11>(multi_attr1);

  auto x2 = cast_to_float(x1);
  auto x3 = log_plus_1_e(x2);
  auto x4 = cast_to_float(x3);
  auto x6 = cast_to_float(x5);
  auto x7 = log_plus_1_e(x6);
  auto x8 = cast_to_float(x7);
  auto x10 = cast_to_float(x9);
  auto x11 = log_plus_1_e(x10);
  auto x12 = cast_to_float(x11);
  auto x14 = cast_to_float(x13);
  auto x15 = log_plus_1_e(x14);
  auto x16 = cast_to_float(x15);
  auto x18 = cast_to_float(x17);
  auto x19 = log_plus_1_e(x18);
  auto x20 = cast_to_float(x19);
  auto x22 = cast_to_float(x21);
  auto x23 = log_plus_1_e(x22);
  auto x24 = cast_to_float(x23);
  auto x26 = cast_to_float(x25);
  auto x27 = log_plus_1_e(x26);
  auto x28 = cast_to_float(x27);
  auto x30 = cast_to_float(x29);
  auto x31 = log_plus_1_e(x30);
  auto x32 = cast_to_float(x31);
  auto x34 = cast_to_float(x33);
  auto x35 = log_plus_1_e(x34);
  auto x36 = cast_to_float(x35);
  auto x38 = cast_to_float(x37);
  auto x39 = log_plus_1_e(x38);
  auto x40 = cast_to_float(x39);
  auto x42 = cast_to_float(x41);
  auto x43 = log_plus_1_e(x42);
  auto x44 = cast_to_float(x43);
  auto x46 = cast_to_float(x45);
  auto x47 = log_plus_1_e(x46);
  auto x48 = cast_to_float(x47);
  auto x49 = merge_float_list_all(x4, x8, x12, x16, x20, x24, x28, x32, x36, x40, x44, x48);
  add_feature_result(x49, 12, result);
}

}  // namespace ad_algorithm
}  // namespace ks
