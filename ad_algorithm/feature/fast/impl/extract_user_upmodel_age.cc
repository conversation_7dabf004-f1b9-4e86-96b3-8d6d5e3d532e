#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_age.h"
namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelAge::Extract(const AdLog& adlog, size_t pos,
                                    std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_ad_user_info()) {
    const auto& ad_user_info_age =
        GetFeature(FeaturePrefix::UP_USER_AGE, adlog.user_info().ad_user_info().age());
    AddFeature(ad_user_info_age, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
