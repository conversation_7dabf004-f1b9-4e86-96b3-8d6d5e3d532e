#pragma once
#include <vector>
#include <string>
#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

class ExtractUserUniverseFakeInfo : public FastFeature {
 public:
  ExtractUserUniverseFakeInfo()
    : FastFeature(FeatureType::USER) {
  }
  virtual void Extract(const AdLog& adlog,
                       size_t pos,
                       std::vector<ExtractResult>* result) {
      if (adlog.has_user_info()) {
          auto& user_info = adlog.user_info();
          if (user_info.has_ad_user_info()) {
              auto& ad_user_info = user_info.ad_user_info();
              if (ad_user_info.has_is_universe_fake_user()) {
                uint64_t id = GetFeature(USER_UNIVERSE_FAKE_INFO,
                                         static_cast<int>(ad_user_info.is_universe_fake_user()));
                AddFeature(id, 1.0, result);
              }
          }
    }
  }
 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserUniverseFakeInfo);
  const std::string USED_FEATURES[1] = {
    "user_info.ad_user_info.is_universe_fake_user"
  };
};

REGISTER_EXTRACTOR(ExtractUserUniverseFakeInfo);

}  // namespace ad_algorithm
}  // namespace ks

