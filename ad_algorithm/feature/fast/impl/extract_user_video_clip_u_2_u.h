#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_content_retrieve_ids.dark
class ExtractUserVideoClipU2U : public FastFeatureNoPrefix {
 public:
  ExtractUserVideoClipU2U();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserVideoClipU2U);
};

REGISTER_EXTRACTOR(ExtractUserVideoClipU2U);
}  // namespace ad_algorithm
}  // namespace ks
