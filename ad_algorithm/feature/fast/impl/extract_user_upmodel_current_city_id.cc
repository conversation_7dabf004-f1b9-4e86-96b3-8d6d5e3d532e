#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_current_city_id.h"
namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelCurrentCityId::Extract(const AdLog& adlog, size_t pos,
                                              std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_current_loc()) {
    const auto& current_loc_city_id =
        GetFeature(FeaturePrefix::UP_USER_CURRENT_LOC_CITY_ID,
                   adlog.user_info().current_loc().city_id());
    AddFeature(current_loc_city_id, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
