#pragma once

#include <string>
#include <vector>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/log_preprocess/ad_model_kconf_util.h"

namespace ks {
namespace ad_algorithm {

class ExtractUserUniverseIndustryStatsOnline : public FastFeature {
 public:
  static const uint32 MAX_FEATURE_NUMS = 4;
  ExtractUserUniverseIndustryStatsOnline() : FastFeature(FeatureType::USER) {}

  virtual void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
    if (pos >= adlog.item_size()) {
      return;
    }
    uint32 nums = 0;

       // 曝光 点击 cvr ctr
    if (adlog.has_user_info() && adlog.user_info().common_info_attr_size() > 0) {
      for (const ::kuaishou::ad::CommonInfoAttr& userAttr : adlog.user_info().common_info_attr()) {
        if (userAttr.name_value() == ::kuaishou::ad::CommonInfoAttr_Name_USER_UNIVERSE_IMP_TAG) {
          ++nums;
          for(const auto &kv : userAttr.map_int64_float_value()){ //MAP_INT64_FLOAT_ATTR
            AddFeature(GetFeature(FeaturePrefix::UNIVERSE_USER_INDUSTRY_SUM_ITEM_IMPRESSION,(int)(kv.second * 100), kv.first), 1.0f, result);
          }
        }
        if (userAttr.name_value() == ::kuaishou::ad::CommonInfoAttr_Name_USER_UNIVERSE_CLICK_TAG) {
          ++nums;
          for(const auto &kv : userAttr.map_int64_float_value()){
            AddFeature(GetFeature(FeaturePrefix::UNIVERSE_USER_INDUSTRY_SUM_ITEM_CLICK,(int)(kv.second * 100), kv.first), 1.0f, result);
          }
        }
        if (userAttr.name_value() == ::kuaishou::ad::CommonInfoAttr_Name_USER_UNIVERSE_CVR_TAG) {
          ++nums;
          for(const auto &kv : userAttr.map_int64_float_value()){
            AddFeature(GetFeature(FeaturePrefix::UNIVERSE_USER_INDUSTRY_CVR,(int)(kv.second * 100000), kv.first), 1.0f, result);
          }
        }
        if (userAttr.name_value() == ::kuaishou::ad::CommonInfoAttr_Name_USER_UNIVERSE_CTR_TAG) {
          ++nums;
          for(const auto &kv : userAttr.map_int64_float_value()){
            AddFeature(GetFeature(FeaturePrefix::UNIVERSE_USER_INDUSTRY_CTR,(int)(kv.second * 100000), kv.first), 1.0f, result);
          }
        }
        //if (nums == MAX_FEATURE_NUMS )
        //  break;
      }
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserUniverseIndustryStatsOnline);
  const std::string USED_FEATURES[4] = {
    "user_info.common_info_attr.USER_UNIVERSE_IMP_TAG",
    "user_info.common_info_attr.USER_UNIVERSE_CLICK_TAG",
    "user_info.common_info_attr.USER_UNIVERSE_CVR_TAG",
    "user_info.common_info_attr.USER_UNIVERSE_CTR_TAG"
  };
};

REGISTER_EXTRACTOR(ExtractUserUniverseIndustryStatsOnline);

}  // namespace ad_algorithm
}  // namespace ks
