#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_is_work.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelIsWork::Extract(const AdLog& adlog, size_t pos,
                                       std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_ad_user_info()) {
    const auto& ad_user_info_is_work = GetFeature(
        FeaturePrefix::UP_USER_IS_WORK, adlog.user_info().ad_user_info().is_work());
    AddFeature(ad_user_info_is_work, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
