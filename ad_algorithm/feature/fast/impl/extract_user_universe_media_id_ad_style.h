#pragma once
#include <vector>
#include <string>
#include "base/strings/string_printf.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

class ExtractUserUniverseMediaIdAdStyle : public FastFeature {
 public:
  ExtractUserUniverseMediaIdAdStyle()
    : FastFeature(FeatureType::USER) {
  }

  
  virtual void Extract(const AdLog& adlog,
                       size_t pos,
                       std::vector<ExtractResult>* result) {
    std::hash<std::string> hash_fn;                    
    uint64 media_id_hash = hash_fn(adlog.context().app_id());

    uint64 ad_style = 0;
    if (adlog.has_context() && adlog.context().info_common_attr_size() > 0) {
      for (auto &attr : adlog.context().info_common_attr()) {
        if (attr.name_value() == ::kuaishou::ad::ContextInfoCommonAttr::AD_STYLE) {
          // AD_STYLE = 6, 中台广告位样式, 1: 信息流、2: 激励视频、3：插屏、4：开屏、5：banner
          ad_style = attr.int_value();
          break;
        }
      }
    }
    uint64_t id = GetFeature(USER_UN_MEDIA_ID_AD_STYLE, media_id_hash, ad_style);
    AddFeature(id, 1.0, result);  
  }
 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserUniverseMediaIdAdStyle);
};

REGISTER_EXTRACTOR(ExtractUserUniverseMediaIdAdStyle);

}  // namespace ad_algorithm
}  // namespace ks

