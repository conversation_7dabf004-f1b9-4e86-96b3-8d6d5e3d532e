#pragma once
#include <string>
#include <fstream>
#include <iostream>
#include <vector>
#include <unordered_map>
 #include <algorithm>
#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"



namespace ks {namespace ad_algorithm {

class ExtractUserVisionFeatureMocoImpThanos : public FastFeature {
 public:
  ExtractUserVisionFeatureMocoImpThanos()
      :FastFeature(DENSE_USER) {
  }
  virtual void Extract(const AdLog & adlog, size_t pos, std::vector<ExtractResult>* result) {
    if (adlog.has_user_info()) {
      for (const ::kuaishou::ad::CommonInfoAttr& attr : adlog.user_info().common_info_attr()) {
        if (attr.name_value() ==
         ::kuaishou::ad::CommonInfoAttr_Name_USER_THANOS_PHOTO_IMPRESSION_VIDEO_EMBS) {
          if (attr.float_list_value_size() != 256) {
            return;
          }
          for (size_t i = 0; i < attr.float_list_value_size(); ++i) {
            AddFeature(i, std::min(attr.float_list_value(i), 10.0f), result);
          }
          break;
        }
      }
    }
  }
 private:
  const std::string USED_FEATURES[1] = {"user_info.common_info_attr.USER_THANOS_PHOTO_IMPRESSION_VIDEO_EMBS"};
  DISALLOW_COPY_AND_ASSIGN(ExtractUserVisionFeatureMocoImpThanos);
};

REGISTER_EXTRACTOR(ExtractUserVisionFeatureMocoImpThanos);

}  // namespace ad_algorithm
}  // namespace ks
