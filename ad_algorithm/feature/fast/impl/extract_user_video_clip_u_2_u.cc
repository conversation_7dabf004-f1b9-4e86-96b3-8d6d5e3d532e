/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_video_clip_u_2_u.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

ExtractUserVideoClipU2U::ExtractUserVideoClipU2U() : FastFeatureNoPrefix(FeatureType::USER) {}
void ExtractUserVideoClipU2U::Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 5100705);
  add_feature_result(x1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
