#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_fans_count.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelFansCount::Extract(const AdLog& adlog, size_t pos,
                                          std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_attribute()) {
    const auto& attribute_fans_count = GetFeature(
        FeaturePrefix::UP_USER_FANS_COUNT, adlog.user_info().attribute().fans_count());
    AddFeature(attribute_fans_count, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
