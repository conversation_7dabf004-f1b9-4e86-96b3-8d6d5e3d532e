#pragma once
#include <string>
#include <vector>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
namespace ks {
namespace ad_algorithm {
class ExtractUserUPModelIsWork : public FastFeature {
 public:
  ExtractUserUPModelIsWork():FastFeature(FeatureType::USER) {}
  virtual void Extract(const AdLog& adlog,
                       size_t pos,
                       std::vector<ExtractResult>* result);
 private:
  const std::string  USED_FEATURES[1] = {
    "user_info.ad_user_info.is_work"
  };
  DISALLOW_COPY_AND_ASSIGN(ExtractUserUPModelIsWork);
};
REGISTER_EXTRACTOR(ExtractUserUPModelIsWork);
}  // namespace ad_algorithm
}  // namespace ks
