#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/impl/extract_user_sparse_outer_native_ad_list_rank_candidates_sort_no_prefix.dark
class ExtractUserSparseNativeHardRankCandidatesAccountIdSortByEnsembleNoPrefix : public FastFeatureNoPrefix {
 public:
  ExtractUserSparseNativeHardRankCandidatesAccountIdSortByEnsembleNoPrefix();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserSparseNativeHardRankCandidatesAccountIdSortByEnsembleNoPrefix);
};

REGISTER_EXTRACTOR(ExtractUserSparseNativeHardRankCandidatesAccountIdSortByEnsembleNoPrefix);
}  // namespace ad_algorithm
}  // namespace ks
