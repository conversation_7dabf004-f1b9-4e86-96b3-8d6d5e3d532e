#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_current_poi_type.h"
namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelCurrentPoiType::Extract(const AdLog& adlog, size_t pos,
                                               std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_current_loc()) {
    const auto& current_loc_poi_type =
        GetFeature(FeaturePrefix::UP_USER_CURRENT_LOC_POI_TYPE,
                   adlog.user_info().current_loc().poi_type());
    AddFeature(current_loc_poi_type, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
