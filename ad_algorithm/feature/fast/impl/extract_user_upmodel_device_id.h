#pragma once
#include <string>
#include <vector>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
namespace ks {
namespace ad_algorithm {
class ExtractUserUPModelDeviceId : public FastFeature {
 public:
  ExtractUserUPModelDeviceId():FastFeature(FeatureType::USER) {}
  virtual void Extract(const AdLog& adlog,
                       size_t pos,
                       std::vector<ExtractResult>* result);
 private:
  const std::string  USED_FEATURES[1] = {
    "user_info.device_info.id"
  };
  DISALLOW_COPY_AND_ASSIGN(ExtractUserUPModelDeviceId);
};
REGISTER_EXTRACTOR(ExtractUserUPModelDeviceId);
}  // namespace ad_algorithm
}  // namespace ks
