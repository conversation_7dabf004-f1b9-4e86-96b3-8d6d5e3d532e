#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_long_poi_type.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelLongPoiType::Extract(const AdLog& adlog, size_t pos,
                                            std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_long_term_loc()) {
    const auto& long_term_poi_type = GetFeature(
        FeaturePrefix::UP_USER_POI_TYPE, adlog.user_info().long_term_loc().poi_type());
    AddFeature(long_term_poi_type, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
