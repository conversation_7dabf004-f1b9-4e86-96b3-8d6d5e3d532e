#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_combine_user_author_list.dark
class ExtractUserMerchantSeqLengthDense : public FastFeature {
 public:
  ExtractUserMerchantSeqLengthDense();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserMerchantSeqLengthDense);
};

REGISTER_EXTRACTOR(ExtractUserMerchantSeqLengthDense);
}  // namespace ad_algorithm
}  // namespace ks
