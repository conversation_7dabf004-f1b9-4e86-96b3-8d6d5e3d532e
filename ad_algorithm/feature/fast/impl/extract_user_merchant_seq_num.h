#pragma once
#include <map>
#include <string>
#include <unordered_set>
#include <vector>

#include "ks/reco_proto/proto/user_profile.pb.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

template <MerchantSeqNumTypeV1 seq_type, int max_len>
class ExtractUserMerchantSeqNumV1 : public FastFeature {
 public:
  ExtractUserMerchantSeqNumV1() : FastFeature(FeatureType::DENSE_USER) {
    if (seq_type == MerchantSeqNumTypeV1::BUY) {
      merchant_seq_common_info_attr_set_.insert(
          ::kuaishou::ad::
              CommonInfoAttr_Name_USER_FANSTOP_MERCHANT_COMMODITY_ITEM_ID_SEQ);
      merchant_seq_common_info_attr_set_.insert(
          ::kuaishou::ad::
              CommonInfoAttr_Name_USER_FANSTOP_MERCHANT_COMMODITY_ENTITY_TAG_SEQ);
      merchant_seq_common_info_attr_set_.insert(
          ::kuaishou::ad::
              CommonInfoAttr_Name_USER_FANSTOP_MERCHANT_COMMODITY_TIMESTAMP_SEQ);
      merchant_seq_common_info_attr_set_.insert(
          ::kuaishou::ad::
              CommonInfoAttr_NameExtendOne_MERCHANT_BUY_SELLER_ID_LIST);
      merchant_seq_common_info_attr_set_.insert(
          ::kuaishou::ad::
              CommonInfoAttr_NameExtendOne_MERCHANT_BUY_COMMODITY_TOP28_LEVEL1_TAG_LIST);
      merchant_seq_common_info_attr_set_.insert(
          ::kuaishou::ad::
              CommonInfoAttr_NameExtendOne_MERCHANT_BUY_COMMODITY_TOP28_LEVEL2_TAG_LIST);
      merchant_seq_common_info_attr_set_.insert(
          ::kuaishou::ad::
              CommonInfoAttr_NameExtendOne_MERCHANT_BUY_COMMODITY_TOP28_LEVEL3_TAG_LIST);
    } else if (seq_type == MerchantSeqNumTypeV1::CLICK) {
      merchant_seq_common_info_attr_set_.insert(
          ::kuaishou::ad::
              CommonInfoAttr_NameExtendOne_MERCHANT_DETAIL_PAGE_VIEW_ITEM_ID_LIST);
      merchant_seq_common_info_attr_set_.insert(
          ::kuaishou::ad::
              CommonInfoAttr_NameExtendOne_MERCHANT_DETAIL_PAGE_VIEW_ENTITY_TAG_LIST);
      merchant_seq_common_info_attr_set_.insert(
          ::kuaishou::ad::
              CommonInfoAttr_NameExtendOne_MERCHANT_DETAIL_PAGE_VIEW_TIMESTAMP_LIST);
      merchant_seq_common_info_attr_set_.insert(
          ::kuaishou::ad::
              CommonInfoAttr_NameExtendOne_MERCHANT_SHOW_SELLER_ID_LIST);
      merchant_seq_common_info_attr_set_.insert(
          ::kuaishou::ad::
              CommonInfoAttr_NameExtendOne_MERCHANT_SHOW_COMMODITY_TOP28_LEVEL1_TAG_LIST);
      merchant_seq_common_info_attr_set_.insert(
          ::kuaishou::ad::
              CommonInfoAttr_NameExtendOne_MERCHANT_SHOW_COMMODITY_TOP28_LEVEL2_TAG_LIST);
      merchant_seq_common_info_attr_set_.insert(
          ::kuaishou::ad::
              CommonInfoAttr_NameExtendOne_MERCHANT_SHOW_COMMODITY_TOP28_LEVEL3_TAG_LIST);
    }
  }

  virtual void Extract(const AdLog& adlog, size_t pos,
                       std::vector<ExtractResult>* result) {
    int seq_num = GetSeqList(adlog) / 3;
    if (seq_num > max_len) {
      seq_num = max_len;
    }
    AddFeature(0, (float)seq_num, result);
  }

 private:
  std::unordered_set<int64> merchant_seq_common_info_attr_set_;

  int GetSeqList(const AdLog& adlog) {
    if (adlog.has_user_info() &&
        adlog.user_info().common_info_attr_size() > 0) {
      const auto& common_infos = adlog.user_info().common_info_attr();

      std::map<int64, int> merchant_seq_result;

      for (const auto& userAttr : common_infos) {
        if (merchant_seq_common_info_attr_set_.find(userAttr.name_value()) !=
            merchant_seq_common_info_attr_set_.end()) {
          merchant_seq_result[userAttr.name_value()] =
              userAttr.int_list_value_size();
        }
        if (merchant_seq_result.size() ==
            merchant_seq_common_info_attr_set_.size()) {
          break;
        }
      }
      int merchant_seq_list_size = 0;

      if (merchant_seq_result.size() ==
          merchant_seq_common_info_attr_set_.size()) {
        bool merchant_seq_list_equal = true;

        for (auto& item : merchant_seq_result) {
          if (merchant_seq_list_size == 0) {
            merchant_seq_list_size = item.second;
          } else {
            if (item.second != merchant_seq_list_size) {
              merchant_seq_list_equal = false;
              break;
            }
          }
        }
        if (!merchant_seq_list_equal || merchant_seq_list_size % 3 != 0) {
          merchant_seq_list_size = 0;
        }
      }
      return merchant_seq_list_size;
    }
    return 0;
  }

  DISALLOW_COPY_AND_ASSIGN(ExtractUserMerchantSeqNumV1);
};

using ExtractUserMerchantBuyNum10 =
    ExtractUserMerchantSeqNumV1<MerchantSeqNumTypeV1::BUY, 10>;
using ExtractUserMerchantBuyNum20 =
    ExtractUserMerchantSeqNumV1<MerchantSeqNumTypeV1::BUY, 20>;
using ExtractUserMerchantBuyNum30 =
    ExtractUserMerchantSeqNumV1<MerchantSeqNumTypeV1::BUY, 30>;
using ExtractUserMerchantBuyNum50 =
    ExtractUserMerchantSeqNumV1<MerchantSeqNumTypeV1::BUY, 50>;
using ExtractUserMerchantBuyNum100 =
    ExtractUserMerchantSeqNumV1<MerchantSeqNumTypeV1::BUY, 100>;
using ExtractUserMerchantBuyNum200 =
    ExtractUserMerchantSeqNumV1<MerchantSeqNumTypeV1::BUY, 200>;

using ExtractUserMerchantClickNum20 =
    ExtractUserMerchantSeqNumV1<MerchantSeqNumTypeV1::CLICK, 20>;
using ExtractUserMerchantClickNum30 =
    ExtractUserMerchantSeqNumV1<MerchantSeqNumTypeV1::CLICK, 30>;
using ExtractUserMerchantClickNum50 =
    ExtractUserMerchantSeqNumV1<MerchantSeqNumTypeV1::CLICK, 50>;
using ExtractUserMerchantClickNum100 =
    ExtractUserMerchantSeqNumV1<MerchantSeqNumTypeV1::CLICK, 100>;
using ExtractUserMerchantClickNum200 =
    ExtractUserMerchantSeqNumV1<MerchantSeqNumTypeV1::CLICK, 200>;

REGISTER_EXTRACTOR(ExtractUserMerchantBuyNum10);
REGISTER_EXTRACTOR(ExtractUserMerchantBuyNum20);
REGISTER_EXTRACTOR(ExtractUserMerchantBuyNum30);
REGISTER_EXTRACTOR(ExtractUserMerchantBuyNum50);
REGISTER_EXTRACTOR(ExtractUserMerchantBuyNum100);
REGISTER_EXTRACTOR(ExtractUserMerchantBuyNum200);

REGISTER_EXTRACTOR(ExtractUserMerchantClickNum20);
REGISTER_EXTRACTOR(ExtractUserMerchantClickNum30);
REGISTER_EXTRACTOR(ExtractUserMerchantClickNum50);
REGISTER_EXTRACTOR(ExtractUserMerchantClickNum100);
REGISTER_EXTRACTOR(ExtractUserMerchantClickNum200);

}  // namespace ad_algorithm
}  // namespace ks
