#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_current_ip.h"
namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelCurrentIp::Extract(const AdLog& adlog, size_t pos,
                                          std::vector<ExtractResult>* result) {
  std::hash<std::string> hash_fn;
  if (adlog.has_user_info() && adlog.user_info().has_current_loc()) {
    const auto& current_loc_ip =
        GetFeature(FeaturePrefix::UP_USER_CURRENT_LOC_IP,
                   hash_fn(adlog.user_info().current_loc().ip()));
    AddFeature(current_loc_ip, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
