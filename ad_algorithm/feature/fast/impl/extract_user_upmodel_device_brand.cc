#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_device_brand.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelDeviceBrand::Extract(const AdLog& adlog, size_t pos,
                                            std::vector<ExtractResult>* result) {
  std::hash<std::string> hash_fn;
  if (adlog.has_user_info() && adlog.user_info().has_ad_user_info()) {
    auto& device_info_list = adlog.user_info().ad_user_info().device_info();
    for (auto& device_info : device_info_list) {
      auto& brand = device_info.brand();
      std::string curr = brand.data();
      const auto& attribute_brand =
          GetFeature(FeaturePrefix::UP_USER_DEVICE_BRAND, hash_fn(curr));
      AddFeature(attribute_brand, 1.0f, result);
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
