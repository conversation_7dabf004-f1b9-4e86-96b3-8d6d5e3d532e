#pragma once
#include <string>
#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

template <MerchantSeqTypeV1 seq_type, MerchantSeqDataTypeV1 seq_data_type>
class ExtractUserMerchantSeqBaseV1 : public FastFeatureNoPrefix {
 public:
  ExtractUserMerchantSeqBaseV1() : FastFeatureNoPrefix(FeatureType::USER) {
    feature_index_ = 0;
  }

  explicit ExtractUserMerchantSeqBaseV1(size_t index)
      : FastFeatureNoPrefix(FeatureType::USER) {
    feature_index_ = index;
  }

  virtual void Extract(const AdLog& adlog, size_t pos,
                       std::vector<ExtractResult>* result) {
    const auto seq_list_ptr = GetSeqList(adlog);
    if (seq_list_ptr != nullptr) {
      const auto& seq_list = *seq_list_ptr;
      int seq_num = seq_list.size();
      if (seq_num > 0 && seq_num % 3 == 0 && feature_index_ * 3 < seq_num) {
        int begin_index = seq_num - feature_index_ * 3;
        if (seq_data_type == MerchantSeqDataTypeV1::TIMESTAMP &&
            begin_index - 3 >= 0) {
          AddFeature(
              TimeMsTransform(adlog.Get().time() - seq_list[begin_index - 3]),
              1.0, result);
        } else {
          for (int i = begin_index - 3; i < begin_index && i >= 0; i++) {
            if (seq_list[i] > 0) {
              AddFeature(seq_list[i], 1.0, result);
            }
          }
        }
      }
    }
  }

 private:
  // 时间特征按天分段离散化
  int TimeMsTransform(uint64_t time_ms) {
    uint64_t days = time_ms / 1000 / 3600 / 24;
    int i = 0;
    for (auto interval : intervals_) {
      if (days < interval) {
        return i;
      }
      ++i;
    }
    return i;
  }

 private:
  size_t feature_index_;
  std::vector<int> intervals_ = {1, 2, 3, 7, 15, 30, 60, 90, 180, 360};

  const ::google::protobuf::RepeatedField<::google::protobuf::int64>*
  GetSeqList(const AdLog& adlog) {
    if (adlog.has_user_info() &&
        adlog.user_info().common_info_attr_size() > 0) {
      const auto& common_infos = adlog.user_info().common_info_attr();
      const ::google::protobuf::RepeatedField<::google::protobuf::int64>*
          item_id_seq_list = nullptr;
      const ::google::protobuf::RepeatedField<::google::protobuf::int64>*
          entity_tag_seq_list = nullptr;
      const ::google::protobuf::RepeatedField<::google::protobuf::int64>*
          timestamp_seq_list = nullptr;
      const ::google::protobuf::RepeatedField<::google::protobuf::int64>*
          seller_id_seq_list = nullptr;
      const ::google::protobuf::RepeatedField<::google::protobuf::int64>*
          mmu_top28_first_cate_seq_list = nullptr;
      const ::google::protobuf::RepeatedField<::google::protobuf::int64>*
          mmu_top28_second_cate_seq_list = nullptr;
      const ::google::protobuf::RepeatedField<::google::protobuf::int64>*
          mmu_top28_third_cate_seq_list = nullptr;

      int valid_seq_size = 0;

      if (seq_type == MerchantSeqTypeV1::BUY) {
        for (const auto& userAttr : common_infos) {
          if (userAttr.name_value() ==
              ::kuaishou::ad::
                  CommonInfoAttr_Name_USER_FANSTOP_MERCHANT_COMMODITY_ITEM_ID_SEQ) {
            item_id_seq_list = &userAttr.int_list_value();
            valid_seq_size += 1;
          } else if (
              userAttr.name_value() ==
              ::kuaishou::ad::
                  CommonInfoAttr_Name_USER_FANSTOP_MERCHANT_COMMODITY_ENTITY_TAG_SEQ) {
            entity_tag_seq_list = &userAttr.int_list_value();
            valid_seq_size += 1;
          } else if (
              userAttr.name_value() ==
              ::kuaishou::ad::
                  CommonInfoAttr_Name_USER_FANSTOP_MERCHANT_COMMODITY_TIMESTAMP_SEQ) {
            timestamp_seq_list = &userAttr.int_list_value();
            valid_seq_size += 1;
          } else if (
              userAttr.name_value() ==
              ::kuaishou::ad::
                  CommonInfoAttr_NameExtendOne_MERCHANT_BUY_SELLER_ID_LIST) {
            seller_id_seq_list = &userAttr.int_list_value();
            valid_seq_size += 1;
          } else if (
              userAttr.name_value() ==
              ::kuaishou::ad::
                  CommonInfoAttr_NameExtendOne_MERCHANT_BUY_COMMODITY_TOP28_LEVEL1_TAG_LIST) {
            mmu_top28_first_cate_seq_list = &userAttr.int_list_value();
            valid_seq_size += 1;
          } else if (
              userAttr.name_value() ==
              ::kuaishou::ad::
                  CommonInfoAttr_NameExtendOne_MERCHANT_BUY_COMMODITY_TOP28_LEVEL2_TAG_LIST) {
            mmu_top28_second_cate_seq_list = &userAttr.int_list_value();
            valid_seq_size += 1;
          } else if (
              userAttr.name_value() ==
              ::kuaishou::ad::
                  CommonInfoAttr_NameExtendOne_MERCHANT_BUY_COMMODITY_TOP28_LEVEL3_TAG_LIST) {
            mmu_top28_third_cate_seq_list = &userAttr.int_list_value();
            valid_seq_size += 1;
          } else {
            if (valid_seq_size == 7 && item_id_seq_list != nullptr &&
                entity_tag_seq_list != nullptr &&
                timestamp_seq_list != nullptr &&
                seller_id_seq_list != nullptr &&
                mmu_top28_first_cate_seq_list != nullptr &&
                mmu_top28_second_cate_seq_list != nullptr &&
                mmu_top28_third_cate_seq_list != nullptr) {
              break;
            }
          }
        }
      } else if (seq_type == MerchantSeqTypeV1::CLICK) {
        for (const auto& userAttr : common_infos) {
          if (userAttr.name_value() ==
              ::kuaishou::ad::
                  CommonInfoAttr_NameExtendOne_MERCHANT_DETAIL_PAGE_VIEW_ITEM_ID_LIST) {
            item_id_seq_list = &userAttr.int_list_value();
            valid_seq_size += 1;

          } else if (
              userAttr.name_value() ==
              ::kuaishou::ad::
                  CommonInfoAttr_NameExtendOne_MERCHANT_DETAIL_PAGE_VIEW_ENTITY_TAG_LIST) {
            entity_tag_seq_list = &userAttr.int_list_value();
            valid_seq_size += 1;

          } else if (
              userAttr.name_value() ==
              ::kuaishou::ad::
                  CommonInfoAttr_NameExtendOne_MERCHANT_DETAIL_PAGE_VIEW_TIMESTAMP_LIST) {
            timestamp_seq_list = &userAttr.int_list_value();
            valid_seq_size += 1;

          } else if (
              userAttr.name_value() ==
              ::kuaishou::ad::
                  CommonInfoAttr_NameExtendOne_MERCHANT_SHOW_SELLER_ID_LIST) {
            seller_id_seq_list = &userAttr.int_list_value();
            valid_seq_size += 1;
          } else if (
              userAttr.name_value() ==
              ::kuaishou::ad::
                  CommonInfoAttr_NameExtendOne_MERCHANT_SHOW_COMMODITY_TOP28_LEVEL1_TAG_LIST) {
            mmu_top28_first_cate_seq_list = &userAttr.int_list_value();
            valid_seq_size += 1;
          } else if (
              userAttr.name_value() ==
              ::kuaishou::ad::
                  CommonInfoAttr_NameExtendOne_MERCHANT_SHOW_COMMODITY_TOP28_LEVEL2_TAG_LIST) {
            mmu_top28_second_cate_seq_list = &userAttr.int_list_value();
            valid_seq_size += 1;
          } else if (
              userAttr.name_value() ==
              ::kuaishou::ad::
                  CommonInfoAttr_NameExtendOne_MERCHANT_SHOW_COMMODITY_TOP28_LEVEL3_TAG_LIST) {
            mmu_top28_third_cate_seq_list = &userAttr.int_list_value();
            valid_seq_size += 1;
          } else {
            if (valid_seq_size == 7 && item_id_seq_list != nullptr &&
                entity_tag_seq_list != nullptr &&
                timestamp_seq_list != nullptr &&
                seller_id_seq_list != nullptr &&
                mmu_top28_first_cate_seq_list != nullptr &&
                mmu_top28_second_cate_seq_list != nullptr &&
                mmu_top28_third_cate_seq_list != nullptr) {
              break;
            }
          }
        }
      }
      if (item_id_seq_list == nullptr || entity_tag_seq_list == nullptr ||
          timestamp_seq_list == nullptr || seller_id_seq_list == nullptr ||
          mmu_top28_first_cate_seq_list == nullptr ||
          mmu_top28_second_cate_seq_list == nullptr ||
          mmu_top28_third_cate_seq_list == nullptr) {
        return nullptr;
      }
      if (item_id_seq_list->size() != entity_tag_seq_list->size() ||
          item_id_seq_list->size() != timestamp_seq_list->size() ||
          item_id_seq_list->size() != seller_id_seq_list->size() ||
          item_id_seq_list->size() != mmu_top28_first_cate_seq_list->size() ||
          item_id_seq_list->size() != mmu_top28_second_cate_seq_list->size() ||
          item_id_seq_list->size() != mmu_top28_third_cate_seq_list->size() ||
          item_id_seq_list->size() == 0 || item_id_seq_list->size() % 3 != 0) {
        return nullptr;
      }

      if (seq_data_type == MerchantSeqDataTypeV1::ITEM_ID) {
        return item_id_seq_list;
      } else if (seq_data_type == MerchantSeqDataTypeV1::ENTITY_TAG) {
        return entity_tag_seq_list;
      } else if (seq_data_type == MerchantSeqDataTypeV1::TIMESTAMP) {
        return timestamp_seq_list;
      } else if (seq_data_type == MerchantSeqDataTypeV1::SELLER_ID) {
        return seller_id_seq_list;
      } else if (seq_data_type == MerchantSeqDataTypeV1::MMU_TOP28_FIRST) {
        return mmu_top28_first_cate_seq_list;
      } else if (seq_data_type == MerchantSeqDataTypeV1::MMU_TOP28_SECOND) {
        return mmu_top28_second_cate_seq_list;
      } else if (seq_data_type == MerchantSeqDataTypeV1::MMU_TOP28_THIRD) {
        return mmu_top28_third_cate_seq_list;
      }
    }
    return nullptr;
  }

  DISALLOW_COPY_AND_ASSIGN(ExtractUserMerchantSeqBaseV1);
};

using ExtractUserMerchantBuyItemIdSlot =
    ExtractUserMerchantSeqBaseV1<MerchantSeqTypeV1::BUY,
                                 MerchantSeqDataTypeV1::ITEM_ID>;
using ExtractUserMerchantBuyItemEntityTagSlot =
    ExtractUserMerchantSeqBaseV1<MerchantSeqTypeV1::BUY,
                                 MerchantSeqDataTypeV1::ENTITY_TAG>;
using ExtractUserMerchantBuyItemTimestampSlot =
    ExtractUserMerchantSeqBaseV1<MerchantSeqTypeV1::BUY,
                                 MerchantSeqDataTypeV1::TIMESTAMP>;
using ExtractUserMerchantBuySellerIdSlot =
    ExtractUserMerchantSeqBaseV1<MerchantSeqTypeV1::BUY,
                                 MerchantSeqDataTypeV1::SELLER_ID>;
using ExtractUserMerchantBuyMMUTop28FirstCateSlot =
    ExtractUserMerchantSeqBaseV1<MerchantSeqTypeV1::BUY,
                                 MerchantSeqDataTypeV1::MMU_TOP28_FIRST>;
using ExtractUserMerchantBuyMMUTop28SecondCateSlot =
    ExtractUserMerchantSeqBaseV1<MerchantSeqTypeV1::BUY,
                                 MerchantSeqDataTypeV1::MMU_TOP28_SECOND>;
using ExtractUserMerchantBuyMMUTop28ThirdCateSlot =
    ExtractUserMerchantSeqBaseV1<MerchantSeqTypeV1::BUY,
                                 MerchantSeqDataTypeV1::MMU_TOP28_THIRD>;

using ExtractUserMerchantClickItemIdSlot =
    ExtractUserMerchantSeqBaseV1<MerchantSeqTypeV1::CLICK,
                                 MerchantSeqDataTypeV1::ITEM_ID>;
using ExtractUserMerchantClickItemEntityTagSlot =
    ExtractUserMerchantSeqBaseV1<MerchantSeqTypeV1::CLICK,
                                 MerchantSeqDataTypeV1::ENTITY_TAG>;
using ExtractUserMerchantClickItemTimestampSlot =
    ExtractUserMerchantSeqBaseV1<MerchantSeqTypeV1::CLICK,
                                 MerchantSeqDataTypeV1::TIMESTAMP>;

using ExtractUserMerchantClickSellerIdSlot =
    ExtractUserMerchantSeqBaseV1<MerchantSeqTypeV1::CLICK,
                                 MerchantSeqDataTypeV1::SELLER_ID>;
using ExtractUserMerchantClickMMUTop28FirstCateSlot =
    ExtractUserMerchantSeqBaseV1<MerchantSeqTypeV1::CLICK,
                                 MerchantSeqDataTypeV1::MMU_TOP28_FIRST>;
using ExtractUserMerchantClickMMUTop28SecondCateSlot =
    ExtractUserMerchantSeqBaseV1<MerchantSeqTypeV1::CLICK,
                                 MerchantSeqDataTypeV1::MMU_TOP28_SECOND>;
using ExtractUserMerchantClickMMUTop28ThirdCateSlot =
    ExtractUserMerchantSeqBaseV1<MerchantSeqTypeV1::CLICK,
                                 MerchantSeqDataTypeV1::MMU_TOP28_THIRD>;

REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantBuyItemIdSlot, 200);
REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantBuyItemEntityTagSlot, 200);
REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantBuyItemTimestampSlot, 200);
REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantBuySellerIdSlot, 200);
REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantBuyMMUTop28FirstCateSlot, 200);
REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantBuyMMUTop28SecondCateSlot, 200);
REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantBuyMMUTop28ThirdCateSlot, 200);

REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantClickItemIdSlot, 200);
REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantClickItemEntityTagSlot, 200);
REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantClickItemTimestampSlot, 200);
REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantClickSellerIdSlot, 200);
REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantClickMMUTop28FirstCateSlot, 200);
REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantClickMMUTop28SecondCateSlot,
                            200);
REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantClickMMUTop28ThirdCateSlot, 200);
}  // namespace ad_algorithm
}  // namespace ks
