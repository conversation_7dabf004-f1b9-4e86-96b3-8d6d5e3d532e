#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_has_house.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelHasHouse::Extract(const AdLog& adlog, size_t pos,
                                         std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_ad_user_info()) {
    const auto& ad_user_info_has_house = GetFeature(
        FeaturePrefix::UP_USER_HAS_HOUSE, adlog.user_info().ad_user_info().has_house());
    AddFeature(ad_user_info_has_house, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
