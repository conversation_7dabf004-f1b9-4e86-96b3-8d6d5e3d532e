/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_valid_action_semantic_id.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/uv_action_seq_util.h"

namespace ks {
namespace ad_algorithm {

ExtractUserUniverseValidActionSemanticId::ExtractUserUniverseValidActionSemanticId()
    : FastFeatureNoPrefix(FeatureType::USER) {}
void ExtractUserUniverseValidActionSemanticId::Extract(const AdLog& adlog, size_t pos,
                                                       std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 5104668);
  auto x2 = get_adlog_int64(adlog.user_info().common_info_attr(), 5104667);
  auto x3 = process_action_semantic_id_list(x1, x2);
  add_feature_result(x3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
