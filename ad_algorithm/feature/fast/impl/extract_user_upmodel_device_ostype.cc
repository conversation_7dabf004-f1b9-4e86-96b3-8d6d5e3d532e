#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_device_ostype.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelDeviceOstype::Extract(const AdLog& adlog, size_t pos,
                                             std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_ad_user_info()) {
    auto& device_info_list = adlog.user_info().ad_user_info().device_info();
    for (auto& device_info : device_info_list) {
      auto os_type = device_info.os_type();
      const auto& attribute_ostype =
          GetFeature(FeaturePrefix::UP_USER_DEVICE_OSTYPE, os_type);
      AddFeature(attribute_ostype, 1.0f, result);
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks