#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_region.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelRegion::Extract(const AdLog& adlog, size_t pos,
                                       std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_ad_user_info()) {
    const auto& ad_user_info_region = GetFeature(
        FeaturePrefix::UP_USER_REGION, adlog.user_info().ad_user_info().region());
    AddFeature(ad_user_info_region, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
