#pragma once
#include <string>
#include <vector>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"
namespace ks {
namespace ad_algorithm {

class ExtractUserMerchantNativeLiveClickSellerIdSequence: public FastFeatureNoPrefix {
 public:
  ExtractUserMerchantNativeLiveClickSellerIdSequence(): FastFeatureNoPrefix(FeatureType::USER) {
    feature_index_ = 0;
  }

  explicit ExtractUserMerchantNativeLiveClickSellerIdSequence(size_t index):
    FastFeatureNoPrefix(FeatureType::USER) {
    feature_index_ = index;
  }

  virtual void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
    if (adlog.has_user_info()) {
      for (const auto& attr : adlog.user_info().common_info_attr()) {
        if (attr.name_value() ==
          kuaishou::ad::CommonInfoAttr_Name_ECOM_BATCH_USER_GOODS_NATURAL_LIVE_CLICK_ITEM_SELLER_ID_LIST) {
          const auto& values = attr.int_list_value();
          int size = values.size();
          if (size > 0 && feature_index_ < size) {
            AddFeature(values.Get(feature_index_), 1.0, result);
          }
        }
      }
    }
  }

 private:
  size_t feature_index_;

  const std::string  USED_FEATURES[1] = {
    "user_info.common_info_attr.ECOM_BATCH_USER_GOODS_NATURAL_LIVE_CLICK_ITEM_SELLER_ID_LIST"
  };
  DISALLOW_COPY_AND_ASSIGN(ExtractUserMerchantNativeLiveClickSellerIdSequence);
};

REGISTER_SEQUENCE_EXTRACTOR(ExtractUserMerchantNativeLiveClickSellerIdSequence, 100);

}  // namespace ad_algorithm
}  // namespace ks

