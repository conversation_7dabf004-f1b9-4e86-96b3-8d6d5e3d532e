#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_user_universe_convert_action_sequences.dark
class ExtractUserUniverseConvertActionSeqMediumCooperationMode : public FastFeatureNoPrefix {
 public:
  ExtractUserUniverseConvertActionSeqMediumCooperationMode();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserUniverseConvertActionSeqMediumCooperationMode);
};

REGISTER_EXTRACTOR(ExtractUserUniverseConvertActionSeqMediumCooperationMode);
}  // namespace ad_algorithm
}  // namespace ks
