#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_medium_cooperation_mode_extend.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_multi_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/uv_action_seq_util.h"

namespace ks {
namespace ad_algorithm {

ExtractUserUniverseConvertActionSeqMediumCooperationModeExtend::
    ExtractUserUniverseConvertActionSeqMediumCooperationModeExtend()
    : FastFeatureNoPrefix(FeatureType::USER) {}
void ExtractUserUniverseConvertActionSeqMediumCooperationModeExtend::Extract(
    const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto multi_attr1 =
      get_adlog_multi<absl::Span<const int64_t>, absl::Span<const int64_t>, absl::Span<const int64_t>,
                      absl::Span<const int64_t>, absl::Span<const int64_t>, absl::Span<const int64_t>,
                      absl::Span<const int64_t>>(
          adlog.user_info().common_info_attr(),
          std::make_tuple(CommonInfoAttr::EVENT_FORM_SUBMIT_COOPERATION_MODE,
                          CommonInfoAttr::EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE,
                          CommonInfoAttr::EVENT_FORM_SUBMIT_TIMESTAMP_LIST,
                          CommonInfoAttr::EVENT_CONVERSION_COOPERATION_MODE,
                          CommonInfoAttr::EVNET_CONVERSION_MEDIUM_ATTRIBUTE,
                          CommonInfoAttr::EVENT_CONVERSION_TIMESTAMP_LIST,
                          CommonInfoAttr::UNIVERSE_AD_CONVERT_ACTION_SEQ_MEDIUM_COOPERATION_MODE));
  auto x1 = std::get<0>(multi_attr1);
  auto x2 = std::get<1>(multi_attr1);
  auto x3 = std::get<2>(multi_attr1);
  auto x8 = std::get<3>(multi_attr1);
  auto x9 = std::get<4>(multi_attr1);
  auto x10 = std::get<5>(multi_attr1);
  auto x18 = std::get<6>(multi_attr1);

  auto x5 = get_adlog_time(adlog);
  auto x7 = get_universe_seq_with_max_len_and_timestamp(x1, x2, x3, 30, x5, 86400000);
  auto x14 = get_universe_seq_with_max_len_and_timestamp(x8, x9, x10, 30, x5, 86400000);
  auto x17 = merge_two_seq_with_two_max_len(x7, x14, 2, 8);
  auto x21 = merge_two_seq_with_max_len(x17, x18, 10, 30);
  add_feature_result(x21, result);
}

}  // namespace ad_algorithm
}  // namespace ks
