#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_platform.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelPlatform::Extract(const AdLog& adlog, size_t pos,
                                         std::vector<ExtractResult>* result) {
  std::hash<std::string> hash_fn;
  if (adlog.has_user_info() && adlog.user_info().has_ad_user_info()) {
    const auto& ad_user_info_platform =
        GetFeature(FeaturePrefix::UP_USER_PLATFORM,
                   hash_fn(adlog.user_info().ad_user_info().platform()));
    AddFeature(ad_user_info_platform, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
