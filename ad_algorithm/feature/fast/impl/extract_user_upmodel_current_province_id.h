#pragma once
#include <string>
#include <vector>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
namespace ks {
namespace ad_algorithm {
class ExtractUserUPModelCurrentProvinceId : public FastFeature {
 public:
  ExtractUserUPModelCurrentProvinceId():FastFeature(FeatureType::USER) {}
  virtual void Extract(const AdLog& adlog,
                       size_t pos,
                       std::vector<ExtractResult>* result);
 private:
  const std::string  USED_FEATURES[1] = {
    "user_info.current_loc.province_id"
  };
  DISALLOW_COPY_AND_ASSIGN(ExtractUserUPModelCurrentProvinceId);
};
REGISTER_EXTRACTOR(ExtractUserUPModelCurrentProvinceId);
}  // namespace ad_algorithm
}  // namespace ks
