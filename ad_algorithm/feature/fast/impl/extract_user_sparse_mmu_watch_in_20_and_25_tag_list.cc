#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_mmu_watch_in_20_and_25_tag_list.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_multi_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

ExtractUserSparseMmuWatchIn20And25TagList::
    ExtractUserSparseMmuWatchIn20And25TagList()
    : FastFeature(FeatureType::USER) {}
void ExtractUserSparseMmuWatchIn20And25TagList::Extract(
    const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.user_info().common_info_attr(),
                                 CommonInfoAttr::PLAY_LESS_5S_GREET_ONLY_HAND);
  add_feature_result(x1, get_feature_func_,
                     FeaturePrefix::PLAY_LESS_5S_GREET_ONLY_HAND, result);
}

}  // namespace ad_algorithm
}  // namespace ks
