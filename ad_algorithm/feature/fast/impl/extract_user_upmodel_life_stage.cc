#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_life_stage.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelLifeStage::Extract(const AdLog& adlog, size_t pos,
                                          std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_ad_user_info()) {
    const auto& ad_user_info_life_stage = GetFeature(
        FeaturePrefix::UP_USER_LIFE_STAGE, adlog.user_info().ad_user_info().life_stage());
    AddFeature(ad_user_info_life_stage, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
