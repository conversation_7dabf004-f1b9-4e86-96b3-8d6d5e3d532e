#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature:
// teams/ad/ad_algorithm/dark_feature/impl/extract_user_universe_convert_action_sequences_mask.dark
class ExtractUserUniverseConvertActionSeqMediumCooperationModeMask : public FastFeature {
 public:
  ExtractUserUniverseConvertActionSeqMediumCooperationModeMask();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserUniverseConvertActionSeqMediumCooperationModeMask);
};

REGISTER_EXTRACTOR(ExtractUserUniverseConvertActionSeqMediumCooperationModeMask);
}  // namespace ad_algorithm
}  // namespace ks
