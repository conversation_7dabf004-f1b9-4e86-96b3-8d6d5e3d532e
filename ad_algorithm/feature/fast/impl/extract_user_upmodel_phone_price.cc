#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_phone_price.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelPhonePrice::Extract(const AdLog& adlog, size_t pos,
                                           std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_ad_user_info()) {
    const auto& ad_user_info_phone_price = GetFeature(
        FeaturePrefix::UP_USER_DEVICE_INFO_PRICE,
        static_cast<int>(adlog.user_info().ad_user_info().phone_price() / 1000));
    AddFeature(ad_user_info_phone_price, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
