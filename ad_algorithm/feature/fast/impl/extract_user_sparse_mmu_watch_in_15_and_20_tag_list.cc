#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_mmu_watch_in_15_and_20_tag_list.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_multi_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

ExtractUserSparseMmuWatchIn15And20TagList::
    ExtractUserSparseMmuWatchIn15And20TagList()
    : FastFeature(FeatureType::USER) {}
void ExtractUserSparseMmuWatchIn15And20TagList::Extract(
    const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.user_info().common_info_attr(),
                                 CommonInfoAttr::PLAY_LESS_5S_SERVICE_ATTRACT);
  add_feature_result(x1, get_feature_func_,
                     FeaturePrefix::PLAY_LESS_5S_SERVICE_ATTRACT, result);
}

}  // namespace ad_algorithm
}  // namespace ks
