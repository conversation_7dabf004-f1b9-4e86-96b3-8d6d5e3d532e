#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_device_readablemod.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelDeviceReadableMod::Extract(const AdLog& adlog, size_t pos,
                                                  std::vector<ExtractResult>* result) {
  std::hash<std::string> hash_fn;
  if (adlog.has_user_info() && adlog.user_info().has_ad_user_info()) {
    auto& device_info_list = adlog.user_info().ad_user_info().device_info();
    for (auto& device_info : device_info_list) {
      auto& readable_mod = device_info.brand();
      std::string curr = readable_mod.data();
      const auto& attribute_readable_mod =
          GetFeature(FeaturePrefix::UP_USER_DEVICE_READABLE_MOD, hash_fn(curr));
      AddFeature(attribute_readable_mod, 1.0f, result);
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
