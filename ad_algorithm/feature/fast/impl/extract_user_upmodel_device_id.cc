#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_device_id.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelDeviceId::Extract(const AdLog& adlog, size_t pos,
                                         std::vector<ExtractResult>* result) {
  std::hash<std::string> hash_fn;
  if (adlog.has_user_info() && adlog.user_info().has_long_term_loc()) {
    const auto& device_info_id =
        GetFeature(FeaturePrefix::UP_USER_DEVICE_INFO_ID,
                   hash_fn(adlog.user_info().device_info().id()));
    AddFeature(device_info_id, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
