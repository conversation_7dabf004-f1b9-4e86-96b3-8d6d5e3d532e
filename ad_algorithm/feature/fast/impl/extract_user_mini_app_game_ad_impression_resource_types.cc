/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_mini_app_game_ad_impression_resource_types.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"

namespace ks {
namespace ad_algorithm {

ExtractUserMiniAppGameAdImpressionResourceTypes::ExtractUserMiniAppGameAdImpressionResourceTypes()
    : FastFeatureNoPrefix(FeatureType::USER) {}
void ExtractUserMiniAppGameAdImpressionResourceTypes::Extract(const AdLog& adlog, size_t pos,
                                                              std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.user_info().common_info_attr(), 66790);
  add_feature_result(x1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
