#pragma once
#include <string>
#include <fstream>
#include <iostream>
#include <vector>
#include <unordered_map>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {


class ExtractUserMerchantPaidSellerIdListScale : public FastFeature {
 public:
  ExtractUserMerchantPaidSellerIdListScale() : FastFeature(USER) {}

  virtual void Extract(const AdLog & adlog, size_t pos, std::vector<ExtractResult>* result) {
    if (adlog.has_user_info() && adlog.user_info().user_real_time_action().ad_ecom_realtime_user_common_attr_size() > 0) {
        const auto& eds = adlog.user_info().user_real_time_action().ad_ecom_realtime_user_common_attr();
        for (const auto& userAttr : eds) { 
          if (userAttr.name_value() == ::kuaishou::ad::CommonInfoAttr_Name_ECOM_REALTIME_MERCHANT_PAID_SELLER_ID_LIST && userAttr.int_list_value_size() >= 20) {
            double value = 1.0;
            std::unordered_set<int> tmp_set2;
            std::unordered_set<int> tmp_set4;
            std::unordered_set<int> tmp_set8;
            std::unordered_set<int> tmp_set16;
            
            // LOG(INFO) << "ExtractUserMerchantPaidSellerIdListScale list size: " << userAttr.int_list_value_size();

            for (int i = userAttr.int_list_value_size() - 1; i >= 0; i--) {
              if (tmp_set2.size() < 2) {
                tmp_set2.insert(userAttr.int_list_value(i));
              }
              if (tmp_set4.size() < 4){
                tmp_set4.insert(userAttr.int_list_value(i));
              }
              if (tmp_set8.size() < 8){
                tmp_set8.insert(userAttr.int_list_value(i));
              }
              if (tmp_set16.size() < 16){
                tmp_set16.insert(userAttr.int_list_value(i));
              }
              if (tmp_set16.size() == 16) {
                break;
              }
            }

            // LOG(INFO) << "ExtractUserMerchantPaidSellerIdListScale ywc_test tmp_set2 size: " << tmp_set2.size();
            // LOG(INFO) << "ExtractUserMerchantPaidSellerIdListScale ywc_test tmp_set4 size: " << tmp_set4.size();
            // LOG(INFO) << "ExtractUserMerchantPaidSellerIdListScale ywc_test tmp_set8 size: " << tmp_set8.size();
            // LOG(INFO) << "ExtractUserMerchantPaidSellerIdListScale ywc_test tmp_set16 size: " << tmp_set16.size();

            for (const int &id : tmp_set2) {
              AddFeature(GetFeature(FeaturePrefix::USER_REALTIME_MERCHANT_PAID_SELLER_ID_LIST_2, id), value, result);
            }
            for (const int &id : tmp_set4) {
              AddFeature(GetFeature(FeaturePrefix::USER_REALTIME_MERCHANT_PAID_SELLER_ID_LIST_4, id), value, result);
            }
            for (const int &id : tmp_set8) {
              AddFeature(GetFeature(FeaturePrefix::USER_REALTIME_MERCHANT_PAID_SELLER_ID_LIST_8, id), value, result);
            }
            for (const int &id : tmp_set16) {
              AddFeature(GetFeature(FeaturePrefix::USER_REALTIME_MERCHANT_PAID_SELLER_ID_LIST_16, id), value, result);
            }
          }
        }
    }
  }

 private:
  const std::string USED_FEATURES[1] = {
    "user_info.common_info_attr.REALTIME_MERCHANT_PAID_SELLER_ID_LIST"
  };
  DISALLOW_COPY_AND_ASSIGN(ExtractUserMerchantPaidSellerIdListScale);
};

REGISTER_EXTRACTOR(ExtractUserMerchantPaidSellerIdListScale);

}  // namespace ad_algorithm
}  // namespace ks