#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_long_term_goods_view_ts_ple_dense.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_dense_ple.h"

namespace ks {
namespace ad_algorithm {

ExtractUserMerchantLongTermGoodsViewTsPleDense::ExtractUserMerchantLongTermGoodsViewTsPleDense()
    : FastFeature(FeatureType::DENSE_USER) {}
void ExtractUserMerchantLongTermGoodsViewTsPleDense::Extract(const AdLog& adlog, size_t pos,
                                                             std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_time(adlog);
  auto x2 = get_adlog_int64_list(adlog.user_info().common_info_attr(),
                                 CommonInfoAttr::USER_MERCHANT_LONG_TERM_GOODS_VIEW_TIMEREAL);
  const auto& x3 = get_lt_op_ts_bins();
  auto x5 = get_ts_list_dense_ple_helper(x1, x2, x3, 100);
  add_feature_result(x5, 400, result);
}

}  // namespace ad_algorithm
}  // namespace ks
