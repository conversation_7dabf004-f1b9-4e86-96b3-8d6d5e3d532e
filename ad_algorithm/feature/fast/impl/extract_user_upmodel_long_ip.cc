#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_long_ip.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelLongIp::Extract(const AdLog& adlog, size_t pos,
                                       std::vector<ExtractResult>* result) {
  std::hash<std::string> hash_fn;
  if (adlog.has_user_info() && adlog.user_info().has_long_term_loc()) {
    const auto& long_term_ip = GetFeature(
        FeaturePrefix::USER_IP_PROVINCE, hash_fn(adlog.user_info().long_term_loc().ip()));
    AddFeature(long_term_ip, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
