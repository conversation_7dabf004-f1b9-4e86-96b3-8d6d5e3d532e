#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_ug_flag.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/universe_extract_def.h"

namespace ks {
namespace ad_algorithm {

ExtractUserUniverseUgFlag::ExtractUserUniverseUgFlag() : FastFeature(FeatureType::DENSE_ITEM) {}
void ExtractUserUniverseUgFlag::Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_str(adlog.item(pos).ad_dsp_info().advertiser_base().product_name());
  auto x2 = ug_product_flag(x1);
  add_feature_result(x2, 1, result);
}

}  // namespace ad_algorithm
}  // namespace ks
