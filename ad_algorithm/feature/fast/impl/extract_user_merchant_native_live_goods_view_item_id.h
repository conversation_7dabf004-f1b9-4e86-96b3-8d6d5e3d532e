#pragma once
#include <string>
#include <fstream>
#include <iostream>
#include <vector>
#include <unordered_map>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {


class ExtractUserMerchantNativeLiveGoodsViewItemId : public FastFeature {
 public:
  ExtractUserMerchantNativeLiveGoodsViewItemId() : FastFeature(USER) {}

  virtual void Extract(const AdLog & adlog, size_t pos, std::vector<ExtractResult>* result) {
    if (adlog.has_user_info() && adlog.user_info().user_real_time_action().ad_ecom_realtime_user_common_attr_size() > 0) {
      const auto& eds = adlog.user_info().user_real_time_action().ad_ecom_realtime_user_common_attr();
      for (const ::kuaishou::ad::CommonInfoAttr& userAttr : eds) {
        if (userAttr.name_value() ==
            ::kuaishou::ad::CommonInfoAttr_Name_ECOM_REALTIME_USER_MERCHANT_LIVE_GOODS_VIEW_ITEM_ID_LIST) {
          for (auto id : userAttr.int_list_value()) {
            auto id_index = GetFeature(FeaturePrefix::USER_NATIVE_MERCHANT_LIVE_GOODS_VIEW_ITEM_ID, id);
            AddFeature(id_index, 1.0, result);
          }
          auto id_num = GetFeature(FeaturePrefix::USER_NATIVE_MERCHANT_LIVE_GOODS_VIEW_ITEM_ID_NUM,
                                            userAttr.int_list_value_size());
          AddFeature(id_num, 1.0, result);
          // std::cout << "wubo_debug ExtractUserMerchantNativeLiveGoodsViewItemId :" 
          //           << std::to_string(userAttr.int_list_value_size())
          //           << std::endl;

          break;

        }
      }
    }
  }

 private:
  const std::string USED_FEATURES[1] = {
    "user_info.common_info_attr.ECOM_REALTIME_USER_MERCHANT_LIVE_GOODS_VIEW_ITEM_ID_LIST"
  };
  DISALLOW_COPY_AND_ASSIGN(ExtractUserMerchantNativeLiveGoodsViewItemId);
};

REGISTER_EXTRACTOR(ExtractUserMerchantNativeLiveGoodsViewItemId);

}  // namespace ad_algorithm
}  // namespace ks