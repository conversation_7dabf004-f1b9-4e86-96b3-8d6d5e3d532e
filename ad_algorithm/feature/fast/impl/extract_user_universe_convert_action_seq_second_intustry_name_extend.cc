#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_second_intustry_name_extend.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/uv_action_seq_util.h"

namespace ks {
namespace ad_algorithm {

ExtractUserUniverseConvertActionSeqSecondIntustryNameExtend::
    ExtractUserUniverseConvertActionSeqSecondIntustryNameExtend()
    : FastFeatureNoPrefix(FeatureType::USER) {}
void ExtractUserUniverseConvertActionSeqSecondIntustryNameExtend::Extract(
    const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.user_info().common_info_attr(),
                                 CommonInfoAttr::EVNET_FORM_SUBMIT_SECOND_INDUSTRY_NAME_CH);
  auto x2 = get_adlog_int64_list(adlog.user_info().common_info_attr(),
                                 CommonInfoAttr::EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE);
  auto x3 = get_adlog_int64_list(adlog.user_info().common_info_attr(),
                                 CommonInfoAttr::EVENT_FORM_SUBMIT_TIMESTAMP_LIST);
  auto x5 = get_adlog_time(adlog);
  auto x7 = get_universe_hash_seq_with_max_len_and_timestamp(x1, x2, x3, 30, x5, 86400000);
  auto x8 = get_adlog_int64_list(adlog.user_info().common_info_attr(),
                                 CommonInfoAttr::EVNET_CONVERSION_SECOND_INDUSTRY_NAME_CH);
  auto x9 = get_adlog_int64_list(adlog.user_info().common_info_attr(),
                                 CommonInfoAttr::EVNET_CONVERSION_MEDIUM_ATTRIBUTE);
  auto x10 = get_adlog_int64_list(adlog.user_info().common_info_attr(),
                                  CommonInfoAttr::EVENT_CONVERSION_TIMESTAMP_LIST);
  auto x14 = get_universe_hash_seq_with_max_len_and_timestamp(x8, x9, x10, 30, x5, 86400000);
  auto x17 = merge_two_seq_with_two_max_len(x7, x14, 2, 8);
  auto x18 = get_adlog_int64_list(adlog.user_info().common_info_attr(),
                                  CommonInfoAttr::UNIVERSE_AD_CONVERT_ACTION_SEQ_SECOND_INDUSTRY_NAME);
  auto x21 = merge_two_seq_with_max_len(x17, x18, 10, 30);
  add_feature_result(x21, result);
}

}  // namespace ad_algorithm
}  // namespace ks
