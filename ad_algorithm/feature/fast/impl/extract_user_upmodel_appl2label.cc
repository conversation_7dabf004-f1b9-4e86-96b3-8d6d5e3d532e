#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_appl2label.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelAppL2Label::Extract(const AdLog& adlog, size_t pos,
                                           std::vector<ExtractResult>* result) {
  std::hash<std::string> hash_fn;
  if (adlog.has_user_info() && adlog.user_info().has_ad_user_hdfs_feature()) {
    auto& app_l2_label_list = adlog.user_info().ad_user_hdfs_feature().app_l2_label();
    for (auto& app_l2_label : app_l2_label_list) {
      std::string curr = app_l2_label.data();
      const auto& attribute_app_l2_label =
          GetFeature(FeaturePrefix::UP_USER_APP_L2_LABEL, hash_fn(curr));
      AddFeature(attribute_app_l2_label, 1.0f, result);
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
