#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_convert_action_seq_second_industry_name.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/uv_action_seq_util.h"

namespace ks {
namespace ad_algorithm {

ExtractUserUniverseConvertActionSeqSecondIndustryName::ExtractUserUniverseConvertActionSeqSecondIndustryName()
    : FastFeatureNoPrefix(FeatureType::USER) {}
void ExtractUserUniverseConvertActionSeqSecondIndustryName::Extract(const AdLog& adlog, size_t pos,
                                                                    std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64_list(adlog.user_info().common_info_attr(),
                                 CommonInfoAttr::UNIVERSE_AD_CONVERT_ACTION_SEQ_SECOND_INDUSTRY_NAME);
  auto x3 = get_seq_with_max_len(x1, 30);
  add_feature_result(x3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
