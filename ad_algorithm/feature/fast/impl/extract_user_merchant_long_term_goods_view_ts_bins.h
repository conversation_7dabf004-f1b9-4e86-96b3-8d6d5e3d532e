#pragma once
#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_user_tim.dark
class ExtractUserMerchantLongTermGoodsViewTsBins : public FastFeatureNoPrefix {
 public:
  ExtractUserMerchantLongTermGoodsViewTsBins();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserMerchantLongTermGoodsViewTsBins);
};

REGISTER_EXTRACTOR(ExtractUserMerchantLongTermGoodsViewTsBins);
}  // namespace ad_algorithm
}  // namespace ks
