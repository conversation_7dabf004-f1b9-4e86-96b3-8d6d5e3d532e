#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_app_purchase.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelAppPurchase::Extract(const AdLog& adlog, size_t pos,
                                            std::vector<ExtractResult>* result) {
  std::hash<std::string> hash_fn;
  if (adlog.has_user_info() && adlog.user_info().has_ad_user_info()) {
    auto& device_info_list = adlog.user_info().ad_user_info().device_info();
    for (auto& device_info : device_info_list) {
      auto& app_package_list = device_info.app_package_purchase();
      for (auto& app_package : app_package_list) {
        std::string curr = app_package.data();
        const auto& attribute_app_package =
            GetFeature(FeaturePrefix::UP_USER_APP_PURCHASE, hash_fn(curr));
        AddFeature(attribute_app_package, 1.0f, result);
      }
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
