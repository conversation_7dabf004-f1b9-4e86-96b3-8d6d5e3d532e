#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_merchant_seq_length_dense.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_multi_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_realtime_cross_feature.h"
#include "teams/ad/ad_algorithm/fed/compute/merge_list.h"

namespace ks {
namespace ad_algorithm {

ExtractUserMerchantSeqLengthDense::ExtractUserMerchantSeqLengthDense()
    : FastFeature(FeatureType::DENSE_USER) {}
void ExtractUserMerchantSeqLengthDense::Extract(const AdLog& adlog, size_t pos,
                                                std::vector<ExtractResult>* result) {
  auto multi_attr1 = get_adlog_multi<absl::Span<const int64_t>, absl::Span<const int64_t>,
                                     absl::Span<const int64_t>, absl::Span<const int64_t>>(
      adlog.user_info().common_info_attr(),
      std::make_tuple(CommonInfoAttr::AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_AUTHOR_ID_LIST,
                      CommonInfoAttr::AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_AUTHOR_ID_LIST,
                      CommonInfoAttr::USER_MERCHANT_LONG_TERM_ORDER_PAIED_AUTHOR_ID,
                      CommonInfoAttr::USER_MERCHANT_LONG_TERM_GOODS_VIEW_AUTHOR_ID));
  auto x1 = std::get<0>(multi_attr1);
  auto x3 = std::get<1>(multi_attr1);
  auto x5 = std::get<2>(multi_attr1);
  auto x7 = std::get<3>(multi_attr1);

  auto x2 = get_list_length(x1);
  auto x4 = get_list_length(x3);
  auto x6 = get_list_length(x5);
  auto x8 = get_list_length(x7);
  auto x9 = merge_float_list_all(x2, x4, x6, x8);
  add_feature_result(x9, 4, result);
}

}  // namespace ad_algorithm
}  // namespace ks
