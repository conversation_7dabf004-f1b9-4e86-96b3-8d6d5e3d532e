#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_interest.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelInterest::Extract(const AdLog& adlog, size_t pos,
                                         std::vector<ExtractResult>* result) {
  std::hash<std::string> hash_fn;
  if (adlog.has_user_info() && adlog.user_info().has_ad_user_info()) {
    auto& interest_list = adlog.user_info().ad_user_info().interest();
    for (auto& interest : interest_list) {
      std::string curr = interest.data();
      const auto& attribute_interest =
          GetFeature(FeaturePrefix::UP_USER_INTEREST, hash_fn(curr));
      AddFeature(attribute_interest, 1.0f, result);
    }
  }
}
}  // namespace ad_algorithm
}  // namespace ks
