#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_upmodel_device_platform.h"

namespace ks {
namespace ad_algorithm {
void ExtractUserUPModelDevicePlatform::Extract(const AdLog& adlog, size_t pos,
                                               std::vector<ExtractResult>* result) {
  if (adlog.has_user_info() && adlog.user_info().has_device_info()) {
    const auto& device_info_platform =
        GetFeature(FeaturePrefix::UP_USER_DEVICE_INFO_PLATFORM,
                   adlog.user_info().device_info().platform());
    AddFeature(device_info_platform, 1.0f, result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
