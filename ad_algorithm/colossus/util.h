#pragma once

#include <string>
#include <fstream>

#include "tensorflow/core/public/session.h"

#include "teams/ad/ad_algorithm/feature/fast/ad_log.h"

namespace ks {
namespace ad_algorithm {

using Tensor = tensorflow::Tensor;
using Session = tensorflow::Session;

void Index2String(const Tensor& tensor, std::string* str);

void Value2String(const Tensor& tensor, std::string* str);

void TopK2String(const Tensor& tensor, std::string* str);

void Dense2String(const Tensor& tensor, std::string* str);

class TensorDumper {
 public:
  TensorDumper();
  ~TensorDumper() {
    rfs.close();
  }
  void DumpDebugTensors(Session* session, const std::vector<std::pair<std::string, Tensor>>& feed_dict);

 private:
  std::fstream rfs;
  std::vector<std::string> tensor_names;
  std::mutex lock_;
};

struct InputSample {
  std::unique_ptr<ks::ad_algorithm::AdLog> ad_log = nullptr;
  std::unique_ptr<std::vector<std::pair<std::string, Tensor>>> feed_dict = nullptr;
};

}  // namespace ad_algorithm
}  // namespace ks
