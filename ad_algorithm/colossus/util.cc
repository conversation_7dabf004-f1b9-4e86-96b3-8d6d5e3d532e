#include "teams/ad/ad_algorithm/colossus/util.h"

#include <vector>

#include "base/strings/string_printf.h"
#include "base/strings/string_util.h"

DEFINE_string(dump_input_path, "", "");
DEFINE_string(debug_tensor_list, "", "");

namespace ks {
namespace ad_algorithm {

void Index2String(const Tensor& tensor, std::string* str) {
  auto mat = tensor.matrix<tensorflow::int64>();
  auto dims = mat.dimensions();
  for (int i = 0; i < dims[0]; i++) {
    if (i == 0) {
      base::StringAppendF(str, "%lld:%lld", mat(i, 0), mat(i, 1));
    } else {
      base::StringAppendF(str, ",%lld:%lld", mat(i, 0), mat(i, 1));
    }
  }
}

void Value2String(const Tensor& tensor, std::string* str) {
  int d = tensor.shape().dims();
  if (d == 2) {
    auto mat = tensor.matrix<tensorflow::int64>();
    auto dims = mat.dimensions();
    for (int i = 0; i < dims[1]; i++) {
      if (i == 0) {
        base::StringAppendF(str, "%lld", mat(0, i));
      } else {
        base::StringAppendF(str, ",%lld", mat(0, i));
      }
    }
  } else {
    auto mat = tensor.vec<tensorflow::int64>();
    auto dims = mat.dimensions();
    for (int i = 0; i < dims[0]; i++) {
      if (i == 0) {
        base::StringAppendF(str, "%lld", mat(i));
      } else {
        base::StringAppendF(str, ",%lld", mat(i));
      }
    }
  }
}

void TopK2String(const Tensor& tensor, std::string* str) {
  int d = tensor.shape().dims();
  if (d == 2) {
    auto mat = tensor.matrix<tensorflow::int32>();
    auto dims = mat.dimensions();
    for (int i = 0; i < dims[1]; i++) {
      if (i == 0) {
        base::StringAppendF(str, "%d", mat(0, i));
      } else {
        base::StringAppendF(str, ",%d", mat(0, i));
      }
    }
  } else {
    auto mat = tensor.vec<tensorflow::int32>();
    auto dims = mat.dimensions();
    for (int i = 0; i < dims[0]; i++) {
      if (i == 0) {
        base::StringAppendF(str, "%d", mat(i));
      } else {
        base::StringAppendF(str, ",%d", mat(i));
      }
    }
  }
}

void Dense2String(const Tensor& tensor, std::string* str) {
  int d = tensor.shape().dims();
  if (d == 2) {
    auto mat = tensor.matrix<float>();
    auto dims = mat.dimensions();
    for (int i = 0; i < dims[1]; i++) {
      if (i == 0) {
        base::StringAppendF(str, "%.6lf", mat(0, i));
      } else {
        base::StringAppendF(str, ",%.6lf", mat(0, i));
      }
    }
  } else {
    auto mat = tensor.vec<float>();
    auto dims = mat.dimensions();
    for (int i = 0; i < dims[0]; i++) {
      if (i == 0) {
        base::StringAppendF(str, "%.6lf", mat(i));
      } else {
        base::StringAppendF(str, ",%.6lf", mat(i));
      }
    }
  }
}

TensorDumper::TensorDumper() {
  if (FLAGS_dump_input_path != "") {
    rfs = std::fstream(FLAGS_dump_input_path, std::ios::trunc|std::ios::out);
  }
  if (FLAGS_debug_tensor_list != "") {
    std::ifstream infile(FLAGS_debug_tensor_list);
    std::string line;
    while (std::getline(infile, line))
    {
      base::TrimWhitespaces(&line);
      if (line != "") {
        tensor_names.push_back(line);
      }
    }
  }
}

void TensorDumper::DumpDebugTensors(Session* session,
                      const std::vector<std::pair<std::string, Tensor>>& feed_dict) {
  if (FLAGS_dump_input_path == "" || tensor_names.empty()) {
    return;
  }
  std::vector<std::string> tensors_output;
  std::vector<int> tensors_index(tensor_names.size(), -1);
  std::vector<bool> is_input(tensor_names.size(), false);
  for (int i = 0; i < tensor_names.size(); i++) {
    for (int j = 0; j < feed_dict.size(); j++) {
      if(feed_dict[j].first == tensor_names[i]) {
        is_input[i] = true;
        tensors_index[i] = j;
        break;
      }
    }
    if (!is_input[i]) {
      tensors_index[i] = tensors_output.size();
      tensors_output.push_back(tensor_names[i]);
    }
  }
  std::string debug_str;
  std::vector<Tensor> debug_outs;
  TF_CHECK_OK(session->Run(feed_dict, tensors_output, {}, &debug_outs));
  for (int k = 0; k < tensor_names.size(); k++) {
    std::string dense_field;
    if (is_input[k]) {
      Dense2String(feed_dict[tensors_index[k]].second, &dense_field);
    } else {
      Dense2String(debug_outs[tensors_index[k]], &dense_field);
    }
    base::StringAppendF(&debug_str, "%s\t%s\n", tensor_names[k].c_str(), dense_field.c_str());
  }
  lock_.lock();
  rfs << debug_str;
  lock_.unlock();
}
}  // namespace ad_algorithm
}  // namespace ks
