#pragma once
#include <unordered_set>
#include <utility>
#include <mutex>
#include <atomic>
#include <vector>
#include <memory>
#include <set>
#include <string>
#include "base/common/gflags.h"

namespace ks {
namespace gpu_ps {

class FeatureClient {
 public:
  FeatureClient();
  ~FeatureClient() {};

  bool NextBatch(int32_t hub_idx,
                 bool* over, bool* need_wait,
                 int64_t* batch_id,
                 int32_t* label, int32_t label_count,
                 float* dense_feature,
                 int32_t dense_feature_count,
                 int32_t* debug_sizes, int batch_size, char* debug_info_str);

 private:
};

}  // namespace gpu_ps
}  // namespace ks
