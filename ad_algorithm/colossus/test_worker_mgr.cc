#include <thread>
#include "base/common/base.h"
#include "base/common/gflags.h"
#include "teams/ad/ad_nn/gpu_ps/common/stream/stream_mgr.h"
#include "learning/kuiba/base/hdfs_line_bin_file.h"

DECLARE_bool(logtostderr);
DEFINE_bool(bg_run, false, "");

void ReadFromHdfs() {
  kuiba::HdfsTextFile file("ad");
  CHECK(file.open("/home/<USER>/liliangmin/tmp/test_adlog_07_01/fanstop_click-2021-07-01-18", O_RDONLY)) << "open file fail";
  if (file.empty()) {
    return;
  }
  while (true) {
    std::shared_ptr<std::string> line = std::make_shared<std::string>();
    if (!file.read_line(line.get()) || line->empty()) {
      LOG(INFO) << "read line done";
      std::this_thread::sleep_for(std::chrono::seconds(200));
      break;
    }
    LOG(INFO) << "line size: " << line->size();
  }
}

int main(int argc, char* argv[]) {
  FLAGS_logtostderr = true;
  base::InitApp(&argc, &argv, "test_worker_mgr");
  if (FLAGS_bg_run) {
    std::thread task(ReadFromHdfs);
    task.join();
  } else {
    ReadFromHdfs();
  }
  return 0;
}
