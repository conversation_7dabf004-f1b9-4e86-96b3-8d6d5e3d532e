#pragma once

#include <mutex>
#include <atomic>
#include <thread>
#include <map>
#include <utility>
#include <unordered_map>
#include "teams/ad/ad_nn/gpu_ps/common/stream/stream.h"
#include "teams/ad/ad_nn/kafka_feature/proto/kafka_feature.pb.h"
#include "teams/ad/ad_nn/gpu_ps/feature_convert/feature_converter.h"
#include "perfutil/perfutil.h"
#include "teams/ad/ad_nn/feature/ad_field_feature.h"
#include "teams/ad/ad_nn/feature/feature_extractor_bundle.h"

namespace ks {
namespace gpu_ps {

using ks::infra::PerfUtil;

class FileCandidateGenerator;
class WorkerMgr;
class BatchProcessor;

// NOTE(dongxing) 目前prefix_key到 2000
const static int MAX_PREFIX = 10000;
struct SparseFieldInfo {
  std::string class_name;
  int64_t prefix;
  int32_t index;
  int64_t size;
  bool valid;

  SparseFieldInfo(): prefix(-1), index(0), size(0), valid(false) {}
};
typedef std::vector<SparseFieldInfo> PrefixList;
typedef std::vector<SparseFieldInfo> FieldList;

struct WorkerOption {
  std::string model_name;
  int32_t batch_size;

  std::vector<int64_t> sparse_field_count;
  std::vector<int64_t> sparse_hash_size;
  std::vector<int32_t> sparse_field_index;
  std::vector<int64_t> dense_field_count;
  std::vector<std::string> sparse_class_names;
  std::vector<std::string> dense_class_names;
  PrefixList prefix_list;
  FieldList field_list;

  int32_t embedding_size;
  std::string shard_addr;

  int32_t dense_total_size;
  //for batch
  int32_t need_batch;
  //Todo: pass feature info by data not by file_path
  std::string label_extractor;
  std::string target;
  std::string tab;
  std::string item_type;
  std::string item_filter;
  std::string feature_config_path;
  StreamSource src_type;
  bool use_weight;
  //for hash
  int32_t ps_hash;
  std::string hash_type;
  std::function<uint64_t(uint64_t)> hash_func;
  //for debug
  int32_t debug;

  std::string aim;
  float sample_rate;
  float neg_sample_rate;

  bool enable_format_opt;

  std::string debug_info;
};

class Worker {
public:
  Worker(Stream* stream, const WorkerOption& opt, WorkerMgr* mgr);
  ~Worker();

  Worker(const Worker&) = delete;
  Worker &operator=(const Worker&) = delete;
  Worker &operator=(const Worker&) volatile = delete;

  Status Init();
  Status Start();
  Status Stop();

  inline bool IsOver() {
    return over_.load();
  }
   
private:
  void run();
  Status consume(std::shared_ptr<std::string>& record);
  void release(std::shared_ptr<std::string>& record);
  bool process_batched(const std::string& sample_str,
               int64_t* batch_id,
               std::map<int32_t, int32_t>* ps_pending_batch,
               int32_t* worker_pending_batch);

  // not own
  Stream* stream_;
  WorkerOption opt_;
  std::vector<int64_t> field_size_;

  std::thread* bg_thd_;
  std::atomic<bool> stop_;

  std::atomic<bool> over_;

  WorkerMgr* mgr_;

  ks::gpu_ps::FeatureConverter* fc_;

  std::mutex mtx_;

  BatchProcessor* bp_;

  // std::unique_ptr<ks::ad_algorithm::LogPreprocess> processor = nullptr;

  std::shared_ptr<ks::ad_nn::FeatureFileInfo> feature_file_info = nullptr;

  std::unique_ptr<ks::ad_nn::AdFieldFeature> feature_extractor = nullptr;

  std::vector<std::vector<float>> dense_field_value;

  std::vector<ks::ad_nn::FeatureType> sparse_feature_type_sorted;
  std::vector<ks::ad_nn::FeatureType> dense_feature_type_sorted;

  std::vector<int> dense_field_offset;

  std::unique_ptr<ks::ad_nn::AdFeatureExtractorBundle> ad_fea_ext_bundle_ptr = nullptr;
  int dense_total_size;
};

}  // namespace gpu_ps
}  // namespace ks
