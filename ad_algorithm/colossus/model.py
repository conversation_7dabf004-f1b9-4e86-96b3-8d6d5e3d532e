import math
import base64
import tensorflow as tf

from teams.ad.ad_proto.kuaishou.ad.algorithm.model.ad_joint_labeled_log_pb2 import AdJointLabeledLog


SPARSE_FEATURES = [
    [100000, 16],  # product_name
    [100000, 16],  # account
    [100000, 16],  # photo_id
]

MAX_SEQ_LEN = 10000
SEQ_EMB_SIZE = 128


def get_input():
    input_list = []
    for i in range(len(SPARSE_FEATURES)):
        field_ph = tf.sparse_placeholder(tf.int64, name="field_" + str(i))
        emb_var_name = "embedding_%d" % i
        bucket_size = SPARSE_FEATURES[i][0]
        embedding_size = SPARSE_FEATURES[i][1]
        emb_var = tf.get_variable(
            emb_var_name,
            [bucket_size, embedding_size],
            initializer=tf.random_uniform_initializer(-0.01, 0.01),
            trainable=True)
        embed = tf.nn.embedding_lookup_sparse(emb_var, field_ph, None, combiner="sum")
        input_list.append(embed)

    input_list.append(tf.placeholder(tf.float32,
                      name="dense_field",
                      shape=[None, (MAX_SEQ_LEN + 1) * SEQ_EMB_SIZE]))
    return tf.concat(input_list, 1)


def inference():
    global_step = tf.train.get_or_create_global_step()
    dnn_input = get_input()
    input_size = dnn_input.get_shape().as_list()[-1]
    user_input_size = SEQ_EMB_SIZE * MAX_SEQ_LEN
    item_input_size = input_size - SEQ_EMB_SIZE * MAX_SEQ_LEN
    item = tf.slice(dnn_input, [0, 0], [-1, item_input_size])
    item = tf.reshape(item, [-1, item_input_size])
    user = tf.slice(dnn_input, [0, item_input_size], [-1, user_input_size])
    user = tf.reshape(user, [-1, SEQ_EMB_SIZE])

    # user tower
    layer_size = 16
    w = tf.get_variable(
            'user/w', [SEQ_EMB_SIZE, layer_size],
            initializer=tf.random_normal_initializer(stddev=1.0/math.sqrt(float(SEQ_EMB_SIZE))),
            trainable=True)
    b = tf.get_variable('user/b', [layer_size], initializer=tf.zeros_initializer, trainable=True)
    print("%s length=%d * %d" % (w.name, SEQ_EMB_SIZE, layer_size))
    print("%s length=%d" % (b.name, layer_size))
    user = tf.add(tf.matmul(user, w), b)
    user = tf.reshape(user, [-1, MAX_SEQ_LEN, layer_size])

    # item tower
    layer_size = 16
    w = tf.get_variable(
            'item/w', [item_input_size, layer_size],
            initializer=tf.random_normal_initializer(stddev=1.0/math.sqrt(float(item_input_size))),
            trainable=True)
    b = tf.get_variable('item/b', [layer_size], initializer=tf.zeros_initializer, trainable=True)
    print("%s length=%d * %d" % (w.name, item_input_size, layer_size))
    print("%s length=%d" % (b.name, layer_size))
    item = tf.add(tf.matmul(item, w), b)
    item = tf.reshape(item, [-1, 1, layer_size])

    dot = tf.reshape(tf.matmul(item, user, transpose_b=True), [-1, 100])
    values, indices = tf.math.top_k(dot, 10)
    indices = tf.identity(indices, name="top_indices")
    top = tf.gather(user, indices, batch_dims=-1, name="top_embeddings")

    return indices, top


def load_file_and_process(file_content):
    file_content = base64.b64decode(file_content.rstrip('\n'))
    adlog = AdJointLabeledLog()
    adlog.ParseFromString(file_content)
    return adlog.user_info.user_id


def feed_sample(paths=['1624877986263.bjlt-h8157.sy_2_0']):
    dataset = tf.data.Dataset.from_tensor_slices(paths)
    dataset = dataset.interleave(lambda x: tf.data.TextLineDataset(x)
            .map(lambda x: tf.py_function(load_file_and_process, [x], [tf.int64])), cycle_length=4, block_length=16)
    iterator = dataset.make_initializable_iterator()

    return iterator


def build(model_path=""):
    with tf.Graph().as_default():
        # build graph
        indices, top = inference()

        # restore trainer from checkpoint
        saver = tf.train.Saver(tf.global_variables())
        sess_config = tf.ConfigProto(
            device_count={"CPU": 15},
            allow_soft_placement=True,
            log_device_placement=False,
            intra_op_parallelism_threads=64,
            inter_op_parallelism_threads=64)
        sess_config.graph_options.optimizer_options.global_jit_level = tf.OptimizerOptions.ON_1
        # sess_config.gpu_options.allow_growth = True
        # sess_config.gpu_options.per_process_gpu_memory_fraction = per_process_gpu_memory_fraction
        # sess_config.gpu_options.visible_device_list = str(klearn_dist.local_rank())
        # sess_config.gpu_options.force_gpu_compatible = True
        
        dataset = feed_sample()

        init_op = tf.group(tf.global_variables_initializer(),
                           tf.local_variables_initializer(),
                           dataset.initializer,
                           name="init_op")
        with tf.Session(config=sess_config) as sess:
            sess.run(init_op)
            while True:
                try:
                    print(sess.run(dataset.get_next()))
                except tf.errors.OutOfRangeError:
                    print('data iterator finish.')
                    break
            # print("restore from model:" + str(model_path))
            # saver.restore(sess, '%s/model_tf' % (model_path))


if __name__ == "__main__":
    build()
