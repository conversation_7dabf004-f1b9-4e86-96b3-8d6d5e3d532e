#include <functional>
#include <string>
#include <iostream>

#include "absl/time/time.h"
#include "base/common/base.h"
#include "folly/stats/Histogram.h"
#include "serving_base/util/dynamic_config.h"
#include "serving_base/utility/signal.h"

#include "teams/reco-arch/colossus/client/checkpoint_reader.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

DEFINE_string(scr_root_dir, "", "checkpoint root directory");
DEFINE_string(scr_single_file, "", "only read single checkpoint file");
DEFINE_int32(scr_shard_num, 0, "shard number of checkpoint");
DEFINE_int32(scr_shard_id, -1, "only read single shard's all checkpoint files");
DEFINE_int32(scr_thread_num, 1, "read thread number of checkpoint reader");

int main(int argc, char* argv[]) {
  base::InitApp(&argc, &argv, "sim_checkpoint_reader");
  std::ios_base::sync_with_stdio(false);
  std::cin.tie(nullptr);
  std::cerr.tie(nullptr);
  CHECK(FLAGS_scr_shard_num > 0) << "shard_num " << FLAGS_scr_shard_num << " must be greater than 0";
  auto handler = [](const uint32_t& code, const uint64_t& key, const char* value, const int size) -> bool {
    const size_t type_size = sizeof(colossus::AdLiveItemT);
    CHECK_EQ(size % type_size, 0) << size << " " << type_size;
    int item_num = size / type_size;
    for (int i = 0; i < item_num; ++i) {
      const auto* item = reinterpret_cast<const colossus::AdLiveItemT*>(value + type_size * i);
      // item 是 key 下面的每个 item 的数据的指针
      // 在这里实现你的业务逻辑
      std::cout << key << "\t"
                << i << "\t"
                << item->live_id << "\t"
                << item->photo_id << "\t"
                << item->author_id << "\t"
                << item->timestamp << "\t"
                << item->photo_duration << "\t"
                << item->photo_play_time << "\t"
                << item->live_play_time << "\t"
                << item->label << "\t"
                << item->channel << "\t"
                << item->cluster_id << "\t"
                << item->tag << "\t"
                << item->gmv << "\n";
    }
    return true;
  };
  auto reader = std::make_unique<colossus::SimCheckpointReader>(FLAGS_scr_shard_num, FLAGS_scr_root_dir,
                                                                FLAGS_scr_thread_num, handler);
  if (!FLAGS_scr_single_file.empty()) {
    LOG(INFO) << "Loading single file: " << FLAGS_scr_single_file;
    reader->LoadSingleFile(FLAGS_scr_single_file);
  } else if (FLAGS_scr_shard_id > -1 && FLAGS_scr_shard_id < FLAGS_scr_shard_num) {
    LOG(INFO) << "Loading single shard: " << FLAGS_scr_shard_id << "'s all checkpoint files";
    std::unordered_set<int> shard_ids = {FLAGS_scr_shard_id};
    reader->LoadFilesByShards(shard_ids);
  } else {
    LOG(INFO) << "Loading all shards' checkpoint files";
    reader->LoadAllShards();
  }
  serving_base::SignalCatcher::Initialize();
  serving_base::SignalCatcher::WaitForSignal();
  reader->StopAndWait();
  return 0;
}
