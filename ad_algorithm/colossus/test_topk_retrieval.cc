#include "base/common/base.h"

#include "teams/ad/ad_algorithm/colossus/topk_retrieval.h"
#include "teams/ad/ad_algorithm/colossus/sample_generator.h"

DECLARE_bool(logtostderr);

int main(int argc, char *argv[]) {
  FLAGS_logtostderr = true;
  base::InitApp(&argc, &argv, "train dnn model");
  ks::ad_algorithm::TensorDumper tensor_dumper;

  ks::ad_algorithm::TopKRetrievalMgr topk_retrieval_mgr(&tensor_dumper);
  ks::ad_algorithm::SampleGeneratorMgr sample_generator_mgr(&topk_retrieval_mgr);

  sample_generator_mgr.Stop();
  topk_retrieval_mgr.Stop();
}
