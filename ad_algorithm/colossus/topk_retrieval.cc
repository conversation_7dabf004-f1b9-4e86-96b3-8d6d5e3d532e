#include "teams/ad/ad_algorithm/colossus/topk_retrieval.h"

#include <iostream>

#include "base/common/gflags.h"
#include "base/time/timestamp.h"
#include "base/strings/string_printf.h"
#include "base/encoding/base64.h"
#include "tensorflow/core/protobuf/meta_graph.pb.h"

DEFINE_int32(intra_op_parallelism_threads, 0, "intra op parallelism_threads");
DEFINE_int32(inter_op_parallelism_threads, 0, "inter op parallelism_threads");
DEFINE_string(graph_path, "./graph.pb", "graph path");
DEFINE_string(model_tf, "", "");
DEFINE_int32(num_retrievals, 2, "");
DEFINE_int32(feed_queue_max_size, 10, "");

namespace ks {
namespace ad_algorithm {

using TensorShape = tensorflow::TensorShape;

TopKRetrieval::TopKRetrieval(TopKRetrievalMgr* mgr, TensorDumper* tensor_dumper) {
  mgr_ = mgr;
  bg_thd_ = new std::thread(std::bind(&TopKRetrieval::Run, this, std::placeholders::_1), tensor_dumper);
}

void TopKRetrieval::Run(TensorDumper* tensor_dumper) {
  tensorflow::SessionOptions options;
  options.config.set_inter_op_parallelism_threads(FLAGS_inter_op_parallelism_threads);
  options.config.set_intra_op_parallelism_threads(FLAGS_intra_op_parallelism_threads);
  session = std::unique_ptr<Session>(NewSession(options));

  tensorflow::MetaGraphDef meta_graph_def;
  TF_CHECK_OK(
      tensorflow::ReadBinaryProto(tensorflow::Env::Default(), FLAGS_graph_path, &meta_graph_def));
  TF_CHECK_OK(session->Create(meta_graph_def.graph_def()));
  TF_CHECK_OK(session->Run({}, {}, {"init_op"}, nullptr));
  LOG(INFO) << "restore model from " << FLAGS_model_tf;
  Tensor restore_model_path(tensorflow::DataType::DT_STRING, TensorShape({1, 1}));
  restore_model_path.matrix<tensorflow::string>()(0, 0) = FLAGS_model_tf;
  TF_CHECK_OK(session->Run({{"save/Const:0", restore_model_path}}, {},
                           {"save/restore_all"}, nullptr));
  LOG(INFO) << "restore model done";
  std::vector<Tensor> outputs;
  
  while (!mgr_->IsOver()) {
    std::shared_ptr<InputSample> input_sample = nullptr;
    if (!mgr_->GetInput(input_sample)) {
      LOG_EVERY_N(INFO, 1000) << "data queue empty, try to increase number of threads";
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      continue;
    }
    auto feed_dict = input_sample->feed_dict.get();
    if (feed_dict == nullptr) {
      LOG(INFO) << "dequeue adlog success, user_id: " << input_sample->ad_log->user_info().id()
                << ", item_id: " << input_sample->ad_log->item(0).id();
      continue;
    }
    uint64 ts_start = base::GetTimestamp();
    TF_CHECK_OK(session->Run(*feed_dict, {"top_100:0"}, {}, &outputs));
    uint64 ts_end = base::GetTimestamp();
    LOG_EVERY_N(INFO, 1000) << "session run top_k time: " << (ts_end - ts_start) / 1000 << "ms";
    auto topk_indices = outputs[0].matrix<tensorflow::int32>();
    auto log = input_sample->ad_log->Get();
    const auto& hetu_emb_valid = *(input_sample->ad_log->hetu_emb_valid());
    for (int i = 0; i < topk_indices.dimension(0); i++) {
      std::vector<int> tmp_indices(topk_indices.dimension(1), 0);
      for (int j = 0; j < topk_indices.dimension(1); j++) {
        tmp_indices[j] = topk_indices(i, j);
      }
      std::sort(tmp_indices.begin(), tmp_indices.end());
      int counter = 0;
      int idx = 0;
      for (int j = 0; j < hetu_emb_valid.size(); j++) {
        if (!hetu_emb_valid[j]) {
          continue;
        }
        counter++;
        if (tmp_indices[idx] != counter) {
          continue;
        }
        auto action = log.mutable_item(i)->add_topk_actions();
        action->mutable_item()->CopyFrom(input_sample->ad_log->user_term()->items(j));
        for (float k : (*input_sample->ad_log->user_term_hetu_emb())[j]) {
          action->add_hetu_emb(k);
        }
        idx++;
      }
    }
    std::shared_ptr<std::string> output_str = std::make_shared<std::string>();
    std::string adlog_str;
    if (!log.SerializeToString(&adlog_str) || !base::Base64Encode(adlog_str, output_str.get())) {
      LOG_EVERY_N(WARNING, 10) << "serialize or base64encode error";
    }
    mgr_->PushOutput(output_str);
    outputs.clear();

    tensor_dumper->DumpDebugTensors(const_cast<Session*>(session.get()), *feed_dict);
  }
}

TopKRetrieval::~TopKRetrieval() {
  delete bg_thd_;
}

void TopKRetrieval::Stop() {
  bg_thd_->join();
  LOG(INFO) << "[TopKRetrieval] stop";
}

void TopKRetrievalMgr::Run() {
  while (!is_over.load()) {
    std::shared_ptr<std::string> line = nullptr;
    if (!GetOutput(line)) {
      LOG_EVERY_N(INFO, 1000) << "output queue empty, wait for 10 milliseconds";
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      continue;
    }
    std::cout << *line << std::endl;
  }
}

void TopKRetrievalMgr::PushInput(std::shared_ptr<InputSample> input_sample) {
  while (!is_over.load()) {
    if (data_queue.size_approx() < FLAGS_feed_queue_max_size) {
      LOG_EVERY_N(INFO, 1000) << "queue size=" << data_queue.size_approx();
      data_queue.put(input_sample);
      break;
    } else {
      std::this_thread::sleep_for(std::chrono::microseconds(10));
    }
  }
}

void TopKRetrievalMgr::PushOutput(std::shared_ptr<std::string> output_sample) {
  while (!is_over.load()) {
    if (output_queue.size_approx() < FLAGS_feed_queue_max_size) {
      LOG_EVERY_N(INFO, 1000) << "queue size=" << output_queue.size_approx();
      output_queue.put(output_sample);
      break;
    } else {
      std::this_thread::sleep_for(std::chrono::microseconds(10));
    }
  }
}

void TopKRetrievalMgr::Stop() {
  is_over.store(true);
  for (auto& retrieval: retrievals_) {
    retrieval->Stop();
  }
  bg_thd_->join();
  LOG(INFO) << "[TopKRetrievalMgr] stop";
}

TopKRetrievalMgr::TopKRetrievalMgr(TensorDumper* tensor_dumper) {
  is_over.store(false);
  bg_thd_ = new std::thread(std::bind(&TopKRetrievalMgr::Run, this));
  for (int i = 0; i < FLAGS_num_retrievals; ++i) {
    retrievals_.emplace_back(new TopKRetrieval(this, tensor_dumper));
  }
  LOG(INFO) << "[TopKRetrievalMgr] init. worker_size=" << retrievals_.size();
}

TopKRetrievalMgr::~TopKRetrievalMgr() {
  for (auto& retrieval: retrievals_) {
    delete retrieval;
  }
  retrievals_.clear();
  delete bg_thd_;
}

bool TopKRetrievalMgr::GetInput(std::shared_ptr<InputSample>& input_sample) {
  return data_queue.get(input_sample);
}

bool TopKRetrievalMgr::GetOutput(std::shared_ptr<std::string>& output_sample) {
  return output_queue.get(output_sample);
}
}  // namespace ad_algorithm
}  // namespace ks

