#pragma once

#include <string>
#include <thread>
#include <vector>

#include "base/common/base.h"
#include "base/encoding/base64.h"
#include "base/strings/string_split.h"
#include "third_party/lz4/lib/lz4.h"
#include "third_party/abseil/absl/strings/str_cat.h"
#include "third_party/snappy/snappy.h"
#include "learning/kuiba/base/hdfs_line_bin_file.h"

#include "teams/reco-arch/colossus/proto/long_term_service.pb.h"
#include "teams/reco-arch/colossus/storage/item.h"

namespace ks {
namespace ad_algorithm {

DEFINE_string(scr_output_hdfs_dir, "", "output directory");
DEFINE_bool(scr_use_snappy, false, "use snappy compress");
DEFINE_bool(scr_use_lz4, false, "use snappy compress");
DEFINE_int32(lz4_compress_level, 1, "lz4 compress level");

using colossus::SimItem;
using colossus::SingleTerm;
using colossus::UserTerm;

bool ParseUserTerm(const uint32_t& code, const uint64_t& key, const char* value, const int size,
                   kuiba::HdfsLineBinFile* text_file) {
  size_t type_size = sizeof(SimItem);
  CHECK_EQ(size % type_size, 0) << size << " " << type_size;
  int item_num = size / type_size;
  LOG_EVERY_N(INFO, 10000) << "code: " << code << " user_id: " << key << " item_num: " << item_num;
  // serialize, compress and write to hdfs
  thread_local UserTerm user_term;
  user_term.Clear();
  user_term.set_user_id(key);
  for (int i = 0; i < item_num; ++i) {
    reinterpret_cast<const SimItem*>(value + type_size * i)->CopyTo(user_term.add_items());
  }
  thread_local std::string user_term_seri;
  user_term_seri.clear();
  thread_local std::string user_term_base64;
  user_term_base64.clear();
  if (!user_term.SerializeToString(&user_term_seri)) {
    LOG(ERROR) << "serialize failed";
    return false;
  }
  if (FLAGS_scr_use_lz4 || FLAGS_scr_use_snappy) {
    thread_local std::string cmp_data;
    cmp_data.clear();
    if (FLAGS_scr_use_lz4) {
      thread_local std::string lz4_dst;
      thread_local LZ4_stream_t state;
      auto size_bound = LZ4_compressBound(user_term_seri.size());
      if (lz4_dst.size() < size_bound) {
        lz4_dst.resize(size_bound*1.2, '\0');
      }
      int compressed_size = LZ4_compress_fast_extState(&state, static_cast<const char*>(user_term_seri.data()),
                                                       &lz4_dst[0], user_term_seri.size(), lz4_dst.size(),
                                                       FLAGS_lz4_compress_level);
      cmp_data.assign(lz4_dst.data(), compressed_size);
    }
    else if (FLAGS_scr_use_snappy &&
        !snappy::Compress(user_term_seri.data(), user_term_seri.size(), &cmp_data)) {
      LOG(ERROR) << "Compress failed";
      return false;
    }
    if (!base::Base64Encode(cmp_data, &user_term_base64)) {
      LOG(ERROR) << "base64encode failed";
      return false;
    }
  } else {
    if (!base::Base64Encode(user_term_seri, &user_term_base64)) {
      LOG(ERROR) << "base64encode failed";
      return false;
    }
  }
  std::string output_line = absl::StrCat(key, "\t", user_term_base64);
  text_file->write_line(&output_line, false);
  return true;
}

bool HdfsBinaryToText(kuiba::HdfsLineBinFile* file,
                      kuiba::HdfsLineBinFile* text_file, uint64_t* key_num) {
  if (!file || !key_num) return false;
  std::string line_data;
  uint64_t key;
  uint32_t value_size;
  uint32_t code;
  file->read_line(&line_data);
  code = *reinterpret_cast<const uint32_t*>(line_data.data());
  while (!file->empty() && file->read_line(&line_data) && !line_data.empty()) {
    key = *reinterpret_cast<const uint64_t*>(line_data.data());
    value_size = line_data.size() - sizeof(uint64_t);
    if (ParseUserTerm(code, key, line_data.data() + sizeof(uint64_t), value_size, text_file)) {
      (*key_num)++;
    }
  }
  return true;
}

class AdSimCkptReader {
  int read_thread_num_;
  std::vector<std::vector<std::string>> files_;
  std::vector<std::thread> threads_;

 public:
  // 读取 checkpoint 里面的 key/value，每个 value 里面有 N 个 items，需要用户自己解析
  AdSimCkptReader(int read_thread_num) :
        read_thread_num_(read_thread_num),
        files_(read_thread_num, std::vector<std::string>()) {}

  // 只想加载某个文件
  void LoadSingleFile(const std::string& path) {
    files_.resize(1);
    files_[0].push_back(path);
    threads_.emplace_back(&AdSimCkptReader::ThreadFunc, this, 0);
  }

  // 加载某个服务的所有 checkpoint 数据
  void LoadAllFiles(const std::vector<std::string>& paths) {
    int id = 0;
    for (const auto& path : paths) {
      LOG(INFO) << "target path: " << path;
      files_[id % read_thread_num_].push_back(path);
      ++id;
    }
    for (int i = 0; i < read_thread_num_; ++i) {
      threads_.emplace_back(&AdSimCkptReader::ThreadFunc, this, i);
    }
  }

  void WaitAllThreadsDone() {
    for (size_t i = 0; i < threads_.size(); ++i) {
      if (threads_[i].joinable()) threads_[i].join();
    }
  }

 private:
  void ThreadFunc(int thread_id) {
    for (const auto& file_name : files_[thread_id]) {
      kuiba::HdfsLineBinFile bin_file("ad");
      kuiba::HdfsTextFile text_file("ad");
      CHECK(bin_file.open(file_name, O_RDONLY)) << file_name;
      std::vector<std::string> path_segs;
      base::SplitString(file_name, "/", &path_segs);
      CHECK(path_segs.size() >= 2) << file_name;
      std::string out_file_name = absl::StrCat(FLAGS_scr_output_hdfs_dir, "/",
                                               path_segs[path_segs.size() - 2], "/",
                                               path_segs[path_segs.size() - 1]);
      CHECK(text_file.open(out_file_name, O_WRONLY)) << out_file_name;
      uint64_t key_num = 0;
      LOG(INFO) << "Start loading file " << file_name;
      LOG(INFO) << "Start writing file " << out_file_name;
      HdfsBinaryToText(&bin_file, &text_file, &key_num);
      LOG(INFO) << "Finish loading file " << file_name << " total key_num: " << key_num;
    }
  }
};

}  // namespace ad_algorithm
}  // namespace ks
