#include <fstream>
#include <string>
#include <random>
#include <atomic>

#include "tensorflow/core/protobuf/meta_graph.pb.h"
#include "tensorflow/core/public/session.h"
#include "tensorflow/core/graph/default_device.h"
#include "teams/ad/ad_nn/feature/ad_field_feature.h"
#include "teams/ad/ad_base/src/container/concurrent_queue.h"
#include "teams/ad/ad_algorithm/util/input_queue.h"
#include "teams/ad/ad_algorithm/log_preprocess/log_preprocess.h"
#include "teams/ad/ad_algorithm/log_preprocess/log_filter_def.h"
#include "teams/ad/ad_algorithm/log_preprocess/item_filter_def.h"
#include "teams/ad/ad_algorithm/log_preprocess/label_extractor_def.h"
#include "teams/ad/ad_algorithm/log_preprocess/debug_info_def.h"
#include "teams/ad/ad_algorithm/feature/fast/ad_log_wrapper.h"
#include "teams/ad/ad_algorithm/util/hdfs_util.h"

#include "base/common/gflags.h"
#include "base/common/base.h"
#include "base/common/logging.h"
#include "base/time/timestamp.h"
#include "base/strings/string_split.h"
#include "base/encoding/base64.h"
#include "base/encoding/line_escape.h"

#include "teams/ad/ad_nn/gpu_ps/common/stream/stream_mgr.h"

using namespace std;
using namespace ks::ad_algorithm;

using Tensor = tensorflow::Tensor;
using TensorShape = tensorflow::TensorShape;
using Session = tensorflow::Session;
using SessionOptions = tensorflow::SessionOptions;

DEFINE_string(ks_feature_config_path, "../data/dsp_ctr_field_list.txt","ks feature config path");
DEFINE_int32(max_queue_size, 1000000, "");
DEFINE_string(graph_path, "./graph.pb", "graph path");
DEFINE_int32(batch_size, 100, "batch_size");
DEFINE_int32(num_threads, 1, "num_thread");
DEFINE_string(model_tf, "", "");
DEFINE_string(prefix, "hdfs://td-m3.cp", "");
DEFINE_string(suffix, "model_tf", "");
DEFINE_string(tab, "EXPLORE", "");
DEFINE_string(target, "ctr", "");
DEFINE_string(item_type, "AD_DSP", "");
DEFINE_string(item_filter, "", "item filter functions");
DEFINE_double(sample_rate, 1.0, "");
DEFINE_int32(bucket_size, 100000, "");
DEFINE_string(label_extractor, "", "");
DEFINE_int32(intra_op_parallelism_threads, 0, "intra op parallelism_threads");
DEFINE_int32(inter_op_parallelism_threads, 0, "inter op parallelism_threads");

std::vector<std::atomic<bool>> finish_flags(FLAGS_num_threads);

const auto hdfs_prefix = "viewfs://hadoop-lt-cluster";
 
bool IsOver() {
  bool ret = true;
  for (int i = 0; i < FLAGS_num_threads; i++) {
    ret &= finish_flags[i].load();
    if (!ret) {
      break;
    }
  }
  LOG_EVERY_N(INFO, 1000) << "is_over=" << ret;
  return ret;
}

// generate_data
void generate_data_func(size_t id, ks::gpu_ps::Stream* stream,
                        ks::ad_base::AdConcurrentQueue<
                        vector<pair<string, Tensor>> *> *data_queue) {
  std::shared_ptr<std::string> record = nullptr;
  // 更新特征抽取逻辑，用 ks::ad_nn 最新的特征抽取接口
  InputQueue input_queue(FLAGS_ks_feature_config_path, FLAGS_batch_size,
                         FLAGS_max_queue_size, FLAGS_target, data_queue);
  LogPreprocess processor(FLAGS_tab, FLAGS_item_type, FLAGS_item_filter,
                          FLAGS_label_extractor);

  std::mt19937 rng;
  rng.seed(std::random_device()());
  std::uniform_real_distribution<double> dist(0.0, 1.0);

  tensorflow::Tensor labels;
  std::this_thread::sleep_for(std::chrono::seconds(10));
  while (true) {
    auto st = stream->Next(record);
    if (!st.ok()) {
      if (st.code() == ks::gpu_ps::Status::kEof) {
        LOG(INFO) << "[Worker] finish";
      } else {
        LOG(ERROR) << "[Worker] consume fail, worker will stop";
      }
      break;
    }
    if (FLAGS_sample_rate < 1.0) {
      double rand_val = dist(rng);
      if (rand_val > FLAGS_sample_rate) {
        continue;
      }
    }
    base::TrimWhitespaces(const_cast<std::string*>(record.get()));
    string decoded_record;
    if (!base::Base64Decode(*record, &decoded_record)) {
      LOG(ERROR) << "parser base64 failed";
      continue;
    }
    ks::ad_algorithm::AdLogWrapper adlog;
    if (!adlog.parse_from(decoded_record)) {
      LOG(ERROR) << "parse error";
      continue;
    }
    if (processor.preprocess(&adlog, &labels)) {
      adlog.move_items();
      LOG_EVERY_N(INFO, 1000) << "enqueued one sample";
      input_queue.put(adlog, labels);
    }
  }
  std::this_thread::sleep_for(std::chrono::seconds(10));
  finish_flags[id].store(true);
};


// train
void train_func(
    ks::ad_base::AdConcurrentQueue<vector<pair<string, Tensor>> *> *data_queue,
    std::unique_ptr<Session> session) {
  int step = 0;
  std::vector<Tensor> outputs;
  uint64_t start = base::GetTimestamp();
  vector<pair<string, Tensor>> *feed_dict = nullptr;
  
  while (!IsOver()) {
    feed_dict = nullptr;
    if (!data_queue->get(feed_dict)) {
      LOG(INFO) << "data queue empty, try to increase number of threads";
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      continue;
    }
    auto label =
        (*feed_dict)[feed_dict->size() - 2].second.matrix<tensorflow::int64>();
    auto scores = (*feed_dict)[feed_dict->size() - 1].second.matrix<float>();
    feed_dict->pop_back();
    TF_CHECK_OK(session->Run(*feed_dict, {"global_step", "softmax", "loss"},
                             {"train_op"}, &outputs));
    int step = outputs[0].scalar<int>()(0);
    auto prob = outputs[1].matrix<float>();
    auto loss = outputs[2].scalar<float>()(0);

    outputs.clear();
    if (feed_dict != nullptr) {
      delete feed_dict;
    }
  }
}

int main(int argc, char *argv[]) {
  base::InitApp(&argc, &argv, "train dnn model");
  ks::gpu_ps::StreamOption stream_opt;
  stream_opt.src = ks::gpu_ps::kHdfs;
  stream_opt.file_list = {"viewfs://hadoop-lt-cluster/home/<USER>/liliangmin/tmp/test_adlog_07_01/fanstop_click-2021-07-01-18"};
  ks::gpu_ps::StreamMgr* stream_mgr = new ks::gpu_ps::StreamMgr(stream_opt, FLAGS_num_threads);
  LOG(INFO) << "[StreamMgr] created. worker_size=" << FLAGS_num_threads;
  auto st = stream_mgr->Init();
  if (!st.ok()) {
    return 1;
  }
  LOG(INFO) << "[StreamMgr] started.";

  ks::ad_base::AdConcurrentQueue<vector<pair<string, Tensor>> *> data_queue;

  SessionOptions options;
  options.config.set_inter_op_parallelism_threads(FLAGS_inter_op_parallelism_threads);
  options.config.set_intra_op_parallelism_threads(FLAGS_intra_op_parallelism_threads);
  std::unique_ptr<Session> session(NewSession(options));

  tensorflow::MetaGraphDef meta_graph_def;
  TF_CHECK_OK(
      tensorflow::ReadBinaryProto(tensorflow::Env::Default(), FLAGS_graph_path, &meta_graph_def));
  TF_CHECK_OK(session->Create(meta_graph_def.graph_def()));
  TF_CHECK_OK(session->Run({}, {}, {"init_op"}, nullptr));
  auto recover_model = [&]() {
    bool restore_success = false;
    if (FLAGS_model_tf != "") {
        LOG(INFO) << "restore model from " << hdfs_prefix + FLAGS_model_tf + "/" + FLAGS_suffix;
        Tensor restore_model_path(tensorflow::DataType::DT_STRING, TensorShape({1, 1}));
        restore_model_path.matrix<tensorflow::string>()(0, 0) =
          hdfs_prefix + FLAGS_model_tf + "/" + FLAGS_suffix;
        TF_CHECK_OK(session->Run({{"save/Const:0", restore_model_path}}, {},
                                 {"save/restore_all"}, nullptr));
        LOG(INFO) << "restore model done";
        restore_success = true;
    }
    if (!restore_success) {
      LOG(FATAL) << "restore model fail";
      exit(-1);
    }
  };
  recover_model();

  for (int i = 0; i < FLAGS_num_threads; i++) {
    finish_flags[i].store(false);
  }
  std::thread *threads = new std::thread[FLAGS_num_threads];
  for (size_t i = 0; i < FLAGS_num_threads; ++i) {
    auto stream = stream_mgr->alloc_stream();
    if (stream) {
      threads[i] = std::thread(generate_data_func, i, stream, &data_queue);
    } else {
      LOG(ERROR) << "fail to allocate stream for thread " << i << ", exit";
      return 1;
    }
  }
  std::thread train_thread(train_func, &data_queue,std::move(session));
  for (size_t i = 0; i < FLAGS_num_threads; ++i) {
    threads[i].join();
  }
  train_thread.join();

  return 0;
}
