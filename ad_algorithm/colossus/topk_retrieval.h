#pragma once

#include <string>
#include <vector>
#include <atomic>
#include "tensorflow/core/public/session.h"

#include "teams/ad/ad_base/src/container/concurrent_queue.h"

#include "teams/ad/ad_algorithm/colossus/util.h"

namespace ks {
namespace ad_algorithm {

using Session = tensorflow::Session;
using Tensor = tensorflow::Tensor;

class TopKRetrievalMgr;

class TopKRetrieval {
 public:
  TopKRetrieval(TopKRetrievalMgr* mgr, TensorDumper* tensor_dumper);
  ~TopKRetrieval();
  void Run(TensorDumper* tensor_dumper);
  void Stop();
 private:
  std::unique_ptr<Session> session;
  TopKRetrievalMgr* mgr_;
  std::thread* bg_thd_;
};

class TopKRetrievalMgr {
 public:
  TopKRetrievalMgr(TensorDumper* tensor_dumper);
  ~TopKRetrievalMgr();
  bool GetInput(std::shared_ptr<InputSample>& input_sample);
  void PushInput(std::shared_ptr<InputSample> input_sample);
  void PushOutput(std::shared_ptr<std::string> input_sample);
  bool GetOutput(std::shared_ptr<std::string>& output_sample);
  void Run();
  void Stop();
  bool IsOver() {
    return is_over.load();
  }
 private:
  ks::ad_base::AdConcurrentQueue<std::shared_ptr<InputSample>> data_queue;
  ks::ad_base::AdConcurrentQueue<std::shared_ptr<std::string>> output_queue;
  std::vector<TopKRetrieval*> retrievals_;
  std::atomic<bool> is_over;
  std::thread* bg_thd_;
};
}  // namespace ad_algorithm
}  // namespace ks
