cc_binary(name="sim_checkpoint_reader",
          srcs=[
            "sim_checkpoint_reader.cc",
          ],
          deps=[
            "//base/testing/BUILD:test_main",
            "//teams/reco-arch/colossus/BUILD:checkpoint_helper",
            "//teams/reco-arch/colossus/BUILD:item",
          ],
          )

cc_library(name = "util",
          srcs = ["util.cc"],

          cppflags = [
                     "-D GOOGLE_LOGGING=1",
                     ],
          deps = [
                  "//base/strings/BUILD:strings",
                  "//third_party/tensorflow-1.15/BUILD:tensorflow_cc",
                  "//teams/ad/ad_algorithm/feature/BUILD:fast_feature",
                ],
          )

cc_library(name = "topk_retrieval",
          srcs = ["topk_retrieval.cc"],

          cppflags = [
                     "-D GOOGLE_LOGGING=1",
                     ],
          deps = [
                  ":util",
                  "//third_party/tensorflow-1.15/BUILD:tensorflow_cc",
                  "//teams/reco-arch/colossus/BUILD:client",
                  "//teams/ad/picasso/BUILD:picassobatch_sdk"
                ],
          )

cc_library(name = "sample_generator",
          srcs = ["sample_generator.cc"],

          cppflags = [
                     "-D GOOGLE_LOGGING=1",
                     "-D USE_HDFS",
                     "-Ithird_party/flatbuffers/include/",
                     "-Ithird_party/glog",
                     ],
          deps = [
                  ":topk_retrieval",
                  "//third_party/glog/BUILD:glog",
                  "//base/common/BUILD:base",
                  "//teams/ad/ad_algorithm/log_preprocess/BUILD:log_preprocess",
                  "//teams/ad/ad_nn/feature/BUILD:feature_extract",
                  # "//teams/ad/ad_nn/kafka_feature/BUILD:kafka_feature_proto",
                ],
          )

cc_binary(name = "test_topk_retrieval",
          srcs = ["test_topk_retrieval.cc"],

          cppflags = [
                     "-D GOOGLE_LOGGING=1",
                     "-D USE_HDFS",
                     "-Ithird_party/flatbuffers/include/",
                     "-Ithird_party/glog",
                     ],
          deps = [
                  ":topk_retrieval",
                  ":sample_generator",
                ],
          )

cc_binary(name = "test_picasso",
          srcs = ["test_picasso.cc"],
          deps = [
                  "//base/common/BUILD:base",
                  "//base/time/BUILD:time",
                  "//third_party/glog/BUILD:glog",
                  "//third_party/abseil/BUILD:abseil",
                  "//third_party/lz4/BUILD:lz4",
                  "//teams/ad/picasso/BUILD:picassobatch_sdk",
                  "//teams/ad/picasso/BUILD:picasso_sdk",
                  "//teams/reco-arch/colossus/BUILD:proto",
                ],
          )

cc_binary(name = "test_worker_mgr",
          srcs = ["test_worker_mgr.cc"],
          deps = [
                  "//base/common/BUILD:base",
                  "//third_party/glog/BUILD:glog",
                  "//teams/ad/ad_nn/gpu_ps/common/BUILD:stream",
                  "//learning/kuiba/base/BUILD:base",
                ],
          )
