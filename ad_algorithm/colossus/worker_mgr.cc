#include "teams/ad/ad_algorithm/colossus/worker_mgr.h"

#include "base/time/timestamp.h"
#include "base/common/sleep.h" 

DEFINE_int32(feed_queue_max_size, 128, "");

namespace ks {
namespace gpu_ps {

WorkerMgr::WorkerMgr(size_t worker_num,
          const WorkerOption& worker_opt,
          const StreamOption& stream_opt) :
  worker_num_(worker_num), 
  worker_opt_(worker_opt),
  stream_opt_(stream_opt) {

  LOG(INFO) << "new WorkerMgr for model[" << worker_opt_.model_name << "]: " 
            << " worker_opt_.batch_size is " << worker_opt_.batch_size
            << " worker_opt_.embedding_size is " << worker_opt_.embedding_size
            << " worker_opt_.shard_addr is " << worker_opt_.shard_addr
            << " worker_opt_.dense_total_size is " << worker_opt_.dense_total_size
            << " worker_opt_.need_batch is " << worker_opt_.need_batch
            << " worker_opt_.label_extractor is " << worker_opt_.label_extractor
            << " worker_opt_.target is " << worker_opt_.target
            << " worker_opt_.tab is " << worker_opt_.tab
            << " worker_opt_.item_type is " << worker_opt_.item_type
            << " worker_opt_.item_filter is " << worker_opt_.item_filter
            << " worker_opt_.feature_config_path is " << worker_opt_.feature_config_path
            << " worker_opt_.src_type is " << worker_opt_.src_type
            << " worker_opt_.debug_info is " << worker_opt_.debug_info;

  LOG(INFO) << "new WorkerMgr for stream[" << stream_opt_.name << "]: " 
            << " stream_opt_.dir_path is " << stream_opt_.dir_path
            << " stream_opt_.file_list.size() is " << stream_opt_.file_list.size()
            << " stream_opt_.topic_id is " << stream_opt_.topic_id
            << " stream_opt_.group_id is " << stream_opt_.group_id
            << " stream_opt_.consumer_user_param is " << stream_opt_.consumer_user_param
            << " stream_opt_.src is " << stream_opt_.src;

  stream_mgr_ = new ks::gpu_ps::StreamMgr(stream_opt_, worker_num);
  LOG(INFO) << "[WorkerMgr] created. worker_size=" << worker_num;
}

WorkerMgr::~WorkerMgr() {
  for (auto& worker: workers_) {
    delete worker;
  }
  workers_.clear();
  delete stream_mgr_;
}

Status WorkerMgr::Init() {
  int32_t dense_total_size = 0;
  for (auto& field_size: worker_opt_.dense_field_count) {
    dense_total_size += field_size;
  }
  worker_opt_.dense_total_size = dense_total_size;
  
  auto st = stream_mgr_->Init();
  if (!st.ok()) {
    return st;
  }

  for (int i = 0; i < worker_num_; ++i) {
    auto stream = stream_mgr_->alloc_stream();
    if (stream) {
      workers_.emplace_back(new Worker(stream, worker_opt_, this));
    } else {
      break;
    }
  }

  for (auto& worker: workers_) {
    auto st = worker->Init();
    if (!st.ok()) {
      return st;
    }
  }
  LOG(INFO) << "[WorkerMgr] init. worker_size=" << workers_.size();
  return Status::OK();
}

Status WorkerMgr::Start() {
  auto st = stream_mgr_->Start();
  if (!st.ok()) {
    return st;
  }

  for (auto& worker: workers_) {
    auto st = worker->Start();
    if (!st.ok()) {
      return st;
    }
  }
  LOG(INFO) << "[WorkerMgr] start";
  return Status::OK();
}

Status WorkerMgr::Stop() {
  for (auto& worker: workers_) {
    auto st = worker->Stop();
    if (!st.ok()) {
      return st;
    }
  }

  auto st = stream_mgr_->Stop();
  if (!st.ok()) {
    return st;
  }

  LOG(INFO) << "[WorkerMgr] stop";
  return Status::OK();
}

bool WorkerMgr::PushFeedQueue(std::shared_ptr<SampleType> one_sample) {
//if (label_dense_queue_.size_approx() < FLAGS_feed_queue_max_size) {
//  LOG(INFO) << "queue size=" << label_dense_queue_.size_approx();
//  return label_dense_queue_.put(one_sample);
//} else {
//  return false;
//}
  std::unique_lock<std::mutex> lock(mu_);
  if (label_dense_queue_.size() <= FLAGS_feed_queue_max_size) {
    label_dense_queue_.push(one_sample);
    LOG_EVERY_N(INFO, 100) << "queue size=" << label_dense_queue_.size();
    return true;
  } else {
    return false;
  }
}

std::shared_ptr<SampleType> WorkerMgr::PopFeedQueue() {
//std::shared_ptr<SampleType> one_sample;
//if (label_dense_queue_.get(one_sample) && one_sample != nullptr) {
//  return one_sample;
//}
//return nullptr;
  std::unique_lock<std::mutex> lock(mu_);
  if (label_dense_queue_.size() == 0) {
    return nullptr;
  }
  std::shared_ptr<SampleType> one_sample = label_dense_queue_.front();
  label_dense_queue_.pop();
  return one_sample;
}

bool WorkerMgr::IsOver() {
  bool ret = true;
  for (auto& worker: workers_) {
    ret &= worker->IsOver();
    if (!ret) {
      break;
    }
  }

  if (label_dense_queue_.size() > 0) {
    ret = false;
  }

  LOG_EVERY_N(INFO, 500) << "[WorkerMgr] is_over=" << ret;
  return ret;
}
 
}  // namespace gpu_ps
}  // namespace ks
