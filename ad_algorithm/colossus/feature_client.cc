#include "teams/ad/ad_algorithm/colossus/feature_client.h"
#include <sstream>
#include <thread>
#include <chrono>
#include <iostream>
#include "base/common/gflags.h"
#include "base/common/logging.h"
#include "base/strings/string_split.h"
#include "base/strings/string_printf.h"
#include "base/encoding/base64.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "teams/ad/ad_nn/gpu_ps/messenger/zmq_pull.h"
#include "teams/ad/ad_nn/gpu_ps/util/util.h"
#include "teams/ad/ad_nn/gpu_ps/proto/ad_gpu_hub.pb.h"

DEFINE_int32(start_timeout, 45000, "pull timeout");
DEFINE_int32(stop_timeout,  30000, "stop timeout");
DEFINE_int32(next_batch_timeout, 30000, "pull timeout");

namespace ks {
namespace gpu_ps {

FeatureClient::FeatureClient() {}

bool FeatureClient::NextBatch(int64_t* batch_id,
                             int32_t* label, int32_t label_count,
                             float* dense_feature, int32_t dense_feature_count,
                             int32_t* debug_sizes, int batch_size, char* debug_info_str) {
  auto start = std::chrono::high_resolution_clock::now();

  // 校验当前消息
  {
    zmq::message_t label_msg = res_msg->Pop();
    if (label_msg.size() != label_count * sizeof(int32_t)) {
      LOG(ERROR) << "label size not match"
                 << " label msg size=" << label_msg.size()
                 << " label count size=" << label_count * sizeof(int32_t);
      return false;
    }
    int32_t* src = label_msg.data<int32_t>();
    std::memcpy(label, src, label_count * sizeof(int32_t));
  }

  {
    zmq::message_t dense_msg = res_msg->Pop();
    if (dense_msg.size() != dense_feature_count * sizeof(float)) {
      LOG(ERROR) << "dense feature size not match"
                 << " dense msg size=" << dense_msg.size()
                 << " dense count size=" << dense_feature_count * sizeof(float);
      return false;
    }
    float* src = dense_msg.data<float>();
    std::memcpy(dense_feature, src, dense_feature_count * sizeof(float));
  }

  {
    zmq::message_t debug_msg = res_msg->Pop();
    if (debug_msg.size() > 0) {
      for (int i = 0; i < batch_size; i++) {
        memcpy(debug_sizes + i, debug_msg.data<char>() + i * sizeof(int32_t), sizeof(int32_t));
      }
      int info_len = debug_msg.size() - batch_size * sizeof(int32_t);
      if (info_len < 1000 * batch_size - 1) {
        std::memcpy(debug_info_str, debug_msg.data<char>() + batch_size * sizeof(int32_t), info_len);
        debug_info_str[info_len] = '\0';
      } else {
        LOG(INFO) << "debug info size is bigger than 1000 * " << batch_size
                  << ", trim";
        std::memcpy(debug_info_str, debug_msg.data<char>() + batch_size * sizeof(int32_t),
                    1000 * batch_size - 1);
        debug_info_str[1000 * batch_size - 1] = '\0';
      }
    }
  }

  auto end = std::chrono::high_resolution_clock::now();
  falcon::Stat("gpu_hub_client.pull_handle_response",
               DurationByMicrosecond(start, end));
  return b;
}

}  // namespace gpu_ps
}  // namespace ks
