#pragma once

#include <string>
#include <vector>
#include <atomic>

#include "teams/ad/ad_base/src/container/concurrent_queue.h"
#include "teams/ad/ad_algorithm/feature/fast/ad_log_wrapper.h"
#include "teams/ad/picasso/sdk/picassobatch_client/picasso_client.h"
#include "teams/ad/ad_algorithm/colossus/util.h"
#include "teams/ad/ad_algorithm/colossus/topk_retrieval.h"

namespace ks {
namespace ad_algorithm {

class SampleGeneratorMgr;

class SampleGenerator {
 public:
  SampleGenerator(SampleGeneratorMgr* mgr, TopKRetrievalMgr* retr_mgr);
  ~SampleGenerator();
  void Run(TopKRetrievalMgr* retr_mgr);
  void GetUserHistoryEmb(ks::ad_picasso_batch::sdk::PicassoClient* picasso,
                         ks::ad_algorithm::AdLogWrapper* ad_log);
  void Stop();
 private:
  SampleGeneratorMgr* mgr_;
  TopKRetrievalMgr* retr_mgr_;
  std::thread* bg_thd_;
};

class SampleGeneratorMgr {
 public:
  SampleGeneratorMgr(TopKRetrievalMgr* retr_mgr);
  ~SampleGeneratorMgr();
  void PushInput(std::shared_ptr<std::string> line);
  bool GetInput(std::shared_ptr<std::string>& line);
  void ReadFromStdIn();
  void ReadFromHdfs();
  void Stop();
  bool IsOver() {
    return is_over.load();
  }
 private:
  ks::ad_base::AdConcurrentQueue<std::shared_ptr<std::string>> input_queue;
  std::thread* bg_thd_;
  std::vector<SampleGenerator*> generators_;
  std::atomic<bool> is_over;
  uint64 total_count;
};
}  // namespace ad_algorithm
}  // namespace ks
