#include "teams/ad/ad_algorithm/colossus/sample_generator.h"

#include <iostream>

#include "base/common/gflags.h"
#include "base/time/timestamp.h"
#include "base/strings/string_printf.h"
#include "serving_base/jansson/json.h"
#include "learning/kuiba/base/hdfs_line_bin_file.h"
#include "tensorflow/core/public/session.h"
#include "tensorflow/core/protobuf/meta_graph.pb.h"
#include "tensorflow/core/framework/tensor_util.h"

#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.pb.h"
#include "teams/ad/ad_algorithm/log_preprocess/log_filter_def.h"
#include "teams/ad/ad_algorithm/log_preprocess/item_filter_def.h"
#include "teams/ad/ad_algorithm/log_preprocess/label_extractor_def.h"
#include "teams/ad/ad_algorithm/log_preprocess/debug_info_def.h"
#include "teams/ad/ad_algorithm/log_preprocess/log_preprocess.h"
#include "teams/ad/ad_nn/feature/feature_extractor_bundle.h"

DEFINE_string(tab, "", "");
DEFINE_string(item_type, "", "");
DEFINE_string(item_filter, "", "");
DEFINE_string(label_extractor, "", "");
DEFINE_string(debug_info, "", "");
DEFINE_string(ks_feature_config_path, "", "");
DEFINE_int32(num_generators, 1, "");
DEFINE_int32(input_queue_max_size, 10, "");
DEFINE_int32(num_actions, 10000, "");
DEFINE_int32(num_picasso_threads, 1, "");
DEFINE_bool(scr_env_file_list, false, "");
DEFINE_bool(use_batch_api, true, "");
DEFINE_int32(batch_size, 10000, "");

namespace ks {
namespace ad_algorithm {

using TensorShape = tensorflow::TensorShape;

using LogPreprocess = ks::ad_algorithm::LogPreprocess;
using FeatureFileInfo = ks::ad_nn::FeatureFileInfo;
using FeatureType = ks::ad_nn::FeatureType;
using AdFeatureExtractorBundle = ks::ad_nn::AdFeatureExtractorBundle;
using Tensor = tensorflow::Tensor;
using TensorShape = tensorflow::TensorShape;

SampleGenerator::SampleGenerator(SampleGeneratorMgr* mgr, TopKRetrievalMgr* retr_mgr) {
  mgr_ = mgr;
  bg_thd_ = new std::thread(std::bind(&SampleGenerator::Run, this, std::placeholders::_1), retr_mgr);
}

void SampleGenerator::Run(TopKRetrievalMgr* retr_mgr) {
  std::unique_ptr<LogPreprocess> processor = std::make_unique<LogPreprocess>(
        FLAGS_tab, FLAGS_item_type, FLAGS_item_filter, FLAGS_label_extractor, FLAGS_debug_info);

  std::shared_ptr<FeatureFileInfo> feature_file_info = std::make_shared<FeatureFileInfo>();
  FeatureFileInfo::LoadFeatureFile(FLAGS_ks_feature_config_path, feature_file_info.get());

  std::vector<FeatureType> sparse_feature_type_sorted;
  std::vector<FeatureType> dense_feature_type_sorted;
  sparse_feature_type_sorted.push_back(FeatureType::RECO_USER);
  sparse_feature_type_sorted.push_back(FeatureType::USER);
  sparse_feature_type_sorted.push_back(FeatureType::ITEM);
  sparse_feature_type_sorted.push_back(FeatureType::RECO_ITEM);
  sparse_feature_type_sorted.push_back(FeatureType::COMBINE);
  dense_feature_type_sorted.push_back(FeatureType::DENSE_USER);
  dense_feature_type_sorted.push_back(FeatureType::DENSE_ITEM);
  dense_feature_type_sorted.push_back(FeatureType::DENSE_COMBINE);

  bool is_nohash = false;
  for (const auto& slot : feature_file_info->slots) {
    if (slot.slot_size == 0) {
      is_nohash = true;
    }
  }

  std::unique_ptr<AdFeatureExtractorBundle> ad_fea_ext_bundle_ptr =
      std::make_unique<AdFeatureExtractorBundle>(feature_file_info,
                                                 ks::ad_nn::ExtractType::ALL,
                                                 ks::ad_nn::FeatureOrgLayout::BY_FIELD,
                                                 is_nohash);
  if (is_nohash) {
    auto nohash_convert = [](FeatureType type, uint64_t sign) {
      return sign;
    };
    ad_fea_ext_bundle_ptr->SetSignConvert(nohash_convert);
  }

  int dense_total_size = 0;
  std::vector<int> dense_field_offset;
  dense_field_offset.resize(feature_file_info->dense_slots_count);
  for (int i = 0; i < feature_file_info->dense_slots_count; ++i) {
    if (i == 0) {
      dense_field_offset[i] = 0;
    } else {
      dense_field_offset[i] = dense_field_offset[i-1] + feature_file_info->dense_slots_size[i];
    }
    dense_total_size += feature_file_info->dense_slots_size[i];
  }

  ks::ad_picasso_batch::sdk::PicassoOption opt;
  opt.client_tag = "feature_extractor_client_tag";
  opt.caller_kess_service = "extract_user_reco_sim_sample_test";
  opt.target_kess_service = "grpc_adPicassoBatchOfflineGatewayService";
  ks::ad_picasso_batch::sdk::PicassoClient* picasso = ks::ad_picasso_batch::sdk::PicassoClient::GetInstance();
  if (!picasso->Init(opt)) {
    LOG(FATAL) << "init picasso failed. ";
  }

  while (!mgr_->IsOver()) {
    std::shared_ptr<std::string> line = nullptr;
    if (!mgr_->GetInput(line)) {
      LOG_EVERY_N(INFO, 1000) << "input queue empty, wait for 10 milliseconds";
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      continue;
    }

    base::TrimWhitespaces(line.get());
    string decoded_line;
    ks::ad_algorithm::AdLogWrapper adlog_wrapper;
    if (!base::Base64Decode(*line, &decoded_line)) {
      LOG_EVERY_N(WARNING, 100) << "parser base64 failed";
      continue;
    }
    if (!adlog_wrapper.parse_from(decoded_line)) {
      LOG_EVERY_N(WARNING, 100) << "parser adlog failed";
      continue;
    }
    Tensor t;
    std::vector<std::string> debug_info_strings;
    if (!processor->preprocess(&adlog_wrapper, &t, &debug_info_strings)) {
      LOG_EVERY_N(INFO, 10000) << "log filtered";
      continue;
    }
    adlog_wrapper.move_items();
    LOG_EVERY_N(INFO, 10000) << "parse adlog success, user_id: " << adlog_wrapper.user_info().id()
                     << ", item_id: " << adlog_wrapper.item(0).id();
    auto reco_user_info = adlog_wrapper.mutable_reco_user_info();
    if (adlog_wrapper.Get().serialized_reco_user_info().empty() ||
        !reco_user_info->ParseFromString(adlog_wrapper.Get().serialized_reco_user_info())) {
      LOG_EVERY_N(WARNING, 10000) << "reco_user_info empty or parse reco_user_info error";
    }
    GetUserHistoryEmb(picasso, &adlog_wrapper);
    ks::ad_nn::Type2ExtractResultsPtr sparse_results_bundle;
    ks::ad_nn::Type2ExtractResultsPtr dense_results_bundle;

    const ks::ad_nn::Type2RetIndices& sparse_indices = ad_fea_ext_bundle_ptr->sparse_ret_indices();
    const ks::ad_nn::Type2RetIndices& dense_indices = ad_fea_ext_bundle_ptr->dense_ret_indices();
    ks::ad_nn::CacheInfo cache_info;

    ad_fea_ext_bundle_ptr->ExtractAllSparseFeatures(adlog_wrapper,
                                                    cache_info,
                                                    &sparse_results_bundle,
                                                    ks::ad_nn::FeatureOrgLayout::BY_FIELD);
    ad_fea_ext_bundle_ptr->ExtractAllDenseFeatures(adlog_wrapper,
                                                    cache_info,
                                                    &dense_results_bundle,
                                                    ks::ad_nn::FeatureOrgLayout::BY_FIELD);

    auto input_sample = std::make_shared<ks::ad_algorithm::InputSample>();
    input_sample->ad_log = std::move(std::make_unique<ks::ad_algorithm::AdLogWrapper>(adlog_wrapper));
    input_sample->feed_dict.reset(new std::vector<std::pair<std::string, Tensor>>());
    vector<uint32_t> field_size(feature_file_info->slots_count, 0);
    vector<uint32_t> field_max(feature_file_info->slots_count, 0);
    std::vector<std::vector<std::vector<uint64>>> sparse_values(adlog_wrapper.item_size(),
          std::vector<std::vector<uint64>>(feature_file_info->slots_count));
    std::vector<std::vector<float>> dense_values(adlog_wrapper.item_size(),
          std::vector<float>(dense_total_size));
    for (int i = 0; i < adlog_wrapper.item_size(); i++) {
      std::fill(dense_values[i].begin(), dense_values[i].end(), 0.0f);
    }
    for (size_t i = 0; i < adlog_wrapper.item_size(); ++i) {
      for (auto& sparse_feature_type : sparse_feature_type_sorted) {
        if (sparse_results_bundle.find(sparse_feature_type) == sparse_results_bundle.end()) {
          continue;
        }
        auto& sparse_vec = sparse_results_bundle.find(sparse_feature_type)->second;
        auto& sparse_vec_index = sparse_indices.find(sparse_feature_type)->second;
        if (IsUserType(sparse_feature_type)) {
          for (size_t j = 0; j < sparse_vec->size(); j++) {
            if (sparse_vec->at(j).size() > 0) {
              for (auto& ret : sparse_vec->at(j)) {
                int feature_index = sparse_vec_index[j];
                sparse_values[i][feature_index].push_back(ret.sign);
                field_size[feature_index] += 1;
                if (sparse_values[i][feature_index].size() > field_max[feature_index]) {
                  field_max[feature_index] = sparse_values[i][feature_index].size();
                }
              }
            }
          }
        } else {
          for (size_t j = 0; j < sparse_vec->size(); j++) {
            if (j % adlog_wrapper.item_size() == i) {
              if (sparse_vec->at(j).size() > 0) {
                for (auto& ret : sparse_vec->at(j)) {
                  int feature_index = sparse_vec_index[j / adlog_wrapper.item_size()];
                  sparse_values[i][feature_index].push_back(ret.sign);
                  field_size[feature_index] += 1;
                  if (sparse_values[i][feature_index].size() > field_max[feature_index]) {
                    field_max[feature_index] = sparse_values[i][feature_index].size();
                  }
                }
              }
            }
          }
        }
      }

      int offset = 0;
      for (auto& dense_feature_type : dense_feature_type_sorted) {
        if (dense_results_bundle.find(dense_feature_type) == dense_results_bundle.end()) {
          continue;
        }
        auto& dense_vec = dense_results_bundle.find(dense_feature_type)->second;
        auto& dense_vec_index = dense_indices.find(dense_feature_type)->second;
        if (IsUserType(dense_feature_type)) {
          for (size_t j = 0; j < dense_vec->size(); j++) {
            if (dense_vec->at(j).size() > 0) {
              for (auto& ret : dense_vec->at(j)) {
                int feature_index = dense_vec_index[j];
                if (feature_index < feature_file_info->dense_slots_count &&
                    ret.dense_sign < feature_file_info->dense_slots_size[feature_index]) {
                  dense_values[i][dense_field_offset[feature_index] + ret.dense_sign] = ret.value;
                }
              }
            }
          }
        } else {
          for (size_t j = 0; j < dense_vec->size(); j++) {
            if (j % adlog_wrapper.item_size() == i) {
              if (dense_vec->at(j).size() > 0) {
                for (auto& ret : dense_vec->at(j)) {
                  int feature_index = dense_vec_index[j / adlog_wrapper.item_size()];
                  if (feature_index < feature_file_info->dense_slots_count &&
                      ret.dense_sign < feature_file_info->dense_slots_size[feature_index]) {
                    dense_values[i][dense_field_offset[feature_index] + ret.dense_sign] = ret.value;
                  }
                }
              }
            }
          }
        }
      }
    }
    int64 batch_size = adlog_wrapper.item_size();
    for(int x = 0; x < feature_file_info->slots_count; ++x) {
      Tensor x_index(tensorflow::DT_INT64, TensorShape({field_size[x],2}));
      Tensor x_value(tensorflow::DT_INT64, TensorShape({field_size[x]}));
      Tensor x_shape(tensorflow::DT_INT64, TensorShape({2}));
      auto x_shape_eigen = x_shape.vec<tensorflow::int64>();
      auto x_index_eigen = x_index.matrix<tensorflow::int64>();
      auto x_value_eigen = x_value.vec<tensorflow::int64>();
      x_shape_eigen(0) = batch_size;
      x_shape_eigen(1) = field_max[x];
      size_t c = 0;
      for(size_t j = 0; j < batch_size; ++j) {
        size_t k = 0;
        for(auto ret : sparse_values[j][x]) {
          x_index_eigen(c,0) = j;
          x_index_eigen(c,1) = k;
          x_value_eigen(c) = ret;
          ++c;
          ++k;
        }
        sparse_values[j][x].clear();
      }
      field_size[x] = 0;
      field_max[x] = 0;
      input_sample->feed_dict->emplace_back(std::make_pair(base::StringPrintf("field_%d/indices:0", x), x_index));
      input_sample->feed_dict->emplace_back(std::make_pair(base::StringPrintf("field_%d/values:0", x), x_value));
      input_sample->feed_dict->emplace_back(std::make_pair(base::StringPrintf("field_%d/shape:0", x), x_shape));
    }
    for(int x = 0; x < feature_file_info->dense_slots_count; ++x) {
      Tensor x_dense(tensorflow::DT_FLOAT, TensorShape({batch_size, feature_file_info->dense_slots_size[x]}));
      auto x_dense_eigen = x_dense.matrix<float>();
      for(size_t j = 0; j < batch_size; ++j) {
        for(size_t n = 0; n < feature_file_info->dense_slots_size[x]; ++n) {
          x_dense_eigen(j,n) = dense_values[j][dense_field_offset[x] + n];
          dense_values[j][dense_field_offset[x] + n] = 0.0;
        }
      }
      input_sample->feed_dict->emplace_back(std::make_pair(base::StringPrintf("dense_field_%d:0", x), x_dense));
    } 
    input_sample->feed_dict->emplace_back(std::make_pair("label", t));
    retr_mgr->PushInput(input_sample);
  }
  std::this_thread::sleep_for(std::chrono::seconds(10));
}

void SampleGenerator::GetUserHistoryEmb(ks::ad_picasso_batch::sdk::PicassoClient* picasso,
                                        ks::ad_algorithm::AdLogWrapper* ad_log) {
  if(ad_log->has_user_info()) {
    int64_t user_id = ad_log->user_info().id();
    uint64_t time = ad_log->Get().time() / 1000;
    std::string history_table_name("adSimLongInterest");
    std::string history_key(std::to_string(user_id));
    std::vector<std::string> history_value_v;

    auto ret = ks::ad_picasso_batch::sdk::STATUS_TIMEOUT;
    int l_retry = 1;
    do {
      uint64 ts_start = base::GetTimestamp();
      ret = picasso->HGetAll(history_table_name, history_key, history_value_v);
      uint64 ts_end = base::GetTimestamp();
      LOG_EVERY_N(INFO, 1000) << "sim colossus fetch time: " << (ts_end - ts_start) / 1000 << "ms";
    } while (--l_retry > 0 && ret != ks::ad_picasso_batch::sdk::STATUS_OK && ret != ks::ad_picasso_batch::sdk::STATUS_NOT_FOUND);
    if (ret != ks::ad_picasso_batch::sdk::STATUS_OK) {
      LOG_EVERY_N(WARNING, 1000) << history_table_name << " get picasso failed: " << history_key;
      return;
    }
    // 历史行为序列处理
    // lz4解压缩
    std::string lz4_dst;
    lz4_dst.resize(history_value_v[0].size() * 4);
    int decom_size = LZ4_decompress_safe(history_value_v[0].data(), &lz4_dst[0], history_value_v[0].size(), lz4_dst.size());
    if (decom_size < 0) {
      LOG_EVERY_N(WARNING, 100) << "LZ4 decompress failed.";
      return;
    }
    lz4_dst.resize(decom_size);
    // 反序列化为UserTerm
    if (!ad_log->mutable_user_term()->ParseFromString(lz4_dst)) {
      LOG_EVERY_N(WARNING, 100) << "UserTerm ParseFromString failed.";
      return;
    }
    uint64 ts_start = base::GetTimestamp();
    // hetu embedding 读取
    const int64_t emb_size = 128;
    ad_log->mutable_user_term_hetu_emb()->resize(ad_log->user_term()->items_size(), std::vector<float>(emb_size, 0.0));
    ad_log->mutable_hetu_emb_valid()->resize(ad_log->user_term()->items_size(), false);
    std::vector<std::thread*> tasks;
    int task_num = ad_log->user_term()->items_size() > FLAGS_num_picasso_threads ? FLAGS_num_picasso_threads : ad_log->user_term()->items_size();
    int cnt_per_task = (ad_log->user_term()->items_size() + task_num - 1) / task_num;
    for (int t = 0; t < task_num; t++) {
      auto func = [t, cnt_per_task, time, picasso, ad_log] () {
        std::vector<std::string> pids;
        ks::ad_picasso_batch::sdk::PicassoValuesResultOfKeys res;
        for (int j = t * cnt_per_task; j < (t + 1) * cnt_per_task && j < ad_log->user_term()->items_size(); j++) {
          if (FLAGS_use_batch_api) {
            pids.push_back(std::to_string(ad_log->user_term()->items(j).photo_id()));
            if ((j + 1) % FLAGS_batch_size == 0 || j == (t + 1) * cnt_per_task - 1 || j == ad_log->user_term()->items_size() - 1) {
              res.clear();
              auto ret = picasso->BatchHGetAll("adHetuEmbedding", pids, res);
              for (int k = 0; k < res.size(); k++) {
                const auto& r = res[k];
                if (r.first != ks::ad_picasso_batch::sdk::STATUS_OK) {
                  LOG_EVERY_N(WARNING, 1000000) << " get_hetu_key_failed: " << pids[k];
                  continue;
                }
                std::vector<std::string> str_vec;
                base::SplitString(r.second[0], std::string(","), &str_vec);
                if (str_vec.size() != emb_size) {
                  LOG_EVERY_N(WARNING, 100) << "hetu embedding size != " << emb_size << " : " << r.second[0];
                  continue;
                }
                for (int i = 0; i < str_vec.size(); ++i) {
                  float element = 0.0;
                  bool valid_double = absl::SimpleAtof(str_vec[i], &element);
                  (*ad_log->mutable_user_term_hetu_emb())[j-res.size()+1+k][i] = element;
                }
                (*ad_log->mutable_hetu_emb_valid())[j-res.size()+1+k] = ad_log->user_term()->items(j-res.size()+1+k).timestamp() <= time;
              }
              pids.clear();
            }
            continue;
          }
          if (ad_log->user_term()->items(j).timestamp() > time) continue;
          std::string hetu_key(std::to_string(ad_log->user_term()->items(j).photo_id()));
          std::vector<std::string> hetu_value_v;
          auto ret = ks::ad_picasso_batch::sdk::STATUS_TIMEOUT;
          ret = picasso->HGetAll("adHetuEmbedding", hetu_key, hetu_value_v);
          if (ret != ks::ad_picasso_batch::sdk::STATUS_OK) {
            LOG_EVERY_N(WARNING, 1000000) << " get_hetu_key_failed: " << hetu_key;
            continue;
          }
          std::vector<std::string> str_vec;
          base::SplitString(hetu_value_v[0], std::string(","), &str_vec);
          if (str_vec.size() != emb_size) {
            LOG_EVERY_N(WARNING, 100) << "hetu embedding size != " << emb_size << " : " << hetu_value_v[0];
            continue;
          }
          for (int i = 0; i < str_vec.size(); ++i) {
            float element = 0.0;
            bool valid_double = absl::SimpleAtof(str_vec[i], &element);
            (*ad_log->mutable_user_term_hetu_emb())[j][i] = element;
          }
          (*ad_log->mutable_hetu_emb_valid())[j] = true;
        }
      };
      tasks.push_back(new std::thread(func));
    }
    for (std::thread* t : tasks) {
      t->join();
    }
    for (std::thread* t : tasks) {
      delete t;
    }
    uint64 ts_end = base::GetTimestamp();
    LOG_EVERY_N(INFO, 1000) << "hetu embedding fetch time: " << (ts_end - ts_start) / 1000 << "ms";
  }
}

SampleGenerator::~SampleGenerator() {
  delete bg_thd_;
}

void SampleGenerator::Stop() {
  bg_thd_->join();
  LOG(INFO) << "[SampleGenerator] stop";
}

void SampleGeneratorMgr::ReadFromHdfs() {
  std::vector<std::string> file_list;
  base::Json path_conf(base::StringToJson(std::getenv("INPUT_FILE_LIST")));
  auto file_list_json = path_conf.Get("data");
  if (file_list_json != nullptr && file_list_json->IsArray() && file_list_json->size() > 0) {
    for (int i = 0; i < file_list_json->size(); ++i) {
      std::string tmp_file;
      if (!file_list_json->GetString(i, &tmp_file)) {
        LOG(FATAL) << "file_list parse failed i " << i;
      }
      file_list.push_back(tmp_file);
      LOG(INFO) << "candidate file: " << tmp_file;
    }
  }
  kuiba::HdfsTextFile file("ad");
  for (const std::string& file_path : file_list) {
    CHECK(file.open(file_path, O_RDONLY)) << "open file fail: " << file_path;
    LOG(INFO) << "open hdfs file success: " << file_path;
    if (file.empty()) {
      LOG(INFO) << "file empty: " << file_path;
      continue;
    }
    while (true) {
      std::shared_ptr<std::string> line = std::make_shared<std::string>();
      if (!file.read_line(line.get()) || line->empty()) {
        LOG(INFO) << "read line done: " << file_path;
        break;
      }
      PushInput(line);
      ++total_count;
      LOG_EVERY_N(INFO, 10000) << "total_count: " << total_count;
    }
  }
  std::this_thread::sleep_for(std::chrono::seconds(200));
  is_over.store(true);
}

void SampleGeneratorMgr::ReadFromStdIn() {
  while (true) {
    std::shared_ptr<std::string> line = std::make_shared<std::string>();
    if (!std::getline(std::cin, *line)) {
      LOG(INFO) << "read line done";
      std::this_thread::sleep_for(std::chrono::seconds(200));
      is_over.store(true);
      break;
    }

    PushInput(line);
    ++total_count;
    LOG_EVERY_N(INFO, 10000) << "total_count: " << total_count;
  }
}

void SampleGeneratorMgr::PushInput(std::shared_ptr<std::string> line) {
  while (!is_over.load()) {
    if (input_queue.size_approx() < FLAGS_input_queue_max_size) {
      LOG_EVERY_N(INFO, 1000) << "input queue size=" << input_queue.size_approx();
      input_queue.put(line);
      break;
    } else {
      std::this_thread::sleep_for(std::chrono::microseconds(10));
    }
  }
}

void SampleGeneratorMgr::Stop() {
  bg_thd_->join();
  is_over.store(true);
  for (auto& generator: generators_) {
    generator->Stop();
  }
  LOG(INFO) << "[SampleGeneratorMgr] stop";
}

SampleGeneratorMgr::SampleGeneratorMgr(TopKRetrievalMgr* retr_mgr) {
  total_count = 0;
  is_over.store(false);
  for (int i = 0; i < FLAGS_num_generators; ++i) {
    generators_.emplace_back(new SampleGenerator(this, retr_mgr));
  }
  if (FLAGS_scr_env_file_list) {
    bg_thd_ = new std::thread(std::bind(&SampleGeneratorMgr::ReadFromHdfs, this));
  } else {
    bg_thd_ = new std::thread(std::bind(&SampleGeneratorMgr::ReadFromStdIn, this));
  }
  LOG(INFO) << "[SampleGeneratorMgr] init. worker_size=" << generators_.size();
}

SampleGeneratorMgr::~SampleGeneratorMgr() {
  for (auto& generator: generators_) {
    delete generator;
  }
  generators_.clear();
  delete bg_thd_;
}

bool SampleGeneratorMgr::GetInput(std::shared_ptr<std::string>& line) {
  return input_queue.get(line);
}
}  // namespace ad_algorithm
}  // namespace ks

