#include <atomic>
#include "base/common/base.h"
#include "base/common/gflags.h"
#include "base/time/timestamp.h"
#include "third_party/lz4/lib/lz4.h"
#include "absl/strings/numbers.h"
#include "teams/ad/picasso/sdk/picasso_client/picasso_client.h"
#include "teams/ad/picasso/sdk/picassobatch_client/picasso_client.h"
#include "teams/reco-arch/colossus/proto/long_term_service.pb.h"

DECLARE_bool(logtostderr);
DEFINE_int32(num_threads, 1, "");
DEFINE_bool(use_batch_api, true, "");
DEFINE_int32(batch_size, 100, "");

using ks::ad_picasso_batch::sdk::PicassoValuesResultOfKeys;

void GetUserHistoryEmb(ks::ad_picasso_batch::sdk::PicassoClient* picasso, int64 user_id,
                       std::atomic<uint64>* behav_cnt, std::atomic<uint64>* action_cnt,
                       std::atomic<uint64>* behav_success_cnt, std::atomic<uint64>* action_success_cnt,
                       std::atomic<uint64>* behav_time, std::atomic<uint64>* action_time,
                       std::atomic<uint64>* behav_parse_time, std::atomic<uint64>* action_parse_time) {
  std::string history_table_name("adSimLongInterest");
  std::string history_key(std::to_string(user_id));
  std::vector<std::string> history_value_v;

  auto ret = ks::ad_picasso_batch::sdk::STATUS_TIMEOUT;
  uint64 ts_start = base::GetTimestamp();
  ret = picasso->HGetAll(history_table_name, history_key, history_value_v);
  uint64 ts_end = base::GetTimestamp();
  *behav_cnt += 1;
  *behav_time += (ts_end - ts_start) / 1000;
  if (ret != ks::ad_picasso_batch::sdk::STATUS_OK) {
    return;
  }
  *behav_success_cnt += 1;
  // 历史行为序列处理
  // lz4解压缩
  ts_start = base::GetTimestamp();
  std::string lz4_dst;
  lz4_dst.resize(history_value_v[0].size() * 4);
  int decom_size = LZ4_decompress_safe(history_value_v[0].data(), &lz4_dst[0], history_value_v[0].size(), lz4_dst.size());
  if (decom_size < 0) {
    return;
  }
  lz4_dst.resize(decom_size);
  // 反序列化为UserTerm
  colossus::UserTerm user_term;
  if (!user_term.ParseFromString(lz4_dst)) {
    return;
  }
  ts_end = base::GetTimestamp();
  *behav_parse_time += (ts_end - ts_start) / 1000;
  if (user_term.items_size() == 0) {
    return;
  }
  // hetu embedding 读取
  const int64_t emb_size = 128;
  std::vector<std::vector<float>> hetu_emb(user_term.items_size(), std::vector<float>(emb_size, 0.0));
  std::vector<bool> hetu_emb_index(user_term.items_size(), false);
  std::vector<std::thread*> tasks;
  int task_num = user_term.items_size() > FLAGS_num_threads ? FLAGS_num_threads : user_term.items_size();
  int cnt_per_task = (user_term.items_size() + task_num - 1) / task_num;
  for (int t = 0; t < task_num; t++) {
    auto func = [t, cnt_per_task, picasso, &user_term, &hetu_emb, &hetu_emb_index,
                 action_cnt, action_time, action_success_cnt, action_parse_time] () {
      std::vector<std::string> pids;
      PicassoValuesResultOfKeys res;
      for (int j = t * cnt_per_task; j < (t + 1) * cnt_per_task && j < user_term.items_size(); j++) {
        if (FLAGS_use_batch_api) {
          if ((j + 1) % FLAGS_batch_size == 0 || j == (t + 1) * cnt_per_task - 1 || j == user_term.items_size() - 1) {
            res.clear();
            uint64 ts_start = base::GetTimestamp();
            auto ret = picasso->BatchHGetAll("adHetuEmbedding", pids, res);
            uint64 ts_end = base::GetTimestamp();
            *action_cnt += res.size();
            *action_time += (ts_end - ts_start) / 1000;
            for (int k = 0; k < res.size(); k++) {
              const auto& r = res[k];
              if (r.first != ks::ad_picasso_batch::sdk::STATUS_OK) {
                continue;
              }
              *action_success_cnt += 1;
              LOG_EVERY_N(INFO, 1000) << r.second[0];
              ts_start = base::GetTimestamp();
              std::vector<std::string> str_vec;
              base::SplitString(r.second[0], std::string(","), &str_vec);
              if (str_vec.size() != emb_size) {
                continue;
              }
              for (int i = 0; i < str_vec.size(); ++i) {
                float element = 0.0;
                bool valid_double = absl::SimpleAtof(str_vec[i], &element);
                hetu_emb[j-res.size()+1+k][i] = element;
              }
              hetu_emb_index[j-res.size()+1+k] = true;
              ts_end = base::GetTimestamp();
              *action_parse_time += (ts_end - ts_start) / 1000;
            }
            pids.clear();
          } else {
            pids.push_back(std::to_string(user_term.items(j).photo_id()));
          }
          continue;
        }
        std::string hetu_key(std::to_string(user_term.items(j).photo_id()));
        std::vector<std::string> hetu_value_v;
        auto ret = ks::ad_picasso_batch::sdk::STATUS_TIMEOUT;
        uint64 ts_start = base::GetTimestamp();
        ret = picasso->HGetAll("adHetuEmbedding", hetu_key, hetu_value_v);
        uint64 ts_end = base::GetTimestamp();
        *action_cnt += 1;
        *action_time += (ts_end - ts_start) / 1000;
        if (ret != ks::ad_picasso_batch::sdk::STATUS_OK) {
          continue;
        }
        *action_success_cnt += 1;
        LOG_EVERY_N(INFO, 1000) << hetu_value_v[0];
        ts_start = base::GetTimestamp();
        std::vector<std::string> str_vec;
        base::SplitString(hetu_value_v[0], std::string(","), &str_vec);
        if (str_vec.size() != emb_size) {
          continue;
        }
        for (int i = 0; i < str_vec.size(); ++i) {
          float element = 0.0;
          bool valid_double = absl::SimpleAtof(str_vec[i], &element);
          hetu_emb[j][i] = element;
        }
        hetu_emb_index[j] = true;
        ts_end = base::GetTimestamp();
        *action_parse_time += (ts_end - ts_start) / 1000;
      }
    };
    tasks.push_back(new std::thread(func));
  }
  for (std::thread* t : tasks) {
    t->join();
  }
}

int main(int argc, char *argv[]) {
  FLAGS_logtostderr = true;
  base::InitApp(&argc, &argv, "test picasso");
  ks::ad_picasso_batch::sdk::PicassoOption opt;
  opt.client_tag = "feature_extractor_client_tag";
  opt.caller_kess_service = "extract_user_reco_sim_sample_test";
  opt.target_kess_service = "grpc_adPicassoBatchOfflineGatewayService";
  ks::ad_picasso_batch::sdk::PicassoClient* picasso = ks::ad_picasso_batch::sdk::PicassoClient::GetInstance();
  if (!picasso->Init(opt)) {
    LOG(FATAL) << "init picasso failed. ";
  }
  std::atomic<uint64> behav_cnt(0);
  std::atomic<uint64> behav_success_cnt(0);
  std::atomic<uint64> behav_time(0);
  std::atomic<uint64> behav_parse_time(0);
  std::atomic<uint64> action_cnt(0);
  std::atomic<uint64> action_success_cnt(0);
  std::atomic<uint64> action_time(0);
  std::atomic<uint64> action_parse_time(0);
  while (true) {
    std::string line;
    if (!std::getline(std::cin, line)) {
      LOG(INFO) << "read line done";
      break;
    }
    base::TrimWhitespaces(&line);
    int64 user_id;
    if (!absl::SimpleAtoi(line, &user_id)) {
      continue;
    }
    GetUserHistoryEmb(picasso, user_id,
                      &behav_cnt, &action_cnt,
                      &behav_success_cnt, &action_success_cnt,
                      &behav_time, &action_time,
                      &behav_parse_time, &action_parse_time);
    LOG(INFO) << "user behavior list fetch count: " << behav_cnt;
    if (behav_cnt > 0) {
      LOG(INFO) << "user behavior list fetch success rate: " << 100.0 * behav_success_cnt / behav_cnt << "%";
      LOG(INFO) << "user behavior list fetch time: " << behav_time / behav_cnt << "ms";
    }
    if (behav_success_cnt > 0) {
      LOG(INFO) << "user behavior list parse time: " << behav_parse_time / behav_success_cnt << "ms";
    }
    LOG(INFO) << "hetu embedding fetch count: " << action_cnt;
    if (action_cnt > 0) {
      LOG(INFO) << "hetu embedding fetch success rate: " << 100.0 * action_success_cnt / action_cnt << "%";
      LOG(INFO) << "hetu embedding fetch time: " << action_time / action_cnt << "ms";
    }
    if (action_success_cnt > 0) {
      LOG(INFO) << "hetu embedding parse time: " << action_parse_time / action_success_cnt << "ms";
    }
    ::google::FlushLogFiles(::google::INFO);
  }
}
