_Pragma("once");

#include "teams/ad/ad_algorithm/colossus/worker.h"

#include <mutex>
#include <queue>
#include <vector>
#include "teams/ad/ad_nn/gpu_ps/common/stream/stream_mgr.h"
#include "teams/ad/ad_base/src/container/concurrent_ms_queue.h"
#include "teams/ad/ad_nn/gpu_ps/messenger/message_handle.h"

namespace ks {
namespace gpu_ps {

using SampleType = std::pair<std::vector<std::vector<uint64>>, std::vector<float>>;

class WorkerMgr {
public:
  WorkerMgr(size_t worker_num,
            const WorkerOption& worker_opt,
            const StreamOption& stream_opt);
  ~WorkerMgr();

  WorkerMgr(const WorkerMgr&) = delete;
  WorkerMgr &operator=(const WorkerMgr&) = delete;
  WorkerMgr &operator=(const WorkerMgr&) volatile = delete;
   
  Status Init();
  Status Start();
  Status Stop();

  bool PushFeedQueue(std::shared_ptr<SampleType> one_sample);
  std::shared_ptr<SampleType> PopFeedQueue();
  bool IsOver();

private:
  size_t worker_num_;

  WorkerOption worker_opt_;
  StreamOption stream_opt_;

  std::vector<Worker*> workers_;

  StreamMgr* stream_mgr_;

//ks::ad_base::AdConcurrentMSQueue<std::shared_ptr<SampleType> > label_dense_queue_;

  std::queue<std::shared_ptr<SampleType> > label_dense_queue_;
  mutable std::mutex mu_;
};

}  // namespace gpu_ps
}  // namespace ks
