#include "worker.h"

#include <zstd.h>
#include "base/encoding/base64.h"
#include "base/strings/string_split.h"
#include "base/file/file_util.h"
#include "teams/ad/ad_nn/gpu_ps/client/gpu_ps_client.h"
#include "teams/ad/ad_algorithm/colossus/worker_mgr.h"
#include "teams/ad/ad_nn/gpu_ps/proto/ad_gpu_hub.pb.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "teams/ad/ad_nn/gpu_ps/util/util.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_algorithm/feature/fast/ad_log_wrapper.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_feature.pb.h"
#include "teams/ad/ad_algorithm/log_preprocess/log_preprocess.h"

DECLARE_string(local_config_file);
DEFINE_string(tab, "", "");
DEFINE_string(item_type, "", "");
DEFINE_string(item_filter, "", "");
DEFINE_string(label_extractor, "", "");
DEFINE_string(ks_feature_config_path, "", "");
DEFINE_string(debug_info, "", "");
DEFINE_int32(batch_size, 1, "");

namespace ks {
namespace gpu_ps {

using SampleType = std::pair<std::vector<std::vector<uint64>>, std::vector<float>>;

Worker::Worker(Stream* stream, const WorkerOption& opt, WorkerMgr* mgr) :
  stream_(stream), opt_(opt), mgr_(mgr) {
  LOG(INFO) << "[Worker] created"
            << ". model_name=" << opt_.model_name
            << ". batch_size=" << opt_.batch_size
            << ". sparse_field_count=" << opt_.sparse_field_count.size()
            << ". dense_field_count=" << opt_.dense_field_count.size()
            << ". embedding_size=" << opt_.embedding_size
            << ". dense_total_size=" << opt_.dense_total_size
            << ". ps_hash=" << opt_.ps_hash
            << ". hash_type=" << opt_.hash_type;
  field_size_ = opt_.sparse_field_count;
  if (FLAGS_ks_feature_config_path.size() == 0) {
    LOG(INFO) << "empty feature file, return";
    return;
  }

  feature_file_info = std::move(std::make_shared<ks::ad_nn::FeatureFileInfo>());
  const base::FilePath file_path(FLAGS_ks_feature_config_path);
  if (!base::file_util::PathExists(file_path)) {
    LOG(ERROR) << "feature file: " << FLAGS_ks_feature_config_path << " not exists!!!";
  } else {
    ks::ad_nn::FeatureFileInfo::LoadFeatureFile(FLAGS_ks_feature_config_path, feature_file_info.get());
  }

  std::unique_ptr<ks::ad_nn::AdFieldFeature> tmp_ptr(new ks::ad_nn::AdFieldFeature({feature_file_info.get()}));
  feature_extractor = std::move(tmp_ptr);

  sparse_feature_type_sorted.push_back(ks::ad_nn::FeatureType::RECO_USER);
  sparse_feature_type_sorted.push_back(ks::ad_nn::FeatureType::USER);
  sparse_feature_type_sorted.push_back(ks::ad_nn::FeatureType::ITEM);
  sparse_feature_type_sorted.push_back(ks::ad_nn::FeatureType::RECO_ITEM);
  sparse_feature_type_sorted.push_back(ks::ad_nn::FeatureType::COMBINE);

  dense_feature_type_sorted.push_back(ks::ad_nn::FeatureType::DENSE_USER);
  dense_feature_type_sorted.push_back(ks::ad_nn::FeatureType::DENSE_ITEM);
  dense_feature_type_sorted.push_back(ks::ad_nn::FeatureType::DENSE_COMBINE);

  bool is_nohash = false;
  for (const auto& slot : feature_file_info->slots) {
    if (slot.slot_size == 0) {
      is_nohash = true;
    }
  }

  std::unique_ptr<ks::ad_nn::AdFeatureExtractorBundle> tmp_bundle_ptr =
    std::make_unique<ks::ad_nn::AdFeatureExtractorBundle>(feature_file_info,
                                               ks::ad_nn::ExtractType::ALL,
                                               ks::ad_nn::FeatureOrgLayout::BY_FIELD,
                                               is_nohash);
  if (is_nohash) {
    auto nohash_convert = [](ks::ad_nn::FeatureType type, uint64_t sign) {
      return sign;
    };
    tmp_bundle_ptr->SetSignConvert(nohash_convert);
  }
  ad_fea_ext_bundle_ptr = std::move(tmp_bundle_ptr);

  dense_total_size = 0;
  dense_field_offset.resize(feature_file_info->dense_slots_count);
  for (int i = 0; i < feature_file_info->dense_slots_count; ++i) {
    if (i == 0) {
      dense_field_offset[i] = 0;
    } else {
      dense_field_offset[i] = dense_field_offset[i-1] + feature_file_info->dense_slots_size[i];
    }
    dense_total_size += feature_file_info->dense_slots_size[i];
  }
}

Worker::~Worker() {
  delete bg_thd_;
}

Status Worker::Init() {
  DLOG(INFO) << "[Worker] init";
  return Status::OK();
}

Status Worker::Start() {
  stop_.store(false);
  over_.store(false);
  bg_thd_ = new std::thread(std::bind(&Worker::run, this));
  DLOG(INFO) << "[Worker] start";
  return Status::OK();
}

Status Worker::Stop() {
  stop_.store(true);
  bg_thd_->join();
  LOG(INFO) << "[Worker] stop";
  return Status::OK();
}

void Worker::run() {
  std::shared_ptr<std::string> record = nullptr;
  std::this_thread::sleep_for(std::chrono::microseconds(7000000));
  while (!stop_.load()) {
    auto st = consume(record);
    if (!st.ok()) {
      if (st.code() == Status::kEof) {
        LOG(INFO) << "[Worker] finish";
        break;
      } else {
        LOG(ERROR) << "[Worker] consume fail, worker will stop";
        break;
      }
    }

    PerfUtil::CountLogStash(1, KlearnConst::KLEARN_HUB, opt_.model_name, KlearnConst::COUNT_MESSAGE);

    if (opt_.sample_rate < 1.0) {
      if (ks::ad_base::AdRandom::GetDouble() > opt_.sample_rate) {
        LOG_EVERY_N(INFO, 10000) << "throw away by sample_rate: " << opt_.sample_rate;
        release(record);
        continue;
      }
    }
    
    base::TrimWhitespaces(record.get());
    int64_t batch_id = 0;
    std::map<int32_t, int32_t> ps_pending_batch;
    int32_t worker_pending_batch;
    bool ret = false;
    bool batched = false;
    std::ostringstream oss;
    ret = process_batched(*record, &batch_id, &ps_pending_batch, &worker_pending_batch);
    release(record);
  }
  over_.store(true);
}

Status Worker::consume(std::shared_ptr<std::string>& record) {
  return stream_->Next(record);
}

void Worker::release(std::shared_ptr<std::string>& record) {
  return stream_->Release(record);
}

bool Worker::process_batched(const std::string& sample_str,
                             int64_t* batch_id,
                             std::map<int32_t, int32_t>* ps_pending_batch,
                             int32_t* worker_pending_batch) {
  auto start = std::chrono::high_resolution_clock::now();

  static thread_local std::string parsed_sample_str;
  if (!base::Base64Decode(sample_str, &parsed_sample_str)) {
    LOG(ERROR) << "failed to decode base64 string, str len";
    return false;
  }
  ks::ad_algorithm::AdLogWrapper adlog_wrapper;
  if (!adlog_wrapper.parse_from(parsed_sample_str)) {
    LOG(ERROR) << "parser adlog failed";
    return false;
  }
  auto reco_user_info = adlog_wrapper.mutable_reco_user_info();
  if (adlog_wrapper.Get().serialized_reco_user_info().empty() ||
      !reco_user_info->ParseFromString(adlog_wrapper.Get().serialized_reco_user_info())) {
    LOG_EVERY_N(WARNING, 10000) << "reco_user_info empty or parse reco_user_info error";
  }
  thread_local ks::ad_algorithm::LogPreprocess processor(
          FLAGS_tab, FLAGS_item_type, FLAGS_item_filter, FLAGS_label_extractor, FLAGS_debug_info);
  tensorflow::Tensor t;
  std::vector<std::string> extra_fields;
  if (!processor.preprocess(&adlog_wrapper, &t, &extra_fields)) {
    return false;
  }
  adlog_wrapper.move_items();

  ks::ad_nn::Type2ExtractResultsPtr sparse_results_bundle;
  ks::ad_nn::Type2ExtractResultsPtr dense_results_bundle;

  const ks::ad_nn::Type2RetIndices& sparse_indices = ad_fea_ext_bundle_ptr->sparse_ret_indices();
  const ks::ad_nn::Type2RetIndices& dense_indices = ad_fea_ext_bundle_ptr->dense_ret_indices();
  ks::ad_nn::CacheInfo cache_info;

  ad_fea_ext_bundle_ptr->ExtractAllSparseFeatures(adlog_wrapper,
                                                  cache_info,
                                                  &sparse_results_bundle,
                                                  ks::ad_nn::FeatureOrgLayout::BY_FIELD);
  ad_fea_ext_bundle_ptr->ExtractAllDenseFeatures(adlog_wrapper,
                                                  cache_info,
                                                  &dense_results_bundle,
                                                  ks::ad_nn::FeatureOrgLayout::BY_FIELD);

  for (size_t i = 0; i < adlog_wrapper.item_size(); ++i) {
    auto one_sample = std::make_shared<SampleType>();
    std::vector<std::vector<uint64>>& sparse_values = one_sample->first;
    sparse_values.resize(feature_file_info->slots_count);
    for (int j = 0; j < feature_file_info->slots_count; j++) {
      sparse_values[j].clear();
    }
    for (auto& sparse_feature_type : sparse_feature_type_sorted) {
      if (sparse_results_bundle.find(sparse_feature_type) == sparse_results_bundle.end()) {
        continue;
      }
      auto& sparse_vec = sparse_results_bundle.find(sparse_feature_type)->second;
      auto& sparse_vec_index = sparse_indices.find(sparse_feature_type)->second;
      if (IsUserType(sparse_feature_type)) {
        for (size_t j = 0; j < sparse_vec->size(); j++) {
          if (sparse_vec->at(j).size() > 0) {
            for (auto& ret : sparse_vec->at(j)) {
              int feature_index = sparse_vec_index[j];
              sparse_values[feature_index].push_back(ret.sign);
            }
          }
        }
      } else {
        for (size_t j = 0; j < sparse_vec->size(); j++) {
          if (j % adlog_wrapper.item_size() == i) {
            if (sparse_vec->at(j).size() > 0) {
              for (auto& ret : sparse_vec->at(j)) {
                int feature_index = sparse_vec_index[j / adlog_wrapper.item_size()];
                sparse_values[feature_index].push_back(ret.sign);
              }
            }
          }
        }
      }
    }

    std::vector<float>& dense_values = one_sample->second;
    dense_values.resize(dense_total_size);
    std::fill(dense_values.begin(), dense_values.end(), 0.0f);
    int offset = 0;
    for (auto& dense_feature_type : dense_feature_type_sorted) {
      if (dense_results_bundle.find(dense_feature_type) == dense_results_bundle.end()) {
        continue;
      }
      auto& dense_vec = dense_results_bundle.find(dense_feature_type)->second;
      auto& dense_vec_index = dense_indices.find(dense_feature_type)->second;
      if (IsUserType(dense_feature_type)) {
        for (size_t j = 0; j < dense_vec->size(); j++) {
          if (dense_vec->at(j).size() > 0) {
            for (auto& ret : dense_vec->at(j)) {
              int feature_index = dense_vec_index[j];
              if (feature_index < feature_file_info->dense_slots_count &&
                  ret.dense_sign < feature_file_info->dense_slots_size[feature_index]) {
                dense_values[dense_field_offset[feature_index] + ret.dense_sign] = ret.value;
              }
            }
          }
        }
      } else {
        for (size_t j = 0; j < dense_vec->size(); j++) {
          if (j % adlog_wrapper.item_size() == i) {
            if (dense_vec->at(j).size() > 0) {
              for (auto& ret : dense_vec->at(j)) {
                int feature_index = dense_vec_index[j / adlog_wrapper.item_size()];
                if (feature_index < feature_file_info->dense_slots_count &&
                    ret.dense_sign < feature_file_info->dense_slots_size[feature_index]) {
                  dense_values[dense_field_offset[feature_index] + ret.dense_sign] = ret.value;
                }
              }
            }
          }
        }
      }
    }
    while (!stop_.load()) {
      auto ret = mgr_->PushFeedQueue(one_sample);
      if (ret) {
        break;
      } else {
        std::this_thread::sleep_for(std::chrono::microseconds(10));
      }
    }
  }
  auto end = std::chrono::high_resolution_clock::now();
  falcon::Stat("gpu_hub.process_cost",
               DurationByMicrosecond(start, end));

  return true;
}

}  // namespace gpu_ps
}  // namespace ks
