using("ExtractUserRealtimeActionLiveMergeAdLivePosidSimJumpClickPhotoId",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_JUMP_CLICK_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_JUMP_CLICK_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_JUMP_CLICK_EVENT_SERVER_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_JUMP_CLICK_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_JUMP_CLICK_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_JUMP_CLICK_EVENT_SERVER_TIMESTAMP_LIST:int64_list",
      "adlog.context.pos_id:int64",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_sim_action_live_merge);

using("ExtractUserRealtimeActionLiveMergeAdLivePosidSimPlayedMeanPhotoId",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_MEAN_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_PLAYED_MEAN_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_MEAN_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_PLAYED_MEAN_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_PLAYED_MEAN_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_PLAYED_MEAN_EVENT_SERVER_TIMESTAMP_LIST:int64_list",
      "adlog.context.pos_id:int64",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_sim_action_live_merge);

using("ExtractUserRealtimeActionLiveMergeAdLivePosidSimToProfilePhotoId",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_TO_PROFILE_PHOTO_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_TO_PROFILE_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_TO_PROFILE_EVENT_SERVER_TIMESTAMP_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_TO_PROFILE_PHOTO_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_TO_PROFILE_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_TO_PROFILE_EVENT_SERVER_TIMESTAMP_LIST:int64_list",  
      "adlog.context.pos_id:int64",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_sim_action_live_merge);

using("ExtractURealTimeMerchantPosidSimOrderPaiedAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_AUTHOR_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_GET_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_TIMESTAMP_LIST:int64_list",
      "adlog.context.pos_id:int64",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_sim_action_live_solo);

using("ExtractURealTimeMerchantPosidSimOrderPaiedItemPriceList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_ITEM_PACKAGE_0_ITEM_PRICE_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_GET_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_TIMESTAMP_LIST:int64_list", 
      "adlog.context.pos_id:int64",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_sim_action_live_solo);

using("ExtractURealTimeMerchantPosidSimGoodsViewAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_TIMESTAMP_LIST:int64_list",  
      "adlog.context.pos_id:int64",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_sim_action_live_solo);

using("ExtractURealTimeMerchantPosidSimGoodsViewItemPriceList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_PACKAGE_0_ITEM_PRICE_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_TIMESTAMP_LIST:int64_list",
      "adlog.context.pos_id:int64",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_sim_action_live_solo);

using("ExtractURealTimeMerchantPosidSimGoodsViewItemBrandList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_PACKAGE_0_CLICK_ITEM_ID_302_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_TIMESTAMP_LIST:int64_list",
      "adlog.context.pos_id:int64",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_sim_action_live_solo);

using("ExtractURealTimeMerchantPosidSimGoodsViewItemX7Category3List",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_X7_CATEGORY3_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_TIMESTAMP_LIST:int64_list",
      "adlog.context.pos_id:int64",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_sim_action_live_solo);