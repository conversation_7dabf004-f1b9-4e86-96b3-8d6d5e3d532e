import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_tmpl.dark"
using("ExtractUserSparseMmuMstaImpMediaInfoPicTextPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_MEDIA_INFO_PIC_TEXT:int64_list",
      FeaturePrefix::IMPRESSION_MEDIA_INFO_PIC_TEXT
      );
using("ExtractUserSparseMmuMstaImpMediaInfoDramaPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_MEDIA_INFO_DRAMA:int64_list",
      FeaturePrefix::IMPRESSION_MEDIA_INFO_DRAMA
      );
using("ExtractUserSparseMmuMstaImpMediaInfoAppRecPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_MEDIA_INFO_APP_RECORD:int64_list",
      FeaturePrefix::IMPRESSION_MEDIA_INFO_APP_RECORD
      );
using("ExtractUserSparseMmuMstaImpMediaInfoTextOnPicPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_MEDIA_INFO_TEXT_ON_PIC:int64_list",
      FeaturePrefix::IMPRESSION_MEDIA_INFO_TEXT_ON_PIC
      );
using("ExtractUserSparseMmuMstaImpMediaInfoGameRecordPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_GAME_GAME_RECORD:int64_list",
      FeaturePrefix::IMPRESSION_GAME_GAME_RECORD
      );
using("ExtractUserSparseMmuMstaImpMediaInfoDramaRelaxPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_MEDIA_INFO_DRAMA_RELAX_TALK:int64_list",
      FeaturePrefix::IMPRESSION_MEDIA_INFO_DRAMA_RELAX_TALK
      );
using("ExtractUserSparseMmuMstaImpMediaInfoPersonOralPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_MEDIA_INFO_PERSONAL_ORAL:int64_list",
      FeaturePrefix::IMPRESSION_MEDIA_INFO_PERSONAL_ORAL
      );
using("ExtractUserSparseMmuMstaImpMediaInfoCutVideoPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_MEDIA_INFO_CUTTED_VIDEO:int64_list",
      FeaturePrefix::IMPRESSION_MEDIA_INFO_CUTTED_VIDEO
      );
using("ExtractUserSparseMmuMstaImpMediaInfoPptPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_MEDIA_INFO_SNAP_PPT:int64_list",
      FeaturePrefix::IMPRESSION_MEDIA_INFO_SNAP_PPT
      );
using("ExtractUserSparseMmuMstaImpEcomItemServePidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_E_COMMERCE_PLATFORM_ITEM_SERVICE:int64_list",
      FeaturePrefix::IMPRESSION_E_COMMERCE_PLATFORM_ITEM_SERVICE
      );
using("ExtractUserSparseMmuMstaImpGameDramaPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_GAME_DRAMA:int64_list",
      FeaturePrefix::IMPRESSION_GAME_DRAMA
      );
using("ExtractUserSparseMmuMstaImpGamePersonOralPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_GAME_PERSONAL_ORAL:int64_list",
      FeaturePrefix::IMPRESSION_GAME_PERSONAL_ORAL
      );
using("ExtractUserSparseMmuMstaClkMediaInfoPicTextPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_MEDIA_INFO_PIC_TEXT:int64_list",
      FeaturePrefix::CLICK_MEDIA_INFO_PIC_TEXT
      );
using("ExtractUserSparseMmuMstaImpGameDramaRelaxPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_GAME_DRAMA_RELAX_TALK:int64_list",
      FeaturePrefix::IMPRESSION_GAME_DRAMA_RELAX_TALK
      );
using("ExtractUserSparseMmuMstaImpGameCgVideoPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_GAME_CG_VIDEO:int64_list",
      FeaturePrefix::IMPRESSION_GAME_CG_VIDEO
      );
using("ExtractUserSparseMmuMstaImpMediaInfoRelaxVideoPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_MEDIA_INFO_RELAX_VIDEO:int64_list",
      FeaturePrefix::IMPRESSION_MEDIA_INFO_RELAX_VIDEO
      );
using("ExtractUserSparseMmuMstaImpEcomPicTextPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_E_COMMERCE_PLATFORM_PIC_TEXT:int64_list",
      FeaturePrefix::IMPRESSION_E_COMMERCE_PLATFORM_PIC_TEXT
      );
using("ExtractUserSparseMmuMstaImpMediaInfoCgVideoPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_MEDIA_INFO_CG_VIDEO:int64_list",
      FeaturePrefix::IMPRESSION_MEDIA_INFO_CG_VIDEO
      );
using("ExtractUserSparseMmuMstaClkMediaInfoDramaPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_MEDIA_INFO_DRAMA:int64_list",
      FeaturePrefix::CLICK_MEDIA_INFO_DRAMA
      );
using("ExtractUserSparseMmuMstaImpMediaInfoDramaConflictPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_MEDIA_INFO_DRAMA_CONFLICT:int64_list",
      FeaturePrefix::IMPRESSION_MEDIA_INFO_DRAMA_CONFLICT
      );
using("ExtractUserSparseMmuMstaClkGameRecPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_GAME_GAME_RECORD:int64_list",
      FeaturePrefix::CLICK_GAME_GAME_RECORD
      );
using("ExtractUserSparseMmuMstaClkMediaInfoAppRecPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_MEDIA_INFO_APP_RECORD:int64_list",
      FeaturePrefix::CLICK_MEDIA_INFO_APP_RECORD
      );
using("ExtractUserSparseMmuMstaClkMediaInfoTextOnPicPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_MEDIA_INFO_TEXT_ON_PIC:int64_list",
      FeaturePrefix::CLICK_MEDIA_INFO_TEXT_ON_PIC
      );
using("ExtractUserSparseMmuMstaClkMediaInfoDramaRelaxPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_MEDIA_INFO_DRAMA_RELAX_TALK:int64_list",
      FeaturePrefix::CLICK_MEDIA_INFO_DRAMA_RELAX_TALK
      );
using("ExtractUserSparseMmuMstaClkGameDramaPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_GAME_DRAMA:int64_list",
      FeaturePrefix::CLICK_GAME_DRAMA
      );
using("ExtractUserSparseMmuMstaClkEcomItemServePidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_E_COMMERCE_PLATFORM_ITEM_SERVICE:int64_list",
      FeaturePrefix::CLICK_E_COMMERCE_PLATFORM_ITEM_SERVICE
      );
using("ExtractUserSparseMmuMstaClkMediaInfoPptPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_MEDIA_INFO_SNAP_PPT:int64_list",
      FeaturePrefix::CLICK_MEDIA_INFO_SNAP_PPT
      );
using("ExtractUserSparseMmuMstaClkMediaInfoPersonOralPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_MEDIA_INFO_PERSONAL_ORAL:int64_list",
      FeaturePrefix::CLICK_MEDIA_INFO_PERSONAL_ORAL
      );
using("ExtractUserSparseMmuMstaClkMediaInfoCutVideoPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_MEDIA_INFO_CUTTED_VIDEO:int64_list",
      FeaturePrefix::CLICK_MEDIA_INFO_CUTTED_VIDEO
      );
using("ExtractUserSparseMmuMstaClkGamePersonOralPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_GAME_PERSONAL_ORAL:int64_list",
      FeaturePrefix::CLICK_GAME_PERSONAL_ORAL
      );
using("ExtractUserSparseMmuMstaImpGameRelaxVideoPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.IMPRESSION_GAME_RELAX_VIDEO:int64_list",
      FeaturePrefix::IMPRESSION_GAME_RELAX_VIDEO
      );
using("ExtractUserSparseMmuMstaClkGameDramaRelaxPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_GAME_DRAMA_RELAX_TALK:int64_list",
      FeaturePrefix::CLICK_GAME_DRAMA_RELAX_TALK
      );
using("ExtractUserSparseMmuMstaClkGameCgVideoPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_GAME_CG_VIDEO:int64_list",
      FeaturePrefix::CLICK_GAME_CG_VIDEO
      );
using("ExtractUserSparseMmuMstaClkEcomPicTextPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_E_COMMERCE_PLATFORM_PIC_TEXT:int64_list",
      FeaturePrefix::CLICK_E_COMMERCE_PLATFORM_PIC_TEXT
      );
using("ExtractUserSparseMmuMstaClkMediaInfoDramaConflictPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_MEDIA_INFO_DRAMA_CONFLICT:int64_list",
      FeaturePrefix::CLICK_MEDIA_INFO_DRAMA_CONFLICT
      );
using("ExtractUserSparseMmuMstaClkMediaInfoRelaxVideoPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_MEDIA_INFO_RELAX_VIDEO:int64_list",
      FeaturePrefix::CLICK_MEDIA_INFO_RELAX_VIDEO
      );
using("ExtractUserSparseMmuMstaClkMediaInfoCgVideoPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_MEDIA_INFO_CG_VIDEO:int64_list",
      FeaturePrefix::CLICK_MEDIA_INFO_CG_VIDEO
      );
using("ExtractUserSparseMmuMstaClkGameRelaxVideoPidList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_GAME_RELAX_VIDEO:int64_list",
      FeaturePrefix::CLICK_GAME_RELAX_VIDEO
      );