using("ExtractUserSparseRealtimeAdxPhotoList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_REAL_TIME_ADX_PHOTO_ID:int64_list",
       "adlog.user_info.common_info_attr.USER_REAL_TIME_ADX_TIMESTAMP:int64_list",
       "adlog.time",
       filter_action_by_time_diff);

using("ExtractUserSparseRealtimeAdxSpuList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_REAL_TIME_ADX_SPU_ID:int64_list",
      "adlog.user_info.common_info_attr.USER_REAL_TIME_ADX_TIMESTAMP:int64_list",
      "adlog.time",
       filter_action_by_time_diff);