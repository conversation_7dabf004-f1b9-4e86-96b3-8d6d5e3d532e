using("ExtractUserSparseHottestIntentionList",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.USER_SEARCH_INTENTION_TOTTEST_SEQ:int64_list",
  3,
  seq_list_completion_sparse_string
);
using("ExtractUserDenseHottestIntentionListCnt",
  FeatureType::DENSE_USER,
  "adlog.user_info.common_info_attr.USER_SEARCH_INTENTION_TOTTEST_SEQ_CNT:int64_list",
  3,
  0,
  seq_list_completion_dense,
  3
);

using("ExtractUserSparseNewestIntentionList",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.USER_SEARCH_INTENTION_NEWEST_SEQ:int64_list",
  3,
  seq_list_completion_sparse_string
);
using("ExtractUserDenseNewestIntentionListCnt",
  FeatureType::DENSE_USER,
  "adlog.user_info.common_info_attr.USER_SEARCH_INTENTION_NEWEST_SEQ_CNT:int64_list",
  3,
  10000,
  seq_list_completion_dense,
  3
);
