import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_sparse_realtime_match_detail_no_prefix_tmpl.dark"  

using("ExtractURealTimeAdPhotoClickPhotoIdMatchDetail",
      "ExtractUserSparseRealtimePhotoMatchDetailNoPrefixTmpl",
      {"adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_CLICK_PHOTO_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_CLICK_TIMESTAMP_LIST:int64_list",
       50});

using("ExtractURealTimeAdPhotoClickAuthorIdMatchDetail",
      "ExtractUserSparseRealtimeAuthorMatchDetailNoPrefixTmpl",
      {"adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_CLICK_AUTHOR_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_CLICK_TIMESTAMP_LIST:int64_list",
       50});
