using("ExtractUserLoseCpmStyle13Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100065:float",
    0,
    value_or,
    multiply10_cast2int
    );
using("ExtractUserLoseCpmStyle2Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100066:float",
    0,
    value_or,
    multiply10_cast2int
    );
using("ExtractUserLoseCpmStyle1Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100067:float",
    0,
    value_or,
    multiply10_cast2int);
using("ExtractUserLoseCpmStyle4Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100068:float",
    0,
    value_or,
    multiply10_cast2int);

using("ExtractUserLoseCpmStyle13Day15",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100077:float",
    0,
    value_or,
    multiply10_cast2int);
using("ExtractUserLoseCpmStyle4Day15",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100078:float",
    0,
    value_or,
    multiply10_cast2int);
using("ExtractUserLoseCpmStyle2Day15",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100079:float",
    0,
    value_or,
    multiply10_cast2int);
using("ExtractUserLoseCpmStyle1Day15",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100080:float",
    0,
    value_or,
    multiply10_cast2int);


using("ExtractUserLoseCpmStyle2Day30",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100073:float",
    0,
    value_or,
    multiply10_cast2int);
using("ExtractUserLoseCpmStyle1Day30",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100074:float",
    0,
    value_or,
    multiply10_cast2int);
using("ExtractUserLoseCpmStyle13Day30",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100075:float",
    0,
    value_or,
    multiply10_cast2int);
using("ExtractUserLoseCpmStyle4Day30",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100076:float",
    0,
    value_or,
    multiply10_cast2int);


using("ExtractUserLoseCpmStyle13Day90",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100069:float",
    0,
    value_or,
    multiply10_cast2int);
using("ExtractUserLoseCpmStyle1Day90",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100070:float",
    0,
    value_or,
    multiply10_cast2int);
using("ExtractUserLoseCpmStyle4Day90",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100071:float",
    0,
    value_or,
    multiply10_cast2int);
using("ExtractUserLoseCpmStyle2Day90",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100072:float",
    0,
    value_or,
    multiply10_cast2int);


using("ExtractUserLoseCpmStyle13Day7List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100065:float",
    0,
    value_or,
    multiply10_cast2binary
    );
using("ExtractUserLoseCpmStyle2Day7List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100066:float",
    0,
    value_or,
    multiply10_cast2binary);
using("ExtractUserLoseCpmStyle1Day7List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100067:float",
    0,
    value_or,
    multiply10_cast2binary);
using("ExtractUserLoseCpmStyle4Day7List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100068:float",
    0,
    value_or,
    multiply10_cast2binary);

using("ExtractUserLoseCpmStyle13Day15List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100077:float",
    0,
    value_or,
    multiply10_cast2binary);
using("ExtractUserLoseCpmStyle4Day15List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100078:float",
        0,
    value_or,
    multiply10_cast2binary);
using("ExtractUserLoseCpmStyle2Day15List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100079:float",
        0,
    value_or,
    multiply10_cast2binary);
using("ExtractUserLoseCpmStyle1Day15List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100080:float",
        0,
    value_or,
    multiply10_cast2binary);


using("ExtractUserLoseCpmStyle2Day30List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100073:float",
        0,
    value_or,
    multiply10_cast2binary);
using("ExtractUserLoseCpmStyle1Day30List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100074:float",
        0,
    value_or,
    multiply10_cast2binary);
using("ExtractUserLoseCpmStyle13Day30List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100075:float",
        0,
    value_or,
    multiply10_cast2binary);
using("ExtractUserLoseCpmStyle4Day30List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100076:float",
        0,
    value_or,
    multiply10_cast2binary);


using("ExtractUserLoseCpmStyle13Day90List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100069:float",
        0,
    value_or,
    multiply10_cast2binary);
using("ExtractUserLoseCpmStyle1Day90List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100070:float",
        0,
    value_or,
    multiply10_cast2binary);
using("ExtractUserLoseCpmStyle4Day90List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100071:float",
    0,
    value_or,
    multiply10_cast2binary);
using("ExtractUserLoseCpmStyle2Day90List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100072:float",
    0,
    value_or,
    multiply10_cast2binary);
