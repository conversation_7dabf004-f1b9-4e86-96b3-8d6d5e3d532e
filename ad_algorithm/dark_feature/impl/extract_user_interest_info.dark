using_seq("ExtractUserPhotoLevel2ScoreInfoV2",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PHOTO_LEVEL_2_SCORE_INFO_V2:int64_list",
       100);
using_seq("ExtractUserClassSecondScoreInfo",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_CLASS_SECOND_SCORE_INFO:int64_list",
       100);
using_seq("ExtractUserLivepayKewordLevel2ScoreInfo",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_LIVEPAY_KEWORD_LEVEL_2_SCORE_INFO:int64_list",
       100);
using_seq("ExtractUserTopInterestItemCateId1List",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.CATE_ID1_LIST:int64_list",
       100);
using_seq("ExtractUserTopInterestItemCateId2List",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.CATE_ID2_LIST:int64_list",
       100);
using_seq("ExtractUserTopInterestItemCateId3List",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.CATE_ID3_LIST:int64_list",
       100);
using_seq("ExtractUserTopInterestItemCateId4List",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.CATE_ID4_LIST:int64_list",
       100);