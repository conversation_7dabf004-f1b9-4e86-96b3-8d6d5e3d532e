using("ExtractUserSparseEduSearchCate4List7D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.key:5100092:int64_list",
       100,
       take);
using("ExtractUserSparseEduSearchCate3List7D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.key:5100091:int64_list",
       100,
       take);
using("ExtractUserSparseEduSearchOriCate2List30D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.key:5100090:int64_list",
       100,
       take);
using("ExtractUserSparseEduSearchCate4List30D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.key:5100089:int64_list",
       100,
       take);
using("ExtractUserSparseEduSearchCate2List30D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.key:5100088:int64_list",
       100,
       take);
using("ExtractUserSparseEduSearchOriCate2List7D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.key:5100087:int64_list",
       100,
       take);
using("ExtractUserSparseEduSearchCate3List30D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.key:5100085:int64_list",
       100,
       take);
using("ExtractUserSparseEduSearchNum30D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.key:5100086:int64");