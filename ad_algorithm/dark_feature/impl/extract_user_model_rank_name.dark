using("ExtractUserShortVideoAndLiveOrderPaidModelRankName",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.INNER_RANK_ITEM_MODEL_NAME:map_int64_string",
      "adlog.item.id:int64",
      get_value_from_map,
      "adlog.item.ad_dsp_info.campaign.base.type",
      "adlog.is_train",
      extract_user_model_rank_name
  );
using("ExtractUserShortVideoAndLiveOrderPaidModelRankType",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.INNER_RANK_ITEM_MODEL_NAME:map_int64_string",
      "adlog.item.id:int64",
      get_value_from_map,
      "adlog.item.ad_dsp_info.campaign.base.type",
      "adlog.is_train",
      extract_user_model_rank_type
  ); 
using("ExtractUserShortVideoAndLiveOrderPaidModelRankNameV2",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.INNER_RANK_ITEM_MODEL_NAME:map_int64_string",
      "adlog.item.id:int64",
      get_value_from_map,
      "adlog.item.ad_dsp_info.campaign.base.type",
      "adlog.is_train",
      extract_user_model_rank_name_v2
  ); 