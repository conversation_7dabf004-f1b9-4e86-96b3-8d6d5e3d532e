using("ExtractUserSparseAllRankCandidatesPhotoIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      -1,
      -1,
      1,
      600,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseAllRankCandidatesAuthorIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      -1,
      -1,
      1,
      600,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseAllRankCandidatesIndustryIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      -1,
      -1,
      1,
      600,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseAllRankCandidatesAccountIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      -1,
      -1,
      1,
      600,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseAllRankCandidatesIndustryParentIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      -1,
      -1,
      1,
      600,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseAllRankCandidatesProductIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      -1,
      -1,
      1,
      600,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseAllRankCandidatesOcpxActionTypeSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      -1,
      -1,
      1,
      600,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseAllRankCandidatesPackageNameSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      -1,
      -1,
      1,
      600,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseAllRankCandidatesAdListInfoSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      61,
      3, 
      -1,
      -1,
      1,
      600,
      generate_rank_candidates_list_feature
  );