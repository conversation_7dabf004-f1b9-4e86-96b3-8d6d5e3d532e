using("ExtractUserKeywordIdListKeywordLikeList1D",
    "ExtractUserSparseTmpl",
    "adlog.user_info.common_info_attr.USER_LIKE_PHOTO_KEYWORDS_1D:int64_list",
    FeaturePrefix::KEYWORD_LIKE_LIST_1D);

using("ExtractUserKeywordIdListKeywordLikeList7D",
    "ExtractUserSparseTmpl",
    "adlog.user_info.common_info_attr.USER_LIKE_PHOTO_KEYWORDS_7D:int64_list",
    FeaturePrefix::KEYWORD_LIKE_LIST_7D);

using("ExtractUserKeywordIdListKeywordLikeList30D",
    "ExtractUserSparseTmpl",
    "adlog.user_info.common_info_attr.USER_LIKE_PHOTO_KEYWORDS_30D:int64_list",
    FeaturePrefix::KEYWORD_LIKE_LIST_30D);

using("ExtractUserKeywordIdListKeywordCommentList1D",
    "ExtractUserSparseTmpl",
    "adlog.user_info.common_info_attr.USER_COMMENT_PHOTO_KEYWORDS_1D:int64_list",
    FeaturePrefix::KEYWORD_COMMENT_LIST_1D);

using("ExtractUserKeywordIdListKeywordCommentList7D",
    "ExtractUserSparseTmpl",
    "adlog.user_info.common_info_attr.USER_COMMENT_PHOTO_KEYWORDS_7D:int64_list",
    FeaturePrefix::KEYWORD_COMMENT_LIST_7D);

using("ExtractUserKeywordIdListKeywordCommentList30D",
    "ExtractUserSparseTmpl",
    "adlog.user_info.common_info_attr.USER_COMMENT_PHOTO_KEYWORDS_30D:int64_list",
    FeaturePrefix::KEYWORD_COMMENT_LIST_30D);

using("ExtractUserKeywordIdListKeywordCollectList1D",
    "ExtractUserSparseTmpl",
    "adlog.user_info.common_info_attr.USER_COLLECT_PHOTO_KEYWORDS_1D:int64_list",
    FeaturePrefix::KEYWORD_COLLECT_LIST_1D);

using("ExtractUserKeywordIdListKeywordCollectList7D",
    "ExtractUserSparseTmpl",
    "adlog.user_info.common_info_attr.USER_COLLECT_PHOTO_KEYWORDS_7D:int64_list",
    FeaturePrefix::KEYWORD_COLLECT_LIST_7D);

using("ExtractUserKeywordIdListKeywordCollectList30D",
    "ExtractUserSparseTmpl",
    "adlog.user_info.common_info_attr.USER_COLLECT_PHOTO_KEYWORDS_30D:int64_list",
    FeaturePrefix::KEYWORD_COLLECT_LIST_30D);

using("ExtractUserKeywordIdListKeywordShareList1D",
    "ExtractUserSparseTmpl",
    "adlog.user_info.common_info_attr.USER_SHARE_PHOTO_KEYWORDS_1D:int64_list",
    FeaturePrefix::KEYWORD_SHARE_LIST_1D);

using("ExtractUserKeywordIdListKeywordShareList7D",
    "ExtractUserSparseTmpl",
    "adlog.user_info.common_info_attr.USER_SHARE_PHOTO_KEYWORDS_7D:int64_list",
    FeaturePrefix::KEYWORD_SHARE_LIST_7D);

using("ExtractUserKeywordIdListKeywordShareList30D",
    "ExtractUserSparseTmpl",
    "adlog.user_info.common_info_attr.USER_SHARE_PHOTO_KEYWORDS_30D:int64_list",
    FeaturePrefix::KEYWORD_SHARE_LIST_30D);