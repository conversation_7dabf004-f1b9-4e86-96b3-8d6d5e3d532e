using("ExtractUserSparseNativeHardRankCandidatesPhotoIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesAuthorIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesIndustryIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesAccountIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesIndustryParentIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesProductIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesOcpxActionTypeSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      0,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesPackageNameSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesPhotoIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesAuthorIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesIndustryIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesAccountIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesIndustryParentIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesProductIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesOcpxActionTypeSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      0,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesPackageNameSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesPhotoIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesAuthorIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesIndustryIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesAccountIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesIndustryParentIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesProductIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesOcpxActionTypeSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      0,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeHardRankCandidatesPackageNameSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesPhotoIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesAuthorIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesIndustryIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesAccountIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesIndustryParentIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesProductIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesOcpxActionTypeSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      0,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesPackageNameSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesPhotoIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesAuthorIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesIndustryIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesAccountIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesIndustryParentIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesProductIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesOcpxActionTypeSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      0,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesPackageNameSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesPhotoIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesAuthorIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesIndustryIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesAccountIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesIndustryParentIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesProductIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesOcpxActionTypeSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      0,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftRankCandidatesPackageNameSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesPhotoIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      2,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesAuthorIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      2,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesIndustryIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      2,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesAccountIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      2,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesIndustryParentIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      2,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesProductIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      2,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesOcpxActionTypeSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      0,
      2,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesPackageNameSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      2,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesPhotoIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      2,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesAuthorIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      2,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesIndustryIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      2,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesAccountIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      2,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesIndustryParentIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      2,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesProductIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      2,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesOcpxActionTypeSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      0,
      2,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesPackageNameSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      2,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesPhotoIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      2,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesAuthorIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      0,
      2,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesIndustryIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      2,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesAccountIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      2,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesIndustryParentIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      0,
      2,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesProductIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      2,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesOcpxActionTypeSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      0,
      2,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseNativeSoftFanstopRankCandidatesPackageNameSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      0,
      2,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesPhotoIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      1,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesAuthorIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      1,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesIndustryIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      1,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesAccountIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesIndustryParentIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      1,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesProductIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesOcpxActionTypeSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      1,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesPackageNameSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      0,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesPhotoIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      1,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesAuthorIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      1,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesIndustryIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      1,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesAccountIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesIndustryParentIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      1,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesProductIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesOcpxActionTypeSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      1,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterHardRankCandidatesPackageNameSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      0,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesPhotoIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      1,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesAuthorIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      1,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesIndustryIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      1,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesAccountIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesIndustryParentIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      1,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesProductIdSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesOcpxActionTypeSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      1,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesPackageNameSortByPrerankNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      1,
      0,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesPhotoIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      1,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesAuthorIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      1,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesIndustryIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      1,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesAccountIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesIndustryParentIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      1,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesProductIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesOcpxActionTypeSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      1,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesPackageNameSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      1,
      1,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesPhotoIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      1,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesAuthorIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      1,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesIndustryIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      1,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesAccountIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesIndustryParentIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      1,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesProductIdSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesOcpxActionTypeSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      1,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
using("ExtractUserSparseOuterSoftRankCandidatesPackageNameSortByRandomNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      1,
      2,
      50,
      generate_rank_candidates_list_feature
);
