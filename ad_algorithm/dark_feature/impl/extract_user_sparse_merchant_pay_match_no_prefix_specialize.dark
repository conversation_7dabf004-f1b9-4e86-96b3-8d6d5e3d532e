import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_sparse_merchant_pay_match_no_prefix_tmpl.dark"

using("ExtractUserSparseMerchantPayAuthorIdMatchNoPredix",
      "ExtractUserSparseMerchantPayMatchNoPrefixTmpl",
      {"adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_AUTHOR_ID:int64_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_TIMESTAMP:int64_list",
      100,
      0,
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_2:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_3:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_SPU_ID_LIST:int64_list"});

using("ExtractUserSparseMerchantPayLiveIdMatchNoPredix",
      "ExtractUserSparseMerchantPayMatchNoPrefixTmpl",
      {"adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_LIVE_ID:int64_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_TIMESTAMP:int64_list",
      100,
      1,
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_2:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_3:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_SPU_ID_LIST:int64_list"});

using("ExtractUserSparseMerchantPayX7Cate2MatchNoPredix",
      "ExtractUserSparseMerchantPayMatchNoPrefixTmpl",
      {"adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_X7_CATE2:int64_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_TIMESTAMP:int64_list",
      100,
      2,
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_2:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_3:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_SPU_ID_LIST:int64_list"});

using("ExtractUserSparseMerchantPayX7Cate3MatchNoPredix",
      "ExtractUserSparseMerchantPayMatchNoPrefixTmpl",
      {"adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_X7_CATE3:int64_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_TIMESTAMP:int64_list",
      100,
      3,
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_2:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_3:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_SPU_ID_LIST:int64_list"});

using("ExtractUserSparseMerchantPaySpuIdMatchNoPredix",
      "ExtractUserSparseMerchantPayMatchNoPrefixTmpl",
      {"adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_SPU_ID:int64_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_TIMESTAMP:int64_list",
      100,
      4,
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_2:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_3:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_SPU_ID_LIST:int64_list"});
