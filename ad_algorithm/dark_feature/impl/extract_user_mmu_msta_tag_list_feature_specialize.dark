import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_tmpl.dark"
using("ExtractUserSparseMmuImpTimeIn0And5STagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_QUESTION:int64_list",
      FeaturePrefix::PLAY_LESS_5S_QUESTION
      );

using("ExtractUserSparseMmuClkTimeIn0And5STagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_CALL_GUIDANCE:int64_list",
      FeaturePrefix::PLAY_LESS_5S_CALL_GUIDANCE
      );

using("ExtractUserSparseMmuImpTimeIn5And10STagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_FUNCTIONAL_BETTER:int64_list",
      FeaturePrefix::PLAY_LESS_5S_FUNCTIONAL_BETTER
      );

using("ExtractUserSparseMmuClkTimeIn5And10STagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_MORE_5S_QUESTION:int64_list",
      FeaturePrefix::PLAY_MORE_5S_QUESTION
      );

using("ExtractUserSparseMmuImpTimeIn10And15STagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_COST_ADVANTAGE:int64_list",
      FeaturePrefix::PLAY_LESS_5S_COST_ADVANTAGE
      );

using("ExtractUserSparseMmuClkTimeIn10And15STagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_ACTIVITIY_ATTRACT:int64_list",
      FeaturePrefix::PLAY_LESS_5S_ACTIVITIY_ATTRACT
      );

using("ExtractUserSparseMmuImpTimeIn15And20STagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_NORM_WOMEN_GENERAL:int64_list",
      FeaturePrefix::PLAY_LESS_5S_NORM_WOMEN_GENERAL
      );

using("ExtractUserSparseMmuClkTimeIn15And20STagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_MORE_5S_FUNCTIONAL_BETTER:int64_list",
      FeaturePrefix::PLAY_MORE_5S_FUNCTIONAL_BETTER
      );

using("ExtractUserSparseMmuImpTimeIn20And25STagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_MORE_5S_CALL_GUIDANCE:int64_list",
      FeaturePrefix::PLAY_MORE_5S_CALL_GUIDANCE
      );

using("ExtractUserSparseMmuClkTimeIn20And25STagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_NORM_WOMEN_OPERATION:int64_list",
      FeaturePrefix::PLAY_LESS_5S_NORM_WOMEN_OPERATION
      );

using("ExtractUserSparseMmuImpTimeIn25And30STagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_ITEM_SHOW:int64_list",
      FeaturePrefix::PLAY_LESS_5S_ITEM_SHOW
      );

using("ExtractUserSparseMmuClkTimeIn25And30STagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_MORE_5S_COST_ADVANTAGE:int64_list",
      FeaturePrefix::PLAY_MORE_5S_COST_ADVANTAGE
      );

using("ExtractUserSparseMmuImpTimeMore30STagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_MORE_5S_NORM_WOMEN_GENERAL:int64_list",
      FeaturePrefix::PLAY_MORE_5S_NORM_WOMEN_GENERAL
      );

using("ExtractUserSparseMmuClkTimeMore30STagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_MORE_5S_ACTIVITIY_ATTRACT:int64_list",
      FeaturePrefix::PLAY_MORE_5S_ACTIVITIY_ATTRACT
      );

using("ExtractUserSparseMmuImpTagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_EXTERNAL_ADVANTAGE:int64_list",
      FeaturePrefix::PLAY_LESS_5S_EXTERNAL_ADVANTAGE
      );

using("ExtractUserSparseMmuClkTagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_OTHER_QUESTION_NORM_WOMEN_GENERAL:int64_list",
      FeaturePrefix::PLAY_LESS_5S_OTHER_QUESTION_NORM_WOMEN_GENERAL
      );

using("ExtractUserSparseMmuActivateTagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_MORE_5S_NORM_WOMEN_OPERATION:int64_list",
      FeaturePrefix::PLAY_MORE_5S_NORM_WOMEN_OPERATION
      );

using("ExtractUserSparseMmuFormSubmitTagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_TRUST_ADVANTAGE:int64_list",
      FeaturePrefix::PLAY_LESS_5S_TRUST_ADVANTAGE
      );

using("ExtractUserSparseMmuOrderPayTagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_NORM_WOMEN_GENERAL_AFTER_XX:int64_list",
      FeaturePrefix::PLAY_LESS_5S_NORM_WOMEN_GENERAL_AFTER_XX
      );

using("ExtractUserSparseMmuWatchIn0And5TagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_MORE_5S_ITEM_SHOW:int64_list",
      FeaturePrefix::PLAY_MORE_5S_ITEM_SHOW
      );

using("ExtractUserSparseMmuWatchIn5And10TagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_QUALITY_ADVANTAGE:int64_list",
      FeaturePrefix::PLAY_LESS_5S_QUALITY_ADVANTAGE
      );

using("ExtractUserSparseMmuWatchIn10And15TagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_MORE_5S_EXTERNAL_ADVANTAGE:int64_list",
      FeaturePrefix::PLAY_MORE_5S_EXTERNAL_ADVANTAGE
      );

using("ExtractUserSparseMmuWatchIn15And20TagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_SERVICE_ATTRACT:int64_list",
      FeaturePrefix::PLAY_LESS_5S_SERVICE_ATTRACT
      );

using("ExtractUserSparseMmuWatchIn20And25TagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_LESS_5S_GREET_ONLY_HAND:int64_list",
      FeaturePrefix::PLAY_LESS_5S_GREET_ONLY_HAND
      );


using("ExtractUserSparseMmuWatchIn25And30TagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_MORE_5S_OTHER_QUESTION_NORM_WOMEN_GENERAL:int64_list",
      FeaturePrefix::PLAY_MORE_5S_OTHER_QUESTION_NORM_WOMEN_GENERAL
      );

using("ExtractUserSparseMmuWatchMore30TagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_MORE_5S_NORM_WOMEN_GENERAL_AFTER_XX:int64_list",
      FeaturePrefix::PLAY_MORE_5S_NORM_WOMEN_GENERAL_AFTER_XX
      );


using("ExtractUserSparseMmuWatchTagList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.PLAY_MORE_5S_QUALITY_ADVANTAGE:int64_list",
      FeaturePrefix::PLAY_MORE_5S_QUALITY_ADVANTAGE
      );