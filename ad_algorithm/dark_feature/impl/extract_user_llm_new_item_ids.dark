using("ExtractUserLlmColdstartNewItemId1",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.key:5100670:int64"
);
using("ExtractUserLlmColdstartNewItemId2",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.key:5100672:int64"
);
using("ExtractUserLlmColdstartTag",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.key:5100671:int64",
      cast_to_float,
      1
);
