// pxs
using("ExtractUserSparseTagCenterPxsProductGameplay",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_gameplay,
    const_game_product_name_mapping,
    KeyPosType::USER_AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_PHOTO_PLAYED_END_PRODUCT_NAME_LIST,
    tag_center_general2,
    FeaturePrefix::USER_AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterPxsProductTheme",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_theme,
    const_game_product_name_mapping,
    KeyPosType::USER_AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_PHOTO_PLAYED_END_PRODUCT_NAME_LIST,
    tag_center_general2,
    FeaturePrefix::USER_AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterPxsProductCate1",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_first_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_PHOTO_PLAYED_END_PRODUCT_NAME_LIST,
    tag_center_general2,
    FeaturePrefix::USER_AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterPxsProductCate2",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_second_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_PHOTO_PLAYED_END_PRODUCT_NAME_LIST,
    tag_center_general2,
    FeaturePrefix::USER_AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterPxsProductCate3",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_third_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_PHOTO_PLAYED_END_PRODUCT_NAME_LIST,
    tag_center_general2,
    FeaturePrefix::USER_AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST);

// click
using("ExtractUserSparseTagCenterClkProductGameplay",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_gameplay,
    const_game_product_name_mapping,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CLICK_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST,
    tag_center_general2,
    FeaturePrefix::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterClkProductTheme",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_theme,
    const_game_product_name_mapping,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CLICK_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST,
    tag_center_general2,
    FeaturePrefix::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterClkProductCate1",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_first_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CLICK_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST,
    tag_center_general2,
    FeaturePrefix::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterClkProductCate2",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_second_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CLICK_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST,
    tag_center_general2,
    FeaturePrefix::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterClkProductCate3",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_third_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CLICK_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST,
    tag_center_general2,
    FeaturePrefix::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST);

// conv
using("ExtractUserSparseTagCenterConvProductGameplay",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_gameplay,
    const_game_product_name_mapping,
    KeyPosType::USER_EVENT_CONVERSION_PRODUCT_NAME_LIST,
    KeyPosType::USER_EVENT_PAY_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_EVENT_NEXTDAY_STAY_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_EVENT_CONVERSION_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterConvProductTheme",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_theme,
    const_game_product_name_mapping,
    KeyPosType::USER_EVENT_CONVERSION_PRODUCT_NAME_LIST,
    KeyPosType::USER_EVENT_PAY_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_EVENT_NEXTDAY_STAY_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_EVENT_CONVERSION_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterConvProductCate1",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_first_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_EVENT_CONVERSION_PRODUCT_NAME_LIST,
    KeyPosType::USER_EVENT_PAY_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_EVENT_NEXTDAY_STAY_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_EVENT_CONVERSION_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterConvProductCate2",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_second_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_EVENT_CONVERSION_PRODUCT_NAME_LIST,
    KeyPosType::USER_EVENT_PAY_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_EVENT_NEXTDAY_STAY_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_EVENT_CONVERSION_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterConvProductCate3",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_third_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_EVENT_CONVERSION_PRODUCT_NAME_LIST,
    KeyPosType::USER_EVENT_PAY_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_EVENT_NEXTDAY_STAY_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_EVENT_CONVERSION_PRODUCT_NAME_LIST);

// other
using("ExtractUserSparseTagCenterOtherProductGameplay",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_gameplay,
    const_game_product_name_mapping,
    KeyPosType::USER_AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_ITEM_DOWNLOAD_INSTALLED_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_APPROXIMATE_PURCHASE_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterOtherProductTheme",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_theme,
    const_game_product_name_mapping,
    KeyPosType::USER_AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_ITEM_DOWNLOAD_INSTALLED_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_APPROXIMATE_PURCHASE_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterOtherProductCate1",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_first_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_ITEM_DOWNLOAD_INSTALLED_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_APPROXIMATE_PURCHASE_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterOtherProductCate2",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_second_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_ITEM_DOWNLOAD_INSTALLED_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_APPROXIMATE_PURCHASE_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterOtherProductCate3",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_third_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_ITEM_DOWNLOAD_INSTALLED_PRODUCT_NAME_LIST,
    KeyPosType::USER_AD_APPROXIMATE_PURCHASE_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST);

// neg
using("ExtractUserSparseTagCenterNegProductGameplay",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_gameplay,
    const_game_product_name_mapping,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CANCEL_LIKE_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_AD_LIVE_CANCEL_FOLLOW_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterNegProductTheme",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_theme,
    const_game_product_name_mapping,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CANCEL_LIKE_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_AD_LIVE_CANCEL_FOLLOW_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterNegProductCate1",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_first_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CANCEL_LIKE_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_AD_LIVE_CANCEL_FOLLOW_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterNegProductCate2",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_second_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CANCEL_LIKE_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_AD_LIVE_CANCEL_FOLLOW_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_PRODUCT_NAME_LIST);
using("ExtractUserSparseTagCenterNegProductCate3",
    FeatureType::USER,
    log,
    pos,
    const_industry_v5_third_level_name,
    const_game_product_name_mapping,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_CANCEL_LIKE_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_PRODUCT_NAME_LIST,
    KeyPosType::USER_ADLOGFULL_AD_LIVE_CANCEL_FOLLOW_PRODUCT_NAME_LIST,
    tag_center_general3,
    FeaturePrefix::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_PRODUCT_NAME_LIST);
