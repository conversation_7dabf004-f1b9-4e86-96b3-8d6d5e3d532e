using("ExtractUserSparsePrerankCandidatesListHardAccountIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_HARD_ACCOUNT_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesListHardPhotoIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_HARD_PHOTO_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesListHardIndustryIdV3NoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_HARD_INDUSTRY_V3_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesListHardCityProductIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_HARD_CITY_PRODUCT_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesListHardOcpxActionTypeNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_HARD_OCPX_ACTION_TYPE_LIST:int64_list"
      );
      
using("ExtractUserSparsePrerankCandidatesListSoftAccountIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_SOFT_ACCOUNT_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesListSoftPhotoIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_SOFT_PHOTO_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesListSoftIndustryIdV3NoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_SOFT_INDUSTRY_V3_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesListSoftCityProductIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_SOFT_CITY_PRODUCT_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesListSoftOcpxActionTypeNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_SOFT_OCPX_ACTION_TYPE_LIST:int64_list"
      );