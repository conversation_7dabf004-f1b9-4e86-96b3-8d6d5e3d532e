using("ExtractUserSparseBuyerEffectiveTypeV2",
    FeatureType::USER,
    "adlog.context.info_common_attr.BUYER_EFFECTIVE_TYPE:string",
    get_user_buyer_effective_type);

using("ExtractUserSparseUserIdU4Plus",
    FeatureType::USER,
    "adlog.context.info_common_attr.BUYER_EFFECTIVE_TYPE:string",
    "adlog.user_info.id",
    6,
    get_user_id_by_u_type);

using("ExtractUserSparseUserIdU4",
    FeatureType::USER,
    "adlog.context.info_common_attr.BUYER_EFFECTIVE_TYPE:string",
    "adlog.user_info.id",
    5,
    get_user_id_by_u_type);

using("ExtractUserSparseUserIdU3",
    FeatureType::USER,
    "adlog.context.info_common_attr.BUYER_EFFECTIVE_TYPE:string",
    "adlog.user_info.id",
    4,
    get_user_id_by_u_type);

using("ExtractUserSparseUserIdU2",
    FeatureType::USER,
    "adlog.context.info_common_attr.BUYER_EFFECTIVE_TYPE:string",
    "adlog.user_info.id",
    3,
    get_user_id_by_u_type);
