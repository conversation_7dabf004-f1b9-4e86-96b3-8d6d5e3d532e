using("ExtractUserSparsePhysicalPosId",
      FeatureType::USER,
      "adlog.context.info_common_attr.PHYSICAL_POS_ID:int64",
      0,
      value_or);
using("ExtractUserSparseWinCpmStyle1Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_1_D1:int64",
    0,
    value_or);
using("ExtractUserSparseWinCpmStyle2Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_2_D1:int64",
    0,
    value_or);
using("ExtractUserSparseWinCpmStyle4Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_4_D1:int64",
    0,
    value_or);
using("ExtractUserSparseWinCpmStyle13Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_13_D1:int64",
    0,
    value_or);
using("ExtractUserSparseWinCpmStyle0Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_0_D1:int64",
    0,
    value_or);

using("ExtractUserSparseWinCpmStyle1Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_1_D3:int64",
    0,
    value_or);
using("ExtractUserSparseWinCpmStyle2Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_2_D3:int64",
    0,
    value_or);
using("ExtractUserSparseWinCpmStyle4Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_4_D3:int64",
    0,
    value_or);
using("ExtractUserSparseWinCpmStyle13Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_13_D3:int64",
    0,
    value_or);
using("ExtractUserSparseWinCpmStyle0Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_0_D3:int64",
    0,
    value_or);

using("ExtractUserSparseWinCpmStyle1Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_1_D7:int64",
    0,
    value_or);
using("ExtractUserSparseWinCpmStyle2Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_2_D7:int64",
    0,
    value_or);
using("ExtractUserSparseWinCpmStyle4Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_4_D7:int64",
    0,
    value_or);
using("ExtractUserSparseWinCpmStyle13Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_13_D7:int64",
    0,
    value_or);
using("ExtractUserSparseWinCpmStyle0Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_WIN_RANK_0_D7:int64",
    0,
    value_or);



using("ExtractUserSparseLoseCpmStyle1Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_1_D1:int64",
    0,
    value_or);
using("ExtractUserSparseLoseCpmStyle2Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_2_D1:int64",
    0,
    value_or);
using("ExtractUserSparseLoseCpmStyle4Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_4_D1:int64",
    0,
    value_or);
using("ExtractUserSparseLoseCpmStyle13Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_13_D1:int64",
    0,
    value_or);
using("ExtractUserSparseLoseCpmStyle0Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_0_D1:int64",
    0,
    value_or);

using("ExtractUserSparseLoseCpmStyle1Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_1_D3:int64",
    0,
    value_or);
using("ExtractUserSparseLoseCpmStyle2Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_2_D3:int64",
    0,
    value_or);
using("ExtractUserSparseLoseCpmStyle4Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_4_D3:int64",
    0,
    value_or);
using("ExtractUserSparseLoseCpmStyle13Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_13_D3:int64",
    0,
    value_or);
using("ExtractUserSparseLoseCpmStyle0Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_0_D3:int64",
    0,
    value_or);

using("ExtractUserSparseLoseCpmStyle1Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_1_D7:int64",
    0,
    value_or);
using("ExtractUserSparseLoseCpmStyle2Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_2_D7:int64",
    0,
    value_or);
using("ExtractUserSparseLoseCpmStyle4Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_4_D7:int64",
    0,
    value_or);
using("ExtractUserSparseLoseCpmStyle13Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_13_D7:int64",
    0,
    value_or);
using("ExtractUserSparseLoseCpmStyle0Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_APM_LOSE_RANK_0_D7:int64",
    0,
    value_or);

using("ExtractUserSparseRtaStyle1Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_1_D7:int64",
    0,
    value_or);
using("ExtractUserSparseRtaStyle2Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_2_D7:int64",
    0,
    value_or);
using("ExtractUserSparseRtaStyle4Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_4_D7:int64",
    0,
    value_or);
using("ExtractUserSparseRtaStyle13Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_13_D7:int64",
    0,
    value_or);

using("ExtractUserSparseRtaStyle1Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_1_D3:int64",
    0,
    value_or);
using("ExtractUserSparseRtaStyle2Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_2_D3:int64",
    0,
    value_or);
using("ExtractUserSparseRtaStyle4Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_4_D3:int64",
    0,
    value_or);
using("ExtractUserSparseRtaStyle13Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_13_D3:int64",
    0,
    value_or);

using("ExtractUserSparseRtaStyle1Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_1_D1:int64",
    0,
    value_or);
using("ExtractUserSparseRtaStyle2Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_2_D1:int64",
    0,
    value_or);
using("ExtractUserSparseRtaStyle4Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_4_D1:int64",
    0,
    value_or);
using("ExtractUserSparseRtaStyle13Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_13_D1:int64",
    0,
    value_or);

using("ExtractUserSparseRtaStyle0Day1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_0_D1:int64",
    0,
    value_or);
using("ExtractUserSparseRtaStyle0Day3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_0_D3:int64",
    0,
    value_or);
using("ExtractUserSparseRtaStyle0Day7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_RTA_BID_RANK_0_D7:int64",
    0,
    value_or);

