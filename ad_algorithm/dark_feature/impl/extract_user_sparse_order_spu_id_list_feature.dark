using("ExtractUserSparseOrderSpuIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.key:5102836:int64_list");

using("ExtractUserSparseOrderSkuIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.key:5102836:int64_list",
      50,
      0,
      fill_int64_list_with_default);

using("ExtractUserDenseOrderGmvList",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.key:5102839:float_list",
      50,
      0.0,
      fill_float_list_with_default,
      50);