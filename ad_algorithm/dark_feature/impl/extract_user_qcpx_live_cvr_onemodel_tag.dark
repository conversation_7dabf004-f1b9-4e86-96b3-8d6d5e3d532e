using("ExtractQcpxDenseLiveCvrOnemodelTag",
    FeatureType::DENSE_USER,
    "adlog.item.ad_dsp_info.unit.base.ocpc_action_type:int64",
    "adlog.context.info_common_attr.INNER_LIVE_QCPX_CVR_ONEMODEL_ORDER:int64",
    "adlog.context.info_common_attr.INNER_LIVE_QCPX_CVR_ONEMODEL_ROAS:int64",
    "adlog.context.info_common_attr.INNER_LIVE_QCPX_CVR_ONEMODEL_T7ROAS:int64",
    get_inner_live_qcpx_onemodel_tag,
    cast_to_float,
    1);

using("ExtractQcpxSparseLiveCvrOnemodelTag",
    FeatureType::USER,
    "adlog.item.ad_dsp_info.unit.base.ocpc_action_type:int64",
    "adlog.context.info_common_attr.INNER_LIVE_QCPX_CVR_ONEMODEL_ORDER:int64",
    "adlog.context.info_common_attr.INNER_LIVE_QCPX_CVR_ONEMODEL_ROAS:int64",
    "adlog.context.info_common_attr.INNER_LIVE_QCPX_CVR_ONEMODEL_T7ROAS:int64",
    get_inner_live_qcpx_onemodel_tag
    );