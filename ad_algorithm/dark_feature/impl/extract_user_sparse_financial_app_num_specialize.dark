import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_tmpl.dark"

using("ExtractUserSparseFinancial15d",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.DMP_APP_INSTALL_NUM_FINACIAL_15D:int64_list",
      FeaturePrefix::DMP_APP_INSTALL_NUM_FINACIAL_15D);
using("ExtractUserSparseFinancial7d",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.DMP_APP_INSTALL_NUM_FINACIAL_7D:int64_list",
      FeaturePrefix::DMP_APP_INSTALL_NUM_FINACIAL_7D);
using("ExtractUserSparseFinancial3d",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.DMP_APP_INSTALL_NUM_FINACIAL_3D:int64_list",
      FeaturePrefix::DMP_APP_INSTALL_NUM_FINACIAL_3D);
using("ExtractUserSparseCredit1d",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.DMP_APP_INSTALL_NUM_CREDIT_CARD_1D:int64_list",
      FeaturePrefix::DMP_APP_INSTALL_NUM_CREDIT_CARD_1D);
using("ExtractUserSparseLoan3d",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.DMP_APP_INSTALL_NUM_LOAN_3D:int64_list",
      FeaturePrefix::DMP_APP_INSTALL_NUM_LOAN_3D);
using("ExtractUserSparseLoan7d",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.DMP_APP_INSTALL_NUM_LOAN_7D:int64_list",
      FeaturePrefix::DMP_APP_INSTALL_NUM_LOAN_7D);
using("ExtractUserSparseLoan15d",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.DMP_APP_INSTALL_NUM_LOAN_15D:int64_list",
      FeaturePrefix::DMP_APP_INSTALL_NUM_LOAN_15D);
using("ExtractUserSparseLoan30d",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.DMP_APP_INSTALL_NUM_LOAN_30D:int64_list",
      FeaturePrefix::DMP_APP_INSTALL_NUM_LOAN_30D);
using("ExtractUserSparseLoan90d",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.DMP_APP_INSTALL_NUM_LOAN_90D:int64_list",
      FeaturePrefix::DMP_APP_INSTALL_NUM_LOAN_90D);
using("ExtractUserSparseLoan180d",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.DMP_APP_INSTALL_NUM_LOAN_180D:int64_list",
      FeaturePrefix::DMP_APP_INSTALL_NUM_LOAN_180D);