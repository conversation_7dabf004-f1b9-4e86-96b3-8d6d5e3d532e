class ExtractUserSparseMerchantPayMatchNoPrefixTmpl<action_field : Field,
                                                      ts_field: Field,
                                                      max_len: int,
                                                      target_type: int,
                                                      x7_level_2_field:Field,
                                                      x7_level_3_field:Field,
                                                      spu_id_field:Field
                                                      > {
  set_feature_type(FeatureType::COMBINE);

  fn Extract(log, pos, result) {
    live_author_id = get_field("adlog.item.ad_dsp_info.live_info.author_info.id");
    photo_author_id = get_field("adlog.item.ad_dsp_info.photo_info.author_info.id");
    live_id = get_field("adlog.item.ad_dsp_info.live_info.id");
    x7_level_2_list = get_field(x7_level_2_field);
    x7_level_3_list = get_field(x7_level_3_field);
    spu_id_list = get_field(spu_id_field);
    target_id_list = get_mathched_target_id(live_author_id,photo_author_id,live_id,x7_level_2_list,x7_level_3_list,spu_id_list,target_type);
   
    adlog_time = get_field("adlog.time");
    action_content_list = get_field(action_field);
    action_timestamp_list = get_field(ts_field);
    enums = get_match_union(action_content_list, action_timestamp_list,target_id_list, max_len, adlog_time,3600);
    add_feature_result(enums, result);
  }
}
