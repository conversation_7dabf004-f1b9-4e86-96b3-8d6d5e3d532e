class ExtractUserSparseRealtimeAuthorMatchDetailNoPrefixTmpl<action_field : str,
                                                      ts_field: str,
                                                      max_len: int> {
  set_feature_type(FeatureType::COMBINE);

  fn Extract(log, pos, result) {
    adlog_time = get_field("adlog.time");
    author_id = get_field("adlog.item.ad_dsp_info.photo_info.author_info.id");
    user_action_content_list = get_field(action_field);
    user_action_timestamp_list = get_field(ts_field);
    enums = get_match_detail(user_action_content_list, user_action_timestamp_list,
                             author_id, max_len, adlog_time);

    add_feature_result(enums, result);
  }
}

class ExtractUserSparseRealtimePhotoMatchDetailNoPrefixTmpl<action_field : str,
                                                      ts_field: str,
                                                      max_len: int> {
  set_feature_type(FeatureType::COMBINE);

  fn Extract(log, pos, result) {
    adlog_time = get_field("adlog.time");
    photo_id = get_field("adlog.item.ad_dsp_info.photo_info.id");
    user_action_content_list = get_field(action_field);
    user_action_timestamp_list = get_field(ts_field);
    enums = get_match_detail(user_action_content_list, user_action_timestamp_list,
                             photo_id, max_len, adlog_time);

    add_feature_result(enums, result);
  }
}