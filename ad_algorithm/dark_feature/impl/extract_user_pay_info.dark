using_seq("ExtractUserPayInfoIndustryList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoIndustryAmountProductList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_AMOUNT_PRODUCT_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoIndustryAmountTimeList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_AMOUNT_TIME_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoIndustryTimeProductList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_TIME_PRODUCT_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoIndustryProductSumList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_PRODUCT_SUM_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoIndustryBigRList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_BIG_R_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoProductBigRList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_PRODUCT_BIG_R_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoIndustryDailyGiftList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_DAILY_GIFT_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoProductDailyGiftList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_PRODUCT_DAILY_GIFT_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoIndustryMonthlyCardList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_MONTHLY_CARD_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoProductMonthlyCardList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_PRODUCT_MONTHLY_CARD_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoIndustryWeeklyCardList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_WEEKLY_CARD_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoProductWeeklyCardList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_PRODUCT_WEEKLY_CARD_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoIndustryFundCardList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_FUND_CARD_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoProductFundCardList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_PRODUCT_FUND_CARD_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoIndustryOneOffChargeList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_ONE_OFF_CHARGE_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoProductOneOffChargeList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_PRODUCT_ONE_OFF_CHARGE_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoIndustryKPOList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_KPO_LIST:int64_list",
       100);
using_seq("ExtractUserPayInfoProductKPOList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_INFO_PRODUCT_KPO_LIST:int64_list",
       100);
using("ExtractUserPayInfoIndustryAmountProductCombine",
    FeatureType::COMBINE,
    "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_LIST:int64_list",
    "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_AMOUNT_PRODUCT_LIST:int64_list",
    "adlog.item.ad_dsp_info.creative.base.industry_id_v3",
    get_feature_from_industry_lists_map);
using("ExtractUserPayInfoIndustryAmountTimeCombine",
    FeatureType::COMBINE,
    "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_LIST:int64_list",
    "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_AMOUNT_TIME_LIST:int64_list",
    "adlog.item.ad_dsp_info.creative.base.industry_id_v3",
    get_feature_from_industry_lists_map);
using("ExtractUserPayInfoIndustryTimeProductCombine",
    FeatureType::COMBINE,
    "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_LIST:int64_list",
    "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_TIME_PRODUCT_LIST:int64_list",
    "adlog.item.ad_dsp_info.creative.base.industry_id_v3",
    get_feature_from_industry_lists_map);
using("ExtractUserPayInfoIndustryProductSumCombine",
    FeatureType::COMBINE,
    "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_LIST:int64_list",
    "adlog.user_info.common_info_attr.USER_PAY_INFO_INDUSTRY_PRODUCT_SUM_LIST:int64_list",
    "adlog.item.ad_dsp_info.creative.base.industry_id_v3",
    get_feature_from_industry_lists_map);