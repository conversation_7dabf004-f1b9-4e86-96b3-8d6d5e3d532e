import "teams/ad/ad_algorithm/dark_feature/impl/extract_multi_field_tmpl.dark"
import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_tmpl.dark"

using("ExtractUserCurPageNum",
      "ExtractUserSparseNoPrefixTmpl",
      "adlog.context.info_common_attr.CURRENT_PAGE_NUMBER:int64");

using("ExtractUserHistRequestTimeGap",
      "ExtractUserSparse2FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_TIMESTAMPS:int64_list",
      "adlog.time:int64",
      get_hist_req_time_gap);

using("ExtractUserHistReqSuccTimeGap",
      "ExtractUserSparse2FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_TIMESTAMPS:int64_list",
      "adlog.time:int64",
      get_successive_time_gap);

using("ExtractUserHistReqSubPageIds",
      "ExtractUserSparseNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_SUB_PAGE_IDS:int64_list");

using("ExtractUserHistReqPageNums",
      "ExtractUserSparse2FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_PAGE_NUMS:int64_list",
      "adlog.context.info_common_attr.CURRENT_PAGE_NUMBER:int64",
      get_user_hist_page_num_ordered);


using("ExtractUserHistRequestTimeGapOrdered",
      FeatureType::USER,
      "adlog.context.info_common_attr.HIST_REQ_TIMESTAMPS:int64_list",
      "adlog.time",
      get_hist_req_time_gap_ordered);

using("ExtractUserHistReqSuccTimeGapOrdered",
      FeatureType::USER,
      "adlog.context.info_common_attr.HIST_REQ_TIMESTAMPS:int64_list",
      "adlog.time",
      get_successive_time_gap_ordered);

using("ExtractUserHistReqSubPageIdsOrdered",
      FeatureType::USER,
      "adlog.context.info_common_attr.HIST_REQ_SUB_PAGE_IDS:int64_list",
      get_hist_req_sub_page_id_ordered);

using("ExtractUserHistReqPageNumsGapOrdered",
      FeatureType::USER,
      "adlog.context.info_common_attr.HIST_REQ_PAGE_NUMS:int64_list",
      "adlog.context.info_common_attr.CURRENT_PAGE_NUMBER:int64",
      get_user_hist_page_num_gap_ordered);

using("ExtractUserHistReqPageNumsSuccGapOrdered",
      FeatureType::USER,
      "adlog.context.info_common_attr.HIST_REQ_PAGE_NUMS:int64_list",
      "adlog.context.info_common_attr.CURRENT_PAGE_NUMBER:int64",
      get_user_hist_page_num_succ_gap_ordered);

