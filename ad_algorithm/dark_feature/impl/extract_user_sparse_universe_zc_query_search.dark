using("ExtractUserSparseUniverseDenseQueryTypeOneHot",
      FeatureType::DENSE_COMBINE, "adlog.context.info_common_attr.UNIVERSE_TINY_QUERY_TYPE:map_int64_int64",
      "adlog.item.ad_dsp_info.advertiser_base.product_name",
      city_hash64,
      get_value_from_map,
      11,
      one_hot,
      11
      );
using("ExtractUserSparseUniverseDenseQueryOneHot",
      FeatureType::DENSE_USER,
      "adlog.context.info_common_attr.QUERY:string",
      city_hash64,
      1000,
      mod,
      1000,
      one_hot,
      1000
      );
using("ExtractUserDenseUniverseQueryTypeProductOneHot",
      FeatureType::DENSE_COMBINE,
      "adlog.item.ad_dsp_info.advertiser_base.product_name",
      "adlog.context.info_common_attr.UNIVERSE_TINY_QUERY_TYPE:map_int64_int64",
      "adlog.item.ad_dsp_info.advertiser_base.product_name",
      city_hash64,
      get_value_from_map,
      get_query_type_product_hash,
      1000,
      mod,
      1000,
      one_hot,
      1000
      );
using("ExtractUserSparseUniverseQueryTypeProductOneHot",
       FeatureType::COMBINE,
       "adlog.item.ad_dsp_info.advertiser_base.product_name",
       city_hash64,
       "adlog.context.info_common_attr.UNIVERSE_TINY_QUERY_TYPE:map_int64_int64",
       "adlog.item.ad_dsp_info.advertiser_base.product_name",
       city_hash64,
       get_value_from_map,
       0,
       value_or,
       combine
       );

using("ExtractUserSparseProductNameId",
      FeatureType::USER,
      "adlog.item.ad_dsp_info.advertiser_base.product_name",
      city_hash64
      );

// 2023-08-29
using("ExtractCombineSparseUniverseQueryTypeProduct",
       FeatureType::COMBINE,
       "adlog.item.ad_dsp_info.advertiser_base.product_name",
       city_hash64,
       "adlog.context.info_common_attr.UNIVERSE_TINY_QUERY_TYPE:map_int64_int64",
       "adlog.item.ad_dsp_info.advertiser_base.product_name",
       city_hash64,
       get_value_from_map,
       -1,
       value_or,
       combine
       );

using("ExtractPhotoProductNameId",
      FeatureType::ITEM,
      "adlog.item.ad_dsp_info.advertiser_base.product_name",
      city_hash64
     );
//2023-09-07
using("ExtractUserSparseUniverseQueryTypeFix",
  FeatureType::COMBINE,
  "adlog.context.info_common_attr.UNIVERSE_TINY_QUERY_TYPE:map_int64_int64",
  "adlog.item.ad_dsp_info.advertiser_base.product_name",
  city_hash64,
  get_value_from_map,
  -1,
  value_or
  );

