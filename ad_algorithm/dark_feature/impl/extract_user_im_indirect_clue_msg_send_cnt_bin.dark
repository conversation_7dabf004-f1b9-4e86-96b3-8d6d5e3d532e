using("ExtractUserImIndirectTurnCntBinList30D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_TURN_CNT_BINING_LIST_30D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectC2bSendMsgCntBinList30D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_C2B_SEND_MSG_CNT_BINING_LIST_30D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectB2cAfterSendMsgCntBinList30D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_B2C_AFTER_SEND_MSG_CNT_BINING_LIST_30D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectTurnCntBinList15D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_TURN_CNT_BINING_LIST_15D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectC2bSendMsgCntBinList15D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_C2B_SEND_MSG_CNT_BINING_LIST_15D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectB2cAfterSendMsgCntBinList15D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_B2C_AFTER_SEND_MSG_CNT_BINING_LIST_15D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectTurnCntBinList7D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_TURN_CNT_BINING_LIST_7D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectC2bSendMsgCntBinList7D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_C2B_SEND_MSG_CNT_BINING_LIST_7D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectB2cAfterSendMsgCntBinList7D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_B2C_AFTER_SEND_MSG_CNT_BINING_LIST_7D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectTurnCntBinList1D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_TURN_CNT_BINING_LIST_1D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectC2bSendMsgCntBinList1D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_C2B_SEND_MSG_CNT_BINING_LIST_1D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectB2cAfterSendMsgCntBinList1D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_B2C_AFTER_SEND_MSG_CNT_BINING_LIST_1D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

