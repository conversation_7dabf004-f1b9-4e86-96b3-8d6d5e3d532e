using("ExtractUserSparseRealtimeP3sNoFollowNoPrefix",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_FOLLOW_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_FOLLOW_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_FOLLOW_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.time:int64",
      get_diff_value_and_merge);

using("ExtractUserSparseRealtimeP3sNoLikeNoPrefix",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_LIKE_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_LIKE_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_LIKE_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.time:int64",
      get_diff_value_and_merge);

using("ExtractUserSparseRealtimePedNoFollowNoPrefix",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_FOLLOW_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_FOLLOW_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_FOLLOW_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.time:int64",
      get_diff_value_and_merge);

using("ExtractUserSparseRealtimePedNoLikeNoPrefix",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_LIKE_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_LIKE_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_LIKE_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.time:int64",
      get_diff_value_and_merge);
