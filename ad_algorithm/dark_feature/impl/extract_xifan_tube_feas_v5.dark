using("ExtractTubeKsPlayCnt",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104665:int64");
using("ExtractTubeKsCompletePlayCnt",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104664:int64");
using("ExtractTubeKsInspireAdCost",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104663:int64");
using("ExtractTubeCostTotalIaa814",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104662:int64");
using("ExtractTubeKsInspireAdPayNum",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104661:int64");
using("ExtractTubeCpmIap",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104660:int64");
using("ExtractTubePayCnt2",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104659:int64");
using("ExtractTubeIaaActionShow",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104658:int64");
using("ExtractTubeCpmIaa17",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104657:int64");
using("ExtractTubeKsCostTotalNum",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104656:int64");
using("ExtractTubeCostTotalIap814",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104655:int64");
using("ExtractTubeTotalCostLi",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104654:int64");
using("ExtractTubeIapActionShow17",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104653:int64");
using("ExtractTubeCostTotalIaa17",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104652:int64");
using("ExtractTubeIaaActionShow814",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104651:int64");
using("ExtractTubeCpmIaa",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104650:int64");
using("ExtractTubeDescription2",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104649:int64");
using("ExtractTubePayPurchaseAmt",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104648:int64");
using("ExtractTubeItemClickCnt",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104647:int64");
using("ExtractTubeCostTotalIap",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104646:int64");
using("ExtractTubeActorRole",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104645:int64");
using("ExtractTubeKsShareCnt",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104644:int64");
using("ExtractTubeKsPlayDuration",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104643:int64");
using("ExtractTubeManagerId",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104642:int64");
using("ExtractTubeTubeSource2",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104641:int64");
using("ExtractTubeActorName",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104640:int64");
using("ExtractTubeKsCollectCnt",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104639:int64");
using("ExtractTubeActivationCnt",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104638:int64");
using("ExtractTubeIaaActionShow17",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104637:int64");
using("ExtractTubeKsLikeCnt",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104636:int64");
using("ExtractTubeAdShowCnt",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104635:int64");
using("ExtractTubeIapActionShow814",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104634:int64");
using("ExtractTubeCpmIap814",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104633:int64");
using("ExtractTubeKsShowCnt",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104632:int64");
using("ExtractTubeEntityId",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104631:int64");
using("ExtractTubeIapActionShow",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104630:int64");
using("ExtractTubeCostTotalIaa",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104629:int64");
using("ExtractTubeCreateTime2",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104628:int64");
using("ExtractTubeItemShowCnt2",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104627:int64");
using("ExtractTubeKsCommentCnt",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104626:int64");
using("ExtractTubeCpmIap17",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104625:int64");
using("ExtractTubeCostTotalIap17",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104624:int64");
using("ExtractTubeAdPhotoPlayed3s",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104623:int64");
using("ExtractTubeDirector",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104622:int64");
using("ExtractTubeProducer",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104621:int64");
using("ExtractTubeCpmIaa814",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104620:int64");
using("ExtractTubeKsPlayNum",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104619:int64");
using("ExtractTubeKsLongPlayCnt",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104618:int64");
using("ExtractTubeKsCostTotal",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104617:int64");
using("ExtractTubeConversionCnt",
       FeatureType::ITEM,
      "adlog.item.tube_item_info.common_info_attr.key:5104616:int64");