using("ExtractUserRealTimeEcomGoodsViewClientTagTimeList",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TAG_TIMESTAMP_LIST:int64_list",
      50,
      "adlog.time",
      5000,
      true,
      filter_timestamp_by_time_diff_thresh,
      cast_to_float,
      50);

using("ExtractUserRealTimeEcomGoodsViewSpuList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_SPUID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TAG_TIMESTAMP_LIST:int64_list",
      50,
      "adlog.time",
      5000,
      true,
      filter_action_by_time_diff_thresh);

using("ExtractUserRealTimeEcomGoodsViewX7Cate3List",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_X7_CATEGORY3_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TAG_TIMESTAMP_LIST:int64_list",
      50,
      "adlog.time",
      5000,
      true,
      filter_action_by_time_diff_thresh);

using("ExtractUserRealTimeEcomGoodsViewClientTimeList",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TIMESTAMP_LIST:int64_list",
      50,
      "adlog.time",
      5000,
      true,
      filter_timestamp_by_time_diff_thresh,
      cast_to_float,
      50);

using("ExtractUserRealTimeEcomGoodsViewItemList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TIMESTAMP_LIST:int64_list",
      50,
      "adlog.time",
      5000,
      true,
      filter_action_by_time_diff_thresh);

using("ExtractUserRealTimeEcomGoodsViewAuthorList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_AUTHOR_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TIMESTAMP_LIST:int64_list",
      50,
      "adlog.time",
      5000,
      true,
      filter_action_by_time_diff_thresh);