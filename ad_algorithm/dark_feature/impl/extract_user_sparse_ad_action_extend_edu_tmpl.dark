class ExtractUserSparseAdActionExtendEduMultiFieldTmpl<id_hash_field: Field,
                                               field1: Field,
                                               field2: Field,
                                               limit: int,
                                               feature_result_type: FeatureResultType,
                                               prefix: FeaturePrefix> {
  set_feature_type(FeatureType::USER);

  fn Extract(log, pos, result) {
    photo_ids = get_field(field1);
    product_id_hashs = get_field(field2);
    compare_ids = get_field(id_hash_field);
    
    edu_second_ids = get_edu_second_industry_id_hash_set();

    action_exists = get_action_not_exists(photo_ids, product_id_hashs, compare_ids);
    log_value = get_action_size_log_int_value(photo_ids);

    v1 = get_remain_ids_by_compare_ids(photo_ids, compare_ids, edu_second_ids);
    v2 = get_remain_ids_by_compare_ids(product_id_hashs, compare_ids, edu_second_ids);
    v3 = get_remain_ids_by_compare_ids(compare_ids, compare_ids, edu_second_ids);

    v = merge_int64_list_with_limit(limit, action_exists, log_value, v1, v2, v3);

    add_feature_result(v, result);
  }
}

using<id_hash_field: Field, field1: Field, field2: Field, limit: int, prefix: FeaturePrefix>
    ("ExtractUserSparseAdActionExtendEduMultiFieldWithPrefixTmpl",
    "ExtractUserSparseAdActionExtendEduMultiFieldTmpl",
    id_hash_field,
    field1,
    field2,
    limit,
    FeatureResultType::SPARSE_WITH_PREFIX,
    prefix);

using<id_hash_field: Field, field1: Field, field2: Field, limit: int>
    ("ExtractUserSparseAdActionExtendEduMultiFieldNoPrefixTmpl",
    "ExtractUserSparseAdActionExtendEduMultiFieldTmpl",
    id_hash_field,
    field1,
    field2,
    limit,
    FeatureResultType::SPARSE_NO_PREFIX,
    _);

  
class ExtractUserSparseAdActionExtendEduMultiFieldWithTimeTmpl<id_hash_field: Field,
                                               time_stamp_field: Field, 
                                               current_time_field: Field,
                                               field1: Field,
                                               field2: Field,
                                               limit: int,
                                               feature_result_type: FeatureResultType,
                                               prefix: FeaturePrefix> {
  set_feature_type(FeatureType::USER);

  fn Extract(log, pos, result) {
    photo_ids = get_field(field1);
    product_id_hashs = get_field(field2);
    compare_ids = get_field(id_hash_field);
    timestamp_ids = get_field(time_stamp_field);
    current_time = get_field(current_time_field);
    
    edu_second_ids = get_edu_second_industry_id_hash_set();

    action_exists = get_action_not_exists(photo_ids, product_id_hashs, compare_ids, timestamp_ids);
    log_value = get_action_size_log_int_value(photo_ids);

    v1 = get_remain_ids_by_compare_ids(photo_ids, compare_ids, edu_second_ids);
    v2 = get_remain_ids_by_compare_ids(product_id_hashs, compare_ids, edu_second_ids);
    v3 = get_remain_ids_by_compare_ids(compare_ids, compare_ids, edu_second_ids);
    v4 = get_remain_ids_by_compare_ids_with_passed_time(product_id_hashs, compare_ids, timestamp_ids, current_time, edu_second_ids);
    v5 = get_remain_ids_by_compare_ids_with_passed_time(compare_ids, compare_ids, timestamp_ids, current_time, edu_second_ids);
    v6 = get_remain_ids_by_compare_ids_with_recent_time(product_id_hashs, compare_ids, timestamp_ids, current_time, edu_second_ids);
    v7 = get_remain_ids_by_compare_ids_with_recent_time(compare_ids, compare_ids, timestamp_ids, current_time, edu_second_ids);

    v = merge_int64_list_with_limit(limit, action_exists, log_value, v1, v2, v3, v4, v5, v6 ,v7);

    add_feature_result(v, result);
  }
}


using<id_hash_field: Field, time_stamp_field: Field, current_time_field: Field, field1: Field, field2: Field, limit: int, prefix: FeaturePrefix>
    ("ExtractUserSparseAdActionExtendEduMultiFieldWithTimeWithPrefixTmpl",
    "ExtractUserSparseAdActionExtendEduMultiFieldWithTimeTmpl",
    id_hash_field,
    time_stamp_field,
    current_time_field,
    field1,
    field2,
    limit,
    FeatureResultType::SPARSE_WITH_PREFIX,
    prefix);

using<id_hash_field: Field, time_stamp_field: Field, current_time_field: Field, field1: Field, field2: Field, limit: int>
    ("ExtractUserSparseAdActionExtendEduMultiFieldWithTimeNoPrefixTmpl",
    "ExtractUserSparseAdActionExtendEduMultiFieldWithTimeTmpl",
    id_hash_field,
    time_stamp_field,
    current_time_field,
    field1,
    field2,
    limit,
    FeatureResultType::SPARSE_NO_PREFIX,
    _);