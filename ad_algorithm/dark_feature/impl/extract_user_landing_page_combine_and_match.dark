using("ExtractP3sCateMatchDense",
    FeatureType::DENSE_COMBINE,
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P3S_1cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,    
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P3S_2cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,    
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P3S_3cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,
    merge_float_list_all,
    3);
    
using("ExtractP5sCateMatchDense",
    FeatureType::DENSE_COMBINE,
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P5S_1cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,    
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P5S_2cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,    
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P5S_3cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,
    merge_float_list_all,
    3);
    
using("ExtractPendCateMatchDense",
    FeatureType::DENSE_COMBINE,
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_PEND_1cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,    
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_PEND_2cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,    
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_PEND_3cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,
    merge_float_list_all,
    3);
    
    
using("ExtractClkCateMatchDense",
    FeatureType::DENSE_COMBINE,
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_CLK_1cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,    
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_CLK_2cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,    
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_CLK_3cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,
    merge_float_list_all,
    3);
    
    
using("ExtractLpsCateMatchDense",
    FeatureType::DENSE_COMBINE,
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_LPS_1cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,    
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_LPS_2cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,    
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_LPS_3cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
    get_realtime_target_id_match_cnt_v2,
    get_segment_cnt_log_value,
    merge_float_list_all,
    3);


using("ExtractCombineP3s1CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P3S_1cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
      get_realtime_target_id_cross_list
);

using("ExtractCombineP3s2CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P3S_2cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
      get_realtime_target_id_cross_list
);

using("ExtractCombineP3s3CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P3S_3cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
      get_realtime_target_id_cross_list
);


using("ExtractCombineP5s1CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P5S_1cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
      get_realtime_target_id_cross_list
);

using("ExtractCombineP5s2CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P5S_2cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
      get_realtime_target_id_cross_list
);

using("ExtractCombineP5s3CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P5S_3cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
      get_realtime_target_id_cross_list
);


using("ExtractCombinePend1CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_PEND_1cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
      get_realtime_target_id_cross_list
);

using("ExtractCombinePend2CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_PEND_2cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
      get_realtime_target_id_cross_list
);

using("ExtractCombinePend3CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_PEND_3cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
      get_realtime_target_id_cross_list
);



using("ExtractCombineClk1CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_CLK_1cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
      get_realtime_target_id_cross_list
);

using("ExtractCombineClk2CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_CLK_2cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
      get_realtime_target_id_cross_list
);

using("ExtractCombineClk3CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_CLK_3cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
      get_realtime_target_id_cross_list
);


using("ExtractCombineLps1CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_LPS_1cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
      get_realtime_target_id_cross_list
);

using("ExtractCombineLps2CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_LPS_2cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
      get_realtime_target_id_cross_list
);

using("ExtractCombineLps3CateIdCross",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_LPS_3cate_30D:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
      get_realtime_target_id_cross_list
);

using("ExtractP3sCateMatchRatioDense",
    FeatureType::DENSE_COMBINE,
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P3S_1cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
    get_realtime_target_id_match_ratio,

    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P3S_2cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
    get_realtime_target_id_match_ratio,

    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P3S_3cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
     get_realtime_target_id_match_ratio,
     merge_float_list_all,
     3);


using("ExtractP5sCateMatchRatioDense",
    FeatureType::DENSE_COMBINE,
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P5S_1cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
    get_realtime_target_id_match_ratio,

    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P5S_2cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
    get_realtime_target_id_match_ratio,

    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_P5S_3cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
     get_realtime_target_id_match_ratio,
     merge_float_list_all,
     3);



using("ExtractPendCateMatchRatioDense",
    FeatureType::DENSE_COMBINE,
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_PEND_1cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
    get_realtime_target_id_match_ratio,

   "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_PEND_2cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
    get_realtime_target_id_match_ratio,

    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_PEND_3cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
     get_realtime_target_id_match_ratio,
     merge_float_list_all,
     3);


using("ExtractClkCateMatchRatioDense",
    FeatureType::DENSE_COMBINE,
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_CLK_1cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
    get_realtime_target_id_match_ratio,

   "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_CLK_2cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
    get_realtime_target_id_match_ratio,

    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_CLK_3cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
     get_realtime_target_id_match_ratio,
     merge_float_list_all,
     3);


using("ExtractLpsCateMatchRatioDense",
    FeatureType::DENSE_COMBINE,
    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_LPS_1cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100040:int64",
    get_realtime_target_id_match_ratio,

    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_LPS_2cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100041:int64",
    get_realtime_target_id_match_ratio,

    "adlog.user_info.common_info_attr.LPS_LLM_LANDING_USER_LPS_3cate_30D:int64_list",
    "adlog.item.ad_dsp_info.common_info_attr.key:5100039:int64",
    get_realtime_target_id_match_ratio,
    merge_float_list_all,
    3);
