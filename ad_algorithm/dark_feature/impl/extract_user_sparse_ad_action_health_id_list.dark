using("ExtractUserSparseAdActionHealthPhotoIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_AD_ACTION_HEALTH_PHOTO_ID_LIST:int64_list",
      get_industry_seq_feature);

using("ExtractUserSparseAdActionHealthAccountIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_AD_ACTION_HEALTH_ACCOUNT_ID_LIST:int64_list",
      get_industry_seq_feature);

using("ExtractUserSparseAdActionHealthAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_AD_ACTION_HEALTH_AUTHOR_ID_LIST:int64_list",
      get_industry_seq_feature);

using("ExtractUserSparseAdActionHealthProductNameList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_AD_ACTION_HEALTH_PRODUCT_NAME_LIST:int64_list",
      get_industry_seq_feature);

using("ExtractUserSparseAdActionHealthActionTypeList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_AD_ACTION_HEALTH_ACTION_TYPE_LIST:int64_list",
      get_industry_seq_feature);
