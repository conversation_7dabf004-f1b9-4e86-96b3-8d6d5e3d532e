// imp
using("ExtractUserSparseAdImpressionExtendWangfu",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:0.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:0.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:0.list.product_id_hash",
    get_wangfu_user_action_extend);
// click2
using("ExtractUserSparseAdClick2ExtendWangfu",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:2.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:2.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:2.list.product_id_hash",
    get_wangfu_user_action_extend);
// p3s
using("ExtractUserSparseAdP3sExtendWangfu",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:3.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:3.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:3.list.product_id_hash",
    get_wangfu_user_action_extend);
// p5s
using("ExtractUserSparseAdP5sExtendWangfu",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:4.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:4.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:4.list.product_id_hash",
    get_wangfu_user_action_extend);
// play_end
using("ExtractUserSparseAdPedExtendWangfu",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:5.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:5.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:5.list.product_id_hash",
    get_wangfu_user_action_extend);
// Conv
using("ExtractUserSparseAdConvExtendWangfu",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:6.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:6.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:6.list.product_id_hash",
    get_wangfu_user_action_extend);
// Pay
using("ExtractUserSparseAdPayExtendWangfu",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:7.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:7.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:7.list.product_id_hash",
    get_wangfu_user_action_extend);
// Form
using("ExtractUserSparseAdFormExtendWangfu",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:8.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:8.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:8.list.product_id_hash",
    get_wangfu_user_action_extend);


