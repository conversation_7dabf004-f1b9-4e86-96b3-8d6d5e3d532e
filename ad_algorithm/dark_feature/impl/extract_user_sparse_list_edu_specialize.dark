using("ExtractUserSparseDmpEduAppInstallListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.DMP_EDU_APP_INSTALL_LIST:int64_list",
      FeaturePrefix::DMP_EDU_APP_INSTALL_LIST);

using("ExtractUserSparseDmpEduConversionlListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.DMP_EDU_AD_CONVERSION_LIST:int64_list",
      FeaturePrefix::DMP_EDU_AD_CONVERSION_LIST);

using("ExtractUserSparseDmpEduStarInteractListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.DMP_EDU_STAR_INTERACTION_LIST:int64_list",
      FeaturePrefix::DMP_EDU_STAR_INTERACTION_LIST);

using("ExtractUserSparseDmpEduShortPhotoListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.DMP_EDU_SHORT_PHOTO_LIST:int64_list",
      FeaturePrefix::DMP_EDU_SHORT_PHOTO_LIST);

using("ExtractUserSparseDmpEduMobileAppListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.DMP_EDU_MOBILE_APP_LIST:int64_list",
      FeaturePrefix::DMP_EDU_MOBILE_APP_LIST);

using("ExtractUserSparseDmpEduInteractBehaviorListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.DMP_EDU_INTERACTION_BEHAVIOR_LIST:int64_list",
      FeaturePrefix::DMP_EDU_INTERACTION_BEHAVIOR_LIST);

using("ExtractUserSparseDmpEduLongPhotoListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.DMP_EDU_LONG_PHOTO_LIST:int64_list",
      FeaturePrefix::DMP_EDU_LONG_PHOTO_LIST);

using("ExtractUserSparseDmpEduAppUnloadListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.DMP_EDU_APP_UNLOAD_LIST:int64_list",
      FeaturePrefix::DMP_EDU_APP_UNLOAD_LIST);

using("ExtractUserSparseHetuEduCate2ImpListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_CATE2_IMP_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_CATE2_IMP_LIST);
using("ExtractUserSparseHetuEduCate3ImpListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_CATE3_IMP_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_CATE3_IMP_LIST);
using("ExtractUserSparseHetuEduCate2P3sListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_CATE2_P3S_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_CATE3_P3S_LIST);
using("ExtractUserSparseHetuEduCate3P3sListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_CATE3_P3S_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_CATE2_P5S_LIST);
using("ExtractUserSparseHetuEduCate2P5sListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_CATE2_P5S_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_CATE2_CLICK_LIST);
using("ExtractUserSparseHetuEduCate3P5sListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_CATE3_P5S_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_CATE3_CLICK_LIST);
using("ExtractUserSparseHetuEduCate2ClickListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_CATE2_CLICK_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_CATE3_LPS_LIST);
using("ExtractUserSparseHetuEduCate3ClickListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_CATE3_CLICK_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_TAG_IMP_LIST);
using("ExtractUserSparseHetuEduCate2LpsListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_CATE2_LPS_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_TAG_P5S_LIST);
using("ExtractUserSparseHetuEduCate3LpsListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_CATE3_LPS_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_TAG_CLICK_LIST);
using("ExtractUserSparseHetuEduTagImpListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_TAG_IMP_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_CATE2_P3S_LIST);
using("ExtractUserSparseHetuEduTagP3sListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_TAG_P3S_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_CATE3_P5S_LIST);
using("ExtractUserSparseHetuEduTagP5sListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_TAG_P5S_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_CATE2_LPS_LIST);
using("ExtractUserSparseHetuEduTagClickListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_TAG_CLICK_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_TAG_P3S_LIST);
using("ExtractUserSparseHetuEduTagLpsListDark",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.HETU_TAGS_EDU_TAG_LPS_LIST:int64_list",
      FeaturePrefix::HETU_TAGS_EDU_TAG_LPS_LIST);

