using("ExtractUserCreateOrderTimestampList30D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100084:int64_list",
    100,
    take);
using("ExtractUserLpsActionTimestampList30D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100083:int64_list",
    100,
    take);
using("ExtractUserClueSubmitTimestampList30D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100082:int64_list",
    100,
    take);
using("ExtractUserPoiArriveTimestampList90D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100081:int64_list",
    100,
    take);
using("ExtractUserGlobalActionSdpaCate3SortByTimestamp",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5003319:int64_list",
    "adlog.user_info.common_info_attr.key:5002944:int64_list",
    "adlog.user_info.common_info_attr.key:5003141:int64_list",
    "adlog.user_info.common_info_attr.key:5002960:int64_list",
    "adlog.user_info.common_info_attr.key:5100081:int64_list",
    "adlog.user_info.common_info_attr.key:5100082:int64_list",
    "adlog.user_info.common_info_attr.key:5100083:int64_list",
    "adlog.user_info.common_info_attr.key:5100084:int64_list",
    120,
    action_list_merge_and_sort_by_key);