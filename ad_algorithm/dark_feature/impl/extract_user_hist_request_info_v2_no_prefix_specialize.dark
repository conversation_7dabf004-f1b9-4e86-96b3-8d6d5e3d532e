import "teams/ad/ad_algorithm/dark_feature/impl/extract_multi_field_tmpl.dark"
import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_tmpl.dark"
import "teams/ad/ad_algorithm/dark_feature/impl/extract_item_tmpl.dark"

using("ExtractUserCurPageNumCrossPosId",
    "ExtractUserSparse2FieldNoPrefixTmpl",
    "adlog.context.info_common_attr.CURRENT_PAGE_NUMBER:int64",
    "adlog.context.pos_id:int64",
    get_cur_page_num_cross_pos_id);

using("ExtractUserCurPageNumCrossPlatform",
    "ExtractUserSparse2FieldNoPrefixTmpl",
    "adlog.context.info_common_attr.CURRENT_PAGE_NUMBER:int64",
    "adlog.user_info.ad_user_info.platform:string",
    get_cur_page_num_cross_platform);

using("ExtractUserCurPageNumCrossPosIdPlatform",
    "ExtractUserSparse3FieldNoPrefixTmpl",
    "adlog.context.info_common_attr.CURRENT_PAGE_NUMBER:int64",
    "adlog.context.pos_id:int64",
    "adlog.user_info.ad_user_info.platform:string",
    get_cur_page_num_cross_pos_id_platform);


using("ExtractUserHistRequestTimeGapCrossPosId",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_TIMESTAMPS:int64_list",
      "adlog.time:int64",
      "adlog.context.pos_id:int64",
      get_hist_req_time_gap_cross_pos_id);

using("ExtractUserHistRequestTimeGapCrossPlatform",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_TIMESTAMPS:int64_list",
      "adlog.time:int64",
      "adlog.user_info.ad_user_info.platform:string",
      get_hist_req_time_gap_cross_platform);

using("ExtractUserHistRequestTimeGapCrossPosIdPlatform",
      "ExtractUserSparse4FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_TIMESTAMPS:int64_list",
      "adlog.time:int64",
      "adlog.context.pos_id:int64",
      "adlog.user_info.ad_user_info.platform:string",
      get_hist_req_time_gap_cross_pos_id_platform);


using("ExtractUserHistReqSuccTimeGapCrossPosId",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_TIMESTAMPS:int64_list",
      "adlog.time:int64",
      "adlog.context.pos_id:int64",
      get_successive_time_gap_cross_pos_id);

using("ExtractUserHistReqSuccTimeGapCrossPlatform",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_TIMESTAMPS:int64_list",
      "adlog.time:int64",
      "adlog.user_info.ad_user_info.platform:string",
      get_successive_time_gap_cross_platform);

using("ExtractUserHistReqSuccTimeGapCrossPosIdPlatform",
      "ExtractUserSparse4FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_TIMESTAMPS:int64_list",
      "adlog.time:int64",
      "adlog.context.pos_id:int64",
      "adlog.user_info.ad_user_info.platform:string",
      get_successive_time_gap_cross_pos_id_platform);


using("ExtractUserHistReqSubPageIdsCrossPosId",
      "ExtractUserSparse2FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_SUB_PAGE_IDS:int64_list",
      "adlog.context.pos_id:int64",
      get_user_hist_req_sub_page_ids_cross_pos_id);

using("ExtractUserHistReqSubPageIdsCrossPlatform",
      "ExtractUserSparse2FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_SUB_PAGE_IDS:int64_list",
      "adlog.user_info.ad_user_info.platform:string",
      get_user_hist_req_sub_page_ids_cross_platform);

using("ExtractUserHistReqSubPageIdsCrossPosIdPlatform",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_SUB_PAGE_IDS:int64_list",
      "adlog.context.pos_id:int64",
      "adlog.user_info.ad_user_info.platform:string",
      get_user_hist_req_sub_page_ids_cross_pos_id_platform);


using("ExtractUserHistReqPageNumsCrossPosId",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_PAGE_NUMS:int64_list",
      "adlog.context.info_common_attr.CURRENT_PAGE_NUMBER:int64",
      "adlog.context.pos_id:int64",
      get_user_hist_page_num_ordered_cross_pos_id);

using("ExtractUserHistReqPageNumsCrossPlatform",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_PAGE_NUMS:int64_list",
      "adlog.context.info_common_attr.CURRENT_PAGE_NUMBER:int64",
      "adlog.user_info.ad_user_info.platform:string",
      get_user_hist_page_num_ordered_cross_platform);

using("ExtractUserHistReqPageNumsCrossPosIdPlatform",
      "ExtractUserSparse4FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_PAGE_NUMS:int64_list",
      "adlog.context.info_common_attr.CURRENT_PAGE_NUMBER:int64",
      "adlog.context.pos_id:int64",
      "adlog.user_info.ad_user_info.platform:string",
      get_user_hist_page_num_ordered_cross_pos_id_platform);


using("ExtractUserHistReqPageNumsSuccessiveCrossPosId",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_PAGE_NUMS:int64_list",
      "adlog.context.info_common_attr.CURRENT_PAGE_NUMBER:int64",
      "adlog.context.pos_id:int64",
      get_user_hist_page_num_succ_cross_pos_id);

using("ExtractUserHistReqPageNumsSuccessiveCrossPlatform",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_PAGE_NUMS:int64_list",
      "adlog.context.info_common_attr.CURRENT_PAGE_NUMBER:int64",
      "adlog.user_info.ad_user_info.platform:string",
      get_user_hist_page_num_succ_cross_platform);

using("ExtractUserHistReqPageNumsSuccessiveCrossPosIdPlatform",
      "ExtractUserSparse4FieldNoPrefixTmpl",
      "adlog.context.info_common_attr.HIST_REQ_PAGE_NUMS:int64_list",
      "adlog.context.info_common_attr.CURRENT_PAGE_NUMBER:int64",
      "adlog.context.pos_id:int64",
      "adlog.user_info.ad_user_info.platform:string",
      get_user_hist_page_num_succ_cross_pos_id_platform);
