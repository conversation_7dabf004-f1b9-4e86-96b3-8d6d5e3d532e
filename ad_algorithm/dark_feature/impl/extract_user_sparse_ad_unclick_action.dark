// user unclick sequence (ad)
// side info: photo_id, product_id_hash, second_industry_id_hash, aid, author_id
// time range: long_term, one_week, one_day, one_session
// outer&inner ad

using("ExtractUserSparseAdUnclickActionOneDayPhotoId",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60534:int64_list",
    "adlog.user_info.common_info_attr.key:60444:int64_list",
    "adlog.user_info.common_info_attr.key:60534:int64_list",
    "adlog.user_info.common_info_attr.key:60530:int64_list",
    "adlog.time",
    100,
    ********,
    unclick_action);

using("ExtractUserSparseAdUnclickActionOneDayPackageName",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60534:int64_list",
    "adlog.user_info.common_info_attr.key:60444:int64_list",
    "adlog.user_info.common_info_attr.key:60539:int64_list",
    "adlog.user_info.common_info_attr.key:60530:int64_list",
    "adlog.time",
    100,
    ********,
    unclick_action);

using("ExtractUserSparseAdUnclickActionOneDayProductName",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60534:int64_list",
    "adlog.user_info.common_info_attr.key:60444:int64_list",
    "adlog.user_info.common_info_attr.key:60538:int64_list",
    "adlog.user_info.common_info_attr.key:60530:int64_list",
    "adlog.time",
    100,
    ********,
    unclick_action);

using("ExtractUserSparseAdUnclickActionOneDayAccountId",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60534:int64_list",
    "adlog.user_info.common_info_attr.key:60444:int64_list",
    "adlog.user_info.common_info_attr.key:60537:int64_list",
    "adlog.user_info.common_info_attr.key:60530:int64_list",
    "adlog.time",
    100,
    ********,
    unclick_action);

using("ExtractUserSparseAdUnclickActionOneDayAuthorId",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60534:int64_list",
    "adlog.user_info.common_info_attr.key:60444:int64_list",
    "adlog.user_info.common_info_attr.key:60535:int64_list",
    "adlog.user_info.common_info_attr.key:60530:int64_list",
    "adlog.time",
    100,
    ********,
    unclick_action);

using("ExtractUserSparseAdUnclickActionOneDayIndustryV3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60534:int64_list",
    "adlog.user_info.common_info_attr.key:60444:int64_list",
    "adlog.user_info.common_info_attr.key:60536:int64_list",
    "adlog.user_info.common_info_attr.key:60530:int64_list",
    "adlog.time",
    100,
    ********,
    unclick_action);

using("ExtractUserSparseAdUnclickActionLongtermPhotoId",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60534:int64_list",
    "adlog.user_info.common_info_attr.key:60444:int64_list",
    "adlog.user_info.common_info_attr.key:60534:int64_list",
    "adlog.user_info.common_info_attr.key:60530:int64_list",
    "adlog.time",
    100,
    -1,
    unclick_action);

using("ExtractUserSparseAdUnclickActionLongtermPackageName",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60534:int64_list",
    "adlog.user_info.common_info_attr.key:60444:int64_list",
    "adlog.user_info.common_info_attr.key:60539:int64_list",
    "adlog.user_info.common_info_attr.key:60530:int64_list",
    "adlog.time",
    100,
    -1,
    unclick_action);

using("ExtractUserSparseAdUnclickActionLongtermProductName",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60534:int64_list",
    "adlog.user_info.common_info_attr.key:60444:int64_list",
    "adlog.user_info.common_info_attr.key:60538:int64_list",
    "adlog.user_info.common_info_attr.key:60530:int64_list",
    "adlog.time",
    100,
    -1,
    unclick_action);

using("ExtractUserSparseAdUnclickActionLongtermAccountId",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60534:int64_list",
    "adlog.user_info.common_info_attr.key:60444:int64_list",
    "adlog.user_info.common_info_attr.key:60537:int64_list",
    "adlog.user_info.common_info_attr.key:60530:int64_list",
    "adlog.time",
    100,
    -1,
    unclick_action);

using("ExtractUserSparseAdUnclickActionLongtermAuthorId",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60534:int64_list",
    "adlog.user_info.common_info_attr.key:60444:int64_list",
    "adlog.user_info.common_info_attr.key:60535:int64_list",
    "adlog.user_info.common_info_attr.key:60530:int64_list",
    "adlog.time",
    100,
    -1,
    unclick_action);

using("ExtractUserSparseAdUnclickActionLongtermIndustryV3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60534:int64_list",
    "adlog.user_info.common_info_attr.key:60444:int64_list",
    "adlog.user_info.common_info_attr.key:60536:int64_list",
    "adlog.user_info.common_info_attr.key:60530:int64_list",
    "adlog.time",
    100,
    -1,
    unclick_action);