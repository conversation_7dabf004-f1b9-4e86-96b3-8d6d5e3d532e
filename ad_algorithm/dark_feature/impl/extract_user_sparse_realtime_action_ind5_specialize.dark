import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_tag_center_tmpl.dark"

using("ExtractUserSparseRealtimeActionInd5Pxs",
      "ExtractUserSparseTagCenterTmpl2",
      "KeyPosType::USER_ADLOGFULL_AD_PHOTO_PLAYED_5S_ACCOUNT_ID_LIST",
      "KeyPosType::USER_ADLOGFULL_AD_PHOTO_PLAYED_END_ACCOUNT_ID_LIST",
        FeaturePrefix::INDUSTRY_V5_RTS_PXS_THIRD_CATEGORY_ID_LPS);

using("ExtractUserSparseRealtimeActionInd5Click",
      "ExtractUserSparseTagCenterTmpl2",
      "KeyPosType::USER_ADLOGFULL_AD_PHOTO_CLICK_ACCOUNT_ID_LIST",
      "KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST",
        FeaturePrefix::INDUSTRY_V5_RTS_CLICK_THIRD_CATEGORY_ID_LPS);

using("ExtractUserSparseRealtimeActionInd5Conv",
      "ExtractUserSparseTagCenterTmpl3",
      "KeyPosType::USER_ADLOGFULL_EVENT_CONVERSION_ACCOUNT_ID_LIST",
      "KeyPosType::USER_ADLOGFULL_EVENT_PAY_UNION_ACCOUNT_ID_LIST",
      "KeyPosType::USER_ADLOGFULL_EVENT_NEXTDAY_STAY_ACCOUNT_ID_LIST",
        FeaturePrefix::INDUSTRY_V5_RTS_CONV_THIRD_CATEGORY_ID_LPS);

using("ExtractUserSparseRealtimeActionInd5Other",
      "ExtractUserSparseTagCenterTmpl3",
      "KeyPosType::USER_ADLOGFULL_AD_ITEM_DOWNLOAD_COMPLETED_ACCOUNT_ID_LIST",
      "KeyPosType::USER_ADLOGFULL_AD_ITEM_DOWNLOAD_INSTALLED_ACCOUNT_ID_LIST",
      "KeyPosType::USER_ADLOGFULL_AD_APPROXIMATE_PURCHASE_ACCOUNT_ID_LIST",
        FeaturePrefix::INDUSTRY_V5_RTS_OTHER_THIRD_CATEGORY_ID_LPS);

using("ExtractUserSparseRealtimeActionInd5Neg",
      "ExtractUserSparseTagCenterTmpl3",
      "KeyPosType::USER_ADLOGFULL_AD_PHOTO_CANCEL_LIKE_ACCOUNT_ID_LIST",
      "KeyPosType::USER_ADLOGFULL_AD_PHOTO_NEGATIVE_ACCOUNT_ID_LIST",
      "KeyPosType::USER_ADLOGFULL_AD_LIVE_CANCEL_FOLLOW_ACCOUNT_ID_LIST",
        FeaturePrefix::INDUSTRY_V5_RTS_NEG_THIRD_CATEGORY_ID_LPS);
