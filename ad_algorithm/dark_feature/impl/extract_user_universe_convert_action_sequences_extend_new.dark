using("ExtractUserUniverseConvertActionSeqLogMinuteIntervalExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_log2_min_diff_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_log2_min_diff_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_DATE_INTERVAL:int64_list",
  convert_date_diff_to_log2_min_diff,
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqFirstIntustryNameExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_FIRST_INDUSTRY_NAME_CH:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_FIRST_INDUSTRY_NAME_CH:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_FIRST_INDUSTRY_NAME:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqSecondIntustryNameExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_SECOND_INDUSTRY_NAME_CH:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_SECOND_INDUSTRY_NAME_CH:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_SECOND_INDUSTRY_NAME:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqMediaAppIdExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIA_APP_ID:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVNET_CONVERSIONIS_MEDIA_APP_ID:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_MEDIUM_APP_ID:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);