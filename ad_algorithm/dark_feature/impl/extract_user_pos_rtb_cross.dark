using("ExtractUserPosImpRateRate1d",
     FeatureType::USER,
    "adlog.context.pos_id:int64",
     "adlog.user_info.common_info_attr.key:5103889:int64_list",
     "adlog.user_info.common_info_attr.key:5103898:float_list",
     pos_rtb_cross
     );

using("ExtractUserImpRate5ppRate1d",
     FeatureType::USER,
    "adlog.context.pos_id:int64",
     "adlog.user_info.common_info_attr.key:5103889:int64_list",
     "adlog.user_info.common_info_attr.key:5103900:float_list",
     pos_rtb_cross
     );

using("ExtractUserImpRate10ppRate1d",
     FeatureType::USER,
    "adlog.context.pos_id:int64",
     "adlog.user_info.common_info_attr.key:5103889:int64_list",
     "adlog.user_info.common_info_attr.key:5103891:float_list",
     pos_rtb_cross
     );


using("ExtractUserImpRate15ppRate1d",
     FeatureType::USER,
    "adlog.context.pos_id:int64",
     "adlog.user_info.common_info_attr.key:5103889:int64_list",
     "adlog.user_info.common_info_attr.key:5103902:float_list",
     pos_rtb_cross
     );


using("ExtractUserCpmRate1d",
     FeatureType::USER,
    "adlog.context.pos_id:int64",
     "adlog.user_info.common_info_attr.key:5103889:int64_list",
     "adlog.user_info.common_info_attr.key:5103894:float_list",
     pos_rtb_cross
     );


using("ExtractUserCpmRate5pp1d",
     FeatureType::USER,
    "adlog.context.pos_id:int64",
     "adlog.user_info.common_info_attr.key:5103889:int64_list",
     "adlog.user_info.common_info_attr.key:5103895:float_list",
     pos_rtb_cross
     );

using("ExtractUserCpmRate10pp1d",
     FeatureType::USER,
    "adlog.context.pos_id:int64",
     "adlog.user_info.common_info_attr.key:5103889:int64_list",
     "adlog.user_info.common_info_attr.key:5103903:float_list",
     pos_rtb_cross
     );

using("ExtractUserCpmRate15pp1d",
     FeatureType::USER,
    "adlog.context.pos_id:int64",
     "adlog.user_info.common_info_attr.key:5103889:int64_list",
     "adlog.user_info.common_info_attr.key:5103907:float_list",
     pos_rtb_cross
     );