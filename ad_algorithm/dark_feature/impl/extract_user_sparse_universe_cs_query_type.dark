using("ExtractUserSparseUniverseQueryType",
      FeatureType::USER,
      "adlog.context.info_common_attr.UNIVERSE_TINY_QUERY_TYPE:string",
      city_hash64,
      FeaturePrefix::UNIVERSE_TINY_QUERY_TYPE);
using("ExtractUserSparseUniverseQueryDenseType",
      FeatureType::DENSE_COMBINE,
      "adlog.context.info_common_attr.UNIVERSE_TINY_QUERY_TYPE:map_int64_int64",
      "adlog.item.ad_dsp_info.advertiser_base.product_name",
      city_hash64,
      get_value_from_map,
      cast_to_float,
      1);