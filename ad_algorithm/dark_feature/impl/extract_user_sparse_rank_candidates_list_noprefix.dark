using("ExtractUserSparseRankCandidatesListAccountIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ACCOUNT_ID_LIST:int64_list"
      );

using("ExtractUserSparseRankCandidatesListAuthorIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_AUTHOR_ID_LIST:int64_list"
      );

using("ExtractUserSparseRankCandidatesListPhotoIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_PHOTO_ID_LIST:int64_list"
      );

using("ExtractUserSparseRankCandidatesListIndustryIdV3NoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_INDUSTRY_V3_ID_LIST:int64_list"
      );

using("ExtractUserSparseRankCandidatesListIndustryParentIdV3NoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_INDUSTRY_PARENT_V3_LIST:int64_list"
      );

using("ExtractUserSparseRankCandidatesListCityProductIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_CITY_PRODUCT_ID_LIST:int64_list"
      );

using("ExtractUserSparseRankCandidatesListPackageIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_PACKAGE_ID_LIST:int64_list"
      );

using("ExtractUserSparseRankCandidatesListOcpxActionTypeNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_OCPX_ACTION_TYPE_LIST:int64_list"
      );