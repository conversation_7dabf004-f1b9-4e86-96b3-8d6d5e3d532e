
import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_sparse_merchant_pay_match_realTime_no_prefix_tmpl.dark"


using("ExtractUserSparseAuthorIdRealTimeMatchNoPredix",
      "ExtractUserSparseMerchantPayMatchRealTimeNoPrefixTmpl",
      {"adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_DISTRIBUTOR_ID:int64_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_TIMESTAMP:int64_list",
      100,
      0,
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_2:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_3:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_SPU_ID_LIST:int64_list"});

using("ExtractUserSparseLiveIdRealTimeMatchNoPredix",
      "ExtractUserSparseMerchantPayMatchRealTimeNoPrefixTmpl",
      {"adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_CARRIER_ID:int64_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_TIMESTAMP:int64_list",
      100,
      1,
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_2:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_3:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_SPU_ID_LIST:int64_list"});

using("ExtractUserSparseX7Cate2RealTimeMatchNoPredix",
      "ExtractUserSparseMerchantPayMatchRealTimeNoPrefixTmpl",
      {"adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_X7_CATE2:int64_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_TIMESTAMP:int64_list",
      100,
      2,
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_2:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_3:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_SPU_ID_LIST:int64_list"});

using("ExtractUserSparseX7Cate3RealTimeMatchNoPredix",
      "ExtractUserSparseMerchantPayMatchRealTimeNoPrefixTmpl",
      {"adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_X7_CATE3:int64_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_TIMESTAMP:int64_list",
      100,
      3,
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_2:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_3:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_SPU_ID_LIST:int64_list"});

using("ExtractUserSparseX7SpuIdRealTimeMatchNoPredix",
      "ExtractUserSparseMerchantPayMatchRealTimeNoPrefixTmpl",
      {"adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_SPU_ID:int64_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_TIMESTAMP:int64_list",
      100,
      4,
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_2:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AD_LIVE_YELLOW_CAR_MERCHANT_X7_LEVEL_3:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_SPU_ID_LIST:int64_list"});
