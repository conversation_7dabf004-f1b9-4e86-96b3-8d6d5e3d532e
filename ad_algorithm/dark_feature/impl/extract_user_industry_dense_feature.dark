using("ExtractUserIndustry14dPaycnt",
    FeatureType::COMBINE,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.key:5102377:int64",
    "adlog.user_info.common_info_attr.key:5102379:int64_list",
    get_match_pos_target_sequence,
    get_live_paycnt_segment);

using("ExtractUserIndustry14dPrice",
    FeatureType::COMBINE,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.key:5102377:int64",
    "adlog.user_info.common_info_attr.key:5102380:float_list",
    get_match_pos_target_sequence_float,
    get_live_price_segment);

using("ExtractUserIndustry14dGmv",
    FeatureType::COMBINE,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.key:5102377:int64",
    "adlog.user_info.common_info_attr.key:5102382:float_list",
    get_match_pos_target_sequence_float,
    get_live_gmv_segment);

using("ExtractUserIndustry7dPaycnt",
    FeatureType::COMBINE,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.key:5102377:int64",
    "adlog.user_info.common_info_attr.key:5102383:int64_list",
    get_match_pos_target_sequence,
    get_live_paycnt_segment);

using("ExtractUserIndustry7dPrice",
    FeatureType::COMBINE,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.key:5102377:int64",
    "adlog.user_info.common_info_attr.key:5102381:float_list",
    get_match_pos_target_sequence_float,
    get_live_price_segment);

using("ExtractUserIndustry7dGmv",
    FeatureType::COMBINE,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.key:5102377:int64",
    "adlog.user_info.common_info_attr.key:5102384:float_list",
    get_match_pos_target_sequence_float,
    get_live_gmv_segment);
