import "teams/ad/ad_algorithm/dark_feature/impl/extract_common_feature_tmpl.dark"
import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_sparse_edu_action_tmpl.dark";

// no count, size log, id list 特征都加上。
using("ExtractUserSparseEduActionClickMultiFieldAll",
    "ExtractUserSparseEduActionMultiFieldNoPrefixTmpl",
    "adlog.user_info.explore_long_term_ad_action.key:1.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:1.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:1.list.product_id_hash",
    10);

using("ExtractUserSparseEduActionClickNotExists",
    "ExtractUserSparse1FieldNoPrefixTmpl",
    "adlog.user_info.explore_long_term_ad_action.key:1.list.photo_id",
     get_action_not_exists);

using("ExtractUserSparseEduActionClickSizeLogIntValue",
     "ExtractUserSparse1FieldNoPrefixTmpl",
     "adlog.user_info.explore_long_term_ad_action.key:1.list.photo_id",
     get_action_size_log_int_value);
