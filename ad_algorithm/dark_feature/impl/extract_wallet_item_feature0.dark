using("ExtractWalletCapitalIdNoPrefix",
      FeatureType::ITEM,
      "adlog.item.ad_dsp_info.common_info_attr.WALLET_CAPITAL_ID:int64");
using("ExtractWalletCapitalProductNameNoPrefix",
      FeatureType::ITEM,
      "adlog.item.ad_dsp_info.common_info_attr.WALLET_CAPITAL_PRODUCT_NAME:int64");
using("ExtractWalletCapitalLoanRateListNoPrefix",
      FeatureType::ITEM,
      "adlog.item.ad_dsp_info.common_info_attr.WALLET_CAPITAL_LOAN_RATE_LIST:float_list",
      cast_to_int64_list);
using("ExtractWalletCapitalLoanPeriodListNoPrefix",
      FeatureType::ITEM,
      "adlog.item.ad_dsp_info.common_info_attr.WALLET_CAPITAL_LOAN_PERIOD_LIST:int64_list");
using("ExtractWalletCapitalAvgCreditNoPrefix",
      FeatureType::ITEM,
      "adlog.item.ad_dsp_info.common_info_attr.WALLET_CAPITAL_AVG_CREDIT_LIMIT:int64");
using("ExtractWalletCapitalAvgLoanApplyNoPrefix",
      FeatureType::ITEM,
      "adlog.item.ad_dsp_info.common_info_attr.WALLET_CAPITAL_AVG_LOAN_APPLY:int64");
using("ExtractWalletCapitalAvgLoanNoPrefix",
      FeatureType::ITEM,
      "adlog.item.ad_dsp_info.common_info_attr.WALLET_CAPITAL_AVG_LOAN:int64");
using("ExtractWalletCapitalAvgLoanDaysNoPrefix",
      FeatureType::ITEM,
      "adlog.item.ad_dsp_info.common_info_attr.WALLET_CAPITAL_AVG_LOAN_DAYS:int64");