using("ExtractUserUniverseActionSemanticId1",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5104320:int64_list",
  3,
  get_seq_with_max_len
);

using("ExtractUserUniverseActionSemanticId2",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5104319:int64_list",
  3,
  get_seq_with_max_len
);

using("ExtractUserUniverseActionSemanticId3",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5104318:int64_list",
  3,
  get_seq_with_max_len
);

using("ExtractUserUniverseActionSemanticIdEmbedding",
  FeatureType::DENSE_USER,
  "adlog.user_info.common_info_attr.key:5104321:float_list",
  128
);
