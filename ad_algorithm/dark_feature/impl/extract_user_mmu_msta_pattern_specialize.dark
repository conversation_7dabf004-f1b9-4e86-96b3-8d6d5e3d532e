using("ExtractUserSparseMmuToolAppIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.TOOLS_APP:int64_list",
      FeaturePrefix::TOOLS_APP
      );

using("ExtractUserSparseMmuMediaOtherIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.MEDIA_INFO_OTHER:int64_list",
      FeaturePrefix::MEDIA_INFO_OTHER
      );

using("ExtractUserSparseMmuEcomPlatIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.E_COMMERCE_PLATFORM:int64_list",
      FeaturePrefix::E_COMMERCE_PLATFORM
      );

using("ExtractUserSparseMmuAudioVisualIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AUDIO_VISUAL_APP:int64_list",
      FeaturePrefix::AUDIO_VISUAL_APP
      );

using("ExtractUserSparseMmuReadBookIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.READ_BOOK_APP:int64_list",
      FeaturePrefix::READ_BOOK_APP
      );

using("ExtractUserSparseMmuFoodOtherIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.FOOD_OTHER:int64_list",
      FeaturePrefix::FOOD_OTHER
      );

using("ExtractUserSparseMmuGameOtherIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.OTHER_GAME:int64_list",
      FeaturePrefix::OTHER_GAME
      );

using("ExtractUserSparseMmuFreeGameIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.FREE_GAME:int64_list",
      FeaturePrefix::FREE_GAME
      );

using("ExtractUserSparseMmuBeautyOtherIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.BEAUTY_OTHER:int64_list",
      FeaturePrefix::BEAUTY_OTHER
      );
      
using("ExtractUserSparseMmuBeautyGenIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.BEAUTY_GENERAL_COSMETICS:int64_list",
      FeaturePrefix::BEAUTY_GENERAL_COSMETICS
      );

using("ExtractUserSparseMmuL5MediaVisElemIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_LESS_5S_MEDIA_INFO_VISUAL_ELEMENT_FIRST5S:int64_list",
      FeaturePrefix::AD_PLAY_LESS_5S_MEDIA_INFO_VISUAL_ELEMENT_FIRST5S
      );

using("ExtractUserSparseMmuM5MediaVisElemList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_MORE_5S_MEDIA_INFO_VISUAL_ELEMENT_FIRST5S:int64_list",
      FeaturePrefix::AD_PLAY_MORE_5S_MEDIA_INFO_VISUAL_ELEMENT_FIRST5S
      );

using("ExtractUserSparseMmuL5GameVisElemIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_LESS_5S_GAME_VISUAL_ELEMENT_FIRST5S:int64_list",
      FeaturePrefix::AD_PLAY_LESS_5S_GAME_VISUAL_ELEMENT_FIRST5S
      );

using("ExtractUserSparseMmuM5GameVisElemIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_MORE_5S_GAME_VISUAL_ELEMENT_FIRST5S:int64_list",
      FeaturePrefix::AD_PLAY_MORE_5S_GAME_VISUAL_ELEMENT_FIRST5S
      );

using("ExtractUserSparseMmuL5EcomVisElemIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_LESS_5S_E_COMMERCE_PLATFORM_VISUAL_ELEMENT_FIRST5S:int64_list",
      FeaturePrefix::AD_PLAY_LESS_5S_E_COMMERCE_PLATFORM_VISUAL_ELEMENT_FIRST5S
      );

using("ExtractUserSparseMmuM5EcomVisElemIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_MORE_5S_E_COMMERCE_PLATFORM_VISUAL_ELEMENT_FIRST5S:int64_list",
      FeaturePrefix::AD_PLAY_MORE_5S_E_COMMERCE_PLATFORM_VISUAL_ELEMENT_FIRST5S
      );

using("ExtractUserSparseMmuL5MediaContIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_LESS_5S_MEDIA_INFO_CONTENT_EXPRESS_FIRST_5S:int64_list",
      FeaturePrefix::AD_PLAY_LESS_5S_MEDIA_INFO_CONTENT_EXPRESS_FIRST_5S
      );

using("ExtractUserSparseMmuM5MediaContIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_MORE_5S_MEDIA_INFO_CONTENT_EXPRESS_FIRST_5S:int64_list",
      FeaturePrefix::AD_PLAY_MORE_5S_MEDIA_INFO_CONTENT_EXPRESS_FIRST_5S
      );

using("ExtractUserSparseMmuL5GameContIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_LESS_5S_GAME_CONTENT_EXPRESS_FIRST_5S:int64_list",
      FeaturePrefix::AD_PLAY_LESS_5S_GAME_CONTENT_EXPRESS_FIRST_5S
      );

using("ExtractUserSparseMmuM5GameContIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_MORE_5S_GAME_CONTENT_EXPRESS_FIRST_5S:int64_list",
      FeaturePrefix::AD_PLAY_MORE_5S_GAME_CONTENT_EXPRESS_FIRST_5S
      );

using("ExtractUserSparseL5EcomContIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_LESS_5S_E_COMMERCE_PLATFORM_CONTENT_EXPRESS_FIRST_5S:int64_list",
      FeaturePrefix::AD_PLAY_LESS_5S_E_COMMERCE_PLATFORM_CONTENT_EXPRESS_FIRST_5S
      );

using("ExtractUserSparseM5EcomContIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_MORE_5S_E_COMMERCE_PLATFORM_CONTENT_EXPRESS_FIRST_5S:int64_list",
      FeaturePrefix::AD_PLAY_MORE_5S_E_COMMERCE_PLATFORM_CONTENT_EXPRESS_FIRST_5S
      );

using("ExtractUserSparseMmuM5AllVtypeIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_MORE_5S_ALL_VIDEO_TYPE_ACTION_TIME:int64_list",
      FeaturePrefix::AD_PLAY_MORE_5S_ALL_VIDEO_TYPE_ACTION_TIME
      );

using("ExtractUserSparseMmuM5MediaVtypeIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_MORE_5S_MEDIA_INFO_VIDEO_TYPE_ACTION_TIME:int64_list",
      FeaturePrefix::AD_PLAY_MORE_5S_MEDIA_INFO_VIDEO_TYPE_ACTION_TIME
      );

using("ExtractUserSparseMmuM5GameVtypeIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_MORE_5S_GAME_VIDEO_TYPE_ACTION_TIME:int64_list",
      FeaturePrefix::AD_PLAY_MORE_5S_GAME_VIDEO_TYPE_ACTION_TIME
      );

using("ExtractUserSparseMmuM5EcomVtypeIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PLAY_MORE_5S_E_COMMERCE_PLATFORM_VIDEO_TYPE_ACTION_TIME:int64_list",
      FeaturePrefix::AD_PLAY_MORE_5S_E_COMMERCE_PLATFORM_VIDEO_TYPE_ACTION_TIME
      );
      
using("ExtractUserSparseMmuClkNotActAllIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_NOT_ACTIVATE_ALL_VIDEO_TYPE_ACTION_TIME:int64_list",
      FeaturePrefix::CLICK_NOT_ACTIVATE_ALL_VIDEO_TYPE_ACTION_TIME
      );

using("ExtractUserSparseMmuClkNotActMediaIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_NOT_ACTIVATE_MEDIA_INFO_VIDEO_TYPE_ACTION_TIME:int64_list",
      FeaturePrefix::CLICK_NOT_ACTIVATE_MEDIA_INFO_VIDEO_TYPE_ACTION_TIME
      );

using("ExtractUserSparseMmuClkNotActEcomIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_NOT_ACTIVATE_E_COMMERCE_PLATFORM_VIDEO_TYPE_ACTION_TIME:int64_list",
      FeaturePrefix::CLICK_NOT_ACTIVATE_E_COMMERCE_PLATFORM_VIDEO_TYPE_ACTION_TIME
      );

using("ExtractUserSparseMmuClkNotActGameIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_NOT_ACTIVATE_GAME_VIDEO_TYPE_ACTION_TIME:int64_list",
      FeaturePrefix::CLICK_NOT_ACTIVATE_GAME_VIDEO_TYPE_ACTION_TIME
      );

using("ExtractUserSparseMmuClkAndActMediaIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CLICK_ACTIVATE_MEDIA_INFO_VIDEO_TYPE_ACTION_TIME:int64_list",
      FeaturePrefix::CLICK_ACTIVATE_MEDIA_INFO_VIDEO_TYPE_ACTION_TIME
      );