using("ExtractUserSparseR3AuthorList",
      FeatureType::USER, 
      "adlog.context.info_common_attr.USER_5R_R3_AUTHOR_LST:int64_list");
using("ExtractUserSparseR4AuthorList",
      FeatureType::USER, 
      "adlog.context.info_common_attr.USER_5R_R4_AUTHOR_LST:int64_list");
using("ExtractUserSparseR5AuthorList",
      FeatureType::USER, 
      "adlog.context.info_common_attr.USER_5R_R5_AUTHOR_LST:int64_list");

using("ExtractComineR3R4R5Flag",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.USER_5R_R3_AUTHOR_LST:int64_list",
      "adlog.item.ad_dsp_info.photo_info.author_info.id",
      target_author_in_author_list_or_not,
      "adlog.context.info_common_attr.USER_5R_R4_AUTHOR_LST:int64_list",
      "adlog.item.ad_dsp_info.photo_info.author_info.id",
      target_author_in_author_list_or_not,
      "adlog.context.info_common_attr.USER_5R_R5_AUTHOR_LST:int64_list",
      "adlog.item.ad_dsp_info.photo_info.author_info.id",
      target_author_in_author_list_or_not,
      merge_int64_list_3,
      combine_fans_tag
);

using("ExtractLiveAuthorBrandId",
      FeatureType::ITEM,
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64");
using("ExtractLiveAuthorIsFlagShip",
      FeatureType::ITEM,
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_FLAG_SHIP:int64");
using("ExtractLiveAuthorIsBlueV",
      FeatureType::ITEM,
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_IS_BLUEV:int64");

using("ExtractUser5RShowBrandLst",
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_SHOW_BRAND_LST:int64_list");
using("ExtractUser5RPvBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_PV_BRAND_LST:int64_list");
using("ExtractUser5RItemAddBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_ITEM_ADD_BRAND_LST:int64_list");
using("ExtractUser5RRepurchaseBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_REPURCHASE_BRAND_LST:int64_list");
using("ExtractUser5RPraiseBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_PRAISE_BRAND_LST:int64_list");
using("ExtractUser5ROrderSubmitBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_ORDER_SUBMIT_BRAND_LST:int64_list");
using("ExtractUser5RPlayed5SBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_PLAYED_5S_BRAND_LST:int64_list");
using("ExtractUser5RClkBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_CLK_BRAND_LST:int64_list");
using("ExtractUser5RInnerPlayBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_INNER_PLAY_BRAND_LST:int64_list");
using("ExtractUser5RLiveAudienceBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_LIVE_AUDIENCE_BRAND_LST:int64_list");
using("ExtractUser5RPlayDurationBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_LIVE_PLAY_DURATION_BRAND_LST:int64_list");
using("ExtractUser5RLiveRewardBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_LIVE_REWARD_BRAND_LST:int64_list");
using("ExtractUser5RLiveReservationBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_LIVE_RESERVATION_BRAND_LST:int64_list");
using("ExtractUser5RValidPlayBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_VALID_PLAY_BRAND_LST:int64_list");
using("ExtractUser5RCpnRcvBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_CPN_RCV_BRAND_LST:int64_list");
using("ExtractUser5RNewMemberBrandLst", 
      FeatureType::USER, 
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_NEW_MENBER_BRAND_LST:int64_list");


using("ExtractDenseCombineSparse5RActionCnt",
      FeatureType::DENSE_COMBINE,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_SHOW_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_SHOW_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_PV_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_PV_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_ITEM_ADD_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_ITEM_ADD_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_REPURCHASE_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_REPURCHASE_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_PRAISE_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_PRAISE_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_ORDER_SUBMIT_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_ORDER_SUBMIT_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_PLAYED_5S_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_PLAYED_5S_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_CLK_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_CLK_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_INNER_PLAY_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_INNER_PLAY_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_LIVE_AUDIENCE_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_LIVE_AUDIENCE_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_LIVE_PLAY_DURATION_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_LIVE_PLAY_DURATION_TIME_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_LIVE_REWARD_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_LIVE_REWARD_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_LIVE_RESERVATION_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_LIVE_RESERVATION_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_VALID_PLAY_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_VALID_PLAY_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_CPN_RCV_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_CPN_RCV_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_NEW_MENBER_BRAND_LST:int64_list",
      "adlog.user_info.common_info_attr.USER_5R_BEHAVIOR_NEW_MENBER_CNT_LST:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_5R_BRAND_ID:int64",
      cast_to_int64,
      100,
      get_brand_behaviour_cnt,
      10000,
      min,
      1,
      add,
      cast_to_float,
      log_e,
      cast_to_float,
      merge_float_list_with_limit,
      16);