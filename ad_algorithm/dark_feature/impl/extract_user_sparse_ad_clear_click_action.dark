// user click sequence (filter left slide)
// side info: photo_id, product_id_hash, second_industry_id_hash, aid, author_id
// time range: long_term, one_week, one_day, one_session
// outer&inner ad

using("ExtractUserSparseAdClearClickActionOneDayPhotoId",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60444:int64_list",
    "adlog.user_info.common_info_attr.key:60440:int64_list",
    "adlog.user_info.common_info_attr.key:61361:int64_list",
    "adlog.time",
    100,
    ********,
    clear_click_action_by_time_range);
    
using("ExtractUserSparseAdClearClickActionLongTermPhotoId",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60444:int64_list",
    "adlog.user_info.common_info_attr.key:61361:int64_list",
    100,
    clear_click_action);

using("ExtractUserSparseAdClearClickActionOneDayPackageName",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60449:int64_list",
    "adlog.user_info.common_info_attr.key:60440:int64_list",
    "adlog.user_info.common_info_attr.key:61361:int64_list",
    "adlog.time",
    100,
    ********,
    clear_click_action_by_time_range);

using("ExtractUserSparseAdClearClickActionLongTermPackageName",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60449:int64_list",
    "adlog.user_info.common_info_attr.key:61361:int64_list",
    100,
    clear_click_action);

using("ExtractUserSparseAdClearClickActionOneDayProductName",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60448:int64_list",
    "adlog.user_info.common_info_attr.key:60440:int64_list",
    "adlog.user_info.common_info_attr.key:61361:int64_list",
    "adlog.time",
    100,
    ********,
    clear_click_action_by_time_range);

using("ExtractUserSparseAdClearClickActionLongTermProductName",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60448:int64_list",
    "adlog.user_info.common_info_attr.key:61361:int64_list",
    100,
    clear_click_action);

using("ExtractUserSparseAdClearClickActionOneDayAccountId",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60447:int64_list",
    "adlog.user_info.common_info_attr.key:60440:int64_list",
    "adlog.user_info.common_info_attr.key:61361:int64_list",
    "adlog.time",
    100,
    ********,
    clear_click_action_by_time_range);

using("ExtractUserSparseAdClearClickActionLongTermAccountId",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60447:int64_list",
    "adlog.user_info.common_info_attr.key:61361:int64_list",
    100,
    clear_click_action);

using("ExtractUserSparseAdClearClickActionOneDayAuthorId",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60445:int64_list",
    "adlog.user_info.common_info_attr.key:60440:int64_list",
    "adlog.user_info.common_info_attr.key:61361:int64_list",
    "adlog.time",
    100,
    ********,
    clear_click_action_by_time_range);

using("ExtractUserSparseAdClearClickActionLongTermAuthorId",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60445:int64_list",
    "adlog.user_info.common_info_attr.key:61361:int64_list",
    100,
    clear_click_action);

using("ExtractUserSparseAdClearClickActionOneDayIndustryV3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60446:int64_list",
    "adlog.user_info.common_info_attr.key:60440:int64_list",
    "adlog.user_info.common_info_attr.key:61361:int64_list",
    "adlog.time",
    100,
    ********,
    clear_click_action_by_time_range);

using("ExtractUserSparseAdClearClickActionLongTermIndustryV3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:60446:int64_list",
    "adlog.user_info.common_info_attr.key:61361:int64_list",
    100,
    clear_click_action);

