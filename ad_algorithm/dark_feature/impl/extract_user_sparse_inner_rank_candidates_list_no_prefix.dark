using("ExtractUserSparseInnerRankCandidatesAccountIdsAllNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ACCOUNT_IDS:int64_list"
      );
using("ExtractUserSparseInnerRankCandidatesAuthorIdsAllNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_AUTHOR_IDS:int64_list"
      );
using("ExtractUserSparseInnerRankCandidatesAutoCpaBidsAllNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_CPA_BIDS:int64_list"
      );
using("ExtractUserSparseInnerRankCandidatesCampaignIdsAllNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_CAMPAIGN_IDS:int64_list"
      );
using("ExtractUserSparseInnerRankCandidatesCampaignTypesAllNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_CAMPAIGN_TYPES:int64_list"
      );
using("ExtractUserSparseInnerRankCandidatesCpaBidsAllNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_AUTO_CPA_BIDS:int64_list"
      );
using("ExtractUserSparseInnerRankCandidatesCreativeIdsAllNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_CREATIVES_IDS:int64_list"
      );
using("ExtractUserSparseInnerRankCandidatesCreativeTypesAllNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_CREATIVE_TYPES:int64_list"
      );
using("ExtractUserSparseInnerRankCandidatesLiveStreamIdsAllNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_LIVE_STREAM_IDS:int64_list"
      );
using("ExtractUserSparseInnerRankCandidatesOcpxActionTypesAllNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_OCPX_ACTION_TYPES:int64_list"
      );
using("ExtractUserSparseInnerRankCandidatesPhotoIdsAllNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_PHOTO_IDS:int64_list"
      );
using("ExtractUserSparseInnerRankCandidatesUnitIdsAllNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_UNIT_IDS:int64_list"
      );