using("ExtractUserUniverseClickActionSeqProductName",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_PRODUCT_NAME:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqIndustryTag",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_INDUSTRY_TAG:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqIsPlayable",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_IS_PLAYABLE:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqAdStyle",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_AD_STYLE:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqMediumCooperationMode",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_MEDIUM_COOPERATION_MODE:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqMaterialFeatureType",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_MATERIAL_FEATURE_TYPE:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqMediumAppId",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_MEDIUM_APP_ID:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqFirstIndustryName",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_FIRST_INDUSTRY_NAME:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqSecondIndustryName",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_SECOND_INDUSTRY_NAME:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqEndcardId",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_ENDCARD_ID:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqPlaycardId",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_PLAYCARD_ID:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqHour",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_HOUR:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqDateInterval",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_DATE_INTERVAL:int64_list",
  30,
  get_seq_with_max_len
);