import "teams/ad/ad_algorithm/dark_feature/impl/extract_multi_field_tmpl.dark"

// user sparse with prefix
class ExtractUserSparseTmpl<field: Field, prefix: FeaturePrefix> {
  set_feature_type(FeatureType::USER);

  fn Extract(log, pos, result) {
    v = get_field(field);
    add_feature_result(v, get_feature_func_, prefix, result);
  }
}

// user sparse no prefix
class ExtractUserSparseNoPrefixTmpl<field : Field> {
  set_feature_type(FeatureType::USER);

  fn Extract(log, pos, result) {
    v = get_field(field);
    add_feature_result(v, result);
  }
}

// user dense
class ExtractUserDenseTmpl<field : Field, total: int> {
  set_feature_type(FeatureType::DENSE_USER);

  fn Extract(log, pos, result) {
    v = get_field(field);
    add_feature_result(v, total, result);
  }
}

// read int or int list, cast as dense feature
class ExtractUserDenseCastTmpl<field : Field, total: int> {
  set_feature_type(FeatureType::DENSE_USER);

  fn Extract(log, pos, result) {
    v = get_field(field);
    float_v = cast_to_float(v);
    add_feature_result(float_v, total, result);
  }
}

using<field: Field>
    ("ExtractUserSparseLogNoPrefixTmpl",
    "ExtractUserSparse1FieldNoPrefixTmpl",
    field,
    log_e_cast_int);

using<field: Field, prefix: FeaturePrefix>
    ("ExtractUserSparseLogTmpl",
     "ExtractUserSparse1FieldTmpl",
     field,
     log_e_cast_int,
     prefix);

using<field: Field>
    ("ExtractUserDenseLogTmpl",
    "ExtractUserDense1FieldTmpl",
    field,
    log_e);
