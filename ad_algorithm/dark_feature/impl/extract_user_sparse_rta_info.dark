using("ExtractUActiveProductNumSegment",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100032:int64");
using("ExtractUAvgPayCoefficientSegment",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100029:int64");
using("ExtractUMaxPayCoefficientSegment",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100034:int64");
using("ExtractUActiveProductList3d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100031:int64_list");
using("ExtractUActiveProductList7d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100033:int64_list");
using("ExtractUActiveProductList15d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100030:int64_list");
using("ExtractUActiveProductListnd",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100036:int64_list");
using("ExtractUHighPayProductList1d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100035:int64_list");