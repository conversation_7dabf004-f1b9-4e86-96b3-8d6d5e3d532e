import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_sparse_realtime_match_detail_no_prefix_tmpl.dark"  

using("ExtractURealTimeActionGiftPriceAuthorIdMatchDetail",
      "ExtractCombine4FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_GIFT_PRICE_OBJECT_RELATIVE_ID_0_LIST:int64_list",
      "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_GIFT_PRICE_DATA_TIME_STAMP_LIST:int64_list",
      "adlog.item.ad_dsp_info.photo_info.author_info.id",
      "adlog.time",
      get_match_detail_fix);

using("ExtractURealTimeActionGiftPriceAuthorIdMatchDetailWithPrice",
      "ExtractCombine5FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_GIFT_PRICE_OBJECT_RELATIVE_ID_0_LIST:int64_list",
       "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_GIFT_PRICE_OBJECT_VALUE_0_LIST:int64_list",
       "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_GIFT_PRICE_DATA_TIME_STAMP_LIST:int64_list",
       "adlog.item.ad_dsp_info.photo_info.author_info.id",
       "adlog.time",
       get_match_detail_with_gift_price
       );

using("ExtractURealTimeActionJoinFansGroupAuthorIdMatchDetail",
      "ExtractCombine4FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_JOIN_FANS_GROUP_OBJECT_RELATIVE_ID_0_LIST:int64_list",
      "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_JOIN_FANS_GROUP_DATA_TIME_STAMP_LIST:int64_list",
      "adlog.item.ad_dsp_info.photo_info.author_info.id",
      "adlog.time",
      get_match_detail_fix);

using("ExtractURealTimeActionFollowAuthorIdMatchDetail",
      "ExtractCombine4FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.RECOACTIONLOG_ACTION_FOLLOW_OBJECTRELETIVE_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_FOLLOW_DATA_TIME_STAMP_LIST:int64_list",
      "adlog.item.ad_dsp_info.photo_info.author_info.id",
      "adlog.time",
      get_match_detail_fix);       