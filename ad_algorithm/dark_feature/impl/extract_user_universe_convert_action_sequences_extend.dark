using("ExtractUserUniverseConvertActionSeqProductNameExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_PRODUCT_NAME_CH:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_PRODUCT_NAME_CH:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_PRODUCT_NAME:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqIndustryTagExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_CRM_INDUSTRY_TYPE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_CRM_INDUSTRY_TYPE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_INDUSTRY_TAG:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqIsPlayableExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_IS_PLAYABLE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVNET_CONVERSIONIS_PLAYABLE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_IS_PLAYABLE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqAdStyleExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_AD_STYLE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_AD_STYLE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_AD_STYLE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqMediumCooperationModeExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_COOPERATION_MODE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_COOPERATION_MODE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_MEDIUM_COOPERATION_MODE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqMaterialFeatureTypeExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MATERIAL_FEATUR_TYPE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MATERIAL_FEATUR_TYPE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_MATERIAL_FEATURE_TYPE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqHourExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hour_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hour_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_HOUR:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqDateIntervalExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_date_diff_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_date_diff_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_DATE_INTERVAL:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);
