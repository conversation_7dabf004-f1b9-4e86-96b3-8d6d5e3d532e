
// imp
using("ExtractUserSparseAdImpressionExtendWangfuWithTime",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:0.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:0.list.action_timestamp",
    "adlog.time",
    "adlog.user_info.explore_long_term_ad_action.key:0.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:0.list.product_id_hash",
    get_wangfu_user_action_extend_with_time);
// click2
using("ExtractUserSparseAdClick2ExtendWangfuWithTime",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:2.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:2.list.action_timestamp",
    "adlog.time",
    "adlog.user_info.explore_long_term_ad_action.key:2.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:2.list.product_id_hash",
    get_wangfu_user_action_extend_with_time);
// p3s
using("ExtractUserSparseAdP3sExtendWangfuWithTime",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:3.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:3.list.action_timestamp",
    "adlog.time",
    "adlog.user_info.explore_long_term_ad_action.key:3.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:3.list.product_id_hash",
    get_wangfu_user_action_extend_with_time);
// p5s
using("ExtractUserSparseAdP5sExtendWangfuWithTime",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:4.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:4.list.action_timestamp",
    "adlog.time",
    "adlog.user_info.explore_long_term_ad_action.key:4.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:4.list.product_id_hash",
    get_wangfu_user_action_extend_with_time);
// play_end
using("ExtractUserSparseAdPedExtendWangfuWithTime",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:5.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:5.list.action_timestamp",
    "adlog.time",
    "adlog.user_info.explore_long_term_ad_action.key:5.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:5.list.product_id_hash",
    get_wangfu_user_action_extend_with_time);
// Conv
using("ExtractUserSparseAdConvExtendWangfuWithTime",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:6.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:6.list.action_timestamp",
    "adlog.time",
    "adlog.user_info.explore_long_term_ad_action.key:6.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:6.list.product_id_hash",
    get_wangfu_user_action_extend_with_time);
// Pay
using("ExtractUserSparseAdPayExtendWangfuWithTime",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:7.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:7.list.action_timestamp",
    "adlog.time",
    "adlog.user_info.explore_long_term_ad_action.key:7.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:7.list.product_id_hash",
    get_wangfu_user_action_extend_with_time);
// Form
using("ExtractUserSparseAdFormExtendWangfuWithTime",
    FeatureType::USER,
    "adlog.user_info.explore_long_term_ad_action.key:8.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:8.list.action_timestamp",
    "adlog.time",
    "adlog.user_info.explore_long_term_ad_action.key:8.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:8.list.product_id_hash",
    get_wangfu_user_action_extend_with_time);


