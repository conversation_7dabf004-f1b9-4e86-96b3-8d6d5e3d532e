using("ExtractUserUniverseAggActionSeqProductName",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100115:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqIndustryTag",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100118:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqIsPlayable",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100125:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqAdStyle",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100129:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqMediumCooperationMode",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100116:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqMaterialFeatureType",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100128:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqMediumAppId",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100122:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqFirstIndustryName",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100120:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqSecondIndustryName",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100124:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqEndcardId",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100121:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqPlaycardId",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100119:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqHour",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100127:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqDateInterval",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100123:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqLogMinuteInterval",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100123:int64_list",
  convert_date_diff_to_log2_min_diff,
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqActionType",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100126:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseAggActionSeqLastActionCnt",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5100117:int64_list",
  30,
  get_seq_with_max_len
);