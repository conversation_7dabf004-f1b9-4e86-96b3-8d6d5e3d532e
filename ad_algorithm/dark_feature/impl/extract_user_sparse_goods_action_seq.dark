using("ExtractUserSparseGoodsActionSeq",
      FeatureType::USER,
      "adlog.user_info.id",
      "adlog.time",
      256,
      20,
      14,
      "adlog.is_train",
      "adlog.ad_user_all_goods_action",
      get_gooods_action_seq
      );
using("ExtractUserDenseGoodsActionSeqInfo",
      FeatureType::DENSE_USER,
      "adlog.user_info.id",
      "adlog.time",
      256,
      20,
      14,
      "adlog.is_train",
      "adlog.ad_user_all_goods_action",
      get_gooods_action_seq_info,
      112
      );
