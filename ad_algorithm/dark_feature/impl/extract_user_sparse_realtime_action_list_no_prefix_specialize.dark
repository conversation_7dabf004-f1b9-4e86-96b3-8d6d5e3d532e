import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_sparse_realtime_action_list_no_prefix_tmpl.dark"  

using("ExtractUserRealtimeActionEcomActionJoinFansGroupAuthorId",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_JOIN_FANS_GROUP_OBJECT_RELATIVE_ID_0_LIST:int64_list",
       "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_JOIN_FANS_GROUP_DATA_TIME_STAMP_LIST:int64_list",
       "adlog.time",
       filter_action_by_time_diff);

using("ExtractUserRealtimeActionEcomActionFollowAuthorId",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.RECOACTIONLOG_ACTION_FOLLOW_OBJECTRELETIVE_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_FOLLOW_DATA_TIME_STAMP_LIST:int64_list",
       "adlog.time",
       filter_action_by_time_diff);

using("ExtractUserRealtimeActionEcomActionGiftPriceAuthorId",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_GIFT_PRICE_OBJECT_RELATIVE_ID_0_LIST:int64_list",
       "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_GIFT_PRICE_DATA_TIME_STAMP_LIST:int64_list",
       "adlog.time",
       filter_action_by_time_diff);  

using("ExtractUserRealtimeActionEcomActionGiftPriceValue",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_GIFT_PRICE_OBJECT_VALUE_0_LIST:int64_list",
       "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_GIFT_PRICE_DATA_TIME_STAMP_LIST:int64_list",
       "adlog.time",
       filter_action_by_time_diff);              

using("ExtractUserRealtimeActionEcomActionGiftPriceObjectId",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_GIFT_PRICE_OBJECT_ID_0_LIST:int64_list",
       "adlog.user_info.common_info_attr.RECO_ACTION_LOG_ACTION_GIFT_PRICE_DATA_TIME_STAMP_LIST:int64_list",
       "adlog.time",
       filter_action_by_time_diff);