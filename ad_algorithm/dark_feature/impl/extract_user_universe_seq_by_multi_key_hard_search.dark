using("ExtractUserSparseActionListWithMultiKeyHardSearch",
  FeatureType::COMBINE,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_INDUSTRY_TAG:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_IS_PLAYABLE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_AD_STYLE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_COOPERATION_MODE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_MATERIAL_FEATURE_TYPE:int64_list",
  "adlog.item.ad_dsp_info.common_info_attr.PHOTO_CRM_DEFINE_THIRD_INDUSTRY:string",
  "adlog.item.ad_dsp_info.common_info_attr.IS_PLAYABLE:int64",
  "adlog.context.info_common_attr.AD_STYLE:int64",
  "adlog.context.info_common_attr.COOPERATION_MODE:int64",
  "adlog.item.ad_dsp_info.common_info_attr.MATERIAL_FEATURE_TYPE:int64",
  seq_list_input_with_sparse_hard_search
);
using("ExtractUserDenseActionListWithMultiKeyHardSearch",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_INDUSTRY_TAG:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_IS_PLAYABLE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_AD_STYLE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_COOPERATION_MODE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_MATERIAL_FEATURE_TYPE:int64_list",
  "adlog.item.ad_dsp_info.common_info_attr.PHOTO_CRM_DEFINE_THIRD_INDUSTRY:string",
  "adlog.item.ad_dsp_info.common_info_attr.IS_PLAYABLE:int64",
  "adlog.context.info_common_attr.AD_STYLE:int64",
  "adlog.context.info_common_attr.COOPERATION_MODE:int64",
  "adlog.item.ad_dsp_info.common_info_attr.MATERIAL_FEATURE_TYPE:int64",
  seq_list_input_with_sparse_hard_search,
  cast_to_bool_list,
  cast_to_float_list,
  150
);

using("ExtractUserSparseActionListWithMultiKeyHardSearchPooling",
  FeatureType::COMBINE,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_INDUSTRY_TAG:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_IS_PLAYABLE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_AD_STYLE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_COOPERATION_MODE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_MATERIAL_FEATURE_TYPE:int64_list",
  "adlog.item.ad_dsp_info.common_info_attr.PHOTO_CRM_DEFINE_THIRD_INDUSTRY:string",
  "adlog.item.ad_dsp_info.common_info_attr.IS_PLAYABLE:int64",
  "adlog.context.info_common_attr.AD_STYLE:int64",
  "adlog.context.info_common_attr.COOPERATION_MODE:int64",
  "adlog.item.ad_dsp_info.common_info_attr.MATERIAL_FEATURE_TYPE:int64",
  seq_list_input_with_sparse_hard_search_filter
);