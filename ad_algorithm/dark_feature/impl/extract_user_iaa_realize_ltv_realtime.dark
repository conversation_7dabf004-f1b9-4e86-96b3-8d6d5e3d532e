// 66991: ProductNameHash, 
// 66990: CreativeId,
// 66989: TimeStamp30d,
using("ExtractUserMiniGameDspRealizeLtvLogLast1dRawList",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    1,
    "adlog.time",
    get_seq_with_date_limit_iaa_game);
    
using("ExtractUserMiniGameDspRealizeLtvLogLast1dSum",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    1,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_sum_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast1dAvg",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    1,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_avg_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast1dMin",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    1,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_min_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast1dMax",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    1,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_max_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeTimesLast1d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66990:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    1,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    get_size_of_vector_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast3dRawList",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    3,
    "adlog.time",
    get_seq_with_date_limit_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast3dSum",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    3,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_sum_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast3dAvg",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    3,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_avg_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast3dMin",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    3,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_min_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast3dMax",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    3,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_max_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeTimesLast3d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66990:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    3,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    get_size_of_vector_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast7dRawList",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    7,
    "adlog.time",
    get_seq_with_date_limit_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast7dSum",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    7,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_sum_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast7dAvg",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    7,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_avg_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast7dMin",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    7,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_min_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast7dMax",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    7,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_max_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeTimesLast7d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66990:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    7,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    get_size_of_vector_iaa_game);


using("ExtractUserMiniGameDspRealizeLtvLogLast14dRawList",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    14,
    "adlog.time",
    get_seq_with_date_limit_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast14dSum",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    14,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_sum_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast14dAvg",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    14,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_avg_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast14dMin",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    14,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_min_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast14dMax",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    14,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_max_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeTimesLast14d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66990:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    14,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    get_size_of_vector_iaa_game);


using("ExtractUserMiniGameDspRealizeLtvLogLast30dRawList",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    30,
    "adlog.time",
    get_seq_with_date_limit_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast30dSum",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    30,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_sum_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast30dAvg",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    30,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_avg_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast30dMin",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    30,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_min_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeLtvLogLast30dMax",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66993:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    30,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    1.5, 
    get_max_log_result_iaa_game);

using("ExtractUserMiniGameDspRealizeTimesLast30d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66990:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    30,
    "adlog.time",
    get_seq_with_date_limit_iaa_game,
    get_size_of_vector_iaa_game);


using("ExtractUserMiniGameDspRealizeProductNameLast1dRawList",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66991:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    1,
    "adlog.time",
    get_seq_with_date_limit_iaa_game);

using("ExtractUserMiniGameDspRealizeProductNameLast14dRawList",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66991:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    14,
    "adlog.time",
    get_seq_with_date_limit_iaa_game);

using("ExtractUserMiniGameDspRealizeProductNameLast30dRawList",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66991:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    30,
    "adlog.time",
    get_seq_with_date_limit_iaa_game);

using("ExtractUserMiniGameDspRealizeCreativeIdLast1dRawList",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66990:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    1,
    "adlog.time",
    get_seq_with_date_limit_iaa_game);

using("ExtractUserMiniGameDspRealizeCreativeIdLast14dRawList",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66990:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    14,
    "adlog.time",
    get_seq_with_date_limit_iaa_game);

using("ExtractUserMiniGameDspRealizeCreativeIdLast30dRawList",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:66990:int64_list",
    "adlog.user_info.common_info_attr.key:66989:int64_list",
    30,
    "adlog.time",
    get_seq_with_date_limit_iaa_game);