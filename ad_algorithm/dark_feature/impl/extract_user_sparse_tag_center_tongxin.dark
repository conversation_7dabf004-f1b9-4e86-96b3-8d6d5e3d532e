using("ExtractUserSparseTagTongxinUserName",
    FeatureType::USER,
    log,
    LabelFieldEnum::TONGXIN_TAG_NAME,
    MappingType::kTongxinTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagTongxinUserBrandName",
    FeatureType::USER,
    log,
    LabelFieldEnum::TONGXIN_TAG_BRAND_NAME,
    MappingType::kTongxinTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagTongxinUserFirstCategory",
    FeatureType::USER,
    log,
    LabelFieldEnum::TONGXIN_TAG_FIRST_CATEGORY,
    MappingType::kTongxinTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagTongxinUserSecondCategory",
    FeatureType::USER,
    log,
    LabelFieldEnum::TONGXIN_TAG_SECOND_CATEGORY,
    MappingType::kTongxinTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagTongxinUserThirdCategory",
    FeatureType::USER,
    log,
    LabelFieldEnum::TONGXIN_TAG_THIRD_CATEGORY,
    MappingType::kTongxinTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagTongxinUserPrice",
    FeatureType::USER,
    log,
    LabelFieldEnum::TONGXIN_TAG_PRICE,
    MappingType::kTongxinTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_int);

using("ExtractUserSparseTagTongxinUserFeature",
    FeatureType::USER,
    log,
    LabelFieldEnum::TONGXIN_TAG_FEATURE,
    MappingType::kTongxinTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagTongxinUserDirectionalTraffic",
    FeatureType::USER,
    log,
    LabelFieldEnum::TONGXIN_TAG_DIRECTIONALTRAFFIC,
    MappingType::kTongxinTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_float);

using("ExtractUserSparseTagTongxinUserGeneralTraffic",
    FeatureType::USER,
    log,
    LabelFieldEnum::TONGXIN_TAG_GENERALTRAFFIC,
    MappingType::kTongxinTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_float);

using("ExtractUserSparseTagTongxinItemName",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::TONGXIN_TAG_NAME,
    MappingType::kTongxinTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);

using("ExtractUserSparseTagTongxinItemBrandName",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::TONGXIN_TAG_BRAND_NAME,
    MappingType::kTongxinTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);

using("ExtractUserSparseTagTongxinItemFirstCategory",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::TONGXIN_TAG_FIRST_CATEGORY,
    MappingType::kTongxinTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);

using("ExtractUserSparseTagTongxinItemSecondCategory",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::TONGXIN_TAG_SECOND_CATEGORY,
    MappingType::kTongxinTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);

using("ExtractUserSparseTagTongxinItemThirdCategory",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::TONGXIN_TAG_THIRD_CATEGORY,
    MappingType::kTongxinTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);

using("ExtractUserSparseTagTongxinItemPrice",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::TONGXIN_TAG_PRICE,
    MappingType::kTongxinTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_int_item);

using("ExtractUserSparseTagTongxinItemFeature",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::TONGXIN_TAG_FEATURE,
    MappingType::kTongxinTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);

using("ExtractUserSparseTagTongxinItemDirectionalTraffic",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::TONGXIN_TAG_DIRECTIONALTRAFFIC,
    MappingType::kTongxinTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_float_item);

using("ExtractUserSparseTagTongxinItemGeneralTraffic",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::TONGXIN_TAG_GENERALTRAFFIC,
    MappingType::kTongxinTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_float_item);