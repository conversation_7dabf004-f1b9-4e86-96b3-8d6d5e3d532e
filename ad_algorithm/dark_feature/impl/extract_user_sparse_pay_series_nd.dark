using("ExtractUserSparsePayLockLessonIndexList7d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100773:int64_list",
    get_seq_pay_lock_category);

using("ExtractUserSparsePayLockLessonIndexList30d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100778:int64_list",
    get_seq_pay_lock_category);

using("ExtractUserSparseClickMinChargeList7d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100774:float_list",
    get_min_pay_price_category);

using("ExtractUserSparsePayMinChargeList7d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100775:float_list",
    get_min_pay_price_category);

using("ExtractUserSparseClickMinChargeList30d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100776:float_list",
    get_min_pay_price_category);

using("ExtractUserSparsePayMinChargeList30d",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100777:float_list",
    get_min_pay_price_category);


