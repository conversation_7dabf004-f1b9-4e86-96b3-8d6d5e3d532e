using("ExtractUserImIndirectTurnCntList30D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_TURN_CNT_LIST_30D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectIsCSendList30D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_IS_C_SEND_LIST_30D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectC2bSendMsgCntList30D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_C2B_SEND_MSG_CNT_LIST_30D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectIsBReplyList30D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_IS_B_REPLY_LIST_30D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectB2cAfterSendMsgCntList30D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_B2C_AFTER_SEND_MSG_CNT_LIST_30D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectTurnCntList15D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_TURN_CNT_LIST_15D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectIsCSendList15D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_IS_C_SEND_LIST_15D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectC2bSendMsgCntList15D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_C2B_SEND_MSG_CNT_LIST_15D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectIsBReplyList15D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_IS_B_REPLY_LIST_15D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectB2cAfterSendMsgCntList15D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_B2C_AFTER_SEND_MSG_CNT_LIST_15D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectTurnCntList7D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_TURN_CNT_LIST_7D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectIsCSendList7D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_IS_C_SEND_LIST_7D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectC2bSendMsgCntList7D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_C2B_SEND_MSG_CNT_LIST_7D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectIsBReplyList7D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_IS_B_REPLY_LIST_7D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectB2cAfterSendMsgCntList7D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_B2C_AFTER_SEND_MSG_CNT_LIST_7D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectTurnCntList1D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_TURN_CNT_LIST_1D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectIsCSendList1D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_IS_C_SEND_LIST_1D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectC2bSendMsgCntList1D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_C2B_SEND_MSG_CNT_LIST_1D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectIsBReplyList1D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_IS_B_REPLY_LIST_1D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);

using("ExtractUserImIndirectB2cAfterSendMsgCntList1D",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.USER_B2C_AFTER_SEND_MSG_CNT_LIST_1D:int64_list",
    "adlog.item.ad_dsp_info.photo_info.author_info.id",
    get_match_msg_cnt_by_author_id);
