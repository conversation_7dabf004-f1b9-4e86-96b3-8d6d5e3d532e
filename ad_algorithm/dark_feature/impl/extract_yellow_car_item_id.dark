using("ExtractYellowCarItemIdDense0",
    FeatureType::DENSE_ITEM,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_LIST:int64_list",
    0,
    get_item_id_from_yellow_car_list,
    4
    );

using("ExtractYellowCarItemIdDense1",
    FeatureType::DENSE_ITEM,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_LIST:int64_list",
    1,
    get_item_id_from_yellow_car_list,
    4
    );

using("ExtractYellowCarItemIdDense2",
    FeatureType::DENSE_ITEM,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_LIST:int64_list",
    2,
    get_item_id_from_yellow_car_list,
    4
    );

using("ExtractYellowCarItemIdDense3",
    FeatureType::DENSE_ITEM,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_LIST:int64_list",
    3,
    get_item_id_from_yellow_car_list,
    4
    );

using("ExtractYellowCarItemIdDense4",
    FeatureType::DENSE_ITEM,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_LIST:int64_list",
    4,
    get_item_id_from_yellow_car_list,
    4
    );

using("ExtractYellowCarItemIdDense5",
    FeatureType::DENSE_ITEM,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_YELLOWTROLLEY_ITEM_LIST:int64_list",
    5,
    get_item_id_from_yellow_car_list,
    4
    );

using("ExtractItemDenseExplainingItemId",
    FeatureType::DENSE_ITEM,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_EXPLAINING_ITEM:int64",
    split_int64_to_4_float,
    4);
  
using("ExtractAuthorSellHistoryItemIdDense0",
    FeatureType::DENSE_ITEM,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_MERCHANT_HISTORY_ITEM_ID:int64_list",
    0,
    get_item_id_from_yellow_car_list,
    4
    );

using("ExtractAuthorSellHistoryItemIdDense1",
    FeatureType::DENSE_ITEM,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_MERCHANT_HISTORY_ITEM_ID:int64_list",
    1,
    get_item_id_from_yellow_car_list,
    4
    );

using("ExtractAuthorSellHistoryItemIdDense2",
    FeatureType::DENSE_ITEM,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_MERCHANT_HISTORY_ITEM_ID:int64_list",
    2,
    get_item_id_from_yellow_car_list,
    4
    );

using("ExtractAuthorSellHistoryItemIdDense3",
    FeatureType::DENSE_ITEM,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_MERCHANT_HISTORY_ITEM_ID:int64_list",
    3,
    get_item_id_from_yellow_car_list,
    4
    );

using("ExtractAuthorSellHistoryItemIdDense4",
    FeatureType::DENSE_ITEM,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_MERCHANT_HISTORY_ITEM_ID:int64_list",
    4,
    get_item_id_from_yellow_car_list,
    4
    );