using("ExtractUserSparseRealtimeRtaDeliveryPhotoList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_DELIVERY_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_DELIVERY_TIMESTAMP:int64_list",
      "adlog.time",
      filter_action_by_time_diff);

using("ExtractUserSparseRealtimeRtaDeliverySpuList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_DELIVERY_SPU_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_DELIVERY_TIMESTAMP:int64_list",
      "adlog.time",
       filter_action_by_time_diff);

using("ExtractUserSparseRealtimeRtaImpressionPhotoList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_IMPRESSION_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_IMPRESSION_TIMESTAMP:int64_list",
      "adlog.time",
      filter_action_by_time_diff);

using("ExtractUserSparseRealtimeRtaImpressionSpuList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_IMPRESSION_SPU_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_IMPRESSION_TIMESTAMP:int64_list",
      "adlog.time",
       filter_action_by_time_diff);

using("ExtractUserSparseRealtimeRtaItemClickPhotoList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_ITEM_CLICK_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_ITEM_CLICK_TIMESTAMP:int64_list",
      "adlog.time",
      filter_action_by_time_diff);

using("ExtractUserSparseRealtimeRtaItemClickSpuList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_ITEM_CLICK_SPU_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_ITEM_CLICK_TIMESTAMP:int64_list",
      "adlog.time",
       filter_action_by_time_diff);