using("ExtractUserModelRankNameCombine",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.INNER_RANK_ITEM_MODEL_NAME:map_int64_string",
      "adlog.item.id:int64",
      get_value_from_map,
      "adlog.context.info_common_attr.AD_QUEUE_TYPE_V2:map_unit64_bool",
      "adlog.item.id:int64",
      get_value_from_map,
      "adlog.context.info_common_attr.INTERACTIVE_FORM:int64",
      "adlog.item.ad_dsp_info.unit.base.ocpc_action_type",
      0,
      0,
      "adlog.is_train",
      generate_model_name_combine
  );
  using("ExtractUserModelRankNameOcpxCombine",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.INNER_RANK_ITEM_MODEL_NAME:map_int64_string",
      "adlog.item.id:int64",
      get_value_from_map,
      "adlog.context.info_common_attr.AD_QUEUE_TYPE_V2:map_unit64_bool",
      "adlog.item.id:int64",
      get_value_from_map,
      "adlog.context.info_common_attr.INTERACTIVE_FORM:int64",
      "adlog.item.ad_dsp_info.unit.base.ocpc_action_type",
      0,
      1,
      "adlog.is_train",
      generate_model_name_combine
  );
  using("ExtractUserModelRankNameFormCombine",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.INNER_RANK_ITEM_MODEL_NAME:map_int64_string",
      "adlog.item.id:int64",
      get_value_from_map,
      "adlog.context.info_common_attr.AD_QUEUE_TYPE_V2:map_unit64_bool",
      "adlog.item.id:int64",
      get_value_from_map,
      "adlog.context.info_common_attr.INTERACTIVE_FORM:int64",
      "adlog.item.ad_dsp_info.unit.base.ocpc_action_type",
      0,
      2,
      "adlog.is_train",
      generate_model_name_combine
  );
  using("ExtractUserModelRankNameFormOcpxCombine",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.INNER_RANK_ITEM_MODEL_NAME:map_int64_string",
      "adlog.item.id:int64",
      get_value_from_map,
      "adlog.context.info_common_attr.AD_QUEUE_TYPE_V2:map_unit64_bool",
      "adlog.item.id:int64",
      get_value_from_map,
      "adlog.context.info_common_attr.INTERACTIVE_FORM:int64",
      "adlog.item.ad_dsp_info.unit.base.ocpc_action_type",
      0,
      3,
      "adlog.is_train",
      generate_model_name_combine
  );
  using("ExtractUserModelRankNameQueueCombine",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.INNER_RANK_ITEM_MODEL_NAME:map_int64_string",
      "adlog.item.id:int64",
      get_value_from_map,
      "adlog.context.info_common_attr.AD_QUEUE_TYPE_V2:map_unit64_bool",
      "adlog.item.id:int64",
      get_value_from_map,
      "adlog.context.info_common_attr.INTERACTIVE_FORM:int64",
      "adlog.item.ad_dsp_info.unit.base.ocpc_action_type",
      0,
      4,
      "adlog.is_train",
      generate_model_name_combine
  );
