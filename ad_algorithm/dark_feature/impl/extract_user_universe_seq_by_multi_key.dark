using("ExtractUserActionListWithMultiKey",
  FeatureType::DENSE_USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_INDUSTRY_TAG:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_IS_PLAYABLE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_AD_STYLE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_COOPERATION_MODE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_MATERIAL_FEATURE_TYPE:int64_list",
  seq_list_input_with_sparse_id_v1,
  150
);
using_seq("ExtractUserConvertActionListWithMultiValue",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_VALUE_PRODUCT_NAME:int64_list",
  30
);