using("ExtractUserUniverseClickActionSeqProductNameExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_PROUDUCT_NAME_CH:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_PRODUCT_NAME:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqIndustryTagExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_CRM_INDUSTRY_TYPE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_INDUSTRY_TAG:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqIsPlayableExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_IS_PLAYABLE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_IS_PLAYABLE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqAdStyleExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_AD_STYLE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_AD_STYLE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqMediumCooperationModeExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_COOPERATION_MODE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_MEDIUM_COOPERATION_MODE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);

using("ExtractUserUniverseClickActionSeqMaterialFeatureTypeExtend",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_MATERIAL_FEATUR_TYPE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_MATERIAL_FEATURE_TYPE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len
);
