using("ExtractSparseCombineLiveRealTimePayCategory2",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_ITEM_PACKAGE_0_ORDER_ITEM_ID_311_LIST:int_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_CLIENT_TAG_TIMESTAMP_LIST:int_list",
      "adlog.time",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_CATEGORY_LEVEL2:int_list",
      get_real_pay_combine_category);

using("ExtractSparseCombineLiveRealTimePayCategory3",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_ORDER_ITEM_X7_CATEGORY3_LIST:int_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_CLIENT_TAG_TIMESTAMP_LIST:int_list",
      "adlog.time",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_CATEGORY_LEVEL3:int_list",
      get_real_pay_combine_category);

using("ExtractSparseCombineLiveRealTimeViewCategory2",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_PACKAGE_0_CLICK_ITEM_ID_311_LIST:int_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TAG_TIMESTAMP_LIST:int_list",
      "adlog.time",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_CATEGORY_LEVEL2:int_list",
      get_real_view_combine_category);

using("ExtractSparseCombineLiveRealTimeViewCategory3",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_X7_CATEGORY3_LIST:int_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TAG_TIMESTAMP_LIST:int_list",
      "adlog.time",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_CATEGORY_LEVEL3:int_list",
      get_real_view_combine_category);

using("ExtractSparseCombineLiveRealTimeViewC2VolumeRank",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_PACKAGE_0_CLICK_ITEM_ID_311_LIST:int_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TAG_TIMESTAMP_LIST:int_list",
      "adlog.time",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_CATEGORY_LEVEL2:int_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_SV_RANK2:int_list",
      get_real_view_combine_cg_rank);

using("ExtractSparseCombineLiveRealTimeViewC2CxrRank",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_PACKAGE_0_CLICK_ITEM_ID_311_LIST:int_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TAG_TIMESTAMP_LIST:int_list",
      "adlog.time",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_CATEGORY_LEVEL2:int_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_SV_RANK2:int_list",
      get_real_view_combine_cg_rank);

using("ExtractSparseCombineLiveRealTimeViewC2PriceRank",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_PACKAGE_0_CLICK_ITEM_ID_311_LIST:int_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TAG_TIMESTAMP_LIST:int_list",
      "adlog.time",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_CATEGORY_LEVEL2:int_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_PRICE_RANK2:int_list",
      get_real_view_combine_cg_rank);


using("ExtractSparseCombineLiveRealTimeViewC2DiscountRank",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_X7_CATEGORY3_LIST:int_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TAG_TIMESTAMP_LIST:int_list",
      "adlog.time",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_CATEGORY_LEVEL3:int_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_DISCOUNT_RANK3:int_list",
      get_real_view_combine_cg_rank);

using("ExtractSparseCombineLiveRealTimeViewC3VolumeRank",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_X7_CATEGORY3_LIST:int_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TAG_TIMESTAMP_LIST:int_list",
      "adlog.time",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_CATEGORY_LEVEL3:int_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_SV_RANK3:int_list",
      get_real_view_combine_cg_rank);

using("ExtractSparseCombineLiveRealTimeViewC3CxrRank",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_X7_CATEGORY3_LIST:int_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TAG_TIMESTAMP_LIST:int_list",
      "adlog.time",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_CATEGORY_LEVEL3:int_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_SV_RANK3:int_list",
      get_real_view_combine_cg_rank);

using("ExtractSparseCombineLiveRealTimeViewC3PriceRank",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_X7_CATEGORY3_LIST:int_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TAG_TIMESTAMP_LIST:int_list",
      "adlog.time",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_CATEGORY_LEVEL3:int_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_PRICE_RANK3:int_list",
      get_real_view_combine_cg_rank);


using("ExtractSparseCombineLiveRealTimeViewC3DiscountRank",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_X7_CATEGORY3_LIST:int_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_CLIENT_TAG_TIMESTAMP_LIST:int_list",
      "adlog.time",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_CATEGORY_LEVEL3:int_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_AUTHOR_DISCOUNT_RANK3:int_list",
      get_real_view_combine_cg_rank);

using("ExtractItemDenseLivePlayDuration",
    FeatureType::DENSE_ITEM,
    "adlog.item.ad_dsp_info.live_info.common_info_attr.LIVE_REALTIME_DURATION:int64",
    cast_to_float,
    1);
