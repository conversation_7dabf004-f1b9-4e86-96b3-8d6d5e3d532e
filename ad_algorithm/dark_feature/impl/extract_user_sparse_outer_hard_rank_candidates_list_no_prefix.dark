using("ExtractUserSparseOuterHardRankCandidatesPhotoIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PHOTO_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      1,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseOuterHardRankCandidatesAuthorIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_AUTHOR_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      64, 
      1,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseOuterHardRankCandidatesIndustryIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      1,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseOuterHardRankCandidatesAccountIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_V3_ACCOUNT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseOuterHardRankCandidatesIndustryParentIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      32, 
      1,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseOuterHardRankCandidatesProductIdSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_INDUSTRY_PARENT_V3_PRODUCT_ID_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseOuterHardRankCandidatesOcpxActionTypeSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      32,
      29, 
      1,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
  );
using("ExtractUserSparseOuterHardRankCandidatesPackageNameSortByEnsembleNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_PRERANK_PCTRS_PRERANK_PCVRS_LIST:int64_list",
      "adlog.context.info_common_attr.RANK_CANDIDATES_ALL_IS_OUTER_PACKAGE_ID_OCPX_ACTION_TYPE_LIST:int64_list",
      "adlog.llsid",
      0,
      32, 
      1,
      0,
      1,
      50,
      generate_rank_candidates_list_feature
  );