using_seq("ExtractUserProfessionLevel1IdList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PROFESSION_LEVEL_1_ID_LIST:int64_list",
       100);
using_seq("ExtractUserProfessionLevel2IdList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PROFESSION_LEVEL_1_ID_LIST:int64_list",
       100);
using_seq("ExtractUserProfessionLevel3IdList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PROFESSION_LEVEL_1_ID_LIST:int64_list",
       100);