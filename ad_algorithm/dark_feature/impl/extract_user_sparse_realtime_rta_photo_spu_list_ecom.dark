  using("ExtractUserSparseRealtimeRtaDeliverySpuListEcom",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_DELIVERY_SPU_ID_LIST_ECOM:int64_list",
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_DELIVERY_TIMESTAMP:int64_list",
      "adlog.time",
       filter_action_by_time_diff);

using("ExtractUserSparseRealtimeRtaImpressionSpuListEcom",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_IMPRESSION_SPU_ID_LIST_ECOM:int64_list",
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_IMPRESSION_TIMESTAMP:int64_list",
      "adlog.time",
       filter_action_by_time_diff);

using("ExtractUserSparseRealtimeRtaItemClickSpuListEcom",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_ITEM_CLICK_SPU_ID_LIST_ECOM:int64_list",
      "adlog.user_info.common_info_attr.USER_REAL_TIME_RTA_ITEM_CLICK_TIMESTAMP:int64_list",
      "adlog.time",
       filter_action_by_time_diff);

using("ExtractUserSparseRealtimeAdxSpuListEcom",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_REAL_TIME_ADX_SPU_ID_ECOM:int64_list",
      "adlog.user_info.common_info_attr.USER_REAL_TIME_ADX_TIMESTAMP:int64_list",
      "adlog.time",
       filter_action_by_time_diff);