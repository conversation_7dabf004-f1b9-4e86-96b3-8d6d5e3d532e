using("ExtractUserSparseLocalLifeTradeVerifyAuthorList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.LOCAL_LIFE_USER_TRADE_VERIFY_ORDER_AUTHOR_ARRAY:int64_list",
      "adlog.user_info.common_info_attr.LOCAL_LIFE_USER_TRADE_VERIFY_ORDER_TIMESTAMP_ARRAY:int64_list",
      100,
      "adlog.time",
      5000,
      false,
      filter_action_by_time_diff_thresh);

using("ExtractUserSparseLocalLifeTradeAuthorList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.LOCAL_LIFE_USER_TRADE_PAY_ORDER_AUTHOR_ARRAY:int64_list",
      "adlog.user_info.common_info_attr.LOCAL_LIFE_USER_TRADE_PAY_ORDER_TIMESTAMP_ARRAY:int64_list",
      100,
      "adlog.time",
      5000,
      false,
      filter_action_by_time_diff_thresh);

using("ExtractUserSparseKsmpOrderPayAuthorList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.KSMP_AUTHOR_ID_ARRAY:int64_list",
      "adlog.user_info.common_info_attr.KSMP_ORDER_PAY_TIME_ARRAY:int64_list",
      100,
      "adlog.time",
      5000,
      false,
      filter_action_by_time_diff_thresh);

using("ExtractUserSparseEcomOrderPayAuthorList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.ECOM_REAL_SELLER_ID_180D_ARRAY:int64_list",
      "adlog.user_info.common_info_attr.ECOM_ORDER_PAY_TIME_180D_ARRAY:int64_list",
      100,
      "adlog.time",
      5000,
      false,
      filter_action_by_time_diff_thresh);

using("ExtractUserSparseEcomOrderPayWithCouponAuthorList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.CPN_REAL_SELLER_ID_180D_ARRAY:int64_list",
      "adlog.user_info.common_info_attr.CPN_PAY_VERIFY_TIME_180D_ARRAY:int64_list",
      100,
      "adlog.time",
      5000,
      false,
      filter_action_by_time_diff_thresh);