using("ExtractCombineUserDay7StayDurationByIndustryId",
    FeatureType::COMBINE,
    "adlog.user_info.common_info_attr.USER_DAY7_STAY_DURA_INDUS_ID_LIST:int64_list",
    "adlog.user_info.common_info_attr.USER_DAY7_STAY_DURA_INTVAL_LIST:int64_list",
    "adlog.item.ad_dsp_info.creative.base.industry_id_v3",
    get_feature_from_industry_lists_map);
using("ExtractCombineUserDay7StayCntByIndustryId",
    FeatureType::COMBINE,
    "adlog.user_info.common_info_attr.USER_DAY7_STAY_CNT_INDUS_ID_LIST:int64_list",
    "adlog.user_info.common_info_attr.USER_DAY7_STAY_CNT_INTVAL_LIST:int64_list",
    "adlog.item.ad_dsp_info.creative.base.industry_id_v3",
    get_feature_from_industry_lists_map);
using("ExtractCombineUserDay30StayDurationByIndustryId",
    FeatureType::COMBINE,
    "adlog.user_info.common_info_attr.USER_DAY30_STAY_DURA_INDUS_ID_LIST:int64_list",
    "adlog.user_info.common_info_attr.USER_DAY30_STAY_DURA_INTVAL_LIST:int64_list",
    "adlog.item.ad_dsp_info.creative.base.industry_id_v3",
    get_feature_from_industry_lists_map);
using("ExtractCombineUserDay30StayCntByIndustryId",
    FeatureType::COMBINE,
    "adlog.user_info.common_info_attr.USER_DAY30_STAY_CNT_INDUS_ID_LIST:int64_list",
    "adlog.user_info.common_info_attr.USER_DAY30_STAY_CNT_INTVAL_LIST:int64_list",
    "adlog.item.ad_dsp_info.creative.base.industry_id_v3",
    get_feature_from_industry_lists_map);