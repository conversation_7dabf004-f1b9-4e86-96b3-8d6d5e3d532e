using("ExtractCombineSparseLocalLifeDistance",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LOCAL_LIFE_LIVE_LIST:int64_list",
      "adlog.user_info.common_info_attr.USER_LOCAL_LIFE_LIVE_DISTANCE_LIST:float_list",
      cast_to_int64_list,
      "adlog.item.ad_dsp_info.live_info.id",
      cast_to_int64,
      200,
      get_brand_behaviour_cnt,
      10000,
      min);

using("ExtractCombineSparseLocalLifeDistanceWithClassId",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LOCAL_LIFE_LIVE_LIST:int64_list",
      "adlog.user_info.common_info_attr.USER_LOCAL_LIFE_LIVE_DISTANCE_LIST:float_list",
      cast_to_int64_list,
      "adlog.item.ad_dsp_info.live_info.id",
      cast_to_int64,
      200,
      get_brand_behaviour_cnt,
      10000,
      min,
      "adlog.item.ad_dsp_info.live_info.common_info_attr.LOCAL_LIFE_LIVE_FIRST_CLASS_ID:int64",
      combine);

using("ExtractCombineSparseLocalLifeDistanceWithAuthorId",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LOCAL_LIFE_LIVE_LIST:int64_list",
      "adlog.user_info.common_info_attr.USER_LOCAL_LIFE_LIVE_DISTANCE_LIST:float_list",
      cast_to_int64_list,
      "adlog.item.ad_dsp_info.live_info.id",
      cast_to_int64,
      200,
      get_brand_behaviour_cnt,
      10000,
      min,
      "adlog.item.ad_dsp_info.live_info.author_info.id",
      "adlog.item.ad_dsp_info.photo_info.author_info.id",
      get_first_non_zero,
      combine);      
