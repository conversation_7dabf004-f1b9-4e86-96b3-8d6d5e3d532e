using("ExtractUserMsgClkType",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.key:5103418:int64_list"
     );

using("ExtractUserMsgClkTypeXPhotoId",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.key:5103418:int64_list",
     "adlog.user_info.common_info_attr.key:5103417:int64_list",
     side_info_cross
     );

using("ExtractUserMsgClkTypeXAuthorId",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.key:5103418:int64_list",
     "adlog.user_info.common_info_attr.key:5103416:int64_list",
     side_info_cross
     );