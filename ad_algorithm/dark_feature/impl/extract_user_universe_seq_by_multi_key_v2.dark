using("ExtractUserSparseActionListWith<PERSON>ultiKey",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_INDUSTRY_TAG:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_IS_PLAYABLE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_AD_STYLE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_COOPERATION_MODE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_MATERIAL_FEATURE_TYPE:int64_list",
  seq_list_input_with_sparse_id_v1,
  cast_to_int64_list
);
using("ExtractUserDenseActionListWithMultiKeyWeight",
  FeatureType::DE<PERSON><PERSON>_USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_INDUSTRY_TAG:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_IS_PLAYABLE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_AD_STYLE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_COOPERATION_MODE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_MATERIAL_FEATURE_TYPE:int64_list",
  seq_list_input_with_sparse_id_v1,
  cast_to_bool_list,
  cast_to_float_list,
  150
);
using("ExtractUserSparseActionListWithMultiKeyPooling",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_INDUSTRY_TAG:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_IS_PLAYABLE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_AD_STYLE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_COOPERATION_MODE:int64_list",
  "adlog.user_info.common_info_attr.UNIVERSE_AD_ACTION_LIST_KEY_MATERIAL_FEATURE_TYPE:int64_list",
  seq_list_input_with_sparse_id_v1,
  cast_to_int64_list
);