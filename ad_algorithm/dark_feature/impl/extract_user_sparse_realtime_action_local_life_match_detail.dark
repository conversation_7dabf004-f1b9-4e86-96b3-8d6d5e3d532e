using("ExtractCombineKsmpMatchDetal",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.KSMP_AUTHOR_ID_ARRAY:int64_list",
       "adlog.user_info.common_info_attr.KSMP_OUT_ORDER_AMOUNT:int64_list",
       "adlog.user_info.common_info_attr.KSMP_ORDER_PAY_TIME_ARRAY:int64_list",
       timestamp_second_2_millisecond,
        "adlog.item.ad_dsp_info.live_info.author_info.id",
        "adlog.item.ad_dsp_info.photo_info.author_info.id",
        get_first_non_zero,
       "adlog.time",
       get_match_detail_with_gift_price
       );

using("ExtractCombineEcomOrderPayMatchDetal",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.ECOM_REAL_SELLER_ID_180D_ARRAY:int64_list",
       "adlog.user_info.common_info_attr.ECOM_OUT_ORDER_AMOUNT_180D:int64_list",
       "adlog.user_info.common_info_attr.ECOM_ORDER_PAY_TIME_180D_ARRAY:int64_list",
       timestamp_second_2_millisecond,
        "adlog.item.ad_dsp_info.live_info.author_info.id",
        "adlog.item.ad_dsp_info.photo_info.author_info.id",
        get_first_non_zero,
       "adlog.time",
       get_match_detail_with_gift_price
       );
using("ExtractCombineEcomOrderPayWithCouponMatchDetal",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.CPN_REAL_SELLER_ID_180D_ARRAY:int64_list",
       "adlog.user_info.common_info_attr.CPN_PAY_ORDER_AMOUNT_180D:int64_list",
       "adlog.user_info.common_info_attr.CPN_PAY_VERIFY_TIME_180D_ARRAY:int64_list",
       timestamp_second_2_millisecond,
        "adlog.item.ad_dsp_info.live_info.author_info.id",
        "adlog.item.ad_dsp_info.photo_info.author_info.id",
        get_first_non_zero,
       "adlog.time",
       get_match_detail_with_gift_price
       );    

using("ExtractCombineLocalOrderPayMatchDetal",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LOCAL_LIFE_USER_TRADE_PAY_ORDER_AUTHOR_ARRAY:int64_list",
       "adlog.user_info.common_info_attr.LOCAL_LIFE_USER_TRADE_PAY_ORDER_AMT_ARRAY:int64_list",
       "adlog.user_info.common_info_attr.LOCAL_LIFE_USER_TRADE_PAY_ORDER_TIMESTAMP_ARRAY:int64_list",
       timestamp_second_2_millisecond,
        "adlog.item.ad_dsp_info.live_info.author_info.id",
        "adlog.item.ad_dsp_info.photo_info.author_info.id",
        get_first_non_zero,
       "adlog.time",
       get_match_detail_with_gift_price
       );    

using("ExtractCombineLocalOrderPayVerifyMatchDetal",
      FeatureType::COMBINE,
      "adlog.user_info.common_info_attr.LOCAL_LIFE_USER_TRADE_VERIFY_ORDER_AUTHOR_ARRAY:int64_list",
       "adlog.user_info.common_info_attr.LOCAL_LIFE_USER_TRADE_VERIFY_ORDER_AMT_ARRAY:int64_list",
       "adlog.user_info.common_info_attr.LOCAL_LIFE_USER_TRADE_VERIFY_ORDER_TIMESTAMP_ARRAY:int64_list",
       timestamp_second_2_millisecond,
        "adlog.item.ad_dsp_info.live_info.author_info.id",
        "adlog.item.ad_dsp_info.photo_info.author_info.id",
        get_first_non_zero,
       "adlog.time",
       get_match_detail_with_gift_price
       );    
