using("ExtractUserAdT0ImpSpuMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:61408:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.GOODS_INFO_SPU_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6 
     );
using("ExtractUserAdT0ImpIndustryMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60536:int64_list",
      "adlog.item.ad_dsp_info.creative.base.industry_id_v3",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserAdT0ImpAccountMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60537:int64_list",
      "adlog.item.ad_dsp_info.creative.base.account_id",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserAdT0ImpAuthorMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60535:int64_list",
      "adlog.item.ad_dsp_info.photo_info.author_info.id",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );

using("ExtractUserAdT0PlayEndSpuMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:61404:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.GOODS_INFO_SPU_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserAdT0PlayEndIndustryMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60256:int64_list",
      "adlog.item.ad_dsp_info.creative.base.industry_id_v3",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserAdT0PlayEndAccountMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60257:int64_list",
      "adlog.item.ad_dsp_info.creative.base.account_id",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserAdT0PlayEndAuthorMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60255:int64_list",
      "adlog.item.ad_dsp_info.photo_info.author_info.id",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );

using("ExtractUserAdT0ClickSpuMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:61405:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.GOODS_INFO_SPU_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserAdT0ClickIndustryMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60446:int64_list",
      "adlog.item.ad_dsp_info.creative.base.industry_id_v3",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserAdT0ClickAccountMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60447:int64_list",
      "adlog.item.ad_dsp_info.creative.base.account_id",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserAdT0ClickAuthorMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60445:int64_list",
      "adlog.item.ad_dsp_info.photo_info.author_info.id",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );

using("ExtractUserAdT0GoodViewSpuMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_GOODS_VIEW_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:61407:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.GOODS_INFO_SPU_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserAdT0GoodViewIndustryMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_GOODS_VIEW_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60296:int64_list",
      "adlog.item.ad_dsp_info.creative.base.industry_id_v3",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserAdT0GoodViewAccountMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_GOODS_VIEW_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60297:int64_list",
      "adlog.item.ad_dsp_info.creative.base.account_id",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserAdT0GoodViewAuthorMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_GOODS_VIEW_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60295:int64_list",
      "adlog.item.ad_dsp_info.photo_info.author_info.id",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );

using("ExtractUserAdT0OrderpaySpuMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.EVENT_ORDER_PAIED_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:61406:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.GOODS_INFO_SPU_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserAdT0OrderpayIndustryMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.EVENT_ORDER_PAIED_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60506:int64_list",
      "adlog.item.ad_dsp_info.creative.base.industry_id_v3",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserAdT0OrderpayAccountMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.EVENT_ORDER_PAIED_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60507:int64_list",
      "adlog.item.ad_dsp_info.creative.base.account_id",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserAdT0OrderpayAuthorMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.EVENT_ORDER_PAIED_TIMESTAMP_LIST:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:60505:int64_list",
      "adlog.item.ad_dsp_info.photo_info.author_info.id",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );

using("ExtractUserRecoT0OrderSubmitSpuMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.key:65599:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:65601:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.GOODS_INFO_SPU_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserRecoT0OrderSubmitX7Cat2MatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.key:65599:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:65602:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.ECOM_GOODS_INFO_MMU_A_CATEGORY2_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserRecoT0OrderSubmitX7Cat3MatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.key:65599:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:65603:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.ECOM_GOODS_INFO_MMU_A_CATEGORY3_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserRecoT0OrderSubmitX7Cat4MatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.key:65599:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:65604:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.ECOM_GOODS_INFO_MMU_A_CATEGORY4_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );

using("ExtractUserRecoT0OrderpaySpuMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.key:65424:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:65428:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.GOODS_INFO_SPU_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserRecoT0OrderpayX7Cat2MatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.key:65424:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:65606:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.ECOM_GOODS_INFO_MMU_A_CATEGORY2_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserRecoT0OrderpayX7Cat3MatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.key:65424:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:65429:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.ECOM_GOODS_INFO_MMU_A_CATEGORY3_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserRecoT0OrderpayX7Cat4MatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.key:65424:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:65607:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.ECOM_GOODS_INFO_MMU_A_CATEGORY4_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );

using("ExtractUserRecoT0GoodViewSpuMatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.key:65421:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:65422:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.GOODS_INFO_SPU_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserRecoT0GoodViewX7Cat2MatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.key:65421:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:65612:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.ECOM_GOODS_INFO_MMU_A_CATEGORY2_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserRecoT0GoodViewX7Cat3MatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.key:65421:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:65423:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.ECOM_GOODS_INFO_MMU_A_CATEGORY3_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
using("ExtractUserRecoT0GoodViewX7Cat4MatchPleDense",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.key:65421:int64_list",
      "adlog.time",
      "adlog.user_info.common_info_attr.key:65613:int64_list",
      "adlog.item.ad_dsp_info.common_info_attr.ECOM_GOODS_INFO_MMU_A_CATEGORY4_ID:int64",
      get_24h_match_list_size,
      get_24h_other_bins,
      get_dense_ple_helper,
      6
     );
