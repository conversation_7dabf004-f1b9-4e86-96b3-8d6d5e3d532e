using("ExtractUserSparseGestureTypeCross",
    FeatureType::USER,
    "adlog.context.info_common_attr.GESTURE_TYPE:int64",
    "adlog.context.pos_id:int64",
    "adlog.context.info_common_attr.INTERACTIVE_FORM:int64",
    "adlog.user_info.ad_user_info.platform:string",
    get_client_info_combine);

using("ExtractUserSparseClientVolumeCross",
    FeatureType::USER,
    "adlog.context.info_common_attr.CLIENT_VOLUME_V2:int64",
    "adlog.context.pos_id:int64",
    "adlog.context.info_common_attr.INTERACTIVE_FORM:int64",
    "adlog.user_info.ad_user_info.platform:string",
    get_client_info_combine);