using("ExtractUserSparseRankRtaAccountIdList",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_RTA_ACCOUNT_ID_LIST:int64_list"
      );

using("ExtractUserSparseRankRtaPhotoIdList",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_RTA_PHOTO_ID_LIST:int64_list"
      );

using("ExtractUserSparseRankRtaIndustryIdList",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_RTA_INDUSTRY_V3_ID_LIST:int64_list"
      );

using("ExtractUserSparseRankRtaProductIdList",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_RTA_CITY_PRODUCT_ID_LIST:int64_list"
      );

using("ExtractUserSparseRankRtaOcpxActionList",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_RTA_OCPX_ACTION_TYPE_LIST:int64_list"
      );

using("ExtractUserSparseRankRtaBidMaxLevel",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_RTA_RTA_BID_LIST:int64_list",
      2,
      calc_bid_level);

using("ExtractUserSparseRankRtaBidMinLevel",
      FeatureType::USER,
      "adlog.context.info_common_attr.RANK_RTA_RTA_BID_LIST:int64_list",
      1,
      calc_bid_level);

