using("ExtractUserSparseAdcuTextGB1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    0,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB2",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    50,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    100,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB4",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    150,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB5",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    200,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB6",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    250,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    300,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB8",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    350,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB9",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    400,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB10",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    450,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB11",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    500,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB12",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    550,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB13",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    600,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB14",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    650,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB15",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    700,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB16",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    750,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB17",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    800,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB18",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    850,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB19",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    900,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB20",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    950,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB21",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    1000,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB22",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    1050,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB23",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    1100,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB24",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    1150,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB25",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    1200,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB26",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    1250,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB27",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    1300,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB28",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    1350,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB29",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    1400,
    50,
    get_adcu_slice);
using("ExtractUserSparseAdcuTextGB30",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_B_TOKEN_LIST:int_list",
    1450,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC1",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    0,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC2",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    50,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC3",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    100,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC4",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    150,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC5",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    200,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC6",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    250,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC7",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    300,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC8",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    350,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC9",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    400,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC10",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    450,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC11",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    500,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC12",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    550,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC13",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    600,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC14",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    650,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC15",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    700,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC16",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    750,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC17",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    800,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC18",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    850,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC19",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    900,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC20",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    950,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC21",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    1000,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC22",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    1050,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC23",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    1100,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC24",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    1150,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC25",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    1200,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC26",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    1250,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC27",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    1300,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC28",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    1350,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC29",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    1400,
    50,
    get_adcu_slice);

using("ExtractUserSparseAdcuTextGC30",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.ADCU_RECO_C_TOKEN_LIST:int_list",
    1450,
    50,
    get_adcu_slice);

