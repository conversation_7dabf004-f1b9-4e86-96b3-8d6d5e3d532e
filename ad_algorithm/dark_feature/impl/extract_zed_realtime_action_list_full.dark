using("ExtractUserRealtimeAppDeepEvoFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_APPSTORE_DEEPLINK_EVOCATION_SUCCESS_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_APPSTORE_DEEPLINK_EVOCATION_SUCCESS_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_AD_APPSTORE_DEEPLINK_EVOCATION_SUCCESS_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_APPSTORE_DEEPLINK_EVOCATION_SUCCESS_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_APPSTORE_DEEPLINK_EVOCATION_SUCCESS_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
    get_realtime_action_list_full);

using("ExtractUserRealtimeDownloadStartFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_ITEM_DOWNLOAD_STARTED_TIMESTAMP_LIST:int64_list",     
     "adlog.user_info.common_info_attr.AD_ITEM_DOWNLOAD_STARTED_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_AD_ITEM_DOWNLOAD_STARTED_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_DOWNLOAD_STARTED_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_DOWNLOAD_STARTED_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
    get_realtime_action_list_full);

using("ExtractUserRealtimeInvokeFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.EVENT_APP_INVOKED_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_APP_INVOKED_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_APP_INVOKED_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_APP_INVOKED_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_APP_INVOKED_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
    get_realtime_action_list_full);

using("ExtractUserRealtimeConvFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_CONVERSION_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_CONVERSION_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_CONVERSION_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_CONVERSION_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
    get_realtime_action_list_full);

using("ExtractUserRealtimePayFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.EVENT_PAY_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_PAY_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_PAY_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_PAY_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_PAY_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
    get_realtime_action_list_full);

using("ExtractUserRealtimeNextstayFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_NEXTDAY_STAY_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_NEXTDAY_STAY_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_NEXTDAY_STAY_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_NEXTDAY_STAY_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_NEXTDAY_STAY_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
    get_realtime_action_list_full);

// 添加 综合电商过滤 逻辑 for imp、click、pxr、conv、invoke、pay、nextstay
using("ExtractUserRealtimeCompItemimpFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
     1032,
     get_industry_filter_realtime_action_list_full);

using("ExtractUserRealtimeCompItemclickFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_CLICK_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_CLICK_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_CLICK_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
     1032,
     get_industry_filter_realtime_action_list_full);

using("ExtractUserRealtimeCompP3sFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_PLAYED_3S_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
     1032,
     get_industry_filter_realtime_action_list_full);

using("ExtractUserRealtimeCompPedFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_PLAYED_END_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
     1032,
     get_industry_filter_realtime_action_list_full);

using("ExtractUserRealtimeCompAppDeepEvoFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_APPSTORE_DEEPLINK_EVOCATION_SUCCESS_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_APPSTORE_DEEPLINK_EVOCATION_SUCCESS_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_AD_APPSTORE_DEEPLINK_EVOCATION_SUCCESS_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_APPSTORE_DEEPLINK_EVOCATION_SUCCESS_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_APPSTORE_DEEPLINK_EVOCATION_SUCCESS_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
     1032,
    get_industry_filter_realtime_action_list_full);

using("ExtractUserRealtimeCompDownloadStartFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_ITEM_DOWNLOAD_STARTED_TIMESTAMP_LIST:int64_list",     
     "adlog.user_info.common_info_attr.AD_ITEM_DOWNLOAD_STARTED_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_AD_ITEM_DOWNLOAD_STARTED_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_DOWNLOAD_STARTED_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_DOWNLOAD_STARTED_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
     1032,
    get_industry_filter_realtime_action_list_full);

using("ExtractUserRealtimeCompInvokeFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.EVENT_APP_INVOKED_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_APP_INVOKED_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_APP_INVOKED_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_APP_INVOKED_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_APP_INVOKED_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
     1032,
    get_industry_filter_realtime_action_list_full);

using("ExtractUserRealtimeCompConvFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_CONVERSION_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_CONVERSION_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_CONVERSION_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_CONVERSION_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
     1032,
    get_industry_filter_realtime_action_list_full);

using("ExtractUserRealtimeCompPayFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.EVENT_PAY_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_PAY_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_PAY_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_PAY_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.EVENT_PAY_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
     1032,
    get_industry_filter_realtime_action_list_full);

using("ExtractUserRealtimeCompNextstayFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_NEXTDAY_STAY_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_NEXTDAY_STAY_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_NEXTDAY_STAY_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_NEXTDAY_STAY_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_EVENT_NEXTDAY_STAY_PRODUCT_NAME_LIST:int64_list",
     "adlog.time",
     1032,
     get_industry_filter_realtime_action_list_full);

// 添加 ocpc_action_type 过滤行为序列的逻辑，目前只有 4 个 imp、click、p3r、ped 
using("ExtractUserRealtimeConvOcpcItemImpFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_PRODUCT_NAME_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_OCPC_ACTION_LIST:int64_list",
     "adlog.time",
     180,
     **********,
     get_ocpc_filter_realtime_action_list_full);

using("ExtractUserRealtimeConvOcpcItemclickFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_CLICK_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_CLICK_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_CLICK_PRODUCT_NAME_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_CLICK_OCPC_ACTION_TYPE_LIST:int64_list",
     "adlog.time",
     180,
     **********,
     get_ocpc_filter_realtime_action_list_full);

using("ExtractUserRealtimeConvOcpcP3sFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_PLAYED_3S_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_PRODUCT_NAME_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_OCPC_ACTION_LIST:int64_list",
     "adlog.time",
     180,
     **********,
     get_ocpc_filter_realtime_action_list_full);

using("ExtractUserRealtimeConvOcpcPedFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_PLAYED_END_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_PRODUCT_NAME_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_OCPC_ACTION_TYPE_LIST:int64_list",
     "adlog.time",
     180,
     **********,
     get_ocpc_filter_realtime_action_list_full);

using("ExtractUserRealtimeInvokeOcpcItemImpFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_PRODUCT_NAME_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_OCPC_ACTION_LIST:int64_list",
     "adlog.time",
     324,
     **********,
     get_ocpc_filter_realtime_action_list_full);

using("ExtractUserRealtimeInvokeOcpcItemclickFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_CLICK_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_CLICK_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_CLICK_PRODUCT_NAME_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_ITEM_CLICK_OCPC_ACTION_TYPE_LIST:int64_list",
     "adlog.time",
     324,
     **********,
     get_ocpc_filter_realtime_action_list_full);

using("ExtractUserRealtimeInvokeOcpcP3sFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_PLAYED_3S_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_PRODUCT_NAME_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_OCPC_ACTION_LIST:int64_list",
     "adlog.time",
     324,
     **********,
     get_ocpc_filter_realtime_action_list_full);

using("ExtractUserRealtimeInvokeOcpcPedFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_TIMESTAMP_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_PHOTO_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.ADLOGFULL_AD_PHOTO_PLAYED_END_ACCOUNT_ID_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_INDUSTRY_ID_V3_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_PRODUCT_NAME_LIST:int64_list",
     "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_OCPC_ACTION_TYPE_LIST:int64_list",
     "adlog.time",
     324,
     **********,
     get_ocpc_filter_realtime_action_list_full);


// add kaipin realtime action_list full for itemclick | p3s | playend | invoke ， 缺少 photo_id
using("ExtractUserRealtimeKpItemclickFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.KP_AD_ITEM_CLICK_TIMESTAMP:int64_list",
     "adlog.user_info.common_info_attr.KP_AD_ITEM_CLICK_OCPC_ACTION_TYPE:int64_list",
     "adlog.user_info.common_info_attr.KP_AD_ITEM_CLICK_ACCOUNT_ID:int64_list",
     "adlog.user_info.common_info_attr.KP_AD_ITEM_CLICK_INDUSTRY_V3_NAME:int64_list",
     "adlog.user_info.common_info_attr.KP_AD_ITEM_CLICK_PRODUCT_NAME:int64_list",
     "adlog.time",
    get_realtime_action_list_full);

using("ExtractUserRealtimeKpP3sFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.KP_AD_PHOTO_PLAYED_3S_TIMESTAMP:int64_list",
     "adlog.user_info.common_info_attr.KP_AD_PHOTO_PLAYED_3S_OCPC_ACTION_TYPE:int64_list",
     "adlog.user_info.common_info_attr.KP_AD_PHOTO_PLAYED_3S_ACCOUNT_ID:int64_list",
     "adlog.user_info.common_info_attr.KP_AD_PHOTO_PLAYED_3S_INDUSTRY_V3_NAME:int64_list",
     "adlog.user_info.common_info_attr.KP_AD_PHOTO_PLAYED_3S_PRODUCT_NAME:int64_list",
     "adlog.time",
    get_realtime_action_list_full);

using("ExtractUserRealtimeKpPedFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.KP_AD_PHOTO_PLAYED_END_TIMESTAMP:int64_list",
     "adlog.user_info.common_info_attr.KP_AD_PHOTO_PLAYED_END_OCPC_ACTION_TYPE:int64_list",
     "adlog.user_info.common_info_attr.KP_AD_PHOTO_PLAYED_END_ACCOUNT_ID:int64_list",
     "adlog.user_info.common_info_attr.KP_AD_PHOTO_PLAYED_END_INDUSTRY_V3_NAME:int64_list",
     "adlog.user_info.common_info_attr.KP_AD_PHOTO_PLAYED_END_PRODUCT_NAME:int64_list",
     "adlog.time",
    get_realtime_action_list_full);

using("ExtractUserRealtimeKpInvokeFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.KP_EVENT_APP_INVOKED_TIMESTAMP:int64_list",
     "adlog.user_info.common_info_attr.KP_EVENT_APP_INVOKED_OCPC_ACTION_TYPE:int64_list",
     "adlog.user_info.common_info_attr.KP_EVENT_APP_INVOKED_ACCOUNT_ID:int64_list",
     "adlog.user_info.common_info_attr.KP_EVENT_APP_INVOKED_INDUSTRY_V3_NAME:int64_list",
     "adlog.user_info.common_info_attr.KP_EVENT_APP_INVOKED_PRODUCT_NAME:int64_list",
     "adlog.time",
    get_realtime_action_list_full);

// use all invoke ocpc grep action_list for play3s | playend | itemclick 
using("ExtractUserRealtimeHdOcpcItemclickFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_ITEM_CLICK_TIMESTAMP:int64_list",
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_ITEM_CLICK_PHOTO_ID:int64_list",
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_ITEM_CLICK_ACCOUNT_ID:int64_list",
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_ITEM_CLICK_INDUSTRY_V3_NAME:int64_list",
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_ITEM_CLICK_PRODUCT_NAME:int64_list",
     "adlog.time",
    get_realtime_action_list_full);

using("ExtractUserRealtimeHdOcpcP3sFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_PHOTO_PLAYED_3S_TIMESTAMP:int64_list",
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_PHOTO_PLAYED_3S_PHOTO_ID:int64_list",
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_PHOTO_PLAYED_3S_ACCOUNT_ID:int64_list",
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_PHOTO_PLAYED_3S_INDUSTRY_V3_NAME:int64_list",
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_PHOTO_PLAYED_3S_PRODUCT_NAME:int64_list",
     "adlog.time",
    get_realtime_action_list_full);

using("ExtractUserRealtimeHdOcpcPedFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_PHOTO_PLAYED_END_TIMESTAMP:int64_list",
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_PHOTO_PLAYED_END_PHOTO_ID:int64_list",
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_PHOTO_PLAYED_END_ACCOUNT_ID:int64_list",
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_PHOTO_PLAYED_END_INDUSTRY_V3_NAME:int64_list",
     "adlog.user_info.common_info_attr.OCPC_INVOKE_ACTION_AD_PHOTO_PLAYED_END_PRODUCT_NAME:int64_list",
     "adlog.time",
    get_realtime_action_list_full);