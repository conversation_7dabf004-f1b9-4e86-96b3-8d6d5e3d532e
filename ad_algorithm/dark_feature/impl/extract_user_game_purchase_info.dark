using_seq("ExtractUserGameProductNameList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PRODUCT_NAME_LIST:int64_list",
       100);
using_seq("ExtractUserGamePurchaseAmt30DList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_AMT_30D_LIST:int64_list",
       100);
using_seq("ExtractUserGamePurchaseAmt60DList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_AMT_60D_LIST:int64_list",
       100);
using_seq("ExtractUserGamePurchaseAmt90DList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_AMT_90D_LIST:int64_list",
       100);
using_seq("ExtractUserGamePurchaseAmt180DList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_AMT_180D_LIST:int64_list",
       100);
using_seq("ExtractUserGamePurchaseCnt30DList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_CNT_30D_LIST:int64_list",
       100);
using_seq("ExtractUserGamePurchaseCnt60DList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_CNT_60D_LIST:int64_list",
       100);
using_seq("ExtractUserGamePurchaseCnt90DList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_CNT_90D_LIST:int64_list",
       100);
using_seq("ExtractUserGamePurchaseCnt180DList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_CNT_180D_LIST:int64_list",
       100);
using("ExtractUserGamePurchaseAmt30D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_AMT_30D:int64");
using("ExtractUserGamePurchaseAmt60D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_AMT_60D:int64");
using("ExtractUserGamePurchaseAmt90D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_AMT_90D:int64");
using("ExtractUserGamePurchaseAmt180D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_AMT_180D:int64");
using("ExtractUserGamePurchaseCnt30D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_CNT_30D:int64");
using("ExtractUserGamePurchaseCnt60D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_CNT_60D:int64");
using("ExtractUserGamePurchaseCnt90D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_CNT_90D:int64");
using("ExtractUserGamePurchaseCnt180D",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_GAME_PURCHASE_CNT_180D:int64");

