import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_sparse_ad_action_extend_fangchan_tmpl.dark";

// imp
using("ExtractUserSparseAdImpressionExtendFangchan",
    "ExtractUserSparseAdActionExtendFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.explore_long_term_ad_action.key:0.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:0.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:0.list.product_id_hash",
    30);
// click2
using("ExtractUserSparseAdClick2ExtendFangchan",
    "ExtractUserSparseAdActionExtendFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.explore_long_term_ad_action.key:2.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:2.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:2.list.product_id_hash",
    30);
// p3s
using("ExtractUserSparseAdP3sExtendFangchan",
    "ExtractUserSparseAdActionExtendFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.explore_long_term_ad_action.key:3.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:3.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:3.list.product_id_hash",
    30);
// p5s
using("ExtractUserSparseAdP5sExtendFangchan",
    "ExtractUserSparseAdActionExtendFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.explore_long_term_ad_action.key:4.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:4.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:4.list.product_id_hash",
    30);
// play_end
using("ExtractUserSparseAdPedExtendFangchan",
    "ExtractUserSparseAdActionExtendFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.explore_long_term_ad_action.key:5.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:5.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:5.list.product_id_hash",
    30);
// Conv
using("ExtractUserSparseAdConvExtendFangchan",
    "ExtractUserSparseAdActionExtendFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.explore_long_term_ad_action.key:6.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:6.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:6.list.product_id_hash",
    30);
// Pay
using("ExtractUserSparseAdPayExtendFangchan",
    "ExtractUserSparseAdActionExtendFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.explore_long_term_ad_action.key:7.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:7.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:7.list.product_id_hash",
    30);
// Form
using("ExtractUserSparseAdFormExtendFangchan",
    "ExtractUserSparseAdActionExtendFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.explore_long_term_ad_action.key:8.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:8.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:8.list.product_id_hash",
    30);
// Download Completed
using("ExtractUserSparseAdDownloadExtendFangchan",
    "ExtractUserSparseAdActionExtendFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.explore_long_term_ad_action.key:31.list.second_industry_id_hash",
    "adlog.user_info.explore_long_term_ad_action.key:31.list.photo_id",
    "adlog.user_info.explore_long_term_ad_action.key:31.list.product_id_hash",
    30);


// imp
using("ExtractUserSparseAdImpressionFangchan",
    "ExtractUserSparseAdActionFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.ad_dsp_action_detail.key:0.list.industry_id",
    "adlog.user_info.ad_dsp_action_detail.key:0.list.photo_id",
    "adlog.user_info.ad_dsp_action_detail.key:0.list.campaign_id",
    30);
// click2
using("ExtractUserSparseAdClick2Fangchan",
    "ExtractUserSparseAdActionFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.ad_dsp_action_detail.key:2.list.industry_id",
    "adlog.user_info.ad_dsp_action_detail.key:2.list.photo_id",
    "adlog.user_info.ad_dsp_action_detail.key:2.list.campaign_id",
    30);
// p3s
using("ExtractUserSparseAdP3sFangchan",
    "ExtractUserSparseAdActionFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.ad_dsp_action_detail.key:3.list.industry_id",
    "adlog.user_info.ad_dsp_action_detail.key:3.list.photo_id",
    "adlog.user_info.ad_dsp_action_detail.key:3.list.campaign_id",
    30);
// p5s
using("ExtractUserSparseAdP5sFangchan",
    "ExtractUserSparseAdActionFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.ad_dsp_action_detail.key:4.list.industry_id",
    "adlog.user_info.ad_dsp_action_detail.key:4.list.photo_id",
    "adlog.user_info.ad_dsp_action_detail.key:4.list.campaign_id",
    30);
// play_end
using("ExtractUserSparseAdPedFangchan",
    "ExtractUserSparseAdActionFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.ad_dsp_action_detail.key:5.list.industry_id",
    "adlog.user_info.ad_dsp_action_detail.key:5.list.photo_id",
    "adlog.user_info.ad_dsp_action_detail.key:5.list.campaign_id",
    30);
// Conv
using("ExtractUserSparseAdConvFangchan",
    "ExtractUserSparseAdActionFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.ad_dsp_action_detail.key:6.list.industry_id",
    "adlog.user_info.ad_dsp_action_detail.key:6.list.photo_id",
    "adlog.user_info.ad_dsp_action_detail.key:6.list.campaign_id",
    30);
// Pay
using("ExtractUserSparseAdPayFangchan",
    "ExtractUserSparseAdActionFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.ad_dsp_action_detail.key:7.list.industry_id",
    "adlog.user_info.ad_dsp_action_detail.key:7.list.photo_id",
    "adlog.user_info.ad_dsp_action_detail.key:7.list.campaign_id",
    30);
// Form
using("ExtractUserSparseAdFormFangchan",
    "ExtractUserSparseAdActionFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.ad_dsp_action_detail.key:8.list.industry_id",
    "adlog.user_info.ad_dsp_action_detail.key:8.list.photo_id",
    "adlog.user_info.ad_dsp_action_detail.key:8.list.campaign_id",
    30);
// Download Completed
using("ExtractUserSparseAdDownloadFangchan",
    "ExtractUserSparseAdActionFangchanMultiFieldNoPrefixTmpl",
    "adlog.user_info.ad_dsp_action_detail.key:31.list.industry_id",
    "adlog.user_info.ad_dsp_action_detail.key:31.list.photo_id",
    "adlog.user_info.ad_dsp_action_detail.key:31.list.campaign_id",
    30);


