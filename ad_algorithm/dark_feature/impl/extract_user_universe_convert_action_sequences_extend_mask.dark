using("ExtractUserUniverseConvertActionSeqIndustryTagExtendMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_CRM_INDUSTRY_TYPE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_CRM_INDUSTRY_TYPE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_INDUSTRY_TAG:int64_list",
  10,
  30,
  merge_two_seq_with_max_len,
  "adlog.item.ad_dsp_info.common_info_attr.PHOTO_CRM_DEFINE_THIRD_INDUSTRY:string",
  30,
  hard_search_seq_with_single_key,
  30
);

using("ExtractUserUniverseConvertActionSeqIsPlayableExtendMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_IS_PLAYABLE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVNET_CONVERSIONIS_PLAYABLE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_IS_PLAYABLE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len,
  "adlog.item.ad_dsp_info.common_info_attr.IS_PLAYABLE:int64",
  30,
  hard_search_seq_with_single_key,
  30
);

using("ExtractUserUniverseConvertActionSeqAdStyleExtendMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_AD_STYLE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_AD_STYLE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_AD_STYLE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len,
  "adlog.context.info_common_attr.AD_STYLE:int64",
  30,
  hard_search_seq_with_single_key,
  30
);

using("ExtractUserUniverseConvertActionSeqMediumCooperationModeExtendMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_COOPERATION_MODE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_COOPERATION_MODE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_MEDIUM_COOPERATION_MODE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len,
  "adlog.context.info_common_attr.COOPERATION_MODE:int64",
  30,
  hard_search_seq_with_single_key,
  30
);

using("ExtractUserUniverseConvertActionSeqMaterialFeatureTypeExtendMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MATERIAL_FEATUR_TYPE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MATERIAL_FEATUR_TYPE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_MATERIAL_FEATURE_TYPE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len,
  "adlog.item.ad_dsp_info.common_info_attr.MATERIAL_FEATURE_TYPE:int64",
  30,
  hard_search_seq_with_single_key,
  30
);
