using("ExtractUserUniverseValidActionSemanticId",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5104668:int64_list",
  "adlog.user_info.common_info_attr.key:5104667:int64",
  process_action_semantic_id_list
);

using("ExtractUserUniverseValidActionNum",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5104667:int64",
  0,
  value_or
);

using("ExtractUserUniverseActionSequenceSemanticId",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.key:5104669:int64_list",
  3,
  get_seq_with_max_len
);

using("ExtractUserUniverseActionSequenceEmbedding",
  FeatureType::DENSE_USER,
  "adlog.user_info.common_info_attr.key:5104666:float_list",
  64
);
