import "teams/ad/ad_algorithm/dark_feature/impl/extract_multi_field_tmpl.dark"

using("ExtractSparseUserLiveTotalCost",
      "ExtractUserSparse4FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_GMV:float_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_ORDER_PRICE:float_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_DISCOUNT_FEE:float_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_ORDER_CNT:int64_list",
      get_user_live_total_cost);

using("ExtractSparseUserLiveDiscountRate",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_ORDER_PRICE:float_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_DISCOUNT_FEE:float_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_ORDER_CNT:int64_list",
      get_user_live_discount_rate);

using("ExtractSparseUserLiveDiscountRatio",
      "ExtractUserSparse1FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_DISCOUNT_FEE:float_list",
      get_live_discount_ratio);

using("ExtractSparseItemLiveDiscountRatio",
      "ExtractItemSparse1FieldNoPrefixTmpl",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_PURCHASE_DISCOUNT_FEE:float_list",
      get_live_discount_ratio);

using("ExtractSparseUserLiveAvgPrice",
      "ExtractUserSparse4FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_GMV:float_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_ORDER_PRICE:float_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_DISCOUNT_FEE:float_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_ORDER_CNT:int64_list",
      get_user_live_avg_price);

using("ExtractSparseItemLiveHistoryAvgPrice",
      "ExtractItemSparse3FieldNoPrefixTmpl",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_PURCHASE_GMV:float_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_PURCHASE_ORDER_CNT:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_PURCHASE_KEY_CNT:int64_list",
      get_live_history_avg_price);

using("ExtractSparseItemLiveRealtimeAvgPrice",
      "ExtractItemSparse3FieldNoPrefixTmpl",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_REALTIME_PURCHASE_GMV:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_REALTIME_PURCHASE_ORDER_PRICE:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_REALTIME_PURCHASE_ORDER_CNT:int64_list",
      get_live_realtime_avg_price);

using("ExtractDenseItemLiveRealtimeTop5Price",
      "ExtractItemDense4FieldTmpl",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_REALTIME_PURCHASE_GMV:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_REALTIME_PURCHASE_ORDER_PRICE:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_REALTIME_PURCHASE_ORDER_CNT:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_REALTIME_PURCHASE_TIMESTAMP_Flag:int64_list",
      get_live_top5_price,
      160);

using("ExtractSparseCombineLivePriceComm",
      "ExtractCombine2FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_GMV:float_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_PURCHASE_GMV:float_list",
      get_live_combine_price_comm);

using("ExtractSparseCombineLiveC2PriceComm",
      "ExtractCombine4FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_GMV:float_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_X7_CATE2:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_PURCHASE_GMV:float_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_PURCHASE_X7_CATE2:int64_list",
      get_live_combine_ind_price_comm);

using("ExtractSparseCombineLiveC3PriceComm",
      "ExtractCombine4FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_GMV:float_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_X7_CATE3:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_PURCHASE_GMV:float_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_PURCHASE_X7_CATE3:int64_list",
      get_live_combine_ind_price_comm);

using("ExtractSparseCombineLiveRealtimePriceComm",
      "ExtractCombine4FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_GMV:float_list",
      "adlog.user_info.common_info_attr.USER_HISTORY_PURCHASE_X7_CATE2:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_REALTIME_PURCHASE_GMV:int64_list",
      "adlog.item.ad_dsp_info.live_info.common_info_attr.AUTHOR_HISTORY_REALTIME_PURCHASE_X7_CATE2:int64_list",
      get_live_combine_realtime_price_comm);
