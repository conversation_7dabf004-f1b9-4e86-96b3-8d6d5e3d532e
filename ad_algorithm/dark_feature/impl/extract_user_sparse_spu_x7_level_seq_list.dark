using("ExtractUserSparseClickSpuList",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100057:int64_list",
    0,
    seq_list_completion_sparse_int64_kconf_seq);

using("ExtractUserSparseClickX7Level1List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100058:int64_list",
    0,
    seq_list_completion_sparse_int64_kconf_seq);

using("ExtractUserSparseClickX7Level2List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100060:int64_list",
    0,
    seq_list_completion_sparse_int64_kconf_seq);

using("ExtractUserSparseClickX7Level3List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100059:int64_list",
    0,
    seq_list_completion_sparse_int64_kconf_seq);

using("ExtractUserSparsePedSpuList",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100064:int64_list",
    0,
    seq_list_completion_sparse_int64_kconf_seq);

using("ExtractUserSparsePedX7Level1List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100061:int64_list",
    0,
    seq_list_completion_sparse_int64_kconf_seq);

using("ExtractUserSparsePedX7Level2List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100062:int64_list",
    0,
    seq_list_completion_sparse_int64_kconf_seq);

using("ExtractUserSparsePedX7Level3List",
    FeatureType::USER,
    "adlog.user_info.common_info_attr.key:5100063:int64_list",
    0,
    seq_list_completion_sparse_int64_kconf_seq);
