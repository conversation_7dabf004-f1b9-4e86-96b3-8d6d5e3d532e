class ExtractUserSparseRealtimeEcomActionNoPrefixTmpl<action_field : str,
                                                      ts_field: str,
                                                      max_len: int> {
  set_feature_type(FeatureType::COMBINE);

  fn Extract(log, pos, result) {
    author_id = get_adlog_live_or_photo_author_id(log, pos);
    adlog_time = get_field("adlog.time");
    user_action_content_list = get_field(action_field);
    user_action_timestamp_list = get_field(ts_field);
    enums = get_match_detail(user_action_content_list, user_action_timestamp_list,
                             author_id, max_len, adlog_time);

    add_feature_result(enums, result);
  }
}
