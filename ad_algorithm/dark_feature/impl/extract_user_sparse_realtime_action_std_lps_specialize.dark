using("ExtractUserAdRealtimeActionStdLpsAuthorId",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_AUTHOR_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_LIVE_ROOM_PATTERN_NEW_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_AUTHOR_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_LIVE_ROOM_PATTERN_LIST:int64_list",
       "adlog.time",
       RoomPatternType::STD_LIVE_ROOM_PATTERN,
       get_specific_room_pattern_action_lst_merge);
using("ExtractUserAdRealtimeActionStdLpsLiveId",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_LIVE_STREAM_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_LIVE_ROOM_PATTERN_NEW_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_LIVE_STREAM_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_LIVE_ROOM_PATTERN_LIST:int64_list",
       "adlog.time",
       RoomPatternType::STD_LIVE_ROOM_PATTERN,
       get_specific_room_pattern_action_lst_merge);
using("ExtractUserAdRealtimeActionStdLpsInteractiveForm",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_INTERACTIVE_FORM_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_LIVE_ROOM_PATTERN_NEW_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_INTERACTIVE_FORM_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_LIVE_ROOM_PATTERN_LIST:int64_list",
       "adlog.time",
       RoomPatternType::STD_LIVE_ROOM_PATTERN,
       get_specific_room_pattern_action_lst_merge);    
using("ExtractUserAdRealtimeActionStdLpsItemType",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_ITEM_TYPE_NEW_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_LIVE_ROOM_PATTERN_NEW_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_ITEM_TYPE_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_LIVE_ROOM_PATTERN_LIST:int64_list",
       "adlog.time",
       RoomPatternType::STD_LIVE_ROOM_PATTERN,
       get_specific_room_pattern_action_lst_merge);                     