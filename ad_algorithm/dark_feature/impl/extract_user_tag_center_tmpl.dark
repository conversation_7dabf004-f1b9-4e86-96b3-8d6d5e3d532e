// user sparse tag center with 2 pos_type
class ExtractUserSparseTagCenterTmpl2<pos_type1: KeyPosType,
                                     pos_type2: KeyPosType,
                                     prefix: FeaturePrefix> {
  set_feature_type(FeatureType::USER);

  fn Extract(log, pos, result) {
    v = tag_center_ind5(log, pos, pos_type1, pos_type2);
    add_feature_result(v, get_feature_func_, prefix, result);
  }
}

// user sparse tag center with 3 pos_type
class ExtractUserSparseTagCenterTmpl3<pos_type1: KeyPosType,
                                     pos_type2: KeyPosType,
                                     pos_type3: KeyPosType,
                                     prefix: FeaturePrefix> {
  set_feature_type(FeatureType::USER);

  fn Extract(log, pos, result) {
    v = tag_center_ind5(log, pos, pos_type1, pos_type2, pos_type3);
    add_feature_result(v, get_feature_func_, prefix, result);
  }
}

// user sparse tag center with 2 pos_type
class ExtractUserSparseTagCenterProductTagTmpl2<pos_type1: KeyPosType,
                                     pos_type2: KeyPosType,
                                     prefix: FeaturePrefix,
                                     field_name: IndustryV5FieldName> {
  set_feature_type(FeatureType::USER);

  fn Extract(log, pos, result) {
    v = tag_center_product_tag(log, pos, field_name, pos_type1, pos_type2);
    add_feature_result(v, get_feature_func_, prefix, result);
  }
}
// user sparse tag center with 3 pos_type
class ExtractUserSparseTagCenterProductTagTmpl3<pos_type1: KeyPosType,
                                     pos_type2: KeyPosType,
                                     pos_type3: KeyPosType,
                                     prefix: FeaturePrefix,
                                     field_name: IndustryV5FieldName> {
  set_feature_type(FeatureType::USER);

  fn Extract(log, pos, result) {
    v = tag_center_product_tag(log, pos, field_name, pos_type1, pos_type2, pos_type3);
    add_feature_result(v, get_feature_func_, prefix, result);
  }
}
