using("ExtractUserRealtimeActionAdImpNoStdlpsAuthorId",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_AUTHOR_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_LIVE_ROOM_PATTERN_NEW_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_AUTHOR_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_LIVE_ROOM_PATTERN_LIST:int64_list",
       "adlog.time",
       RoomPatternType::SIMPLE_LIVE_ROOM_PATTERN,
       get_specific_room_pattern_action_lst_merge,
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_IMPRESSION_AUTHOR_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_IMPRESSION_TIMESTAMP_LIST:int64_list",
       "adlog.time",
       filter_action_by_time_diff,
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_IMPRESSION_AUTHOR_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_IMPRESSION_TIMESTAMP_LIST:int64_list",
       "adlog.time",
       filter_action_by_time_diff,
       merge_int64_list_2,
       merge_int64_list_2,
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_AUTHOR_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_LIVE_ROOM_PATTERN_NEW_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_AUTHOR_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_LIVE_ROOM_PATTERN_LIST:int64_list",
       "adlog.time",
       RoomPatternType::STD_LIVE_ROOM_PATTERN,
       get_specific_room_pattern_action_lst_merge,
       get_action_a_no_action_b_list);

using("ExtractUserRealtimeActionAdImpNoStdlpsLiveId",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_LIVE_STREAM_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_LIVE_ROOM_PATTERN_NEW_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_LIVE_STREAM_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_LIVE_ROOM_PATTERN_LIST:int64_list",
       "adlog.time",
       RoomPatternType::SIMPLE_LIVE_ROOM_PATTERN,
       get_specific_room_pattern_action_lst_merge,
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_IMPRESSION_LIVE_STREAM_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_IMPRESSION_TIMESTAMP_LIST:int64_list",
       "adlog.time",
       filter_action_by_time_diff,
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_IMPRESSION_LIVE_STREAM_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_IMPRESSION_TIMESTAMP_LIST:int64_list",
       "adlog.time",
       filter_action_by_time_diff,
       merge_int64_list_2,
       merge_int64_list_2,
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_LIVE_STREAM_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_STARTED_LIVE_ROOM_PATTERN_NEW_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_LIVE_STREAM_ID_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_TIMESTAMP_LIST:int64_list",
       "adlog.user_info.common_info_attr.FANSTOPLOGFULL_AD_LIVE_PLAYED_STARED_LIVE_ROOM_PATTERN_LIST:int64_list",
       "adlog.time",
       RoomPatternType::STD_LIVE_ROOM_PATTERN,
       get_specific_room_pattern_action_lst_merge,
       get_action_a_no_action_b_list);            