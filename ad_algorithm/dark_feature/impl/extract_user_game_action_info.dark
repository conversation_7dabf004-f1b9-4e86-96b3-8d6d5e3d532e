using_seq("ExtractUserGameClickProductNameList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_CLICK_PRODUCT_NAME_LIST:int64_list",
       100);
using_seq("ExtractUserGameConvertionProductNameList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_CONVERSION_PRODUCT_NAME_LIST:int64_list",
       100);
using_seq("ExtractUserGamePayProductNameList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_PAY_PRODUCT_NAME_LIST:int64_list",
       100);
using_seq("ExtractUserGameNagetiveProductNameList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.USER_NAGETIVE_PRODUCT_NAME_LIST:int64_list",
       100);
using_seq("ExtractUserGameLivePlay7DDurationList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.LIVE_NEW_GMAE_PLAY_7D_DURATION:int64_list",
       100);
using_seq("ExtractUserGameSubcaregoryYear1List",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.GAME_SUBCAREGORY_YEAR1_LIST:int64_list",
       100);
using_seq("ExtractUserGamePhotoPlay7DDurationList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.PHOTO_NEW_GAME_PLAY_7D_DURATION:int64_list",
       100);
using_seq("ExtractUserGameRealactons3DDurationList",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.NEW_GAME_REALACTONS_3D_DURATION:int64_list",
       100);
using_seq("ExtractUserGameCurCareL2List",
       FeatureType::USER,
       "adlog.user_info.common_info_attr.CUR_CARE_L2_LIST:int64_list",
       100);