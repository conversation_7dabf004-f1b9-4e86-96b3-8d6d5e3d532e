using("ExtractUserRealtimeLiveMergePosidCombineJumpClickPhotoIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_JUMP_CLICK_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_JUMP_CLICK_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_JUMP_CLICK_EVENT_SERVER_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_JUMP_CLICK_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_JUMP_CLICK_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_JUMP_CLICK_EVENT_SERVER_TIMESTAMP_LIST:int64_list",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_merge);

using("ExtractUserRealtimeLiveMergePosidCombineJumpClickAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_JUMP_CLICK_AUTHOR_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_JUMP_CLICK_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_JUMP_CLICK_EVENT_SERVER_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_JUMP_CLICK_AUTHOR_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_JUMP_CLICK_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_JUMP_CLICK_EVENT_SERVER_TIMESTAMP_LIST:int64_list",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_merge);

using("ExtractUserRealtimeLiveMergePosidCombinePlayedMeanPhotoIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_MEAN_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_PLAYED_MEAN_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_MEAN_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_PLAYED_MEAN_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_PLAYED_MEAN_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_PLAYED_MEAN_EVENT_SERVER_TIMESTAMP_LIST:int64_list",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_merge);

using("ExtractUserRealtimeLiveMergePosidCombinePlayedMeanAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_MEAN_AUTHOR_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_PLAYED_MEAN_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_MEAN_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_PLAYED_MEAN_AUTHOR_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_PLAYED_MEAN_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_PLAYED_MEAN_EVENT_SERVER_TIMESTAMP_LIST:int64_list",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_merge);

using("ExtractUserRealtimeLiveMergePosidCombineToProfilePhotoIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_TO_PROFILE_PHOTO_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_TO_PROFILE_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_TO_PROFILE_EVENT_SERVER_TIMESTAMP_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_TO_PROFILE_PHOTO_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_TO_PROFILE_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_TO_PROFILE_EVENT_SERVER_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_merge);

using("ExtractUserRealtimeLiveMergePosidCombineToProfileAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_TO_PROFILE_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_TO_PROFILE_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_TO_PROFILE_EVENT_SERVER_TIMESTAMP_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_TO_PROFILE_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_TO_PROFILE_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_TO_PROFILE_EVENT_SERVER_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_merge);

using("ExtractUserRealtimeLiveMergePosidCombineLiveClickAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_LIVE_CLICK_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_CLICK_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LIVE_CLICK_TIMESTAMP_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_CLICK_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_CLICK_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_CLICK_EVENT_SERVER_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_merge);

using("ExtractUserRealtimeLiveMergePosidCombinePlayedMedianAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_MEDIAN_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_PLAYED_MEDIAN_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.ADLOGFULL_AD_LIVE_PLAYED_MEDIAN_TIMESTAMP_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_PLAYED_MEAN_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_PLAYED_MEAN_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_PLAYED_MEAN_EVENT_SERVER_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_merge);

using("ExtractUserRealtimeLiveMergePosidCombineLiveLikeAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_LIKE_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_LIKE_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_LIKE_EVENT_SERVER_TIMESTAMP_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_LIKE_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_LIKE_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_LIKE_EVENT_SERVER_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_merge);

using("ExtractUserRealtimeLiveMergePosidCombineLiveCommentAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_COMMENT_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_COMMENT_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LOG_FULL_AD_LIVE_COMMENT_EVENT_SERVER_TIMESTAMP_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_COMMENT_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_COMMENT_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_AD_LIVE_COMMENT_EVENT_SERVER_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_merge);

using("ExtractUserRealtimeLiveMergePosidCombineOrderSubmitClickAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_LOG_FULL_EVENT_ORDER_SUBMIT_CLICK_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LOG_FULL_EVENT_ORDER_SUBMIT_CLICK_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_LOG_FULL_EVENT_ORDER_SUBMIT_CLICK_EVENT_SERVER_TIMESTAMP_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_EVENT_ORDER_SUBMIT_CLICK_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_EVENT_ORDER_SUBMIT_CLICK_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.FANSTOP_LOG_FULL_EVENT_ORDER_SUBMIT_CLICK_EVENT_SERVER_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_merge);

using("ExtractUserRealTimeMerchantPosidCombineOrderPaiedAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_AUTHOR_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_GET_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_TIMESTAMP_LIST:int64_list",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);

using("ExtractUserRealTimeMerchantPosidCombineOrderPaiedItemPriceList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_ITEM_PACKAGE_0_ITEM_PRICE_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_GET_POS_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_TIMESTAMP_LIST:int64_list", 
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);

using("ExtractUserRealTimeMerchantPosidCombineGoodsViewAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);

using("ExtractUserRealTimeMerchantPosidCombineGoodsViewItemPriceList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_PACKAGE_0_ITEM_PRICE_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_TIMESTAMP_LIST:int64_list",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);

using("ExtractUserRealTimeMerchantPosidCombineGoodsViewItemBrandList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_PACKAGE_0_CLICK_ITEM_ID_302_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_TIMESTAMP_LIST:int64_list",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);

using("ExtractUserRealTimeMerchantPosidCombineGoodsViewItemX7Category3List",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_ITEM_X7_CATEGORY3_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_TIMESTAMP_LIST:int64_list",
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);

using("ExtractUserRealTimeMerchantPosidCombineOrderImpressionItemAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_IMPRESSION_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_IMPRESSION_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_IMPRESSION_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);

using("ExtractUserRealTimeMerchantPosidCombineOrderImpressionItemPriceList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_IMPRESSION_ITEM_PACKAGE_0_ITEM_PRICE_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_IMPRESSION_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_IMPRESSION_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);

using("ExtractUserRealTimeMerchantPosidCombineOrderSubmitClickAuthorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_SUBMIT_CLICK_AUTHOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_SUBMIT_CLICK_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_SUBMIT_CLICK_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);

using("ExtractUserRealTimeMerchantPosidCombineOrderSubmitClickItemPriceList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_SUBMIT_CLICK_ITEM_PACKAGE_0_ITEM_PRICE_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_SUBMIT_CLICK_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_SUBMIT_CLICK_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);



using("ExtractURealTimeMerchantPosidSimOrderPaiedDistributorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_DISTRIBUTOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_PAIED_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);

using("ExtractUserRealTimeMerchantPosidCombineOrderSubmitDistributorIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_SUBMIT_DISTRIBUTOR_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_SUBMIT_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_ORDER_EVENT_ORDER_SUBMIT_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);

using("ExtractUserRealTimeMerchantPosidCombineGoodsViewUserIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_USER_ID:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_GOODS_VIEW_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);

using("ExtractUserRealTimeMerchantPosidCombineOrderImpressionUserIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_IMPRESSION_USER_ID:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_IMPRESSION_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_ORDER_IMPRESSION_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);

using("ExtractUserRealTimeMerchantPosidCombineProductBuyClickUserIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_PRODUCT_BUY_CLICK_USER_ID:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_PRODUCT_BUY_CLICK_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_PRODUCT_BUY_CLICK_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);

using("ExtractUserRealTimeMerchantPosidCombineShopClickUserIdList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_SHOP_CLICK_USER_ID:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_SHOP_CLICK_GET_POS_ID_LIST:int64_list",  
      "adlog.user_info.common_info_attr.AD_MERCHANT_RECO_CALLBACK_EVENT_SHOP_CLICK_TIMESTAMP_LIST:int64_list",  
      100,
      "adlog.time",
      3000,
      extract_user_realtime_combine_action_live_solo);