import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_tmpl.dark"

using("ExtractUserSparseLpsP90List1",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_USER_P90_PHOTO_ID_LIST:int64_list",
      FeaturePrefix::AD_LOG_FULL_USER_P90_PHOTO_ID_LIST);
using("ExtractUserSparseLpsP90List2",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_USER_P90_PRODUCT_LIST:int64_list",
      FeaturePrefix::AD_LOG_FULL_USER_P90_PRODUCT_LIST);
using("ExtractUserSparseLpsP90List3",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_USER_P90_AUTHOR_LIST:int64_list",
      FeaturePrefix::AD_LOG_FULL_USER_P90_AUTHOR_LIST);
using("ExtractUserSparseLpsP90List4",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_USER_P90_PACK_LIST:int64_list",
      FeaturePrefix::AD_LOG_FULL_USER_P90_PACK_LIST);
using("ExtractUserSparseLpsP90List5",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_USER_P90_1IND_LIST:int64_list",
      FeaturePrefix::AD_LOG_FULL_USER_P90_1IND_LIST);
using("ExtractUserSparseLpsP90List6",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.AD_LOG_FULL_USER_P90_2IND_LIST:int64_list",
      FeaturePrefix::AD_LOG_FULL_USER_P90_2IND_LIST);
