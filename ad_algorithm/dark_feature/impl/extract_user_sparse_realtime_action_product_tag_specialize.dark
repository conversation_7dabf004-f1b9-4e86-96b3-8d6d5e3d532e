import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_tag_center_tmpl.dark"

using("ExtractUserSparseTagCenterP5sProductGameplay",
      "ExtractUserSparseTagCenterProductTagTmpl2",
      "KeyPosType::USER_AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST",
      "KeyPosType::USER_AD_PHOTO_PLAYED_END_PRODUCT_NAME_LIST",
        FeaturePrefix::USER_AD_PHOTO_PLAYED_5S_PRODUCT_NAME_LIST,
        const_industry_v5_gameplay);

using("ExtractUserSparseTagCenterClickProductTheme",
      "ExtractUserSparseTagCenterProductTagTmpl2",
      "KeyPosType::USER_ADLOGFULL_AD_PHOTO_CLICK_PRODUCT_NAME_LIST",
      "KeyPosType::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST",
        FeaturePrefix::USER_ADLOGFULL_AD_PHOTO_CLICK_PRODUCT_NAME_LIST,
        const_industry_v5_theme);

using("ExtractUserSparseTagCenterConvProductThird",
      "ExtractUserSparseTagCenterProductTagTmpl3",
      "KeyPosType::USER_EVENT_CONVERSION_PRODUCT_NAME_LIST",
      "KeyPosType::USER_EVENT_PAY_PRODUCT_NAME_LIST",
      "KeyPosType::USER_ADLOGFULL_EVENT_NEXTDAY_STAY_PRODUCT_NAME_LIST",
        FeaturePrefix::USER_EVENT_CONVERSION_PRODUCT_NAME_LIST,
        const_industry_v5_third_level_name);

using("ExtractUserSparseTagCenterOtherProductSecond",
      "ExtractUserSparseTagCenterProductTagTmpl3",
      "KeyPosType::USER_AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST",
      "KeyPosType::USER_AD_ITEM_DOWNLOAD_INSTALLED_PRODUCT_NAME_LIST",
      "KeyPosType::USER_AD_APPROXIMATE_PURCHASE_PRODUCT_NAME_LIST",
        FeaturePrefix::USER_AD_ITEM_DOWNLOAD_COMPLETED_PRODUCT_NAME_LIST,
        const_industry_v5_second_level_name);
