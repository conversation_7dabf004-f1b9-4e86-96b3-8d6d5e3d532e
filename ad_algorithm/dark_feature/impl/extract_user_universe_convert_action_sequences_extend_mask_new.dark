using("ExtractUserUniverseConvertActionSeqMediaAppIdExtendMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIA_APP_ID:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVNET_CONVERSIONIS_MEDIA_APP_ID:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_MEDIUM_APP_ID:int64_list",
  10,
  30,
  merge_two_seq_with_max_len,
  "adlog.context.app_id",
  universe_cast_string_to_int64,
  30,
  hard_search_seq_with_single_key,
  30
);

using("ExtractUserUniverseConvertActionSeqIndustryTagExtendReMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_CRM_INDUSTRY_TYPE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_FORM_SUBMIT_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_FORM_SUBMIT_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_CRM_INDUSTRY_TYPE:int64_list",
  "adlog.user_info.common_info_attr.EVNET_CONVERSION_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.EVENT_CONVERSION_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  2,
  8,
  merge_two_seq_with_two_max_len,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_INDUSTRY_TAG:int64_list",
  10,
  30,
  merge_two_seq_with_max_len,
  "adlog.item.ad_dsp_info.common_info_attr.PHOTO_CRM_DEFINE_THIRD_INDUSTRY:string",
  30,
  hard_search_seq_with_string_single_key,
  30
);

