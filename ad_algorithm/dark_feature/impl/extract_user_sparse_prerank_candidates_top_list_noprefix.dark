using("ExtractUserSparsePrerankCandidatesTopListHardAccountIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_TOP_HARD_ACCOUNT_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesTopListHardPhotoIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_TOP_HARD_PHOTO_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesTopListHardIndustryIdV3NoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_TOP_HARD_INDUSTRY_V3_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesTopListHardCityProductIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_TOP_HARD_CITY_PRODUCT_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesTopListHardOcpxActionTypeNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_TOP_HARD_OCPX_ACTION_TYPE_LIST:int64_list"
      );
      
using("ExtractUserSparsePrerankCandidatesTopListSoftAccountIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_TOP_SOFT_ACCOUNT_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesTopListSoftPhotoIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_TOP_SOFT_PHOTO_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesTopListSoftIndustryIdV3NoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_TOP_SOFT_INDUSTRY_V3_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesTopListSoftCityProductIdNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_TOP_SOFT_CITY_PRODUCT_ID_LIST:int64_list"
      );

using("ExtractUserSparsePrerankCandidatesTopListSoftOcpxActionTypeNoPrefix",
      FeatureType::USER,
      "adlog.context.info_common_attr.PRERANK_CANDIDATES_TOP_SOFT_OCPX_ACTION_TYPE_LIST:int64_list"
      );