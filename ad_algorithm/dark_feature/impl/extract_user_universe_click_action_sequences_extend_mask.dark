using("ExtractUserUniverseClickActionSeqIndustryTagExtendMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_CRM_INDUSTRY_TYPE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_hash_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_INDUSTRY_TAG:int64_list",
  10,
  30,
  merge_two_seq_with_max_len,
  "adlog.item.ad_dsp_info.common_info_attr.PHOTO_CRM_DEFINE_THIRD_INDUSTRY:string",
  30,
  hard_search_seq_with_single_key,
  30
);

using("ExtractUserUniverseClickActionSeqIsPlayableExtendMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_IS_PLAYABLE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_IS_PLAYABLE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len,
  "adlog.item.ad_dsp_info.common_info_attr.IS_PLAYABLE:int64",
  30,
  hard_search_seq_with_single_key,
  30
);

using("ExtractUserUniverseClickActionSeqAdStyleExtendMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_AD_STYLE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_AD_STYLE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len,
  "adlog.context.info_common_attr.AD_STYLE:int64",
  30,
  hard_search_seq_with_single_key,
  30
);

using("ExtractUserUniverseClickActionSeqMediumCooperationModeExtendMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_COOPERATION_MODE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_MEDIUM_COOPERATION_MODE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len,
  "adlog.context.info_common_attr.COOPERATION_MODE:int64",
  30,
  hard_search_seq_with_single_key,
  30
);

using("ExtractUserUniverseClickActionSeqMaterialFeatureTypeExtendMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_MATERIAL_FEATUR_TYPE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_MEDIUM_ATTRIBUTE:int64_list",
  "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
  30,
  "adlog.time",
  86400000,
  get_universe_seq_with_max_len_and_timestamp,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_MATERIAL_FEATURE_TYPE:int64_list",
  10,
  30,
  merge_two_seq_with_max_len,
  "adlog.item.ad_dsp_info.common_info_attr.MATERIAL_FEATURE_TYPE:int64",
  30,
  hard_search_seq_with_single_key,
  30
);