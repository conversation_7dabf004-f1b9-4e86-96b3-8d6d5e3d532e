using("ExtractUserSparseJxOffItemimpFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_ITEM_IMP_PHOTO_LIST_7D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_ITEM_IMP_AUTHOR_LIST_7D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffItemClickFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_ITEM_CLICK_PHOTO_LIST_7D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_ITEM_CLICK_AUTHOR_LIST_7D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffLikeFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_LIKE_PHOTO_LIST_7D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_LIKE_AUTHOR_LIST_7D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffCommentFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_COMMENT_PHOTO_LIST_7D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_COMMENT_AUTHOR_LIST_7D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffPlayed3sFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_PLAYED_3S_PHOTO_LIST_7D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_PLAYED_3S_AUTHOR_LIST_7D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffPlayed5sFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_PLAYED_5S_PHOTO_LIST_7D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_PLAYED_5S_AUTHOR_LIST_7D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffPlayedendFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_COMPLETED_PLAY_PHOTO_LIST_7D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_COMPLETED_PLAY_AUTHOR_LIST_7D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffInvokeFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_INVOKE_PHOTO_LIST_7D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_INVOKE_AUTHOR_LIST_7D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffDownloadFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_DOWNLOAD_PHOTO_LIST_7D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_DOWNLOAD_AUTHOR_LIST_7D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffLandingPageLoadFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_LANDING_PAGE_LOAD_PHOTO_LIST_7D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_LANDING_PAGE_LOAD_AUTHOR_LIST_7D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffComeBackMainAppFull",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_COME_BACK_MAIN_APP_PHOTO_LIST_7D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_COME_BACK_MAIN_APP_AUTHOR_LIST_7D:int64_list",
     merge_int64_list_all);

// 添加 聚星 30D 行为序列
using("ExtractUserSparseJxOffItemimpFull30D",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_ITEM_IMP_PHOTO_LIST_30D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_ITEM_IMP_AUTHOR_LIST_30D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffItemClickFull30D",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_ITEM_CLICK_PHOTO_LIST_30D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_ITEM_CLICK_AUTHOR_LIST_30D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffLikeFull30D",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_LIKE_PHOTO_LIST_30D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_LIKE_AUTHOR_LIST_30D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffCommentFull30D",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_COMMENT_PHOTO_LIST_30D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_COMMENT_AUTHOR_LIST_30D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffPlayed3sFull30D",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_PLAYED_3S_PHOTO_LIST_30D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_PLAYED_3S_AUTHOR_LIST_30D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffPlayed5sFull30D",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_PLAYED_5S_PHOTO_LIST_30D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_PLAYED_5S_AUTHOR_LIST_30D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffPlayedendFull30D",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_COMPLETED_PLAY_PHOTO_LIST_30D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_COMPLETED_PLAY_AUTHOR_LIST_30D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffInvokeFull30D",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_INVOKE_PHOTO_LIST_30D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_INVOKE_AUTHOR_LIST_30D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffDownloadFull30D",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_DOWNLOAD_PHOTO_LIST_30D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_DOWNLOAD_AUTHOR_LIST_30D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffLandingPageLoadFull30D",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_LANDING_PAGE_LOAD_PHOTO_LIST_30D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_LANDING_PAGE_LOAD_AUTHOR_LIST_30D:int64_list",
     merge_int64_list_all);

using("ExtractUserSparseJxOffComeBackMainAppFull30D",
     FeatureType::USER,
     "adlog.user_info.common_info_attr.JX_XSP_COME_BACK_MAIN_APP_PHOTO_LIST_30D:int64_list",
     "adlog.user_info.common_info_attr.JX_XSP_COME_BACK_MAIN_APP_AUTHOR_LIST_30D:int64_list",
     merge_int64_list_all);
