using("ExtractUserMerchantPayAbilityV1",
      FeatureType::DENSE_USER,
      "adlog.user_info.common_info_attr.USER_MERCHANT_PAY_ABILITY_ORDER_TIMES_30D:int64",
      cast_to_float,
      log_plus_1_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_MERCHANT_PAY_ABILITY_ORDER_TIMES_90D:int64",
      cast_to_float,
      log_plus_1_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_MERCHANT_PAY_ABILITY_ORDER_TIMES_360D:int64",
      cast_to_float,
      log_plus_1_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_MERCHANT_PAY_ABILITY_TOTAL_FEE_30D:int64",
      cast_to_float,
      log_plus_1_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_MERCHANT_PAY_ABILITY_TOTAL_FEE_90D:int64",
      cast_to_float,
      log_plus_1_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_MERCHANT_PAY_ABILITY_TOTAL_FEE_360D:int64",
      cast_to_float,
      log_plus_1_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_MERCHANT_PAY_ABILITY_ORDER_PRICE_MEDIAN_30D:int64",
      cast_to_float,
      log_plus_1_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_MERCHANT_PAY_ABILITY_ORDER_PRICE_MEDIAN_90D:int64",
      cast_to_float,
      log_plus_1_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_MERCHANT_PAY_ABILITY_ORDER_PRICE_MEDIAN_360D:int64",
      cast_to_float,
      log_plus_1_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_MERCHANT_PAY_ABILITY_ORDER_PRICE_AVG_30D:int64",
      cast_to_float,
      log_plus_1_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_MERCHANT_PAY_ABILITY_ORDER_PRICE_AVG_90D:int64",
      cast_to_float,
      log_plus_1_e,
      cast_to_float,
      "adlog.user_info.common_info_attr.USER_MERCHANT_PAY_ABILITY_ORDER_PRICE_AVG_360D:int64",
      cast_to_float,
      log_plus_1_e,
      cast_to_float,
      merge_float_list_all,
      12
);