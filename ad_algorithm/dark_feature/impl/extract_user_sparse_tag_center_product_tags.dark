using("ExtractUserSparseTagCenterProductTag1",
    FeatureType::USER,
    log,
    LabelFieldEnum::TONGXIN_TAG_PRODUCT_TAG1,
    MappingType::kCommonProductNameMapping,
    KeyPosType::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagCenterProductTag2",
    FeatureType::USER,
    log,
    LabelFieldEnum::TONGXIN_TAG_PRODUCT_TAG2,
    MappingType::kCommonProductNameMapping,
    KeyPosType::USER_AD_ITEM_CLICK_PRODUCT_NAME_LIST,
    tag_center_common_tag);
