using("ExtractUserUniverseConvertActionSeqProductName",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_PRODUCT_NAME:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqIndustryTag",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_INDUSTRY_TAG:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqIsPlayable",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_IS_PLAYABLE:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqAdStyle",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_AD_STYLE:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqMediumCooperationMode",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_MEDIUM_COOPERATION_MODE:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqMaterialFeatureType",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_MATERIAL_FEATURE_TYPE:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqMediumAppId",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_MEDIUM_APP_ID:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqFirstIndustryName",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_FIRST_INDUSTRY_NAME:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqSecondIndustryName",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_SECOND_INDUSTRY_NAME:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqEndcardId",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_ENDCARD_ID:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqPlaycardId",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_PLAYCARD_ID:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqHour",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_HOUR:int64_list",
  30,
  get_seq_with_max_len
);

using("ExtractUserUniverseConvertActionSeqDateInterval",
  FeatureType::USER,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CONVERT_ACTION_SEQ_DATE_INTERVAL:int64_list",
  30,
  get_seq_with_max_len
);