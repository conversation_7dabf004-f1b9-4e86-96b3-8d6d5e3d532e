using("ExtractUserMerchantLongTermOrderPaiedTsPleDense",
    FeatureType::DENSE_USER,
    "adlog.time",
    "adlog.user_info.common_info_attr.USER_MERCHANT_LONG_TERM_ORDER_PAIED_TIMEREAL:int64_list",
    get_lt_op_ts_bins,
    100,
    get_ts_list_dense_ple_helper,
    400
    );

using("ExtractUserMerchantLongTermGoodsViewTsPleDense",
    FeatureType::DENSE_USER,
    "adlog.time",
    "adlog.user_info.common_info_attr.USER_MERCHANT_LONG_TERM_GOODS_VIEW_TIMEREAL:int64_list",
    get_lt_op_ts_bins,
    100,
    get_ts_list_dense_ple_helper,
    400
    );


using("ExtractUserMerchantLongTermOrderPaiedTsBins",
    FeatureType::USER,
    "adlog.time",
    "adlog.user_info.common_info_attr.USER_MERCHANT_LONG_TERM_ORDER_PAIED_TIMEREAL:int64_list",
    get_lt_op_ts_bins_detail,
    100,
    get_ts_list_bins_helper
    );

using("ExtractUserMerchantLongTermGoodsViewTsBins",
    FeatureType::USER,
    "adlog.time",
    "adlog.user_info.common_info_attr.USER_MERCHANT_LONG_TERM_GOODS_VIEW_TIMEREAL:int64_list",
    get_lt_gs_ts_bins_detail,
    100,
    get_ts_list_bins_helper
    );