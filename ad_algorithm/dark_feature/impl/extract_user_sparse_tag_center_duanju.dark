using("ExtractUserSparseTagCenterDuanjuName",
    FeatureType::USER,
    log,
    LabelFieldEnum::DUANJU_TAG_NAME,
    MappingType::kDuanjuTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_UNIT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagCenterDuanjuTheme",
    FeatureType::USER,
    log,
    LabelFieldEnum::DUANJU_TAG_THEME,
    MappingType::kDuanjuTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_UNIT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagCenterDuanjuPlot",
    FeatureType::USER,
    log,
    LabelFieldEnum::DUANJU_TAG_PLOT,
    MappingType::kDuanjuTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_UNIT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagCenterDuanjuChannel",
    FeatureType::USER,
    log,
    LabelFieldEnum::DUANJU_TAG_CHANNEL,
    MappingType::kDuanjuTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_UNIT_ID_LIST,
    tag_center_common_tag);

using("ExtractItemSparseTagCenterMinCharge",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::DUANJU_TAG_MIN_CHARGE,
    MappingType::kDuanjuTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ID,
    tag_center_common_float_item);

using("ExtractItemSparseTagCenterMaxCharge",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::DUANJU_TAG_MAX_CHARGE,
    MappingType::kDuanjuTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ID,
    tag_center_common_float_item);

using("ExtractItemSparseTagCenterRecommendCharge",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::DUANJU_TAG_RECOMMEND_CHARGE,
    MappingType::kDuanjuTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ID,
    tag_center_common_float_item);

using("ExtractItemSparseTagCenterUnitPrice",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::DUANJU_TAG_UNIT_PRICE,
    MappingType::kDuanjuTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ID,
    tag_center_common_float_item);

using("ExtractItemSparseTagCenterEpisodeDuration",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::DUANJU_TAG_EPISODE_DURATION,
    MappingType::kDuanjuTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ID,
    tag_center_common_int_item);

using("ExtractItemSparseTagCenterPaidEpisodes",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::DUANJU_TAG_PAID_EPISODES,
    MappingType::kDuanjuTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ID,
    tag_center_common_int_item);

using("ExtractItemSparseTagCenterEpisodes",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::DUANJU_TAG_EPISODES,
    MappingType::kDuanjuTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ID,
    tag_center_common_int_item);

using("ExtractItemSparseTagCenterAnnualVipType",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::DUANJU_TAG_ANNUAL_VIP_TYPE,
    MappingType::kDuanjuTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ID,
    tag_center_common_int_item);

using("ExtractItemSparseTagCenterSemiannualVipType",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::DUANJU_TAG_SEMIANNUAL_VIP_TYPE,
    MappingType::kDuanjuTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ID,
    tag_center_common_int_item);