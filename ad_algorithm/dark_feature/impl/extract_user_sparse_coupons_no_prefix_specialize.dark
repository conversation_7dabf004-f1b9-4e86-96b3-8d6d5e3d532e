import "teams/ad/ad_algorithm/dark_feature/impl/extract_multi_field_tmpl.dark"

using("ExtractSparseCombineUserAuthorAvailCouponsNum",
      "ExtractCombine4FieldNoPrefixTmpl",
      "adlog.item.ad_dsp_info.live_info.author_info.id",
      "adlog.user_info.common_info_attr.MERCHANT_COUPON_ITEM_LIST:int64_list",
      "adlog.user_info.common_info_attr.MERCHANT_COUPON_TARGET_LIST:int64_list",
      "adlog.user_info.common_info_attr.MERCHANT_COUPON_SOURCE_ID_LIST:int64_list",
      get_author_avail_coupon_match_cnt);

using("ExtractSparseUserDiffCouponsNum",
      "ExtractUserSparse3FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.MERCHANT_COUPON_TARGET_LIST:int64_list",
      "adlog.user_info.common_info_attr.MERCHANT_COUPON_RIGHT_TYPE_LIST:int64_list",
      "adlog.user_info.common_info_attr.MERCHANT_COUPON_SOURCE_LIST:int64_list",
      get_diff_coupon_cnt);

using("ExtractSparseUserCouponThresholdStat",
      "ExtractUserSparse1FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.MERCHANT_COUPON_THRESHOLD_LIST:int64_list",
      get_coupons_value_stat);

using("ExtractSparseUserCouponReduceStat",
      "ExtractUserSparse1FieldNoPrefixTmpl",
      "adlog.user_info.common_info_attr.MERCHANT_COUPON_REDUCE_LIST:int64_list",
      get_coupons_value_stat);
