import "teams/ad/ad_algorithm/dark_feature/impl/extract_user_tmpl.dark"
import "teams/ad/ad_algorithm/dark_feature/impl/extract_multi_field_tmpl.dark"

using("ExtractUserSparseActionWifiTime",
      "ExtractUserSparseTmpl",
      "adlog.user_info.common_info_attr.USER_ACTION_WIFI_TIME:int64",
      FeaturePrefix::USER_ACTION_WIFI_TIME);
 using("ExtractUserSparseActionWifiTimeProportion",
      "ExtractUserSparse1FieldTmpl",
      "adlog.user_info.common_info_attr.USER_ACTION_WIFI_TIME_PROPORTION:float",
      get_wifi_bucket,
      FeaturePrefix::USER_ACTION_WIFI_TIME_PROPORTION);
using("ExtractUserSparseUniverseSearchQuery",
      "ExtractUserSparse1FieldTmpl",
      "adlog.context.info_common_attr.UNIVERSE_SEARCH_QUERY:string",
      city_hash64,
      FeaturePrefix::UNIVERSE_SEARCH_QUERY);
using("ExtractUserSparseUniverseAppDistributeType",
      "ExtractUserSparse1FieldTmpl",
      "adlog.context.info_common_attr.UNIVERSE_APP_DISTRIBUTE_TYPE:string",
      city_hash64,
      FeaturePrefix::UNIVERSE_APP_DISTRIBUTE_TYPE);
