using("ExtractUserSparseRealtimeAdxOuterPhotoList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_REAL_TIME_ADX_OUTER_PHOTO_ID:int64_list",
       "adlog.user_info.common_info_attr.USER_REAL_TIME_ADX_OUTER_TIMESTAMP:int64_list",
       "adlog.time",
       filter_action_by_time_diff);

using("ExtractUserSparseRealtimeAdxOuterProductNameList",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.USER_REAL_TIME_ADX_OUTER_PRODUCT_NAME:int64_list",
      "adlog.user_info.common_info_attr.USER_REAL_TIME_ADX_OUTER_TIMESTAMP:int64_list",
      "adlog.time",
       filter_action_by_time_diff);
