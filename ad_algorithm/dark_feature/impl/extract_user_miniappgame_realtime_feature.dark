using("ExtractUserMiniAppGameAdImpressionMarketingObjectives",
   "adlog.user_info.common_info_attr.key:66786:int64_list");
using("ExtractUserMiniAppGameAdImpressionOptimizationGoals",
   "adlog.user_info.common_info_attr.key:66787:int64_list");
using("ExtractUserMiniAppGameAdImpressionProductNames",
   "adlog.user_info.common_info_attr.key:66788:int64_list");
using("ExtractUserMiniAppGameAdImpressionMaterialTypes",
   "adlog.user_info.common_info_attr.key:66789:int64_list");
using("ExtractUserMiniAppGameAdImpressionResourceTypes",
   "adlog.user_info.common_info_attr.key:66790:int64_list");
using("ExtractUserMiniAppGameAdImpressionMiniGameIds",
   "adlog.user_info.common_info_attr.key:66792:int64_list");
using("ExtractUserMiniAppGameAdClickMarketingObjectives",
   "adlog.user_info.common_info_attr.key:66799:int64_list");
using("ExtractUserMiniAppGameAdClickOptimizationGoals",
   "adlog.user_info.common_info_attr.key:66800:int64_list");
using("ExtractUserMiniAppGameAdClickProductNames",
   "adlog.user_info.common_info_attr.key:66801:int64_list");
using("ExtractUserMiniAppGameAdClickMaterialTypes",
   "adlog.user_info.common_info_attr.key:66802:int64_list");
using("ExtractUserMiniAppGameAdClickResourceTypes",
   "adlog.user_info.common_info_attr.key:66803:int64_list");
using("ExtractUserMiniAppGameAdClickMiniGameIds",
   "adlog.user_info.common_info_attr.key:66805:int64_list");
using("ExtractUserMiniAppGameAdClickAdBehaviors",
   "adlog.user_info.common_info_attr.key:66809:int64_list");
using("ExtractUserMiniAppGameAdActivationMarketingObjectives",
   "adlog.user_info.common_info_attr.key:66812:int64_list");
using("ExtractUserMiniAppGameAdActivationOptimizationGoals",
   "adlog.user_info.common_info_attr.key:66813:int64_list");
using("ExtractUserMiniAppGameAdActivationProductNames",
   "adlog.user_info.common_info_attr.key:66814:int64_list");
using("ExtractUserMiniAppGameAdActivationMaterialTypes",
   "adlog.user_info.common_info_attr.key:66815:int64_list");
using("ExtractUserMiniAppGameAdActivationResourceTypes",
   "adlog.user_info.common_info_attr.key:66816:int64_list");
using("ExtractUserMiniAppGameAdActivationMiniGameIds",
   "adlog.user_info.common_info_attr.key:66818:int64_list");
using("ExtractUserMiniAppGameAdPlaytimeMarketingObjectives",
   "adlog.user_info.common_info_attr.key:66825:int64_list");
using("ExtractUserMiniAppGameAdPlaytimeOptimizationGoals",
   "adlog.user_info.common_info_attr.key:66826:int64_list");
using("ExtractUserMiniAppGameAdPlaytimeProductNames",
   "adlog.user_info.common_info_attr.key:66827:int64_list");
using("ExtractUserMiniAppGameAdPlaytimeMaterialTypes",
   "adlog.user_info.common_info_attr.key:66828:int64_list");
using("ExtractUserMiniAppGameAdPlaytimeResourceTypes",
   "adlog.user_info.common_info_attr.key:66829:int64_list");
using("ExtractUserMiniAppGameAdPlaytimeMiniGameIds",
   "adlog.user_info.common_info_attr.key:66831:int64_list");
using("ExtractUserMiniAppGameRewardedAdImpressionPageIds",
   "adlog.user_info.common_info_attr.key:66875:int64_list");
using("ExtractUserMiniAppGameRewardedAdImpressionMarketingObjectives",
   "adlog.user_info.common_info_attr.key:66838:int64_list");
using("ExtractUserMiniAppGameRewardedAdImpressionOptimizationGoals",
   "adlog.user_info.common_info_attr.key:66839:int64_list");
using("ExtractUserMiniAppGameRewardedAdImpressionProductNames",
   "adlog.user_info.common_info_attr.key:66840:int64_list");
using("ExtractUserMiniAppGameRewardedAdImpressionMaterialTypes",
   "adlog.user_info.common_info_attr.key:66841:int64_list");
using("ExtractUserMiniAppGameRewardedAdImpressionPrimaryEntrySources",
   "adlog.user_info.common_info_attr.key:66876:int64_list");
using("ExtractUserMiniAppGameRewardedAdImpressionResourceTypes",
   "adlog.user_info.common_info_attr.key:66842:int64_list");
using("ExtractUserMiniAppGameRewardedAdImpressionDeviceOsIds",
   "adlog.user_info.common_info_attr.key:66843:int64_list");
using("ExtractUserMiniAppGameRewardedAdImpressionMiniGameIds",
   "adlog.user_info.common_info_attr.key:66844:int64_list");
using("ExtractUserMiniAppGameRewardedAdClickPageIds",
   "adlog.user_info.common_info_attr.key:66877:int64_list");
using("ExtractUserMiniAppGameRewardedAdClickMarketingObjectives",
   "adlog.user_info.common_info_attr.key:66851:int64_list");
using("ExtractUserMiniAppGameRewardedAdClickOptimizationGoals",
   "adlog.user_info.common_info_attr.key:66852:int64_list");
using("ExtractUserMiniAppGameRewardedAdClickProductNames",
   "adlog.user_info.common_info_attr.key:66853:int64_list");
using("ExtractUserMiniAppGameRewardedAdClickMaterialTypes",
   "adlog.user_info.common_info_attr.key:66854:int64_list");
using("ExtractUserMiniAppGameRewardedAdClickPrimaryEntrySources",
   "adlog.user_info.common_info_attr.key:66878:int64_list");
using("ExtractUserMiniAppGameRewardedAdClickResourceTypes",
   "adlog.user_info.common_info_attr.key:66855:int64_list");
using("ExtractUserMiniAppGameRewardedAdClickDeviceOsIds",
   "adlog.user_info.common_info_attr.key:66856:int64_list");
using("ExtractUserMiniAppGameRewardedAdClickMiniGameIds",
   "adlog.user_info.common_info_attr.key:66857:int64_list");
using("ExtractUserMiniAppGameRewardedAdConversionPageIds",
   "adlog.user_info.common_info_attr.key:66879:int64_list");
using("ExtractUserMiniAppGameRewardedAdConversionMarketingObjectives",
   "adlog.user_info.common_info_attr.key:66864:int64_list");
using("ExtractUserMiniAppGameRewardedAdConversionOptimizationGoals",
   "adlog.user_info.common_info_attr.key:66865:int64_list");
using("ExtractUserMiniAppGameRewardedAdConversionProductNames",
   "adlog.user_info.common_info_attr.key:66866:int64_list");
using("ExtractUserMiniAppGameRewardedAdConversionMaterialTypes",
   "adlog.user_info.common_info_attr.key:66867:int64_list");
using("ExtractUserMiniAppGameRewardedAdConversionPrimaryEntrySources",
   "adlog.user_info.common_info_attr.key:66880:int64_list");
using("ExtractUserMiniAppGameRewardedAdConversionResourceTypes",
   "adlog.user_info.common_info_attr.key:66868:int64_list");
using("ExtractUserMiniAppGameRewardedAdConversionDeviceOsIds",
   "adlog.user_info.common_info_attr.key:66869:int64_list");
using("ExtractUserMiniAppGameRewardedAdConversionMiniGameIds",
   "adlog.user_info.common_info_attr.key:66870:int64_list");