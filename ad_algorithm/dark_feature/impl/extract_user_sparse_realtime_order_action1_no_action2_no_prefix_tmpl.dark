class ExtractUserSparseRealtimeOrderAction1NoAction2NoPrefixTmpl<
                                               field1: Field,
                                               field2: Field,
                                               field3: Field,
                                               field4: Field,
                                               field5: Field,
                                               field6: Field,
                                               field7: Field,
                                               field8: Field> {
  set_feature_type(FeatureType::USER);

  fn Extract(log, pos, result) {

    process_time = get_field("adlog.time");

    x1 = get_field(field1);
    x2 = get_field(field2);
    x3 = get_field(field3);
    x4 = get_field(field4);
    x5 = get_field(field5);
    x6 = get_field(field6);
    x7 = get_field(field7);
    x8 = get_field(field8);

    y1 = get_diff_action_from_two_list(x1, x2, x3, x4, x5, x6, x7, x8, process_time, 1);
    y2 = get_diff_action_from_two_list(x1, x2, x3, x4, x5, x6, x7, x8, process_time, 6);
    y3 = get_diff_action_from_two_list(x1, x2, x3, x4, x5, x6, x7, x8, process_time, 24);
    y4 = get_diff_action_from_two_list(x1, x2, x3, x4, x5, x6, x7, x8, process_time, 72);
    y5 = get_diff_action_from_two_list(x1, x2, x3, x4, x5, x6, x7, x8, process_time, 168);

    a1 = get_first_list(y1);
    a2 = get_first_list(y2);
    a3 = get_first_list(y3);
    a4 = get_first_list(y4);
    a5 = get_first_list(y5);
    b1 = get_second_list(y1);
    b2 = get_second_list(y2);
    b3 = get_second_list(y3);
    b4 = get_second_list(y4);
    b5 = get_second_list(y5);

    z = merge_int64_list_with_limit(100, a1, a2, a3, a4, a5, b1, b2, b3, b4, b5);

    add_feature_result(z, result);

  }
}

