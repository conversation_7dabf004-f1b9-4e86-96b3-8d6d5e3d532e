using("ExtractUserSparseTagTagCenterSocializingType",
    FeatureType::USER,
    log,
    LabelFieldEnum::SHEJIAO_TAG_SOCIALIZING_TYPE,
    MappingType::kShejiaoTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagTagCenterProfitModelFirst",
    FeatureType::USER,
    log,
    LabelFieldEnum::SHEJIAO_TAG_PROFIT_MODEL_FIRST,
    MappingType::kShejiaoTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagTagCenterProfitModelSecond",
    FeatureType::USER,
    log,
    LabelFieldEnum::SHEJIAO_TAG_PROFIT_MODEL_SECOND,
    MappingType::kShejiaoTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagTagCenterCoreGameplayFirst",
    FeatureType::USER,
    log,
    LabelFieldEnum::SHEJIAO_TAG_CORE_GAMEPLAY_FIRST,
    MappingType::kShejiaoTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagTagCenterCoreGameplaySecond",
    FeatureType::USER,
    log,
    LabelFieldEnum::SHEJIAO_TAG_CORE_GAMEPLAY_SECOND,
    MappingType::kShejiaoTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagTagCenterPaymentThreshold",
    FeatureType::USER,
    log,
    LabelFieldEnum::SHEJIAO_TAG_PAYMENT_THRESHOLD,
    MappingType::kShejiaoTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagTagCenterGuild",
    FeatureType::USER,
    log,
    LabelFieldEnum::SHEJIAO_TAG_GUILD,
    MappingType::kShejiaoTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagTagCenterCarrierFirst",
    FeatureType::USER,
    log,
    LabelFieldEnum::SHEJIAO_TAG_CARRIER_FIRST,
    MappingType::kShejiaoTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractUserSparseTagTagCenterCarrierSecond",
    FeatureType::USER,
    log,
    LabelFieldEnum::SHEJIAO_TAG_CARRIER_SECOND,
    MappingType::kShejiaoTagMapping,
    KeyPosType::USER_ADLOGFULL_AD_ITEM_CLICK_ACCOUNT_ID_LIST,
    tag_center_common_tag);

using("ExtractItemSparseTagCenterSocializingType",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::SHEJIAO_TAG_SOCIALIZING_TYPE,
    MappingType::kShejiaoTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);

using("ExtractItemSparseTagCenterProfitModelFirst",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::SHEJIAO_TAG_PROFIT_MODEL_FIRST,
    MappingType::kShejiaoTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);

using("ExtractItemSparseTagCenterProfitModelSecond",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::SHEJIAO_TAG_PROFIT_MODEL_SECOND,
    MappingType::kShejiaoTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);

using("ExtractItemSparseTagCenterCoreGameplayFirst",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::SHEJIAO_TAG_CORE_GAMEPLAY_FIRST,
    MappingType::kShejiaoTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);

using("ExtractItemSparseTagCenterCoreGameplaySecond",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::SHEJIAO_TAG_CORE_GAMEPLAY_SECOND,
    MappingType::kShejiaoTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);

using("ExtractItemSparseTagCenterPaymentThreshold",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::SHEJIAO_TAG_PAYMENT_THRESHOLD,
    MappingType::kShejiaoTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);

using("ExtractItemSparseTagCenterGuild",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::SHEJIAO_TAG_GUILD,
    MappingType::kShejiaoTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);

using("ExtractItemSparseTagCenterCarrierFirst",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::SHEJIAO_TAG_CARRIER_FIRST,
    MappingType::kShejiaoTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);

using("ExtractItemSparseTagCenterCarrierSecond",
    FeatureType::ITEM,
    log,
    pos,
    LabelFieldEnum::SHEJIAO_TAG_CARRIER_SECOND,
    MappingType::kShejiaoTagMapping,
    KeyPosType::ITEM_ADLOG_ITEM_AD_DSP_INFO_UNIT_BASE_ACCOUNT_ID,
    tag_center_common_tag_item);