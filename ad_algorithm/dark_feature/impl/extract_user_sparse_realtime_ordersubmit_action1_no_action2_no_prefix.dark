using("ExtractUserSparseRealtimeImpNoSubmit",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_ITEM_IMPRESSION_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.time",
      get_diff_action_no_ocpc
      );
using("ExtractUserSparseRealtimeP3sNoSubmit",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_3S_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.time",
      get_diff_action_no_ocpc
      );
using("ExtractUserSparseRealtimePedNoSubmit",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_PHOTO_PLAYED_END_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.time",
      get_diff_action_no_ocpc
      );
using("ExtractUserSparseRealtimeClickNoSubmit",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_ITEM_CLICK_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_ITEM_CLICK_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_ITEM_CLICK_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.time",
      get_diff_action_no_ocpc
      );
using("ExtractUserSparseRealtimeApxmNoSubmit",
      FeatureType::USER,
      "adlog.user_info.common_info_attr.AD_APPROXIMATE_PURCHASE_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_APPROXIMATE_PURCHASE_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.AD_APPROXIMATE_PURCHASE_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_TIMESTAMP_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_PHOTO_ID_LIST:int64_list",
      "adlog.user_info.common_info_attr.EVENT_ORDER_SUBMIT_INDUSTRY_ID_V3_LIST:int64_list",
      "adlog.time",
      get_diff_action_no_ocpc
      );
