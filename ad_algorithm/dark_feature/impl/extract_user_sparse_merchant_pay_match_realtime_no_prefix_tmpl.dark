class ExtractUserSparseMerchantPayMatchRealTimeNoPrefixTmpl<action_field : Field,
                                                      ts_field: Field,
                                                      max_len: int,
                                                      target_type: int,
                                                      x7_level_2_field:Field,
                                                      x7_level_3_field:Field,
                                                      spu_id_field:Field
                                                      > {
  set_feature_type(FeatureType::COMBINE);

  fn Extract(log, pos, result) {
    live_author_id = get_field("adlog.item.ad_dsp_info.live_info.author_info.id");
    photo_author_id = get_field("adlog.item.ad_dsp_info.photo_info.author_info.id");
    live_id = get_field("adlog.item.ad_dsp_info.live_info.id");
    x7_level_2_list = get_field(x7_level_2_field);
    x7_level_3_list = get_field(x7_level_3_field);
    spu_id_list = get_field(spu_id_field);
    target_id_list = get_mathched_target_id(live_author_id,photo_author_id,live_id,x7_level_2_list,x7_level_3_list,spu_id_list,target_type);
   
    adlog_time = get_field("adlog.time");
    action_content_list = get_field(action_field);
    timestamp_flag_list = get_field("adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_TIMESTAMP_Flag:int64_list");
    action_timestamp_list = get_field(ts_field);

    cps_type_list = get_field("adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_CPS_TYPE:int64_list");
    seller_id_list =  get_field("adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_SELLER_ID:int64_list");
    distribute_id_list = get_field("adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_DISTRIBUTOR_ID:int64_list");
    carrier_id_list = get_field("adlog.user_info.common_info_attr.USER_HISTORY_REALTIME_PURCHASE_CARRIER_ID:int64_list");
    
    action_real_content_list = get_real_attr_list(action_timestamp_list,timestamp_flag_list,action_content_list,
        cps_type_list,seller_id_list,distribute_id_list,carrier_id_list,target_type,100);

    time_real_content_list = get_attr_list_by_time(action_timestamp_list,timestamp_flag_list,action_timestamp_list,100);
    enums = get_match_union(action_real_content_list, time_real_content_list,target_id_list, max_len, adlog_time,600);
    add_feature_result(enums, result);
  }
}
