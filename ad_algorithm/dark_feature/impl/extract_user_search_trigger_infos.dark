using("ExtractUserSearchInnerTriggerPhotoId",
       FeatureType::USER,
       "adlog.context.info_common_attr.SEARCH_ADS_TRIGGER_PHOTO_ID:int64"
  );

using("ExtractUserSearchInnerTriggerPage",
       FeatureType::USER,
       "adlog.context.info_common_attr.SEARCH_ADS_TRIGGER_PAGE:int64"
  );

using("ExtractUserSearchInnerTriggerPosition",
       FeatureType::USER,
       "adlog.context.info_common_attr.SEARCH_ADS_TRIGGER_POSITION:int64"
  );