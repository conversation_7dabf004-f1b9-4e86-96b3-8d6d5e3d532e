using("ExtractUserUniverseClickActionSeqAdStyleMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_AD_STYLE:int64_list",
  "adlog.context.info_common_attr.AD_STYLE:int64",
  30,
  hard_search_seq_with_single_key,
  30
);

using("ExtractUserUniverseClickActionSeqIsPlayableMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_IS_PLAYABLE:int64_list",
  "adlog.item.ad_dsp_info.common_info_attr.IS_PLAYABLE:int64",
  30,
  hard_search_seq_with_single_key,
  30
);

using("ExtractUserUniverseClickActionSeqIndustryTagMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_INDUSTRY_TAG:int64_list",
  "adlog.item.ad_dsp_info.common_info_attr.PHOTO_CRM_DEFINE_THIRD_INDUSTRY:string",
  30,
  hard_search_seq_with_single_key,
  30
);

using("ExtractUserUniverseClickActionSeqMediumCooperationModeMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_MEDIUM_COOPERATION_MODE:int64_list",
  "adlog.context.info_common_attr.COOPERATION_MODE:int64",
  30,
  hard_search_seq_with_single_key,
  30
);

using("ExtractUserUniverseClickActionSeqMaterialFeatureTypeMask",
  FeatureType::DENSE_COMBINE,
  "adlog.user_info.common_info_attr.UNIVERSE_AD_CLICK_ACTION_SEQ_MATERIAL_FEATURE_TYPE:int64_list",
  "adlog.item.ad_dsp_info.common_info_attr.MATERIAL_FEATURE_TYPE:int64",
  30,
  hard_search_seq_with_single_key,
  30
);