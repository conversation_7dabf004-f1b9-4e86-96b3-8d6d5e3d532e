#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import Leaf<PERSON><PERSON>rieve<PERSON>
from dragonfly.common_leaf_util import check_arg, strict_types

class TableLiteAllIdRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "table_lite_all"

  @property 
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("item_table")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("item_attrs", []))

  @strict_types
  def will_add_items(self) -> bool:
    return False

class AdDeserializeProtoDataframeRetriever(LeafRetriever): 
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_retrieval_dataframe_from_pb"

  @strict_types
  def will_add_items(self) -> bool:
    return False

  @property 
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("from_column"))
    attrs.add(self._config.get("msg_timestamp_column"))
    attrs.add(self._config.get("item_table"))
    return attrs

  @property 
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    item_table = self._config.get("item_table")
    for attr in self._config.get("column_extract", []):
      if "target" in attr:
        attrs.add(item_table + "::" + attr["target"])
    return attrs

class AdItemAccumulateRetriever(LeafRetriever): 
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_item_accumulate_retriever"

  @strict_types
  def will_add_items(self) -> bool:
    return False

  @property 
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("from_column"))
    return attrs

  @property 
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("batch_full_flag"));
    return attrs

  @property 
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set();
    accumulate_tables = self._config.get("accumulate_tables", {})
    if "table_name" in accumulate_tables and "column" in accumulate_tables:
      table_name = accumulate_tables["table_name"]
      columns = accumulate_tables["column"]
      for column in columns:
        attrs.add(table_name + "::" + column)
    return attrs

  @property 
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set();
    accumulate_tables = self._config.get("accumulate_tables", {})
    if "table_name" in accumulate_tables and "column" in accumulate_tables:
      table_name = accumulate_tables["table_name"]
      columns = accumulate_tables["column"]
      for column in columns:
        attrs.add(table_name + "::" + column)
    return attrs

class AdStrategyDataFetchRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_strategy_data_fetch_retriever"
  
  @strict_types
  def will_add_items(self) -> bool:
    return False

class AdStrategyGlobalDataFetchRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_strategy_global_data_fetch"
  
  @strict_types
  def will_add_items(self) -> bool:
    return False

class AccountAllMcbCampaignDataFetchRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "account_all_mcb_campaign_data_fetch_retriever"

  @strict_types
  def will_add_items(self) -> bool:
    return False

class AdBackupDataPrepareRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_backup_data_prepare_retriever"
  
  @strict_types
  def will_add_items(self) -> bool:
    return False

class AdRollbackDataPrepareRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_rollback_data_prepare_retriever"
  
  @strict_types
  def will_add_items(self) -> bool:
    return False

class AdGetAllBackupDataRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_get_all_backup_data_retriever"
  
  @strict_types
  def will_add_items(self) -> bool:
    return False

class AdGetAllRollbackDataRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_get_all_rollback_data_retriever"

  @strict_types
  def will_add_items(self) -> bool:
    return False

class MessageQueueRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "message_queue_retriever"

  @strict_types
  def will_add_items(self) -> bool:
    return False

  def _check_config(self) -> None:
    check_arg("message_queue_type" in self._config, "必须写入message_queue_type参数")
    check_arg("max_queue_size" in self._config, "必须写入max_queue_size参数")
    check_arg("queue_number" in self._config, "必须写入queue_number参数")

class AdPidMessageQueueDispatchRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_pid_message_queue_retriever"


class AccountAllUnitDataFetchRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "account_all_unit_data_fetch_retriever"

  @classmethod
  def get_item_attr(self, conf) -> str:
    if "table_name" in conf and "column" in conf:
      return conf["table_name"] + "::" + conf["column"]
    return ""

  @property 
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set();
    attrs.add(self.get_item_attr(self._config.get("account_id", {})))
    attrs.add(self.get_item_attr(self._config.get("charge_tag", {})))
    attrs.add(self.get_item_attr(self._config.get("unit_id_list", {})))
    return attrs

class PidStrategyDataFetchRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_strategy_data_fetch"

class CommonRecoResultKafkaReceiverMixer(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_reco_result_kafka_receiver"
class CommonRecoResultKafkaSendMixer(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_reco_result_kafka_send"


