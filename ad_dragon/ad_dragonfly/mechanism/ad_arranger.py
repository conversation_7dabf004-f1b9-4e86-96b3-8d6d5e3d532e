#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import <PERSON><PERSON>rra<PERSON>
from dragonfly.common_leaf_util import check_arg, strict_types

class AdBidServerItemFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_bid_server_item_filter"
    
  @property 
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property 
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("whitelist_on_attr"))
    attrs.add(self._config.get("whitelist_kconf_path"))
    attrs.add(self._config.get("ad_log_account_delivery_type_column"))
    attrs.add(self._config.get("ad_log_campaign_bid_type_column"))
    attrs.add(self._config.get("ad_budget_campaign_id_column"))
    attrs.add(self._config.get("ad_budget_campaign_bid_type_column"))
    attrs.add(self._config.get("ad_budget_budget_status_column"))

    filter_flags = self._config.get("filter_flags", [])
    for filter_flag in filter_flags:
      attrs.add(filter_flag)

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class BidMsgItemFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bid_msg_item_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self.get_input("account_delivery_type"))
    attrs.add(self.get_input("campaign_bidtype"))
    attrs.add(self.get_input("bid_type"))
    attrs.add(self.get_input("account_id"))
    attrs.add(self.get_input("campaign_id"))
    attrs.add(self.get_input("ocpc_action_type"))
    attrs.add(self.get_input("deep_conversion_type"))
    attrs.add(self.get_input("need_rollback"))
    attrs.add(self.get_input("product_id"))
    attrs.add(self.get_input("hosting_cpa_bid"))
    attrs.add(self.get_input("hosting_roi_ratio"))
    return attrs

  def get_input(self, cfg_obj) -> str:
    config_obj = dict(self._config.get(cfg_obj, {}))
    if "item_table" in config_obj and "column" in config_obj:
      return config_obj["item_table"] + "::" + config_obj["column"]
    else:
      return ""
    

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs
