#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from .ad_arranger import *
from .ad_enricher import *
from .ad_observer import *
from .ad_retriever import *
from .ad_mixer import *

class AdApiMixin(CommonLeafBaseMixin):
  '''
  广告引擎 Process API 接口的 MixIn 实现
  '''
  def ad_fetch_dataframe_pb_from_kafka(self, **kwargs):
    '''
    AdKafkaMessageFetchEnricher
    ------
    同步方式从 kafka fetch 一个 string, 解析为 DataFrame proto 类型数据

    参数配置
    ------
    `kafka_topic`: [string] 指定要订阅的 kafka topic

    `kafka_group`: [string] 指定消费 kafka 的 consumer_group

    `kafka_params`: [string] 消费 kafka 的初始化参数

    `kafka_read_timeout`: [int] 消费一个 kafka msg 的超时时间

    `use_shard`: [bool] 是否使用 shard 信息更新新配置

    `tag_config`: [dict] 需要订阅的 tags
      - `shard_0`: [string] 分片 0 订阅的 tag
      - `shard_1`: [string] 分片 1 订阅的 tag

    调用示例
    ------
    ``` python
    .ad_fetch_dataframe_pb_from_kafka(
      kafka_group = "dragon_bidserver_adlog_group_test",
      kafka_topic = "ad_log_for_algo_bidserver_graph",
      proto_message = "kuaishou.ad.dw.AdLogForAlgo",
      item_table = "AdLogTable",
      item_key_path = "campaign_id",
      proto_to_column_map = [
        dict(column_name="account_id", path="account_id"),
        dict(column_name="campaign_id", path="campaign_id"),
        dict(column_name="unit_id", path="unit_id")
      ]
    )
    ```
    '''
    self._add_processor(AdKafkaMessageFetchEnricher(kwargs))
    return self;

  def ad_fetch_from_kafka(self, **kwargs):
    '''
    AdKafkaMessageFetchEnricher
    ------
    同步方式从 kafka fetch 一个 string, 解析为 proto 类型数据

    参数配置
    ------
    `kafka_topic`: [string] 指定要订阅的 kafka topic

    `kafka_group`: [string] 指定消费 kafka 的 consumer_group

    `kafka_params`: [string] 消费 kafka 的初始化参数

    `kafka_read_timeout`: [int] 消费一个 kafka msg 的超时时间

    `use_shard`: [bool] 是否使用 shard 信息更新新配置

    调用示例
    ------
    ``` python
    .ad_fetch_dataframe_pb_from_kafka(
      kafka_group = "dragon_bidserver_adlog_group_test",
      kafka_topic = "ad_log_for_algo_bidserver_graph",
      proto_message = "kuaishou.ad.dw.AdLogForAlgo",
      item_table = "AdLogTable",
      item_key_path = "campaign_id",
      proto_to_column_map = [
        dict(column_name="account_id", path="account_id"),
        dict(column_name="campaign_id", path="campaign_id"),
        dict(column_name="unit_id", path="unit_id")
      ]
    )
    ```
    '''
    self._add_processor(AdKafkaFetchEnricher(kwargs))
    return self;

  def pid_test_message_fetch(self, **kwargs):
    '''
    '''
    self._add_processor(PidTestMessageFetchEnricher(kwargs))
    return self;

  def ad_fetch_kafka_data_from_queue(self, **kwargs):
    '''
    '''
    self._add_processor(AdKafkaQueueGetEnricher(kwargs))
    return self;

  def pid_data_save_observer(self, **kwargs):
    '''
    '''
    self._add_processor(PidDataSaveObserver(kwargs))
    return self;

  def pid_common_config(self, **kwargs):
    '''
    '''
    self._add_processor(PidCommonConfigEnricher(kwargs))
    return self;

  def pid_strategy_core_enricher(self, **kwargs):
    '''
    '''
    self._add_processor(PidStrategyCoreEnricher(kwargs))
    return self;

  def pid_universe_dark_auto_control_trigger_enricher(self, **kwargs):
    '''
    '''
    self._add_processor(PidUniverseDarkAutoControlTriggerEnricher(kwargs))
    return self;

  def pid_universe_dark_auto_control_exec(self, **kwargs):
    '''
    '''
    self._add_processor(PidUniverseDarkAutoControlExec(kwargs))
    return self;

  def pid_universe_ecpc_trigger_enricher(self, **kwargs):
    '''
    '''
    self._add_processor(PidUniverseEcpcTriggerEnricher(kwargs))
    return self;

  def pid_universe_bonus_control_trigger_enricher(self, **kwargs):
    '''
    '''
    self._add_processor(PidUniverseBonusControlTriggerEnricher(kwargs))
    return self;

  def pid_universe_bonus_control_exec_enricher(self, **kwargs):
    '''
    '''
    self._add_processor(PidUniverseBonusControlExecEnricher(kwargs))
    return self;

  def pid_universe_bonus_control_exec(self, **kwargs):
    '''
    '''
    self._add_processor(PidUniverseBonusControlExec(kwargs))
    return self;

  def ad_fetch_dataframe_pb_from_log_file(self, **kwargs):
      '''
      AdTestMessageFetchEnricher
      ------
      同步方式从测试文件中获取一个 string, 解析为 DataFrame proto 类型数据
      调用示例
      ------
      ``` python
      .ad_fetch_dataframe_pb_from_log_file(
      )
      ```
      '''
      self._add_processor(AdTestMessageFetchEnricher(kwargs))
      return self;

  def ad_budget_fetch_dataframe_pb_from_kafka(self, **kwargs):
    '''
    AdBudgetKafkaMessageFetchEnricher
    ------
    同步方式从 kafka fetch 一个 string, 解析为 DataFrame proto 类型数据

    参数配置
    ------
    `kafka_topic`: [string] 指定要订阅的 kafka topic

    `kafka_group`: [string] 指定消费 kafka 的 consumer_group

    `kafka_params`: [string] 消费 kafka 的初始化参数

    `kafka_read_timeout`: [int] 消费一个 kafka msg 的超时时间

    `use_shard`: [bool] 是否使用 shard 信息更新新配置

    `tag_config`: [dict] 需要订阅的 tags
      - `shard_0`: [string] 分片 0 订阅的 tag
      - `shard_1`: [string] 分片 1 订阅的 tag

    调用示例
    ------
    ``` python
    .ad_budget_fetch_dataframe_pb_from_kafka(
      kafka_group = "dragon_bidserver_adlog_group_test",
      kafka_topic = "ad_log_for_algo_bidserver_graph",
      proto_message = "kuaishou.ad.dw.AdLogForAlgo",
      item_table = "AdLogTable",
      item_key_path = "campaign_id",
      proto_to_column_map = [
        dict(column_name="account_id", path="account_id"),
        dict(column_name="campaign_id", path="campaign_id"),
        dict(column_name="unit_id", path="unit_id")
      ]
    )
    ```
    '''
    self._add_processor(AdBudgetKafkaMessageFetchEnricher(kwargs))
    return self;


  def ad_subflow_shard_id_enrich(self, **kwargs):
    '''
    AdSubflowShardIdEnricher
    ------
    使用DataFrame 指定的column 计算 subflow 使用的 shardid， 将结果存入指定的 OutputColumn，同时把第一条结果写入 CommonColumn 中

    参数配置
    ------
    `mod_dividend_column`: [string] 用来计算 shard_id 的输入列
    `mod_base`: [int] 取模 base, 与gflags 配置的 subflow_thread_size 相同
    `output_column`: [string] 要输出的 column name, 同时输出 ItemTable 和 CommonTable

    调用示例
    ------
    ``` python
    ad_subflow_shard_id_enrich(
      mod_dividend_column = "account_id",
      mod_base = 64,
      output_column = "subflow_shard_id"
    )
    ```
    '''
    self._add_processor(AdSubflowShardIdEnricher(kwargs))
    return self;


  def ad_get_thread_info(self, **kwargs):
    '''
    AdThreadInfoEnricher
    ------
    获取当前线程 ID 的 hash 值， 存入 common_attr 中

    参数配置
    ------
    `output_attr`: [string] 要输出的 common_attr

    调用示例
    ------
    ``` python
    ad_get_thread_info(output_attr="thread_id")
    ```
    '''
    self._add_processor(AdThreadInfoEnricher(kwargs))
    return self;

  def ad_table_lite_enrich(self, **kwargs):
    '''
    AdTableLiteEnricher
    ------
    从简版索引中查询数据

    参数配置
    ------
    `account_info`: [dict] 选配项，通过查询 Account 表获取信息的配置
      - `id_column`: [string] 查询 Account 表用的 ID 列名
      - `column_config`: [list] 要查询的列名 以及输出的列名，每个元素为 dict(target="xxx", origin="YYY") 表示将 Account 表里的 YYY 列输出到目标表的 xxx 列

    `campaign_info`: [dict] 选配项，通过查询 Campaign 表获取信息的配置
      - `id_column`: [string] 查询 Campaign 表用的 ID 列名
      - `column_config`: [list] 要查询的列名 以及输出的列名，每个元素为 dict(target="xxx", origin="YYY") 表示将 Campaign 表里的 YYY 列输出到目标表的 xxx 列

    调用示例
    ------
    ``` python
    .ad_table_lite_enrich(
      item_table = "AdLogTable",
      account_info = dict (
        id_column = 'account_id',
        column_config = [
          dict(target="account_delivery_type", origin="delivery_type")
        ]
      ),
      campaign_info = dict (
        id_column = 'campaign_id',
        column_config = [
          dict(target="campaign_bidtype", origin="bid_type")
        ]
      )
    )
    ```
    '''
    self._add_processor(AdTableLiteEnricher(kwargs))
    return self;
  def inner_loop_table_lite_enrich(self, **kwargs):
    '''
    InnerLoopTableLiteMixer
    ------
    从简版索引中查询数据
    调用示例
    ------
    ``` python
    .inner_loop_table_lite_enrich(
      item_table = "TriggerTable",
      unit_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
      has_bid_state_info_ptr = {"item_table" : "TriggerTable", "column" : "has_bid_state_info_ptr"},
      bid_state_info_ptr = {"item_table" : "TriggerTable", "column" : "bid_state_info_ptr"},
    )
    ```
    '''
    self._add_processor(InnerLoopTableLiteMixer(kwargs))
    return self;
  def inner_loop_build_unit_list(self, **kwargs):
    '''
    InnerLoopBuildUnitListMixer
    ------
    构建 unit_list
    调用示例
    ------
    ``` python
    .inner_loop_build_unit_list(
      item_table = "TriggerTable",
      account_id = {"item_table" : "TriggerTable", "column" : "account_id"},
      unit_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
      group_tag = {"item_table" : "TriggerTable", "column" : "group_tag"},
      account_unit_list = {"item_table" : "AccountTagTable", "column" : "unit_list"},
    )
    ```
    '''
    self._add_processor(InnerLoopBuildUnitListMixer(kwargs))
    return self;
  def inner_loop_strategy_data_fetch(self, **kwargs):
    '''
    InnerLoopStrategyDataFetchMixer
    ------
    从 redis 或者 cache取数据
    调用示例
    ------
    ``` python
    .inner_loop_strategy_data_fetch(
      item_table = "TriggerTable",
      cluster_name = "adEngineFlowProcess",
      candidate_cluster_name = "adEngineFlowProcess",
      key_prefix = "inner_ctx_"
      output_table_name = "UnitTagTable",
      cache_key_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
      group_tag = {"item_table" : "TriggerTable", "column" : "group_tag"},
    )
    ```
    '''
    self._add_processor(InnerLoopStrategyDataFetchMixer(kwargs))
    return self;
  def inner_loop_strategy_data_save(self, **kwargs):
    '''
    InnerLoopStrategyDataSaveMixer
    ------
    保存cache
    调用示例
    ------
    ``` python
    .inner_loop_strategy_data_save(
      "tables":[
        {"table_name": "AdBaseInfoTable", "table_category": "AdBaseInfo", "save_all_columns": True}
      ])
    ```
    '''
    self._add_processor(InnerLoopStrategyDataSaveMixer(kwargs))
    return self;

  def inner_loop_ocpm_check_pacing(self, **kwargs):
    '''
    InnerloopOcpmCheckPacingRateMixer
    ------
    adjust_auto_value_rate字段校验(参考AdjustValueCheckProcessor->AdjustValueCheckTask)
    调用示例
    ------
    ``` python
    .inner_loop_ocpm_check_pacing(
      item_table = "TriggerTable",
      bid_context_attr_info = {
        "last_update_adjust_timestamp" : {"item_table" : "UnitTagTable", "column" :"last_update_adjust_timestamp"},
        "relax_roi_ratio" : {"item_table" : "UnitTagTable", "column" :"relax_roi_ratio"}
      },
      util_vals_attr_info = {
        "new_adjust_auto_value_rate" : {"item_table" : "UnitTagTable", "column" :"util_vals_new_adjust_auto_value_rate"},
        "is_roas" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_roas"}
      }
    ```
    '''
    self._add_processor(InnerloopOcpmCheckPacingRateMixer(kwargs))
    return self;

  def inner_loop_monitor_mixer(self, **kwargs):
    '''
    InnerloopMonitorMixer
    ------
    内循环监控算子
    调用示例
    ------
    ``` python
    .inner_loop_monitor_mixer(
      item_table = "TriggerTable",
      bid_context_attr_info = {
        "last_update_adjust_timestamp" : {"item_table" : "UnitTagTable", "column" :"last_update_adjust_timestamp"},
        "relax_roi_ratio" : {"item_table" : "UnitTagTable", "column" :"relax_roi_ratio"}
      },
      util_vals_attr_info = {
        "new_adjust_auto_value_rate" : {"item_table" : "UnitTagTable", "column" :"util_vals_new_adjust_auto_value_rate"},
        "is_roas" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_roas"}
      }
    ```
    '''
    self._add_processor(InnerloopMonitorMixer(kwargs))
    return self;

  def inner_loop_bid_result_store_mix(self, **kwargs):
    '''
    InnerloopBidResultStoreMix
    ------
    内循环数据发送算子
    调用示例
    ------
    ``` python
    .inner_loop_bid_result_store_mix(
      item_table = "TriggerTable",
        no_diff = False,
        redis_name = "sadsaf",
        candidate_redis_name = "BidServerGraphTest",
        topic = "fsagasg",
        trace_topic = "fsafsa",
        ttl = 86400,
        bid_context_attr_info = {
          "campaign_id" : {"item_table" : "UnitTagTable", "column" :"campaign_id"},
          "account_id" : {"item_table" : "UnitTagTable", "column" :"account_id"},
        },
        util_vals_attr_info = {
          "util_vals_ad_status_tag" : {"item_table" : "UnitTagTable", "column" :"util_vals_ad_status_tag"},
          "util_vals_is_update_adjust" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_update_adjust"},
        },
        lowest_cost_context = {
          "lowest_cost_context_current_budget" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_current_budget"},
          "lowest_cost_context_past_pv_ratio" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_past_pv_ratio"},
        },
        bid_state_info = {
          "bid_state_info_online" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_online"},
          "bid_state_info_advertisable" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_advertisable"},
        },
    ```
    '''
    self._add_processor(InnerloopBidResultStoreMix(kwargs))
    return self;

  def inner_loop_ocpm_bid_context_accumulate(self, **kwargs):
    '''
    InnerloopOcpmBidContextAccumulateMixer
    ------
    从 redis 或者 cache取数据
    调用示例
    ------
    ``` python
    .inner_loop_ocpm_bid_context_accumulate(
      item_table = "TriggerTable",
      # 1-4 STRING
      msg_campaign_type ={"item_table" : "TriggerTable", "column" :"campaign_type"},
      msg_ocpc_action_type ={"item_table" : "TriggerTable", "column" :"ocpc_action_type"},
      msg_promotion_type ={"item_table" : "TriggerTable", "column" :"promotion_type"},
      msg_item_type ={"item_table" : "TriggerTable", "column" :"item_type"},
      # 5-11 INT
      msg_author_id ={"item_table" : "TriggerTable", "column" :"author_id"},
      msg_account_id ={"item_table" : "TriggerTable", "column" :"account_id"},
      msg_live_stream_id ={"item_table" : "TriggerTable", "column" :"live_stream_id"},
      msg_unit_id ={"item_table" : "TriggerTable", "column" :"unit_id"},
      msg_conv_num ={"item_table" : "TriggerTable", "column" :"conv_num"},
      msg_campaign_id ={"item_table" : "TriggerTable", "column" :"campaign_id"},
      msg_item_type_num ={"item_table" : "TriggerTable", "column" :"item_type_num"},
      # 12-14 DOUBLE
      msg_cost ={"item_table" : "TriggerTable", "column" :"cost"},
      msg_gmv ={"item_table" : "TriggerTable", "column" :"gmv"},
      msg_roi_ratio ={"item_table" : "TriggerTable", "column" :"roi_ratio"},
      # 15-17 STRING
      msg_action_type ={"item_table" : "TriggerTable", "column" :"action_type"},
      msg_group_tag ={"item_table" : "TriggerTable", "column" :"group_tag"},
      msg_bid_type ={"item_table" : "TriggerTable", "column" :"bid_type"},
      # 18-21 INT
      msg_medium_attribute ={"item_table" : "TriggerTable", "column" :"medium_attribute"},
      msg_speed_type ={"item_table" : "TriggerTable", "column" :"speed_type"},
      msg_delivery_timestamp ={"item_table" : "TriggerTable", "column" :"delivery_timestamp"},
      msg_cpa_bid ={"item_table" : "TriggerTable", "column" :"cpa_bid"},
      # 22 INT
      msg_is_soft ={"item_table" : "TriggerTable", "column" :"is_soft"},
      # 23-29 DOUBLE
      msg_target_cost ={"item_table" : "TriggerTable", "column" :"target_cost"},
      msg_target_gmv ={"item_table" : "TriggerTable", "column" :"target_gmv"},
      msg_separate_gsp_price ={"item_table" : "TriggerTable", "column" :"separate_gsp_price"},
      msg_record_gsp_price ={"item_table" : "TriggerTable", "column" :"record_gsp_price"},
      msg_pred_conv ={"item_table" : "TriggerTable", "column" :"pred_conv"},
      msg_pred_cvr_sum ={"item_table" : "TriggerTable", "column" :"pred_cvr_sum"},
      msg_pred_ctr_sum ={"item_table" : "TriggerTable", "column" :"pred_ctr_sum"},
      # 30-31 INT
      msg_price_before_billing_separate ={"item_table" : "TriggerTable", "column" :"price_before_billing_separate"},
      msg_price_after_billing_separate ={"item_table" : "TriggerTable", "column" :"price_after_billing_separate"},
      # 32-34 DOUBLE
      msg_ecpm ={"item_table" : "TriggerTable", "column" :"ecpm"},
      msg_auction_bid ={"item_table" : "TriggerTable", "column" :"auction_bid"},
      msg_price_ratio ={"item_table" : "TriggerTable", "column" :"price_ratio"},
    )
    ```
    '''
    self._add_processor(InnerloopOcpmBidContextAccumulateMixer(kwargs))
    return self;
  def inner_loop_ocpm_trigger(self, **kwargs):
    '''
    InnerLoopTriggerMixer
    ------
    限制 unit 调价频率
    调用示例
    ------
    ``` python
    .inner_loop_ocpm_trigger(
      item_table = "UnitTagTable",
      unit_id = {"item_table" : "UnitTagTable", "column" :"unit_id"},
    )
    ```
    '''
    self._add_processor(InnerLoopTriggerMixer(kwargs))
    return self;
  def ad_get_runtime_env(self, **kwargs):
    '''
    AdRuntimeEnvEnricher
    ------
    运行环境状态获取算子，可以获取分片信息, 获取指定 path 的主从状态

    参数配置
    ------
    `shard_id_out_column`: [string] shardid 的输出列名

    `zk_configs`: [list] 监听的 zk 路径，和对应输出的 common_attr，每项值为字符串或 {"dynamic_config_node"="xxx", "output"="yyy"} 的 dict 格式。
      - `dynamic_config_node`: [string] 要监控的 ZK 路径的配置，对应 预先在 dynamic_json_config.json 里定义好的一个配置。
      - `out_attr`: [string] 可选项, 选主状态设置的 common_attr 的名称。
      - `config_node_concat_shard_id`: [bool] 可选项，默认false，是否将 shard id 拼接到 dynmiac_config_node 后面

    调用示例
    ------
    ``` python
    .ad_get_runtime_env(
      shard_id_out_column = "current_shard_id",
      zk_configs = [
        "zk_dragon_bidserver_main",
        dict(dynamic_config_node="zk_dragon_bidserver_backup", output_attr="zk_backup_stat", config_node_concat_shard_id=True)
      ])
    ```
    '''
    self._add_processor(AdRuntimeEnvEnricher(kwargs))
    return self;

  def ad_parse_proto_from_kconf(self, **kwargs):
    '''
    AdParseProtoFromKconfEnricher
    ------
    将某个路径的 Kconf 配置解析为 protobuf message

    参数配置
    ------
    `proto_msg`: [string]目标 proto message 类型

    `kconf_path`: [string] kconf 配置路径

    `output_attr`: [string] 将 pb message 输出的目标 common_attr

    调用示例
    ------
    ``` python
    .ad_parse_proto_from_kconf(
      kconf_path="ad.bidServer.accountRollbackManagerConf",
      proto_msg="ks.bid_server_account.kconf.AccountRollbackManagerConfPB",
      output_attr="account_rollback_conf")
    ```
    '''
    self._add_processor(AdParseProtoFromKconfEnricher(kwargs))
    return self;

  def ad_dispatch_item_attrs_by_key(self, **kwargs):
    '''
    AdDispatchItemAttrsByKeyEnricher
    ------
    根据指定item_key list 输入拷贝原始表的attr 到目的表的attr
    支持多行操作

    参数配置
    ------
    `item_key_list`: [list] 指定 item_key list, 不配置则遍历默认表所有item_key
    `attrs`: [list] 需要分发的 atrr 列表
      - `from_table`: [string] 来自哪个表
      - `from_column`: [string] 来自哪个列
      - `type`: [string] column 类型，目前支持 nt int_list double double_list string string_list
      - `to_table`: [string] 拷贝到哪个表
      - `to_column`: [string] 拷贝到哪个列

    调用示例
    ------
    ``` python
    .ad_dispatch_item_attrs_by_key(
      item_key_list="campaign_id",
      attrs=[
        {"from_table": "a_table", "from_column": "a_column", "type": "int",
         "to_table": "b_table", "to_column": "b_column"}
        }
      ]
    )
    ```
    '''
    self._add_processor(AdDispatchItemAttrsByKeyEnricher(kwargs))
    return self

  def ad_dispatch_item2common_by_key(self, **kwargs):
    '''
    AdDispatchItem2CommonByKeyEnricher
    ------
    根据指定 item_key 输入拷贝原始表的attr 到目的表的attr
    不支持多行操作

    参数配置
    ------
    `item_key`: [string] 指定 item_key
    `attrs`: [list] 需要分发的 atrr 列表
      - `from_table`: [string] 来自哪个表
      - `from_column`: [string] 来自哪个列
      - `type`: [string] column 类型，目前支持 nt int_list double double_list string string_list
      - `output_common_attr`: [string] 拷贝到哪个 common_attr

    调用示例
    ------
    ``` python
    .ad_dispatch_item2common_by_key(
      item_key="campaign_id",
      attrs=[
        {"from_table": "a_table", "from_column": "a_column",
         "type": "int", "output_common_attr": "common_attr"}
        }
      ]
    )
    ```
    '''
    self._add_processor(AdDispatchItem2CommonByKeyEnricher(kwargs))
    return self

  def ad_dispatch_common2item_by_key(self, **kwargs):
    '''
    AdDispatchItemAttrsByKeyEnricher
    ------
    根据指定item_key 输入拷贝原始表的attr 到目的表的attr
    支持多行操作

    参数配置
    ------
    `item_key_list`: [string_list] 指定 item_key list, 不配置则遍历默认表所有 item_key
    `attrs`: [list] 需要分发的 atrr 列表
      - `from_common_attr`: [string] 来自哪个表
      - `type`: [string] column 类型，目前支持 nt int_list double double_list string string_list
      - `to_table`: [string] 拷贝到哪个表
      - `to_column`: [string] 拷贝到哪个列

    调用示例
    ------
    ``` python
    .ad_dispatch_common2item_by_key(
      item_key_list="campaign_id",
      attrs=[
        {"from_common_attr": "common_attr", "type": "Int",
         "to_table": "b_table", "to_column": "b_column"}
        }
      ]
    )
    ```
    '''
    self._add_processor(AdDispatchCommon2ItemByKeyEnricher(kwargs))
    return self


  def hot_message_add(self, **kwargs):
    '''
    HotMessageAddMixer
    ------
    消息频数 +1

    参数配置
    ------
    `use_item_key`: [string] 是否使用 item_key
    `freq_type`: [string] 统计维度
    `freq_id`: [table] 使用的 key

    调用示例
    ------
    ``` python
    .hot_message_sub(
      item_table = "AdLogTable",
      use_item_key = false,
      freq_type = "freq_campaign",
      freq_id = {"item_table" : "AdLogTable", "column" : "campiagn_id"},
    )
    ```
    '''
    self._add_processor(HotMessageAddMixer(kwargs))
    return self

  def hot_message_sub(self, **kwargs):
    '''
    HotMessageSubMixer
    ------
    消息频数 -1

    参数配置
    ------
    `use_item_key`: [string] 是否使用 item_key
    `freq_type`: [string] 统计维度
    `freq_id`: [table] 使用的 key

    调用示例
    ------
    ``` python
    .hot_message_sub(
      item_table = "AdLogTable",
      use_item_key = false,
      freq_type = "freq_campaign",
      freq_id = {"item_table" : "AdLogTable", "column" : "campiagn_id"},
    )
    ```
    '''
    self._add_processor(HotMessageSubMixer(kwargs))
    return self

  def data_prepare_init(self, **kwargs):
    '''
    数据准备算子
    ------

    参数配置
    ------
    `use_index`: [bool] 是否使用索引
    `enable_campaign_manager`: [bool] 是否使用 campaign_manager
    `enable_account_manager`: [bool] 是否使用 account_manager
    `enable_compete_campaign_manager`: [bool] 是否加载参竞数据
    `index_listener`: [list] 索引监听函数 
      - `level`: [String] 索引层级，需要和 AdInstanceType 枚举下面对应
      - `listener_name`: [String] 监听函数名，如果没有注册，需在算子中注册后使用
    `p2p_name_attrs`: [list] 加载的 p2p 文件名，如果没有注册，需先在算子中注册

    调用示例
    ------
    ``` python
    .data_prepare_init(
      use_index = True,
      enable_campaign_manager = True,
      enable_account_manager = True,
      enable_compete_campaign_manager = True,
      index_listener = [
        {"level": "UNIT", "listener_name": "bid_unit_listener"},
        {"level": "CAMPAIGN", "listener_name": "bid_campaign_listener"},
      ],
      p2p_name_attrs = ["main_cost_second_bucket_to_bid_server",
                        "author_live_duration",
                        "mcb_retention_target",
                        "mcb_best_performance_control",
                        "mcb_best_performance_control_after_admit",
                        "mcb_best_performance_control_after_admit_using_hc",
                        "performance_delivery_calibration_p2p",
                        "mcb_initial_data_bid",
                        "mcb_perform_control",
                        "performance_predict_mcb_product_p2p_cali_mix",
                        "performance_predict_mcb_campaign_p2p_cali_mix",
                        "universe_operation_boost_cpa_price",
                        "impression_curve_by_second"],
    )
    ```
    '''
    self._add_processor(DataPrepareInitMixer(kwargs))
    return self

  def bid_log_transform_enricher(self, **kwargs):
    '''
    BidLogTransformEnricher
    ------
    bid 日志字段转换

    参数配置
    ------
    `item_table` : [string] 统一输出到该表
    `inner_ocpx_action_type_enum`: [json array] 将原有日志中 ocpx_action_type 从 str -> enum int,
                                   输出字段即为 inner_ocpx_action_type_enum
      - `item_table`: 来源数据表
      - `columns`: 来自于该 item_table 的依赖字段

    调用示例
    ------
    ``` python
    .bid_log_transform_enricher(
      item_table="AdLogTable",
      inner_ocpx_action_type_enum=[{
        "item_table": "AdLogTable", "columns": ["ocpx_action_type"]
      }],
    )
    ```
    '''
    self._add_processor(BidLogTransformEnricher(kwargs))
    return self

  def budget_log_transform_enricher(self, **kwargs):
    '''
    BudgetLogTransformEnricher
    ------
    budget 日志字段转换

    参数配置
    ------
    `table` : [string] 统一输出到该表
    `inner_ocpx_action_type_enum`: [json array] 将原有日志中 ocpx_action_type 从 str -> enum int,
                                   输出字段即为 inner_ocpx_action_type_enum
      - `table`: 来源数据表
      - `columns`: 来自于该 table 的依赖字段

    调用示例
    ------
    ``` python
    .budget_log_transform_enricher(
      table="AdBudgetTable",
      inner_ocpx_action_type_enum=[{
        "table": "AdBudgetTable", "columns": ["ocpx_action_type"]
      }],
    )
    ```
    '''
    self._add_processor(BudgetLogTransformEnricher(kwargs))
    return self

  def bid_accumulator_enricher(self, **kwargs):
    self._add_processor(BidAccumulatorEnricher(kwargs))
    return self

  def mcb_log_trigger_enricher(self, **kwargs):
    self._add_processor(McbLogTriggerEnricher(kwargs))
    return self

  def mcb_budget_trigger_enricher(self, **kwargs):
    self._add_processor(McbBudgetTriggerEnricher(kwargs))
    return self

  def bid_performance_predict_enricher(self, **kwargs):
    self._add_processor(BidPerformancePredictEnricher(kwargs))
    return self

  def bid_performance_result_store_observer(self, **kwargs):
    self._add_processor(BidPerformanceResultStoreObserver(kwargs))
    return self

  def bid_data_update_enricher(self, **kwargs):
    self._add_processor(BidDataUpdateEnricher(kwargs))
    return self

  '''
  PiddEnricher
  ------
  pidd 核心调控

  参数配置
  ------
  `id`: [string] item_key 所在的 column_name

  `kp`: [string] 动态参数，指定所在的 table_name 和 column_name

  `ki`: [string] 动态参数，指定所在的 table_name 和 column_name

  `kd`: [string] 动态参数，指定所在的 table_name 和 column_name

  `kd2`: [string] 动态参数，指定所在的 table_name 和 column_name

  `i_decay`: [string] 动态参数，指定所在的 table_name 和 column_name

  `d_decay`: [string] 动态参数，指定所在的 table_name 和 column_name

  `d2_decay`: [string] 动态参数，指定所在的 table_name 和 column_name

  `last_p_error`: [string] 动态参数，指定所在的 table_name 和 column_name

  `last_i_error`: [string] 动态参数，指定所在的 table_name 和 column_name

  `last_d_error`: [string] 动态参数，指定所在的 table_name 和 column_name

  `last_d2_error`: [string] 动态参数，指定所在的 table_name 和 column_name

  `target_acc_value`: [string] 动态参数，指定所在的 table_name 和 column_name

  `current_acc_value`: [string] 动态参数，指定所在的 table_name 和 column_name

  `target_value`: [string] 动态参数，指定所在的 table_name 和 column_name

  `current_value`: [string] 动态参数，指定所在的 table_name 和 column_name

  `pidd`: [string] 动态参数，指定所在的 table_name 和 column_name

  `p_error`: [string] 动态参数，指定所在的 table_name 和 column_name

  `i_error`: [string] 动态参数，指定所在的 table_name 和 column_name

  `d_error`: [string] 动态参数，指定所在的 table_name 和 column_name

  `d2_error`: [string] 动态参数，指定所在的 table_name 和 column_name

  调用示例
  ------
  ``` python
      .mcb_update(
        id = "campaign_id",
        kp = {"item_table" : "McbPiddHyperParamterTable", "column" : "kp"},
        ki = {"item_table" : "McbPiddHyperParamterTable", "column" : "ki"},
        kd = {"item_table" : "McbPiddHyperParamterTable", "column" : "kd"},
        kd2 = {"item_table" : "McbPiddHyperParamterTable", "column" : "kd2"},
        i_decay = {"item_table" : "McbPiddHyperParamterTable", "column" : "i_decay"},
        d_decay = {"item_table" : "McbPiddHyperParamterTable", "column" : "d_decay"},
        d2_decay = {"item_table" : "McbPiddHyperParamterTable", "column" : "d2_decay"},
        last_p_error = {"item_table" : "McbBidDataTable", "column" : "p"},
        last_i_error = {"item_table" : "McbBidDataTable", "column" : "i"},
        last_d_error = {"item_table" : "McbBidDataTable", "column" : "d"},
        last_d2_error = {"item_table" : "McbBidDataTable", "column" : "d2"},
        target_acc_value = {"item_table" : "McbBidDataTable", "column" : "flow_speed"},
        current_acc_value = {"item_table" : "McbBidDataTable", "column" : "budget_speed"},
        target_value = {"item_table" : "McbBidDataTable", "column" : "base_coef"},
        current_value = {"item_table" : "McbBidDataTable", "column" : "current_coef"},
        pidd = {"item_table" : "McbBidDataTable", "column" : "budget_coef"},
        p_error = {"item_table" : "McbBidDataTable", "column" : "p"},
        i_error = {"item_table" : "McbBidDataTable", "column" : "i"},
        d_error = {"item_table" : "McbBidDataTable", "column" : "d"},
        d2_error = {"item_table" : "McbBidDataTable", "column" : "d2"}
      )
  ```
  '''
  def mcb_update(self, **kwargs):
    self._add_processor(PiddEnricher(kwargs))
    return self
  '''
  McbConfigFetchEnricher
  ------
  ad.bidServer.accountStrategyTag / ad.bidServer.accountExpConfig 配置获取算子

  参数配置
  ------
  `id`: [string] item_key 所在的 column_name

  `strategy_name`: [string] 策略名称

  `config_items`: [list] 动态参数，其中第个元素指定所需获取的配置、默认值、以及输出的 table_name 和 column_name

  调用示例
  ------
  ``` python
      .mcb_config_fetcher(
            id = "campaign_id",
            strategy_name = "mcb_budget",
            config_items = [
              {"key" : "kp", "default_value" : 1.0,  "item_table" : "McbPiddHyperParamterTable", "column" : "kp"},
              {"key" : "ki", "default_value" : 0.0,  "item_table" : "McbPiddHyperParamterTable", "column" : "ki"},
              {"key" : "kd_p", "default_value" : 0.0,  "item_table" : "McbPiddHyperParamterTable", "column" : "kd"},
              {"key" : "kd_out", "default_value" : 0.0,  "item_table" : "McbPiddHyperParamterTable", "column" : "kd2"},
              {"key" : "i_decay", "default_value" : 0.9,  "item_table" : "McbPiddHyperParamterTable", "column" : "i_decay"},
              {"key" : "d_decay", "default_value" : 0.9,  "item_table" : "McbPiddHyperParamterTable", "column" : "d_decay"},
              {"key" : "d_out_decay", "default_value" : 0.9,  "item_table" : "McbPiddHyperParamterTable", "column" : "kd2_decayp"}
            ]
          )
  ```
  '''
  def mcb_config_fetcher(self, **kwargs):
    self._add_processor(McbConfigFetchEnricher(kwargs))
    return self

  def mcb_retention_update(self, **kwargs):
    '''
    PiddRetentionEnricher
    ------
    次留调价

    参数配置
    ------

    调用示例
    ------
    ``` python
        .mcb_retention_update(
            item_table = "McbBidDataTable",
            use_window_conv = {"item_table" : "McbConfigTable", "column" : "use_window_conv"},
            use_window_thresh_base = {"item_table" : "McbConfigTable", "column" : "use_window_thresh_base"},
            kp = {"item_table" : "McbConfigTable", "column" : "kp"},
            ki = {"item_table" : "McbConfigTable", "column" : "ki"},
            kd = {"item_table" : "McbConfigTable", "column" : "kd"},
            i_decay = {"item_table" : "McbConfigTable", "column" : "i_decay"},
            min_bound = {"item_table" : "McbConfigTable", "column" : "min_bound"},
            max_bound = {"item_table" : "McbConfigTable", "column" : "max_bound"},
            min_p_bound = {"item_table" : "McbConfigTable", "column" : "min_p_bound"},
            max_p_bound = {"item_table" : "McbConfigTable", "column" : "max_p_bound"},
            min_d_bound = {"item_table" : "McbConfigTable", "column" : "min_d_bound"},
            max_d_bound = {"item_table" : "McbConfigTable", "column" : "max_d_bound"},
            count_threshold = {"item_table" : "McbConfigTable", "column" : "count_threshold"},
            target_ratio = {"item_table" : "McbConfigTable", "column" : "target_ratio"},
            global_predict_conversion = {"item_table" : "McbGlobalDataTable", "column" : "predict_conversion"},
            global_predict_deep_conversion = {"item_table" : "McbGlobalDataTable", "column" : "predict_deep_conversion"},
            predict_conversion = {"item_table" : "McbBidDataTable", "column" : "predict_conversion"},
            predict_deep_conversion = {"item_table" : "McbBidDataTable", "column" : "predict_deep_conversion"},
            window_predict_conversion = {"item_table" : "McbGlobalDataWindowTable", "column" : "window_data.predict_conversion"},
            window_predict_deep_conversion = {"item_table" : "McbGlobalDataWindowTable", "column" : "window_data.predict_deep_conversion"},
            retention_count = {"item_table" : "McbGlobalDataTable", "column" : "retention_count"},
            retention_active = {"item_table" : "McbBidDataTable", "column" : "retention_active"},
            mcb_retention_target = {"item_table" : "McbBidDataTable", "column" : "mcb_retention_target"},
            mcb_retention_target_final = {"item_table" : "McbBidDataTable", "column" : "mcb_retention_target_final"},
            retention_thresh_pid_p = {"item_table" : "McbBidDataTable", "column" : "retention_thresh_pid.p"},
            retention_thresh_pid_i = {"item_table" : "McbBidDataTable", "column" : "retention_thresh_pid.i"},
            retention_thresh_pid_d_p = {"item_table" : "McbBidDataTable", "column" : "retention_thresh_pid.d_p"},
          )
    ```
    '''
    self._add_processor(PiddRetentionEnricher(kwargs))
    return self
  def account_budget_optimization(self, **kwargs):
    '''
    AccountBudgetOptimizationEnricher
    ------
    abo 调价

    参数配置
    ------

    调用示例
    ------
    ``` python
        .account_budget_optimization(
          item_table = "AccountAllMcbCampaignBidDataTable",
          bid_data_table_name = "McbBidDataTable",
          account_id = {"item_table" : "AccountAllMcbCampaignBidDataTable", "column" : "account_id"},
          account_left_budget = {"item_table" : "AccountAllMcbCampaignBidDataTable", "column" : "account_left_budget"},
          account_all_budget = {"item_table" : "AccountAllMcbCampaignBidDataTable", "column" : "account_all_budget"},
          account_today_charge = {"item_table" : "AccountAllMcbCampaignBidDataTable", "column" : "account_today_charge"},
          account_product_name = {"item_table" : "AccountAllMcbCampaignBidDataTable", "column" : "account_product_name"},

          campaign_id = {"item_table" : "McbBidDataTable", "column" : "campaign_id"},
          ocpx_action_type = {"item_table" : "McbBidDataTable", "column" : "ocpx_action_type"},
          campaign_time_schedule = {"item_table" : "McbBidDataTable", "column" : "campaign_time_schedule"},
          product_roi_ratio = {"item_table" : "McbBidDataTable", "column" : "product_roi_ratio"},
          product_cpa_bid = {"item_table" : "McbBidDataTable", "column" : "product_cpa_bid"},
          product_name = {"item_table" : "McbBidDataTable", "column" : "product_name"},
          origin_bid_type = {"item_table" : "McbBidDataTable", "column" : "origin_bid_type"},
          create_source_type = {"item_table" : "McbBidDataTable", "column" : "create_source_type"},
          budget_opt_info_cap_bid = {"item_table" : "McbBidDataTable", "column" : "budget_opt_info_cap_bid"},
          cap_bid_type = {"item_table" : "McbBidDataTable", "column" : "cap_bid_type"},

          locked_left_budget = {"item_table" : "McbBidDataTable", "column" : "locked_left_budget"},
          today_cost = {"item_table" : "McbBidDataTable", "column" : "today_cost"},
          budget_to_conv_left_budget_string = {"item_table" : "McbBidDataTable", "column" : "budget_to_conv_left_budget_string"},
          budget_to_conv_recommend_string = {"item_table" : "McbBidDataTable", "column" : "budget_to_conv_recommend_string"},

          do_abo_simulation = {"item_table" : "McbAboHyperParamterTable", "column" : "do_abo_simulation"},
          open_pure_mcb_lock = {"item_table" : "McbAboHyperParamterTable", "column" : "open_pure_mcb_lock"},
          cpa_step = {"item_table" : "McbAboHyperParamterTable", "column" : "cpa_step"},
          budget_step = {"item_table" : "McbAboHyperParamterTable", "column" : "budget_step"},
          abo_by_performance = {"item_table" : "McbAboHyperParamterTable", "column" : "abo_by_performance"},
          abo_by_slice = {"item_table" : "McbAboHyperParamterTable", "column" : "abo_by_slice"},
          abo_by_merge_slice = {"item_table" : "McbAboHyperParamterTable", "column" : "abo_by_merge_slice"},
          do_abo_loop2 = {"item_table" : "McbAboHyperParamterTable", "column" : "do_abo_loop2"},
          min_budget = {"item_table" : "McbAboHyperParamterTable", "column" : "min_budget"},
          mcb_proportion = {"item_table" : "McbAboHyperParamterTable", "column" : "mcb_proportion"},
          step_increment = {"item_table" : "McbAboHyperParamterTable", "column" : "step_increment"},
          increment = {"item_table" : "McbAboHyperParamterTable", "column" : "increment"},
        )
    ```
    '''
    self._add_processor(AccountBudgetOptimizationEnricher(kwargs))
    return self
  def mcb_cem_update(self, **kwargs):
    '''
    PiddCemEnricher
    ------
    强化学习 cem 调价

    参数配置
    ------

    调用示例
    ------
    ``` python
        .mcb_cem_update(
          item_table = "McbBidDataTable",
          budget_coef_strategy = {"item_table" : "McbBidDataTable", "column" : "budget_coef_strategy"},
          campaign_id = {"item_table" : "McbBidDataTable", "column" : "campaign_id"},
          account_id = {"item_table" : "McbBidDataTable", "column" : "account_id"},
          today_cost = {"item_table" : "McbBidDataTable", "column" : "today_cost"},
          today_target_cost = {"item_table" : "McbBidDataTable", "column" : "today_target_cost"},
          kp = {"item_table" : "McbPiddHyperParamterTable", "column" : "kp"},
          ki = {"item_table" : "McbPiddHyperParamterTable", "column" : "ki"},
          kd_p = {"item_table" : "McbPiddHyperParamterTable", "column" : "kd_p"},
          kd_out = {"item_table" : "McbPiddHyperParamterTable", "column" : "kd_out"},
          i_decay = {"item_table" : "McbPiddHyperParamterTable", "column" : "i_decay"},
          d_decay = {"item_table" : "McbPiddHyperParamterTable", "column" : "d_decay"},
          d_out_decay = {"item_table" : "McbPiddHyperParamterTable", "column" : "d_out_decay"},
          cem_kp = {"item_table" : "McbPiddHyperParamterTable", "column" : "cem_kp"},
          cem_ki = {"item_table" : "McbPiddHyperParamterTable", "column" : "cem_ki"},
          cem_kd_p = {"item_table" : "McbPiddHyperParamterTable", "column" : "cem_kd_p"},
          cem_kp_200 = {"item_table" : "McbPiddHyperParamterTable", "column" : "cem_kp_200"},
          cem_kp_600 = {"item_table" : "McbPiddHyperParamterTable", "column" : "cem_kp_600"},
          cem_kp_1800 = {"item_table" : "McbPiddHyperParamterTable", "column" : "cem_kp_1800"},
          decay_200 = {"item_table" : "McbPiddHyperParamterTable", "column" : "decay_200"},
          decay_600 = {"item_table" : "McbPiddHyperParamterTable", "column" : "decay_600"},
          decay_1800 = {"item_table" : "McbPiddHyperParamterTable", "column" : "decay_1800"},
          bid_coef_upper = {"item_table" : "McbPiddHyperParamterTable", "column" : "bid_coef_upper"},
          bid_coef_lower = {"item_table" : "McbPiddHyperParamterTable", "column" : "bid_coef_lower"},
          bid_coef_low_range = {"item_table" : "McbPiddHyperParamterTable", "column" : "bid_coef_low_range"},
          bid_coef_up_range = {"item_table" : "McbPiddHyperParamterTable", "column" : "bid_coef_up_range"},
          budget_coef_upper = {"item_table" : "McbPiddHyperParamterTable", "column" : "budget_coef_upper"},
          budget_coef_lower = {"item_table" : "McbPiddHyperParamterTable", "column" : "budget_coef_lower"},
          budget_coef_low_range = {"item_table" : "McbPiddHyperParamterTable", "column" : "budget_coef_low_range"},
          budget_coef_up_range = {"item_table" : "McbPiddHyperParamterTable", "column" : "budget_coef_up_range"},
          instant_max_p = {"item_table" : "McbPiddHyperParamterTable", "column" : "instant_max_p"},
          max_p = {"item_table" : "McbPiddHyperParamterTable", "column" : "max_p"},
          min_speed_stat_cnt = {"item_table" : "McbPiddHyperParamterTable", "column" : "min_speed_stat_cnt"},
          daily_reset = {"item_table" : "McbPiddHyperParamterTable", "column" : "daily_reset"},
          advance_sec = {"item_table" : "McbPiddHyperParamterTable", "column" : "advance_sec"},
          cost_rate_threshold = {"item_table" : "McbPiddHyperParamterTable", "column" : "cost_rate_threshold"},
          fanstop_clear = {"item_table" : "McbPiddHyperParamterTable", "column" : "fanstop_clear"},
          reset_budget_coef = {"item_table" : "McbPiddHyperParamterTable", "column" : "reset_budget_coef"},
          reset_bid_coef = {"item_table" : "McbPiddHyperParamterTable", "column" : "reset_bid_coef"},
          init_budget_coef = {"item_table" : "McbPiddHyperParamterTable", "column" : "init_budget_coef"},
          init_bid_coef = {"item_table" : "McbPiddHyperParamterTable", "column" : "init_bid_coef"},
          speed_stat_sec = {"item_table" : "McbPiddHyperParamterTable", "column" : "speed_stat_sec"},
          use_history_stat = {"item_table" : "McbPiddHyperParamterTable", "column" : "use_history_stat"},
          max_delta_flow_speed = {"item_table" : "McbPiddHyperParamterTable", "column" : "max_delta_flow_speed"},
          use_last_step = {"item_table" : "McbPiddHyperParamterTable", "column" : "use_last_step"},
          use_cem_avg = {"item_table" : "McbPiddHyperParamterTable", "column" : "use_cem_avg"},
          action_mode_add = {"item_table" : "McbPiddHyperParamterTable", "column" : "action_mode_add"},
          action_mode_mult = {"item_table" : "McbPiddHyperParamterTable", "column" : "action_mode_mult"},
          reset_rnn_sequence = {"item_table" : "McbPiddHyperParamterTable", "column" : "reset_rnn_sequence"},
          bid_coef = {"item_table" : "McbBidDataTable", "column" : "bid_coef"},
          cem_bid_coef = {"item_table" : "McbBidDataTable", "column" : "cem_bid_coef"},
          budget_speed_200 = {"item_table" : "McbBidDataTable", "column" : "budget_speed_200"},
          flow_speed_200 = {"item_table" : "McbBidDataTable", "column" : "flow_speed_200"},
          budget_speed_600 = {"item_table" : "McbBidDataTable", "column" : "budget_speed_600"},
          flow_speed_600 = {"item_table" : "McbBidDataTable", "column" : "flow_speed_600"},
          budget_speed_1800 = {"item_table" : "McbBidDataTable", "column" : "budget_speed_1800"},
          flow_speed_1800 = {"item_table" : "McbBidDataTable", "column" : "flow_speed_1800"},
          today_charge = {"item_table" : "McbBidDataTable", "column" : "today_charge"},
          locked_left_budget = {"item_table" : "McbBidDataTable", "column" : "locked_left_budget"},
          current_ts = {"item_table" : "McbBidDataTable", "column" : "current_ts"},
          last_change_budget_ts = {"item_table" : "McbBidDataTable", "column" : "last_change_budget_ts"},
          remain_budget_ratio = {"item_table" : "McbBidDataTable", "column" : "remain_budget_ratio"},
          remain_flow_ratio = {"item_table" : "McbBidDataTable", "column" : "remain_flow_ratio"},
          pid_data_kp = {"item_table" : "McbBidDataTable", "column" : "pid_data_kp"},
          pid_data_ki = {"item_table" : "McbBidDataTable", "column" : "pid_data_ki"},
          pid_data_kd_p = {"item_table" : "McbBidDataTable", "column" : "pid_data_kd_p"},
          pid_data_kd_out = {"item_table" : "McbBidDataTable", "column" : "pid_data_kd_out"},
          p_raw = {"item_table" : "McbBidDataTable", "column" : "p_raw"},
          p_error = {"item_table" : "McbBidDataTable", "column" : "p_error"},
          i_error = {"item_table" : "McbBidDataTable", "column" : "i_error"},
          d_error = {"item_table" : "McbBidDataTable", "column" : "d_error"},
          d_out_error = {"item_table" : "McbBidDataTable", "column" : "d_out_error"},
          p_200_error = {"item_table" : "McbBidDataTable", "column" : "p_200_error"},
          p_600_error = {"item_table" : "McbBidDataTable", "column" : "p_600_error"},
          p_1800_error = {"item_table" : "McbBidDataTable", "column" : "p_1800_error"},
          history_cem_bid_coef = {"item_table" : "McbBidDataTable", "column" : "history_cem_bid_coef"},
          history_bid_coef = {"item_table" : "McbBidDataTable", "column" : "history_bid_coef"},
          history_ts = {"item_table" : "McbBidDataTable", "column" : "history_ts"},
          history_remain_flow_ratio = {"item_table" : "McbBidDataTable", "column" : "history_remain_flow_ratio"},
          history_today_charge = {"item_table" : "McbBidDataTable", "column" : "history_today_charge"},
          rl_data = {"item_table" : "McbBidDataTable", "column" : "rl_data"},
          budget_speed = {"item_table" : "McbBidDataTable", "column" : "budget_speed"},
          flow_speed = {"item_table" : "McbBidDataTable", "column" : "flow_speed"},
          last_budget_speed = {"item_table" : "McbBidDataTable", "column" : "last_budget_speed"},
          last_flow_speed = {"item_table" : "McbBidDataTable", "column" : "last_flow_speed"},
          bid_avg_coef = {"item_table" : "McbBidDataTable", "column" : "bid_avg_coef"},
          cem_avg_coef = {"item_table" : "McbBidDataTable", "column" : "cem_avg_coef"},
          budget_speed1 = {"item_table" : "McbBidDataTable", "column" : "budget_speed1"},
          flow_speed1 = {"item_table" : "McbBidDataTable", "column" : "flow_speed1"},
          budget_speed2 = {"item_table" : "McbBidDataTable", "column" : "budget_speed2"},
          flow_speed2 = {"item_table" : "McbBidDataTable", "column" : "flow_speed2"},
          budget_speed3 = {"item_table" : "McbBidDataTable", "column" : "budget_speed3"},
          flow_speed3 = {"item_table" : "McbBidDataTable", "column" : "flow_speed3"},
        )
    ```
    '''
    self._add_processor(PiddCemEnricher(kwargs))
    return self

  def abo_campaign_cpa_coef_optimal_enricher(self, **kwargs):
    self._add_processor(AboCampaignCpaCoefOptimalEnricher(kwargs))
    return self

  def final_result_update(self, **kwargs):
    self._add_processor(FinalResultUpdateEnricher(kwargs))
    return self

  def store_experiment_result_enricher(self, **kwargs):
    self._add_processor(StoreExperimentResultEnricher(kwargs))
    return self

  def kml_feature_extract(self, **kwargs):
    '''
    KmlFeatureExtractEnricher
    ------
    强化学习特征抽取

    参数配置
    ------

    调用示例
    ------
    ``` python
        .kml_feature_extract(
          budget_coef_strategy = {"item_table" : "McbBidDataTable", "column" : "budget_coef_strategy"},
          rl_model_version = {"item_table" : "McbPiddCemHyperParamterTable", "column" : "rl_model_version"},
          pid_data_kp = {"item_table" : "McbBidDataTable", "column" : "pid_data_kp"},
          flow_speed1 = {"item_table" : "McbBidDataTable", "column" : "flow_speed1"},
          flow_speed2 = {"item_table" : "McbBidDataTable", "column" : "flow_speed2"},
          flow_speed3 = {"item_table" : "McbBidDataTable", "column" : "flow_speed3"},
          budget_speed1 = {"item_table" : "McbBidDataTable", "column" : "budget_speed1"},
          budget_speed2 = {"item_table" : "McbBidDataTable", "column" : "budget_speed2"},
          budget_speed3 = {"item_table" : "McbBidDataTable", "column" : "budget_speed3"},
          remain_budget_ratio = {"item_table" : "McbBidDataTable", "column" : "remain_budget_ratio"},
          remain_flow_ratio = {"item_table" : "McbBidDataTable", "column" : "remain_flow_ratio"},
          budget_speed = {"item_table" : "McbBidDataTable", "column" : "budget_speed"},
          flow_speed = {"item_table" : "McbBidDataTable", "column" : "flow_speed"},
          last_flow_speed = {"item_table" : "McbBidDataTable", "column" : "last_flow_speed"},
          last_budget_speed = {"item_table" : "McbBidDataTable", "column" : "last_budget_speed"},
          p_error = {"item_table" : "McbBidDataTable", "column" : "p_error"},
          i_error = {"item_table" : "McbBidDataTable", "column" : "i_error"},
          d_error = {"item_table" : "McbBidDataTable", "column" : "d_error"},
          p_200_error = {"item_table" : "McbBidDataTable", "column" : "p_200_error"},
          p_600_error = {"item_table" : "McbBidDataTable", "column" : "p_600_error"},
          p_1800_error = {"item_table" : "McbBidDataTable", "column" : "p_1800_error"},
          rl_data = {"item_table" : "McbBidDataTable", "column" : "rl_data"},
          rl_model_interpreter_str = {"item_table" : "McbBidDataTable", "column" : "rl_model_interpreter_str"},
          rl_input_data = {"item_table" : "McbBidDataTable", "column" : "rl_input_data"},
        )
    ```
    '''
    self._add_processor(KmlFeatureExtractEnricher(kwargs))
    return self

  def kml_rpc_infer(self, **kwargs):
    '''
    KmlRpcInferEnricher
    ------
    强化学习rpc批量请求

    参数配置
    ------

    调用示例
    ------
    ``` python
        .kml_rpc_infer(
          batch_size = 100,
          timeout = 100,
          bid_coef_upper = {"item_table" : "McbPiddCemHyperParamterTable", "column" : "bid_coef_upper"},
          bid_coef_lower = {"item_table" : "McbPiddCemHyperParamterTable", "column" : "bid_coef_lower"},
          bid_coef_low_range = {"item_table" : "McbPiddCemHyperParamterTable", "column" : "bid_coef_low_range"},
          bid_coef_up_range = {"item_table" : "McbPiddCemHyperParamterTable", "column" : "bid_coef_up_range"},
          budget_coef_upper = {"item_table" : "McbPiddCemHyperParamterTable", "column" : "budget_coef_upper"},
          budget_coef_lower = {"item_table" : "McbPiddCemHyperParamterTable", "column" : "budget_coef_lower"},
          budget_coef_low_range = {"item_table" : "McbPiddCemHyperParamterTable", "column" : "budget_coef_low_range"},
          budget_coef_up_range = {"item_table" : "McbPiddCemHyperParamterTable", "column" : "budget_coef_up_range"},
          rl_model_switch = {"item_table" : "McbPiddCemHyperParamterTable", "column" : "rl_model_switch"},
          base_constrict = {"item_table" : "McbPiddCemHyperParamterTable", "column" : "base_constrict"},
          bid_coef = {"item_table" : "McbBidDataTable", "column" : "bid_coef"},
          cem_bid_coef = {"item_table" : "McbBidDataTable", "column" : "cem_bid_coef"},
          rl_model_interpreter_str = {"item_table" : "McbBidDataTable", "column" : "rl_model_interpreter_str"},
          rl_input_data = {"item_table" : "McbBidDataTable", "column" : "rl_input_data"},
        )
    ```
    '''
    self._add_processor(KmlRpcInferEnricher(kwargs))
    return self

  def model_transform_bid_enricher(self, **kwargs):
    '''
    ModelTransformBidEnricher
    ------
    强化学习模型输出转化为系统出价

    参数配置
    ------

    调用示例
    ------
    ``` python
        .model_transform_bid_enricher(
          no_check=True,
          item_table = "McbBidDataTable",
          debug_perf_table = "DebugPerfDataTable",
        )
    ```
    '''
    self._add_processor(ModelTransformBidEnricher(kwargs))
    return self

  def ad_redis_common_attr(self, **kwargs):
    self._add_processor(AdRedisCommonAttrEnricher(kwargs))
    return self

  def ad_redis_item_attr(self, **kwargs):
    self._add_processor(AdRedisItemAttrEnricher(kwargs))
    return self

  def ad_data_frame_write_to_redis(self, **kwargs):
    '''
    AdDataFrameWriteToRedisObserver
    ------
    将指定 DataFrame 存储到redis中，按照行存储

    参数配置
    ------
    tables 指定 DataFrame对应存储配置配置

    调用示例
    ------
    ``` python
    .ad_data_frame_write_to_redis(
      "tables":[
        {"table_name": "AdLogTable", "redis_name": "test_redis", "prefix": "log", "columns":["a", "b", "c"]},
        {"table_name": "AdLogTable1", "redis_name": "test_redis1", "prefix": "log1", "columns":["a1", "b1", "c1"]}
      ])
    ```
    '''
    self._add_processor(AdDataFrameWriteToRedisObserver(kwargs))
    return self

  def ad_write_column_to_redis(self, **kwargs):
    '''
    AdWriteColumnToRedisObserver
    ------
    将指定 DataFrame 的列数据存储到redis

    参数配置
    ------
    `redis_name`: [string] redis 的名字

    `write_redis_datas`: [list] 写 redis 的数据
                         格式[table,key_prefix,key_column,value_column,enable_column,time_out,ttl]
                         table:[string] tame 的名字
                         key_prefix:[string] key 的前缀（可选）
                         key_column:[string] key 所在列名（可选，没有输入则使用item_key）
                         value_column:[string] value 所在的列名
                         enable_column:[string] 行存储标记的列名（可选，无输入则所有行均写入）
                         time_out:[int] 超时时间，ms
                         ttl:[int] 过期时间（可选）

    调用示例
    ------
    ``` python
    .ad_write_column_to_redis(
      redis_name = "test_cluster",
      "write_redis_datas":[
        {"table": "AdLogTable", "key_prefix": "test_redis", "key_column": "log",
         "value_column":"","enable_column":"","time_out":"","ttl":""}}
      ])
    ```
    '''
    self._add_processor(AdWriteColumnToRedisObserver(kwargs))
    return self

  def ad_write_column_to_kafka(self, **kwargs):
    '''
    AdWriteColumnToKafkaObserver
    ------
    将指定 DataFrame 的列数据存储到kafka

    参数配置
    ------
    `tables`: [list] 写 kafka 的数据
                    格式[table,value_column,enable_column,topic]
                    table:[string] 表名
                    value_column:[string] value 所在的列名
                    enable_column:[string] 行存储标记的列名（可选，无输入则所有行均写入）
                    topic:[string] 写入的 topic
    调用示例
    ------
    ``` python
    .ad_write_column_to_kafka(
      "tables":[
        {"table": "AdLogTable", "value_column":"","enable_column":"","topic":""}}
      ])
    ```
    '''
    self._add_processor(AdWriteColumnToKafkaObserver(kwargs))
    return self

  def ad_data_frame_write_to_kafka(self, **kwargs):
    '''
    AdDataFrameWriteToKafkaObserver
    ------
    将指定 DataFrame 发送到kafka中，按照行发送

    参数配置
    ------
    tables 指定 DataFrame对应Kafka配置

    调用示例
    ------
    ``` python
    .ad_data_frame_write_to_kafka(
      "tables":[
        {"table_name": "AdLogTable", "topic": "test_redis", "columns":["a", "b", "c"]},
        {"table_name": "AdLogTable1", "topic": "test_redis1", "columns":["a1", "b1", "c1"]}
      ])
    ```
    '''
    self._add_processor(AdDataFrameWriteToKafkaObserver(kwargs))
    return self

  def ad_log_bid_data_write_to_redis(self, **kwargs):
    '''
    AdBidDataWriteToRedisObserver
    ------
    无diff测试工具算子

    参数配置
    ------
    TODO(zhaizhiqiang): 补充

    调用示例
    ------
    ``` python
    .ad_log_bid_data_write_to_kafka(
    )
    ```
    '''
    self._add_processor(AdLogBidDataWriteToRedisObserver(kwargs))
    return self

  def ad_bid_data_write_to_redis(self, **kwargs):
    '''
    AdBidDataWriteToRedisObserver
    ------
    无diff测试工具算子

    参数配置
    ------
    TODO(zhaizhiqiang): 补充

    调用示例
    ------
    ``` python
    .ad_bid_data_write_to_kafka(
    )
    ```
    '''
    self._add_processor(AdBidDataWriteToRedisObserver(kwargs))
    return self
  def ad_data_frame_debug_log(self, **kwargs):
    '''
    AdDataFrameDebugObserver
    ------
    debug log

    参数配置
    ------
    TODO(zhaizhiqiang): 补充

    调用示例
    ------
    ``` python
    .ad_data_frame_debug_log(
      item_table = "xxx",
      msg = "xxx"
    )
    ```
    '''
    self._add_processor(AdDataFrameDebugObserver(kwargs))
    return self

  def ad_global_data_write_to_redis(self, **kwargs):
    '''
    AdBidDataWriteToRedisObserver
    ------
    无diff测试工具算子

    参数配置
    ------
    TODO(zhaizhiqiang): 补充

    调用示例
    ------
    ``` python
    .ad_global_data_write_to_kafka(
    )
    ```
    '''
    self._add_processor(AdGlobalDataWriteToRedisObserver(kwargs))
    return self

  def ad_bid_result_store(self, **kwargs):
    '''
    AdBidResultStoreObserver
    ------
    无diff测试工具算子

    参数配置
    ------
    TODO(zhaizhiqiang): 补充

    调用示例
    ------
    ``` python
    .ad_bid_result_store(
    )
    ```
    '''
    self._add_processor(AdBidResultStoreObserver(kwargs))
    return self

  def ad_auto_bid_send(self, **kwargs):
    '''
    AdAutoBidStoreObserver
    参数配置
    ------
    TODO(liming11): 补充

    调用示例
    ------
    ``` python
    .ad_auto_bid_send(
    )
    ```
    '''
    self._add_processor(AdAutoBidStoreObserver(kwargs))
    return self

  def get_table_lite_all_id(self, **kwargs):
    '''
    TableLiteAllIdRetriever
    ------
    召回全量的id，并构造一个新的表

    参数配置
    ------
    `item_table`: [string] 构造表的名字

    `output_item_attr`: [string] id单独构造一列 item_attr

    `key_prefix`: [string] output_item_attr 增加前缀

    调用示例
    ------
    ``` python
    .get_table_lite_all_id(
      key_prefix = "account_step2_tag_",
      item_table = "Account",
      output_item_attr = "key_id")
    ```
    '''
    self._add_processor(TableLiteAllIdRetriever(kwargs))
    return self;

  def ad_dump_data_frame_to_file(self, **kwargs):
    '''
    AdDumpItemAttrToFileObserver
    ------
    将 table 的 item_attr 数据存储到本地中

    参数配置
    ------
    `root_path`: [string] 文件存储本地路径

    `storage_hour`: [int] 保存的时间

    `input_dump_infos`: [list] 是否发送 hdfs. 格式[table,column,file_name]

    调用示例
    ------
    ``` python
    .ad_dump_data_frame_to_file(
        root_path="/ad-dragon-bid-server/bid_server_campaign/",
        storage_hour = 6,
        input_dump_infos = [{"table" : "AdBackupTable", "column" : "GlobalData", file_name : "GlobalData"},
                            {"table" : "AdBackupTable", "column" : "WindowTable", file_name : "WindowTable"}]
        )
    ```
    '''
    self._add_processor(AdDumpDataFrameToFileObserver(kwargs))
    return self;

  def ad_rollback_admit_enrich(self, **kwargs):
    '''
    AdRollbackAdmitEnrich
    ------
    广告维度回滚准入

    参数配置
    ------
    `item_table`: [string] 输入的 item_table

    `input_attrs`: [list] 准入的依赖，格式[name, column]。name：准入依赖的输入，column：对应table的列
                          依赖name字段：
                          campaign_id(int64),account_id(int64),group_tag(int64),
                          ocpx_action_type(int64),first_industry_name(string),
                          campaign_type(string),product_name(string)

    `output_column_attr`: [int64] 输出的 column。0表示不需要回滚，1表示需要回滚
    `all_enable_rollback`: [int64] commom_attr。0不全部需要回滚，1全部需要回滚

    调用示例
    ------
    ``` python
    .ad_rollback_admit_enrich(
      item_table = "Account",
      input_attrs=[{"name" : "campaign_id", "column" : "campaign_id"},
                   {"name" : "account_id", "column" : "account_id"}],
      output_column_attr="need_rollback",
      all_enable_rollback="need_filter")
    ```
    '''
    self._add_processor(AdRollbackAdmitEnrich(kwargs))
    return self

  def ad_backup_data_prepare_retriever(self, **kwargs):
    '''
    AdBackupDataPrepareRetriever
    ------
    获取备份依赖数据和准入打标

    参数配置
    ------
    `item_table`: [string] 生成后的 table

    `backup_prefix`: [string] 备份时间戳前缀,用于获取上次备份时间

    `redis_name`: [string] 备份时间戳存储 redis

    `enable_backup`:[int] commom_attr 是否开启备份, 0:不开启，1:开启

    `output_attrs`: [list] 输出 dataframe 的列，格式[name, column]。
                           name：输出名字，column：对应table的列

    调用示例
    ------
    ``` python
    .ad_backup_data_prepare_retriever(
      item_table = "BackupCommomTable",
      backup_prefix = "bid_server_campaign_",
      redis_name = "BidServerGraphTest",
      enable_backup = "enable_backup")
    ```
    '''
    self._add_processor(AdBackupDataPrepareRetriever(kwargs))
    return self

  def ad_rollback_data_prepare_retriever(self, **kwargs):
    '''
    AdRollbackDataPrepareRetriever
    ------
    获取备份依赖数据和准入打标

    参数配置
    ------
    `item_table`: [string] 生成后的 table

    `enable_rollback`: [int] commom_attr 是否开启回滚, 0:不开启，1:开启

    `root_path`: [string] 文件的根目录

    `output_attrs`: [list] 输出 dataframe 的列，格式[name, column]。
                           name：输出名字，column：对应table的列
                           例如："name" : "taregt_cur", "column" : "target_cur"

    调用示例
    ------
    ``` python
    .ad_rollback_data_prepare_retriever(
      item_table = "RollbackCommomTable",
      enable_rollback = "enable_rollback",
      root_path = "/ad-dragon-bid-server/bid_server_campaign",
      output_attrs = [{"name" : "taregt_cur", "column" : "target_cur"}])
    ```
    '''
    self._add_processor(AdRollbackDataPrepareRetriever(kwargs))
    return self

  def ad_get_all_backup_data_retriever(self, **kwargs):
    '''
    AdGetAllBackupDataRetriever
    ------
    获取备份依赖数据和准入打标

    参数配置
    ------
    `item_table`: [string] 生成的 table

    `base_info`: [int] 读取的 base 数据 格式[redis_name, key_prefix, column]

    `input_attrs`: [list] 输入的数据，格式[name, table, column]。

    `output_attrs`: [list] 输出 dataframe 的数据，格式[redis_name, key_prefix, column]。
                           redis_name: redis 的名字
                           key_prefix: key 的前缀
                           column: 输出的列

    调用示例
    ------
    ``` python
        .ad_get_all_backup_data_retriever(
          item_table = "AdBackupTable",
          input_attrs = [{"name" : "redis_timeout", "table" : "AdBackupCommonTable:", "column" : "redis_timeout"},
                         {"name" : "redis_batch_size", "table" : "AdBackupCommonTable:", "column" : "redis_batch_size"}],
          base_info = {"redis_name" : "BidServerGraph", "table" : "McbBaseInfoTable:", "column" : "BaseTable"},
          output_attrs = [{"redis_name" : "BidServerGraph", "key_prefix" : "McbGlobalDataTable:", "column" : "GlobalTable"},
                          {"redis_name" : "BidServerGraph", "key_prefix" : "McbGlobalDataWindowTable:", "column" : "GlobalWindowTable"},
                          {"redis_name" : "BidServerGraph", "key_prefix" : "McbBidDataWindowTable:", "column" : "WindowTable"},
                          {"redis_name" : "BidServerGraph", "key_prefix" : "McbBidDataTable:", "column" : "BidTable"},
                          {"redis_name" : "BidServerGraphResult", "key_prefix" : "bid_graph_", "column" : "ResultTable"}])
    ```
    '''
    self._add_processor(AdGetAllBackupDataRetriever(kwargs))
    return self

  def ad_get_all_rollback_data_retriever(self, **kwargs):
    '''
    AdGetAllRollbackDataRetriever
    ------
    获取备份依赖数据和准入打标

    参数配置
    ------
    `item_table`: [string] 生成的 table

    `base_info`: [json] 依赖的 base_info 文件，获取 id 对应的 group_tag,强依赖
                          格式[file_path,key_prefix,key_column,value_column]

    `input_attrs`: [list] 读取文件配置,格式[file_path,key_prefix,key_column,value_column]

    `output_attrs`: [list] 输出的colum。[columb]

    调用示例
    ------
    ``` python
    .ad_get_all_rollback_data_retriever(
      item_table = "AdRollbackTable",
      base_info = {"file_path" : "/ad-dragon-bid-server/bid_server_campaign/BaseTable",
                   "key_prefix" : "McbBaseInfoTable:", "key_column" : "BaseTableKey", "value_column" : "BaseTableValue"},
      input_attrs = [{"file_path" : "/ad-dragon-bid-server/bid_server_campaign/GlobalData",
                      "key_prefix" : "McbGlobalDataTable:", "key_column" : "GlobalDataKey", "value_column" : "GlobalDataValue"},
                     {"file_path" : "/ad-dragon-bid-server/bid_server_campaign/WindowTable",
                      "key_prefix" : "McbBidDataTableWindow:", "key_column" : "WindowTableKey", "value_column" : "WindowTableValue"}],
      output_attrs = [{"column" : "id"}, {"column" : "group_tag"}])
    ```
    '''
    self._add_processor(AdGetAllRollbackDataRetriever(kwargs))
    return self

  def ad_retrieval_dataframe_from_pb(self, **kwargs):
    '''
    AdDeserializeProtoDataframeRetriever
    ------
    从一个 DataTable proto 类型的 common_attr 里解析出一个 DataFrame

    参数配置
    ------
    `from_column`: [string] 要解析的列名

    `column_extract`: [list] 需要解析的列，每项值为字符串或 {origin='a', target='b'} dict 格式, 表示需要解析的column 列名，以及解析后的新列名
      - `from`: [string] pb 源列名。
      - `to`: [string] dataframe 新列名。

    调用示例
    ------
    ``` python
    .ad_retrieval_dataframe_from_pb(
      from_column = "proto_msg",
      column_extract = [
        dict(origin = "AA", target = "aa"),
        dict(origin = "BB", target = "bb"),
        dict(origin = "CC", target = "cc"),
      ]
    )
    ```
    '''
    self._add_processor(AdDeserializeProtoDataframeRetriever(kwargs))
    return self

  def ad_item_accumulate_retrieval(self, **kwargs):
    '''
    AdItemAccumulateRetriever
    ------
    累积 Item 至指定 batch_num 或 batch_interval，生成多个多行 DataFrame 并设置batch full flag

    参数配置
    ------
    `batch_full_flag`: [string] 累积成功后的标记， 1 表示成功， 0 表示没满
    `batch_num`: [int] batch num 需要累积的 item 个数, 与 batch_interval 任一满足就打标 batch_full_flag, 默认值100

    `batch_interval_ms`: [int] batch 累积时间窗， 与 batch_num 任一满足就打标 batch_full_flag， 默认值500ms

    `primary_table`: [int] 计算 batch_num 的主表

    `accumulate_tables`: [list] 需要累积的表和列，每个元素为形如 {table_name='aa', culumn=["xx", 'yy']} 的dict
      - `table_name`: [string] 需要累积的表
      - `column_name`: [list] 该表需要累积的列

    调用示例
    ------
    ``` python
    .ad_item_accumulate_retrieval(
      batch_num = 100,
      batch_interval_ms = 500,
      batch_full_flag = "batch_full",
      primary_table = 'AdBase',
      accumulate_table = [
        dict(
          table_name = "AdBase",
          column = [
            "aa",
          ]
        ),

        dict(
          table_name = "GlobalData",
          column = [
            "bb",
          ]
        )
      ]
    )
    ```
    '''
    self._add_processor(AdItemAccumulateRetriever(kwargs))
    return self

  def ad_strategy_data_save(self, **kwargs):
    '''
    AdStrategyDataSaveObserver
    ------
    将指定 DataFrame 序列化后，存储到本地全局缓存中，按照行存储

    参数配置
    ------
    tables 指定 DataFrame 对应存储配置配置
      `table_name` : [string] 指定要存储到本地全局缓存的表名
      `table_category` : [string] 指定该表所属的类别，支持的类别为 [AdBaseInfo, GlobalData, GlobalDataWindow, BidData, BidDataWindow]
      `columns` : [list of string] 指定该表中需要序列化存储的列名

    调用示例
    ------
    ``` python
    .ad_strategy_data_save(
      "tables":[
        {"table_name": "AdBaseInfoTable", "table_category": "AdBaseInfo", "columns":["a", "b", "c"]},
        {"table_name": "McbGlobalDataTable", "table_category": "GlobalData", "columns":["x", "y", "y"]}
      ])
    ```
    '''
    self._add_processor(AdStrategyDataSaveObserver(kwargs))
    return self

  def ad_strategy_abo_data_save(self, **kwargs):
    '''
    AdStrategyAboDataSaveObserver
    ------
    将指定 DataFrame 序列化后，存储到本地全局缓存中，按照行存储

    参数配置
    ------
    tables 指定 DataFrame 对应存储配置配置
      `table_name` : [string] 指定要存储到本地全局缓存的表名
      `table_category` : [string] 指定该表所属的类别，支持的类别为 [AdBaseInfo, GlobalData, GlobalDataWindow, BidData, BidDataWindow]
      `columns` : [list of string] 指定该表中需要序列化存储的列名

    调用示例
    ------
    ``` python
    .ad_strategy_abo_data_save(
      "tables":[
        {"table_name": "AdBaseInfoTable", "table_category": "AdBaseInfo", "columns":["a", "b", "c"]},
        {"table_name": "McbGlobalDataTable", "table_category": "GlobalData", "columns":["x", "y", "y"]}
      ])
    ```
    '''
    self._add_processor(AdStrategyAboDataSaveObserver(kwargs))
    return self

  def ad_strategy_data_fetch(self, **kwargs):
    '''
    AdStrategyDataFetchRetriever
    ------
    从本地全局缓存或redis获取策略数据，并将 PB 格式转换为对应的DataFrame

    参数配置
    ------
    `input_table_name`: [string] 输入的 DataFrame 表名

    `cluster_name`: [string] redis 集群名称

    `base_info_redis_key_prefix`: [string] BaseInfo 表的 redis key 前缀

    `global_data_redis_key_prefix`: [string] GlobalData 表的 redis key 前缀

    `global_data_window_redis_key_prefix`: [string] GlobalDataWindow 表的 redis key 前缀

    `bid_data_redis_key_prefix`: [string] BidData 表的 redis key 前缀

    `bid_data_window_redis_key_prefix`: [string] BidDataWindow 表的 redis key 前缀

    调用示例
    ------
    ``` python
    .ad_strategy_data_fetch(
      input_table_name = "AdLogTable",
      cluster_name = "BidServerGraph",
      base_info_redis_key_prefix = "McbBaseInfoTable:",
      global_data_redis_key_prefix = "McbGloabDataTable:",
      global_data_window_redis_key_prefix = "McbGloabDataTableWindow:",
      bid_data_redis_key_prefix = "McbBidDataTable:",
      bid_data_window_redis_key_prefix = "McbBidDataTableWindow:",
    )
    ```
    '''
    self._add_processor(AdStrategyDataFetchRetriever(kwargs))
    return self

  def ad_strategy_global_data_fetch(self, **kwargs):
    '''
    AdStrategyGlobalDataFetchRetriever
    ------
    从本地全局缓存或redis获取策略数据，并将 PB 格式转换为对应的DataFrame

    参数配置
    ------
    `input_table_name`: [string] 输入的 DataFrame 表名

    `cluster_name`: [string] redis 集群名称

    `base_info_redis_key_prefix`: [string] BaseInfo 表的 redis key 前缀

    `global_data_redis_key_prefix`: [string] GlobalData 表的 redis key 前缀

    `global_data_window_redis_key_prefix`: [string] GlobalDataWindow 表的 redis key 前缀

    `bid_data_redis_key_prefix`: [string] BidData 表的 redis key 前缀

    `bid_data_window_redis_key_prefix`: [string] BidDataWindow 表的 redis key 前缀

    调用示例
    ------
    ``` python
    .ad_strategy_global_data_fetch(
      input_table_name = "AdLogTable",
      cluster_name = "BidServerGraph",
      base_info_redis_key_prefix = "McbBaseInfoTable:",
      global_data_redis_key_prefix = "McbGloabDataTable:",
      global_data_window_redis_key_prefix = "McbGloabDataTableWindow:",
      bid_data_redis_key_prefix = "McbBidDataTable:",
      bid_data_window_redis_key_prefix = "McbBidDataTableWindow:",
    )
    ```
    '''
    self._add_processor(AdStrategyGlobalDataFetchRetriever(kwargs))
    return self

  def ocpm_account_cache_save_mixer(self, **kwargs):
    '''
    OcpmAccountCacheSaveMixer
    ------
    ocpm account 全表保存 cache

    参数配置
    ------
    tables 指定 DataFrame对应存储配置配置

    调用示例
    ------
    ``` python
    .ad_data_frame_write_to_redis(
      "tables":[
        {"table_name": "AdLogTable", "redis_name": "test_redis", "prefix": "log", "columns":["a", "b", "c"]},
        {"table_name": "AdLogTable1", "redis_name": "test_redis1", "prefix": "log1", "columns":["a1", "b1", "c1"]}
      ])
    ```
    '''
    self._add_processor(OcpmAccountCacheSaveMixer(kwargs))
    return self

  def outer_ocpm_table_fetch_mixer(self, **kwargs):
    '''
    OuterOcpmTableFetchMixer
    ------
    从本地全局缓存或redis获取策略数据，并将 PB 格式转换为对应的DataFrame

    参数配置
    ------

    `candidate_cluster_name`: [string] 测试 redis 集群名

    `cluster_name`: [string] redis 集群名

    `charge_tag_column_name`: [string] 输出的 charge_tag 列名

    `redis_key_column_name`: [string] 输出的 redis key 列名

    `local_cache_key_column_name`: [int] 输出的 local cache key 列名

    `group_tag`: [string]  trigger table 中 group_tag

    `unit_id`: [int]  trigger table 中 unit_id 列

    `account_id`: [int]  trigger table 中 account_id 列

    `campaign_type`: [int] trigger table 中 campaign_type 列

    `ocpx_action_type`: [int] trigger table 中 group_tag 列

    `deep_conversion_type`: [int] trigger table 中 deep_conversion_type 列

    `soft_hard_code`" [int] trigger table 中 soft_hard_code 列

    `placement_type_code`: [int] trigger table 中 placement_type_code 列

    `mcb_type`: [int] trigger table 中 mcb_type 列

    `charge_tag`: [int]  trigger table 中 charge_tag 列

    `output_tables`: [list]
        [
          {
            "key_column": "account_id",
            "table_name": "AccountTable',
            "redis_prefix": "ocpm_account_account_table",
            "need_charge_tag_key": false
          }
        ]

    调用示例
    ------
    ``` python
    .outer_ocpm_table_fetch_mixer(
      item_table = 'TriggerTable',
      candidate_cluster_name = 'BidServerGraphTest',
      cluster_name = 'BidServerGraph',
      charge_tag_column_name = 'charge_tag',
      redis_key_column_name = 'redis_key',
      local_cache_key_column_name = 'local_cache_key',
      group_tag = {"item_table" : "TriggerTable", "column" : "group_tag"},
      unit_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
      account_id = {"item_table" : "TriggerTable", "column" : "account_id"},
      campaign_type = {"item_table" : "TriggerTable", "column" : "campaign_type"},
      ocpx_action_type = {"item_table" : "TriggerTable", "column" : "ocpx_action_type"},
      deep_conversion_type = {"item_table" : "TriggerTable", "column" : "deep_conversion_type"},
      soft_hard_code = {"item_table" : "TriggerTable", "column" : "soft_hard_code"},
      placement_type_code = {"item_table" : "TriggerTable", "column" : "placement_type_code"},
      mcb_type = {"item_table" : "TriggerTable", "column" : "mcb_type"},
      charge_tag = {"item_table" : "TriggerTable", "column" : "charge_tag"},

      output_tables = [
          dict(key_column="account_id",
            table_name="AccountTable",
            redis_prefix="ocpm_account_table",
            output_table_key_column="account_table_key",
            need_charge_tag_key=False),
          dict(key_column="account_id",
              table_name="AccountTagTable",
              redis_prefix="ocpm_account_tag_table",
              output_table_key_column="account_tag_table_key",
              need_charge_tag_key=True),
          dict(key_column="unit_id",
              table_name="UnitTable",
              redis_prefix="ocpm_unit_table",
              output_table_key_column="unit_table_key",
              need_charge_tag_key=False),
          dict(key_column="unit_id",
              table_name="UnitTagTable",
              redis_prefix="ocpm_unit_tag_table",
              output_table_key_column="unit_tag_table_key",
              need_charge_tag_key=True)
        ]
    )
    ```
    '''
    self._add_processor(OuterOcpmTableFetchMixer(kwargs))
    return self

  def ocpm_account_cpa_coef_optimal_mixer(self, **kwargs):
    '''
    OcpmAccountCpaCoefOptimalMixer
    ------
    ocpm 账户最优调价算子

    参数配置
    ------
    `item_table`: 输入表 unit 表
    `group_tag`: [int] 回流预估的 group_tag
    `unit_id`: [int] unit_id
    `output_table_name`: [stirng] 回流预估表明
    `output_map_column`: [string] 存储 map 的列名

    调用示例
    ------
    ``` python
    .back_flow_fetch_data_mixer(
        item_table = "UnitTable",
        group_tag = {"item_table" : "UnitTable", "column" : "back_flow_group_tag"},
        unit_id = {"item_table" : "UnitTable", "column" : "unit_id"},
        output_table_name = "BackFlowUnitTagTable",
        output_map_column = "moss_map"
      )
    ```
    '''
    self._add_processor(OcpmAccountCpaCoefOptimalMixer(kwargs))
    return self

  def back_flow_fetch_data_mixer(self, **kwargs):
    '''
    BackFlowFetchDataMixer
    ------
    从本地全局缓存或redis获取策略数据，并将 PB 格式转换为对应的DataFrame

    参数配置
    ------
    `item_table`: 输入表 unit 表
    `group_tag`: [int] 回流预估的 group_tag
    `unit_id`: [int] unit_id
    `output_table_name`: [stirng] 回流预估表明
    `output_map_column`: [string] 存储 map 的列名

    调用示例
    ------
    ``` python
    .back_flow_fetch_data_mixer(
        item_table = "UnitTable",
        group_tag = {"item_table" : "UnitTable", "column" : "back_flow_group_tag"},
        unit_id = {"item_table" : "UnitTable", "column" : "unit_id"},
        output_table_name = "BackFlowUnitTagTable",
        output_map_column = "moss_map"
      )
    ```
    '''
    self._add_processor(BackFlowFetchDataMixer(kwargs))
    return self

  def back_flow_update_cache_mixer(self, **kwargs):
    '''
    BackFlowUpdateCacheMixer
    ------
    从本地全局缓存或redis获取策略数据，并将 PB 格式转换为对应的DataFrame

    参数配置
    ------

    `item_table`: 输入表 trigger 表
    `group_tag`: [int] 回流预估的 group_tag
    `unit_id`: [int] unit_id
    `moss_map_key_list`: [list] moss map key list
    `moss_map_value_list`: [list] moss map value list

    调用示例
    ------
    ``` python
    .back_flow_fetch_data_mixer(
        item_table = "TriggerTable",
        group_tag = {"item_table" : "TriggerTable", "column" : "group_tag"},
        unit_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
        moss_map_key_list = {"item_table" : "TriggerTable", "column" : "moss_map_key_list"},
        moss_map_value_list = {"item_table" : "TriggerTable", "column" : "moss_map_value_list"}
      )
    ```
    '''
    self._add_processor(BackFlowUpdateCacheMixer(kwargs))
    return self


  def bid_fill_base_info_enricher(self, **kwargs):
    '''
    BidFillBaseInfoEnricher
    ------
    AdBaseInfoTable 信息填充

    参数配置
    ------
    `input_table` : [string] 输入的表名
    `base_info_table` : [string] 输出的 ad_base_info_table 表名
    `mcb_level` : [int] 指定 MCBLevel

    调用示例
    ------
    ``` python
    .bid_fill_base_info_enricher(
      input_table="AdLogTable",
      ad_base_info_table="McbBaseInfoTable",
      mcb_level=2
    )
    ```
    '''
    self._add_processor(BidFillBaseInfoEnricher(kwargs))
    return self

  def budget_fill_base_info_enricher(self, **kwargs):
    self._add_processor(BudgetFillBaseInfoEnricher(kwargs))
    return self

  def ad_bid_server_item_filter(self, **kwargs):
    '''
    AdBidServerItemFilterArranger
    ------
    bidserver IO 主图过滤算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    ```
    '''
    self._add_processor(AdBidServerItemFilterArranger(kwargs))
    return self

  def bid_msg_item_filter(self, **kwargs):
    '''
    BidMsgItemFilterArranger
    ------
    bidserver IO 主图过滤算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    ```
    '''
    self._add_processor(BidMsgItemFilterArranger(kwargs))
    return self

  def ad_observe_by_sub_flow(self, **kwargs):
    '''
    AdPipelineObserver
    ------
    Ad subflow 算子, 非阻塞调度子图，深拷贝 Context

    参数配置
    ------
    `sub_flow`: [LeafFlow] 待执行的子图Leaf对象

    `pass_common_columns`: [list] 传给子图 ItemTable 列

    `pass_item_columns`: [list] 传给子图 CommonTable 列


    调用示例
    ------
    ``` python
    mcb_subflow = LeafFlow(name="mcb")
    .ad_observe_by_sub_flow(
      sub_flow = mcb_subflow,
      pass_common_columns = ["bb", "aa"]
      pass_item_columns = ["AAA", "BBB"]
    )
    ```
    '''
    self._add_processor(AdPipelineObserver(kwargs))
    return self

  def async_observe_by_sub_flow(self, **kwargs):
    '''
    AdPipelineObserver
    ------
    Ad subflow 算子, 非阻塞调度子图，深拷贝 Context

    参数配置
    ------
    `sub_flow`: [LeafFlow] 待执行的子图Leaf对象

    `pass_common_columns`: [list] 传给子图 ItemTable 列

    `pass_item_columns`: [list] 传给子图 CommonTable 列


    调用示例
    ------
    ``` python
    mcb_subflow = LeafFlow(name="mcb")
    .async_observe_by_sub_flow(
      sub_flow = mcb_subflow,
      pass_common_columns = ["bb", "aa"]
      pass_item_columns = ["AAA", "BBB"]
    )
    ```
    '''
    self._add_processor(AsyncPipelineObserver(kwargs))
    return self

  def ad_message_queue_dispatch_mixer(self, **kwargs):
    '''
    MessageQueueDispatchMixer
    ------
    ad_message_queue_dispatch_mixer

    参数配置
    ------
    `task_queue_id`: 将该条消息发送到哪一个线程的消息队列中, IntCommonAttr

    `packed_common_attrs`: 需要打包发向消息队列的CommonAttr

    `packed_table_columns`: 需要打包发向消息队列的列, 支持多表

    调用示例
    ------
    ``` python
    .ad_message_queue_dispatch_mixer(
      task_queue_id = "{{task_queue_id}}",
      packed_common_attrs = ["aa", "bb"],
      packed_table_columns = [
        {"table_name": "AA", "columns" = ["ddd", "eee"]},
        {"table_name": "BB", "columns" = ["fff", "ggg"]}
      ])
    ```
    '''
    self._add_processor(MessageQueueDispatchMixer(kwargs))
    return self

  def ad_ocpm_account_message_queue_dispatch_mixer(self, **kwargs):
    '''
    AdLogOcpmMessageQueueDispatchMixer
    ------
    ad_ocpm_account_message_queue_dispatch_mixer

    参数配置
    ------
    `task_queue_id`: 将该条消息发送到哪一个线程的消息队列中, IntCommonAttr
    `packed_common_attrs`: 需要打包发向消息队列的CommonAttr
    `packed_table_columns`: 需要打包发向消息队列的列, 支持多表
    `main_tag_send_soft_hard`: 主tag软硬广消息是否发送的flag, IntCommonAttr
    `assist_tag_send`: 子tag消息是否发送的flag, IntCommonAttr
    `assist_tag_send_soft_hard`: 子tag软硬广消息是否发送的flag, IntCommonAttr
    `column_replace_conf`: 向消息队列发送各tag消息时, 需要做的列替换逻辑, 只支持同表内不同列之间的数据替换 [可选]

    调用示例
    ------
    ``` python
    .ad_ocpm_account_message_queue_dispatch_mixer(
      task_queue_id = "{{task_queue_id}}",
      main_tag_send_soft_hard: "main_tag_send_soft_hard",
      assist_tag_send: "assist_tag_send",
      assist_tag_send_soft_hard: "assist_tag_send_soft_hard",

      packed_common_attrs = ["aa", "bb"],
      packed_table_columns = [
        {"table_name": "AA", "columns" = ["ddd", "eee"]},
        {"table_name": "BB", "columns" = ["fff", "ggg"]}
      ]),
      column_replace_conf = {
        "main_tag" : [
          {
            "table": "AdLogTable",
            "from_column": "additional_main_tag_column",
            "to_column": "group_tag"
          },
          {
            "table": "AdLogTable",
            "from_column": "additional_unknown_queue_type_column",
            "to_column": "queue_type"
          }
        ]
      }
    ```
    '''
    self._add_processor(AdLogOcpmMessageQueueDispatchMixer(kwargs))
    return self

  def ad_message_queue_retrieve(self, **kwargs):
    '''
    '''
    self._add_processor(MessageQueueRetriever(kwargs))
    return self

  def ad_pid_message_queue_retrieve(self, **kwargs):
    '''
    '''
    self._add_processor(AdPidMessageQueueDispatchRetriever(kwargs))
    return self

  def ad_timer_start(self, **kwargs):
    '''
    '''
    self._add_processor(AdTimerEnricher(kwargs))
    return self

  def ad_timer_end(self, **kwargs):
    '''
    '''
    self._add_processor(AdTimerObserver(kwargs))
    return self

  def account_all_mcb_campaign_data_fetch_retriever(self, **kwargs):
    '''
    获取 account 下所有 mcb campaign 的指定调价数据

    调用示例
    --------
    ``` python
    .account_all_mcb_campaign_data_fetch_retriever(
      ## 取这张表中的 account 下所有 mcb campaign 的数据
      input_table_name = "AdBudgetTable",
      account_id = {"item_table" : "AdBudgetTable", "column" : "account_id"},
      group_tag = {"item_table" : "AdBudgetTable", "column" : "group_tag"},
      cluster_name = "BidServerGraph",
      candidate_cluster_name = "BidServerGraphTest",
      ## 将下面这些表中的哪些列 copy 到 item_table 的哪些列中
      base_info_table_conf = {
        "table_name": "McbBaseInfoTable",
        "redis_key_prefix": "McbBaseInfoTable:",
      },
      global_data_table_conf = {
        "table_name": "McbGlobalDataTable",
        "redis_key_prefix": "McbGlobalDataTable:",
      },
      bid_data_table_conf = {
        "table_name": "McbBidDataTable",
        "redis_key_prefix": "McbBidDataTable:",
      },
    ) \
    ```
    '''
    self._add_processor(AccountAllMcbCampaignDataFetchRetriever(kwargs))
    return self

  def inner_loop_msg_admit_mixer(self, **kwargs):
    '''
    内循环准入打标

    调用示例
    --------
    ``` python
    .inner_loopr_msg_admit_mixer(
      ## 内循环准入打标
      item_name = "TriggerTable",
    ) \
    ```
    '''
    self._add_processor(InnerLoopMsgAdmitMixer(kwargs))
    return self

  def inner_loop_ocpm_calc_bid_prepare_mixer(self, **kwargs):
    '''
    内循环调价准备

    调用示例
    --------
    ``` python
    .inner_loop_ocpm_calc_bid_prepare_mixer(
      ## 内循环调价准备
      item_name = "UnitTagTable",
    ) \
    ```
    '''
    self._add_processor(InnerLoopOcpmCalcBidPrepareMixer(kwargs))
    return self

  def msg_filter_mixer(self, **kwargs):
    '''
    根据打标删除消息

    调用示例
    --------
    ``` python
    .msg_filter_mixer(
      ## 根据打标删除消息
      item_name = "TriggerTable",
    ) \
    ```
    '''
    self._add_processor(MsgFilterMixer(kwargs))
    return self

  def ocpm_accumulate_backflow_mixer(self, **kwargs):
    self._add_processor(OcpmAccumulateBackflowMixer(kwargs))
    return self

  def merchant_cost_log_consumer_mixer(self, **kwargs):
    self._add_processor(MerchantCostLogConsumerMixer(kwargs))
    return self

  def merchant_trigger_server_log_consumer_mixer(self, **kwargs):
    self._add_processor(MerchantTriggerServerLogConsumerMixer(kwargs))
    return self

  def nodiff_message_load_mix(self, **kwargs):
    self._add_processor(NodiffMessageLoadMix(kwargs))
    return self

  def diff_merchant_back_up_trigger_mixer(self, **kwargs):
    self._add_processor(DiffMerchantBackUpTriggerMixer(kwargs))
    return self

  def cost_cap_supervisor_mixer(self, **kwargs):
    self._add_processor(CostCapSupervisorMixer(kwargs))
    return self

  def lowest_cost_supervisor_mixer(self, **kwargs):
    self._add_processor(LowestCostSupervisorMixer(kwargs))
    return self

  def main_timer_trigger_thread_mixer(self, **kwargs):
    self._add_processor(MainTimerTriggerThreadMixer(kwargs))
    return self

  def mobile_main_timer_trigger_thread_mixer(self, **kwargs):
    self._add_processor(MobileMainTimerTriggerThreadMixer(kwargs))
    return self

  def ocpm_acc_explore_trigger_thread_mixer(self, **kwargs):
    self._add_processor(OcpmAccExploreTriggerThreadMixer(kwargs))
    return self

  def timer_reset_mixer(self, **kwargs):
    self._add_processor(TimerResetMixer(kwargs))
    return self

  def timer_rollback_mixer(self, **kwargs):
    self._add_processor(TimerRollbackMixer(kwargs))
    return self

  def ocpm_accumulate_mixer(self, **kwargs):
    '''
    OcpmAccumulateMixer
    ------
    ocpm account 累计数据算子

    参数配置
    ------
      - `delta_table`: [string] 表名

    调用示例
    --------
    ``` python
    .ocpm_accumulate_prepare_mixer(
      delta_table = "AccountTable",
      delta_key = "account_id",
      deta_column_prepare_input = dict(
        price_column = "price",
        action_type_column = "ocpc_action_type",
        deep_conversion_type_column = "deep_conversion_type",
        cpa_bid_column = "cpa_bid",
        cpa_coef_column = "cpa_coef",
        roi_ratio_column = "roi_ratio",
        auto_cpa_bid_column = "auto_cpa_bid",
        auto_roas_column = "auto_roas",
        target_cost_column = "target_cost",
        target_cost_attr_column = "target_cost_attr",
        deep_target_cost_column = "deep_target_cost",
        is_roi_column = "is_roi",
        is_account_bidding_column = "is_account_bidding",
        is_rta_column = "is_rta",
      ),
      deta_column_prepare_output = dict(
        expect_cv_column = "expect_cv",
        expect_deep_cv_column = "expect_deep_cv",
        expect_target_cost_column = "expect_target_cost",
        expect_deep_target_cost_column = "expect_deep_target_cost",
        cpa_coef_cost_sum_column = "cpa_coef_cost_sum",
        auto_cpa_bid_cost_sum_column = "auto_cpa_bid_cost_sum",
        auto_roas_cost_sum_column = "auto_roas_cost_sum",
        target_cpa_sum_column = "target_cpa_sum",
      ),
    )
    ```
    '''
    self._add_processor(OcpmAccumulateMixer(kwargs))
    return self

  def ocpm_accumulate_prepare_mixer(self, **kwargs):
    '''
    OcpmAccumulatePrepareMixer
    ------
    ocpm account 累计数据准备算子
    参数配置
    ------
      - `delta_table`: [string] 表名


    调用示例
    --------
    ``` python
    .ocpm_accumulate_prepare_mixer(
      delta_table = "AccountTable",
      delta_key = "account_id",
      deta_column_prepare_input = dict(
        price_column = "price",
        action_type_column = "ocpc_action_type",
        deep_conversion_type_column = "deep_conversion_type",
        cpa_bid_column = "cpa_bid",
        cpa_coef_column = "cpa_coef",
        roi_ratio_column = "roi_ratio",
        auto_cpa_bid_column = "auto_cpa_bid",
        auto_roas_column = "auto_roas",
        target_cost_column = "target_cost",
        target_cost_attr_column = "target_cost_attr",
        deep_target_cost_column = "deep_target_cost",
        is_roi_column = "is_roi",
        is_account_bidding_column = "is_account_bidding",
        is_rta_column = "is_rta",
      ),
      deta_column_prepare_output = dict(
        expect_cv_column = "expect_cv",
        expect_deep_cv_column = "expect_deep_cv",
        expect_target_cost_column = "expect_target_cost",
        expect_deep_target_cost_column = "expect_deep_target_cost",
        cpa_coef_cost_sum_column = "cpa_coef_cost_sum",
        auto_cpa_bid_cost_sum_column = "auto_cpa_bid_cost_sum",
        auto_roas_cost_sum_column = "auto_roas_cost_sum",
        target_cpa_sum_column = "target_cpa_sum",
      ),
    )
    ```
    '''
    self._add_processor(OcpmAccumulatePrepareMixer(kwargs))
    return self

  def ocpm_account_diff_observer(self, **kwargs):
    '''
    OcpmAccountDiffObserver
    ------
    ocpm account diff 测试打点算子

    参数配置
    ------
      - `delta_table`: [string] 表名


    调用示例
    --------
    ``` python
    .ocpm_account_diff_observer(
      tag_table_input = dict(
        table_name = "AccountTagTable",
        expect_cv_column = "expect_cv",
        expect_deep_cv_column = "expect_deep_cv",
        expect_target_cost_column = "expect_target_cost",
        expect_deep_target_cost_column = "expect_deep_target_cost",
        cpa_coef_cost_sum_column = "cpa_coef_cost_sum",
        auto_cpa_bid_cost_sum_column = "auto_cpa_bid_cost_sum",
        auto_roas_cost_sum_column = "auto_roas_cost_sum",
        target_cpa_sum_column = "target_cpa_sum",

        retention_count = "retention_count",
        predict_conversion = "predict_conversion",
        predict_deep_conversion = "predict_deep_conversion",
        rta_cost = "rta_cost",
        expect_target_cost = "expect_target_cost",
        expect_deep_target_cost = "expect_deep_target_cost",
        cost = "cost",
        origin_cost = "origin_cost",
        price_after_billing_separate = "price_after_billing_separate",
        target_cost = "target_cost",
        deep_target_cost = "deep_target_cost",
        cv = "cv",
        target_cpa_sum = "target_cpa_sum",
      ),
    )
    ```
    '''
    self._add_processor(OcpmAccountDiffObserver(kwargs))
    return self

  def fill_table_mixer(self, **kwargs):
    '''
    FillSessionTableMixer
    ------
    Ocpm 填充 session table

    参数配置
    ------
      - `fill_column`: [list] 列名


    调用示例
    --------
    ``` python
    .fill_table_mixer(
      item_table = "TriggerTable",
      fill_column = [
        "account_id", "unit_id", "price", "ocpc_action_type", "deep_conversion_type", "cpa_bid",
        "cpa_coef", "roi_ratio", "auto_cpa_bid", "auto_roas", "target_cost", "target_cost_attr",
        "deep_target_cost", "is_roi", "is_account_bidding", "is_rta", "expect_cv", "expect_deep_cv",
        "expect_target_cost", "expect_deep_target_cost", "cpa_coef_cost_sum", "cpa_coef_cost_sum",
        "auto_cpa_bid_cost_sum", "auto_roas_cost_sum", "target_cpa_sum"
      ],
      session_tables = [
        dict(
          table_name = "UnitTable",
          item_key = "unit_id",
        ),
        dict(
          table_name = "AccountTable",
          item_key = "account_id",
        ),
      ],
    )
    ```
    '''
    self._add_processor(FillSessionTableMixer(kwargs))
    return self

  def fill_session_table_mixer(self, **kwargs):
    '''
    FillSessionTableMixer
    ------
    Ocpm 填充 session table

    参数配置
    ------
      - `fill_column`: [list] 列名


    调用示例
    --------
    ``` python
    .fill_session_table_mixer(
      item_table = "TriggerTable",
      fill_column = [
        "account_id", "unit_id", "price", "ocpc_action_type", "deep_conversion_type", "cpa_bid",
        "cpa_coef", "roi_ratio", "auto_cpa_bid", "auto_roas", "target_cost", "target_cost_attr",
        "deep_target_cost", "is_roi", "is_account_bidding", "is_rta", "expect_cv", "expect_deep_cv",
        "expect_target_cost", "expect_deep_target_cost", "cpa_coef_cost_sum", "cpa_coef_cost_sum",
        "auto_cpa_bid_cost_sum", "auto_roas_cost_sum", "target_cpa_sum"
      ],
      session_tables = [
        dict(
          table_name = "UnitTable",
          item_key = "unit_id",
        ),
        dict(
          table_name = "AccountTable",
          item_key = "account_id",
        ),
      ],
    )
    ```
    '''
    self._add_processor(FillSessionTableMixer(kwargs))
    return self

  def inner_loop_ocpm_calc_pacing_rate_acc_mixer(self, **kwargs):
    '''
    内循环加速探索调价

    调用示例
    --------
    ``` python
    .inner_loop_ocpm_calc_pacing_rate_acc_mixer(
      ## 内循环加速探索调价
      item_name = "UnitTagTable",
    ) \
    ```
    '''
    self._add_processor(InnerLoopOcpmCalcPacingRateAccMixer(kwargs))
    return self

  def inner_loop_ocpm_calc_pacing_rate_normal_mixer(self, **kwargs):
    '''
    内循环普通调价

    调用示例
    --------
    ``` python
    .inner_loop_ocpm_calc_pacing_rate_normal_mixer(
      ## 内循环普通调价
      item_name = "UnitTagTable",
    ) \
    ```
    '''
    self._add_processor(InnerLoopOcpmCalcPacingRateNormalMixer(kwargs))
    return self

  def inner_loop_ocpm_check_pacing_rate_mixer(self, **kwargs):
    '''
    内循环调价校验

    调用示例
    --------
    ``` python
    .inner_loop_ocpm_check_pacing_rate_mixer(
      ## 内循环普通调价
      item_name = "UnitTagTable",
    ) \
    ```
    '''
    self._add_processor(InnerLoopOcpmCheckPacingRateMixer(kwargs))
    return self

  def inner_loop_ocpm_calc_bid_mixer(self, **kwargs):
    '''
    内循环出价计算

    调用示例
    --------
    ``` python
    .inner_loop_ocpm_calc_bid_mixer(
      ## 内循环普通调价
      item_name = "UnitTagTable",
    ) \
    ```
    '''
    self._add_processor(InnerLoopOcpmCalcBidMixer(kwargs))
    return self

  def inner_loop_step_one_write_to_redis(self, **kwargs):
    '''
    InnerLoopStepOneWriteToRedisMixer
    ------
    无diff测试工具算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .inner_loop_step_one_write_to_redis(
      item_table = "TriggerTable",
      redis_name = "adEngineFlowProcess",
      ttl = 86400
    )
    ```
    '''
    self._add_processor(InnerLoopStepOneWriteToRedisMixer(kwargs))
    return self

  def ocpm_new_day_reset_mixer(self, **kwargs):
    self._add_processor(OcpmNewDayResetMixer(kwargs))
    return self

  def ocpm_reset_mixer(self, **kwargs):
    '''
    OcpmResetMixer
    ------
    reset 算子

    参数配置
    ------

    调用示例
    ------

    ``` python
      .ocpm_reset_mixer(
        tag_table = "AccountTagTable",
        reset_table = dict (
          common_column = [
            dict(column="target_cost", value = 0),
            dict(column="deep_target_cost", value = 0),
            dict(column="constraint_target_cost", value = 0),
            dict(column="performance_target_cost", value = 0),
            dict(column="origin_performance_target_cost", value = 0),
            dict(column="roas_24h_all_target_cost", value = 0),
            dict(column="roas_24h_all_target_cost_attr", value = 0),
            dict(column="constraint_cv", value = 0),
            dict(column="cost", value = 0),
            dict(column="rta_cost", value = 0),
            dict(column="origin_cost", value = 0),
            dict(column="price_after_billing_separate", value = 0),
            dict(column="expect_cv", value = 0),
            dict(column="constraint_expect_cv", value = 0),
            dict(column="cv", value = 0),
            dict(column="retention_count", value = 0),
            dict(column="expect_target_cost", value = 0),
            dict(column="deep_expect_target_cost", value = 0),
            dict(column="predict_conversion", value = 0),
            dict(column="predict_deep_conversion", value = 0),
            dict(column="target_cpa_sum", value = 0),
          ],

          list_column = [
            "unit_id_list", #"unit_price_ratio_info_map"
            "budget_data", # 复合结构待补齐
          ],

          meta_window_list_column = [
            "window_meta_data.target_cost_info",
            "window_meta_data.deep_target_cost_info",
            "window_meta_data.performance_target_cost_info",
            "window_meta_data.origin_performance_target_cost_info",
            "window_meta_data.cost_info",
            "window_meta_data.second_cost_info",
            "window_meta_data.origin_cost_info",
            "window_meta_data.predict_conversion_info",
            "window_meta_data.predict_deep_conversion_info",
            "window_meta_data.auction_log_new",
            "window_meta_data.cpa_bid_info",
            "window_meta_data.deep_cpa_bid_info",
            "window_meta_data.cpa_coef_cost_sum_info",
            "window_meta_data.target_cpa_sum_info",
            "window_meta_data.auto_cpa_bid_cost_sum_info",
            "window_meta_data.auto_roas_cost_sum_info",
            "window_meta_data.expect_target_cost_info",
            "window_meta_data.deep_expect_target_cost_info",
          ],
        ),
      )
    ```
    '''
    self._add_processor(OcpmResetMixer(kwargs))
    return self

  def ocpm_update_mixer(self, **kwargs):
    '''

    ``` python
    .ocpm_update_mixer(
      item_table = "AccountTagTable",
      gobal_columns = [
        step_two_today_target_cost = "",
        today_performance_target_cost
        today_origin_performance_target_cost
        today_constraint_cv
        today_cv
        today_constraint_target_cost
        today_expect_cv
        today_constraint_expect_cv
        today_origin_cost
        today_price_after_billing_separate
        today_target_cpa_sum
        predict_conversion
        predict_deep_conversion

        account_bidding_info_today_expect_target_cost
        account_bidding_info_today_deep_expect_target_cost
        account_bidding_info_today_deep_target_cost
        account_bidding_info_roas_24h_all_today_target_cost
        account_bidding_info_roas_24h_all_today_target_cost_att
        account_bidding_info_
      ]
    )
    ```
    '''
    self._add_processor(OcpmUpdateMixer(kwargs))
    return self

  def ad_log_ocpm_trigger_enricher(self, **kwargs):
    '''
    AdLogOcpmTriggerEnricher
    ----------------
    bidServer account ad log ocpm 调价触发算子
    -----------------
    参数配置
    -----------------
    调用示例
    ``` python
    -----------------
    .ad_log_ocpm_trigger_enricher(
      item_table = "AccountTable",
      item_key = "account_id",
      output_common_column = "enable_ocpm_ont_time_trigger",
      input_common_column = "ocpm_strategy_version",
      last_cpa_trigger_ts = ["item_table" : "", "column": "last_cpa_trigger_ts"],
      avg_cpa_bid = ["item_table" : "", "column": "avg_cpa_bid"],
      account_separate_unit_cpa_bid = ["item_table" : "", "column": "account_separate_unit_cpa_bid"],
      batch_cost = ["item_table" : "", "column": "batch_cost"],
      mcb_level = ["item_table" : "AccountTable", "column": "mcb_level"],
      combine_key = ["item_table" : "AccountTable", "column": "combine_key"],

      block_second = ["item_table" : "OcpmHyperParamterTable", "column": "block_second"],
      cost_threshold = ["item_table" : "OcpmHyperParamterTable", "column": "cost_threshold"],
      batch_cpa_ratio = ["item_table" : "OcpmHyperParamterTable", "column": "batch_cpa_ratio"],
      force_update_second = ["item_table" : "OcpmHyperParamterTable", "column": "force_update_second"],
      min_avg_cpa_bid = ["item_table" : "OcpmHyperParamterTable", "column": "min_avg_cpa_bid"],
    )
    '''
    self._add_processor(AdLogOcpmTriggerEnricher(kwargs))
    return self

  def account_all_unit_data_fetch_retriever(self, **kwargs):
    '''
    AccountAllUnitDataFetchRetriever
    -----------------
    bidServer account ad log ocpm 获取 account 下所有在投 unit 缓存数据算子
    -----------------
    参数配置
    -----------------
    调用示例
    -----------------
    ``` python
    -----------------
    .account_all_unit_data_fetch_retriever(
      ## 取这张表中的 account 下所有 在投 unit 的数据
      input_table_name = "AccountTable",
      account_id = {"item_table" : "AccountTable", "column" : "account_id"},
      cluster_name = "BidServerGraph",
      candidate_cluster_name = "BidServerGraphTest",
      charge_tag = {"item_table" : "AccountTable", "column" : "charge_tag"},
      unit_id_list = {"item_table" : "AccountTagTable", "column" : "unit_id_list"},
      unit_tag_table_conf = {
        "table_name": "UnitTagTable",
        "redis_key_prefix": "UnitTagTable:",
      },
    )
    '''
    self._add_processor(AccountAllUnitDataFetchRetriever(kwargs))
    return self

  def ocpm_account_step_two_diff_observer(self, **kwargs):
    '''
    OcpmAccountStepTwoDiffObserver
    --------------------------
    ocpm step two 无 diff 数据发送算子
    --------------------------
    '''
    self._add_processor(OcpmAccountStepTwoDiffObserver(kwargs))
    return self

  def ocpm_calc_unit_price_ratio_mixer(self, **kwargs):
    '''
    CalcUnitPriceRatioMixer
    ------
    calc_unit_price_raio
    ------
    '''
    self._add_processor(CalcUnitPriceRatioMixer(kwargs))
    return self

  def ocpm_result_send_mixer(self, **kwargs):
    '''
    OcpmResultSendMixer
    ------
    ocpm_result_send_mixer
    ------
    '''
    self._add_processor(OcpmResultSendMixer(kwargs))
    return self


  def ocpm_perf_mixer(self, **kwargs):
    '''
    OcpmPerfMixer
    ------
    ocpm_perf_mixer
    ------
    '''
    self._add_processor(OcpmPerfMixer(kwargs))
    return self

  def ocpm_experiment_update_mixer(self, **kwargs):
    '''
    OcpmExperimentUpdateMixer
    ------
    ocpm_experiment_update_mixer
    ------
    '''
    self._add_processor(OcpmExperimentUpdateMixer(kwargs))
    return self

  def ocpm_sync_data_mixer(self, **kwagrs):
    '''
    -----------
    OcpmSyncDataMixer
    ---------
    ocpm_sync_data_mixer
    '''
    self._add_processor(OcpmSyncDataMixer(kwagrs))
    return self

  def table_column_diff_compare_observer(self, **kwagrs):
    '''
    TableColumnDiffCompareObserver
    ------------
    该算子用于对比两列数据是否相同, mcb 合表使用
    ------------
    参数配置
    ------------
    调用示例
    ------------
    ``` python
    ------------
    table_column_diff_compare_observer(
      diff_content = [
        {"base_column": "campaign_id", "base_table": "McbBaseInfoTable", "diff_column": "campaign_id", "diff_table": "AdLogTable"}
      ]
    )
    '''
    self._add_processor(TableColumnDiffCompareObserver(kwagrs))
    return self

  def ad_pid_message_queue_dispatch_mixer(self, **kwargs):
    '''
    '''
    self._add_processor(PidMessageQueueDispatchMixer(kwargs))
    return self

  def ad_pid_message_strategy_mixer(self, **kwargs):
    '''
    '''
    self._add_processor(PidMessageStrategyMixer(kwargs))
    return self

  def pid_strategy_data_fetch(self, **kwargs):
    '''
    PidStrategyDataFetchRetriever
    ------
    pid 全局数据获取算子

    参数配置
    ------
    `cluster_name`: redis 集群
    `candidate_cluster_name`: redis 测试集群
    `prefix_name`: redis 前缀
    `output_table_name`: 输出的 table 名

    调用示例
    ------
    ``` python
    .pid_strategy_data_fetch(
        cluster_name = "PidServerGraphInner",
        candidate_cluster_name = "PidServerGraphTest",
        prefix_name = "pid_ctx_:",
        output_table_name = "PidDataTable",
      )
    ```
    '''
    self._add_processor(PidStrategyDataFetchRetriever(kwargs))
    return self

  def pid_hc_admit(self, **kwargs):
    '''
    PidHcAdmitEnricher
    ------
    pid hc 准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_hc_admit(
      )
    ```
    '''
    self._add_processor(PidHcAdmitEnricher(kwargs))
    return self
  
  def pid_ee_search_budget_admit(self, **kwargs):
    '''
    PidEESearchBudgetAdmitEnricher
    ------
    pid hc 准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_ee_search_budget_admit(
      )
    ```
    '''
    self._add_processor(PidEESearchBudgetAdmitEnricher(kwargs))
    return self
  
  def pid_outerloop_ee_budget_admit(self, **kwargs):
    '''
    PidOuterloopEeBudgetAdmitEnricher
    ------
    外循环破圈-ee 准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_outerloop_ee_budget_admit(
      )
    ```
    '''
    self._add_processor(PidOuterloopEeBudgetAdmitEnricher(kwargs))
    return self

  def pid_smb_unit_admit(self, **kwargs):
    '''
    PidSmbUnitAdmitEnricher
    ------
    pid hc 准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_smb_unit_admit(
      )
    ```
    '''
    self._add_processor(PidSmbUnitAdmitEnricher(kwargs))
    return self

  def pid_smb_account_admit(self, **kwargs):
    '''
    PidSmbAccountAdmitEnricher
    ------
    pid hc 准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_smb_account_admit(
      )
    ```
    '''
    self._add_processor(PidSmbAccountAdmitEnricher(kwargs))
    return self

  def pid_bonus_admit(self, **kwargs):
    '''
    PidBonusAdmitEnricher
    ------
    pid bonus 准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_bonus_admit(
      )
    ```
    '''
    self._add_processor(PidBonusAdmitEnricher(kwargs))
    return self

  def pid_cross_admit(self, **kwargs):
    '''
    PidCrossAdmitEnricher
    ------
    pid cross 准入算子
    '''
    self._add_processor(PidCrossAdmitEnricher(kwargs))
    return self

  def pid_accumate_enricher(self, **kwargs):
    '''
    PidAccumateEnricher
    ------
    pid 累加算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_accumate_data(
      )
    ```
    '''
    self._add_processor(PidAccumateEnricher(kwargs))
    return self

  def pid_trigger_enricher(self, **kwargs):
    '''
    PidTriggerEnricher
    ------
    pid 累加算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_trigger_enricher(
      )
    ```
    '''
    self._add_processor(PidTriggerEnricher(kwargs))
    return self

  def pid_bonus_trigger_enricher(self, **kwargs):
    '''
    PidBonusTriggerEnricher
    ------
    pid 补贴触发算法

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_bonus_trigger_enricher(
      )
    ```
    '''
    self._add_processor(PidBonusTriggerEnricher(kwargs))
    return self

  def pid_ee_search_budget_trigger_enricher(self, **kwargs):
    '''
    PidEESearchBudgetTriggerEnricher

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_ee_search_budget_trigger_enricher(
      )
    ```
    '''
    self._add_processor(PidEESearchBudgetTriggerEnricher(kwargs))
    return self
  
  def pid_outerloop_ee_budget_trigger_enricher(self, **kwargs):
    '''
    PidOuterloopEeBudgetTriggerEnricher

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_outerloop_ee_budget_trigger_enricher(
      )
    ```
    '''
    self._add_processor(PidOuterloopEeBudgetTriggerEnricher(kwargs))
    return self

  def pid_smb_unit_trigger_enricher(self, **kwargs):
    '''
    PidSmbUnitTriggerEnricher

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_smb_unit_trigger_enricher(
      )
    ```
    '''
    self._add_processor(PidSmbUnitTriggerEnricher(kwargs))
    return self

  def pid_smb_account_trigger_enricher(self, **kwargs):
    '''
    PidSmbUnitTriggerEnricher

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_smb_account_trigger_enricher(
      )
    ```
    '''
    self._add_processor(PidSmbAccountTriggerEnricher(kwargs))
    return self

  def pid_universe_billing_separate_trigger_enricher(self, **kwargs):
    self._add_processor(PidUniverseBillingSeparateTriggerEnricher(kwargs))
    return self

  def pid_cross_trigger_enricher(self, **kwargs):
    '''
    PidCrossTriggerEnricher

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_cross_trigger_enricher(
      )
    ```
    '''
    self._add_processor(PidCrossTriggerEnricher(kwargs))
    return self

  def pid_bonus_exec(self, **kwargs):
    '''
    PidBonusExecEnricher
    ------
    pid 补贴策略算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_bonus_trigger_enricher(
      )
    ```
    '''
    self._add_processor(PidBonusExecEnricher(kwargs))
    return self

  def pid_cross_exec(self, **kwargs):
    '''
    PidCrossExecEnricher
    '''
    self._add_processor(PidCrossExecEnricher(kwargs))
    return self

  def pid_hc_exec(self, **kwargs):
    '''
    PidHcExecEnricher
    ------
    pid 累加算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_hc_exec(
      )
    ```
    '''
    self._add_processor(PidHcExecEnricher(kwargs))
    return self

  def pid_ee_search_budget_exec(self, **kwargs):
    '''
    PidEESearchBudgetExecEnricher
    ------

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_ee_search_budget_exec(
      )
    ```
    '''
    self._add_processor(PidEESearchBudgetExecEnricher(kwargs))
    return self
  
  def pid_outerloop_ee_budget_exec(self, **kwargs):
    '''
    PidOuterloopEeBudgetExecEnricher
    ------

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_outerloop_ee_budget_exec(
      )
    ```
    '''
    self._add_processor(PidOuterloopEeBudgetExecEnricher(kwargs))
    return self

  def pid_smb_unit_exec(self, **kwargs):
    '''
    PidSmbUnitExecEnricher
    ------

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_smb_unit_exec(
      )
    ```
    '''
    self._add_processor(PidSmbUnitExecEnricher(kwargs))
    return self

  def pid_smb_account_exec(self, **kwargs):
    '''
    PidSmbUnitExecEnricher
    ------

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_smb_account_exec(
      )
    ```
    '''
    self._add_processor(PidSmbAccountExecEnricher(kwargs))
    return self

  def pid_universe_billing_separate_exec(self, **kwargs):
    self._add_processor(PidUniverseBillingSeparateExec(kwargs))
    return self

  def pid_after_exec(self, **kwargs):
    '''
    PidAfterExecEnricher
    ------
    pid 累加算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_after_exec(
      )
    ```
    '''
    self._add_processor(PidAfterExecEnricher(kwargs))
    return self

  def pid_billing_separate_admit(self, **kwargs):
    '''
    PidBillingSeparateAdmitEnricher
    ------
    pid 计费分离准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_billing_separate_admit(
      )
    ```
    '''
    self._add_processor(PidBillingSeparateAdmitEnricher(kwargs))
    return self
  def pid_billing_separate_trigger_enricher(self, **kwargs):
    '''
    PidBillingSeparateTriggerEnricher
    ------
    pid 计费分离触发算法

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_billing_separate_trigger_enricher(
      )
    ```
    '''
    self._add_processor(PidBillingSeparateTriggerEnricher(kwargs))
    return self

  def pid_billing_separate_exec(self, **kwargs):
    '''
    PidBillingSeparateExecEnricher
    ------
    pid 计费分离算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_billing_separate_exec(
      )
    ```
    '''
    self._add_processor(PidBillingSeparateExecEnricher(kwargs))
    return self
  
  def pid_revenue_optimize_base_admit(self, **kwargs):
    '''
    PidRevenueOptimizeBaseAdmitEnricher
    ------
    pid 现金率准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_revenue_optimize_base_admit(
      )
    ```
    '''
    self._add_processor(PidRevenueOptimizeBaseAdmitEnricher(kwargs))
    return self

  def pid_revenue_optimize_base_trigger_enricher(self, **kwargs):
    '''
    PidRevenueOptimizeBaseTriggerEnricher
    ------
    pid 现金率准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_revenue_optimize_base_trigger_enricher(
      )
    ```
    '''
    self._add_processor(PidRevenueOptimizeBaseTriggerEnricher(kwargs))
    return self

  def pid_revenue_optimize_base_exec_enricher(self, **kwargs):
    '''
    PidRevenueOptimizeBaseExecEnricher
    ------
    pid 现金率准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_revenue_optimize_base_exec_enricher(
      )
    ```
    '''
    self._add_processor(PidRevenueOptimizeBaseExecEnricher(kwargs))
    return self
  
  def pid_revenue_optimize_exp_admit(self, **kwargs):
    '''
    PidRevenueOptimizeExpAdmitEnricher
    ------
    pid 现金率准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_revenue_optimize_exp_admit(
      )
    ```
    '''
    self._add_processor(PidRevenueOptimizeExpAdmitEnricher(kwargs))
    return self

  def pid_revenue_optimize_exp_trigger_enricher(self, **kwargs):
    '''
    PidRevenueOptimizeExpTriggerEnricher
    ------
    pid 现金率准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_revenue_optimize_exp_trigger_enricher(
      )
    ```
    '''
    self._add_processor(PidRevenueOptimizeExpTriggerEnricher(kwargs))
    return self

  def pid_revenue_optimize_exp_exec_enricher(self, **kwargs):
    '''
    PidRevenueOptimizeExpExecEnricher
    ------
    pid 现金率准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_revenue_optimize_exp_exec_enricher(
      )
    ```
    '''
    self._add_processor(PidRevenueOptimizeExpExecEnricher(kwargs))
    return self

  def esp_author_fans_pcoc_admit(self, **kwargs):
    '''
    EspAuthorFansPcocAdmitEnricher
    ------
    esp author fans pcoc准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .esp_author_fans_pcoc_admit(
      )
    ```
    '''
    self._add_processor(EspAuthorFansPcocAdmitEnricher(kwargs))
    return self

  def esp_author_fans_pcoc_trigger_enricher(self, **kwargs):
    '''
    EspAuthorFansPcocTriggerEnricher
    ------
    esp author fans pcoc触发算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .esp_author_fans_pcoc_trigger_enricher(
      )
    ```
    '''
    self._add_processor(EspAuthorFansPcocTriggerEnricher(kwargs))
    return self

  def esp_author_fans_pcoc_exec(self, **kwargs):
    '''
    EspAuthorFansPcocExecEnricher
    ------
    esp author fans pcoc 计算算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .esp_author_fans_pcoc_exec(
      )
    ```
    '''
    self._add_processor(EspAuthorFansPcocExecEnricher(kwargs))
    return self

  def pid_gimbal_admit(self, **kwargs):
    '''
    PidGimbalAdmitEnricher
    ------
    pid gimbal准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_gimbal_admit(
      )
    ```
    '''
    self._add_processor(PidGimbalAdmitEnricher(kwargs))
    return self

  def pid_gimbal_trigger_enricher(self, **kwargs):
    '''
    PidGimbalTriggerEnricher
    ------
    pid gimbal准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_gimbal_trigger_enricher(
      )
    ```
    '''
    self._add_processor(PidGimbalTriggerEnricher(kwargs))
    return self

  def pid_gimbal_exec_enricher(self, **kwargs):
    '''
    PidGimbalExecEnricher
    ------
    pid gimbal 准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_gimbal_exec_enricher(
      )
    ```
    '''
    self._add_processor(PidGimbalExecEnricher(kwargs))
    return self

  def bid_server_perf_util_observer(self, **kwagrs):
    '''
    BidServerPerfUtilObserver
    ------------
    该算子用于发送打点数据到kafka
    ------------
    参数配置
    ------------
    调用示例
    ------------
    ``` python
    ------------
    bid_server_perf_util_observer(
      kafka_topic = "kafka_topic_test",
      item_table = "McbBackupTable",
      bid_data_table = "McbBidDataTable"
    )
    '''
    self._add_processor(BidServerPerfUtilObserver(kwagrs))
    return self

  def pid_inspire_photo_roi_admit(self, **kwargs):
    '''
    PidInspirePhotoRoiAdmitEnricher
    ------
    pid inspire_photo_roi准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_inspire_photo_roi_admit(
      )
    ```
    '''
    self._add_processor(PidInspirePhotoRoiAdmitEnricher(kwargs))
    return self

  def pid_inspire_photo_roi_trigger_enricher(self, **kwargs):
    '''
    PidInspirePhotoRoiTriggerEnricher
    ------
    pid inspire_photo_roi准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_inspire_photo_roi_trigger_enricher(
      )
    ```
    '''
    self._add_processor(PidInspirePhotoRoiTriggerEnricher(kwargs))
    return self

  def pid_inspire_photo_roi_exec_enricher(self, **kwargs):
    '''
    PidInspirePhotoRoiExecEnricher
    ------
    pid inspire_photo_roi 准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_inspire_photo_roi_exec_enricher(
      )
    ```
    '''
    self._add_processor(PidInspirePhotoRoiExecEnricher(kwargs))
    return self

  def pid_incentive_auto_dark_btr_admit(self, **kwargs):
    '''
    PidIncentiveAutoDarkBtrAdmitEnricher
    ------
    pid incentive_auto_dark_btr准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_incentive_auto_dark_btr_admit(
      )
    ```
    '''
    self._add_processor(PidIncentiveAutoDarkBtrAdmitEnricher(kwargs))
    return self

  def pid_incentive_auto_dark_btr_trigger_enricher(self, **kwargs):
    '''
    PidIncentiveAutoDarkBtrTriggerEnricher
    ------
    pid incentive_auto_dark_btr准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_incentive_auto_dark_btr_trigger_enricher(
      )
    ```
    '''
    self._add_processor(PidIncentiveAutoDarkBtrTriggerEnricher(kwargs))
    return self

  def pid_incentive_auto_dark_btr_exec_enricher(self, **kwargs):
    '''
    PidIncentiveAutoDarkBtrExecEnricher
    ------
    pid incentive_auto_dark_btr 准入算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .pid_incentive_auto_dark_btr_exec_enricher(
      )
    ```
    '''
    self._add_processor(PidIncentiveAutoDarkBtrExecEnricher(kwargs))
    return self

  def common_reco_result_kafka_receiver(self, **kwargs):
    '''
    CommonRecoResultKafkaReceiverMixer
    ------
    common_reco_result_kafka_receiver

    参数配置
    ------
    `output_column`: 传给下层写内存队列的shard_id

    `packed_common_attrs`: 需要打包发向Kafka的CommonAttr

    `packed_table_columns`: 需要打包发向Kafka的列, 支持多表

    调用示例
    ------
    ``` python
    .common_reco_result_kafka_receiver(
      partition = "{{partition}}",
      packed_common_attrs = ["aa", "bb"],
      packed_table_columns = [
        {"table_name": "AA", "columns" = ["ddd", "eee"]},
        {"table_name": "BB", "columns" = ["fff", "ggg"]}
      ])
    ```
    '''
    self._add_processor(CommonRecoResultKafkaReceiverMixer(kwargs))
    return self


  def common_reco_result_kafka_send(self, **kwargs):
    '''
    '''
    self._add_processor(CommonRecoResultKafkaSendMixer(kwargs))
    return self
