#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import <PERSON><PERSON>nrich<PERSON>
from dragonfly.common_leaf_util import check_arg, strict_types
from ad_dragonfly.utils import get_dynamic_param, is_param_dynamic
from typing import Dict
from copy import deepcopy

class GroupTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "group_tag_calc"

  @strict_types
  def is_async(self) -> bool:
    return False

class DefaultBidInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "default_bid_info_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class ExpInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bid_exp_info"

  @strict_types
  def is_async(self) -> bool:
    return False

class AccountGroupTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "account_group_tag_modify"

  @strict_types
  def is_async(self) -> bool:
    return False

class DeepGroupTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_deep_group_tag"

  @strict_types
  def is_async(self) -> bool:
    return False

class AdTableAccountBidInfoEnricher(LeafEnricher):

  INPUT_COMMON_ATTRS = {
    "enable_account_unit_price_ratio",
    "enable_uax_single_bid",
    "enable_send_trace_log",
    "flow_type",
    "ad_table_account_bid_info_refactor_universe"
  }

  INPUT_ITEM_ATTRS = {
    "unit_id",
    "account_id",
    "author_id",
    "campaign_type",
    "campaign_id",
    "periodic_delivery_type",
    "ad_queue_type",
    "ocpx_action_type",
    "deep_conversion_type",
    "auto_manage",
    "bid_coef",
    "bid_auto_cpa_bid",
    "bid_auto_roas",
    "aggre_key",
    "group_idx",
    "is_uax",
    "basic_group_tag",
    "find_bid_info",
    "is_account_bidding",
    "ad_auto_bid_info",
    # "ad_auto_bid_info_pb",
    "deep_min_bid_coef",
    "guarantee_min_cost",
    "price_ratio",
    "unit_calibration",
    "bit_info",
    "reset_group_tag",
    "cpa_ratio",
    "enable_merge_bid",
    "auto_adjust",
    "auto_build",
    "cpa_coef_upper",
    "cpa_coef_lower",
    "target_factor",
    "sub_bid_coef",
    "bid_gimbal_ratio",
    "wentou_campaign_calibration",
    "auto_bid_explore",
    "account_wentou_gimbal_ratio_str",
    "account_wentou_bid_coef_hit_flag_str",
    "bid_gimbal_type",
    "hit_white_account_explore_cost",
    "aigc_bid_coef",
    "use_ap_gimbal_ratio",
  }

  OUTPUT_ITEM_ATTRS = {
    "unit_id",
    "account_id",
    "author_id",
    "campaign_type",
    "campaign_id",
    "periodic_delivery_type",
    "ad_queue_type",
    "ocpx_action_type",
    "deep_conversion_type",
    "auto_manage",
    "bid_coef",
    "bid_auto_cpa_bid",
    "bid_auto_roas",
    "aggre_key",
    "group_idx",
    "is_uax",
    "basic_group_tag",
    "find_bid_info",
    "is_account_bidding",
    "ad_auto_bid_info",
    # "ad_auto_bid_info_pb",
    "deep_min_bid_coef",
    "guarantee_min_cost",
    "price_ratio",
    "unit_calibration",
    "bit_info",
    "reset_group_tag",
    "cpa_ratio",
    "enable_merge_bid",
    "auto_adjust",
    "auto_build",
    "cpa_coef_upper",
    "cpa_coef_lower",
    "target_factor",
    "sub_bid_coef",
    "bid_gimbal_ratio",
    "wentou_campaign_calibration",
    "bid_gimbal_type",
    "bid_gimbal_ratio",
    "auto_bid_explore",
    "hit_white_account_explore_cost",
    "aigc_bid_coef",
    "use_ap_gimbal_ratio",
    "account_wentou_gimbal_ratio_str",
    "account_wentou_bid_coef_hit_flag_str",
  }

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_table_account_bid_info"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = deepcopy(self.OUTPUT_ITEM_ATTRS)
    if "id" in self._config:
      ret.add(self._config["id"])
    return ret

class AdTableAccountBidInfoEnricherV2(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_table_account_bid_info_v2"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set((
      self._config["id"],
    ))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = deepcopy(AdTableAccountBidInfoEnricher.OUTPUT_ITEM_ATTRS)
    ret = ret.union((
      "bid_gimbal_type",
      "bid_gimbal_ratio",
      "auto_bid_explore",
      "hit_white_account_explore_cost",
      "aigc_bid_coef",
      "use_ap_gimbal_ratio",
      "account_wentou_gimbal_ratio_str",
      "account_wentou_bid_coef_hit_flag_str",
    ))
    return ret

class AdTableCampaignBidInfoEnricher(LeafEnricher):

  INPUT_COMMON_ATTRS = {
    "enable_send_trace_log",
    "skip_auto_manage_open",
    "user_budget_charge_tag_dsp",
    "universe_skip_auto_manage_open"
  }

  INPUT_ITEM_ATTRS = {
    "account_id",
    "campaign_id",
    "bid_strategy_group",
    "ocpx_action_type",
    "group_idx",
    "campaign_type",
    "deep_conversion_type",
    "bid_auto_cpa_bid",
    "bid_auto_roas",
    "is_hosting",
    "auto_manage",
    "mcb_group_tag",
    "update_timestamp",
    "basic_group_tag",
    "find_bid_info",
    "ad_auto_bid_info",
    # "ad_auto_bid_info_pb",  extra
    "product_cpa_bid",
    "product_roi_ratio",
    "budget_coef",
    "deep_min_coef",
    "deep_min_bid_coef",
    "price_ratio",
    "cap_bid_type",
  }

  OUTPUT_ITEM_ATTRS = {
    "update_timestamp",
    "bid_auto_roas",
    "basic_group_tag",
    "find_bid_info",
    "ad_auto_bid_info",
    # "ad_auto_bid_info_pb",  extra
    "product_cpa_bid",
    "product_roi_ratio",
    "budget_coef",
    "deep_min_coef",
    "deep_min_bid_coef",
    "price_ratio",
    "cap_bid_type",
  }

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_table_campaign_bid_info"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = deepcopy(self.OUTPUT_ITEM_ATTRS)
    if "id" in self._config:
      ret.add(self._config["id"])
    return ret

class AdTableCampaignBidInfoEnricherV2(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_table_campaign_bid_info_v2"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set((
      self._config["id"],
    ))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = deepcopy(AdTableCampaignBidInfoEnricher.OUTPUT_ITEM_ATTRS)
    return ret

class AdTableUnitBidInfoEnricher(LeafEnricher):

  INPUT_ITEM_ATTRS = {
    "unit_id",
    "account_id",
    "campaign_id",
    "author_id",
    "explore_bid_type",
    "explore_put_type",
    "is_in_acc_explore_status",
    "bit_info",
    "ocpx_action_type",
    "speed",
    "campaign_type",
    "is_fan_follow",
    "roi_ratio",
    "cpa_bid",
    "is_no_sale_live_stream_ad",
    "reset_group_tag",
    "bid_auto_cpa_bid",
    "auto_bid_explore",
    "bid_auto_roas",
    "target_cpa",
    "update_timestamp",
    "total_cost",
    "target_factor",
    "total_target_cost",
    "is_author_fans_cost",
    "is_author_fans_target_cost",
    "is_cold_unit_photo",
    "ocpm_inner_strategy_tag",
    "group_idx",
    "guarantee_min_cost",
    "bid_server_type",
    "basic_group_tag",
    "find_bid_info",
    "ad_bid_server_group_tag",
    "deep_group_tag",
    "bid_data_type",
    "ad_auto_bid_info",
    # "ad_auto_bid_info_pb",
    "is_account_bidding",
    "auto_deep_cpa_bid",
    "deep_flow_control_rate",
    "real_sctr",
    "cost_cap_p",
    "cost_cap_q",
    "is_not_bid_ad",
    "live_creative_type",
    "first_coef",
    "second_coef",
    "bid_upper_bound",
    "bid_lower_bound",
    "middle_bid",
    "bucket_size",
    "bid_explore_cnt",
    "bid_coef",
    "price_ratio",
    "page_achieve_ratio",
    "inspire_roi_cali_ratio",
    "fanstop_cvr_threshold_ratio",
    "scene_oriented_type",
    "ad_queue_type_attr",
    "fanstop_guaranteed_ratio",
    "cpm_bound",
    "fanstop_cost_ratio",
    "auto_bid_explore",
    "total_cost",
    "total_target_cost",
    "explore_ext_type",
  }

  INPUT_COMMON_ATTRS = {
    "enable_page_bid_cali_trans",
    "follow_select_cali_block_eop",
    "follow_select_cali_block_roas",
    "select_explore_cali_split",
    "enable_default_follow_select_cali",
    "enable_hosting_follow_select_cali",
    "enable_follow_select_split_cali",
    "enable_roas_block_no_sale_ad",
    "enable_photo_cap_page_cali",
    "sub_page_id",
    "enable_send_trace_log",
    "flow_type",
    "page_id",
    "pos_id",
    "request_flow_type",
    "is_thanos_request",
    "is_inspire_live",
    "enable_inner_campaign_bid_unit_price",
    "enable_inner_campaign_bid_unit_trans"
  }

  OUTPUT_ITEM_ATTRS = {
    "unit_id",
    "account_id",
    "campaign_id",
    "author_id",
    "explore_bid_type",
    "explore_put_type",
    "is_in_acc_explore_status",
    "bit_info",
    "ocpx_action_type",
    "speed",
    "campaign_type",
    "is_fan_follow",
    "roi_ratio",
    "cpa_bid",
    "is_no_sale_live_stream_ad",
    "reset_group_tag",
    "bid_auto_cpa_bid",
    "auto_bid_explore",
    "bid_auto_roas",
    "target_cpa",
    "update_timestamp",
    "total_cost",
    "target_factor",
    "total_target_cost",
    "is_author_fans_cost",
    "is_author_fans_target_cost",
    "is_cold_unit_photo",
    "ocpm_inner_strategy_tag",
    "group_idx",
    "guarantee_min_cost",
    "bid_server_type",
    "basic_group_tag",
    "find_bid_info",
    "ad_bid_server_group_tag",
    "deep_group_tag",
    "bid_data_type",
    "ad_auto_bid_info",
    # "ad_auto_bid_info_pb",
    "is_account_bidding",
    "auto_deep_cpa_bid",
    "deep_flow_control_rate",
    "real_sctr",
    "cost_cap_p",
    "cost_cap_q",
    "is_not_bid_ad",
    "live_creative_type",
    "first_coef",
    "second_coef",
    "bid_upper_bound",
    "bid_lower_bound",
    "middle_bid",
    "bucket_size",
    "bid_explore_cnt",
    "bid_coef",
    "price_ratio",
    "page_achieve_ratio",
    "inspire_roi_cali_ratio",
    "fanstop_cvr_threshold_ratio",
    "scene_oriented_type",
    "ad_queue_type_attr",
    "fanstop_guaranteed_ratio",
    "cpm_bound",
    "fanstop_cost_ratio",
    "auto_bid_explore",
    "total_cost",
    "total_target_cost",
    "explore_ext_type",
  }

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_table_unit_bid_info"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = deepcopy(self.OUTPUT_ITEM_ATTRS)
    if "enable_fill_id" in self._config and self._config["enable_fill_id"]:
      ret = ret.union([
        self._config["deep_id"],
        self._config["reset_id"],
        self._config["unit_table_id"],
        self._config["campaign_table_id"],
      ])
    return ret

class AdTableUnitBidInfoEnricherV2(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_table_unit_bid_info_v2"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set((
      self._config["campaign_table_id"],
      self._config["unit_table_id"],
      self._config["deep_id"],
      self._config["reset_id"]))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = deepcopy(AdTableUnitBidInfoEnricher.OUTPUT_ITEM_ATTRS)
    ret = ret.union((
      "fanstop_guaranteed_ratio",
      "cpm_bound",
      "fanstop_cost_ratio",
      "auto_bid_explore",
      "total_cost",
      "total_target_cost",
    ))
    return ret

class BidColdSatrtEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fill_cold_start_bid_info"

  @strict_types
  def is_async(self) -> bool:
    return False

class TraceLogProduceEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "trace_log_produce"

  @strict_types
  def is_async(self) -> bool:
    return False

class CompeteCampaignProduceEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "compete_campaign_produce"

  @strict_types
  def is_async(self) -> bool:
    return False


class MerchantRecoBidInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_merchant_reco_bid_info"

  @strict_types
  def is_async(self) -> bool:
    return False

class MerchantRecoUserInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_merchant_reco_user_info"

  @strict_types
  def is_async(self) -> bool:
    return False

class UniverseOptimizationTargetInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bid_universe_optimization"

  @strict_types
  def is_async(self) -> bool:
    return False

class FanstopGroupTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "update_fanstop_group_tag"

  @strict_types
  def is_async(self) -> bool:
    return False

class NoBidPostHandlerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "no_bid_post_handler"

  @strict_types
  def is_async(self) -> bool:
    return False

class AccountBidPostHandlerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "account_bid_post_handler"

  @strict_types
  def is_async(self) -> bool:
    return False

class EspNobidCpaBidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "esp_no_bid_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class PostHandlerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "post_handler"

  @strict_types
  def is_async(self) -> bool:
    return False

class SearchPostHandlerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_exp_post_handler"

  @strict_types
  def is_async(self) -> bool:
    return False

class FanstopBidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fill_fanstop_bid_info"

  @strict_types
  def is_async(self) -> bool:
    return False

class PreResizeItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pre_resize_item_attr"

  @strict_types
  def is_async(self) -> bool:
    return False

  @strict_types
  def _check_config(self) -> None:
    num_attrs = self._config.get("num_attrs")
    num_list_attrs = self._config.get("num_list_attrs")
    string_attrs = self._config.get("string_attrs")
    string_list_attrs = self._config.get("string_list_attrs")

    check_arg(num_attrs is not None, "`num_attrs` 是必选项")
    check_arg(num_list_attrs is not None, "`num_list_attrs` 是必选项")
    check_arg(string_attrs is not None, "`string_attrs` 是必选项")
    check_arg(string_list_attrs is not None, "`string_list_attrs` 是必选项")

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(
      self._config["num_attrs"] +
      self._config["num_list_attrs"] +
      self._config["string_attrs"] +
      self._config["string_list_attrs"])

class AdTableUnitBidInfoCalcIdEnricher(LeafEnricher):

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_table_unit_bid_info_calc_id"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set((
      self._config["basic_unit_table_id"],
      self._config["basic_campaign_table_id"],
      self._config["backup_unit_table_id"],
      self._config["backup_campaign_table_id"]))

class AdTableCampaignBidInfoCalcIdEnricher(LeafEnricher):

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_table_campaign_bid_info_calc_id"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set((
      self._config["basic_id"],
      self._config["backup_id"],
    ))

class AdTableAccountBidInfoCalcIdEnricher(LeafEnricher):

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_table_account_bid_info_calc_id"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set((
      self._config["basic_id"],
      self._config["backup_id"],
    ))

class AdTableUnitBidInfoCalcDeepIdEnricher(LeafEnricher):

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_table_unit_bid_info_calc_deep_id"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set((
      self._config["basic_unit_table_id"],
      self._config["backup_unit_table_id"],
      self._config["basic_campaign_table_id"],
      self._config["backup_campaign_table_id"]
    ))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set((self._config["id"], ))

class AdTableUnitBidInfoCalcResetIdEnricher(LeafEnricher):

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_table_unit_bid_info_calc_reset_id"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set((self._config["id"], ))

class ParseBidDataTransConfigEnricher(LeafEnricher):

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "parse_bid_data_trans_config"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set((self._config["out_attrs"], ))


class GetAdTableAttrsEnricher(LeafEnricher):

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_ad_table_attrs"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    id = self._config["id"]
    if isinstance(id, str):
      ret.add(id)
    elif isinstance(id, list):
      for id_ in id:
        ret.add(id_)
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    attrs = get_dynamic_param(self._config, "attrs")
    if attrs is not None:
      ret.add(attrs)
    return ret;

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = []
    if is_param_dynamic(self._config, "attrs"):
      attrs = [  # 用于 kconf diff
        "bid_gimbal_type",
        "bid_gimbal_ratio",
        "auto_bid_explore",
        "hit_white_account_explore_cost",
        "aigc_bid_coef",
        "use_ap_gimbal_ratio",
        "account_wentou_gimbal_ratio_str",
        "account_wentou_bid_coef_hit_flag_str",
        "fanstop_guaranteed_ratio",
        "cpm_bound",
        "fanstop_cost_ratio",
        "total_cost",
        "total_target_cost",
      ]
    elif isinstance(self._config["attrs"], list):
      attrs = self._config["attrs"]
    prefix: str = self._config["attr_prefix"] if "attr_prefix" in self._config else ""
    suffix: str = self._config["attr_suffix"] if "attr_suffix" in self._config else ""
    ret = {f"{prefix}{attr}{suffix}" for attr in attrs}
    if "out_id" in self._config:
      ret.add(self._config["out_id"])
    return ret
