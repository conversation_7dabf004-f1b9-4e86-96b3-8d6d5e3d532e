#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import LeafArranger
from dragonfly.common_leaf_util import check_arg, strict_types

class BidAdmitArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bid_admit"

  @strict_types
  def is_async(self) -> bool:
    return False

class BidIndexDataArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "index_data_arranger"

  @strict_types
  def is_async(self) -> bool:
    return False

class AdTableIndexArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_table_index_arranger"

  @strict_types
  def is_async(self) -> bool:
    return False