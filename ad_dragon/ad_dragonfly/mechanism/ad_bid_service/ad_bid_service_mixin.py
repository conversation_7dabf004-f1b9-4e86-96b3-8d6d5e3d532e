#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from .ad_bid_service_enricher import *
from .ad_bid_service_arranger import *
from .ad_bid_service_retriever import *

class AdBidServiceApiMixin(CommonLeafBaseMixin):
  # 通过实验修改group_tag等bid数据的接口
  def group_tag_calc(self, **kwargs):
    """
    GroupTagEnricher
    ------
    参数配置
    ------
    `ab_group_tag_name`: [string] 选配项 ab实验group_tag使用的参数名
    `bid_ad_type`: [string] 必配项
    `group_tag`:   [string] 必配项，对应默认值，未配置ab时直接使用该值
    `basic_group_tag`: [int] 必配项
    `bid_server_type`: [int] 必配项
    `is_skip_bid_server`: [int] 必配项
    `func_name`: [string] 对应该策略对应的广告类型

    调用示例
    ------
    ``` python
    .group_tag_calc(
      ab_group_tag_name="",
      bid_ad_type="photo_acc_explore",
      group_tag="merchant_acc_explore",
      basic_group_tag=5,
      bid_server_type=1,
      is_skip_bid_server=1,
      func_name="BidGroupDefault"
    )
    ```
    """
    self._add_processor(GroupTagEnricher(kwargs))
    return self

  # trace_log生成
  def trace_log_produce(self, **kwargs):
    """
    TraceLogProduceEnricher
    ------
    参数配置
    ------
    `bid_service_trace_log_ratio`: [list] 必配项，抽取算子依赖的 common_attr

    调用示例
    ------
    ``` python
    .trace_log_produce(
      bid_service_trace_log_ratio = "{{bid_service_trace_log_ratio}}",
      bid_table = "bid_info",
      common_table = "ad_common",
    )
    ```
    """
    self._add_processor(TraceLogProduceEnricher(kwargs))
    return self

  # 通过ab和kconf修改group_tag
  def bid_exp_info(self, **kwargs):
    """
    ExpInfoEnricher
    ------
    参数配置
    ------
    `input_common_attrs`: [list] 必配项，抽取算子依赖的 common_attr, 必须依赖 AdSamples

    调用示例
    ------
    ``` python
    .bid_exp_info(
      deep_conversion_type = "deep_conversion_type",
      first_industry_id = "first_industry_id",
      product_id = "product_id",
      ocpx_action_type = "ocpx_action_type",
      speed = "speed",
      campaign_type = "campaign_type",
      group_idx = "group_idx",
      exp_start_ts = "exp_start_ts",
      ad_bid_server_group_tag = "ad_bid_server_group_tag",
    )
    ```
    """
    self._add_processor(ExpInfoEnricher(kwargs))
    return self
  # 账户调价修改group_tag
  def account_group_tag_modify(self, **kwargs):
    """
    AccountGroupTagEnricher
    ------
    参数配置
    ------
    `group_tag`: [list] 必配项

    调用示例
    ------
    ``` python
    .account_group_tag_modify(
        bit_info = "bit_info",
        is_hosting = "is_hosting",
        bid_type = "bid_type",
        account_id = "account_id",
        product_id = "product_id",
        ocpx_action_type = "ocpx_action_type",
        deep_conversion_type = "deep_conversion_type",
        campaign_type = "campaign_type",
        group_idx = "group_idx",
        ad_bid_server_group_tag = "ad_bid_server_group_tag",
        is_account_bidding = "is_account_bidding",
        flow_type = 2,
        group_tag = "search"
    )
    ```
    """
    self._add_processor(AccountGroupTagEnricher(kwargs))
    return self
  # 获取深度group_tag
  def get_deep_group_tag(self, **kwargs):
    """
    DeepGroupTagEnricher
    ------
    参数配置
    ------
    `input_common_attrs`: [list] 必配项，抽取算子依赖的 common_attr, 必须依赖 AdSamples

    调用示例
    ------
    ``` python
    .get_deep_group_tag(
      deep_conversion_type = "deep_conversion_type",
      deep_cpa_bid = "deep_cpa_bid",
      deep_group_tag = "deep_group_tag",
    )
    ```
    """
    self._add_processor(DeepGroupTagEnricher(kwargs))
    return self

  # 获取账户调价 bid数据
  def ad_table_account_bid_info(self, **kwargs):
    """
    AdTableAccountBidInfoEnricher
    ------
    参数配置
    ------
    `enable_account_unit_price_ratio`: [list] 必配项，

    调用示例
    ------
    ``` python
    .ad_table_account_bid_info(
      enable_account_unit_price_ratio = "{{enable_account_unit_price_ratio}}",
    )
    ```
    """
    self._add_processor(AdTableAccountBidInfoEnricher(kwargs))
    return self

  # 获取账户调价 bid数据
  def ad_table_account_bid_info_v2(self, **kwargs):
    """
    AdTableAccountBidInfoEnricherV2
    ------
    参数配置
    ------
    `enable_account_unit_price_ratio`: [list] 必配项，

    调用示例
    ------
    ``` python
    .ad_table_account_bid_info_v2(
      enable_account_unit_price_ratio = "{{enable_account_unit_price_ratio}}",
    )
    ```
    """
    self._add_processor(AdTableAccountBidInfoEnricherV2(kwargs))
    return self

 # 获取 mcb 调价 bid数据
  def ad_table_campaign_bid_info(self, **kwargs):
    """
    AdTableCampaignBidInfoEnricher
    ------
    参数配置
    ------
    `enable_account_unit_price_ratio`: [list] 必配项，

    调用示例
    ------
    ``` python
    .ad_table_campaign_bid_info(
      enable_account_unit_price_ratio = "{{enable_account_unit_price_ratio}}",
    )
    ```
    """
    self._add_processor(AdTableCampaignBidInfoEnricher(kwargs))
    return self

  # 获取 mcb 调价 bid数据
  def ad_table_campaign_bid_info_v2(self, **kwargs):
    """
    AdTableCampaignBidInfoEnricherV2
    ------
    参数配置
    ------
    `enable_account_unit_price_ratio`: [list] 必配项，

    调用示例
    ------
    ``` python
    .ad_table_campaign_bid_info_v2(
      enable_account_unit_price_ratio = "{{enable_account_unit_price_ratio}}",
    )
    ```
    """
    self._add_processor(AdTableCampaignBidInfoEnricherV2(kwargs))
    return self

 # 获取 unit 调价 bid数据
  def ad_table_unit_bid_info(self, **kwargs):
    """
    AdTableUnitBidInfoEnricher
    ------
    参数配置
    ------
    `enable_account_unit_price_ratio`: [list] 必配项，

    调用示例
    ------
    ``` python
    .ad_table_unit_bid_info(
      enable_account_unit_price_ratio = "{{enable_account_unit_price_ratio}}",
    )
    ```
    """
    self._add_processor(AdTableUnitBidInfoEnricher(kwargs))
    return self

  # 获取 unit 调价 bid数据
  def ad_table_unit_bid_info_v2(self, **kwargs):
    """
    AdTableUnitBidInfoEnricherV2
    ------
    参数配置
    ------
    `enable_account_unit_price_ratio`: [list] 必配项，

    调用示例
    ------
    ``` python
    .ad_table_unit_bid_info_v2(
      enable_account_unit_price_ratio = "{{enable_account_unit_price_ratio}}",
    )
    ```
    """
    self._add_processor(AdTableUnitBidInfoEnricherV2(kwargs))
    return self

  # 参竞数据下发
  def compete_campaign_produce(self, **kwargs):
    """
    CompeteCampaignProduceEnricher
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .compete_campaign_produce(
      bid_strategy_group = "bid_strategy_group",
      campaign_id = "campaign_id",
    )
    ```
    """
    self._add_processor(CompeteCampaignProduceEnricher(kwargs))
    return self
  # 全店 roi 出价
  def get_merchant_reco_bid_info(self, **kwargs):
    """
    MerchantRecoBidInfoEnricher
    ------
    参数配置
    ------
    `input_common_attrs`: [list] 必配项，抽取算子依赖的 common_attr, 必须依赖 AdSamples

    调用示例
    ------
    ``` python
    .get_merchant_reco_bid_info(
      auto_roi = "autoRoi",
      ad_trans_info = "adTransInfo",
      budget_status = "budgetStatus"
      key_type = 1,
    )
    ```
    """
    self._add_processor(MerchantRecoBidInfoEnricher(kwargs))
    return self
  def get_merchant_reco_user_info(self, **kwargs):
    """
    MerchantRecoUserInfoEnricher
    ------
    参数配置
    ------
    `input_common_attrs`: [list] 必配项，抽取算子依赖的 common_attr, 必须依赖 AdSamples

    调用示例
    ------
    ``` python
    .get_merchant_reco_user_info(
      buyer_type = "buyer_type",
    )
    ```
    """
    self._add_processor(MerchantRecoUserInfoEnricher(kwargs))
    return self
  # 联盟通过p2p数据设置deep_group_tag
  def bid_universe_optimization(self, **kwargs):
    """
    UniverseOptimizationTargetInfoEnricher
    ------
    参数配置
    ------
    `input_common_attrs`: [list] 必配项，抽取算子依赖的 common_attr, 必须依赖 AdSamples

    调用示例
    ------
    ``` python
    .bid_universe_optimization(
      bit_info = "bit_info",
      acount_id = "acount_id",
      deep_cpa_bid = "deep_cpa_bid",
      industry_id_v3 = "industry_id_v3",
      city_product_id = "city_product_id",
      deep_conversion_type = "deep_conversion_type",
      deep_group_tag = "deep_group_tag",
    )
    ```
    """
    self._add_processor(UniverseOptimizationTargetInfoEnricher(kwargs))
    return self
  # 粉条修改group_tag的独占策略逻辑
  def update_fanstop_group_tag(self, **kwargs):
    """
    FanstopGroupTagEnricher
    ------
    参数配置
    ------
    `input_common_attrs`: [list] 必配项，抽取算子依赖的 common_attr, 必须依赖 AdSamples

    调用示例
    ------
    ``` python
    .update_fanstop_group_tag(
      account_type = "account_type",
      bid_type = "bid_type",
      bid_strategy = "bid_strategy",
      campaign_type = "campaign_type",
      ocpx_action_type = "ocpx_action_type",
      unit_id = "unit_id",
      campaign_id = "campaign_id",
      item_type = "item_type",
      live_launch_type = "live_launch_type",
      bid_ad_type = "bid_ad_type",
      group_idx = "group_idx",
      basic_group_tag = "basic_group_tag",
      bid_server_type = "bid_server_type",
      is_skip_bid_server = "is_skip_bid_server",
      bid_data_type = "bid_data_type",
    )
    ```
    """
    self._add_processor(FanstopGroupTagEnricher(kwargs))
    return self
  # no_bid的后处理逻辑
  def no_bid_post_handler(self, **kwargs):
    """
    NoBidPostHandlerEnricher
    ------
    参数配置
    ------
    `input_common_attrs`: [list] 必配项，抽取算子依赖的 common_attr, 必须依赖 AdSamples

    调用示例
    ------
    ``` python
    .no_bid_post_handler(
      ocpx_action_type = "ocpx_action_type",
      speed = "speed",
      promotion_type = "promotion_type",
      update_timestamp = "update_timestamp",
      auto_roas = "auto_roas",
      auto_roas_modify_tag = "auto_roas_modify_tag",
      auto_cpa_bid = "auto_cpa_bid",
      auto_cpa_bid_modify_tag = "auto_cpa_bid_modify_tag",
    )
    ```
    """
    self._add_processor(NoBidPostHandlerEnricher(kwargs))
    return self
  # 账户调价的后处理逻辑
  def account_bid_post_handler(self, **kwargs):
    """
    AccountBidPostHandlerEnricher
    ------
    参数配置
    ------
    `input_common_attrs`: [list] 必配项，抽取算子依赖的 common_attr, 必须依赖 AdSamples

    调用示例
    ------
    ``` python
    .account_bid_post_handler(
      enable_project_info = "{{enable_project_info}}",
      ocpx_action_type = "ocpx_action_type",
      roi_ratio = "roi_ratio",
      cpa_bid = "cpa_bid",
      campaign_id = "campaign_id",
      bid_coef = "bid_coef",
      bid_auto_roas = "auto_roas",
      bid_auto_cpa_bid = "auto_cpa_bid",
      auto_roas = "auto_roas",
      auto_roas_modify_tag = "auto_roas_modify_tag",
      auto_cpa_bid = "auto_cpa_bid",
      auto_cpa_bid_modify_tag = "auto_cpa_bid_modify_tag",
      is_account_bidding = "is_account_bidding",
    )
    ```
    """
    self._add_processor(AccountBidPostHandlerEnricher(kwargs))
    return self
  # esp_no_bid的后处理逻辑
  def esp_no_bid_enricher(self, **kwargs):
    """
    EspNobidCpaBidEnricher
    ------
    参数配置
    ------
    `input_common_attrs`: [list] 必配项，抽取算子依赖的 common_attr, 必须依赖 AdSamples

    调用示例
    ------
    ``` python
    .esp_no_bid_enricher(
      esp_nobid_unit_cpa_bid_ratio = "{{esp_nobid_unit_cpa_bid_ratio}}",
      enable_esp_nobid_unit_cpa_bid = "{{enable_esp_nobid_unit_cpa_bid}}",
      enable_esp_nobid_unit_cpa_bid_max = "{{enable_esp_nobid_unit_cpa_bid_max}}",
      enable_esp_deli_nobid_cpa_bid = "{{enable_esp_deli_nobid_cpa_bid}}",
      enable_post_server_author_atv = "{{enable_post_server_author_atv}}",
      enable_post_server_author_fade_atv = "{{enable_post_server_author_fade_atv}}",
      unit_id = "unit_id",
      author_id = "author_id",
      account_id = "account_id",
      ocpx_action_type = "ocpx_action_type",
      promotion_type = "promotion_type",
      campaign_type = "campaign_type",
      account_type = "account_type",
      speed = "speed",
      live_creative_type = "live_creative_type",
      bit_info = "bit_info",
      bid_strategy = "bid_strategy",
      bid_type = "bid_type",
      fans_count = "fans_count",
      nobid_cpa_bid = "nobid_cpa_bid",
      nobid_roi_ratio = "nobid_roi_ratio",
    )
    ```
    """
    self._add_processor(EspNobidCpaBidEnricher(kwargs))
    return self
  # 通用后处理逻辑
  def post_handler(self, **kwargs):
    """
    PostHandlerEnricher
    ------
    参数配置
    ------
    `input_common_attrs`: [list] 必配项，抽取算子依赖的 common_attr, 必须依赖 AdSamples

    调用示例
    ------
    ``` python
    .post_handler(
      pay_times_num = "{{pay_times_num}}",
      enable_live_ocpm_bid_cold_start_modifyTag = "{{enable_live_ocpm_bid_cold_start_modifyTag}}",
      bit_info = "bit_info",
      ocpx_action_type = "ocpx_action_type",
      bid_auto_cpa_bid = "auto_cpa_bid",
      reset_group_tag = "reset_group_tag",
      bid_auto_roas = "auto_roas",
      target_cpa = "target_cpa",
      ocpm_inner_strategy_tag = "ocpm_inner_strategy_tag",
      auto_roas = "auto_roas",
      auto_roas_modify_tag = "auto_roas_modify_tag",
      auto_cpa_bid = "auto_cpa_bid",
      auto_cpa_bid_modify_tag = "auto_cpa_bid_modify_tag",
      raw_auto_cpa_bid = "raw_auto_cpa_bid",
      ad_bid_server_group_tag = "ad_bid_server_group_tag",
      acc_cold_start_coef = "acc_cold_start_coef",
    )
    ```
    """
    self._add_processor(PostHandlerEnricher(kwargs))
    return self
  # 搜索的后处理逻辑
  def search_exp_post_handler(self, **kwargs):
    """
    SearchPostHandlerEnricher
    ------
    参数配置
    ------
    `input_common_attrs`: [list] 必配项，抽取算子依赖的 common_attr, 必须依赖 AdSamples

    调用示例
    ------
    ``` python
    .search_exp_post_handler(
      ocpx_action_type = "ocpx_action_type",
      roi_ratio = "roi_ratio",
      cpa_bid = "cpa_bid",
      bid_table = "bid_info",
      bid_coef = "bid_coef",
      is_account_bidding = "is_account_bidding",
      auto_roas = "auto_roas",
      auto_roas_modify_tag = "auto_roas_modify_tag",
      auto_cpa_bid = "auto_cpa_bid",
      auto_cpa_bid_modify_tag = "auto_cpa_bid_modify_tag",
    )
    ```
    """
    self._add_processor(SearchPostHandlerEnricher(kwargs))
    return self

  def default_bid_info_enricher(self, **kwargs):
    """
    DefaultBidInfoEnricher
    ------
    参数配置
    ------
    `input_common_attrs`: [list] 必配项，抽取算子依赖的 common_attr, 必须依赖 AdSamples

    调用示例
    ------
    ``` python
    .default_bid_info_enricher(
      bid_ad_type = 1,
      basic_group_tag = 0,
      bid_server_type = 0,
      is_skip_bid_server = 0,
      bid_data_type = "bid_data_type",
      budget_smart_allocation = "budget_smart_allocation",
    )
    ```
    """
    self._add_processor(DefaultBidInfoEnricher(kwargs))
    return self

  def ad_table_index_arranger(self, **kwargs):
    """
    AdTableIndexArranger
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .ad_table_index_arranger(
    )
    ```
    """
    self._add_processor(AdTableIndexArranger(kwargs))
    return self

  def fill_cold_start_bid_info(self, **kwargs):
    """
    BidColdSatrtEnricher
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .fill_cold_start_bid_info(
    )
    ```
    """
    self._add_processor(BidColdSatrtEnricher(kwargs))
    return self

  def bid_admit(self, **kwargs):
    """
    BidAdmitArranger
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .bid_admit(
    )
    ```
    """
    self._add_processor(BidAdmitArranger(kwargs))
    return self
  def filter_item_retriever(self, **kwargs):
    """
    BidFilterItemRetriever
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .filter_item_retriever(
    )
    ```
    """
    self._add_processor(BidFilterItemRetriever(kwargs))
    return self

  def fill_fanstop_bid_info(self, **kwargs):
    """
    FanstopBidEnricher
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .fill_fanstop_bid_info(
    )
    ```
    """
    self._add_processor(FanstopBidEnricher(kwargs))
    return self

  def pre_resize_item_attr(self, **kwargs):
    """
    PreResizeItemAttrEnricher
    ------
    功能：对 item_attr 进行前置 resize 处理
    对传入的 item_attr_list, 提前进行 resize, 避免后续频繁resize
    参数配置
    ------
    num_attrs: [list[string]], 必配参数, 需要提前进行resize的列, 列为数字类型时使用
    num_list_attrs: [list[string]], 必配参数, 需要提前进行resize的列, 列为数字列表时使用

    string_attrs: [list[string]], 必配参数, 需要提前进行resize的列, 列为字符串时使用
    string_list_attrs: [list[string]], 必配参数, 需要提前进行resize的列, 列为字符串列表时使用
    extra_attrs: [list[string]], 选配参数, 需要提前进行resize的列, 列为extra时使用
    max_resize_size: [int], 选配参数, 最大 resize 大小

    调用示例
    ------
    ``` python
    .pre_resize_item_attr(
      num_attrs = ["auto_cpa_bid", "is_fanstop"],
      num_list_attrs = [],
      string_attrs = ["group_tag"],
      string_list_attrs = ["group_tag_list"]
      max_resize_size = 10
    )
    ```
    """
    self._add_processor(PreResizeItemAttrEnricher(kwargs))
    return self

  def ad_table_unit_bid_info_calc_id(self, **kwargs):
    self._add_processor(AdTableUnitBidInfoCalcIdEnricher(kwargs))
    return self

  def ad_table_campaign_bid_info_calc_id(self, **kwargs):
    self._add_processor(AdTableCampaignBidInfoCalcIdEnricher(kwargs))

    return self
  def ad_table_account_bid_info_calc_id(self, **kwargs):
    self._add_processor(AdTableAccountBidInfoCalcIdEnricher(kwargs))
    return self

  def ad_table_unit_bid_info_calc_deep_id(self, **kwargs):
    self._add_processor(AdTableUnitBidInfoCalcDeepIdEnricher(kwargs))
    return self

  def ad_table_unit_bid_info_calc_reset_id(self, **kwargs):
    self._add_processor(AdTableUnitBidInfoCalcResetIdEnricher(kwargs))
    return self

  def parse_bid_data_trans_config(self, **kwargs):
    self._add_processor(ParseBidDataTransConfigEnricher(kwargs))
    return self

  def get_ad_table_attrs(self, **kwargs):
    """
    GetAdTableAttrsEnricher
    ------
    * 读取 ad_table 表数据，填入 item_table
    * item_table 字段名为 {attr_prefix}{attr}{attr_suffix} 的拼接
    参数配置
    ------
    table: [string], 必配参数, ad_table 表名
    attrs: [list[string]], 必配参数, 需要填充的字段名
    id: [string], 必配参数, item 到 ad_table 使用的 id 字段名
    attr_prefix: [string], 选配参数, 填充到 item_table 对应 attr 前缀
    attr_suffix: [string], 选配参数, 填充到 item_table 对应 attr 后缀

    调用示例
    ------
    ``` python
    .get_ad_table_attrs(
      table = "WTAutoBidUnit",
      id = "xxxx_id",
      attrs = [
        "aaaaa",
        "bbbbb"
      ],
    )
    ```
    """
    self._add_processor(GetAdTableAttrsEnricher(kwargs))
    return self
