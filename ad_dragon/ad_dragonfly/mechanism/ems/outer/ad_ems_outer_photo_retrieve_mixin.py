import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin

## 素材优选召回通路各阶段定义
class BasePhotoRetrieveMixin(CommonLeafBaseMixin):
  def _retrieve_admit(self):
    ## 召回通路准入
    return self

  def _retrieve(self):
    ## 召回
    return self

  def _rule_filter(self):
    ## 规则过滤
  	return self

  def _sort(self):
    ## 排序
  	return self

  def _truncate(self):
    ## 截断
    return self