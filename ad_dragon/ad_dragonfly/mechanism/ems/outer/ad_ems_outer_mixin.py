#!/usr/bin/env python3
# coding=utf-8

import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from .ad_ems_outer_enricher import *
from .ad_ems_outer_retriever import *

class AdEmsOuterApiMixin(CommonLeafBaseMixin):
  def package_conversion_predict_enricher(self, **kwargs):
    """
    """
    self._add_processor(PackageConversionPredictEnricher(kwargs))
    return self

  def build_mix_asset_request(self, **kwargs):
    """
    BuildMixAssetRequest
    ---------------------
    构造 Mix Asset 请求

    参数配置
    ---------------------
    `photo_id`: [string] photo_id 所在的 column_name
    `video_id`: [string] video_id 所在的 column_name
    `output_column_name`: [string] request 构造后, 存放的 column_name
    """
    self._add_processor(BuildMixAssetRequest(kwargs))
    return self

  def retrieve_item_from_common_attr_value_by_udf(self, **kwargs):
    """
    RetrieveItemFromCommonAttrValue
    ------
    通过UDF解析common_attr，将结果中的item加入到指定的item_table中，并根据解析结果中每个item的列属性填充item的列

    参数配置
    ------
    `input_common_attr`: [string] 输入的 common attr name
    `udf_name`: [string] 用户在创意的 UDF 函数名
    `reason`: [string] 输入的 common attr name
    `input_common_attr_type`: [int] input_common_attr 的类型。0: string类型，1: int_list类型，2: double_list类型，3: string_list类型，4: proto message指针类型
    """
    self._add_processor(RetrieveItemFromCommonAttrValue(kwargs))
    return self

  def enrich_item_attr_by_common_attr(self, **kwargs):
    """
    EnrichItemAttrByCommonAttr
    ------
    通过UDF解析common_attr，将解析结果写入item_attr的对应的列，item_table中找不到的item会被忽略

    参数配置
    ------
    `input_common_attr`: [string] 输入的 common attr name
    `udf_name`: [string] 用户在创意的 UDF 函数名
    `input_common_attr_type`: [int] input_common_attr 的类型。0: string类型，1: int_list类型，2: double_list类型，3: string_list类型，4: proto message指针类型
    """
    self._add_processor(EnrichItemAttrByCommonAttr(kwargs))
    return self
