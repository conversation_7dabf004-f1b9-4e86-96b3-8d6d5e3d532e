import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin

## 素材优选各阶段接口名称定义
class BasePhotoSelectMixin(CommonLeafBaseMixin):
  def _request_admit(self):
    ## 请求准入
    return self

  def _prepare(self):
    ## 请求预处理
    return self

  def _retrieve(self):
    ## 多路召回
    return self

  def _post_data_proc(self):
    ## 素材后验数据填充
    return self

  def _select_strategy(self):
    ## 优选素材
    return self
  
  def _post_proc(self):
    ## 后处理
    return self