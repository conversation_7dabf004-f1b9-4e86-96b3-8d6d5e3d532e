#!/usr/bin/env python3
# coding=utf-8

import os
import sys
from dragonfly.common_leaf_processor import <PERSON><PERSON>nricher
from dragonfly.common_leaf_util import check_arg, strict_types

class PackageConversionPredictEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "package_conversion_predict_enricher"
  @strict_types
  def is_async(self) -> bool:
    return False

class EnrichItemAttrByCommonAttr(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_item_attr_by_common_attr"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_common_attr"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg("input_common_attr" in self._config, f"{self.get_type_alias()} 未配置参数 input_common_attr")
    check_arg("udf_name" in self._config, f"{self.get_type_alias()} 未配置参数 udf_name")
    check_arg("input_common_attr_type" in self._config, f"{self.get_type_alias()} 未配置参数 input_common_attr_type")

class BuildMixAssetRequest(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_mix_asset_request"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("photo_id"))
    attrs.add(self._config.get("video_id"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_column_name"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg("photo_id" in self._config, f"{self.get_type_alias()} 未配置参数 photo_id")
    check_arg("video_id" in self._config, f"{self.get_type_alias()} 未配置参数 video_id")
    check_arg("output_column_name" in self._config, f"{self.get_type_alias()} 未配置参数 output_column_name")

