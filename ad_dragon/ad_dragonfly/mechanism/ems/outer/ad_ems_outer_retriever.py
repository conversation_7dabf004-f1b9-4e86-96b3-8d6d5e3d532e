#!/usr/bin/env python3
# coding=utf-8

import os
import sys
from dragonfly.common_leaf_processor import Leaf<PERSON><PERSON>riever
from dragonfly.common_leaf_util import check_arg, strict_types

class RetrieveItemFromCommonAttrValue(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_item_from_common_attr_value_by_udf"
  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_common_attr"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg("input_common_attr" in self._config, f"{self.get_type_alias()} 未配置参数 input_common_attr")
    check_arg("udf_name" in self._config, f"{self.get_type_alias()} 未配置参数 udf_name")
    check_arg("input_common_attr_type" in self._config, f"{self.get_type_alias()} 未配置参数 input_common_attr_type")
    check_arg("reason" in self._config, f"{self.get_type_alias()} 未配置参数 reason")