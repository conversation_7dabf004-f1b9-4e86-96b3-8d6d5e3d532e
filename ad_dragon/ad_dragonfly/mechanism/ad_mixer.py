#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import LeafMixer
from dragonfly.common_leaf_util import check_arg, strict_types

class InnerLoopMsgAdmitMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_msg_admit"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("is_for_report_engine")
    attrs.add("log_process_timestamp")
    attrs.add("delivery_timestamp")
    attrs.add("is_spam_order")
    attrs.add("unit_id")
    attrs.add("account_id")
    attrs.add("bid_server_group_tag")
    attrs.add("campaign_type")
    attrs.add("ocpc_action_type")
    attrs.add("promotion_type")
    attrs.add("speed_type")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set(["is_valid_message"])
    ret.add("message_seq")
    return ret

class InnerloopBidResultStoreMix(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "innerloop_bid_result_store"
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    util_vals_attr_info = self._config.get("util_vals_attr_info")
    for table_item in util_vals_attr_info.values():
      attrs.add(table_item["item_table"] + "::" + table_item["column"])
    bid_context_attr_info = self._config.get("bid_context_attr_info")
    for table_item in bid_context_attr_info.values():
      attrs.add(table_item["item_table"] + "::" + table_item["column"])
    lowest_cost_context = self._config.get("lowest_cost_context")
    for table_item in lowest_cost_context.values():
      attrs.add(table_item["item_table"] + "::" + table_item["column"])
    bid_state_info = self._config.get("bid_state_info")
    for table_item in bid_state_info.values():
      attrs.add(table_item["item_table"] + "::" + table_item["column"])
    return attrs

class HotMessageAddMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_message_add"

class DataPrepareInitMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "data_prepare_init_mixter"

class HotMessageSubMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_message_sub"

class MsgFilterMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "msg_filter_mixer"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(["is_valid_message"])
    return attrs

class OcpmAccountCacheSaveMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_account_cache_save_mixer"

class BackFlowFetchDataMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "back_flow_fetch_data_mixer"

class BackFlowUpdateCacheMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "back_flow_update_cache_mixer"

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    item_table = self._config.get("item_table")
    input_columns = self._config.get("input_column", [])
    for input_column in input_columns:
      ret.add(item_table + "::" + input_column)
    return ret

class OuterOcpmTableFetchMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "outer_ocpm_table_fetch_mixer"

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    item_table = self._config.get("item_table")
    input_columns = self._config.get("input_column", [])
    for input_column in input_columns:
      ret.add(item_table + "::" + input_column)
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    output_tables = self._config.get("output_tables", [])
    output_columns = self._config.get("output_column", [])
    for output_table in output_tables:
      table_name = output_table["table_name"]
      for output_column in output_columns:
        ret.add(table_name + "::" + output_column)
    return ret

class InnerLoopOcpmCalcBidPrepareMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_ocpm_calc_bid_prepare_mixer"

  @property
  @strict_types
  def input_common_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def output_common_attrs(self):
    ret = set()
    ret.add("is_skip_update")
    return ret

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    item_table = self._config.get("item_table")
    item_table = item_table + "::"
    ret.add(item_table + "ocpc_action_type")
    ret.add(item_table + "promotion_type")
    ret.add(item_table + "group_tag")
    ret.add(item_table + "unit_id")
    ret.add(item_table + "author_id")
    ret.add(item_table + "campaign_type")
    ret.add(item_table + "item_type")
    ret.add(item_table + "speed_type")
    ret.add(item_table + "relax_roi_ratio")
    ret.add(item_table + "relax_cpa_bid")
    ret.add(item_table + "cost")
    ret.add(item_table + "target_cost")
    ret.add(item_table + "last_update_cost")
    ret.add(item_table + "cpa_bid")
    ret.add(item_table + "conv_num")
    ret.add(item_table + "util_vals_ab_exp_ratio")
    ret.add(item_table + "first_delivery_timestamp_ms")
    ret.add(item_table + "last_delivery_timestamp_ms")
    ret.add(item_table + "dry_up_base_value")
    ret.add(item_table + "auto_roi_ratio")
    ret.add(item_table + "auto_cpa_bid")
    ret.add(item_table + "old_is_out_of_budget")
    ret.add(item_table + "msg_price_ratio")
    ret.add(item_table + "msg_conv_num")
    ret.add(item_table + "msg_cost")
    ret.add(item_table + "msg_target_cost")
    ret.add(item_table + "has_bid_state_info_ptr")
    ret.add(item_table + "is_cold_start")
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    item_table = self._config.get("item_table")
    item_table = item_table + "::"
    #ret.add(item_table + "user_cost_prior_algo")
    ret.add(item_table + "util_vals_pacing_type")
    ret.add(item_table + "util_vals_reset_pacing")
    #ret.add(item_table + "util_vals_disable_day_reset_exp")
    ret.add(item_table + "util_vals_p_weight")
    ret.add(item_table + "util_vals_i_weight")
    ret.add(item_table + "util_vals_d_weight")
    #ret.add(item_table + "util_vals_is_ad_open")
    #ret.add(item_table + "util_vals_is_apply_adjust")
    #ret.add(item_table + "util_vals_is_update_adjust")
    #ret.add(item_table + "util_vals_ad_status_tag")
    #ret.add(item_table + "last_ad_valid")
    #ret.add(item_table + "ad_status_tag")
    #ret.add(item_table + "util_vals_ad_status_tag_change")
    #ret.add(item_table + "util_vals_adjust_rate_lower_bound")
    #ret.add(item_table + "util_vals_adjust_rate_upper_bound")
    #ret.add(item_table + "util_vals_is_start_process")
    ret.add(item_table + "util_vals_is_ocpm_bid_process")
    ret.add(item_table + "adjust_auto_value_rate")
    #ret.add(item_table + "is_explore")
    #ret.add(item_table + "ad_off_target_cost")
    #ret.add(item_table + "util_vals_ad_off_target_cost")
    #ret.add(item_table + "last_is_ad_open")
    #ret.add(item_table + "util_vals_last_is_ad_open")
    #ret.add(item_table + "adjust_auto_atv_rate")
    #ret.add(item_table + "old_adjust_auto_atv_rate")
    ret.add(item_table + "util_vals_is_roas")
    ret.add(item_table + "util_vals_is_fanstop")
    ret.add(item_table + "util_vals_is_fanstop_ocpm")
    #ret.add(item_table + "util_vals_ad_short_type")
    #ret.add(item_table + "is_cross_day_open")
    return ret

class InnerLoopOcpmCalcPacingRateAccMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_ocpm_calc_pacing_rate_acc_mixer"

  @property
  @strict_types
  def input_common_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def output_common_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    item_table = self._config.get("item_table")
    item_table = item_table + "::"
    ret.add(item_table + "campaign_type")
    ret.add(item_table + "ocpc_action_type")
    ret.add(item_table + "group_tag")
    ret.add(item_table + "unit_id")
    ret.add(item_table + "util_vals_is_roas")
    ret.add(item_table + "relax_roi_ratio")
    ret.add(item_table + "relax_cpa_bid")
    ret.add(item_table + "rt_cost_speed")
    ret.add(item_table + "util_vals_target_cost_speed")
    ret.add(item_table + "adjust_auto_value_rate")
    ret.add(item_table + "util_vals_p_weight")
    ret.add(item_table + "util_vals_i_weight")
    ret.add(item_table + "util_vals_d_weight")
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    item_table = self._config.get("item_table")
    item_table = item_table + "::"
    ret.add(item_table + "last_update_adjust_timestamp")
    #ret.add(item_table + "util_vals_p_val")
    #ret.add(item_table + "util_vals_i_val")
    #ret.add(item_table + "util_vals_d_val")
    #ret.add(item_table + "util_vals_pacing_weight")
    ret.add(item_table + "util_vals_is_update_adjust")
    ret.add(item_table + "util_vals_is_acc_explore_bid")
    return ret

class InnerLoopOcpmCalcPacingRateNormalMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_ocpm_calc_pacing_rate_normal_mixer"

  @property
  @strict_types
  def input_common_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def output_common_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    item_table = self._config.get("item_table")
    item_table = item_table + "::"
    ret.add(item_table + "util_vals_is_update_adjust")
    ret.add(item_table + "util_vals_is_acc_explore_bid")
    ret.add(item_table + "util_vals_is_cold_start")
    ret.add(item_table + "util_vals_is_ocpm_bid_process")
    ret.add(item_table + "adjust_auto_value_rate")
    ret.add(item_table + "campaign_type")
    ret.add(item_table + "bid_state_info_budget")
    ret.add(item_table + "cost")
    ret.add(item_table + "cpa_bid")
    ret.add(item_table + "group_tag")
    ret.add(item_table + "has_bid_state_info_ptr")
    ret.add(item_table + "ocpc_action_type")
    ret.add(item_table + "promotion_type")
    ret.add(item_table + "target_cost")
    ret.add(item_table + "unit_id")
    ret.add(item_table + "account_id")
    ret.add(item_table + "unit_is_own_status")
    ret.add(item_table + "util_vals_d_weight")
    ret.add(item_table + "util_vals_enable_cost_prior_algo")
    ret.add(item_table + "util_vals_is_fanstop")
    ret.add(item_table + "util_vals_is_fanstop_ocpm")
    ret.add(item_table + "util_vals_is_roas")
    ret.add(item_table + "util_vals_i_weight")
    ret.add(item_table + "util_vals_p_weight")
    ret.add(item_table + "util_vals_prior_cost")
    ret.add(item_table + "util_vals_reset_pacing")
    ret.add(item_table + "util_vals_pro_ocpc_not_been_set")
    ret.add(item_table + "author_id")
    ret.add(item_table + "first_industry_name")
    ret.add(item_table + "relax_cpa_bid")
    ret.add(item_table + "auto_cpa_bid")
    ret.add(item_table + "opt_cost")
    ret.add(item_table + "opt_target_cost")
    ret.add(item_table + "util_vals_pacing_type")
    ret.add(item_table + "target_atv")
    ret.add(item_table + "bid_context_key")
    ret.add(item_table + "last_update_adjust_timestamp")
    ret.add(item_table + "bid_state_info_explore_time_period")
    ret.add(item_table + "bid_state_info_explore_budget_start_time")
    ret.add(item_table + "bid_state_info_explore_budget_status")
    ret.add(item_table + "bid_state_info_explore_budget")
    ret.add(item_table + "bid_state_info_is_live")
    ret.add(item_table + "bid_state_info_ad_status")
    ret.add(item_table + "bid_state_info_advertisable")
    ret.add(item_table + "bid_state_info_is_status_open")
    ret.add(item_table + "bid_state_info_online")
    ret.add(item_table + "bid_state_info_left_budget")
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    item_table = self._config.get("item_table")
    item_table = item_table + "::"
    #ret.add(item_table + "util_vals_p_val")
    #ret.add(item_table + "util_vals_i_val")
    #ret.add(item_table + "util_vals_d_val")
    #ret.add(item_table + "util_vals_pacing_weight")
    return ret

class InnerLoopOcpmCheckPacingRateMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_ocpm_check_pacing_rate_mixer"

  @property
  @strict_types
  def input_common_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def output_common_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    return ret

class InnerLoopOcpmCalcBidMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_ocpm_calc_bid_mixer"

  @property
  @strict_types
  def input_common_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def output_common_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    item_table = self._config.get("item_table")
    ret.add(item_table + "::" + "account_id")
    ret.add(item_table + "::" + "adjust_auto_value_rate")
    ret.add(item_table + "::" + "bid_state_info_account_type")
    ret.add(item_table + "::" + "bid_state_info_bid_strategy")
    ret.add(item_table + "::" + "bid_state_info_bid_type")
    ret.add(item_table + "::" + "bid_state_info_live_launch_type")
    ret.add(item_table + "::" + "campaign_id")
    ret.add(item_table + "::" + "campaign_type")
    ret.add(item_table + "::" + "cost")
    ret.add(item_table + "::" + "group_tag")
    ret.add(item_table + "::" + "has_bid_state_info")
    ret.add(item_table + "::" + "ocpc_action_type")
    ret.add(item_table + "::" + "old_auto_cpa_bid")
    ret.add(item_table + "::" + "old_auto_roi_ratio")
    ret.add(item_table + "::" + "relax_cpa_bid")
    ret.add(item_table + "::" + "relax_roi_ratio")
    ret.add(item_table + "::" + "target_cost")
    ret.add(item_table + "::" + "unit_id")
    ret.add(item_table + "::" + "util_vals_is_fanstop")
    ret.add(item_table + "::" + "util_vals_is_roas")
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    item_table = self._config.get("item_table")
    #ret.add(item_table + "::" + "auto_cpa_bid")
    #ret.add(item_table + "::" + "auto_roi_ratio")
    #ret.add(item_table + "::" + "util_vals_is_normal")
    return ret

class MessageQueueDispatchMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_message_queue_dispatch_mixer"

  @property
  @strict_types
  def input_common_attrs(self):
    ret = set()
    for attr in self._config.get("packed_common_attrs", []):
      ret.add(attr)
    return ret

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    for attr in self._config.get("packed_table_columns", []):
      if "table_name" in attr:
        table = attr["table_name"]

      if "columns" in attr:
        for column in attr["columns"]:
          ret.add(table + "::" + column)
    return ret

class PidMessageQueueDispatchMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_pid_message_queue_dispatch_mixer"

class PidMessageStrategyMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_pid_message_strategy_mixer"

class OcpmAccumulateBackflowMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_accumulate_backflow_mixer"

class MerchantCostLogConsumerMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_cost_log_consumer_mixer"

class MerchantTriggerServerLogConsumerMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_trigger_server_log_consumer_mixer"

class NodiffMessageLoadMix(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nodiff_message_load_mix"

class DiffMerchantBackUpTriggerMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "diff_merchant_back_up_trigger_mixer"

class CostCapSupervisorMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "cost_cap_supervisor_mixer"

class LowestCostSupervisorMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "lowest_cost_supervisor_mixer"

class MainTimerTriggerThreadMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "main_timer_trigger_thread_mixer"

class MobileMainTimerTriggerThreadMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mobile_main_timer_trigger_thread_mixer"

class OcpmAccExploreTriggerThreadMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_acc_explore_trigger_thread_mixer"

class TimerResetMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "timer_reset_mixer"

class TimerRollbackMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "timer_rollback_mixer"

class OcpmAccumulateMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_accumulate_mixer"

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    acc_table = self._config.get("accumulate_table")
    delta_table_key = self._config.get("delta_key")
    ret.add(acc_table + "::" + delta_table_key)

    delta_table = self._config.get("delta_table")
    accumulate_table_key = self._config.get("accumulate_key")
    ret.add(delta_table + "::" + accumulate_table_key)

    accumulate_table_config = self._config.get("acc_table")
    accumulate_table_name = accumulate_table_config.get("table_name")
    acc_columns = accumulate_table_config.get("acc_column", [])
    for acc_column in acc_columns:
      if "delta" in acc_column:
        ret.add(delta_table + "::" + acc_column["delta"])

    acc_meta_window_columns = accumulate_table_config.get("acc_meta_window_column", [])
    for acc_meta_window_column in acc_meta_window_columns:
      if "delta" in acc_meta_window_column:
        ret.add(delta_table + "::" + acc_meta_window_column["delta"])

    acc_list_columns = accumulate_table_config.get("acc_list_column", [])
    for acc_list_column in acc_list_columns:
      if "delta" in acc_list_column:
        ret.add(delta_table + "::" + acc_list_column["delta"])
    return ret


  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    accumulate_table_config = self._config.get("acc_table")
    accumulate_table = self._config.get("accumulate_table")
    acc_columns = accumulate_table_config.get("acc_column", [])
    for acc_column in acc_columns:
      if "column" in acc_column:
        ret.add(accumulate_table + "::" + acc_column["column"])

    acc_meta_window_columns = accumulate_table_config.get("acc_meta_window_column", [])
    for acc_meta_window_column in acc_meta_window_columns:
      if "column" in acc_meta_window_column:
        ret.add(accumulate_table + "::" + acc_meta_window_column["column"] + ".value")
        ret.add(accumulate_table + "::" + acc_meta_window_column["column"] + ".time")
        ret.add(accumulate_table + "::" + acc_meta_window_column["column"] + ".cnt")

    acc_list_columns = accumulate_table_config.get("acc_list_column____", [])
    for acc_list_column in acc_list_columns:
      if "delta" in acc_list_column:
        ret.add(accumulate_table + "::" + acc_list_column["column"])
    return ret

class OcpmPerfMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_perf_mixer"

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    return ret

class OcpmResultSendMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_result_send_mixer"

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    return ret

class OcpmNewDayResetMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_new_day_reset_mixer"

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    return ret

class CalcUnitPriceRatioMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_unit_price_ratio_mixer"

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    return ret

class OcpmResetMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_reset_mixer"

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    return ret


class OcpmUpdateMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_update_mixer"

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    delta_table = self._config.get("tag_table")
    accumulate_table_config = self._config.get("acc_table")
    accumulate_table_name = accumulate_table_config.get("table_name")
    acc_columns = accumulate_table_config.get("acc_column", [])
    for acc_column in acc_columns:
      if "delta" in acc_column:
        ret.add(delta_table + "::" + acc_column["delta"])

    acc_meta_window_columns = accumulate_table_config.get("acc_meta_window_column", [])
    for acc_meta_window_column in acc_meta_window_columns:
      if "delta" in acc_meta_window_column:
        ret.add(delta_table + "::" + acc_meta_window_column["delta"])

    acc_list_columns = accumulate_table_config.get("acc_list_column", [])
    for acc_list_column in acc_list_columns:
      if "delta" in acc_list_column:
        ret.add(delta_table + "::" + acc_list_column["delta"])
    return ret


  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    accumulate_table = self._config.get("tag_table")
    accumulate_table_config = self._config.get("acc_table")
    acc_columns = accumulate_table_config.get("acc_column", [])
    for acc_column in acc_columns:
      if "column" in acc_column:
        ret.add(accumulate_table + "::" + acc_column["column"])

    acc_meta_window_columns = accumulate_table_config.get("acc_meta_window_column", [])
    for acc_meta_window_column in acc_meta_window_columns:
      if "column" in acc_meta_window_column:
        ret.add(accumulate_table + "::" + acc_meta_window_column["column"] + ".value")
        ret.add(accumulate_table + "::" + acc_meta_window_column["column"] + ".time")
        ret.add(accumulate_table + "::" + acc_meta_window_column["column"] + ".cnt")

    acc_list_columns = accumulate_table_config.get("acc_list_column____", [])
    for acc_list_column in acc_list_columns:
      if "delta" in acc_list_column:
        ret.add(accumulate_table + "::" + acc_list_column["column"])
    return ret


class OcpmAccountCpaCoefOptimalMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_account_cpa_coef_optimal_mixer"

  exp_conf_columns = [
    'exp_conf.account_ocpm.cpa_coef_up_range',
    'exp_conf.account_ocpm.cpa_coef_low_range',
    'exp_conf.account_ocpm.cpa_coef_upper',
    'exp_conf.account_ocpm.cpa_coef_lower',
    'exp_conf.account_ocpm.d_out_decay',
    'exp_conf.account_ocpm.auto_adjust_ratio',
    'exp_conf.account_ocpm.auto_adjust_target_factor',
    'exp_conf.account_ocpm.backflow_skip_bounder',
    'exp_conf.account_ocpm.base_cost_cost_thr',
    'exp_conf.account_ocpm.backflow_window_sec',
    'exp_conf.account_ocpm.backflow_bound_ratio_upper',
    'exp_conf.account_ocpm.backflow_bound_ratio_lower',
    'exp_conf.account_ocpm.smooth_upper',
    'exp_conf.account_ocpm.smooth_lower',
    'exp_conf.account_ocpm.min_future_ratio',
    'exp_conf.account_ocpm.max_future_ratio',
    'exp_conf.account_ocpm.max_step_num',
    'exp_conf.account_ocpm.optimal_step',
    'exp_conf.account_ocpm.pow_coef',
    'exp_conf.account_ocpm.cpa_coef_uppe',
    'exp_conf.account_ocpm.enable_account_all',
    'exp_conf.account_ocpm.smooth_max',
    'exp_conf.account_ocpm.yesterday_smooth_coef',
    'exp_conf.account_ocpm.enable_roas_all_target_cost',
    'exp_conf.account_ocpm.roi_smooth_ratio',
    'exp_conf.account_ocpm.today_stable_cost_ratio_v2',
    'exp_conf.account_ocpm.window_stable_cost_ratio',
    'exp_conf.account_ocpm.min_cost_cpa_bid',
    'exp_conf.account_ocpm.account_min_cost',
    'exp_conf.account_ocpm.max_smooth_cost',
    'exp_conf.account_ocpm.cpa_window_step_bound',
    'exp_conf.account_ocpm.stable_max_cpa_ratio',
    'exp_conf.account_ocpm.universe_optimal_fine_tuning',
    'exp_conf.account_ocpm.decay_when_exceed',
    'exp_conf.account_ocpm.decay_when_not_exceed',
    'exp_conf.account_ocpm.history_decay',
    'exp_conf.account_ocpm.optim_search_lower_step',
    'exp_conf.account_ocpm.enable_stability_mix',
    'exp_conf.account_ocpm.universe_enable_guarantee_min_cost',
    'exp_conf.account_ocpm.enable_guarantee_min_cost',
    'exp_conf.account_ocpm.fix_account_expect_before_use_base',
    'exp_conf.account_ocpm.optimal_kd_out',
    'exp_conf.account_ocpm.universe_operation_boost_cpa_price',
    'exp_conf.account_ocpm.use_backflow',
    'exp_conf.account_ocpm.window_cpa_ratio_lower_range',
    'exp_conf.account_ocpm.use_backflow_account_all',
    'exp_conf.account_ocpm.first_day_cpa_coef_lower',
    'exp_conf.account_ocpm.use_simple_smooth_cost',
    'exp_conf.account_ocpm.simple_smooth_factor',
    'exp_conf.account_ocpm. backflow_white_ocpx_set',
    'exp_conf.account_ocpm.enable_account_all_base_cost',
    'exp_conf.account_ocpm.enable_budget_cap',
    'exp_conf.account_ocpm.first_day_roi_smooth_ratio',
  ]

  account_attrs = [
    'product_name',
    'ocpx_action_type',
    'is_universe_opt',
    'campaign_type',
    'deep_conversion_type',
    'second_industry_id',
  ]

  account_tag_in_attrs = [
    'unit_id_attr',
    'account_id_attr',
    'group_tag',
    's2.cpa_coef',
    's2.target_factor',
    's2.auto_adjust_info.today_auto_adjust_cost',
    's2.today_cost',
    's2.account_bidding_info.placement_type',
    's2.today_origin_cost',
    's2.today_target_cost',
    'mcb_level',
    's2.yesterday_cv',
    's2.today_cost_unit_sum',
    's2.today_target_cost_unit_sum',
    's2.account_bidding_info.window_cost_unit_sum',
    's2.account_bidding_info.base_window_cost_unit_sum',
    's2.account_bidding_info.window_before_cost_unit_sum',
    's2.account_bidding_info.base_window_before_cost_unit_sum',
    's2.account_bidding_info.window_target_cost_unit_sum',
    's2.account_bidding_info.window_deep_target_cost_unit_sum',
    's2.account_bidding_info.today_deep_target_cost_unit_sum',
    's2.account_bidding_info.target_cpa_cost_sum',
    's2.account_bidding_info.today_expect_target_cost',
    's2.account_bidding_info.roas_24h_all_today_target_cost_unit_sum',
    's2.account_bidding_info.today_backflow_delta',
    's2.account_bidding_info.today_backflow_label',
    's2.account_bidding_info.window_backflow_delta',
    's2.account_bidding_info.window_backflow_label',
    's2.window_data.cpa_coef',
    's2.window_data.cost',
    's2.window_data.target_cost',
    's2.account_bidding_info.today_deep_target_cost',
    's2.window_data.deep_target_cost',
    's2.account_bidding_info.today_flow_ratio',
    's2.account_bidding_info.window_flow_ratio',
    's2.account_bidding_info.roas_24h_all_today_target_cost',
    's2.account_bidding_info.ad_queue_type',
    's2.account_bidding_info.yesterday_cost',
    's2.account_bidding_info.cpa_window',
    's2.account_bidding_info.expect_cost',
    's2.account_bidding_info.expect_target_cost',
    's2.account_bidding_info.expect_cpa_ratio',
    's2.account_bidding_info.expect_before',
    's2.account_bidding_info.roas_24h_all_today_target_cost_attr',
    's2.today_price_after_billing_separate',
    's2.window_data.origin_cost',
    's2.cpa_pid.d_out',
    's2.cpa_coef',
    's2.account_bidding_info.today_backflow_all_delta',
    's2.account_bidding_info.backflow_all_label',
    's2.account_bidding_info.window_backflow_all_delta',
    's2.account_bidding_info.window_backflow_all_label',
    's2.account_bidding_info.roas_24h_today_backflow_all_delta',
    's2.account_bidding_info.base_window_cost',
    's2.left_budget',

  ]

  account_tag_out_attrs = [
    's2.cpa_coef',
    's2.target_factor',
    's2.today_cost_unit_sum',
    's2.today_origin_cost_unit_sum',
    's2.today_price_after_billing_separate_unit_sum',
    's2.today_target_cost_unit_sum',
    's2.today_target_cpa_unit_sum',
    's2.account_bidding_info.window_cost_unit_sum',
    's2.account_bidding_info.base_window_cost_unit_sum',
    's2.account_bidding_info.window_before_cost_unit_sum',
    's2.account_bidding_info.base_window_before_cost_unit_sum',
    's2.account_bidding_info.window_after_cost_unit_sum',
    's2.account_bidding_info.window_target_cost_unit_sum',
    's2.account_bidding_info.window_deep_target_cost_unit_sum',
    's2.account_bidding_info.today_deep_target_cost_unit_sum',
    's2.account_bidding_info.window_cpa_coef_unit_sum',
    's2.account_bidding_info.target_cpa_cost_sum',
    's2.account_bidding_info.window_expect_cv',
    's2.account_bidding_info.window_expect_target_cost',
    's2.account_bidding_info.today_expect_cv',
    's2.account_bidding_info.today_expect_target_cost',
    's2.account_bidding_info.window_deep_expect_target_cost_unit_sum',
    's2.account_bidding_info.today_deep_expect_target_cost_unit_sum',
    's2.account_bidding_info.base_today_origin_cost_unit_sum',
    's2.account_bidding_info.today_target_cost_unit_sum_attr',
    's2.account_bidding_info.roas_24h_all_today_target_cost_unit_sum',
    's2.account_bidding_info.roas_24h_all_today_target_cost_unit_sum_attr',
    's2.account_bidding_info.today_backflow_delta_raw',
    's2.account_bidding_info.window_backflow_delta_raw',
    's2.account_bidding_info.today_backflow_delta',
    's2.account_bidding_info.today_backflow_label',
    's2.account_bidding_info.window_backflow_delta',
    's2.account_bidding_info.window_backflow_label',
    's2.account_bidding_info.today_backflow_stat_delta',
    's2.account_bidding_info.window_backflow_stat_delta',
    's2.account_bidding_info.cpa_window',
    's2.account_bidding_info.stable_cost_min_cpa_coef_window',
    's2.account_bidding_info.account_min_cost_cpa_coef_window_smooth',
    's2.account_bidding_info.avg_cpa_coef_window',
    's2.account_bidding_info.avg_target_cpa_window',
    's2.account_bidding_info.expect_stable_remain_cost',
    's2.account_bidding_info.remain_flow_ratio',
    's2.account_bidding_info.today_stable_cost',
    's2.account_bidding_info.origin_best_cpa_coef',
    's2.account_bidding_info.stable_cpa_max_cpa_coef_whole_day',
    's2.account_bidding_info.stable_min_cpa_coef_window',
    's2.account_bidding_info.stable_cpa_ratio',
    's2.account_bidding_info.expect_cost',
    's2.account_bidding_info.expect_target_cost',
    's2.account_bidding_info.expect_cpa_ratio',
    's2.account_bidding_info.expect_before',
    's2.cpa_pid.d_out',
    's2.cpa_coef',
    's2.account_bidding_info.set_rage_flag',
    's2.account_bidding_info.today_backflow_all_delta',
    's2.account_bidding_info.backflow_all_label',
    's2.account_bidding_info.window_backflow_all_delta',
    's2.account_bidding_info.window_backflow_all_label',
    's2.account_bidding_info.roas_24h_today_backflow_all_delta_raw',
    's2.account_bidding_info.roas_24h_today_backflow_all_label',
    's2.account_bidding_info.base_window_cost',
    's2.account_bidding_info.left_budget_cpa_coef',
  ]

  unit_tag_in_attrs = [
    'ocpx_action_type',
    'unit_id',
    's2.today_cost',
    's2.today_origin_cost',
    's2.today_price_after_billing_separate',
    's2.today_target_cost',
    's2.today_target_cost_attr',
    's2.roas_24h_all_today_target_cost',
    's2.roas_24h_all_today_target_cost_attr',
    's2.target_cpa_sum',
    's2.window_data.cost',
    's2.window_data.target_cost',
    's2.window_data.deep_target_cost',
    's2.account_bidding_info.today_deep_target_cost',
    's2.account_bidding_info.window_flow_ratio',
    's2.window_data.origin_cost',
    's2.account_bidding_info.today_flow_ratio',
    's2.window_data.price_after',
    's2.window_data.cpa_coef_cost_sum',
    's2.window_data.target_cpa_sum',
    's2.window_data.predict_conversion',
    's2.window_data.expect_target_cost',
    's2.window_data.deep_expect_target_cost',
    's2.predict_conversion',
    's2.account_bidding_info.today_expect_target_cost',
    's2.account_bidding_info.today_deep_expect_target_cost',
    's2.log_process_timestamp',
    'backflow_data_llsid_map',
  ]

  unit_tag_out_attrs = [
    "valid"
  ]

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    # exp_config 表都是输入
    delta_table = "AccountTagTable",
    for column in self.exp_conf_columns:
      ret.add(str(delta_table) + "::" + str(column))
    # account 表都是输入
    delta_table = "AccountTable"
    for column in self.account_attrs:
      ret.add(delta_table + "::" + column)
    # backflow 表都是输入
    ret.add("BackFlowUnitTagTable" + "::" + "moss_map")

    # account_tag 表输入字段
    delta_table = "AccountTagTable"
    for column in self.account_tag_in_attrs:
      ret.add(delta_table + "::" + column)
    # unit_tag 表输入字段
    delta_table = "UnitTagTable"
    for column in self.unit_tag_in_attrs:
      ret.add(delta_table + "::" + column)
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    # account_tag 输出字段
    delta_table = "AccountTagTable"
    for column in self.account_tag_out_attrs:
      ret.add(delta_table + "::" + column)
    # unit_tag 表输出字段
    delta_table = "UnitTagTable"
    for column in self.unit_tag_out_attrs:
      ret.add(delta_table + "::" + column)
    return ret

class OcpmAccumulatePrepareMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_accumulate_prepare_mixer"

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    delta_table = self._config.get("delta_table")
    delta_table_key = self._config.get("delta_key")
    ret.add(delta_table + "::" + delta_table_key)
    input_column = self._config.get("delta_column_prepare_input", {})
    ret.add(delta_table + "::" + input_column["price_column"])
    ret.add(delta_table + "::" + input_column["action_type_column"])
    ret.add(delta_table + "::" + input_column["deep_conversion_type_column"])
    ret.add(delta_table + "::" + input_column["cpa_bid_column"])
    ret.add(delta_table + "::" + input_column["cpa_coef_column"])
    ret.add(delta_table + "::" + input_column["roi_ratio_column"])
    ret.add(delta_table + "::" + input_column["auto_cpa_bid_column"])
    ret.add(delta_table + "::" + input_column["auto_roas_column"])
    ret.add(delta_table + "::" + input_column["target_cost_column"])
    ret.add(delta_table + "::" + input_column["target_cost_attr_column"])
    ret.add(delta_table + "::" + input_column["deep_target_cost_column"])
    ret.add(delta_table + "::" + input_column["is_account_bidding_column"])
    ret.add(delta_table + "::" + input_column["is_rta_column"])
    ret.add(delta_table + "::" + input_column["ctr_column"])
    ret.add(delta_table + "::" + input_column["cvr_column"])
    ret.add(delta_table + "::" + input_column["ltv_column"])
    ret.add(delta_table + "::" + input_column["deep_cvr_column"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    delta_table = self._config.get("delta_table")
    output_column = self._config.get("delta_column_prepare_output", {})
    ret.add(delta_table + "::" + output_column["expect_cv_column"])
    ret.add(delta_table + "::" + output_column["expect_deep_cv_column"])
    ret.add(delta_table + "::" + output_column["expect_target_cost_column"])
    ret.add(delta_table + "::" + output_column["expect_deep_target_cost_column"])
    ret.add(delta_table + "::" + output_column["cpa_coef_cost_sum_info_column"])
    ret.add(delta_table + "::" + output_column["auto_cpa_bid_cost_sum_column"])
    ret.add(delta_table + "::" + output_column["auto_roas_cost_sum_column"])
    ret.add(delta_table + "::" + output_column["target_cpa_sum_info_column"])
    #ret.add(delta_table + "::" + output_column["is_roi_column"])
    return ret

class FillSessionTableMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fill_session_table_mixer"

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    item_table = self._config.get("item_table")
    fill_columns = self._config.get("fill_column", [])
    for fill_column in fill_columns:
      ret.add(item_table + "::" + fill_column)

    session_table_config = self._config.get("session_tables", [])
    for session_table in session_table_config:
      session_table_name = session_table["table_name"]
      session_table_item_key = session_table["item_key"]
      ret.add(item_table + "::" + session_table_item_key)
      table_fill_columns = session_table.get("fill_column", [])
      for table_fill_column in table_fill_columns:
        ret.add(item_table + "::" + table_fill_column)
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()

    fill_columns = self._config.get("fill_column", [])

    session_table_config = self._config.get("session_tables", [])
    for session_table in session_table_config:
      session_table_name = session_table["table_name"]
      session_table_item_key = session_table["item_key"]

      for fill_column in fill_columns:
        ret.add(session_table_name + "::" + fill_column)

      table_fill_columns = session_table.get("fill_column", [])
      for table_fill_column in table_fill_columns:
        ret.add(session_table_name + "::" + table_fill_column)

    return ret

class AdLogOcpmMessageQueueDispatchMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_ocpm_account_message_queue_dispatch_mixer"
  @property
  @strict_types
  def input_common_attrs(self):
    ret = set()
    for attr in self._config.get("packed_common_attrs", []):
      ret.add(attr)
    ret.add("task_queue_id")
    ret.add("main_tag_send_soft_hard")
    ret.add("assist_tag_send")
    ret.add("assist_tag_send_soft_hard")

    return ret
  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    for attr in self._config.get("packed_table_columns", []):
      if "table_name" in attr:
        table = attr["table_name"]
      if "columns" in attr:
        for column in attr["columns"]:
          ret.add(table + "::" + column)
    return ret
  @strict_types
  def _check_config(self) -> None:
    check_arg("task_queue_id" in self._config, "必须写入task_queue_id参数")
    check_arg("main_tag_send_soft_hard" in self._config, "必须写入main_tag_send_soft_hard参数")
    check_arg("assist_tag_send" in self._config, "必须写入assist_tag_send参数")
    check_arg("assist_tag_send_soft_hard" in self._config, "必须写入assist_tag_send_soft_hard参数")

class InnerLoopStepOneWriteToRedisMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_step_one_to_redis_mixer"

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    item_table = self._config.get("item_table")
    # smock
    ret.add(item_table + "::" + "has_bid_state_info_ptr")
    ret.add(item_table + "::" + "bid_state_info_ptr")
    message_seq_attr = self._config.get("message_seq")
    ret.add(message_seq_attr["item_table"] + "::" + message_seq_attr["column"])
    event_server_timestamp_attr = self._config.get("event_server_timestamp")
    ret.add(event_server_timestamp_attr["item_table"] + "::" + event_server_timestamp_attr["column"])
    unit_id_attr = self._config.get("unit_id")
    ret.add(unit_id_attr["item_table"] + "::" + unit_id_attr["column"])
    msg_attr_info = self._config.get("msg_attr_info")
    for table_item in msg_attr_info.values():
      ret.add(table_item["item_table"] + "::" + table_item["column"])
    bid_ctx_attr_info = self._config.get("bid_ctx_attr_info")
    for table_item in bid_ctx_attr_info.values():
      ret.add(table_item["item_table"] + "::" + table_item["column"])
    process_utils_attr_info = self._config.get("process_utils_attr_info")
    for table_item in process_utils_attr_info.values():
      ret.add(table_item["item_table"] + "::" + table_item["column"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    item_table = self._config.get("item_table")
    #ret.add(item_table + "::" + fill_column)
    return ret

class InnerLoopStrategyDataFetchMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_strategy_data_fetch_mixer"
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class InnerLoopStrategyDataSaveMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_strategy_data_save_mixer"
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class InnerLoopTableLiteMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_table_lite_mixer"
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("unit_id")
    attrs.add("group_tag")
    return attrs
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    item_table = self._config.get("item_table")
    item = self._config.get("has_bid_state_info_ptr")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_explore_time_period")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_explore_budget_start_time")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_explore_budget_status")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_explore_budget")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_is_live")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_ad_status")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_advertisable")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_is_status_open")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_online")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_left_budget")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_budget")
    ret.add(item["item_table"] + "::" + item["column"])
    #ret.add(item_table + "::" + "bid_state_info_account_type")
    #ret.add(item_table + "::" + "bid_state_info_bid_strategy")
    #ret.add(item_table + "::" + "bid_state_info_bid_type")
    #ret.add(item_table + "::" + "bid_state_info_live_launch_type")
    item = self._config.get("bid_state_info_budget")
    ret.add(item["item_table"] + "::" + item["column"])

    item = self._config.get("bid_state_info_account_id")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_campaign_id")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_author_id")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_live_stream_id")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_speed_type")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_promotion_type")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_campaign_type")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_cpa_bid")
    ret.add(item["item_table"] + "::" + item["column"])
    item = self._config.get("bid_state_info_roi_ratio")
    ret.add(item["item_table"] + "::" + item["column"])
    return ret

class InnerloopOcpmCheckPacingRateMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_ocpm_check_pacing_rate_mixer"
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    util_vals_attr_info = self._config.get("util_vals_attr_info")
    for table_item in util_vals_attr_info.values():
      attrs.add(table_item["item_table"] + "::" + table_item["column"])
    bid_context_attr_info = self._config.get("bid_context_attr_info")
    for table_item in bid_context_attr_info.values():
      attrs.add(table_item["item_table"] + "::" + table_item["column"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    output_attr_info = self._config.get("output_attr_info")
    for table_item in output_attr_info.values():
      ret.add(table_item["item_table"] + "::" + table_item["column"])
    return ret

class InnerloopMonitorMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_monitor_mixer"
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    util_vals_attr_info = self._config.get("util_vals_attr_info")
    for table_item in util_vals_attr_info.values():
      attrs.add(table_item["item_table"] + "::" + table_item["column"])
    bid_context_attr_info = self._config.get("bid_context_attr_info")
    for table_item in bid_context_attr_info.values():
      attrs.add(table_item["item_table"] + "::" + table_item["column"])
    return attrs

class InnerLoopTriggerMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_ocpm_trigger_mixer"
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    key_attr = self._config.get("key_id")
    attrs.add(key_attr["item_table"] + "::" + key_attr["column"])
    return attrs
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class InnerLoopBuildUnitListMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_build_unit_list_mixer"
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class InnerloopOcpmBidContextAccumulateMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_loop_ocpm_bid_context_mixer"
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    message_seq_attr = self._config.get("message_seq")
    attrs.add(message_seq_attr["item_table"] + "::" + message_seq_attr["column"])
    event_server_timestamp_attr = self._config.get("event_server_timestamp")
    attrs.add(event_server_timestamp_attr["item_table"] + "::" + event_server_timestamp_attr["column"])
    bid_stat_attr_info = self._config.get("bid_state_info")
    for table_item in bid_stat_attr_info.values():
      attrs.add(table_item["item_table"] + "::" + table_item["column"])
    return attrs
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    msg_attr_info = self._config.get("msg_attr_info")
    for table_item in msg_attr_info.values():
      ret.add(table_item["item_table"] + "::" + table_item["column"])
    bid_ctx_attr_info = self._config.get("bid_ctx_attr_info")
    for table_item in bid_ctx_attr_info.values():
      ret.add(table_item["item_table"] + "::" + table_item["column"])
    process_utils_attr_info = self._config.get("process_utils_attr_info")
    for table_item in process_utils_attr_info.values():
      ret.add(table_item["item_table"] + "::" + table_item["column"])
    return ret

class OcpmExperimentUpdateMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_experiment_update_mixer"

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self):
    ret = set()
    return ret

class OcpmSyncDataMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_sync_data_mixer"

  @strict_types
  def _check_config(self) -> None:
    check_arg("session_table_name" in self._config, "必须指明session表名称")
    check_arg("tag_table_name" in self._config, "必须指明tag表名称")
    check_arg("redis_name" in self._config, "必须指明拉取缓存的redis")
