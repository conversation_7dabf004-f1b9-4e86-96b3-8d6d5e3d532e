#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dragonfly.common_leaf_util import check_arg, strict_types

class AdThreadInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_thread_info_enricher"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set([self._config["output_attr"]])
    return ret

class AdTimerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_timer_enricher"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set([self._config["ts_column"]])
    return ret

class AdKafkaMessageFetchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_kafka_message_fetch_enricher"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set([self._config["out_column"]])
    return ret

class AdKafkaFetchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_kafka_fetch_enricher"

class PidTestMessageFetchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_test_message_fetch_enricher"

class AdKafkaQueueGetEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_kafka_queue_get_enrich"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

class AdTestMessageFetchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_test_message_fetch_enricher"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    return ret

class AdBudgetKafkaMessageFetchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_budget_kafka_message_fetch_enricher"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set([self._config["out_column"]])
    return ret


class AdRuntimeEnvEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_runtime_env_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("zk_configs", []):
      if isinstance(attr, str):
        attrs.add(attr)
      elif "output_attr" in attr:
        attrs.add(attr["output_column"])
    attrs.add(self._config.get("shard_id_out_column"));
    return attrs

class AdParseProtoFromKconfEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_parse_proto_from_kconf_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set(["kconf_path", "proto_msg"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set([self._config["output_attr"]])
    return ret

class AdSubflowShardIdEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_subflow_shard_id_enricher"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_column"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("mod_dividend_column"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_column"))
    return attrs

class AdTableLiteEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_table_lite_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    default_item_table = self._config.get("item_table")
    account_info = self._config.get("account_info", {})
    if "id_column" in account_info:
      attrs.add(default_item_table + "::" + account_info.get("id_column"))

    campaign_info = self._config.get("campaign_info", {})
    if "id_column" in campaign_info :
      attrs.add(default_item_table + "::" + campaign_info.get("id_column"))

    unit_info = self._config.get("unit_info", {})
    if "id_column" in unit_info :
      attrs.add(default_item_table + "::" + unit_info.get("id_column"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    default_item_table = self._config.get("item_table")
    account_info = self._config.get("account_info", {})
    if "column_config" in account_info:
      for column_config in account_info.get("column_config", []):
        if "target" in column_config:
          attrs.add(default_item_table + "::" + column_config.get("target"))

    campaign_info = self._config.get("campaign_info", {})
    if "column_config" in campaign_info:
      for column_config in campaign_info.get("column_config", []):
        if "target" in column_config:
          attrs.add(default_item_table + "::" + column_config.get("target"))
    return attrs

class McbLogTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mcb_log_trigger_enricher"

  # TODO: implement real functional

class McbBudgetTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mcb_budget_trigger_enricher"

class BidPerformancePredictEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bid_performance_predict_enricher"

class BidDataUpdateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bid_data_update_enricher"

  # TODO: implement real functional

class BidLogTransformEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bid_log_transform_enricher"

  # TODO: implement real functional

class BudgetLogTransformEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "budget_log_transform_enricher"

class BidAccumulatorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bid_accumulator_enricher"

  # TODO: implement real functional

class AdDispatchItemAttrsByKeyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_dispatch_item_attrs_by_key_enricher"

  # TODO: implement real functional

class AdDispatchItem2CommonByKeyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_dispatch_item2common_by_key_enricher"

  # TODO: implement real functional

class AdDispatchCommon2ItemByKeyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_dispatch_common2item_by_key_enricher"

  # TODO: implement real functional

class McbConfigFetchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mcb_config_fetch_enricher"

class PiddEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pidd_enricher"

class PiddRetentionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pidd_retention_enricher"

class PiddCemEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pidd_cem_enricher"

class FinalResultUpdateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "final_result_update_enricher"

class StoreExperimentResultEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "store_experiment_result_enricher"

class AboCampaignCpaCoefOptimalEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "abo_campaign_cpa_coef_optimal_enricher"

class AdDataFrameDebugObserver(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_data_frame_debug_observer"

class KmlFeatureExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kml_feature_extract_enricher"

class KmlRpcInferEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kml_rpc_infer_enricher"

class ModelTransformBidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "model_transform_bid_enricher"

class AdRedisCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_redis_common_attr_enricher"

class AdRedisItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_redis_item_attr_enricher"

class AdRollbackAdmitEnrich(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_rollback_admit_enrich"

  def addCloumn(self, cloumn_name) -> str:
    ret = ""
    clomn_info = self._config.get(cloumn_name)
    if "item_table" in clomn_info and "column" in clomn_info:
      ret = clomn_info["item_table"] + "::" + clomn_info["column"]
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("trigger_type"))
    attrs.add(str(self._config.get("enable_clear_cache")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self.addCloumn("campaign_id"))
    attrs.add(self.addCloumn("account_id"))
    attrs.add(self.addCloumn("group_tag"))
    attrs.add(self.addCloumn("need_rollback"))
    return attrs

class BidFillBaseInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bid_fill_base_info_enricher"

class BudgetFillBaseInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "budget_fill_base_info_enricher"

class AccountBudgetOptimizationEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "account_budget_optimization_enricher"

class AdLogOcpmTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_log_ocpm_trigger_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_common_column"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_common_column"))
    return attrs

  def get_item_attr(self, item_name) -> str:
    column_conf = self._config.get(item_name, {})
    if "item_table" in column_conf and "column" in column_conf:
      return column_conf.get("item_table") + "::" + column_conf.get("column")
    return ""

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self.get_item_attr("last_cpa_trigger_ts"))
    attrs.add(self.get_item_attr("cost"))
    attrs.add(self.get_item_attr("mcb_level"))
    attrs.add(self.get_item_attr("avg_cpa_bid"))
    attrs.add(self.get_item_attr("combine_key"))
    attrs.add(self.get_item_attr("account_separate_info_unit_cpa_bid"))
    attrs.add(self.get_item_attr("event_server_timestamp"))

    attrs.add(self.get_item_attr("block_second"))
    attrs.add(self.get_item_attr("cost_threshold"))
    attrs.add(self.get_item_attr("batch_cpa_ratio"))
    attrs.add(self.get_item_attr("force_update_second"))
    attrs.add(self.get_item_attr("min_avg_cpa_bid"))

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class PidHcAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_hc_admit"

class PidEESearchBudgetAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_ee_search_budget_admit"

class PidOuterloopEeBudgetAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_outerloop_ee_budget_admit"

class PidSmbUnitAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_smb_unit_admit"

class PidSmbAccountAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_smb_account_admit"

class PidBonusAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_bonus_admit"

class PidCrossAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_cross_admit"

class PidAccumateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_accumate_enricher"

class PidCommonConfigEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_common_config"

class PidStrategyCoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_strategy_core_enricher"

class PidUniverseEcpcTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_universe_ecpc_trigger_enricher"

class PidUniverseBonusControlTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_universe_bonus_control_trigger_enricher"

class PidUniverseBonusControlExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_universe_bonus_control_exec_enricher"

class PidTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_trigger_enricher"

class PidBonusTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_bonus_trigger_enricher"

class PidEESearchBudgetTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_ee_search_budget_trigger_enricher"

class PidOuterloopEeBudgetTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_outerloop_ee_budget_trigger_enricher"

class PidSmbUnitTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_smb_unit_trigger"

class PidSmbAccountTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_smb_account_trigger"

class PidUniverseBillingSeparateTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_universe_billing_separate_trigger_enricher"

class PidCrossTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_cross_trigger"

class PidSmbUnitExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_smb_unit_exec"

class PidSmbAccountExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_smb_account_exec"

class PidUniverseBillingSeparateExec(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_universe_billing_separate_exec"

class PidUniverseDarkAutoControlTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_universe_dark_auto_control_trigger_enricher"

class PidUniverseDarkAutoControlExec(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_universe_dark_auto_control_exec"

class PidBonusExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_bonus_exec_enricher"

class PidCrossExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_cross_exec_enricher"

class PidHcExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_hc_exec"

class PidEESearchBudgetExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_ee_search_budget_exec"

class PidOuterloopEeBudgetExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_outerloop_ee_budget_exec"

class PidAfterExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_after_exec"

class PidBillingSeparateAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_billing_separate_admit"

class PidBillingSeparateTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_billing_separate_trigger_enricher"

class PidBillingSeparateExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_billing_separate_exec_enricher"

class PidRevenueOptimizeBaseAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_revenue_optimize_base_admit"

class PidRevenueOptimizeBaseTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_revenue_optimize_base_trigger_enricher"

class PidRevenueOptimizeBaseExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_revenue_optimize_base_exec_enricher"

class PidRevenueOptimizeExpAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_revenue_optimize_exp_admit"

class PidRevenueOptimizeExpTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_revenue_optimize_exp_trigger_enricher"

class PidRevenueOptimizeExpExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_revenue_optimize_exp_exec_enricher"

class EspAuthorFansPcocExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "esp_author_fans_pcoc_exec"

class EspAuthorFansPcocAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "esp_author_fans_pcoc_admit"

class EspAuthorFansPcocTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "esp_author_fans_pcoc_trigger_enricher"

class PidGimbalAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_gimbal_admit"

class PidGimbalTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_gimbal_trigger_enricher"

class PidGimbalExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_gimbal_exec_enricher"

class PidInspirePhotoRoiAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_inspire_photo_roi_admit"

class PidInspirePhotoRoiTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_inspire_photo_roi_trigger_enricher"

class PidInspirePhotoRoiExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_inspire_photo_roi_exec_enricher"

class PidIncentiveAutoDarkBtrAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_incentive_auto_dark_btr_admit"

class PidIncentiveAutoDarkBtrTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_incentive_auto_dark_btr_trigger_enricher"

class PidIncentiveAutoDarkBtrExecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_incentive_auto_dark_btr_exec_enricher"