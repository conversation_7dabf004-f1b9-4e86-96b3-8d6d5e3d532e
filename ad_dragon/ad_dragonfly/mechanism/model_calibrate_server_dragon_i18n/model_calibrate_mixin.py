#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from .model_calibrate_retriever import *
from .model_calibrate_observer import *
from .model_calibrate_mixer import *
from .model_calibrate_enricher import *
class MockDataRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mock_data_frame"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

class ModelCalibrateMixin(CommonLeafBaseMixin):
  def mock_data_frame(self, **kwargs):
    self._add_processor(MockDataRetriever(kwargs))
    return self

  def ad_calibrate_delay_training_data_accumulator_enricher(self, **kwargs):
    self._add_processor(AdCalibrateDelayTrainingDataAccumulatorEnricher(kwargs))
    return self

  def ad_calibrate_simple_training_data_accumulator_enricher(self, **kwargs):
    self._add_processor(AdCalibrateSimpleTrainingDataAccumulatorEnricher(kwargs))
    return self

  def ad_calibrate_table_fetch_from_cache_enricher(self, **kwargs):
    self._add_processor(AdCalibrateTableFetchFromCacheEnricher(kwargs))
    return self

  def ad_calibrate_table_clear_to_local_cache_enricher(self, **kwargs):
    self._add_processor(AdCalibrateTableClearToLocalCacheEnricher(kwargs))
    return self

  def ad_calibrate_table_fetch_from_redis_observer(self, **kwargs):
    self._add_processor(AdCalibrateTableFetchFromRedisObserver(kwargs))
    return self

  def ad_calibrate_clear_redis_observer(self, **kwargs):
    self._add_processor(AdCalibrateClearRedisObserver(kwargs))
    return self

  def ad_calibrate_delay_label_calculate_enricher(self, **kwargs):
    self._add_processor(AdCalibrateDelayLabelCalculateEnricher(kwargs))
    return self

  def ad_calibrate_simple_label_calculate_enricher(self, **kwargs):
    self._add_processor(AdCalibrateSimpleLabelCalculateEnricher(kwargs))
    return self
  
  def ad_calibrate_common_info_checker_enricher(self, **kwargs):
    self._add_processor(AdCalibrateCommonInfoCheckerEnricher(kwargs))
    return self

  def get_model_calibrate_score(self, **kwargs):
        self._add_processor(ModelCalibrateResultEnrich(kwargs))
        return self
  def get_lower_bound_index(self, **kwargs):
      self._add_processor(FindLowerBoundItemAttrEnricher(kwargs))
      return self
  
  def calculate_calibrate_ratio(self, **kwargs):
      self._add_processor(CalibrateRatioEnrich(kwargs))
      return self
  
  def city_hash_enricher(self, **kwargs):
      self._add_processor(CityHashNewEnricher(kwargs))
      return self

  def zadd_observer(self, **kwargs):
    self._add_processor(ZAddRedisObserver(kwargs))
    return self

  def ad_calibrate_update_result_redis_observer(self, **kwargs):
    self._add_processor(AdCalibrateUpdateResultRedisObserve(kwargs))
    return self

  def ad_calibrate_update_result_retriever(self, **kwargs):
    self._add_processor(AdCalibrateUpdateResultRetriever(kwargs))
    return self

  def zrem_range_observer(self, **kwargs):
    self._add_processor(ZremRangeRedisObserver(kwargs))
    return self
    