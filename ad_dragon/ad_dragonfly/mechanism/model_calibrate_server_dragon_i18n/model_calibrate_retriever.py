#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import LeafR<PERSON>riever
from dragonfly.common_leaf_util import check_arg, strict_types
# class MockDataRetriever(LeafRetriever):
  # @classmethod
  # @strict_types
  # def get_type_alias(cls) -> str:
    # return "mock_data_frame"

  # @property
  # @strict_types
  # def output_common_attrs(self) -> set:
    # return set()
class AdCalibrateUpdateResultRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_calibrate_update_result_retriever"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("key_hash"))
    return ret

