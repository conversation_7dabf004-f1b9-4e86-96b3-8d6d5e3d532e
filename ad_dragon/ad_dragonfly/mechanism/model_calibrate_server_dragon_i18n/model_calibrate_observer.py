#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import LeafObserver
from dragonfly.common_leaf_util import check_arg, strict_types

class AdCalibrateClearRedisObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_calibrate_clear_redis_observer"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("kcc_cluster"))
    ret.add(self._config.get("timeout_ms"))
    ret.add(self._config.get("input_item_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

class AdCalibrateTableFetchFromRedisObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_calibrate_table_fetch_from_redis_observer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("kcc_cluster"))
    ret.add(self._config.get("timeout_ms"))
    ret.add(self._config.get("input_item_attr"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_item_accumulator_info_key"))
    ret.add(self._config.get("input_item_score_info_key"))
    ret.add(self._config.get("input_item_key_timestamp_key"))
    return ret

class ZAddRedisObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "zadd_observer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("kcc_cluster"))
    ret.add(self._config.get("redis_key"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("redis_score_item"))
    ret.add(self._config.get("redis_value_item"))
    return ret

class AdCalibrateUpdateResultRedisObserve(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_calibrate_update_result_redis_observer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("kcc_cluster"))
    ret.add(self._config.get("input_redis_key"))
    ret.add(self._config.get("max_update_interval_min"))
    return ret

class ZremRangeRedisObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "zrem_range_observer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("kcc_cluster"))
    ret.add(self._config.get("redis_key"))
    ret.add(self._config.get("start_score"))
    ret.add(self._config.get("end_score"))
    return ret