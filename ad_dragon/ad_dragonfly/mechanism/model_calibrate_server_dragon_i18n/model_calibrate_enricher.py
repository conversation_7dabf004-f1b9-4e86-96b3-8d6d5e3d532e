#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import <PERSON><PERSON><PERSON><PERSON><PERSON>, try_add_table_name
from dragonfly.common_leaf_util import check_arg, strict_types
class AdCalibrateProtobufBuildAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_calibrate_protobuf_build_attr_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get('input_item_accumulator_info_ptr'))
    ret.add(self._config.get('input_item_score_info_ptr'))
    ret.add(self._config.get('input_item_key_timestamp_ptr'))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get('output_item_accumulator_info_pb'))
    ret.add(self._config.get('output_item_score_info_pb'))
    return set()

class AdCalibrateTableFetchFromCacheEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_calibrate_table_fetch_from_cache_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get('input_key'))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get('output_attr'))
    ret.add(self._config.get('output_item_status'))
    return set()

class AdCalibrateCommonInfoCheckerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_calibrate_common_info_checker_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_ptr"))
    ret.add(self._config.get("input_number_trigger_switch"))
    ret.add(self._config.get("input_max_impression_num"))
    ret.add(self._config.get("input_max_conv_num"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_item_update_switch"))
    return ret

class AdCalibrateDelayTrainingDataAccumulatorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_calibrate_delay_training_data_accumulator_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_ptr"))
    ret.add(self._config.get("start_event_ts"))
    ret.add(self._config.get("event_server_ts"))
    ret.add(self._config.get("bucket_index_list"))
    ret.add(self._config.get("key"))
    ret.add(self._config.get("start_event_num_list"))
    ret.add(self._config.get("end_event_num_list"))
    ret.add(self._config.get("event_type"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class AdCalibrateSimpleTrainingDataAccumulatorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_calibrate_simple_training_label_calculate_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_ptr"))
    ret.add(self._config.get("start_event_ts"))
    ret.add(self._config.get("event_server_ts"))
    ret.add(self._config.get("bucket_index_list"))
    ret.add(self._config.get("key"))
    ret.add(self._config.get("start_event_num_list"))
    ret.add(self._config.get("end_event_num_list"))
    ret.add(self._config.get("event_type"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class AdCalibrateTableClearToLocalCacheEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_calibrate_table_clear_to_local_cache_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_ptr"))
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("enable_clear_period"))
    ret.add(self._config.get("start_time_sec"))
    ret.add(self._config.get("end_time_sec"))
    ret.add(self._config.get("now_time_sec"))
    return ret

class AdCalibrateDelayLabelCalculateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_calibrate_delay_label_calculate_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_ptr"))
    ret.add(self._config.get("output_item_recall_ratio"))
    ret.add(self._config.get("output_item_train_info"))
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("confidence_threshold"))
    ret.add(self._config.get("max_cxr_score_info_list_size"))
    return ret

class AdCalibrateSimpleLabelCalculateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_calibrate_simple_label_calculate_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_ptr"))
    ret.add(self._config.get("output_item_recall_ratio"))
    ret.add(self._config.get("output_item_train_info"))
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("confidence_threshold"))
    ret.add(self._config.get("max_cxr_score_info_list_size"))
    return ret

class ModelCalibrateResultEnrich(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_model_calibrate_score"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("recall_ratio_threshold_item_attr", ''))
    return ret
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("from_item_attr", ''))
    return ret
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_cxr_list_item_attr", ''))
    ret.add(self._config.get("output_pcxr_list_item_attr", ''))
    ret.add(self._config.get("output_flag_item_attr", '')) # 标识已经正常填充
    return ret
  # @property
  # @strict_types
  # def input_common_attrs(self) -> set:
  #   ret = set(["calibrate_key"])
  #   # for x in self._config.get("output_columns", []):
  #   #   ret.add(x)
  #   return ret
  # @property
  # @strict_types
  # def output_common_attrs(self) -> set:
  #   ret = set(['calibrate_score'])
  #   return ret
class FindLowerBoundItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_lower_bound_index"
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("check_item_attr", ''))
    ret.add(self._config.get("check_list_item_attr", ''))
    return ret
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_item_attr", ''))
    return ret
  @strict_types
  def _check_config(self) -> None:
    check_arg("check_item_attr" in self._config, "必须写入check_item_attr参数")
    check_arg("check_list_item_attr" in self._config, "必须写入check_list_item_attr参数")
class CalibrateRatioEnrich(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calculate_calibrate_ratio"
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("bucket_index_item_attr", ''))
    ret.add(self._config.get("cxr_item_attr", ''))
    ret.add(self._config.get("input_cxr_list_item_attr", ''))
    ret.add(self._config.get("input_pcxr_list_item_attr", ''))
    return ret
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_item_attr", ''))
    return ret
  @strict_types
  def _check_config(self) -> None:
    check_arg("bucket_index_item_attr" in self._config, "必须写入 bucket_index_item_attr 参数")
    check_arg("cxr_item_attr" in self._config, "必须写入 cxr_item_attr 参数")
    check_arg("input_cxr_list_item_attr" in self._config, "必须写入 input_cxr_list_item_attr 参数")
    check_arg("input_pcxr_list_item_attr" in self._config, "必须写入 input_pcxr_list_item_attr 参数")

class CityHashNewEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "city_hash_enricher"
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for i in self._config.get("input_item_attrs"):
      ret.add(i)
    # ret.add(self._config.get("input_item_attrs", ''))
    return ret
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for i in self._config.get("output_item_attrs"):
      ret.add(i)
    # ret.add(self._config.get("output_item_attrs", ''))
    return ret
  @strict_types
  def _check_config(self) -> None:
    check_arg("input_item_attrs" in self._config, "必须写入 input_item_attrs 参数")
    check_arg("output_item_attrs" in self._config, "必须写入 output_item_attrs 参数")
