#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from .ad_pid_service_enricher import *
from .ad_pid_service_retriever import *
class AdPidServiceApiMixin(CommonLeafBaseMixin):
  def common_pid_strategy_enricher(self, **kwargs):
    """
    """
    self._add_processor(CommonPidStrategyEnricher(kwargs))
    return self

  def cross_data_proc_enricher(self, **kwargs):
    """
    """
    self._add_processor(CrossDataProcEnricher(kwargs))
    return self

  def hc_pre_handler_enricher(self, **kwargs):
    """
    HcPreHandlerEnricher
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .hc_pre_handler_enricher(
    )
    ```
    """
    self._add_processor(HcPreHandlerEnricher(kwargs))
    return self
  
  def dnchc_pre_handler_enricher(self, **kwargs):
    """
    DnchcPreHandlerEnricher
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .dnchc_pre_handler_enricher(
    )
    ```
    """
    self._add_processor(DnchcPreHandlerEnricher(kwargs))
    return self

  def gimbal_pre_handler_enricher(self, **kwargs):
    """
    GimbalPreHandlerEnricher
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .hc_pre_handler_enricher(
    )
    ```
    """
    self._add_processor(GimbalPreHandlerEnricher(kwargs))
    return self

  def esp_author_fans_pre_handler_enricher(self, **kwargs):
    """
    EspAuthorFansPreHandlerEnricher
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .esp_author_fans_pre_handler_enricher(
    )
    ```
    """
    self._add_processor(EspAuthorFansPreHandlerEnricher(kwargs))
    return self


  def outerloop_ee_budget_pre_handler_enricher(self, **kwargs):
    """
    OuterloopEeBudgetPreHandlerEnricher
    ------
    参数配置
    ------
    调用示例
    ------
    ``` python
    .outerloop_ee_budget_pre_handler_enricher(
    )
    ```
    """
    self._add_processor(OuterloopEeBudgetPreHandlerEnricher(kwargs))
    return self

  def smb_unit_pre_handler_enricher(self, **kwargs):
    """
    HcPreHandlerEnricher
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .smb_unit_pre_handler_enricher(
    )
    ```
    """
    self._add_processor(SmbUnitPreHandlerEnricher(kwargs))
    return self

  def smb_account_pre_handler_enricher(self, **kwargs):
    self._add_processor(SmbAccountPreHandlerEnricher(kwargs))
    return self

  def inspire_photo_roi_pre_handler_enricher(self, **kwargs):
    """
    InspirePhotoRoiPreHandlerEnricher
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .inspire_photo_roi_pre_handler_enricher(
    )
    ```
    """
    self._add_processor(InspirePhotoRoiPreHandlerEnricher(kwargs))
    return self

  def cross_pre_handler_enricher(self, **kwargs):
    """
    CrossPreHandlerEnricher
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .cross_pre_handler_enricher(
    )
    ```
    """
    self._add_processor(CrossPreHandlerEnricher(kwargs))
    return self

  def cross_pre_degradate_handler_enricher(self, **kwargs):
    """
    CrossPreDegradateHandlerEnricher
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .cross_pre_degradate_handler_enricher(
    )
    ```
    """
    self._add_processor(CrossPreDegradateHandlerEnricher(kwargs))
    return self

  def short_play_prepare(self, **kwargs):
    """
    """
    self._add_processor(ShortPlayPrepareEnricher(kwargs))
    return self

  def short_play_strategy(self, **kwargs):
    """
    """
    self._add_processor(ShortPlayStrategyEnricher(kwargs))
    return self

  def short_play_fill_response_strategy(self, **kwargs):
    """
    """
    self._add_processor(ShortPlayFillResponseEnricher(kwargs))
    return self

  def universe_bonus_control_enricher(self, **kwargs):
    """
    """
    self._add_processor(UniverseBonusControlEnricher(kwargs))
    return self

  def universe_billing_separate_enricher(self, **kwargs):
    """
    UniverseBillingSeparateEnricher
    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .universe_billing_separate_enricher(
    )
    ```
    """
    self._add_processor(UniverseBillingSeparateEnricher(kwargs))
    return self
  def outer_bonus_strategy_enricher(self, **kwargs):
    """
    """
    self._add_processor(OuterBonusStrategyEnricher(kwargs))
    return self

  def inner_bonus_info_enricher(self, **kwargs):
    """
    """
    self._add_processor(InnerBonusInfoEnricher(kwargs))
    return self

  def bonus_common_info_retriever(self, **kwargs):
    """
    """
    self._add_processor(BonusCommonInfoRetriever(kwargs))
    return self

  def pid_strategy_table_retriever(self, **kwargs):
    """
    """
    self._add_processor(PidStrategyTableRetriever(kwargs))
    return self
