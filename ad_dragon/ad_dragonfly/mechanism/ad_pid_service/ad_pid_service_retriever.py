#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import LeafRetriever
from dragonfly.common_leaf_util import check_arg, strict_types

class BonusCommonInfoRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bonus_common_info_retriever"

  @strict_types
  def is_async(self) -> bool:
    return False

class PidStrategyTableRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_strategy_table_retriever"

  @strict_types
  def is_async(self) -> bool:
    return False
