#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import <PERSON><PERSON>nricher
from dragonfly.common_leaf_util import check_arg, strict_types

class CommonPidStrategyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_pid_strategy_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class CrossDataProcEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "cross_data_proc_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class InnerBonusInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_bonus_info_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False
class HcPreHandlerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hc_pre_handler_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class DnchcPreHandlerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dnchc_pre_handler_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class GimbalPreHandlerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gimbal_pre_handler_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class SmbUnitPreHandlerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "smb_unit_pre_handler_enricher"

class EspAuthorFansPreHandlerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "esp_author_fans_pre_handler_enricher"

class OuterloopEeBudgetPreHandlerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "outerloop_ee_budget_pre_handler_enricher"

class SmbAccountPreHandlerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "smb_account_pre_handler_enricher"

class InspirePhotoRoiPreHandlerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inspire_photo_roi_pre_handler_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class CrossPreDegradateHandlerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "cross_pre_degradate_handler_enricher"

class CrossPreHandlerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "cross_pre_handler_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class ShortPlayStrategyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "short_play_strategy"

  @strict_types
  def is_async(self) -> bool:
    return False

class ShortPlayFillResponseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "short_play_fill_response_strategy"

  @strict_types
  def is_async(self) -> bool:
    return False

class ShortPlayPrepareEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "short_play_strategy"

  @strict_types
  def is_async(self) -> bool:
    return False

class UniverseBillingSeparateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_billing_separate_enricher"

class UniverseBonusControlEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_bonus_control_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False
class OuterBonusStrategyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "outer_bonus_strategy_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False