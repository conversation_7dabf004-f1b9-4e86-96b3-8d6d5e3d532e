#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import LeafObserver
from dragonfly.common_leaf_util import check_arg, strict_types

class AdDemoObserver(LeafObserver): 
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "ad_demo_observer"

    @property 
    @strict_types
    def input_common_attrs(self) -> set:
        ret = set([self._config["input_attr"]]) 
        return ret

class AdTimerObserver(LeafObserver): 
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "ad_timer_observer"

    @property 
    @strict_types
    def input_common_attrs(self) -> set:
        ret = set([self._config["record_ts_column"]]) 
        return ret

class AdDataFrameWriteToRedisObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_data_frame_write_to_redis_observer"
  # TODO: implement real functional

class PidDataSaveObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pid_data_save_observer"

class AdDataFrameWriteToKafkaObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_data_frame_write_to_kafka_observer"
  # TODO: implement real functional

class AdWriteColumnToRedisObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_write_column_to_redis_observer"
  # TODO: implement real functional

class AdWriteColumnToKafkaObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_write_column_to_kafka_observer"
  # TODO: implement real functional

class AdDumpDataFrameToFileObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dump_data_frame_to_file"

class AdStrategyDataSaveObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_strategy_data_save_observer"

class AdStrategyAboDataSaveObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_strategy_abo_data_save_observer"

class AsyncPipelineObserver(LeafObserver):
  @strict_types
  def __init__(self, config: dict):
    check_arg("sub_flow" in config, "缺少 sub_flow 配置")
    self.__sub_flow = config.pop("sub_flow")
    super().__init__(config)
    self._auto_detect_pass_common_attrs = "pass_common_attrs" not in self._config
    self._auto_detect_pass_item_attrs = "pass_item_attrs" not in self._config
    self._auto_detect_merge_common_attrs = False
    self._auto_detect_merge_item_attrs = False

    #if not self._auto_detect_pass_common_attrs:
    #  self.__sub_flow._import_common_attrs = set(self._config.get("pass_common_columns", []))

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "async_observe_by_subflow"

  @property
  @strict_types
  def input_common_attrs(self):
    ret = set()
    for attr in self._config.get("pass_common_attrs", []):
      ret.add(attr)
    return ret

  @property
  @strict_types
  def output_common_attrs(self):
    return set()
  
  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    for attr in self._config.get("pass_item_attrs", []):
      ret.add(attr)
    return ret

  @strict_types
  def get_sub_flow(self):
    return self.__sub_flow


class AdPipelineObserver(LeafObserver):
  @strict_types
  def __init__(self, config: dict):
    check_arg("sub_flow" in config, "缺少 sub_flow 配置")
    self.__sub_flow = config.pop("sub_flow")
    super().__init__(config)
    self._auto_detect_pass_common_attrs = "pass_common_attrs" not in self._config
    self._auto_detect_pass_item_attrs = "pass_item_attrs" not in self._config
    self._auto_detect_merge_common_attrs = False
    self._auto_detect_merge_item_attrs = False

    #if not self._auto_detect_pass_common_attrs:
    #  self.__sub_flow._import_common_attrs = set(self._config.get("pass_common_columns", []))

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_observe_by_subflow"

  @property
  @strict_types
  def input_common_attrs(self):
    ret = set()
    for attr in self._config.get("pass_common_attrs", []):
      ret.add(attr)
    return ret

  @property
  @strict_types
  def output_common_attrs(self):
    return set()
  
  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    for attr in self._config.get("pass_item_attrs", []):
      ret.add(attr)
    return ret

  @strict_types
  def get_sub_flow(self):
    return self.__sub_flow

class AdLogBidDataWriteToRedisObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_log_bid_data_write_to_redis_observer"

class AdBidDataWriteToRedisObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bid_data_write_to_redis_observer"

class AdGlobalDataWriteToRedisObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "global_data_write_to_redis_observer"

class AdBidResultStoreObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bid_result_store_observer"

class AdAutoBidStoreObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_auto_bid_send"

class BidPerformanceResultStoreObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bid_performance_result_store_observer"

class OcpmAccountDiffObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_account_diff_observer"

  @property
  @strict_types
  def input_item_attrs(self):
    ret = set()
    context_input_table_config = self._config.get("context_table_input", {})
    context_input_table = context_input_table_config.get("table_name")
    #context_input_column = context_input_table_config.get("")
    tag_table = self._config.get("tag_table_name")

    #tag_input_table_config = self._config.get("tag_table_input", {})
    tag_input_table_config = self._config.get("window_meta_columns", {})
    ret.add(tag_table + "::" + tag_input_table_config["predict_conversion_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["predict_conversion_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["predict_conversion_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["predict_deep_conversion_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["predict_deep_conversion_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["predict_deep_conversion_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["cpa_bid_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["cpa_bid_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["cpa_bid_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["deep_cpa_bid_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["deep_cpa_bid_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["deep_cpa_bid_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["expect_target_cost_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["expect_target_cost_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["expect_target_cost_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["deep_expect_target_cost_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["deep_expect_target_cost_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["deep_expect_target_cost_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["target_cost_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["target_cost_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["target_cost_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["deep_target_cost_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["deep_target_cost_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["deep_target_cost_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["cost_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["cost_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["cost_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["second_cost_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["second_cost_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["second_cost_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["origin_cost_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["origin_cost_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["origin_cost_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["price_after_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["price_after_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["price_after_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["cpa_coef_cost_sum_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["cpa_coef_cost_sum_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["cpa_coef_cost_sum_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["auto_cpa_bid_cost_sum_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["auto_cpa_bid_cost_sum_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["auto_cpa_bid_cost_sum_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["auto_roas_cost_sum_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["auto_roas_cost_sum_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["auto_roas_cost_sum_info"] + ".time")
    ret.add(tag_table + "::" + tag_input_table_config["target_cpa_sum_info"] + ".value")
    ret.add(tag_table + "::" + tag_input_table_config["target_cpa_sum_info"] + ".cnt")
    ret.add(tag_table + "::" + tag_input_table_config["target_cpa_sum_info"] + ".time")

    tag_input_table_config = self._config.get("global_columns", {})
    ret.add(tag_table + "::" + tag_input_table_config["account_id"])
    ret.add(tag_table + "::" + tag_input_table_config["retention_count"])
    ret.add(tag_table + "::" + tag_input_table_config["predict_conversion"])
    ret.add(tag_table + "::" + tag_input_table_config["predict_deep_conversion"])
    ret.add(tag_table + "::" + tag_input_table_config["rta_cost"])
    ret.add(tag_table + "::" + tag_input_table_config["expect_target_cost"])
    ret.add(tag_table + "::" + tag_input_table_config["deep_expect_target_cost"])
    ret.add(tag_table + "::" + tag_input_table_config["cost"])
    ret.add(tag_table + "::" + tag_input_table_config["origin_cost"])
    ret.add(tag_table + "::" + tag_input_table_config["price_after_billing_separate"])
    ret.add(tag_table + "::" + tag_input_table_config["target_cost"])
    ret.add(tag_table + "::" + tag_input_table_config["deep_target_cost"])
    ret.add(tag_table + "::" + tag_input_table_config["roas_24h_all_target_cost"])
    ret.add(tag_table + "::" + tag_input_table_config["roas_24h_all_target_cost_attr"])
    ret.add(tag_table + "::" + tag_input_table_config["target_cpa_sum"])
    ret.add(tag_table + "::" + tag_input_table_config["cv"])

    if "unit_id_list_column" in tag_input_table_config:
      ret.add(tag_table + "::" + tag_input_table_config["unit_id_list_column"])

    session_input_table_config = self._config.get("session_table_input", {})
    session_table = session_input_table_config.get("table_name")
    if "unit_id" in session_input_table_config:
      ret.add(session_table + "::" + session_input_table_config["unit_id"])
    if "campaign_id" in session_input_table_config:
      ret.add(session_table + "::" + session_input_table_config["campaign_id"])
    if "account_id" in session_input_table_config:
      ret.add(session_table + "::" + session_input_table_config["account_id"])
    if "charge_tag" in session_input_table_config:
      ret.add(session_table + "::" + session_input_table_config["charge_tag"])
    if "campaign_type" in session_input_table_config:
      ret.add(session_table + "::" + session_input_table_config["campaign_type"])
    if "group_tag" in session_input_table_config:
      ret.add(session_table + "::" + session_input_table_config["group_tag"])
    if "campaign_charge_mode" in session_input_table_config:
      ret.add(session_table + "::" + session_input_table_config["campaign_charge_mode"])
    if "campaign_time_schedule" in session_input_table_config:
      ret.add(session_table + "::" + session_input_table_config["campaign_time_schedule"])
    if "create_source_type" in session_input_table_config:
      ret.add(session_table + "::" + session_input_table_config["create_source_type"])
    if "project_id" in session_input_table_config:
      ret.add(session_table + "::" + session_input_table_config["project_id"])
    return ret

class OcpmAccountStepTwoDiffObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ocpm_account_step_two_diff_observer"

class TableColumnDiffCompareObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "table_column_diff_compare_observer"

class BidServerPerfUtilObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bid_server_perf_util_observer"
