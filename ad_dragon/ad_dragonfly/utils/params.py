from typing import Dict, Any, Optional

def is_param_dynamic(config: Dict[str, Any], key: str) -> bool:
    if key not in config:
        return False
    param = config[key]
    if not isinstance(param, str):
        return False
    return param.startswith("{{") and param.endswith("}}")

def get_dynamic_param(config: Dict[str, Any], key: str) -> Optional[str]:
    if not is_param_dynamic(config, key):
        return None
    return config[key][2:-2]

if __name__ == "__main__":
    config = {
        "foo": "{{foo}}",
        "bar": "bar",
        "baz": [1, 2, 3]
    }
    assert(is_param_dynamic(config, "foo"))
    assert(not is_param_dynamic(config, "bar"))
    assert(not is_param_dynamic(config, "baz"))
    assert(not is_param_dynamic(config, "none"))
    assert(get_dynamic_param(config, "foo") == "foo")
    assert(get_dynamic_param(config, "bar") is None)
    assert(get_dynamic_param(config, "baz") is None)
    assert(get_dynamic_param(config, "none") is None)
