#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from .novel_mixer import *

class NovelApiMixin(CommonLeafBaseMixin):
  def novel_parse_request_mixer(self, **kwargs):
    '''
    NovelParseRequestMixer
    ----------------
    解析小说请求

    调用示例
    ----------------
    ``` python
    .novel_parse_request_mixer()
    '''
    self._add_processor(NovelParseRequestMixer(kwargs))
    return self

  def novel_flow_admiter(self, **kwargs):
    '''
    NovelFlowAdmiter
    ----------------
    小说推荐流量准入

    调用示例
    ----------------
    ``` python
    .novel_flow_admiter()
    '''
    self._add_processor(NovelFlowAdmiter(kwargs))
    return self

  def novel_rpc_response_packer(self, **kwargs):
    '''
    NovelRpcResponsePacker
    ----------------
    小说推荐rpc返回构造

    调用示例
    ----------------
    ``` python
    .novel_rpc_response_packer()
    '''
    self._add_processor(NovelRpcResponsePacker(kwargs))
    return self
