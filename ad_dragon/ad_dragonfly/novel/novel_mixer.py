#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import LeafMixer
from dragonfly.common_leaf_util import strict_types

class NovelParseRequestMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "novel_parse_request_mixer"

class NovelFlowAdmiter(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "novel_flow_admiter"

class NovelRpcResponsePacker(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "novel_rpc_response_packer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'novel_router_response'
    }

