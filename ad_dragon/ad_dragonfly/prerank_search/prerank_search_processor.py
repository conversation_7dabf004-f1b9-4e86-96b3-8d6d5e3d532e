from dragonfly.common_leaf_processor import <PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, LeafObserver
from dragonfly.common_leaf_util import check_arg, extract_common_attrs, strict_types

class PrerankSearchInitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "prerank_search_init"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {}
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {""}


class MergeAsyncEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merge_async"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    tmp = set({'bid_info'})
    return tmp
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {""}
  
class PrerankSearchCmdManager(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "prerank_search_cmd_manager"

class PrerankSearchUnifyRvalueEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "prerank_search_unify_rvalue"

class PrintTableByNameObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "print_table_by_name"
  
class CreateLogicTableByNameMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "create_logic_table_by_name"

class SelectAdByQuotaEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "select_ad_by_quota"
  
class CalRoiBidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "cal_roi_bid"

class AntouIndustryCaliBidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "antou_industry_cali_bid"
  
class CalEcpmEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "cal_ecpm"