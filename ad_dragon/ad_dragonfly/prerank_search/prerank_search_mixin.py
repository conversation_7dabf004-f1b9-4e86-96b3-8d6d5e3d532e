from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from ad_dragonfly.prerank_search.prerank_search_processor import *

class PrerankSearchMixin(CommonLeafBaseMixin):
  def prerank_search_init(self, **kwargs):
    self._add_processor(PrerankSearchInitEnricher(kwargs))
    return self
  def prerank_search_cmd_manager(self, **kwargs):
    self._add_processor(PrerankSearchCmdManager(kwargs))
    return self
  def prerank_search_unify_rvalue(self, **kwargs):
    self._add_processor(PrerankSearchUnifyRvalueEnricher(kwargs))
    return self
  def print_table_by_name(self, **kwargs):
    self._add_processor(PrintTableByNameObserver(kwargs))
    return self

  def create_logic_table_by_name(self, **kwargs):
    self._add_processor(CreateLogicTableByNameMixer(kwargs))
    return self
  def select_ad_by_quota(self, **kwargs):
    self._add_processor(SelectAdByQuotaEnricher(kwargs))
    return self
  
  def cal_roi_bid(self, **kwargs):
    self._add_processor(CalRoiBidEnricher(kwargs))
    return self

  def antou_industry_cali_bid(self, **kwargs):
    self._add_processor(AntouIndustryCaliBidEnricher(kwargs))
    return self
  
  def cal_ecpm(self, **kwargs):
    self._add_processor(CalEcpmEnricher(kwargs))
    return self
  