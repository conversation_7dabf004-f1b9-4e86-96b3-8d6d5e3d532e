#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from .ad_arranger import *
from .ad_enricher import *
from .ad_observer import *
from .ad_retriever import *
from .ad_mixer import *

class MerchantPackApiMixin(CommonLeafBaseMixin):
  def ad_charge_log_observer(self, **kwargs):
    '''
    AdChargeLogObserver
    -------
    构建 & 发送计费日志

    调用示例
    ----
    ``` python
    .ad_charge_log_observer()
    '''
    self._add_processor(AdChargeLogObserver(kwargs))
    return self

  def outer_native_data_prepare_mixer(self, **kwargs):
    '''
    OuterNativeDataPrepareMixer
    -------
    外循环全域推广数据准备

    调用示例
    ----
    ``` python
    .outer_native_data_prepare_mixer()
    '''
    self._add_processor(OuterNativeDataPrepareMixer(kwargs))
    return self

  def outer_native_get_ads_mixer(self, **kwargs):
    '''
    OuterNativeGetAdsMixer
    -------
    外循环全域推广广告获取

    调用示例
    ----
    ``` python
    .outer_native_get_ads_mixer()
    '''
    self._add_processor(OuterNativeGetAdsMixer(kwargs))
    return self

  def outer_union_ios_get_ads_mixer(self, **kwargs):
    '''
    OuterUnionIosGetAdsMixer
    -------
    外循环全域推广联运ios广告获取

    调用示例
    ----
    ``` python
    .outer_union_ios_get_ads_mixer()
    '''
    self._add_processor(OuterUnionIosGetAdsMixer(kwargs))
    return self

  def outer_native_send_log_mixer(self, **kwargs):
    '''
    OuterNativeSendLogMixer
    -------
    外循环全域推广发送日志

    调用示例
    ----
    ``` python
    .outer_native_send_log_mixer()
    '''
    self._add_processor(OuterNativeSendLogMixer(kwargs))
    return self

  def outer_native_fill_response_mixer(self, **kwargs):
    '''
    OuterNativeFillResponseMixer
    -------
    外循环全域推广结果填充

    调用示例
    ----
    ``` python
    .outer_native_fill_response_mixer()
    '''
    self._add_processor(OuterNativeFillResponseMixer(kwargs))
    return self
