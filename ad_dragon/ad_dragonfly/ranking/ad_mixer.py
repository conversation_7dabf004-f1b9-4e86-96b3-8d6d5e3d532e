#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import LeafMixer
from dragonfly.common_leaf_util import check_arg, strict_types

class OuterNativeDataPrepareMixer(LeafMixer): 
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "outer_native_data_prepare_mixer"

class OuterNativeGetAdsMixer(LeafMixer): 
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "outer_native_get_ads_mixer"

class OuterUnionIosGetAdsMixer(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "outer_union_ios_get_ads_mixer"

class OuterNativeSendLogMixer(LeafMixer): 
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "outer_native_send_log_mixer"

class OuterNativeFillResponseMixer(LeafMixer): 
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "outer_native_fill_response_mixer"
