#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>riever, <PERSON><PERSON><PERSON><PERSON>, LeafObserver
from dragonfly.common_leaf_util import check_arg, strict_types

class RunCmdFactor(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "run_cmd_factor"

class PredictMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "predict_mixer"

class RpcAckLogger(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rpc_ack_logger"

class AdPackCaller(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_pack_caller"

class AdFetcher(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_fetcher"
  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "athena_ad_response_ptr"
    }

class TubeFetcher(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tube_fetcher"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "tube_response_ptr"
    }

class HomeMixRanker(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "home_mix_ranker"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      "tube_response_ptr",
      "athena_ad_response_ptr"
    }

class DrawMixRanker(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "draw_mix_ranker"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      "tube_response_ptr",
      "athena_ad_response_ptr"
    }

class RankPredict(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "rank_predict"

class AckLogger(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "ack_logger"

class CalcOldUserAllItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_olduser_all_item_enricher"

class CalcNewUserAllItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_newuser_all_item_enricher"

class CalcOldUserForceItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_olduser_force_item_enricher"

class CalcOldUserTagItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_olduser_tag_item_enricher"

class CalcOldUserSelectedItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_olduser_selected_item_enricher"

class CalcOldUserRandomItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_olduser_random_item_enricher"

class CalcNewUserForceItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_newuser_force_item_enricher"

class CalcNewUserTagItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_newuser_tag_item_enricher"

class CalcNewUserSelectedItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_newuser_selected_item_enricher"

class CalcNewUserRandomItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_newuser_random_item_enricher"

class CalcForceItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_force_item_enricher"

class CalcTagItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_tag_item_enricher"

class CalcSelectedItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_selected_item_enricher"

class CalcTubeEnsembleScore(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_tube_ensemble_score"

class TubeEnsembleSorter(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "tube_ensemble_sorter"

class TubeStrategyDiversity(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "tube_strategy_diversity"

class TubeStrategyClass(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "tube_strategy_class"

class CalcRandomItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_random_item_enricher"

class CalcAllItemEnricher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_all_item_enricher"

class CalcNewuserEnsembleScore(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_newuser_ensemble_score"

class CalcOlduserEnsembleScore(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_olduser_ensemble_score"

class CalcRecoCloseUserEnsembleScore(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_reco_close_user_ensemble_score"

class UserProfileFetcher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "user_profile_fetcher"

class RecoUserProfileFetcher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "reco_user_profile_fetcher"

class StrategyFilter(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "strategy_filter"

class StrategyDispatcher(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "strategy_dispatcher"

class ServerShowLogger(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "server_show_logger"

class RpcServerShowLogger(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "rpc_server_show_logger"

class FlowAdmiter(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "flow_admiter"

class DataPacker(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "data_packer"

class RpcDataPacker(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "rpc_data_packer"

class NewUserStrategyDiversity(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "newuser_strategy_diversity"

class OldUserStrategyDiversity(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "olduser_strategy_diversity"

class RecoCloseUserStrategyDiversity(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "reco_close_user_strategy_diversity"

class NewUserEnsembleSorter(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "newuser_ensemble_sorter"

class OldUserEnsembleSorter(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "olduser_ensemble_sorter"

class RecoCloseUserEnsembleSorter(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "reco_close_user_ensemble_sorter"

class ParseRequestMixer(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "parse_request_mixer"

class IndexInitEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "index_init_enricher"

class PathInitEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "path_init_enricher"

class ModelRetriever(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "model_retriever"

    @strict_types
    def is_async(self) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        return {
            "model_recall_search_num"
        }

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        return {
            "model_retrieve_flag"
        }

class GuaranteeRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "guarantee_retriever"

class TopRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "top_retriever"

class TagRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "tag_retriever"

class RandomRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "random_retriever"

class MultiRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "multi_retriever"

class SelectedTubeRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "selected_tube_retriever"

class HotListRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "hot_list_retrieve"

class TubeRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "tube_retrieve"
class UserTagRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "user_tag_retriever"

class ColdStartRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "cold_start_retriever"

class SessionFreqFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "session_freq_filter_arranger"

class PlayletBrowsetFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "playlet_browset_filter_arranger"

class ExpBatchFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "exp_batch_filter_arranger"

class KconfBlacklistFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "kconf_blacklist_filter_arranger"

class MultiPathMergeArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "multi_path_merge_arranger"

class AttrPaddingEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "attr_padding_enricher"

class DiversityFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "diversity_filter_arranger"


class TubeTraceEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "trace_enricher"

class AuditBlacklistFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "audit_blacklist_filter_arranger"

class BizTypeFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "biz_type_filter_arranger"

class NegativeFeedbackArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "negative_feedback_arranger"

class AdcodeEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "adcode_enricher"

class RepurchaseRanker(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "repurchase_rank_arranger"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        return {"tube_response_ptr"}

class XtabRanker(LeafMixer):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "xtab_rank_arranger"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        return {"tube_response_ptr"}

class PurchasedTagsRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "purchased_tags_retriever"

class InteractiveTagsRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "interactive_tags_retriever"

class AdvertisedRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "advertised_retriever"

class CalcRepurchaseFlowEnsembleScore(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_repurchase_flow_ensemble_score"

class RepurchaseFlowEnsembleSorter(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "repurchase_flow_ensemble_sorter"

class AdTableByCommonAttrRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "ad_table_by_common_attr_retriever"

class PerflogReasonsObserver(LeafObserver):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "perflog_reasons_observer"