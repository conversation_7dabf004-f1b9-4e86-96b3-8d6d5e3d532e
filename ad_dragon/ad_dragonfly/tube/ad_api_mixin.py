#!/usr/bin/env python3
# coding=utf-8

import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from .ad_mixer import *
os.environ["CHECK_TALBE_DEPENDENCY"] = "true"

class PlayletServerApiMixin(CommonLeafBaseMixin):
  def run_cmd_factor(self, **kwargs):
    '''
    RunCmdFactor
    ________________
    执行模型适配函数

    调用示例
    ——————————————
    ```python
    .run_cmd_factor(
      cmd_key_name = "test_cmd_key",
      admit_func_name = "DefaultCmdAdmitor"
      predict_value_column = ["photo_ctr"],
      cmd_table_name = cmd_table_name,
      item_table_name = item_table
    )
    '''
    self._add_processor(RunCmdFactor(kwargs))
    return self

  def predict_mixer(self, **kwargs):
    '''
    PredictMixer
    ________________
    执行模型预估

    调用示例
    ——————————————
    ```python
    .predict_mixer(
      cmd_table_name = cmd_table_name,
      item_table_name = item_table,
      time_out = 100,
      predict_server_name = "predict_server_name"
    )
    '''
    self._add_processor(PredictMixer(kwargs))
    return self


  def rpc_ack_logger(self, **kwargs):
    '''
    RpcAckLogger
    ________________
    输出 ack 日志

    调用示例
    ——————————————
    ```python
    .rpc_ack_logger(
      timeout = 10
    )
    '''
    self._add_processor(RpcAckLogger(kwargs))
    return self

  def ad_pack_caller(self, **kwargs):
    '''
    AdPackCaller
    ---------------
    请求 AdPack服务，执行 计费、servershow 等下发

    调用示例
    ----------------
    ```python
    .ad_pack_caller()
    '''
    self._add_processor(AdPackCaller(kwargs))
    return self

  def ad_fetcher(self, **kwargs):
    '''
    AdFetcher
    ---------------
    请求广告引擎，获取广告推荐结果

    调用示例
    ----------------
    ```python
    .ad_fetcher()
    '''
    self._add_processor(AdFetcher(kwargs))
    return self

  def tube_fetcher(self, **kwargs):
    '''
    TubeFetcher
    ---------------
    请求短剧推荐引擎，获取短剧推荐结果

    调用示例
    ----------------
    ```python
    .tube_fetcher()
    '''
    self._add_processor(TubeFetcher(kwargs))
    return self

  def home_mix_ranker(self, **kwargs):
    '''
    HomeMixRanker
    ---------------
    推荐页混排策略模块

    调用示例
    ----------------
    ```python
    .home_mix_ranker()
    '''
    self._add_processor(HomeMixRanker(kwargs))
    return self

  def draw_mix_ranker(self, **kwargs):
    '''
    DrawMixRanker
    ---------------
    推荐页混排策略模块

    调用示例
    ----------------
    ```python
    .draw_mix_ranker()
    '''
    self._add_processor(DrawMixRanker(kwargs))
    return self

  def reco_user_profile_fetcher(self, **kwargs):
    '''
    RecoUserProfileFetcher
    ---------------
    获取主站用户画像数据，目前只请求 ad-user-profile 获取快手 uid

    调用示例
    ----------------
    ```python
    .reco_user_profile_fetcher()
    '''
    self._add_processor(RecoUserProfileFetcher(kwargs))
    return self

  def parse_request_mixer(self, **kwargs):
    '''
    ParseRequestMixer
    ----------------
    解析短剧请求

    调用示例
    ----------------
    ``` python
    .parse_request_mixer()
    '''
    self._add_processor(ParseRequestMixer(kwargs))
    return self

  def calc_selected_item_enricher(self, **kwargs):
    '''
    CalcSelectedItemEnricher
    ----------------
    精选策略公用算子

    调用示例
    ----------------
    ``` python
    .calc_selected_item_enricher()
    '''
    self._add_processor(CalcSelectedItemEnricher(kwargs))
    return self

  def calc_force_item_enricher(self, **kwargs):
    '''
    CalcTagItemEnricher
    ----------------
    Force 策略公用算子

    调用示例
    ----------------
    ``` python
    .calc_force_item_enricher()
    '''
    self._add_processor(CalcForceItemEnricher(kwargs))
    return self

  def calc_random_item_enricher(self, **kwargs):
    '''
    CalcRandomItemEnricher
    ----------------
    Random 策略公用算子

    调用示例
    ----------------
    ``` python
    .calc_random_item_enricher()
    '''
    self._add_processor(CalcRandomItemEnricher(kwargs))
    return self

  def calc_all_item_enricher(self, **kwargs):
    '''
    CalcAllItemEnricher
    ----------------
    All 策略公用算子

    调用示例
    ----------------
    ``` python
    .calc_all_item_enricher()
    '''
    self._add_processor(CalcAllItemEnricher(kwargs))
    return self


  def calc_tag_item_enricher(self, **kwargs):
    '''
    CalcTagItemEnricher
    ----------------
    Tag 策略公用算子

    调用示例
    ----------------
    ``` python
    .calc_tag_item_enricher()
    '''
    self._add_processor(CalcTagItemEnricher(kwargs))
    return self

  def calc_tube_ensemble_score(self, **kwargs):
    '''
    CalcTubeEnsembleScore
    ----------------
    计算 ensemble_score 策略算子

    调用示例
    ----------------
    ``` python
    .calc_tube_ensemble_score()
    '''
    self._add_processor(CalcTubeEnsembleScore(kwargs))
    return self

  def ack_logger(self, **kwargs):
    '''
    AckLogger
    ----------------
    Ack 数据策略执行算子，强插各种策略先放到这里

    调用示例
    ----------------
    ``` python
    .ack_logger()
    '''
    self._add_processor(AckLogger(kwargs))
    return self

  def rank_predict(self, **kwargs):
    '''
    RankPredict
    ----------------
    短剧预估策略执行算子，强插各种策略先放到这里

    调用示例
    ----------------
    ``` python
    .rank_predict()
    '''
    self._add_processor(RankPredict(kwargs))
    return self


  def calc_force_item_score(self, **kwargs):
    '''
    CalcSelectedItemScore
    ----------------
    强插-短剧策略执行算子，强插各种策略先放到这里

    调用示例
    ----------------
    ``` python
    .calc_force_item_score()
    '''
    self._add_processor(CalcForceItemScore(kwargs))
    return self

  def calc_selected_item_score(self, **kwargs):
    '''
    CalcSelectedItemScore
    ----------------
    精选-短剧策略执行算子，精选各种策略先放到这里

    调用示例
    ----------------
    ``` python
    .calc_selected_item_score()
    '''
    self._add_processor(CalcSelectedItemScore(kwargs))
    return self

  def calc_normal_item_score(self, **kwargs):
    '''
    CalcNormalItemScore
    ----------------
    标准-短剧策略执行算子，标准各种策略先放到这里

    调用示例
    ----------------
    ``` python
    .calc_normal_item_score()
    '''
    self._add_processor(CalcNormalItemScore(kwargs))
    return self

  def user_profile_fetcher(self, **kwargs):
    '''
    UserProfileFetcher
    ----------------
    用户画像数据获取算子

    调用示例
    ----------------
    ``` python
    .user_profile_fetcher()
    '''
    self._add_processor(UserProfileFetcher(kwargs))
    return self

  def strategy_filter(self, **kwargs):
    '''
    StrategyFilter
    ----------------
    排序后的策略过滤算子

    调用示例
    ----------------
    ``` python
    .strategy_filter()
    '''
    self._add_processor(StrategyFilter(kwargs))
    return self

  def strategy_dispatcher(self, **kwargs):
    '''
    StrategyDispatcher
    ----------------
    排序策略分流算子，如新用户策略流、老用户策略流等

    调用示例
    ----------------
    ``` python
    .strategy_dispatcher()
    '''
    self._add_processor(StrategyDispatcher(kwargs))
    return self

  def rpc_server_show_logger(self, **kwargs):
    '''
    RpcServerShowLogger
    ----------------
    打包 & 发送服务端曝光日志

    调用示例
    ----------------
    ``` python
    .rpc_server_show_logger()
    '''
    self._add_processor(RpcServerShowLogger(kwargs))
    return self


  def server_show_logger(self, **kwargs):
    '''
    ServerShowLogger
    ----------------
    打包 & 发送服务端曝光日志

    调用示例
    ----------------
    ``` python
    .server_show_logger()
    '''
    self._add_processor(ServerShowLogger(kwargs))
    return self

  def flow_admiter(self, **kwargs):
    '''
    FlowAdmiter
    ----------------
    短剧推荐服务流量准入策略算子

    调用示例
    ----------------
    ``` python
    .flow_admiter()
    '''
    self._add_processor(FlowAdmiter(kwargs))
    return self

  def rpc_data_packer(self, **kwargs):
    '''
    RpcDataPacker
    ----------------
    短剧推荐服务结果打包算子

    调用示例
    ----------------
    ``` python
    .rpc_data_packer()
    '''
    self._add_processor(RpcDataPacker(kwargs))
    return self


  def data_packer(self, **kwargs):
    '''
    DataPacker
    ----------------
    短剧推荐服务结果打包算子

    调用示例
    ----------------
    ``` python
    .data_packer()
    '''
    self._add_processor(DataPacker(kwargs))
    return self

  def reco_close_user_strategy_diversity(self, **kwargs):
    '''
    RecoCloseUserStrategyDiversity
    ----------------
    关闭个性化用户短剧推荐策略多样性算子

    调用示例
    ----------------
    ``` python
    .reco_close_user_strategy_diversity()
    '''
    self._add_processor(RecoCloseUserStrategyDiversity(kwargs))
    return self

  def tube_strategy_diversity(self, **kwargs):
    '''
    TubeStrategyDiversity
    ----------------
    老用户短剧推荐策略多样性算子

    调用示例
    ----------------
    ``` python
    .tube_strategy_diversity()
    '''
    self._add_processor(TubeStrategyDiversity(kwargs))
    return self

  def tube_strategy_class(self,**kwargs):
    '''
    TubeStrategyClass
    ----------------
    推荐策略不同类别短剧插入比列

    调用示例
    ----------------
    ``` python
    .tube_strategy_diversity()
    '''
    self._add_processor(TubeStrategyClass(kwargs))
    return self

  def newuser_strategy_diversity(self, **kwargs):
    '''
    NewUserStrategyDiversity
    ----------------
    新用户短剧推荐策略多样性算子

    调用示例
    ----------------
    ``` python
    .newuser_strategy_diversity()
    '''
    self._add_processor(NewUserStrategyDiversity(kwargs))
    return self

  def reco_close_user_ensemble_sorter(self, **kwargs):
    '''
    RecoCloseUserEnsembleSorter
    ----------------
    关闭个性化用户短剧推荐策略融合排序策略算子

    调用示例
    ----------------
    ``` python
    .reco_close_user_ensemble_sorter()
    '''
    self._add_processor(RecoCloseUserEnsembleSorter(kwargs))
    return self

  def tube_ensemble_sorter(self, **kwargs):
    '''
    TubeEnsembleSorter
    ----------------
    新用户短剧推荐策略融合排序策略算子

    调用示例
    ----------------
    ``` python
    .tube_ensemble_sorter()
    '''
    self._add_processor(TubeEnsembleSorter(kwargs))
    return self

  def newuser_ensemble_sorter(self, **kwargs):
    '''
    NewUserEnsembleSorter
    ----------------
    新用户短剧推荐策略融合排序策略算子

    调用示例
    ----------------
    ``` python
    .newuser_ensemble_sorter()
    '''
    self._add_processor(NewUserEnsembleSorter(kwargs))
    return self

  def calc_repurchase_flow_ensemble_score(self, **kwargs):
    '''
    CalcRepurchaseFlowEnsembleScore
    ----------------
    复访复购页面计算 ensemble_score

    调用示例
    ----------------
    ``` python
    .calc_repurchase_flow_ensemble_score()
    '''
    self._add_processor(CalcRepurchaseFlowEnsembleScore(kwargs))
    return self

  def repurchase_flow_ensemble_sorter(self, **kwargs):
    '''
    RepurchaseFlowEnsembleSorter
    ----------------
    复访复购页面 sort

    调用示例
    ----------------
    ``` python
    .repurchase_flow_ensemble_sorter()
    '''
    self._add_processor(RepurchaseFlowEnsembleSorter(kwargs))
    return self

  def index_init(self, **kwargs):
    self._add_processor(IndexInitEnricher(kwargs))
    return self

  def path_init(self, **kwargs):
    self._add_processor(PathInitEnricher(kwargs))
    return self

  def retrieve_model(self, **kwargs):
    self._add_processor(ModelRetriever(kwargs))
    return self

  def retrieve_guarantee(self, **kwargs):
    self._add_processor(GuaranteeRetriever(kwargs))
    return self

  def retrieve_top(self, **kwargs):
    self._add_processor(TopRetriever(kwargs))
    return self

  def retrieve_hot_list(self, **kwargs):
    self._add_processor(HotListRetriever(kwargs))
    return self
  
  def retrieve_tube(self, **kwargs):
    self._add_processor(TubeRetriever(kwargs))
    return self
  
  def retrieve_tag(self, **kwargs):
    self._add_processor(TagRetriever(kwargs))
    return self

  def retrieve_random(self, **kwargs):
    self._add_processor(RandomRetriever(kwargs))
    return self

  def retrieve_multi(self, **kwargs):
    self._add_processor(MultiRetriever(kwargs))
    return self

  def retrieve_selected_tube(self, **kwargs):
    self._add_processor(SelectedTubeRetriever(kwargs))
    return self

  def retrieve_user_tag(self, **kwargs):
    self._add_processor(UserTagRetriever(kwargs))
    return self

  def retrieve_cold_start(self, **kwargs):
    self._add_processor(ColdStartRetriever(kwargs))
    return self

  def freq_filter(self, **kwargs):
    self._add_processor(SessionFreqFilterArranger(kwargs))
    return self

  def playlet_browset_filter(self, **kwargs):
    self._add_processor(PlayletBrowsetFilterArranger(kwargs))
    return self

  def exp_batch_filter(self, **kwargs):
    self._add_processor(ExpBatchFilterArranger(kwargs))
    return self

  def blacklist_filter(self, **kwargs):
    self._add_processor(KconfBlacklistFilterArranger(kwargs))
    return self

  def multi_path_merge(self, **kwargs):
    self._add_processor(MultiPathMergeArranger(kwargs))
    return self

  def padding_attr(self, **kwargs):
    self._add_processor(AttrPaddingEnricher(kwargs))
    return self

  def diversity_filter(self, **kwargs):
    self._add_processor(DiversityFilterArranger(kwargs))
    return self

  def trace(self, **kwargs):
    self._add_processor(TubeTraceEnricher(kwargs))
    return self

  def audit_blacklist_filter(self, **kwargs):
    self._add_processor(AuditBlacklistFilterArranger(kwargs))
    return self

  def biz_type_filter(self, **kwargs):
    self._add_processor(BizTypeFilterArranger(kwargs))
    return self

  def negative_feedback_filter(self, **kwargs):
    self._add_processor(NegativeFeedbackArranger(kwargs))
    return self

  def enrich_adcode(self, **kwargs):
    self._add_processor(AdcodeEnricher(kwargs))
    return self

  def repurchase_rank(self, **kwargs):
    self._add_processor(RepurchaseRanker(kwargs))
    return self

  def xtab_rank(self, **kwargs):
    self._add_processor(XtabRanker(kwargs))
    return self

  def retrieve_by_purchased_tags(self, **kwargs):
    self._add_processor(PurchasedTagsRetriever(kwargs))
    return self

  def retrieve_by_interactive_tags(self, **kwargs):
    self._add_processor(InteractiveTagsRetriever(kwargs))
    return self

  def retrieve_by_advertised_tube_ids(self, **kwargs):
    self._add_processor(AdvertisedRetriever(kwargs))
    return self

  def retrieve_from_ad_table_by_common_attr(self, **kwargs):
    '''
    AdTableByCommonAttrRetriever
    ----------------
    根据 common attr 从 ad_table 召回

    调用示例
    ----------------
    ``` python
    .retrieve_from_ad_table_by_common_attr(
      table_name = "ad_kstube_tube_pool",
      common_attr = "tube_request_define_client_city_hash64",
      ad_table_column = "define_client_city_hash64",
      op = "eq",
      reason = 11,
      quota = 5000
    )
    '''
    self._add_processor(AdTableByCommonAttrRetriever(kwargs))
    return self

  def perflog_reasons(self, **kwargs):
    '''
    PerflogReasonsObserver
    ----------------
    非去重召回 reasons 打点

    调用示例
    ----------------
    ``` python
    .perflog_reasons()
    '''
    self._add_processor(PerflogReasonsObserver(kwargs))
    return self