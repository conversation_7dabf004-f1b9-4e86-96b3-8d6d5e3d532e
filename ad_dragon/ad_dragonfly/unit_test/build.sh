CURRENT_DIR=$(pwd)
BASE_DIR=$(dirname "$0")
cd $BASE_DIR
pwd

if [ $1 == "first" ]
then
  wget http://download.corp.kuaishou.com/reco/mkl_lib.tar.gz -O mkl_lib.tar.gz
  wget http://download.corp.kuaishou.com/reco/mkl_2019.3.199.tgz -O mkl_2019.3.199.tgz
  tar zxf mkl_lib.tar.gz
  tar zxf mkl_2019.3.199.tgz
fi

export KWS_SERVICE_QUARANTINE=bTAGING
export KWS_SERVICE_1PAZ=HB1AZ1
export KWS_SERVICE_AZ=ZW
export KWS_SERVICE_REGION=HB1
export KWS_SERVICE_DC=XM
export ENABLE_AD_DRAGON_PYTHON_WRAPPER=true
export LD_PRELOAD=../../../../../build_tools/gcc-10.3.0/lib64/libstdc++.so.6
export LD_LIBRARY_PATH=./mkl_lib/
export PYTHONPATH=../../../../../dragon/:${PYTHONPATH}
if [ $1 == "test1" ]; then
  python3 test1.py
elif [ $1 == "test2" ]; then
  python3 test2.py
elif [ $1 == "test3" ]; then
  python3 test3.py
elif [ $1 == "test4" ]; then
  python3 test4.py
elif [ $1 == "test5" ]; then
  python3 test5.py
elif [ $1 == "all" ]; then
  python3 test1.py
  python3 test2.py
  python3 test3.py
  python3 test4.py
else
  python3 test1.py    # default
fi
cd $CURRENT_DIR
