#!/usr/bin/env python3
# coding=utf-8

import os
import sys
dragon_path=os.path.abspath('../../../../../dragon/')
ad_dragon_path=os.path.abspath('../../')
sys.path.append(dragon_path)
sys.path.append(ad_dragon_path)

import unittest
import math
import random
import json
import time
import shutil
from dragonfly.common_leaf_dsl import LeafFlow, OfflineRunner, LeafService, IndexSource
from ad_dragonfly.ad_api_mixin import AdApiMixin
from dragonfly.common_reco_pipeline_executor_pywrap import CommonRecoLeafPipelineExecutor, set_service_name, ContainerSet
from pathlib import Path
from datetime import datetime, timedelta
from ad_dragonfly.unit_test.mcb_bid_update_test import McbBudgetBidUpdateFlowTest, McbBidDataUpdateMockTable

class TestFlowFunc_BidUpdateHistory1(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()                                                                                                               
  
  def test_bid_data_update(self):
    ad_flow = McbBudgetBidUpdateFlowTest(name="test_bid_data_update") \
              .bid_data_update_flow()

    last_hour_date_time = datetime.now() - timedelta(hours = 2)
    index_dt = last_hour_date_time.strftime('%Y-%m-%d_%H00')
    
    index_path = "../data_push/ad_collective_index_for_bid/"
    Path(index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(index_path + "version", 'w') as f:
        f.write(index_dt)


    p2p_index_path = "../data_push/main_cost_second_bucket_to_bid_server/"
    Path(p2p_index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(p2p_index_path + "version", 'w') as f:
        f.write(index_dt)
    with open(p2p_index_path + index_dt + "/part-1", 'w') as f:
      for num in range(0, 86401):
        f.write("20221115\t"+ str(num) + "\t" + str(1-0.0357565) + "\n")


    ad_leaf = self.__init_service(ad_flow)
    mock_table = McbBidDataUpdateMockTable()
    mock_table.mock_update_history_data1(ad_leaf)
    ad_leaf.run("test_bid_data_update")

    for item in ad_leaf.get_items("McbBidDataWindowTable"):
      window_sz = len(item["history.ts"])
      self.assertEqual(window_sz, 7)
      self.assertEqual(len(item['history.today_charge']), window_sz)
      self.assertEqual(len(item['history.remain_flow_ratio']), window_sz)
      self.assertEqual(len(item['history.remain_budget_ratio']), window_sz)
      self.assertEqual(len(item['history.budget_coef']), window_sz)
      self.assertEqual(len(item['history.bid_coef']), window_sz)
      self.assertEqual(len(item['history.cem_bid_coef']), window_sz)

      self.assertEqual(item["history.ts"][window_sz - 1], 1669647912)
      self.assertEqual(item["history.today_charge"][window_sz - 1], 3450)
      self.assertEqual(item["history.remain_flow_ratio"][window_sz - 1], 0.0357565098)
      self.assertEqual(item["history.remain_budget_ratio"][window_sz - 1], 0.310000151)
      self.assertEqual(item["history.budget_coef"][window_sz - 1], 2.22742462)
      self.assertEqual(item["history.bid_coef"][window_sz - 1], 0.448948979)
      self.assertEqual(item["history.cem_bid_coef"][window_sz - 1], 0.448948979)

      break

class TestFlowFunc_BidUpdateHistory2(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()                                                                                                               
  
  def test_bid_data_update(self):
    ad_flow = McbBudgetBidUpdateFlowTest(name="test_bid_data_update") \
              .bid_data_update_flow()

    last_hour_date_time = datetime.now() - timedelta(hours = 2)
    index_dt = last_hour_date_time.strftime('%Y-%m-%d_%H00')
    
    index_path = "../data_push/ad_collective_index_for_bid/"
    Path(index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(index_path + "version", 'w') as f:
        f.write(index_dt)


    p2p_index_path = "../data_push/main_cost_second_bucket_to_bid_server/"
    Path(p2p_index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(p2p_index_path + "version", 'w') as f:
        f.write(index_dt)
    with open(p2p_index_path + index_dt + "/part-1", 'w') as f:
      for num in range(0, 86401):
        f.write("20221115\t"+ str(num) + "\t" + str(1-0.796862) + "\n")


    perform_control_index_path = "../data_push/mcb_perform_control/"
    Path(perform_control_index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(perform_control_index_path + "version", 'w') as f:
        f.write(index_dt)
    with open(perform_control_index_path + index_dt + "/part-1", 'w') as f:
      for num in range(0, 86401):
        f.write("1222\tAD_ITEM_CLICK\t" + str(num) + "\t-1" + "\n")
    

    ad_leaf = self.__init_service(ad_flow)
    mock_table = McbBidDataUpdateMockTable()
    mock_table.mock_update_history_data2(ad_leaf)
    ad_leaf.run("test_bid_data_update")

    for item in ad_leaf.get_items("McbBidDataWindowTable"):
      window_sz = len(item["history.ts"])
      self.assertEqual(window_sz, 7)
      self.assertEqual(len(item['history.today_charge']), window_sz)
      self.assertEqual(len(item['history.remain_flow_ratio']), window_sz)
      self.assertEqual(len(item['history.remain_budget_ratio']), window_sz)
      self.assertEqual(len(item['history.budget_coef']), window_sz)
      self.assertEqual(len(item['history.bid_coef']), window_sz)
      self.assertEqual(len(item['history.cem_bid_coef']), window_sz)

      self.assertEqual(item["history.ts"][window_sz - 1], 1669684930)
      self.assertEqual(item["history.today_charge"][window_sz - 1], 164453)
      self.assertEqual(item["history.remain_flow_ratio"][window_sz - 1], 0.937602222)
      self.assertEqual(item["history.remain_budget_ratio"][window_sz - 1], 0.93803221)
      self.assertEqual(item["history.budget_coef"][window_sz - 1], 0.7369)
      self.assertEqual(item["history.bid_coef"][window_sz - 1], 1.35704)
      self.assertEqual(item["history.cem_bid_coef"][window_sz - 1], 1.35704)

      break
    

if __name__ == '__main__':
  suite = unittest.TestSuite()
  #suite.addTests(unittest.TestLoader().loadTestsFromName('test5.TestFlowFunc_BidUpdateHistory1'))
  suite.addTests(unittest.TestLoader().loadTestsFromName('test5.TestFlowFunc_BidUpdateHistory2'))
  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)

  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)         
