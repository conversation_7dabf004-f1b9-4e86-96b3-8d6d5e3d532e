#!/usr/bin/env python3
# coding=utf-8

import os
import sys
dragon_path=os.path.abspath('../../../../../dragon/')
ad_dragon_path=os.path.abspath('../../')
sys.path.append(dragon_path)
sys.path.append(ad_dragon_path)

import unittest
import math
import random
import json
import time
from dragonfly.common_leaf_dsl import LeafFlow, OfflineRunner, LeafService, IndexSource
from ad_dragonfly.mechanism.ad_api_mixin import AdApiMixin
from dragonfly.common_reco_pipeline_executor_pywrap import CommonRecoLeafPipelineExecutor, set_service_name, ContainerSet
from pathlib import Path
from datetime import datetime, timedelta

class McbBudgetTriggerMockTable(object):
  def __init__(self, i):
    self.unit_id = 123 + i
    self.campaign_id = ********** + i
    self.account_id = ********
    self.group_tag = 75
    self.combine_key = 8766544 + i * 10

  def get_combine_key(self):
    return self.combine_key

  def mock_ad_budet_table(self, item):
    item['start_of_day_ts'] = ****************
    item['combine_key'] = self.combine_key
    item['campaign_id'] = self.campaign_id
    item['campaign_charge'] = 393774
    item['campaign_budget'] = 966000
    item['campaign_left_budget'] = 572226
    item['locked_campaign_left_budget'] = 572226
    item['budget_status'] = 0
    item['locked_campaign_queue_index'] = 1
    item['account_id'] = self.account_id
    item['account_balance'] = ********
    item['account_charge'] = 766005
    item['account_budget'] = 2000000
    item['account_left_budget'] = 1233995
    item['group_tag'] = self.group_tag
    item['event_server_timestamp'] = 0
    item['tag_charge_info_size'] = 0
    item['tag_charge_info.charge_tag'] = 0
    item['tag_charge_info.account_charge'] = 766005
    item['tag_charge_info.account_budget'] = 2000000
    item['tag_charge_info.account_left_budget'] = 1233995
    item['tag_charge_info.campaign_charge'] = 393774
    item['tag_charge_info.campaign_budget'] = 966000
    item['tag_charge_info.campaign_left_budget'] = 572226
    item['tag_charge_info.locked_campaign_left_budget'] = 572226
    item['account_delivery_type'] = 1
    item['campaign_bidtype'] = 1
    item['increment_charge'] = 986

  def mock_ad_base_table(self, item):
    item['account_id'] = self.account_id
    item['campaign_id'] = self.campaign_id
    item['mcb_level'] = 2
    item['campaign_time_schedule'] = [1] * 24
    item['ocpc_action_type'] = 190
    item['create_source_type'] = 1
    item['product_name'] = "product_name_11111"
    item['first_industry_id'] = 1021
    item['second_industry_id'] = 2012
    item['industry_name'] = "industry_name_222"
    item['project_id'] = 4738282
    item['campaign_type'] = 2
    #item['hosting_scene'] = 0
    #item['hosting_cpa_bid'] = 0 

  def mock_ad_common_table(self, item):
    item['account_id'] = self.account_id
    item['campaign_id'] = self.campaign_id
    #item['state_info.is_adv'] = 
    #item['state_info.last_is_adv'] = 
    #item['state_info.last_change_ts'] = 

  def mock_global_data_table(self, item):
    item['account_id'] = self.account_id
    item['campaign_id'] = self.campaign_id
    item['start_of_day_ts'] = ****************
    item['all_budget'] = 1100000
    item['left_budget'] = 541898
    item['locked_left_budget'] = 542312
    item['today_charge'] = 558102
    item['current_ts'] = **********
    item['last_trigger_charge'] = 558102
    item['last_trigger_ts'] = int(time.time() * 1e6)
    #item['ocpx_action_type'] = 190

  def mock_bid_data_table(self, item):
    item['account_id'] = self.account_id
    item['campaign_id'] = self.campaign_id
    item['is_universe_opt'] = 0
    item['remain_flow_ratio_global'] = 0.483233
    item['last_remain_flow_ratio_global'] = 0.483642
    item['last_budget'] = 7328000
    item['today_charge'] = 3672526
    item['last_today_charge'] = 716662
    item['state_info.is_adv'] = 1
    item['all_budget'] = 7328000
    item['ocpx_action_type'] = 190
    item['budget_info.locked_all_budget'] = 7328000
    item['budget_info.last_locked_all_budget'] = 7328000
    item['last_change_budget_ts'] = **********
    item['master_host'] = "ad-rs6-bjzey255.idczw.hb1.kwaidc.com"
    item['is_hosting'] = 0
    item['event_server_timestamp'] = ****************

  def mock_mcb_config_table(self, item):
    item['trigger_yuan'] = 10.0
    item['trigger_second'] = 15.0
    item['trigger_flow'] = 0.002
    item['block_second'] = 15.0
    item['trigger_by_flow'] = 0.0

  def case_prepre(self, ad_leaf, n):
    global_data = ad_leaf.get_items("McbGlobalDataTable")[n]
    bid_data = ad_leaf.get_items("McbBidDataTable")[n]
    config_data = ad_leaf.get_items("McbPiddHyperParamterTable")[n]
    if n == 0:      # 不触发调价
      pass
    elif n == 1:    # increment_flow，触发调价
      config_data['trigger_by_flow'] = 0.1
      global_data['increment_charge'] = int(config_data['trigger_yuan'] * 1000 + 1)
      global_data['last_trigger_ts'] -= int((config_data['block_second'] + 10) * 1e6)
    elif n == 2:    # increment_flow，触发调价
      config_data['trigger_by_flow'] = 0.1
      bid_data['last_remain_flow_ratio_global'] = float(config_data['trigger_flow'] + 1.01)
      global_data['last_trigger_ts'] -= int((config_data['block_second'] + 10) * 1e6)
    elif n == 3:    # increment_charge，触发调价
      max_sec = max(config_data['block_second'], config_data['trigger_second'])
      global_data['last_trigger_ts'] -= int((max_sec + 10) * 1e6)

class McbBudgetTriggerFlowTest(LeafFlow, AdApiMixin):
  def mcb_budget_trigger_flow(self):
    self.ad_data_frame_debug_log(
        item_table = "AdBudgetTable",
        msg = "mcb_budget_trigger_enricher_before_AdBudgetTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .ad_data_frame_debug_log(
        item_table = "McbBaseInfoTable",
        msg = "mcb_budget_trigger_enricher_before_McbBaseInfoTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .ad_data_frame_debug_log(
        item_table = "McbGlobalDataTable",
        msg = "mcb_budget_trigger_enricher_before_McbGlobalDataTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .ad_data_frame_debug_log(
        item_table = "McbBidDataTable",
        msg = "mcb_budget_trigger_enricher_before_McbBidDataTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .ad_data_frame_debug_log(
        item_table = "McbAdCommonTable",
        msg = "mcb_budget_trigger_enricher_before_McbAdCommonTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .ad_data_frame_debug_log(
        item_table = "McbPiddHyperParamterTable",
        msg = "mcb_budget_trigger_enricher_before_McbPiddHyperParamterTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .set_attr_value(
        no_overwrite=True,
        common_attrs=[{
          "name": "mcb_stragety_version",
          "type": "string",
          "value": "mcb_budget:5"
        }]
      ) \
      .mcb_budget_trigger_enricher(
        item_table = "AdBudgetTable",
        is_camapign_msg_type = True,
        input_column = "mcb_stragety_version",
        output_column = "enable_mcb_one_time_trigger",
        # AdBudgetTable
        combine_key = {"item_table" : "AdBudgetTable", "column" : "combine_key"},
        account_id = {"item_table" : "AdBudgetTable", "column" : "account_id"},
        campaign_id = {"item_table" : "AdBudgetTable", "column" : "campaign_id"},
        charge_tag = {"item_table" : "AdBudgetTable", "column" : "tag_charge_info.charge_tag"},
        group_tag = {"item_table" : "AdBudgetTable", "column" : "group_tag"},
        start_of_day_ts = {"item_table" : "AdBudgetTable", "column" : "start_of_day_ts"},
        campaign_all_budget = {"item_table" : "AdBudgetTable", "column" : "campaign_budget"},
        event_server_timestamp = {"item_table" : "AdLogTable", "column" : "event_server_timestamp"},
        campaign_budget = {"item_table" : "AdBudgetTable", "column" : "tag_charge_info.campaign_budget"},
        increment_charge = {"item_table" : "AdBudgetTable", "column" : "increment_charge"},
        campaign_left_budget = {"item_table" : "AdBudgetTable", "column" : "tag_charge_info.campaign_left_budget"},
        locked_campaign_left_budget = {"item_table" : "AdBudgetTable", "column" : "tag_charge_info.locked_campaign_left_budget"},
        campaign_charge = {"item_table" : "AdBudgetTable", "column" : "tag_charge_info.campaign_charge"},
        locked_campaign_queue_index = {"item_table" : "AdBudgetTable", "column" : "locked_campaign_queue_index"},
        
        # McbBaseInfoTable
        mcb_level = {"item_table" : "McbBaseInfoTable", "column" : "mcb_level"},
        ocpx_action_type = {"item_table" : "McbBaseInfoTable", "column" : "ocpc_action_type"},
        create_source_type = {"item_table" : "McbBaseInfoTable", "column" : "create_source_type"},
        campaign_time_schedule = {"item_table" : "McbBaseInfoTable", "column" : "campaign_time_schedule"},

        # AdCommonTable
        common_state_info_is_adv = {"item_table" : "McbAdCommonTable", "column" : "state_info.is_adv"},
        common_state_info_last_is_adv = {"item_table" : "McbAdCommonTable", "column" : "state_info.last_is_adv"},
        common_state_info_last_change_ts = {"item_table" : "McbAdCommonTable", "column" : "state_info.last_change_ts"},

        # McbGlobalDataTable
        global_account_id = {"item_table" : "McbGlobalDataTable", "column" : "account_id"},
        global_campaign_id = {"item_table" : "McbGlobalDataTable", "column" : "campaign_id"},
        global_start_of_day_ts = {"item_table" : "McbGlobalDataTable", "column" : "start_of_day_ts"},
        global_all_budget = {"item_table" : "McbGlobalDataTable", "column" : "all_budget"},
        global_increment_charge = {"item_table" : "McbGlobalDataTable", "column" : "increment_charge"},
        global_left_budget = {"item_table" : "McbGlobalDataTable", "column" : "left_budget"},
        global_locked_left_budget = {"item_table" : "McbGlobalDataTable", "column" : "locked_left_budget"},
        global_today_charge = {"item_table" : "McbGlobalDataTable", "column" : "today_charge"},
        global_current_ts = {"item_table" : "McbGlobalDataTable", "column" : "current_ts"},
        global_last_trigger_charge = {"item_table" : "McbGlobalDataTable", "column" : "last_trigger_charge"},
        global_last_trigger_ts = {"item_table" : "McbGlobalDataTable", "column" : "last_trigger_ts"},
        global_ocpx_action_type = {"item_table" : "McbGlobalDataTable", "column" : "ocpx_action_type"},
        
        # McbBidDataTable
        step2_is_universe_opt = {"item_table" : "McbBidDataTable", "column" : "is_universe_opt"},
        step2_remain_flow_ratio_global = {"item_table" : "McbBidDataTable", "column" : "remain_flow_ratio_global"},
        step2_last_remain_flow_ratio_global = {"item_table" : "McbBidDataTable", "column" : "last_remain_flow_ratio_global"},
        step2_last_budget = {"item_table" : "McbBidDataTable", "column" : "last_budget"},
        step2_today_charge = {"item_table" : "McbBidDataTable", "column" : "today_charge"},
        step2_last_today_charge = {"item_table" : "McbBidDataTable", "column" : "last_today_charge"},
        step2_state_info_is_adv = {"item_table" : "McbBidDataTable", "column" : "state_info.is_adv"},
        step2_all_budget = {"item_table" : "McbBidDataTable", "column" : "all_budget"},
        step2_ocpx_action_type = {"item_table" : "McbBidDataTable", "column" : "ocpx_action_type"},
        step2_locked_all_budget = {"item_table" : "McbBidDataTable", "column" : "budget_info.locked_all_budget"},
        step2_last_locked_all_budget = {"item_table" : "McbBidDataTable", "column" : "budget_info.last_locked_all_budget"},
        step2_last_change_budget_ts = {"item_table" : "McbBidDataTable", "column" : "last_change_budget_ts"},
        step2_master_host = {"item_table" : "McbBidDataTable", "column" : "master_host"},
        step2_is_hosting = {"item_table" : "McbBidDataTable", "column" : "is_hosting"},
        # 输出
        step2_event_server_timestamp = {"item_table" : "McbBidDataTable", "column" : "event_server_timestamp"},

        trigger_yuan = {"item_table" : "McbPiddHyperParamterTable", "column" : "trigger_yuan"},
        trigger_second = {"item_table" : "McbPiddHyperParamterTable", "column" : "trigger_second"},
        trigger_flow = {"item_table" : "McbPiddHyperParamterTable", "column" : "trigger_flow"},
        block_second = {"item_table" : "McbPiddHyperParamterTable", "column" : "block_second"},
        trigger_by_flow = {"item_table" : "McbPiddHyperParamterTable", "column" : "trigger_by_flow"},

        mcb_update = [
          {"column": "budget_opt_info.campaign_all_budget", "from_column" : "campaign_budget", "type": "int", "from_table": "AdBudgetTable", "use_from_key": True},
          {"column": "locked_campaign_queue_index", "type": "int", "from_table": "AdBudgetTable", "use_from_key": True},

          {"column": "left_budget", "type": "int", "from_table": "McbGlobalDataTable"},
          {"column": "locked_left_budget", "type": "int", "from_table": "McbGlobalDataTable"},
          {"column": "last_target_factor", "type": "int", "from_table": "McbGlobalDataTable", "from_column" : "target_factor"},

          {"column": "create_source_type", "type": "int", "from_table": "McbBaseInfoTable", "use_from_key": True},
          {"column": "mcb_level", "type": "int", "from_table": "McbBaseInfoTable", "use_from_key": True},
          {"column": "product_name", "type": "string", "from_table": "McbBaseInfoTable", "use_from_key": True},
          {"column": "first_industry_id", "type": "int", "from_table": "McbBaseInfoTable", "use_from_key": True},
          {"column": "second_industry_id", "type": "int", "from_table": "McbBaseInfoTable", "use_from_key": True},
          {"column": "industry_name", "type": "string", "from_table": "McbBaseInfoTable", "use_from_key": True},
          {"column": "project_id", "type": "int", "from_table": "McbBaseInfoTable", "use_from_key": True},
          {"column": "campaign_time_schedule", "type": "int_list", "from_table": "McbBaseInfoTable", "use_from_key": True},
          {"column": "campaign_type", "type": "int", "from_table": "McbBaseInfoTable", "use_from_key": True},
          {"column": "hosting_scene", "type": "int", "from_table": "McbBaseInfoTable", "use_from_key": True},
          {"column": "hosting_cpa_bid", "type": "int", "from_table": "McbBaseInfoTable", "use_from_key": True}
        ]
      ) \
      .ad_data_frame_debug_log(
        item_table = "McbGlobalDataTable",
        msg = "mcb_budget_trigger_enricher_after_McbGlobalDataTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .ad_data_frame_debug_log(
        item_table = "McbBidDataTable",
        msg = "mcb_budget_trigger_enricher_after_McbBidDataTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .ad_data_frame_debug_log(
        item_table = "McbAdCommonTable",
        msg = "mcb_budget_trigger_enricher_after_McbAdCommonTable",
        sample_rate = 1000000,
        type = 2
      )
    return self

class McbLogTriggerMockTable(object):
  def __init__(self, i):
    self.unit_id = 123 + i
    self.campaign_id = ********** + i
    self.account_id = ********
    self.group_tag = 75
    self.combine_key = 8766544 + i * 10

  def get_combine_key(self):
    return self.combine_key

  def mock_ad_log_table(self, item):
    item['account_id'] = self.account_id
    item['campaign_id'] = self.campaign_id
    item['inner_charge_tag'] = 0
    item['combine_key'] = self.combine_key
    item['event_server_timestamp'] = *************


  def mock_ad_base_table(self, item):
    item['account_id'] = self.account_id
    item['campaign_id'] = self.campaign_id
    item['mcb_level'] = 2
    item['campaign_time_schedule'] = [1] * 24
  
  def mock_global_data_table(self, item):
    item['account_id'] = self.account_id
    item['campaign_id'] = self.campaign_id
    item['start_of_day_ts'] = ****************
    item['all_budget'] = 3000000
    item['left_budget'] = 1582473
    item['locked_left_budget'] = 1582473
    item['today_charge'] = 1417527
    item['current_ts'] = **********
    item['last_trigger_charge'] = 1417527
    item['last_trigger_ts'] = ****************
    item['ocpx_action_type'] = 191
    item['is_universe_opt'] = 0
    item['cost'] = 20
    item['last_cpa_trigger_ts'] = int(time.time() * 1e6)
    item['retention_active'] = 0
    item['today_cost'] = 20.0
    item['today_target_cost'] = 0
    item['today_expect_cv'] = 0.********
    item['today_performance_target_cost'] = 0
    item['today_cv'] = 0
    item['predict_conversion'] = 0
    item['predict_deep_conversion'] = 0
 
  def mock_global_window_table(self, item):
    item['account_id'] = self.account_id
    item['campaign_id'] = self.campaign_id
    item['target_cost'] = 0
    item['performance_target_cost'] = 0
    item['cost'] = 0
    item['cpa_coef'] = 0

  def mock_bid_data_table(self, item):
    item['account_id'] = self.account_id
    item['campaign_id'] = self.campaign_id
    item['last_today_charge'] = 716662
    item['cost'] = 7
    item['avg_cpa_bid'] = 0
    item['product_cpa_bid'] = 1.09731e+06
    item['retention_pid.p'] = 0.0
    item['bid_info.log_auto_cpa_bid'] = 0.0
    item['bid_info.log_auto_roas'] = 0.0
    item['bid_info.auto_cpa_bid'] = 0.0
    item['bid_info.auto_roas'] = 0
    item['mcb_retention_target_final'] = 0
    item['mcb_retention_threshold'] = 0
    item['event_server_timestamp'] = 0

  def mock_bid_window_table(self, item):
    item['account_id'] = self.account_id
    item['campaign_id'] = self.campaign_id

  def mock_mcb_config_table(self, item):
    item['min_avg_cpa_bid'] = 50.0
    item['force_update_second'] = 30.0
    item['batch_cpa_ratio'] = 0.3
    item['cost_threshold'] = 20.0
    item['block_second'] = 30.0

  def case_prepre(self, ad_leaf, n):
    global_data = ad_leaf.get_items("McbGlobalDataTable")[n]
    bid_data = ad_leaf.get_items("McbBidDataTable")[n]
    config_data = ad_leaf.get_items("McbConfigTable")[n]
    if n == 0:      # 不触发调价
      pass
    elif n == 1:    # 两次调价的最小时间间隔
      global_data['last_cpa_trigger_ts'] -= int((config_data['block_second'] + 10) * 1e6)
    elif n == 2:    # cost 条件 block
      global_data['last_cpa_trigger_ts'] -= int((config_data['force_update_second'] + 10) * 1e6)
      global_data['cost'] = int(config_data['cost_threshold'] + bid_data['cost'] + 10)
    elif n == 3:    # 消耗达到 n * avg_cpa_bid 条件
      global_data['last_cpa_trigger_ts'] -= int((config_data['block_second'] + 10) * 1e6)
      bid_data['avg_cpa_bid'] = 10

class McbLogTriggerFlowTest(LeafFlow, AdApiMixin):
  def mcb_log_trigger_flow(self):
    self.ad_data_frame_debug_log(
        item_table = "McbGlobalDataTable",
        msg = "mcb_log_trigger_enricher_before_McbGlobalDataTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .ad_data_frame_debug_log(
        item_table = "McbGlobalDataWindowTable",
        msg = "mcb_log_trigger_enricher_before_McbGlobalDataWindowTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .ad_data_frame_debug_log(
        item_table = "McbBidDataTable",
        msg = "mcb_log_trigger_enricher_before_McbBidDataTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .ad_data_frame_debug_log(
        item_table = "McbBidDataWindowTable",
        msg = "mcb_log_trigger_enricher_before_McbBidDataWindowTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .ad_data_frame_debug_log(
        item_table = "AdLogTable",
        msg = "mcb_log_trigger_enricher_before_AdLogTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .ad_data_frame_debug_log(
        item_table = "McbBaseInfoTable",
        msg = "mcb_log_trigger_enricher_before_McbBaseInfoTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .ad_data_frame_debug_log(
        item_table = "McbConfigTable",
        msg = "mcb_log_trigger_enricher_before_McbConfigTable",
        sample_rate = 1000000,
        type = 2
      ) \
      .mcb_log_trigger_enricher(
        item_table = "AdLogTable",
        output_common_column = "enable_mcb_one_time_trigger",
        campaign_id = {"item_table" : "AdLogTable", "column" : "campaign_id"},
        charge_tag = {"item_table" : "AdLogTable", "column" : "inner_charge_tag"},
        item_key = {"item_table" : "AdLogTable", "column" : "campaign_id"},
        combine_key = {"item_table" : "AdLogTable", "column" : "combine_key"},
        event_server_timestamp = {"item_table" : "AdLogTable", "column" : "event_server_timestamp"},
        base_table_item_key = {"item_table" : "AdLogTable", "column" : "campaign_id"},

        account_id = {"item_table" : "McbBaseInfoTable", "column" : "account_id"},
        mcb_level = {"item_table" : "McbBaseInfoTable", "column" : "mcb_level"},
        campaign_time_schedule = {"item_table" : "McbBaseInfoTable", "column" : "campaign_time_schedule"},

        is_universe_opt = {"item_table" : "McbGlobalDataTable", "column" : "is_universe_opt"},
        global_cost = {"item_table" : "McbGlobalDataTable", "column" : "cost"},
        today_charge = {"item_table" : "McbGlobalDataTable", "column" : "today_charge"},
        last_cpa_trigger_ts = {"item_table" : "McbGlobalDataTable", "column" : "last_cpa_trigger_ts"},
        retention_active = {"item_table" : "McbGlobalDataTable", "column" : "retention_active"},
        today_cost = {"item_table" : "McbGlobalDataTable", "column" : "today_cost"},
        today_target_cost = {"item_table" : "McbGlobalDataTable", "column" : "today_target_cost"},
        today_expect_cv = {"item_table" : "McbGlobalDataTable", "column" : "today_expect_cv"},
        today_performance_target_cost = {"item_table" : "McbGlobalDataTable", "column" : "today_performance_target_cost"},
        today_cv = {"item_table" : "McbGlobalDataTable", "column" : "today_cv"},
        predict_conversion = {"item_table" : "McbGlobalDataTable", "column" : "predict_conversion"},
        predict_deep_conversion = {"item_table" : "McbGlobalDataTable", "column" : "predict_deep_conversion"},

        window_data_target_cost = {"item_table" : "McbGlobalDataWindowTable", "column" : "target_cost"},
        window_data_performance_target_cost = {"item_table" : "McbGlobalDataWindowTable", "column" : "performance_target_cost"},
        window_data_cost = {"item_table" : "McbGlobalDataWindowTable", "column" : "cost"},
        window_data_cpa_coef = {"item_table" : "McbGlobalDataWindowTable", "column" : "cpa_coef"},

        last_today_charge = {"item_table" : "McbBidDataTable", "column" : "last_today_charge"},
        bid_cost = {"item_table" : "McbBidDataTable", "column" : "cost"},
        avg_cpa_bid = {"item_table" : "McbBidDataTable", "column" : "avg_cpa_bid"},
        product_cpa_bid = {"item_table" : "McbBidDataTable", "column" : "product_cpa_bid"},
        retention_pid_p = {"item_table" : "McbBidDataTable", "column" : "retention_pid.p"},
        bid_info_log_auto_cpa_bid = {"item_table" : "McbBidDataTable", "column" : "bid_info.log_auto_cpa_bid"},
        bid_info_log_auto_roas = {"item_table" : "McbBidDataTable", "column" : "bid_info.log_auto_roas"},
        bid_info_auto_cpa_bid = {"item_table" : "McbBidDataTable", "column" : "bid_info.auto_cpa_bid"},
        bid_info_auto_roas = {"item_table" : "McbBidDataTable", "column" : "bid_info.auto_roas"},
        mcb_retention_target_final = {"item_table" : "McbBidDataTable", "column" : "mcb_retention_target_final"},
        mcb_retention_threshold = {"item_table" : "McbBidDataTable", "column" : "mcb_retention_threshold"},
        # 输出
        bid_event_server_timestamp = {"item_table" : "McbBidDataTable", "column" : "event_server_timestamp"},

        block_second = {"item_table" : "McbConfigTable", "column" : "block_second"},
        cost_threshold = {"item_table" : "McbConfigTable", "column" : "cost_threshold"},
        batch_cpa_ratio = {"item_table" : "McbConfigTable", "column" : "batch_cpa_ratio"},
        force_update_second = {"item_table" : "McbConfigTable", "column" : "force_update_second"},
        min_avg_cpa_bid = {"item_table" : "McbConfigTable", "column" : "min_avg_cpa_bid"},

        enable_log_trigger = {"item_table" : "McbAdCommonTable", "column" : "enable_log_trigger"},
      )
    return self
