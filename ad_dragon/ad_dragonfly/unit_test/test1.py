import unittest
import ad_api_test

if __name__ == '__main__':
  suite = unittest.TestSuite()
  suite.addTests(unittest.TestLoader().loadTestsFromName('ad_api_test.TestFlowFunc'))
  suite.addTests(unittest.TestLoader().loadTestsFromName('ad_api_test.TestFlowFunc1'))
  suite.addTests(unittest.TestLoader().loadTestsFromName('ad_api_test.TestFlowFunc2'))
  suite.addTests(unittest.TestLoader().loadTestsFromName('ad_api_test.TestFlowFunc_DataFetch'))
  suite.addTests(unittest.TestLoader().loadTestsFromName('ad_api_test.TestFlowFunc_BidFillBaseInfo'))
  suite.addTests(unittest.TestLoader().loadTestsFromName('ad_api_test.TestFlowFunc_McbConfigFetch'))
  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)

  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)         
