#!/usr/bin/env python3
# coding=utf-8

import os
import sys
dragon_path=os.path.abspath('../../../../../dragon/')
ad_dragon_path=os.path.abspath('../../')
sys.path.append(dragon_path)
sys.path.append(ad_dragon_path)

import unittest
import math
import random
import json
import time
import shutil
from dragonfly.common_leaf_dsl import LeafFlow, OfflineRunner, LeafService, IndexSource
from ad_dragonfly.mechanism.ad_api_mixin import AdApiMixin
from dragonfly.common_reco_pipeline_executor_pywrap import CommonRecoLeafPipelineExecutor, set_service_name, ContainerSet
from pathlib import Path
from datetime import datetime, timedelta
from ad_dragonfly.unit_test.mcb_bid_update_test import McbBudgetBidUpdateFlowTest
from ad_dragonfly.unit_test.mcb_trigger_test import McbLogTriggerFlowTest, McbLogTriggerMockTable, McbBudgetTriggerFlowTest, McbBudgetTriggerMockTable
class BidServerFlow(LeafFlow, AdApiMixin):
  pass

LeafService.ENABLE_ATTR_CHECK = False

class TestFlowFunc(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_runenv(self):
    ad_flow = BidServerFlow(name="test_runenv") \
      .ad_get_runtime_env(
        shard_id_out_column = "current_shard_id"
      )
    ad_leaf = self.__init_service(ad_flow)
    ad_leaf.run("test_runenv")
    self.assertEqual(ad_leaf['current_shard_id'], 0)

  # item_table = "AdLogTable",
  def test_subflow_shard(self):
    ad_flow = BidServerFlow(name="test_subflow_shard") \
              .ad_subflow_shard_id_enrich(
                mod_dividend_column = "account_id",
                mod_base = 64,
                output_column = "subflow_shard_id",
                no_check=True)

    ad_leaf = self.__init_service(ad_flow)

    item1 = ad_leaf.add_item(1)
    item1['account_id'] = 123
    ad_leaf.run("test_subflow_shard")
    for item in ad_leaf.get_items():
      if item.item_key == 1:
        self.assertEqual(item["account_id"], 123)

    self.assertEqual(ad_leaf['subflow_shard_id'], 59)

  def test_multi_dataframe(self):
    ad_flow = BidServerFlow(name="test_multi_dataframe") \
              .ad_subflow_shard_id_enrich(
                item_table='AdLogTable',
                mod_dividend_column = "account_id",
                mod_base = 64,
                output_column = "subflow_shard_id",
                no_check=True)

    ad_leaf = self.__init_service(ad_flow)

    ad_log_item1 = ad_leaf.add_item(1, table_name="AdLogTable")
    ad_budget_item1 = ad_leaf.add_item(1, table_name="AdBudgetTable")
    ad_log_item1['account_id'] = 123
    ad_budget_item1['campaign_id'] = 356
    ad_leaf.run("test_multi_dataframe")
    for item in ad_leaf.get_items("AdLogTable"):
      if item.item_key == 1:
        self.assertEqual(item["account_id"], 123)

    for item in ad_leaf.get_items("AdBudgetTable"):
      if item.item_key == 1:
        self.assertEqual(item["campaign_id"], 356)
        self.assertNotEqual(item["account_id"], 123)

    self.assertEqual(ad_leaf['subflow_shard_id'], 59)

  def test_kconf_enrich(self):
    ad_flow = BidServerFlow(name="test_kconf_enrich") \
              .ad_parse_proto_from_kconf(
                kconf_path="ad.bidServer.accountRollbackManagerConf",
                proto_msg="ks.bid_server_account.kconf.AccountRollbackManagerConfPB",
                output_attr="account_rollback_conf") \
              .enrich_with_protobuf(
                from_extra_var = "account_rollback_conf",
                attrs = [
                  dict(name="switch_version", path="switch_version"),
                  dict(name="account_id_list",
                        path="rollback_scope_or.account_id",
                        repeat_limit={"rollback_scope_or.account_id": 10}),
                ]
              )

    ad_leaf = self.__init_service(ad_flow)
    ad_leaf.run("test_kconf_enrich")

    for item in ad_leaf.get_items("AdLogTable"):
      if item.item_key == 1:
        self.assertEqual(item["account_id"], 123)

    self.assertEqual(ad_leaf['switch_version'], 11)
    self.assertEqual(ad_leaf['account_id_list'][0], ********)

class TestFlowFunc1(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_tablelite_enrich(self):
    ad_flow = BidServerFlow(name="test_tablelite_enrich") \
              .ad_table_lite_enrich(
                item_table = "AdLogTable",
                account_info = dict (
                  id_column = 'account_id',
                  column_config = [
                    dict(target="product_name", origin="product_name"),
                    dict(target="second_industry_id", origin="industry_id_v3"),
                    dict(target="account_delivery_type", origin="delivery_type")
                  ]
                ),
                campaign_info = dict (
                  id_column = 'campaign_id',
                  column_config = [
                    dict(target="campaign_type", origin="type"),
                    dict(target="campaign_bidtype", origin="bid_type"),
                    dict(target="project_id", origin="project_id"),
                    dict(target="create_source_type", origin="create_source_type"),
                    dict(target="campaign_time_schedule", origin="schedule_list")
                  ]
                ),
                industry_info = dict (
                  id_column = 'second_industry_id',   # 依赖 account 表先处理
                  column_config = [
                    dict(target="industry_name", origin="industry_name"),
                    dict(target="first_industry_id", origin="parent_id")
                  ]
                ),
                hosting_project_info = dict (
                  id_column = 'project_id',   # 依赖 campaign 表先处理
                  column_config = [
                    dict(target="hosting_scene", origin="hosting_scene"),
                    dict(target="hosting_cpa_bid", origin="cpa_bid")
                  ]
                )
              )

    last_hour_date_time = datetime.now() - timedelta(hours = 4)
    index_dt = last_hour_date_time.strftime('%Y-%m-%d_%H00')
    index_path = "../data_push/ad_collective_index_for_bid/"
    Path(index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(index_path + "version", 'w') as f:
        f.write(index_dt)

    container_set = ContainerSet(False);
    container_set.enable_table(1) #account
    container_set.enable_table(2) #campaign
    container_set.enable_table(3) # unit
    container_set.enable_table(31) # industry
    container_set.enable_table(71) # hosting
    container_set.init()
    container_set.add_account_record('''{
                          "id": "123",
                          "userId": "222",
                          "frozenStatus": "FROZEN_STATUS_CLOSED",
                          "putStatus": "PUT_STATUS_OPEN",
                          "reviewStatus": "REVIEW_THROUGH",
                          "deliveryType": "PRIORITY_EFFECT",
                          "industryIdV3": "123321"
                        }''')
    container_set.add_campaign_record('''{
                          "id": "456",
                          "projectId": "456654",
                          "putStatus": "PUT_STATUS_OPEN",
                          "bidType": "MAX_CONV_STRATEGY"
                        }''')
    container_set.add_unit_record('''{
                          "id": "789",
                          "idDeleted": "0",
                          "putStatus": "PUT_STATUS_OPEN",
                          "reviewStatus": "REVIEW_THROUGH",
                          "bidType": "MAX_CONV_STRATEGY"
                        }''')
    container_set.add_hosting_record('''{
                          "id": "456654",
                          "putStatus": "PUT_STATUS_OPEN",
                          "cpaBid": "654",
                          "bidType": "MAX_CONV_STRATEGY"
                        }''')
    container_set.add_industry_record('''{
                          "id": "123321",
                          "industryName": "123321_name",
                          "putStatus": "PUT_STATUS_OPEN",
                          "bidType": "MAX_CONV_STRATEGY"
                        }''')
    container_set.dump_containers(index_path + index_dt,
                    '{"info":[{"file_name":"account", "proto_name":"Account"} ]}');

    ad_leaf = self.__init_service(ad_flow)

    ad_log_item1 = ad_leaf.add_item(1, table_name="AdLogTable")
    ad_log_item1['account_id'] = 123
    ad_log_item1['campaign_id'] = 456
    ad_log_item1['unit_id'] = 789

    ad_leaf.run("test_tablelite_enrich")
    for item in ad_leaf.get_items("AdLogTable"):
      if item.item_key == 1:
        self.assertEqual(item["account_delivery_type"], 1)
        self.assertEqual(item["campaign_bidtype"], 1)
        self.assertEqual(item["industry_name"], "123321_name")
        self.assertEqual(item["hosting_cpa_bid"], 654)

    shutil.rmtree(index_path)

class TestFlowFunc2(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_bid_data_update(self):
    ad_flow = McbBudgetBidUpdateFlowTest(name="test_bid_data_update") \
              .bid_data_update_flow()

    last_hour_date_time = datetime.now() - timedelta(hours = 4)
    index_dt = last_hour_date_time.strftime('%Y-%m-%d_%H00')
    index_path = "../data_push/main_cost_second_bucket_to_bid_server/"
    Path(index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(index_path + "version", 'w') as f:
        f.write(index_dt)
    with open(index_path + index_dt + "/part-1", 'w') as f:
      for num in range(0,86401):
        f.write("********\t"+ str(num) + "\t" + str(num / 86400.0) + "\n")

    ad_leaf = self.__init_service(ad_flow)

    ad_log_item1 = ad_leaf.add_item(1, table_name="AdLogTable")
    ad_log_item1['account_id'] = 123
    ad_log_item1['campaign_id'] = 456
    ad_log_item1['unit_id'] = 789

    ad_leaf.run("test_bid_data_update")
    for item in ad_leaf.get_items("AdLogTable"):
      if item.item_key == 1:
        self.assertEqual(item["account_delivery_type"], 1)
        self.assertEqual(item["campaign_bidtype"], 1)
        self.assertEqual(item["industry_name"], "123321_name")
        self.assertEqual(item["hosting_cpa_bid"], 654)
    shutil.rmtree(index_path)

class TestFlowFunc_DataFetch(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_strategy_data_fetch(self):
    ad_flow = BidServerFlow(name="test_strategy_data_fetch") \
               .ad_strategy_data_fetch(
                  input_table_name = "AdLogTable",
                  base_info_table_name = "McbBaseInfoTable",
                  global_data_table_name = "McbGlobalDataTable",
                  global_data_window_table_name = "McbGlobalDataWindowTable",
                  bid_data_table_name = "McbBidDataTable",
                  bid_data_window_table_name = "McbBidDataWindowTable",
                  cluster_name = "BidServerGraph_staging",
                  base_info_redis_key_prefix = "McbBaseInfoTable:",
                  global_data_redis_key_prefix = "McbGlobalDataTable:",
                  global_data_window_redis_key_prefix = "McbGlobalDataWindowTable:",
                  bid_data_redis_key_prefix = "McbBidDataTable:",
                  bid_data_window_redis_key_prefix = "McbBidDataWindowTable:",
                  campaign_id = {"item_table" : "AdLogTable", "column" : "campaign_id"},
                  group_tag = {"item_table" : "AdLogTable", "column" : "group_tag"},
                ) \

    ad_leaf = self.__init_service(ad_flow)

    ad_log_item = ad_leaf.add_item(1, table_name="AdLogTable")
    ad_log_item['campaign_id'] = 999999
    ad_log_item['group_tag'] = "DSP_TAG_UNKNOWN"

    ad_leaf.run("test_strategy_data_fetch")

    for item in ad_leaf.get_items("McbBaseInfoTable"):
      self.assertEqual(item.item_key, 999999)
      self.assertEqual(item["group_tag"], "DSP_TAG_UNKNOWN")

    for item in ad_leaf.get_items("McbGlobalDataTable"):
      self.assertEqual(item.item_key, 9881053938153123373)
      self.assertEqual(item["campaign_id"], 999999)
      self.assertEqual(item["group_tag"], "DSP_TAG_UNKNOWN")

    for item in ad_leaf.get_items("McbGlobalDataWindowTable"):
      self.assertEqual(item.item_key, 9881053938153123373)
      self.assertEqual(item["campaign_id"], 999999)
      self.assertEqual(item["group_tag"], "DSP_TAG_UNKNOWN")

    for item in ad_leaf.get_items("McbBidDataTable"):
      self.assertEqual(item.item_key, 9881053938153123373)
      self.assertEqual(item["campaign_id"], 999999)
      self.assertEqual(item["group_tag"], "DSP_TAG_UNKNOWN")

    for item in ad_leaf.get_items("McbBidDataWindowTable"):
      self.assertEqual(item.item_key, 9881053938153123373)
      self.assertEqual(item["campaign_id"], 999999)
      self.assertEqual(item["group_tag"], "DSP_TAG_UNKNOWN")

class TestFlowFunc_BudgetLogTransform(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_budget_fill_base_info(self):
    ad_flow = BidServerFlow(name="test_budget_fill_base_info") \
               .budget_fill_base_info_enricher(
                  input_table_name = "AdBudgetTable",
                  base_info_table_name = "McbBaseInfoTable",
                  mcb_level = 2,
                  ocpx_action_type = {"item_table" : "McbBaseInfoTable", "column" : "ocpc_action_type"},
                  deep_conversion_type = {"item_table" : "McbBaseInfoTable", "column" : "deep_conversion_type"},
                  ad_common_update = [
                    {"column": "campaign_id", "type": "int", "from_table": "AdBudgetTable", "use_from_key": True},
                    {"column": "account_id", "type": "int", "from_table": "AdBudgetTable", "use_from_key": True},
                  ]
               )

    last_hour_date_time = datetime.now() - timedelta(hours = 4)
    index_dt = last_hour_date_time.strftime('%Y-%m-%d_%H00')
    index_path = "../data_push/ad_collective_index_for_bid/"
    Path(index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(index_path + "version", 'w') as f:
        f.write(index_dt)

    container_set = ContainerSet(False);
    container_set.enable_table(2) #campaign
    container_set.enable_table(3) # unit
    container_set.init()

    container_set.add_campaign_record('''{
                          "id": "999999",
                          "projectId": "456654",
                          "putStatus": "PUT_STATUS_OPEN",
                          "bidType": "MAX_CONV_STRATEGY"
                        }''')
    container_set.add_unit_record('''{
                          "id": "123",
                          "campaign_id": "999999",
                          "idDeleted": "0",
                          "putStatus": "PUT_STATUS_OPEN",
                          "reviewStatus": "REVIEW_THROUGH",
                          "bidType": "MAX_CONV_STRATEGY",
                          "ocpx_action_type": "AD_CONVERSION",
                          "deep_conversion_type": "EVENT_ORDER_SUCCESSED"
                        }''')
    container_set.dump_containers(index_path + index_dt,
                    '{"info":[{"file_name":"account", "proto_name":"Account"} ]}');
    ad_leaf = self.__init_service(ad_flow)

    ad_budget_item = ad_leaf.add_item(1, table_name="AdBudgetTable")
    ad_budget_item['account_id'] = 666666
    ad_budget_item['campaign_id'] = 999999
    ad_budget_item['group_tag'] = "DSP_TAG_UNKNOWN"

    base_info_item = ad_leaf.add_item(999999, table_name="McbBaseInfoTable");
    base_info_item['campaign_id'] = 999999

    ad_leaf.run("test_budget_fill_base_info")

    for item in ad_leaf.get_items("McbBaseInfoTable"):
      self.assertEqual(item.item_key, 999999)
      self.assertEqual(item["group_tag"], "DSP_TAG_UNKNOWN")
      self.assertEqual(item["account_id"], 666666)
      self.assertEqual(item["ocpc_action_type"], 180)
      self.assertEqual(item["deep_conversion_type"], 119)
      self.assertEqual(item["mcb_level"], 2)
      self.assertEqual(len(item["group_tags"]), 1)
      self.assertListEqual(item["group_tags"], ["DSP_TAG_UNKNOWN"])

  def test_budget_fill_base_info(self):
    ad_flow = BidServerFlow(name="test_budget_log_transform") \
               .budget_log_transform_enricher(
                  item_table = "AdBudgetTable",
                  account_id = {"item_table" : "AdBudgetTable", "column" : "account_id"},
                  tag_charge_info_size = {"item_table" : "AdBudgetTable", "column" : "tag_charge_info_size"},
                  combine_key = {"item_table" : "AdBudgetTable", "column" : "combine_key"},
                  group_tag_str = {"item_table" : "AdBudgetTable", "column" : "group_tag"},
                  charge_tag = {"item_table" : "AdBudgetTable", "column" : "tag_charge_info.charge_tag"},
                  campaign_charge = {"item_table" : "AdBudgetTable", "column" : "tag_charge_info.campaign_charge"},
                  increment_charge = {"item_table" : "AdBudgetTable", "column" : "increment_charge"},
                  new_bid_type = {"item_table" : "AdBudgetTable", "column" : "bid_type_transfer_info.new_bid_type"},
                  bid_type_transfer_type = {"item_table" : "AdBudgetTable", "column" : "bid_type_transfer_info.bid_type_transfer_type"},

                  last_trigger_charge = {"item_table" : "McbBidDataTable", "column" : "last_trigger_charge"},
                  origin_bid_type = {"item_table" : "McbBidDataTable", "column" : "bid_type_transfer_info.origin_bid_type"},

                  product_id = {"item_table" : "McbBaseInfoTable", "column" : "account_id"},
                  ocpx_action_type = {"item_table" : "McbBaseInfoTable", "column" : "ocpc_action_type"},
                  deep_conversion_type = {"item_table" : "McbBaseInfoTable", "column" : "deep_conversion_type"}
              )
    ad_leaf = self.__init_service(ad_flow)

    ad_budget_item = ad_leaf.add_item(1, table_name="AdBudgetTable")
    ad_budget_item['start_of_day_ts'] = ****************
    ad_budget_item['combine_key'] = 666666
    ad_budget_item['campaign_id'] = 999999
    ad_budget_item['campaign_charge'] = 193774
    ad_budget_item['campaign_budget'] = 166000
    ad_budget_item['campaign_left_budget'] = 172226
    ad_budget_item['locked_campaign_left_budget'] = 172226
    ad_budget_item['budget_status'] = 0
    ad_budget_item['locked_campaign_queue_index'] = 1
    ad_budget_item['account_id'] = 11111
    ad_budget_item['account_balance'] = ********
    ad_budget_item['account_charge'] = 166005
    ad_budget_item['account_budget'] = 1000000
    ad_budget_item['account_left_budget'] = 233995
    ad_budget_item['group_tag'] = "DSP_TAG_UNKNOWN"
    ad_budget_item['event_server_timestamp'] = 0
    ad_budget_item['tag_charge_info_size'] = 0
    ad_budget_item['tag_charge_info.charge_tag'] = 0
    ad_budget_item['tag_charge_info.account_charge'] = 766005
    ad_budget_item['tag_charge_info.account_budget'] = 2000000
    ad_budget_item['tag_charge_info.account_left_budget'] = 1233995
    ad_budget_item['tag_charge_info.campaign_charge'] = 393774
    ad_budget_item['tag_charge_info.campaign_budget'] = 966000
    ad_budget_item['tag_charge_info.campaign_left_budget'] = 572226
    ad_budget_item['tag_charge_info.locked_campaign_left_budget'] = 572226

    bid_data_item = ad_leaf.add_item(ad_budget_item['combine_key'], table_name="McbBidDataTable")
    bid_data_item['last_trigger_charge'] = 193770

    ad_leaf.run("test_budget_log_transform")

    for item in ad_leaf.get_items("AdBudgetTable"):
      self.assertEqual(item["tag_charge_info.account_charge"], 166005)
      self.assertEqual(item["tag_charge_info.account_budget"], 1000000)
      self.assertEqual(item["tag_charge_info.account_left_budget"], 233995)
      self.assertEqual(item["tag_charge_info.campaign_charge"], 193774)
      self.assertEqual(item["tag_charge_info.campaign_budget"], 166000)
      self.assertEqual(item["tag_charge_info.campaign_left_budget"], 172226)
      self.assertEqual(item["tag_charge_info.locked_campaign_left_budget"], 172226)
      #self.assertEqual(item["increment_charge"], 4)


class TestFlowFunc_BidFillBaseInfo(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_bid_fill_base_info(self):
    ad_flow = BidServerFlow(name="test_bid_fill_base_info") \
               .bid_fill_base_info_enricher(
                  item_table = "AdLogTable",
                  mcb_level_value = 2,
                  account_id_src = {"item_table" : "AdLogTable", "column" : "account_id"},
                  campaign_id_src = {"item_table" : "AdLogTable", "column" : "campaign_id"},
                  group_tag_src = {"item_table" : "AdLogTable", "column" : "group_tag"},
                  ocpx_action_type_src = {"item_table" : "AdLogTable", "column" : "ocpc_action_type"},
                  deep_conversion_type_src = {"item_table" : "AdLogTable", "column" : "deep_conversion_type"},
                  account_id_dst = {"item_table" : "McbBaseInfoTable", "column" : "account_id"},
                  campaign_id_dst = {"item_table" : "McbBaseInfoTable", "column" : "campaign_id"},
                  ocpx_action_type_dst = {"item_table" : "McbBaseInfoTable", "column" : "ocpc_action_type"},
                  deep_conversion_type_dst = {"item_table" : "McbBaseInfoTable", "column" : "deep_conversion_type"},
                  retention_active_dst = {"item_table" : "McbBaseInfoTable", "column" : "retention_active"},
                  group_tag_dst = {"item_table" : "McbBaseInfoTable", "column" : "group_tag"},
                  group_tags = {"item_table" : "McbBaseInfoTable", "column" : "group_tags"},
                  mcb_level = {"item_table" : "McbBaseInfoTable", "column" : "mcb_level"},
                )

    ad_leaf = self.__init_service(ad_flow)

    ad_log_item = ad_leaf.add_item(1, table_name="AdLogTable")
    ad_log_item['account_id'] = 666666
    ad_log_item['campaign_id'] = 999999
    ad_log_item['group_tag'] = "DSP_TAG_UNKNOWN"
    ad_log_item['ocpc_action_type'] = 180
    ad_log_item['deep_conversion_type'] = 3

    base_info_item = ad_leaf.add_item(999999, table_name="McbBaseInfoTable");
    base_info_item['campaign_id'] = 999999
    base_info_item['group_tags'] = ["DSP_TAG_10"]

    ad_leaf.run("test_bid_fill_base_info")

    for item in ad_leaf.get_items("McbBaseInfoTable"):
      self.assertEqual(item.item_key, 999999)
      self.assertEqual(item["group_tag"], "DSP_TAG_UNKNOWN")
      self.assertEqual(item["account_id"], 666666)
      self.assertEqual(item["ocpc_action_type"], 180)
      self.assertEqual(item["deep_conversion_type"], 3)
      self.assertEqual(item["mcb_level"], 2)
      self.assertEqual(item["retention_active"], 0)
      self.assertEqual(len(item["group_tags"]), 2)
      self.assertListEqual(item["group_tags"], ["DSP_TAG_10", "DSP_TAG_UNKNOWN"])

class TestFlowFunc_McbConfigFetch(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_mcb_config_fetch(self):
    ad_flow = BidServerFlow(name="test_mcb_config_fetch") \
               .mcb_config_fetcher(
                  item_table = "AdLogTable",
                  strategy_name = "mcb_cpa",
                  ocpx_action_type = {"item_table" : "McbBaseInfoTable", "column" : "ocpc_action_type"},
                  deep_conversion_type = {"item_table" : "McbBaseInfoTable", "column" : "deep_conversion_type"},
                  campaign_type = {"item_table" : "McbBaseInfoTable", "column" : "campaign_type"},
                  group_tag = {"item_table" : "McbBaseInfoTable", "column" : "group_tag"},
                  mcb_level = {"item_table" : "McbBaseInfoTable", "column" : "mcb_level"},
                  account_id = {"item_table" : "McbBaseInfoTable", "column" : "account_id"},
                  campaign_id = {"item_table" : "AdLogTable", "column" : "campaign_id"},
                  config_items = [
                    {"key" : "block_second", "default_value" : 60.0,  "item_table" : "McbConfigTable", "column" : "block_second"},
                    {"key" : "cost_threshold", "default_value" : 0.0,  "item_table" : "McbConfigTable", "column" : "cost_threshold"},
                    {"key" : "batch_cpa_ratio", "default_value" : 0.3,  "item_table" : "McbConfigTable", "column" : "batch_cpa_ratio"},
                    {"key" : "force_update_second", "default_value" : 900.0,  "item_table" : "McbConfigTable", "column" : "force_update_second"},
                    {"key" : "min_avg_cpa_bid", "default_value" : 50.0,  "item_table" : "McbConfigTable", "column" : "min_avg_cpa_bid"},
                  ]
                )

    ad_leaf = self.__init_service(ad_flow)

    ad_log_item = ad_leaf.add_item(1234321, table_name="AdLogTable")
    ad_log_item['campaign_id'] = *********
    ad_log_item['group_tag'] = "DSP_TAG_UNKNOWN"

    base_info_item = ad_leaf.add_item(*********, table_name="McbBaseInfoTable");
    base_info_item['account_id'] = 666666
    base_info_item['campaign_id'] = *********
    base_info_item['group_tag'] = "DSP_TAG_UNKNOWN"
    base_info_item['ocpc_action_type'] = 180
    base_info_item['deep_conversion_type'] = 3
    base_info_item['mcb_level'] = 2

    ad_leaf.run("test_mcb_config_fetch")

    self.assertEqual(len(ad_leaf.get_items("McbConfigTable")), 1)
    for item in ad_leaf.get_items("McbConfigTable"):
      self.assertEqual(item.item_key, 1234321)
      self.assertIsNotNone(item["block_second"])
      self.assertIsNotNone(item["cost_threshold"])
      self.assertIsNotNone(item["batch_cpa_ratio"])
      self.assertIsNotNone(item["force_update_second"])
      self.assertIsNotNone(item["min_avg_cpa_bid"])

class TestFlowFunc_log_trigger(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def add_unit_record(self, container_set, campaign_id, unit_id):
    campaign_info = { "id": str(campaign_id),
                      "projectId": "456654",
                      "putStatus": "PUT_STATUS_OPEN",
                      "bidType": "MAX_CONV_STRATEGY"}
    container_set.add_campaign_record(json.dumps(campaign_info))

    unit_info = { "id": str(unit_id),
                  "campaign_id": str(campaign_id),
                  "idDeleted": "0",
                  "putStatus": "PUT_STATUS_OPEN",
                  "reviewStatus": "REVIEW_THROUGH",
                  "bidType": "MAX_CONV_STRATEGY"}
    container_set.add_unit_record(json.dumps(unit_info))

  def test_mcb_log_trigger_enricher(self):
    adlog_flow = McbLogTriggerFlowTest(name="test_mcb_log_trigger_enricher") \
                .mcb_log_trigger_flow()

    # mock table_lite
    last_hour_date_time = datetime.now() - timedelta(hours = 2)
    index_path = "../data_push/ad_collective_index_for_bid/"
    index_dt = last_hour_date_time.strftime('%Y-%m-%d_%H00')
    Path(index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(index_path + "version", 'w') as f:
        f.write(index_dt)

    container_set = ContainerSet(False)
    container_set.enable_table(1) #account
    container_set.enable_table(2) #campaign
    container_set.enable_table(3) # unit
    container_set.init()
    container_set.add_account_record('''{
                          "id": "********",
                          "userId": "222",
                          "frozenStatus": "FROZEN_STATUS_CLOSED",
                          "putStatus": "PUT_STATUS_OPEN",
                          "reviewStatus": "REVIEW_THROUGH",
                          "deliveryType": "PRIORITY_EFFECT",
                          "industryIdV3": "123321"
                        }''')

    for i in range(4):
      self.add_unit_record(container_set, ********** + i, 123 + i)

    container_set.dump_containers(index_path + index_dt,
                    '{"info":[{"file_name":"account", "proto_name":"Account"} ]}');

    ad_leaf = self.__init_service(adlog_flow)

    ''' mock table '''
    for i in range(4):
      mock_table = McbLogTriggerMockTable(i)
      combine_key = mock_table.combine_key
      ad_log_item = ad_leaf.add_item(mock_table.unit_id, table_name="AdLogTable")
      mcb_config_item = ad_leaf.add_item(mock_table.unit_id, table_name="McbConfigTable")
      ad_base_item = ad_leaf.add_item(mock_table.campaign_id, table_name="McbBaseInfoTable")
      global_data_item = ad_leaf.add_item(combine_key, table_name="McbGlobalDataTable")
      global_widow_item = ad_leaf.add_item(combine_key, table_name="McbGlobalDataWindowTable")
      bid_data_item = ad_leaf.add_item(combine_key, table_name="McbBidDataTable")
      bid_widow_item = ad_leaf.add_item(combine_key, table_name="McbBidDataWindowTable")

      mock_table.mock_ad_log_table(ad_log_item)
      mock_table.mock_mcb_config_table(mcb_config_item)
      mock_table.mock_ad_base_table(ad_base_item)
      mock_table.mock_global_data_table(global_data_item)
      mock_table.mock_global_window_table(global_widow_item)
      mock_table.mock_bid_data_table(bid_data_item)
      mock_table.mock_bid_window_table(bid_widow_item)

      mock_table.case_prepre(ad_leaf, i)

    ad_leaf.run("test_mcb_log_trigger_enricher")

    i = 0
    for item in ad_leaf.get_items("McbBidDataTable"):
      event_server_timestamp = item['event_server_timestamp']
      print (i, event_server_timestamp, item['campaign_id'])
      found = False
      for global_item in ad_leaf.get_items("McbGlobalDataTable"):
        if global_item['campaign_id'] == item['campaign_id']:
          found = True
          print (i, global_item['last_cpa_trigger_ts'], item['campaign_id'])
          if i == 0 or i == 1:    # block， 不调价
            self.assertNotEqual(global_item['last_cpa_trigger_ts'], event_server_timestamp)
          else:         # 触发调价
            self.assertEqual(global_item['last_cpa_trigger_ts'], event_server_timestamp)
          break

      self.assertTrue(found)
      i = i + 1
    shutil.rmtree(index_path)

class TestFlowFunc_budget_trigger(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def add_unit_record(self, container_set, campaign_id, unit_id):
    campaign_info = { "id": str(campaign_id),
                      "projectId": "456654",
                      "putStatus": "PUT_STATUS_OPEN",
                      "bidType": "MAX_CONV_STRATEGY"}
    container_set.add_campaign_record(json.dumps(campaign_info))

    unit_info = { "id": str(unit_id),
                  "campaign_id": str(campaign_id),
                  "idDeleted": "0",
                  "putStatus": "PUT_STATUS_OPEN",
                  "reviewStatus": "REVIEW_THROUGH",
                  "bidType": "MAX_CONV_STRATEGY"}
    container_set.add_unit_record(json.dumps(unit_info))

  def test_mcb_budget_trigger_enricher(self):
    ad_flow = McbBudgetTriggerFlowTest(name="test_mcb_budget_trigger_enricher").mcb_budget_trigger_flow()

    last_hour_date_time = datetime.now() - timedelta(hours = 2)
    index_dt = last_hour_date_time.strftime('%Y-%m-%d_%H00')

    # mock p2p
    p2p_index_path = "../data_push/main_cost_second_bucket_to_bid_server/"
    Path(p2p_index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(p2p_index_path + "version", 'w') as f:
        f.write(index_dt)
    with open(p2p_index_path + index_dt + "/part-1", 'w') as f:
      for num in range(0, 86401):
        f.write("********\t"+ str(num) + "\t" + str(num / 86400.0) + "\n")

    # mock table_lite
    index_path = "../data_push/ad_collective_index_for_bid/"
    Path(index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(index_path + "version", 'w') as f:
        f.write(index_dt)

    container_set = ContainerSet(False)
    container_set.enable_table(1) #account
    container_set.enable_table(2) #campaign
    container_set.enable_table(3) # unit
    container_set.init()
    container_set.add_account_record('''{
                          "id": "********",
                          "userId": "222",
                          "frozenStatus": "FROZEN_STATUS_CLOSED",
                          "putStatus": "PUT_STATUS_OPEN",
                          "reviewStatus": "REVIEW_THROUGH",
                          "deliveryType": "PRIORITY_EFFECT",
                          "industryIdV3": "123321"
                        }''')

    for i in range(4):
      self.add_unit_record(container_set, ********** + i, 123 + i)

    container_set.dump_containers(index_path + index_dt,
                    '{"info":[{"file_name":"account", "proto_name":"Account"} ]}');
    ad_leaf = self.__init_service(ad_flow)

    ''' mock table '''
    for i in range(4):
      mock_table = McbBudgetTriggerMockTable(i)
      combine_key = mock_table.combine_key
      ad_budget_item = ad_leaf.add_item(mock_table.campaign_id, table_name="AdBudgetTable")
      mcb_config_item = ad_leaf.add_item(mock_table.campaign_id, table_name="McbPiddHyperParamterTable")
      ad_base_item = ad_leaf.add_item(mock_table.campaign_id, table_name="McbBaseInfoTable")
      ad_common_item = ad_leaf.add_item(combine_key, table_name="McbAdCommonTable")
      global_data_item = ad_leaf.add_item(combine_key, table_name="McbGlobalDataTable")
      bid_data_item = ad_leaf.add_item(combine_key, table_name="McbBidDataTable")

      mock_table.mock_ad_budet_table(ad_budget_item)
      mock_table.mock_mcb_config_table(mcb_config_item)
      mock_table.mock_ad_base_table(ad_base_item)
      mock_table.mock_ad_common_table(ad_common_item)
      mock_table.mock_global_data_table(global_data_item)
      mock_table.mock_bid_data_table(bid_data_item)

      mock_table.case_prepre(ad_leaf, i)

    ad_leaf.run("test_mcb_budget_trigger_enricher")

    budget_data_0 = ad_leaf.get_items("AdBudgetTable")[0]
    base_data_0 = ad_leaf.get_items("McbBaseInfoTable")[0]
    global_data_0 = ad_leaf.get_items("McbGlobalDataTable")[0]
    bid_data_0 = ad_leaf.get_items("McbBidDataTable")[0]

    # FillGlobalDataTable
    self.assertEqual(global_data_0["account_id"], budget_data_0["account_id"])
    self.assertEqual(global_data_0["campaign_id"], budget_data_0["campaign_id"])
    self.assertEqual(global_data_0["start_of_day_ts"], budget_data_0["start_of_day_ts"])
    self.assertEqual(global_data_0["all_budget"], budget_data_0["tag_charge_info.campaign_budget"])
    self.assertEqual(global_data_0["increment_charge"], budget_data_0["increment_charge"])
    self.assertEqual(global_data_0["left_budget"], budget_data_0["tag_charge_info.campaign_left_budget"])
    self.assertEqual(global_data_0["locked_left_budget"], budget_data_0["tag_charge_info.locked_campaign_left_budget"])
    self.assertEqual(global_data_0["today_charge"], budget_data_0["tag_charge_info.campaign_charge"])
    self.assertEqual(global_data_0["ocpx_action_type"], base_data_0["ocpc_action_type"])

    # FillBidDataTable
    self.assertEqual(bid_data_0["budget_opt_info.campaign_all_budget"], budget_data_0["campaign_budget"])
    self.assertEqual(bid_data_0["locked_campaign_queue_index"], budget_data_0["locked_campaign_queue_index"])
    self.assertEqual(bid_data_0["left_budget"], global_data_0["left_budget"])
    self.assertEqual(bid_data_0["locked_left_budget"], global_data_0["locked_left_budget"])
    # self.assertEqual(bid_data_0["last_target_factor"], global_data_0["target_factor"])
    self.assertEqual(bid_data_0["create_source_type"], base_data_0["create_source_type"])
    self.assertEqual(bid_data_0["mcb_level"], base_data_0["mcb_level"])
    self.assertEqual(bid_data_0["product_name"], base_data_0["product_name"])
    self.assertEqual(bid_data_0["first_industry_id"], base_data_0["first_industry_id"])
    self.assertEqual(bid_data_0["second_industry_id"], base_data_0["second_industry_id"])
    self.assertEqual(bid_data_0["industry_name"], base_data_0["industry_name"])
    self.assertEqual(bid_data_0["project_id"], base_data_0["project_id"])
    self.assertEqual(bid_data_0["campaign_time_schedule"], base_data_0["campaign_time_schedule"])
    self.assertEqual(bid_data_0["campaign_type"], base_data_0["campaign_type"])

    i = 0
    for item in ad_leaf.get_items("McbBidDataTable"):
      event_server_timestamp = item['event_server_timestamp']
      print (i, event_server_timestamp, item['campaign_id'])
      found = False
      for global_item in ad_leaf.get_items("McbGlobalDataTable"):
        if global_item['campaign_id'] == item['campaign_id']:
          found = True
          print (i, global_item['last_trigger_ts'], item['campaign_id'])
          if i == 0:    # block， 不调价
            self.assertNotEqual(global_item['last_trigger_ts'], event_server_timestamp)
          else:         # 触发调价
            self.assertEqual(global_item['last_trigger_ts'], event_server_timestamp)
          break

      self.assertTrue(found)
      i = i + 1

    shutil.rmtree(index_path)
    shutil.rmtree(p2p_index_path);

class TestFlowFunc_update(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_bid_data_update(self):
    ad_flow = McbBudgetBidUpdateFlowTest(name="test_bid_data_update") \
              .bid_data_update_flow()

    last_hour_date_time = datetime.now() - timedelta(hours = 2)
    index_path = "../data_push/ad_collective_index_for_bid/"
    index_dt = last_hour_date_time.strftime('%Y-%m-%d_%H00')
    Path(index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(index_path + "version", 'w') as f:
        f.write(index_dt)

    ProductAvgResult_path = "../data_push/mcb_initial_data/"
    Path(ProductAvgResult_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(ProductAvgResult_path + "version", 'w') as f:
        f.write(index_dt)
    with open(ProductAvgResult_path + index_dt + "/part-1", 'w') as f:
      for num in range(0,1):
        f.write("product_name\t1779489\t麦可\tEVENT_NEXTDAY_STAY\t194229.4375\t0.0051\n")

    ProductAvgRetentionTarget_path = "../data_push/mcb_retention_target/"
    Path(ProductAvgRetentionTarget_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(ProductAvgRetentionTarget_path + "version", 'w') as f:
        f.write(index_dt)
    with open(ProductAvgRetentionTarget_path + index_dt + "/part-1", 'w') as f:
      for num in range(0,1):
        f.write("ocpm_target\t麦可\t362625.483\t13835.0\t2607.0\t0.*****************\n")

    container_set = ContainerSet(False)
    container_set.enable_table(1) #account
    container_set.enable_table(2) #campaign
    container_set.init()
    container_set.add_account_record('''{
                          "id": "********",
                          "putStatus": "PUT_STATUS_OPEN"
                        }''')
    container_set.add_campaign_record('''{
                          "id": "**********",
                          "cap_bid": "0",
                          "constraint_cpa": "0",
                          "cap_roi_ratio": "0.0"
                        }''')
    container_set.dump_containers(index_path + index_dt,
                    '{"info":[{"file_name":"campaign", "proto_name":"Campaign"} ]}');

    ad_leaf = self.__init_service(ad_flow)

    bid_data_item = ad_leaf.add_item(**********, table_name="McbBidDataTable")
    bid_data_item['product_roi_ratio'] = 0.*****************
    bid_data_item['product_cpa_bid'] = 194229.4375
    bid_data_item['mcb_retention_target'] = 0.*****************
    bid_data_item['performance_cpa_bid'] = 269658.09375
    bid_data_item['performance_roi_ratio'] = 0.0
    bid_data_item['left_budget'] = 472614
    bid_data_item['retention_active'] = 1

    common_item = ad_leaf.add_item(**********, table_name="McbAdCommonTable")
    common_item['campaign_id'] = **********

    base_item = ad_leaf.add_item(**********, table_name="McbBaseInfoTable")
    base_item['account_id'] = ********
    base_item['campaign_id'] = **********
    base_item['ocpc_action_type'] = 346
    base_item['product_name'] = "麦可"
    base_item['second_industry_id'] = 1248
    base_item['campaign_type'] = 2
    base_item['hosting_cpa_bid'] = 0.0
    base_item['create_source_type'] = 0
    base_item['mcb_level'] = 2

    ad_leaf.run("test_bid_data_update")
    for item in ad_leaf.get_items("McbBidDataTable"):
        self.assertEqual(item["bonus_price_ratio"], 1.0)
        self.assertEqual(item["product_roi_ratio"], 0.*****************)
        self.assertEqual(item["mcb_retention_target"], 0.*****************)
        self.assertEqual(item["budget_opt_info.constraint_cap_bid_type"], 2)
        self.assertEqual(item["budget_opt_info.cap_bid_type"], 2)

class TestFlowFunc_update1(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_bid_data_update(self):
    ad_flow = McbBudgetBidUpdateFlowTest(name="test_bid_data_update") \
              .bid_data_update_flow()

    last_hour_date_time = datetime.now() - timedelta(hours = 2)
    index_path = "../data_push/ad_collective_index_for_bid/"
    index_dt = last_hour_date_time.strftime('%Y-%m-%d_%H00')
    Path(index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(index_path + "version", 'w') as f:
        f.write(index_dt)

    ProductAvgResult_path = "../data_push/mcb_initial_data/"
    Path(ProductAvgResult_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(ProductAvgResult_path + "version", 'w') as f:
        f.write(index_dt)
    with open(ProductAvgResult_path + index_dt + "/part-1", 'w') as f:
      f.write("product_name\t1779489\t麦可\tEVENT_NEXTDAY_STAY\t194229.4375\t0.0051\n")
      f.write("product_name\t7601173\tsoul\tEVENT_NEXTDAY_STAY\t70895.83\t0.0141\n")

    ProductAvgRetentionTarget_path = "../data_push/mcb_retention_target/"
    Path(ProductAvgRetentionTarget_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(ProductAvgRetentionTarget_path + "version", 'w') as f:
        f.write(index_dt)
    with open(ProductAvgRetentionTarget_path + index_dt + "/part-1", 'w') as f:
      f.write("ocpm_target\t麦可\t362625.483\t13835.0\t2607.0\t0.*****************\n")
      f.write("ocpm_target\tsoul\t3489370.055\t108230.0\t53100.0\t0.5\n")

    container_set = ContainerSet(False)
    container_set.enable_table(1) #account
    container_set.enable_table(2) #campaign
    container_set.init()
    container_set.add_account_record('''{
                          "id": "********",
                          "putStatus": "PUT_STATUS_OPEN",
                          "userId": "222",
                          "frozenStatus": "FROZEN_STATUS_CLOSED",
                          "putStatus": "PUT_STATUS_OPEN",
                          "reviewStatus": "REVIEW_THROUGH",
                          "deliveryType": "PRIORITY_EFFECT",
                          "industryIdV3": "123321",
                          "extend_fields": {
                            "expect_cost_info": [{
                              "constraint_value": 32.*****************,
                              "ocpx_action_type": 346,
                              "value": 64.*****************,
                              "constraint_ocpx_action_type": 180}]
                          }
                        }''')
    container_set.add_campaign_record('''{
                          "id": "**********",
                          "projectId": "456654",
                          "putStatus": "PUT_STATUS_OPEN",
                          "bidType": "MAX_CONV_STRATEGY",
                          "cap_bid": "0",
                          "constraint_cpa": "29500",
                          "cap_roi_ratio": "0.0"
                        }''')
    container_set.dump_containers(index_path + index_dt,
                    '{"info":[{"file_name":"account", "proto_name":"Account"} ]}');

    ad_leaf = self.__init_service(ad_flow)
    campaign_id = **********
    bid_data_item = ad_leaf.add_item(campaign_id, table_name="McbBidDataTable")
    bid_data_item['product_roi_ratio'] = 0.*****************
    bid_data_item['product_cpa_bid'] = 64000
    bid_data_item['mcb_retention_target'] = 0.5
    bid_data_item['performance_cpa_bid'] = 64598.2734375
    bid_data_item['performance_roi_ratio'] = 0.0
    bid_data_item['left_budget'] = 51781
    bid_data_item['retention_active'] = 1

    common_item = ad_leaf.add_item(campaign_id, table_name="McbAdCommonTable")
    common_item['campaign_id'] = campaign_id

    base_item = ad_leaf.add_item(campaign_id, table_name="McbBaseInfoTable")
    base_item['account_id'] = ********
    base_item['campaign_id'] = campaign_id
    base_item['ocpc_action_type'] = 346
    base_item['product_name'] = "soul"
    base_item['second_industry_id'] = 1248
    base_item['campaign_type'] = 2
    base_item['hosting_cpa_bid'] = 66000
    base_item['create_source_type'] = 1
    base_item['mcb_level'] = 2

    ad_leaf.run("test_bid_data_update")
    for item in ad_leaf.get_items("McbBidDataTable"):
        self.assertEqual(item["bonus_price_ratio"], 1.0)
        self.assertEqual(item["product_roi_ratio"], 0.*****************)
        self.assertEqual(item["product_cpa_bid"], 64000)
        self.assertEqual(item["mcb_retention_target"], 0.5)
        self.assertEqual(item["budget_opt_info.constraint_cap_bid_type"], 5)
        self.assertEqual(item["budget_opt_info.cap_bid_type"], 5)

class TestFlowFunc_update2(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_bid_data_update(self):
    ad_flow = McbBudgetBidUpdateFlowTest(name="test_bid_data_update") \
              .bid_data_update_flow()

    last_hour_date_time = datetime.now() - timedelta(hours = 2)
    index_path = "../data_push/ad_collective_index_for_bid/"
    index_dt = last_hour_date_time.strftime('%Y-%m-%d_%H00')
    Path(index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(index_path + "version", 'w') as f:
        f.write(index_dt)

    ProductAvgResult_path = "../data_push/mcb_initial_data/"
    Path(ProductAvgResult_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(ProductAvgResult_path + "version", 'w') as f:
        f.write(index_dt)
    with open(ProductAvgResult_path + index_dt + "/part-1", 'w') as f:
      f.write("product_name\t1779489\t麦可\tEVENT_NEXTDAY_STAY\t194229.4375\t0.0051\n")
      f.write("product_name\t7601173\tsoul\tEVENT_NEXTDAY_STAY\t70895.83\t0.0141\n")
      f.write("product_name\t9462402\t易车\tAD_CONVERSION\t96128.09705\t0.0104\n")

    ProductAvgRetentionTarget_path = "../data_push/mcb_retention_target/"
    Path(ProductAvgRetentionTarget_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(ProductAvgRetentionTarget_path + "version", 'w') as f:
        f.write(index_dt)
    with open(ProductAvgRetentionTarget_path + index_dt + "/part-1", 'w') as f:
      f.write("ocpm_target\t麦可\t362625.483\t13835.0\t2607.0\t0.*****************\n")
      f.write("ocpm_target\tsoul\t3489370.055\t108230.0\t53100.0\t0.5\n")
      f.write("ocpm_target\t易车\t2782.305\t154.01\t4.0\t0.*****************\n")

    container_set = ContainerSet(False)
    container_set.enable_table(1) #account
    container_set.enable_table(2) #campaign
    container_set.init()
    container_set.add_account_record('''{
                          "id": "********",
                          "putStatus": "PUT_STATUS_OPEN",
                          "userId": "222",
                          "frozenStatus": "FROZEN_STATUS_CLOSED",
                          "putStatus": "PUT_STATUS_OPEN",
                          "reviewStatus": "REVIEW_THROUGH",
                          "deliveryType": "PRIORITY_EFFECT"
                        }''')
    container_set.add_campaign_record('''{
                          "id": "**********",
                          "projectId": "456654",
                          "putStatus": "PUT_STATUS_OPEN",
                          "bidType": "MAX_CONV_STRATEGY",
                          "cap_bid": "68000",
                          "constraint_cpa": "0",
                          "cap_roi_ratio": "0.0"
                        }''')
    container_set.dump_containers(index_path + index_dt,
                    '{"info":[{"file_name":"account", "proto_name":"Account"} ]}');

    ad_leaf = self.__init_service(ad_flow)
    campaign_id = **********
    bid_data_item = ad_leaf.add_item(campaign_id, table_name="McbBidDataTable")
    bid_data_item['product_roi_ratio'] = 0
    bid_data_item['product_cpa_bid'] = 0
    bid_data_item['mcb_retention_target'] = 0.0
    bid_data_item['performance_cpa_bid'] = 24379.*********
    bid_data_item['performance_roi_ratio'] = 0.0
    bid_data_item['left_budget'] = 74872
    bid_data_item['retention_active'] = 0

    common_item = ad_leaf.add_item(campaign_id, table_name="McbAdCommonTable")
    common_item['campaign_id'] = campaign_id

    base_item = ad_leaf.add_item(campaign_id, table_name="McbBaseInfoTable")
    base_item['account_id'] = ********
    base_item['campaign_id'] = campaign_id
    base_item['ocpc_action_type'] = 180
    base_item['product_name'] = "易车"
    base_item['second_industry_id'] = 1114
    base_item['campaign_type'] = 2
    base_item['hosting_cpa_bid'] = 0
    base_item['create_source_type'] = 0
    base_item['mcb_level'] = 2

    ad_leaf.run("test_bid_data_update")
    for item in ad_leaf.get_items("McbBidDataTable"):
        self.assertEqual(item["bonus_price_ratio"], 1.0)
        self.assertEqual(item["budget_opt_info.cap_bid_type"], 6)
        self.assertEqual(item["product_roi_ratio"], 0.*****************)
        self.assertEqual(item["product_cpa_bid"], 68000)
        self.assertEqual(item["mcb_retention_target"], 0.0)

class TestFlowFunc_update_reset(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_bid_data_update(self):
    ad_flow = McbBudgetBidUpdateFlowTest(name="test_bid_data_update") \
              .bid_data_update_flow()
    last_hour_date_time = datetime.now() - timedelta(hours = 2)
    index_path = "../data_push/ad_collective_index_for_bid/"
    index_dt = last_hour_date_time.strftime('%Y-%m-%d_%H00')
    Path(index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(index_path + "version", 'w') as f:
        f.write(index_dt)

    ProductAvgResult_path = "../data_push/mcb_initial_data/"
    Path(ProductAvgResult_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(ProductAvgResult_path + "version", 'w') as f:
        f.write(index_dt)
    with open(ProductAvgResult_path + index_dt + "/part-1", 'w') as f:
      f.write("product_name\t421144\t弹弹奇妙冒险\tAD_SEVEN_DAY_ROAS\t28821.37447\t0.2947\n")

    ProductAvgRetentionTarget_path = "../data_push/mcb_retention_target/"
    Path(ProductAvgRetentionTarget_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(ProductAvgRetentionTarget_path + "version", 'w') as f:
        f.write(index_dt)
    with open(ProductAvgRetentionTarget_path + index_dt + "/part-1", 'w') as f:
      f.write("ocpm_target\t麦可\t362625.483\t13835.0\t2607.0\t0.*****************\n")

    container_set = ContainerSet(False)
    container_set.enable_table(1) #account
    container_set.enable_table(2) #campaign
    container_set.init()
    campaign_id = **********
    container_set.add_account_record('''{
                          "id": "********",
                          "putStatus": "PUT_STATUS_OPEN"
                        }''')
    container_set.add_campaign_record('''{
                          "id": "**********",
                          "cap_bid": "0",
                          "constraint_cpa": "0",
                          "cap_roi_ratio": "0.0"
                        }''')
    container_set.dump_containers(index_path + index_dt,
                    '{"info":[{"file_name":"campaign", "proto_name":"Campaign"} ]}');

    ad_leaf = self.__init_service(ad_flow)

    bid_data_item = ad_leaf.add_item(campaign_id, table_name="McbBidDataTable")
    bid_data_item['product_roi_ratio'] = 0.*****************
    bid_data_item['product_cpa_bid'] = 28821.375
    bid_data_item['mcb_retention_target'] = 0.0
    bid_data_item['performance_cpa_bid'] = 0.0
    bid_data_item['performance_roi_ratio'] = 0.*****************
    bid_data_item['left_budget'] = 282424
    bid_data_item['retention_active'] = 0
    bid_data_item['today_cost'] = 706976
    bid_data_item['today_target_cost'] = 93685
    bid_data_item['bid_info.auto_roas'] = 0.1885615736246109
    bid_data_item['bid_info.auto_cpa_bid'] = 13984.**********
    bid_data_item['budget_info.last_locked_all_budget'] = 1000000
    bid_data_item['budget_info.locked_all_budget'] = 1000000
    bid_data_item['cpa_current_timestamp'] = 1669375133669169
    bid_data_item['cpa_coef'] = 3.20000004768371582
    bid_data_item["bid_coef"] = 1.*****************
    bid_data_item["cem_bid_coef"] = 0.0
    bid_data_item["predict_deep_conversion"] = 0.0
    bid_data_item["predict_conversion"] = 0.0
    bid_data_item["today_target_cpa_sum"] = 0.0
    bid_data_item["use_performance_flag"] = 0
    bid_data_item["today_origin_cost"] = 699230
    bid_data_item["today_performance_target_cost"] = 93685.00
    bid_data_item["today_origin_performance_target_cost"] = 65579.00
    bid_data_item["today_target_cost"] = 93685.0
    bid_data_item["today_cost"] = 706976.0
    bid_data_item["today_expect_cv"] = 254.8507232666015625
    bid_data_item["today_rl_expect_cv"] = 254.09440317674747689
    bid_data_item["current_ts"] = int(time.time())

    common_item = ad_leaf.add_item(campaign_id, table_name="McbAdCommonTable")
    common_item['campaign_id'] = campaign_id

    config_item = ad_leaf.add_item(campaign_id, table_name="McbConfigTable")
    config_item['daily_reset'] = 1.0
    config_item['advance_sec'] = 0.0
    config_item['fanstop_clear'] = 1.0
    config_item['cost_rate_threshold'] = 1.*****************
    config_item['reset_budget_coef'] = 1.0
    config_item['reset_bid_coef'] = 1.0
    config_item['reset_history'] = 0.0
    config_item['daily_init_reset_ratio'] = 0.8
    config_item['daily_init_reset_coef'] = 0.0

    base_item = ad_leaf.add_item(campaign_id, table_name="McbBaseInfoTable")
    base_item['account_id'] = ********
    base_item['campaign_id'] = campaign_id
    base_item['ocpc_action_type'] = 774
    base_item['product_name'] = "弹弹奇妙冒险"
    base_item['second_industry_id'] = 1222
    base_item['campaign_type'] = 2
    base_item['hosting_cpa_bid'] = 0.0
    base_item['create_source_type'] = 0
    base_item['mcb_level'] = 2

    ad_leaf.run("test_bid_data_update")
    for item in ad_leaf.get_items("McbBidDataTable"):
        self.assertEqual(item["bonus_price_ratio"], 1.0)
        self.assertEqual(item["product_roi_ratio"], 0.*****************)
        self.assertEqual(item["mcb_retention_target"], 0.0)
        self.assertEqual(item["budget_opt_info.cap_bid_type"], 2)
        self.assertEqual(item["bid_info.auto_roas"], 0.*****************)
        self.assertEqual(item["bid_info.auto_cpa_bid"], 13984.**********)
        self.assertEqual(item["bid_coef"], 1.*****************)
        #self.assertEqual(item["cem_bid_coef"], 1.*****************)
        self.assertEqual(item["cpa_coef"], 3.20000004768371582)
        self.assertEqual(item["predict_deep_conversion"], 0.0)
        self.assertEqual(item["predict_conversion"], 0.0)
        self.assertEqual(item["today_target_cpa_sum"], 0.0)
        self.assertEqual(item["use_performance_flag"], 0)
        self.assertEqual(item["today_origin_cost"], 699230)
        self.assertEqual(item["today_performance_target_cost"], 93685.00)
        self.assertEqual(item["today_origin_performance_target_cost"], 65579.00)
        self.assertEqual(item["today_target_cost"], 93685.0)
        self.assertEqual(item["today_cost"], 706976.0)
        self.assertEqual(item["today_expect_cv"], 254.8507232666015625)
        self.assertEqual(item["today_rl_expect_cv"], 254.09440317674747689)

class TestFlowFunc_update_reset1(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_bid_data_update(self):
    ad_flow = McbBudgetBidUpdateFlowTest(name="test_bid_data_update") \
              .bid_data_update_flow()
    last_hour_date_time = datetime.now() - timedelta(hours = 2)
    index_path = "../data_push/ad_collective_index_for_bid/"
    index_dt = last_hour_date_time.strftime('%Y-%m-%d_%H00')
    Path(index_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(index_path + "version", 'w') as f:
        f.write(index_dt)

    ProductAvgResult_path = "../data_push/mcb_initial_data/"
    Path(ProductAvgResult_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(ProductAvgResult_path + "version", 'w') as f:
        f.write(index_dt)
    with open(ProductAvgResult_path + index_dt + "/part-1", 'w') as f:
      f.write("product_name\t421144\t弹弹奇妙冒险\tAD_SEVEN_DAY_ROAS\t28821.37447\t0.2947\n")

    ProductAvgRetentionTarget_path = "../data_push/mcb_retention_target/"
    Path(ProductAvgRetentionTarget_path + index_dt).mkdir(parents=True, exist_ok=True)
    with open(ProductAvgRetentionTarget_path + "version", 'w') as f:
        f.write(index_dt)
    with open(ProductAvgRetentionTarget_path + index_dt + "/part-1", 'w') as f:
      f.write("ocpm_target\t麦可\t362625.483\t13835.0\t2607.0\t0.*****************\n")

    container_set = ContainerSet(False)
    container_set.enable_table(1) #account
    container_set.enable_table(2) #campaign
    container_set.init()
    campaign_id = **********
    container_set.add_account_record('''{
                          "id": "********",
                          "putStatus": "PUT_STATUS_OPEN"
                        }''')
    container_set.add_campaign_record('''{
                          "id": "**********",
                          "cap_bid": "0",
                          "constraint_cpa": "0",
                          "cap_roi_ratio": "0.0"
                        }''')
    container_set.dump_containers(index_path + index_dt,
                    '{"info":[{"file_name":"campaign", "proto_name":"Campaign"} ]}');

    ad_leaf = self.__init_service(ad_flow)

    bid_data_item = ad_leaf.add_item(campaign_id, table_name="McbBidDataTable")
    bid_data_item['product_roi_ratio'] = 0.*****************
    bid_data_item['product_cpa_bid'] = 28821.375
    bid_data_item['mcb_retention_target'] = 0.0
    bid_data_item['performance_cpa_bid'] = 0.0
    bid_data_item['performance_roi_ratio'] = 0.*****************
    bid_data_item['left_budget'] = 282424
    bid_data_item['retention_active'] = 0
    bid_data_item['today_cost'] = 706976
    bid_data_item['today_target_cost'] = 93685
    bid_data_item['bid_info.auto_roas'] = 0.1885615736246109
    bid_data_item['bid_info.auto_cpa_bid'] = 13984.**********
    bid_data_item['budget_info.last_locked_all_budget'] = 1000000
    bid_data_item['budget_info.locked_all_budget'] = 1000000
    bid_data_item['cpa_current_timestamp'] = 1669375133669169
    bid_data_item['cpa_coef'] = 3.20000004768371582
    bid_data_item["bid_coef"] = 1.*****************
    bid_data_item["cem_bid_coef"] = 0.0
    bid_data_item["predict_deep_conversion"] = 0.0
    bid_data_item["predict_conversion"] = 0.0
    bid_data_item["today_target_cpa_sum"] = 0.0
    bid_data_item["use_performance_flag"] = 0
    bid_data_item["today_origin_cost"] = 699230
    bid_data_item["today_performance_target_cost"] = 93685.00
    bid_data_item["today_origin_performance_target_cost"] = 65579.00
    bid_data_item["today_target_cost"] = 93685.0
    bid_data_item["today_cost"] = 706976.0
    bid_data_item["today_expect_cv"] = 254.8507232666015625
    bid_data_item["today_rl_expect_cv"] = 254.09440317674747689
    bid_data_item["current_ts"] = 100

    common_item = ad_leaf.add_item(campaign_id, table_name="McbAdCommonTable")
    common_item['campaign_id'] = campaign_id

    config_item = ad_leaf.add_item(campaign_id, table_name="McbConfigTable")
    config_item['daily_reset'] = 1.0
    config_item['advance_sec'] = 3.0
    config_item['fanstop_clear'] = 0.0
    config_item['cost_rate_threshold'] = 1.*****************
    config_item['reset_budget_coef'] = 1.0
    config_item['reset_bid_coef'] = 1.0
    config_item['reset_history'] = 0.0
    config_item['daily_init_reset_ratio'] = 0.8
    config_item['daily_init_reset_coef'] = 0.0

    base_item = ad_leaf.add_item(campaign_id, table_name="McbBaseInfoTable")
    base_item['account_id'] = ********
    base_item['campaign_id'] = campaign_id
    base_item['ocpc_action_type'] = 774
    base_item['product_name'] = "弹弹奇妙冒险"
    base_item['second_industry_id'] = 1222
    base_item['campaign_type'] = 2
    base_item['hosting_cpa_bid'] = 0.0
    base_item['create_source_type'] = 0
    base_item['mcb_level'] = 2

    ad_leaf.run("test_bid_data_update")
    for item in ad_leaf.get_items("McbBidDataTable"):
        self.assertEqual(item["bonus_price_ratio"], 1.0)
        self.assertEqual(item["product_roi_ratio"], 0.*****************)
        self.assertEqual(item["mcb_retention_target"], 0.0)
        self.assertEqual(item["budget_opt_info.cap_bid_type"], 2)
        self.assertEqual(item["bid_info.auto_roas"], 0.*****************)
        self.assertEqual(item["bid_info.auto_cpa_bid"], 13984.**********)
        self.assertEqual(item["bid_coef"], 1.*****************)
        #self.assertEqual(item["cem_bid_coef"], 1.*****************)
        self.assertEqual(item["cpa_coef"], 2.5600000381469727)
        self.assertEqual(item["predict_deep_conversion"], 0.0)
        self.assertEqual(item["predict_conversion"], 0.0)
        self.assertEqual(item["today_target_cpa_sum"], 0.0)
        self.assertEqual(item["use_performance_flag"], 0)
        self.assertEqual(item["today_origin_cost"], 0.0)
        self.assertEqual(item["today_performance_target_cost"], 0.0)
        self.assertEqual(item["today_origin_performance_target_cost"], 0.0)
        self.assertEqual(item["today_target_cost"], 0.0)
        self.assertEqual(item["today_cost"], 0.0)
        self.assertEqual(item["today_expect_cv"], 0.0)
        self.assertEqual(item["today_rl_expect_cv"], 0.0)

class TestFlowFunc_InnerLoopMsgAdmitMixer(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_inner_loop_msg_admit_mixer(self):
    ad_flow = BidServerFlow(name="test_inner_loop_msg_admit_mixer") \
               .inner_loop_msg_admit_mixer(
                  item_table = "TriggerTable",
                )

    ad_leaf = self.__init_service(ad_flow)

    ad_log_item = ad_leaf.add_item(1234321, table_name="TriggerTable")
    ad_log_item['is_for_report_engine'] = 1
    ad_log_item['log_process_timestamp'] = *************
    ad_log_item['delivery_timestamp'] = *************
    ad_log_item['log_process_timestamp'] = *************
    ad_log_item['is_spam_order'] = 0
    ad_log_item['unit_id'] = 1234321
    ad_log_item['account_id'] = ********
    ad_log_item['bid_server_group_tag'] = "adlogfull_ocpm"
    ad_log_item['campaign_type'] = "LIVE_STREAM_PROMOTE"
    ad_log_item['ocpc_action_type'] = "AD_ITEM_CLICK"
    ad_log_item['promotion_type'] = ""
    ad_log_item['speed_type'] = 2

    ad_leaf.run("test_inner_loop_msg_admit_mixer")

    self.assertEqual(len(ad_leaf.get_items("TriggerTable")), 1)
    for item in ad_leaf.get_items("TriggerTable"):
      self.assertEqual(item.item_key, 1234321)
      self.assertEqual(item["is_valid_message"], 0)

class TestFlowFunc_OuterOcpmTableFetchMixer(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_outer_ocpm_table_fetch_mixer(self):
    os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
    os.environ["KWS_SERVICE_STAGE"] = "CANDIDATE"
    ad_flow = BidServerFlow(name="test_outer_ocpm_table_fetch_mixer") \
               .outer_ocpm_table_fetch_mixer(
                  item_table = 'TriggerTable',
                  candidate_cluster_name = 'BidServerGraphTest',
                  cluster_name = 'BidServerGraph',
                  charge_tag_column_name = 'charge_tag',
                  redis_key_column_name = 'redis_key',
                  local_cache_key_column_name = 'local_cache_key',
                  group_tag = {"item_table" : "TriggerTable", "column" : "group_tag"},
                  unit_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
                  account_id = {"item_table" : "TriggerTable", "column" : "account_id"},
                  campaign_type = {"item_table" : "TriggerTable", "column" : "campaign_type"},
                  ocpx_action_type = {"item_table" : "TriggerTable", "column" : "ocpx_action_type"},
                  deep_conversion_type = {"item_table" : "TriggerTable", "column" : "deep_conversion_type"},
                  soft_hard_code = {"item_table" : "TriggerTable", "column" : "soft_hard_code"},
                  placement_type_code = {"item_table" : "TriggerTable", "column" : "placement_type_code"},
                  mcb_type = {"item_table" : "TriggerTable", "column" : "mcb_type"},
                  charge_tag = {"item_table" : "TriggerTable", "column" : "charge_tag"},
                  mcb_level_column_name = 'mcb_level',
                  output_tables = [
                      dict(key_column="account_id",
                          table_name="AccountTable",
                          redis_prefix="ocpm_account_table",
                          output_table_key_column="account_table_key",
                          need_charge_tag_key=False,
                          mcb_level = 5),
                      dict(key_column="account_id",
                          table_name="AccountTagTable",
                          redis_prefix="ocpm_account_tag_table",
                          output_table_key_column="account_tag_table_key",
                          need_charge_tag_key=True,
                          mcb_level = 5),
                      dict(key_column="unit_id",
                          table_name="UnitTable",
                          redis_prefix="ocpm_unit_table",
                          output_table_key_column="unit_table_key",
                          need_charge_tag_key=False,
                          mcb_level = 7),
                      dict(key_column="unit_id",
                          table_name="UnitTagTable",
                          redis_prefix="ocpm_unit_tag_table",
                          output_table_key_column="unit_tag_table_key",
                          need_charge_tag_key=True,
                          mcb_level = 7)
                  ]
                )

    ad_leaf = self.__init_service(ad_flow)

    trigger_item = ad_leaf.add_item(1234321, table_name="TriggerTable")
    trigger_item['account_id'] = 111
    trigger_item['unit_id'] = 222
    trigger_item['placement_type_code'] = 1
    trigger_item['group_tag'] = 'abc'
    trigger_item['campaign_type'] = 3
    trigger_item['ocpx_action_type'] = 11
    trigger_item['deep_conversion_type'] = 1
    trigger_item['soft_hard_code'] = 1
    trigger_item['placement_type_code'] = 1

    ad_leaf.run("test_outer_ocpm_table_fetch_mixer")

    self.assertEqual(len(ad_leaf.get_items("UnitTable")), 1)
    self.assertEqual(len(ad_leaf.get_items("UnitTagTable")), 1)
    self.assertEqual(len(ad_leaf.get_items("AccountTable")), 1)
    self.assertEqual(len(ad_leaf.get_items("AccountTagTable")), 1)
    for item in ad_leaf.get_items("UnitTable"):
     if item.item_key == 222:
        self.assertEqual(item["account_table_key"], 111)
        self.assertEqual(item["unit_table_key"], 111)

class TestFlowFunc_OcpmAccountCpaCoefOptimalMixer(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_ocpm_account_cpa_coef_optimal_mixer(self):
    os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
    ad_flow = BidServerFlow(name="test_ocpm_account_cpa_coef_optimal_mixer") \
       .ocpm_account_cpa_coef_optimal_mixer(
          no_check=True,
          item_table = "AccountTagTable",
          unit_tag_table_name = "UnitTagTable",
          test_skip_load_p2p = True,
          # exp_config
          cpa_coef_up_range = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.cpa_coef_up_range"),
          cpa_coef_low_range = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.cpa_coef_low_range"),
          cpa_coef_upper = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.cpa_coef_upper"),
          cpa_coef_lower = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.cpa_coef_lower"),
          d_out_decay = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.d_out_decay"),
          auto_adjust_ratio = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.auto_adjust_ratio"),
          auto_adjust_target_factor = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.auto_adjust_target_factor"),
          backflow_skip_bounder = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.backflow_skip_bounder"),
          base_cost_cost_thr = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.base_cost_cost_thr"),
          backflow_window_sec = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.backflow_window_sec"),
          backflow_bound_ratio_upper = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.backflow_bound_ratio_upper"),
          backflow_bound_ratio_lower = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.backflow_bound_ratio_lower"),
          smooth_upper = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.smooth_upper"),
          smooth_lower = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.smooth_lower"),
          min_future_ratio = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.min_future_ratio"),
          max_future_ratio = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.max_future_ratio"),
          max_step_num = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.max_step_num"),
          optimal_step = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.optimal_step"),
          pow_coef = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.pow_coef"),
          enable_account_all = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.enable_account_all"),
          smooth_max = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.smooth_max"),
          yesterday_smooth_coef = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.yesterday_smooth_coef"),
          enable_roas_all_target_cost = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.enable_roas_all_target_cost"),
          roi_smooth_ratio = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.roi_smooth_ratio"),
          today_stable_cost_ratio_v2 = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.today_stable_cost_ratio_v2"),
          window_stable_cost_ratio = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.window_stable_cost_ratio"),
          min_cost_cpa_bid = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.min_cost_cpa_bid"),
          account_min_cost = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.account_min_cost"),
          max_smooth_cost = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.max_smooth_cost"),
          cpa_window_step_bound = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.cpa_window_step_bound"),
          stable_max_cpa_ratio = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.stable_max_cpa_ratio"),
          universe_optimal_fine_tuning = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.universe_optimal_fine_tuning"),
          decay_when_exceed = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.decay_when_exceed"),
          decay_when_not_exceed = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.decay_when_not_exceed"),
          history_decay = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.history_decay"),
          optim_search_lower_step = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.optim_search_lower_step"),
          enable_stability_mix = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.enable_stability_mix"),
          universe_enable_guarantee_min_cost = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.universe_enable_guarantee_min_cost"),
          enable_guarantee_min_cost = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.enable_guarantee_min_cost"),
          fix_account_expect_before_use_base = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.fix_account_expect_before_use_base"),
          optimal_kd_out = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.optimal_kd_out"),
          universe_operation_boost_cpa_price = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.universe_operation_boost_cpa_price"),
          use_backflow = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.use_backflow"),
          window_cpa_ratio_lower_range = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.window_cpa_ratio_lower_range"),
          enable_backflow_roas_all_target_cost = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.enable_backflow_roas_all_target_cost"),
          use_backflow_account_all = dict(item_table = "AccountTagTable", column = "exp_conf.account_ocpm.use_backflow_account_all"),
          # account 表
          product_name = dict(item_table = "AccountTable", column = "product_name"),
          ocpx_action_type = dict(item_table = "AccountTable", column = "ocpx_action_type"),
          is_universe_opt = dict(item_table = "AccountTable", column = "is_universe_opt"),
          campaign_type = dict(item_table = "AccountTable", column = "campaign_type"),
          deep_conversion_type = dict(item_table = "AccountTable", column = "deep_conversion_type"),
          second_industry_id = dict(item_table = "AccountTable", column = "second_industry_id"),
          # account tag 表
          unit_id_attr = dict(item_table = "AccountTagTable", column = "unit_table_key"),
          account_id_attr = dict(item_table = "AccountTagTable", column = "account_table_key"),
          group_tag = dict(item_table = "AccountTagTable", column = "group_tag"),
          cpa_coef = dict(item_table = "AccountTagTable", column = "s2.cpa_coef"),
          target_factor = dict(item_table = "AccountTagTable", column = "s2.target_factor"),
          auto_adjust_info_today_auto_adjust_cost = dict(item_table = "AccountTagTable", column = "s2.auto_adjust_info.today_auto_adjust_cost"),
          today_cost = dict(item_table = "AccountTagTable", column = "s2.today_cost"),
          account_bidding_info_placement_type = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.placement_type"),
          today_origin_cost = dict(item_table = "AccountTagTable", column = "s2.today_origin_cost"),
          today_target_cost = dict(item_table = "AccountTagTable", column = "s2.today_target_cost"),
          mcb_level = dict(item_table = "AccountTagTable", column = "mcb_level"),
          yesterday_cv = dict(item_table = "AccountTagTable", column = "s2.yesterday_cv"),
          today_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.today_cost_unit_sum"),
          today_origin_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.today_origin_cost_unit_sum"),
          today_price_after_billing_separate_unit_sum = dict(item_table = "AccountTagTable", column = "s2.today_price_after_billing_separate_unit_sum"),
          today_target_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.today_target_cost_unit_sum"),
          today_target_cpa_unit_sum = dict(item_table = "AccountTagTable", column = "s2.today_target_cpa_unit_sum"),
          account_bidding_info_window_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_cost_unit_sum"),
          account_bidding_info_base_window_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.base_window_cost_unit_sum"),
          account_bidding_info_window_before_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_before_cost_unit_sum"),
          account_bidding_info_base_window_before_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.base_window_before_cost_unit_sum"),
          account_bidding_info_window_after_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_after_cost_unit_sum"),
          account_bidding_info_window_target_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_target_cost_unit_sum"),
          account_bidding_info_window_deep_target_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_deep_target_cost_unit_sum"),
          account_bidding_info_today_deep_target_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.today_deep_target_cost_unit_sum"),
          account_bidding_info_window_cpa_coef_unit_sum = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_cpa_coef_unit_sum"),
          account_bidding_info_target_cpa_cost_sum = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.target_cpa_cost_sum"),
          account_bidding_info_window_expect_cv = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_expect_cv"),
          account_bidding_info_window_expect_target_cost = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_expect_target_cost"),
          account_bidding_info_today_expect_cv = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.today_expect_cv"),
          account_bidding_info_today_expect_target_cost = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.today_expect_target_cost"),
          account_bidding_info_window_deep_expect_target_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_deep_expect_target_cost_unit_sum"),
          account_bidding_info_today_deep_expect_target_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.today_deep_expect_target_cost_unit_sum"),
          account_bidding_info_base_today_origin_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.base_today_origin_cost_unit_sum"),
          account_bidding_info_today_target_cost_unit_sum_attr = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.today_target_cost_unit_sum_attr"),
          account_bidding_info_roas_24h_all_today_target_cost_unit_sum = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.roas_24h_all_today_target_cost_unit_sum"),
          account_bidding_info_roas_24h_all_today_target_cost_unit_sum_attr = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.roas_24h_all_today_target_cost_unit_sum_attr"),
          account_bidding_info_today_backflow_delta_raw = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.today_backflow_delta_raw"),
          account_bidding_info_window_backflow_delta_raw = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_backflow_delta_raw"),
          account_bidding_info_today_backflow_delta = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.today_backflow_delta"),
          account_bidding_info_today_backflow_label = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.today_backflow_label"),
          account_bidding_info_window_backflow_delta = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_backflow_delta"),
          account_bidding_info_window_backflow_label = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_backflow_label"),

          account_bidding_info_today_backflow_all_delta = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.today_backflow_all_delta"),
          account_bidding_info_today_backflow_all_label = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.backflow_all_label"),
          account_bidding_info_window_backflow_all_delta = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_backflow_all_delta"),
          account_bidding_info_window_backflow_all_label = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_backflow_all_label"),

          window_data_cpa_coef = dict(item_table = "AccountTagTable", column = "s2.window_data.cpa_coef"),
          window_data_cost = dict(item_table = "AccountTagTable", column = "s2.window_data.cost"),
          window_data_target_cost = dict(item_table = "AccountTagTable", column = "s2.window_data.target_cost"),
          account_bidding_info_today_deep_target_cost = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.today_deep_target_cost"),
          window_data_deep_target_cost = dict(item_table = "AccountTagTable", column = "s2.window_data.deep_target_cost"),
          account_bidding_info_today_flow_ratio = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.today_flow_ratio"),
          account_bidding_info_window_flow_ratio = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.window_flow_ratio"),

          account_bidding_info_roas_24h_all_today_target_cost = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.roas_24h_all_today_target_cost"),
          account_bidding_info_ad_queue_type = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.ad_queue_type"),
          account_bidding_info_yesterday_cost = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.yesterday_cost"),
          account_bidding_info_cpa_window = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.cpa_window"),
          account_bidding_info_stable_cost_min_cpa_coef_window = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.stable_cost_min_cpa_coef_window"),
          account_bidding_info_account_min_cost_cpa_coef_window_smooth = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.account_min_cost_cpa_coef_window_smooth"),
          account_bidding_info_avg_cpa_coef_window = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.avg_cpa_coef_window"),
          account_bidding_info_avg_target_cpa_window = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.avg_target_cpa_window"),
          account_bidding_info_expect_stable_remain_cost = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.expect_stable_remain_cost"),
          account_bidding_info_remain_flow_ratio = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.remain_flow_ratio"),
          account_bidding_info_today_stable_cost = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.today_stable_cost"),
          account_bidding_info_origin_best_cpa_coef = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.origin_best_cpa_coef"),
          account_bidding_info_stable_cpa_max_cpa_coef_whole_day = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.stable_cpa_max_cpa_coef_whole_day"),
          account_bidding_info_stable_min_cpa_coef_window = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.stable_min_cpa_coef_window"),
          account_bidding_info_stable_cpa_ratio = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.stable_cpa_ratio"),
          account_bidding_info_expect_cost = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.expect_cost"),
          account_bidding_info_expect_target_cost = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.expect_target_cost"),
          account_bidding_info_expect_cpa_ratio = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.expect_cpa_ratio"),
          account_bidding_info_expect_before = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.expect_before"),
          account_bidding_info_roas_24h_all_today_target_cost_attr = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.roas_24h_all_today_target_cost_attr"),
          today_price_after_billing_separate = dict(item_table = "AccountTagTable", column = "s2.today_price_after_billing_separate"),
          window_data_origin_cost = dict(item_table = "AccountTagTable", column = "s2.window_data.origin_cost"),
          cpa_pid_d_out = dict(item_table = "AccountTagTable", column = "s2.cpa_pid.d_out"),
          account_bidding_info_set_rage_flag = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.set_rage_flag"),
          account_bidding_info_roas_24h_today_backflow_all_delta_raw = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.roas_24h_today_backflow_all_delta_raw"),
          account_bidding_info_roas_24h_today_backflow_all_delta = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.roas_24h_today_backflow_all_delta"),
          account_bidding_info_roas_24h_today_backflow_all_label = dict(item_table = "AccountTagTable", column = "s2.account_bidding_info.roas_24h_today_backflow_all_label"),
          # unit tag 表
          unit_tag_ocpx_action_type = dict(item_table = "UnitTagTable", column = "ocpx_action_type"),
          unit_tag_unit_id = dict(item_table = "UnitTagTable", column = "unit_id"),
          unit_tag_valid = dict(item_table = "UnitTagTable", column = "valid"),
          unit_tag_today_cost = dict(item_table = "UnitTagTable", column = "s2.today_cost"),
          unit_tag_today_origin_cost = dict(item_table = "UnitTagTable", column = "s2.today_origin_cost"),
          unit_tag_today_price_after_billing_separate = dict(item_table = "UnitTagTable", column = "s2.today_price_after_billing_separate"),
          unit_tag_today_target_cost = dict(item_table = "UnitTagTable", column = "s2.today_target_cost"),
          unit_tag_today_target_cost_attr = dict(item_table = "UnitTagTable", column = "s2.today_target_cost_attr"),
          unit_tag_roas_24h_all_today_target_cost = dict(item_table = "UnitTagTable", column = "s2.roas_24h_all_today_target_cost"),
          unit_tag_roas_24h_all_today_target_cost_attr = dict(item_table = "UnitTagTable", column = "s2.roas_24h_all_today_target_cost_attr"),
          unit_tag_target_cpa_sum = dict(item_table = "UnitTagTable", column = "s2.target_cpa_sum"),
          unit_tag_window_data_cost = dict(item_table = "UnitTagTable", column = "s2.window_data.cost"),
          unit_tag_window_data_target_cost = dict(item_table = "UnitTagTable", column = "s2.window_data.target_cost"),
          unit_tag_window_data_deep_target_cost = dict(item_table = "UnitTagTable", column = "s2.window_data.deep_target_cost"),
          unit_tag_account_bidding_info_today_deep_target_cost = dict(item_table = "UnitTagTable", column = "s2.account_bidding_info.today_deep_target_cost"),
          unit_tag_window_flow_ratio = dict(item_table = "UnitTagTable", column = "s2.account_bidding_info.window_flow_ratio"),
          unit_tag_window_before_cost = dict(item_table = "UnitTagTable", column = "s2.window_data.origin_cost"),
          unit_tag_today_flow_ratio = dict(item_table = "UnitTagTable", column = "s2.account_bidding_info.today_flow_ratio"),
          unit_tag_window_data_price_after = dict(item_table = "UnitTagTable", column = "s2.window_data.price_after"),
          unit_tag_window_data_cpa_coef_cost_sum = dict(item_table = "UnitTagTable", column = "s2.window_data.cpa_coef_cost_sum"),
          unit_tag_window_data_target_cpa_sum = dict(item_table = "UnitTagTable", column = "s2.window_data.target_cpa_sum"),
          unit_tag_window_data_predict_conversion = dict(item_table = "UnitTagTable", column = "s2.window_data.predict_conversion"),
          unit_tag_window_data_expect_target_cost = dict(item_table = "UnitTagTable", column = "s2.window_data.expect_target_cost"),
          unit_tag_window_data_deep_expect_target_cost = dict(item_table = "UnitTagTable", column = "s2.window_data.deep_expect_target_cost"),
          unit_tag_predict_conversion = dict(item_table = "UnitTagTable", column = "s2.predict_conversion"),
          unit_tag_account_bidding_info_today_expect_target_cost = dict(item_table = "UnitTagTable", column = "s2.account_bidding_info.today_expect_target_cost"),
          unit_tag_account_bidding_info_today_deep_expect_target_cost = dict(item_table = "UnitTagTable", column = "s2.account_bidding_info.today_deep_expect_target_cost"),
          unit_tag_log_process_timestamp = dict(item_table = "UnitTagTable", column = "s2.log_process_timestamp"),
          unit_tag_backflow_data_llsid_map = dict(item_table = "UnitTagTable", column = "backflow_data_llsid_map"),
          # unit backflow 表
          backflow_moss_map = dict(item_table = "BackFlowUnitTable", column = "moss_map")
        )
    ad_leaf = self.__init_service(ad_flow)

    trigger_item = ad_leaf.add_item(123456, table_name="AccountTagTable")
    trigger_item['unit_id'] = 111
    trigger_item['group_tag'] = 1
    trigger_item['moss_map_key_list'] = [12,13,14]
    trigger_item['moss_map_value_list'] = ["12", "13"]


    ad_leaf.run("test_ocpm_account_cpa_coef_optimal_mixer")

    self.assertEqual(len(ad_leaf.get_items("AccountTagTable")), 1)

class TestFlowFunc_BackFlowFetchDataMixer(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_back_flow_fetch_data_mixer(self):
    os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
    ad_flow = BidServerFlow(name="test_back_flow_fetch_data_mixer") \
               .back_flow_update_cache_mixer(
                  item_table = "TriggerTable",
                  unit_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
                  moss_map_key_list = {"item_table" : "TriggerTable", "column" : "moss_map_key_list"},
                  moss_map_value_list = {"item_table" : "TriggerTable", "column" : "moss_map_value_list"}
                ) \
                .back_flow_fetch_data_mixer(
                  item_table = "UnitTable",
                  group_tag = {"item_table" : "UnitTable", "column" : "back_flow_group_tag"},
                  unit_id = {"item_table" : "UnitTable", "column" : "unit_id"},
                  output_table_name = "BackFlowUnitTag",
                  output_map_column = "moss_map"
                )
    ad_leaf = self.__init_service(ad_flow)

    trigger_item = ad_leaf.add_item(123456, table_name="TriggerTable")
    trigger_item['unit_id'] = 111
    trigger_item['group_tag'] = 1
    trigger_item['moss_map_key_list'] = [12,13,14]
    trigger_item['moss_map_value_list'] = ["12", "13"]

    unit_item = ad_leaf.add_item(123456, table_name="UnitTable")
    unit_item['unit_id'] = 123456
    unit_item['back_flow_group_tag'] = 1

    ad_leaf.run("test_back_flow_fetch_data_mixer")

    self.assertEqual(len(ad_leaf.get_items("BackFlowUnitTag")), 1)


class TestFlowFunc_OcpmAccountCacheSaveMixer(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_ocpm_account_cache_save_mixer(self):
    os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
    ad_flow = BidServerFlow(name="test_ocpm_account_cache_save_mixer") \
               .ocpm_account_cache_save_mixer(
                  item_table = "AccountTagTable",
                  save_all_columns_ = True,
                  local_cache_key_column = "local_cache_key"
                )
    ad_leaf = self.__init_service(ad_flow)

    trigger_item = ad_leaf.add_item(123456, table_name="AccountTagTable")
    trigger_item['unit_id'] = 111
    trigger_item['group_tag'] = 1
    trigger_item['local_cache_key'] = 111111

    ad_leaf.run("test_ocpm_account_cache_save_mixer")

    self.assertEqual(len(ad_leaf.get_items("AccountTagTable")), 1)

class TestFlowFunc_InnerLoopOcpmCalcBidPrepareMixer(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_inner_loop_ocpm_calc_bid_prepare_mixer(self):
    ad_flow = BidServerFlow(name="test_inner_loop_ocpm_calc_bid_prepare_mixer") \
               .inner_loop_ocpm_calc_bid_prepare_mixer(
                  item_table = "UnitTagTable",
                )

    ad_leaf = self.__init_service(ad_flow)

    unit_tag_item = ad_leaf.add_item(1234321, table_name="UnitTagTable")
    unit_tag_item['ocpc_action_type'] = "EVENT_ORDER_PAIED"
    unit_tag_item['promotion_type'] = "SPECIALTY_PROMOTION"
    unit_tag_item['group_tag'] = "adlogfull_ocpm"
    unit_tag_item['author_id'] = 123
    unit_tag_item['unit_id'] = 1234321
    unit_tag_item['campaign_type'] = "LIVE_STREAM_PROMOTE"
    unit_tag_item['item_type'] = "ITEM_LIVE"
    unit_tag_item['speed_type'] = 2
    unit_tag_item['util_vals_is_roas'] = 1
    unit_tag_item['relax_roi_ratio'] = 1.5
    unit_tag_item['relax_cpa_bid'] = 5.5
    unit_tag_item['cost'] = 200.1
    unit_tag_item['target_cost'] = 200.0
    unit_tag_item['last_update_cost'] = 50.5
    unit_tag_item['cpa_bid'] = 5.5
    unit_tag_item['conv_num'] = 1
    unit_tag_item['util_vals_ab_exp_ratio'] = 0.1
    unit_tag_item['first_delivery_timestamp_ms'] = 1677912736000
    unit_tag_item['last_delivery_timestamp_ms'] = 1677912736001
    unit_tag_item['dry_up_base_value'] = 1.0
    unit_tag_item['auto_roi_ratio'] = 1.6
    unit_tag_item['auto_cpa_bid'] = 6.0
    unit_tag_item['price_ratio_context_batch_price_ratio_mean'] = 1.0
    unit_tag_item['old_is_out_of_budget'] = 1
    unit_tag_item['msg_price_ratio'] = 1.0
    unit_tag_item['msg_conv_num'] = 1
    unit_tag_item['msg_cost'] = 1.1
    unit_tag_item['msg_target_cost'] = 2.2
    unit_tag_item['has_bid_state_info_ptr'] = 1
    unit_tag_item['bid_state_info_explore_time_period'] = 12
    unit_tag_item['bid_state_info_explore_budget_start_time'] = 1677912736000
    unit_tag_item['bid_state_info_explore_budget_status'] = 1
    unit_tag_item['bid_state_info_explore_budget'] = 100
    unit_tag_item['bid_state_info_is_live'] = 1
    unit_tag_item['bid_state_info_ad_status'] = 1
    unit_tag_item['bid_state_info_advertisable'] = 1
    unit_tag_item['bid_state_info_is_status_open'] = 1
    unit_tag_item['bid_state_info_online'] = 1
    unit_tag_item['bid_state_info_left_budget'] = 1000
    unit_tag_item['util_vals_is_explore'] = 0
    unit_tag_item['last_sync_result_timestamp'] = 1677912726000
    unit_tag_item['last_update_adjust_timestamp'] = 1677912726000
    unit_tag_item['util_vals_is_target_modify'] = 0
    unit_tag_item['util_vals_is_skip_update'] = 0
    unit_tag_item['util_vals_is_cold_start'] = 0
    unit_tag_item['is_cold_start'] = 1

    ad_leaf.run("test_inner_loop_ocpm_calc_bid_prepare_mixer")

    self.assertEqual(len(ad_leaf.get_items("UnitTagTable")), 1)
    for item in ad_leaf.get_items("UnitTagTable"):
      self.assertEqual(item.item_key, 1234321)
      self.assertEqual(item["util_vals_enable_cost_prior_algo"], 1)
      self.assertEqual(item["util_vals_pacing_type"], 2)
      self.assertEqual(item["util_vals_enable_linear_pacing_grid_correction"], 1)
      self.assertEqual(item["util_vals_enable_linear_after_cold_start_no_conv_drop"], 1)
      self.assertEqual(item["util_vals_enable_linear_adaptive_update_time"], 1)
      self.assertEqual(item["util_vals_enable_linear_pacing_cold_start_low_bound"], 1)
      self.assertEqual(item["util_vals_reset_pacing"], 1)
      self.assertEqual(item["util_vals_disable_day_reset_exp"], 1)
      self.assertEqual(item["util_vals_hard_upper_bound_auto_roas"], 15.0)
      self.assertEqual(item["adjust_auto_value_rate"], 0.****************)
      self.assertEqual(item["util_vals_hard_lower_bound_auto_roas"], 0.30000000000000004)
      self.assertEqual(item["util_vals_hard_upper_bound_auto_cpa_bid"], 0.0)
      self.assertEqual(item["util_vals_hard_lower_bound_auto_cpa_bid"], 0.0)

class TestFlowFunc_InnerLoopOcpmCalcPacingRateNormalMixer(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_inner_loop_ocpm_calc_pacing_rate_normal_mixer(self):
    ad_flow = BidServerFlow(name="test_inner_loop_ocpm_calc_pacing_rate_normal_mixer") \
               .inner_loop_ocpm_calc_pacing_rate_normal_mixer(
                  item_table = "UnitTagTable",
                )

    ad_leaf = self.__init_service(ad_flow)

    unit_tag_item = ad_leaf.add_item(1234321, table_name="UnitTagTable")
    unit_tag_item['ocpc_action_type'] = "EVENT_ORDER_PAIED"
    unit_tag_item['promotion_type'] = "SPECIALTY_PROMOTION"
    unit_tag_item['group_tag'] = "adlogfull_ocpm"
    unit_tag_item['author_id'] = 123
    unit_tag_item['unit_id'] = 1234321
    unit_tag_item['account_id'] = ********
    unit_tag_item['campaign_type'] = "LIVE_STREAM_PROMOTE"
    unit_tag_item['util_vals_is_roas'] = 0
    unit_tag_item['relax_roi_ratio'] = 1.5
    unit_tag_item['relax_cpa_bid'] = 5.5
    unit_tag_item['rt_cost_speed'] = 1.5
    unit_tag_item['util_vals_target_cost_speed'] = 2.0
    unit_tag_item['adjust_auto_value_rate'] = 0.****************
    unit_tag_item['util_vals_p_weight'] = 1.0
    unit_tag_item['util_vals_i_weight'] = 1.0
    unit_tag_item['util_vals_d_weight'] = 1.0
    unit_tag_item['util_vals_is_update_adjust'] = 1
    unit_tag_item['util_vals_is_acc_explore_bid'] = 0
    unit_tag_item['util_vals_is_ocpm_bid_process'] = 1
    unit_tag_item['bid_state_info_budget'] = 1000.0
    unit_tag_item['cost'] = 100.0
    unit_tag_item['cpa_bid'] = 5.0
    unit_tag_item['has_bid_state_info_ptr'] = 1
    unit_tag_item['target_cost'] = 200.0
    unit_tag_item['util_vals_enable_cost_prior_algo'] = 1
    unit_tag_item['util_vals_prior_cost'] = 10.0
    unit_tag_item['util_vals_enable_linear_pacing_grid_correction'] = 1
    unit_tag_item['conv_ratio_adjust_context_pred_conv'] = 6
    unit_tag_item['conv_ratio_adjust_context_real_conv'] = 5
    unit_tag_item['util_vals_hard_upper_bound_auto_cpa_bid'] = 100.0
    unit_tag_item['util_vals_hard_lower_bound_auto_cpa_bid'] = 1.0
    unit_tag_item['bid_context_key'] = "1234321_adlogfull_ocpm"

    ad_leaf.run("test_inner_loop_ocpm_calc_pacing_rate_normal_mixer")

    self.assertEqual(len(ad_leaf.get_items("UnitTagTable")), 1)
    for item in ad_leaf.get_items("UnitTagTable"):
      self.assertEqual(item.item_key, 1234321)
      self.assertEqual(item["adjust_auto_value_rate"], 0.****************)
      self.assertEqual(item["util_vals_new_adjust_auto_value_rate"], 1.9)

class TestFlowFunc_InnerLoopOcpmCalcPacingRateAccMixer(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_inner_loop_ocpm_calc_pacing_rate_acc_mixer(self):
    ad_flow = BidServerFlow(name="test_inner_loop_ocpm_calc_pacing_rate_acc_mixer") \
               .inner_loop_ocpm_calc_pacing_rate_acc_mixer(
                  item_table = "UnitTagTable",
                )

    ad_leaf = self.__init_service(ad_flow)

    unit_tag_item = ad_leaf.add_item(1234321, table_name="UnitTagTable")
    unit_tag_item['ocpc_action_type'] = "EVENT_ORDER_PAIED"
    unit_tag_item['promotion_type'] = "SPECIALTY_PROMOTION"
    unit_tag_item['group_tag'] = "adlogfull_ocpm"
    unit_tag_item['author_id'] = 123
    unit_tag_item['unit_id'] = 1234321
    unit_tag_item['campaign_type'] = "LIVE_STREAM_PROMOTE"
    unit_tag_item['util_vals_is_roas'] = 0
    unit_tag_item['relax_roi_ratio'] = 1.5
    unit_tag_item['relax_cpa_bid'] = 5.5
    unit_tag_item['rt_cost_speed'] = 1.5
    unit_tag_item['util_vals_target_cost_speed'] = 2.0
    unit_tag_item['adjust_auto_value_rate'] = 0.****************
    unit_tag_item['util_vals_p_weight'] = 1.0
    unit_tag_item['util_vals_i_weight'] = 1.0
    unit_tag_item['util_vals_d_weight'] = 1.0
    unit_tag_item['util_vals_is_update_adjust'] = 1
    unit_tag_item['util_vals_is_acc_explore_bid'] = 1

    ad_leaf.run("test_inner_loop_ocpm_calc_pacing_rate_acc_mixer")

    self.assertEqual(len(ad_leaf.get_items("UnitTagTable")), 1)
    for item in ad_leaf.get_items("UnitTagTable"):
      self.assertEqual(item.item_key, 1234321)
      self.assertEqual(item["adjust_auto_value_rate"], 0.****************)
      self.assertEqual(item["util_vals_new_adjust_auto_value_rate"], 0.6717244940980733)

class TestFlowFunc_MsgFilterMixer(unittest.TestCase):
  __service = LeafService(kess_name="ad-dragon-bid-server")
 ## __service = OfflineRunner("ad-dragon-bid-server")

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_msg_filter_mixer(self):
    ad_flow = BidServerFlow(name="test_msg_filter_mixer") \
               .msg_filter_mixer(
                  item_table = "TriggerTable",
                )

    ad_leaf = self.__init_service(ad_flow)

    ad_log_item = ad_leaf.add_item(1234321, table_name="TriggerTable")
    ad_log_item['is_valid_message'] = 1
    ad_log_item_two = ad_leaf.add_item(1234322, table_name="TriggerTable")
    ad_log_item_two['is_valid_message'] = 0

    ad_leaf.run("test_msg_filter_mixer")

    self.assertEqual(len(ad_leaf.get_items("TriggerTable")), 1)

