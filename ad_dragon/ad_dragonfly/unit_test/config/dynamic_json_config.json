{"dump_conf": {"table_dump_configs": [{"table_name": "ad_dsp_account_balance", "type": "ACCOUNT_BALANCE"}, {"table_name": "ad_dsp_account_daily_charge", "type": "ACCOUNT_DAY_CHARGE"}, {"table_name": "ad_dsp_campaign_daily_charge", "type": "CAMPAIGN_DAY_CHARGE"}, {"table_name": "ad_dsp_unit_daily_charge", "type": "UNIT_DAY_CHARGE"}, {"table_name": "ad_dsp_account", "type": "ACCOUNT"}, {"table_name": "ad_dsp_account_support_info", "type": "AD_DSP_ACCOUNT_SUPPORT_INFO"}, {"table_name": "ad_dsp_app", "type": "AD_APP"}, {"table_name": "ad_dsp_unit_support_info", "type": "UNIT_SUPPORT_INFO"}, {"table_name": "ad_dsp_campaign", "type": "CAMPAIGN"}, {"table_name": "ad_dsp_unit", "type": "UNIT"}, {"table_name": "ad_dsp_creative", "type": "CREATIVE"}, {"table_name": "ad_dsp_agent", "type": "AGENT"}, {"table_name": "ad_dsp_agent_account", "type": "AGENT_ACCOUNT"}, {"table_name": "ad_dsp_target", "type": "TARGET"}, {"table_name": "ad_dsp_target_paid_audience", "type": "TARGET_PAID_AUDIENCE"}, {"table_name": "ad_dsp_photo", "type": "PHOTO_STATUS"}, {"table_name": "ad_dsp_position", "type": "AD_POSITION"}, {"table_name": "ad_dsp_position_resource", "type": "AD_POSITION_RESOURCE"}, {"table_name": "ad_dsp_position_strategy", "type": "AD_POSITION_STRATEGY"}, {"table_name": "ad_dsp_creative_preview", "type": "AD_CREATIVE_PREVIEW"}, {"table_name": "ad_dsp_material", "type": "MATERIAL"}, {"table_name": "ad_dsp_industry_v3", "type": "INDUSTRY_V3"}, {"table_name": "ad_dsp_package_bg", "type": "PKG_BG"}, {"table_name": "ad_risk_creative_target", "type": "RISK_CREATIVE_TARGET"}, {"table_name": "ad_risk_target", "type": "RISK_TARGET"}, {"table_name": "ad_risk_account_initiative", "type": "RISK_ACCOUNT_INITIATIVE"}, {"table_name": "ad_risk_industry_initiative", "type": "RISK_INDUSTRY_INITIATIVE"}, {"table_name": "ad_risk_photo_initiative", "type": "RISK_PHOTO_INITIATIVE"}, {"table_name": "factory_creative_info", "type": "FACTORY_CREATIVE_INFO"}, {"table_name": "ad_risk_unit_initiative", "type": "RISK_UNIT_INITIATIVE"}, {"table_name": "ad_risk_industry_white_account", "type": "RISK_INDUSTRY_WHITE_ACCOUNT"}, {"table_name": "ad_dsp_trace_util", "type": "TRACE_UTIL"}, {"table_name": "ad_dsp_merchant_app_unit", "type": "MERCHANT_APP_UNIT"}, {"table_name": "ad_dsp_card_show_data", "type": "CARD_SHOW_DATA"}, {"table_name": "ad_dsp_trace_api_detection", "type": "TRACE_API_DETECTION"}, {"table_name": "ad_dsp_cover", "type": "AD_DSP_COVER"}, {"table_name": "ad_dsp_upload_population_orientation", "type": "UPLOAD_POPULATION_ORIENTATION"}, {"table_name": "ad_dsp_site_ext_info", "type": "SITE_EXT_INFO"}, {"table_name": "ad_dsp_creative_barrages", "type": "CREATIVE_BARRAGES"}, {"type": "AD_DSP_MERCHANT_SMALL_SHOP", "table_name": "ad_dsp_unit_small_shop"}, {"type": "AD_DSP_UNIT_SMALL_SHOP_MERCHANT_SUPPORT_INFO", "table_name": "ad_dsp_unit_small_shop_merchant_support_info"}, {"type": "AD_DSP_SMALL_SHOP_SPU", "table_name": "ad_dsp_small_shop_product_spu"}, {"type": "DPA_CATEGORY_TARGET", "table_name": "ad_dpa_category_target"}, {"type": "MERCHANT_SUPPORT_INFO", "table_name": "ad_dsp_unit_merchant_support_info"}, {"type": "AD_DSP_HOSTING_PROJECT", "table_name": "ad_dsp_hosting_project"}, {"type": "AD_DSP_ECOM_HOSTING_PROJECT", "table_name": "ad_dsp_ecom_hosting_project"}, {"type": "AD_DSP_ECOM_HOSTING_PROJECT_CREATIVE_CREATE_PARAM", "table_name": "ad_dsp_ecom_hosting_project_creative_create_param"}, {"type": "AD_DSP_ADV_CARD", "table_name": "ad_dsp_adv_card"}, {"type": "LIVE_STREAM_USER_INFO", "table_name": "ad_dsp_live_stream_user_info"}, {"type": "AD_DSP_TARGET_MEDIA", "table_name": "ad_dsp_target_media"}, {"type": "AD_RISK_MATERIAL_TARGET", "table_name": "ad_risk_material_target"}, {"type": "AD_LP_PAGE_DAS", "table_name": "ad_lp_page_das"}, {"type": "FANSTOP_CAMPAIGN_SUPPORT_INFO", "table_name": "ad_dsp_campaign_fanstop_support_info"}, {"type": "FANSTOP_CREATIVE_SUPPORT_INFO", "table_name": "ad_dsp_creative_fanstop_support_info"}, {"type": "FANSTOP_UNIT_SUPPORT_INFO", "table_name": "ad_dsp_unit_fanstop_support_info"}, {"type": "AD_CHARGE_BALANCE", "table_name": "ad_charge_balance"}, {"type": "AD_UNIT_RANGE_CHARGE", "table_name": "ad_unit_range_charge"}, {"type": "AD_DSP_MINI_APP", "table_name": "ad_dsp_mini_app"}, {"type": "AD_DSP_WINFO", "table_name": "ad_dsp_winfo"}]}}