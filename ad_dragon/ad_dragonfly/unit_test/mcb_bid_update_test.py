#!/usr/bin/env python3
# coding=utf-8

import os
import sys
dragon_path=os.path.abspath('../../../../../dragon/')
ad_dragon_path=os.path.abspath('../../')
sys.path.append(dragon_path)
sys.path.append(ad_dragon_path)

import unittest
import math
import random
import json
import time
from dragonfly.common_leaf_dsl import LeafFlow, OfflineRunner, LeafService, IndexSource
from ad_dragonfly.mechanism.ad_api_mixin import AdApiMixin
from dragonfly.common_reco_pipeline_executor_pywrap import CommonRecoLeafPipelineExecutor, set_service_name, ContainerSet
from pathlib import Path
from datetime import datetime, timedelta

class McbBidDataUpdateMockTable(object):
  def __init__(self):
    self.account_id = ********** 
    self.campaign_id = **********
    self.combine_key = 666999

  def mock_update_history_data1(self, ad_leaf):
    ad_common_item = ad_leaf.add_item(self.combine_key, table_name="McbAdCommonTable")
    ad_common_item["campaign_id"] = self.campaign_id
    
    ad_base_item = ad_leaf.add_item(self.campaign_id, table_name="McbBaseInfoTable")
    ad_base_item["campaign_id"] = self.campaign_id
    ad_base_item["ocpc_action_type"] = 12
    ad_base_item["campaign_time_schedule"] = [1] * 24

    window_item = ad_leaf.add_item(self.combine_key, table_name="McbBidDataWindowTable")
    window_item["history.ts"] = [**********, **********, **********, **********, **********, **********, **********, **********]
    window_item["history.today_charge"] = [1915, 1986, 2010, 2018, 3035, 3065, 3272, 3401]
    window_item["history.remain_flow_ratio"] = [0.*********, 0.367963, 0.********, 0.*********, 0.**********, 0.**********, 0.**********, 0.**********]
    window_item["history.remain_budget_ratio"] = [0.617000103, 0.602800071, 0.598000109, 0.596400082, 0.393000126, 0.387000114, 0.345600128, 0.319800138]
    window_item["history.budget_coef"] = [1.39376271, 1.54862511, 1.72069454, 1.91188288, 1.78617191, 1.98463535, 1.804214, 2.00468206]
    window_item["history.bid_coef"] = [0.717482269, 0.645734072, 0.581160665, 0.523044586, 0.559856534, 0.503870904, 0.554258, 0.498832196]
    window_item["history.cem_bid_coef"] = [0.717482269, 0.645734072, 0.581160665, 0.523044586, 0.559856534, 0.503870904, 0.554258, 0.498832196]
    window_item["history.target_factor"] = [0.0] * 8

    bid_data_item = ad_leaf.add_item(self.combine_key, table_name="McbBidDataTable")
    #bid_data_item["start_of_day_ts"] = 
    #bid_data_item["remain_flow_ratio_global"] = 
    bid_data_item["current_ts"] = 1669647912
    bid_data_item["locked_left_budget"] = 1550
    bid_data_item["today_charge"] = 3450
    bid_data_item["target_factor"] = 0.0
    bid_data_item["budget_coef"] = 2.22742
    bid_data_item["bid_coef"] = 0.448949
    bid_data_item["cem_bid_coef"] = 0.448949
   
    config_item = ad_leaf.add_item(self.combine_key, table_name="McbConfigTable")
    config_item["daily_reset"] = 0.0
    config_item["window_sec"] = 7200.0
    config_item["support_schedule"] = 0.0
    config_item["support_ad_purchase_conversion_schedule"] = 0.0
    config_item["use_mcb_perform_control"] = 0.0
    config_item["best_performance_control"] = 0.0
    config_item["use_product_curve"] = 0.0

  def mock_update_history_data2(self, ad_leaf):
    ad_common_item = ad_leaf.add_item(self.combine_key, table_name="McbAdCommonTable")
    ad_common_item["campaign_id"] = self.campaign_id
    
    ad_base_item = ad_leaf.add_item(self.campaign_id, table_name="McbBaseInfoTable")
    ad_base_item["campaign_id"] = self.campaign_id
    ad_base_item["ocpc_action_type"] = 2
    ad_base_item["campaign_time_schedule"] = [1] * 24
    ad_base_item["second_industry_id"] = 1120

    window_item = ad_leaf.add_item(self.combine_key, table_name="McbBidDataWindowTable")
    window_item["history.ts"] = [1669683939, 1669683997, 1669684059, 1669684117, 1669684177, 1669684209]
    window_item["history.today_charge"] = [129705, 130502, 132097, 133382, 135025, 137649]
    window_item["history.remain_flow_ratio"] = [0.951985061, 0.951148272, 0.950256, 0.949425459, 0.948554933, 0.948088229]
    window_item["history.remain_budget_ratio"] = [0.951125622, 0.950825334, 0.95022434, 0.949740112, 0.949121, 0.948132277]
    window_item["history.budget_coef"] = [0.911168218, 0.876651943, 0.79695636, 0.75393635, 0.721398771, 0.699690163]
    window_item["history.bid_coef"] = [1.09749222, 1.14070356, 1.25477386, 1.32637191, 1.3861959, 1.3861959]
    window_item["history.cem_bid_coef"] = [1.09749222, 1.14070356, 1.25477386, 1.32637191, 1.3861959, 1.3861959]
    window_item["history.target_factor"] = [1.0] * 6

    bid_data_item = ad_leaf.add_item(self.combine_key, table_name="McbBidDataTable")
    #bid_data_item["start_of_day_ts"] = 
    #bid_data_item["remain_flow_ratio_global"] = 
    bid_data_item["current_ts"] = 1669684930
    bid_data_item["locked_left_budget"] = 2489390
    bid_data_item["today_charge"] = 164453
    bid_data_item["target_factor"] = 1.0
    bid_data_item["budget_coef"] = 0.7369
    bid_data_item["bid_coef"] = 1.35704
    bid_data_item["cem_bid_coef"] = 1.35704
   
    config_item = ad_leaf.add_item(self.combine_key, table_name="McbConfigTable")
    config_item["daily_reset"] = 1.0
    config_item["window_sec"] = 7200.0
    config_item["support_schedule"] = 1.0
    config_item["support_ad_purchase_conversion_schedule"] = 1.0
    config_item["use_mcb_perform_control"] = 1.0
    config_item["best_performance_control"] = 0.0
    config_item["use_product_curve"] = 0.0


class McbBudgetBidUpdateFlowTest(LeafFlow, AdApiMixin):
  def bid_data_update_flow(self):
    self.bid_data_update_enricher(
      item_table = "McbAdCommonTable",
      mcb_update = {
      "global_data_table": "McbGlobalDataTable",
        "global_window_data_table": "McbGlobalDataWindowTable",
        "bid_data_table": "McbBidDataTable",
        "bid_window_data_table": "McbBidDataWindowTable",
        "ad_common_table": "McbAdCommonTable",
        "global_2_bid": [
        {"column": "cost", "type": "double"},
        {"column": "today_cost", "type": "double"},
        {"column": "today_origin_cost", "type": "double"},
        {"column": "today_target_cost", "type": "double"},
        {"column": "today_constraint_target_cost", "type": "double"},
        {"column": "today_performance_target_cost", "type": "double"},
        {"column": "today_origin_performance_target_cost", "type": "double"},
        {"column": "today_constraint_cv", "type": "int"},
        {"column": "today_cv", "type": "int"},
        {"column": "today_rl_expect_cv", "type": "double"},
        {"column": "today_expect_cv", "type": "double"},
        {"column": "today_constraint_expect_cv", "type": "double"},
        {"column": "predict_conversion", "type": "double"},
        {"column": "predict_deep_conversion", "type": "double"},
        {"column": "retention_active", "type": "int"},
        {"column": "retention_count", "type": "int"},
        {"column": "constraint_action_type", "type": "int"},
        {"column": "is_universe_opt", "type": "int"},
        # 下面 2 个字段是budget trigger 触发调价时设置的
        {"column": "last_trigger_charge", "type": "int"},
        {"column": "last_trigger_ts", "type": "int"},
        ],
        "common_2_bid": [
        {"column": "state_info.last_is_adv", "type": "int"},
        {"column": "state_info.is_adv", "type": "int"},
        {"column": "state_info.last_change_ts", "type": "int"},
        ],
        "global_2_bid_window": [
        {"column": "second_cost_info", "type": "double", "sum_to_column": "window_data.second_cost"},
        {"column": "cost_info", "type": "double", "sum_to_column": "window_data.cost"},
        {"column": "origin_cost_info", "type": "double", "sum_to_column": "window_data.origin_cost"},
        {"column": "target_cost_info", "type": "double", "sum_to_column": "window_data.target_cost"},
        {"column": "performance_target_cost_info", "type": "double", "sum_to_column": "window_data.performance_target_cost"},
        {"column": "origin_performance_target_cost_info", "type": "double",
            "sum_to_column": "window_data.origin_performance_target_cost"},
        {"column": "predict_conversion_info", "type": "double", "sum_to_column": "window_data.predict_conversion"},
        {"column": "predict_deep_conversion_info", "type": "double", "sum_to_column": "window_data.predict_deep_conversion"},
        {"column": "retention_thresh_info", "type": "double", "sum_to_column": ""},
        {"column": "cpa_bid_info", "type": "double", "sum_to_column": ""},
        {"column": "deep_cpa_bid_info", "type": "double", "sum_to_column": ""},
        ], 
      },
      base_table_item_key = {"item_table" : "McbAdCommonTable", "column" : "campaign_id"},
      account_id = {"item_table" : "McbBaseInfoTable", "column" : "account_id"},
      campaign_id = {"item_table" : "McbBaseInfoTable", "column" : "campaign_id"},
      ocpx_action_type = {"item_table" : "McbBaseInfoTable", "column" : "ocpc_action_type"},
      product_name = {"item_table" : "McbBaseInfoTable", "column" : "product_name"},
      second_industry_id = {"item_table" : "McbBaseInfoTable", "column" : "second_industry_id"},
      campaign_time_schedule = {"item_table" : "McbBaseInfoTable", "column" : "campaign_time_schedule"},
      campaign_type = {"item_table" : "McbBaseInfoTable", "column" : "campaign_type"},
      hosting_cpa_bid = {"item_table" : "McbBaseInfoTable", "column" : "hosting_cpa_bid"},
      create_source_type = {"item_table" : "McbBaseInfoTable", "column" : "create_source_type"},
      mcb_level = {"item_table" : "McbBaseInfoTable", "column" : "mcb_level"},
      event_server_timestamp = {"item_table" : "McbGlobalDataTable", "column" : "event_server_timestamp"},

      support_schedule = {"item_table" : "McbConfigTable", "column" : "support_schedule"},
      support_ad_purchase_conversion_schedule = {"item_table" : "McbConfigTable", "column" : "support_ad_purchase_conversion_schedule"},
      use_mcb_perform_control = {"item_table" : "McbConfigTable", "column" : "use_mcb_perform_control"},
      best_performance_control = {"item_table" : "McbConfigTable", "column" : "best_performance_control"},
      use_product_curve = {"item_table" : "McbConfigTable", "column" : "use_product_curve"},
      daily_reset = {"item_table" : "McbConfigTable", "column" : "daily_reset"},
      window_sec = {"item_table" : "McbConfigTable", "column" : "window_sec"},
      init_bid_coef = {"item_table" : "McbConfigTable", "column" : "init_bid_coef"},
      advance_sec = {"item_table" : "McbConfigTable", "column" : "advance_sec"},
      fanstop_clear = {"item_table" : "McbConfigTable", "column" : "fanstop_clear"},
      cost_rate_threshold = {"item_table" : "McbConfigTable", "column" : "cost_rate_threshold"},
      reset_budget_coef = {"item_table" : "McbConfigTable", "column" : "reset_budget_coef"},
      reset_bid_coef = {"item_table" : "McbConfigTable", "column" : "reset_bid_coef"},
      reset_rnn_sequence = {"item_table" : "McbConfigTable", "column" : "reset_rnn_sequence"},
      reset_history = {"item_table" : "McbConfigTable", "column" : "reset_history"},
      daily_init_reset_ratio = {"item_table" : "McbConfigTable", "column" : "daily_init_reset_ratio"},
      daily_init_cpa_coef = {"item_table" : "McbConfigTable", "column" : "daily_init_cpa_coef"},
      avg_cpa_bid = {"item_table" : "McbBidDataTable", "column" : "avg_cpa_bid"},
      avg_deep_cpa_bid = {"item_table" : "McbBidDataTable", "column" : "avg_deep_cpa_bid"},
      start_of_day_ts = {"item_table" : "McbBidDataTable", "column" : "start_of_day_ts"},
      current_ts = {"item_table" : "McbBidDataTable", "column" : "current_ts"},
      last_remain_flow_ratio_global = {"item_table" : "McbBidDataTable", "column" : "last_remain_flow_ratio_global"},
      remain_flow_ratio_global = {"item_table" : "McbBidDataTable", "column" : "remain_flow_ratio_global"},
      remain_flow_ratio = {"item_table" : "McbBidDataTable", "column" : "remain_flow_ratio"},
      use_performance_flag = {"item_table" : "McbBidDataTable", "column" : "use_performance_flag"},
      target_factor = {"item_table" : "McbBidDataTable", "column" : "target_factor"},
      budget_coef = {"item_table" : "McbBidDataTable", "column" : "budget_coef"},
      bid_coef = {"item_table" : "McbBidDataTable", "column" : "bid_coef"},
      cem_bid_coef = {"item_table" : "McbBidDataTable", "column" : "cem_bid_coef"},
      auto_cpa_bid = {"item_table" : "McbBidDataTable", "column" : "auto_cpa_bid"},
      auto_roas = {"item_table" : "McbBidDataTable", "column" : "auto_roas"},
      today_charge = {"item_table" : "McbBidDataTable", "column" : "today_charge"},
      locked_left_budget = {"item_table" : "McbBidDataTable", "column" : "locked_left_budget"},
      last_locked_all_budget = {"item_table" : "McbBidDataTable", "column" : "budget_info.last_locked_all_budget"},
      locked_all_budget = {"item_table" : "McbBidDataTable", "column" : "budget_info.locked_all_budget"},
      remain_budget_ratio = {"item_table" : "McbBidDataTable", "column" : "remain_budget_ratio"},
      product_cpa_bid = {"item_table" : "McbBidDataTable", "column" : "product_cpa_bid"},
      product_roi_ratio = {"item_table" : "McbBidDataTable", "column" : "product_roi_ratio"},
      performance_cpa_bid = {"item_table" : "McbBidDataTable", "column" : "performance_cpa_bid"},
      performance_roi_ratio = {"item_table" : "McbBidDataTable", "column" : "performance_roi_ratio"},
      mcb_cpa_bid = {"item_table" : "McbBidDataTable", "column" : "mcb_cpa_bid"},
      mcb_roi_ratio = {"item_table" : "McbBidDataTable", "column" : "mcb_roi_ratio"},
      retention_active = {"item_table" : "McbBidDataTable", "column" : "retention_active"},
      mcb_retention_target = {"item_table" : "McbBidDataTable", "column" : "mcb_retention_target"},
      left_budget = {"item_table" : "McbBidDataTable", "column" : "left_budget"},
      bonus_price_ratio = {"item_table" : "McbBidDataTable", "column" : "bonus_price_ratio"},
      rl_data = {"item_table" : "McbBidDataTable", "column" : "rl_data"},
      cpa_last_timestamp = {"item_table" : "McbBidDataTable", "column" : "cpa_last_timestamp"},
      cpa_current_timestamp = {"item_table" : "McbBidDataTable", "column" : "cpa_current_timestamp"},
      retention_thresh = {"item_table" : "McbBidDataTable", "column" : "window_data.retention_thresh"},

      today_cost = {"item_table" : "McbBidDataTable", "column" : "today_cost"},
      today_target_cost = {"item_table" : "McbBidDataTable", "column" : "today_target_cost"},
      today_performance_target_cost = {"item_table" : "McbBidDataTable", "column" : "today_performance_target_cost"},
      today_origin_performance_target_cost = {"item_table" : "McbBidDataTable", "column" : "today_origin_performance_target_cost"},
      today_cv = {"item_table" : "McbBidDataTable", "column" : "today_cv"},
      today_expect_cv = {"item_table" : "McbBidDataTable", "column" : "today_expect_cv"},
      today_rl_expect_cv = {"item_table" : "McbBidDataTable", "column" : "today_rl_expect_cv"},
      today_origin_cost = {"item_table" : "McbBidDataTable", "column" : "today_origin_cost"},
      today_target_cpa_sum = {"item_table" : "McbBidDataTable", "column" : "today_target_cpa_sum"},
      predict_conversion = {"item_table" : "McbBidDataTable", "column" : "predict_conversion"},
      predict_deep_conversion = {"item_table" : "McbBidDataTable", "column" : "predict_deep_conversion"},
      cpa_coef = {"item_table" : "McbBidDataTable", "column" : "cpa_coef"},
      mcb_retention_threshold = {"item_table" : "McbBidDataTable", "column" : "mcb_retention_threshold"},

      budget_opt_info_abo_cap_info_ocpx_action_type =
        {"item_table" : "McbBidDataTable", "column" : "budget_opt_info.abo_cap_info.ocpx_action_type"},
      budget_opt_info_abo_cap_info_constraint_ocpx_action_type =
        {"item_table" : "McbBidDataTable", "column" : "budget_opt_info.abo_cap_info.constraint_ocpx_action_type"},
      budget_opt_info_abo_cap_info_cap_value =
        {"item_table" : "McbBidDataTable", "column" : "budget_opt_info.abo_cap_info.cap_value"},
      budget_opt_info_abo_cap_info_constraint_cap_bid =
        {"item_table" : "McbBidDataTable", "column" : "budget_opt_info.abo_cap_info.constraint_cap_bid"},
      budget_opt_info_cap_bid = {"item_table" : "McbBidDataTable", "column" : "budget_opt_info.cap_bid"},
      budget_opt_info_cap_bid_type = {"item_table" : "McbBidDataTable", "column" : "budget_opt_info.cap_bid_type"},
      budget_opt_info_constraint_cap_bid = {"item_table" : "McbBidDataTable", "column" : "budget_opt_info.constraint_cap_bid"},
      budget_opt_info_constraint_cap_bid_type = {"item_table" : "McbBidDataTable", "column" : "budget_opt_info.constraint_cap_bid_type"},
      bid_info_auto_cpa_bid = {"item_table" : "McbBidDataTable", "column" : "bid_info.auto_cpa_bid"},
      bid_info_auto_roas = {"item_table" : "McbBidDataTable", "column" : "bid_info.auto_roas"},

      history_ts = {"item_table" : "McbBidDataWindowTable", "column" : "history.ts"},
      history_today_charge = {"item_table" : "McbBidDataWindowTable", "column" : "history.today_charge"},
      history_target_factor = {"item_table" : "McbBidDataWindowTable", "column" : "history.target_factor"},
      history_remain_flow_ratio = {"item_table" : "McbBidDataWindowTable", "column" : "history.remain_flow_ratio"},
      history_remain_budget_ratio = {"item_table" : "McbBidDataWindowTable", "column" : "history.remain_budget_ratio"},
      history_budget_coef = {"item_table" : "McbBidDataWindowTable", "column" : "history.budget_coef"},
      history_bid_coef = {"item_table" : "McbBidDataWindowTable", "column" : "history.bid_coef"},
      history_cem_bid_coef = {"item_table" : "McbBidDataWindowTable", "column" : "history.cem_bid_coef"},
    )
    return self
