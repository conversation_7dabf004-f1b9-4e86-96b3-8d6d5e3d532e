from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from ad_dragonfly.retrieval.search.search_arranger import *
from ad_dragonfly.retrieval.search.search_enricher import *
from ad_dragonfly.retrieval.search.search_retriever import *
from ad_dragonfly.retrieval.search.search_observer import *


class AdRetrievalSearchMixin(CommonLeafBaseMixin):
  def retrieve_by_bidword_retr(self, **kwargs):
    self._add_processor(BidwordRetrRetriever(kwargs))
    return self

  def retrieve_by_bidword_extend(self, **kwargs):
    self._add_processor(BidwordExtendRetriever(kwargs))
    return self

  def retrieve_by_tag_retr(self, **kwargs):
    self._add_processor(TagRetrRetriever(kwargs))
    return self

  def search_retrieve_by_redis(self, **kwargs):
    self._add_processor(RedisRetriever(kwargs))
    return self

  def search_retrieve_by_local(self, **kwargs):
    self._add_processor(LocalRetriever(kwargs))
    return self

  def retrieve_by_common_ad_retr(self, **kwargs):
    self._add_processor(CommonAdRetrRetriever(kwargs))
    return self

  def bidword_merge(self, **kwargs):
    self._add_processor(BidwordMergeArranger(kwargs))
    return self

  def path_quota_init(self, **kwargs):
    self._add_processor(PathQuotaInitEnricher(kwargs))
    return self

  def query_blacklist_init(self, **kwargs):
    self._add_processor(QueryBlacklistInitEnricher(kwargs))
    return self

  def path_prepare(self, **kwargs):
    self._add_processor(PathPrepareEnricher(kwargs))
    return self

  def ad_box_path_prepare(self, **kwargs):
    self._add_processor(AdBoxPathPrepareEnricher(kwargs))
    return self

  def path_limit(self, **kwargs):
    self._add_processor(PathLimitArranger(kwargs))
    return self

  def goods_tab_quota_expand(self, **kwargs):
    self._add_processor(GoodsTabQuotaExpandEnricher(kwargs))
    return self

  def query_preprocess(self, **kwargs):
    self._add_processor(QueryPreprocessEnricher(kwargs))
    return self

  def multi_counter(self, **kwargs):
    self._add_processor(MultiCounterEnricher(kwargs))
    return self

  def photo_valid_check(self, **kwargs):
    self._add_processor(PhotoValidCheckArranger(kwargs))
    return self

  def live_valid_check(self, **kwargs):
    self._add_processor(LiveValidCheckArranger(kwargs))
    return self

  def live_stream_index(self, **kwargs):
    self._add_processor(LiveStreamIndexEnricher(kwargs))
    return self

  def photo_merge(self, **kwargs):
    self._add_processor(PhotoMergeArranger(kwargs))
    return self
  
  def photo_ecpm_rele_merge(self, **kwargs):
    self._add_processor(PhotoEcpmReleMergeArranger(kwargs))
    return self

  def live_merge(self, **kwargs):
    self._add_processor(LiveMergeArranger(kwargs))
    return self

  def author_merge(self, **kwargs):
    self._add_processor(AuthorMergeArranger(kwargs))
    return self

  def sort_by_posterior_ecpm(self, **kwargs):
    self._add_processor(PosteriorEcpmSortArranger(kwargs))
    return self

  def twin_tower_relevance_init(self, **kwargs):
    self._add_processor(TwinTowerRelevanceInitEnricher(kwargs))
    return self

  def twin_tower_relevance(self, **kwargs):
    self._add_processor(TwinTowerRelevanceArranger(kwargs))
    return self
  
  def twin_tower_relevance_calc(self, **kwargs):
    self._add_processor(TwinTowerRelevanceCalcEnricher(kwargs))
    return self
  
  def get_relevance_threshold_by_reason(self, **kwargs):
    self._add_processor(RelevanceThresholdEnricher(kwargs))
    return self

  def twin_tower_relevance_ensemble_sort(self, **kwargs):
    self._add_processor(TwinTowerRelevanceEnsembleSortArranger(kwargs))
    return self
  
  def twin_tower_ecpm_init(self, **kwargs):
    self._add_processor(TwinTowerEcpmInitEnricher(kwargs))
    return self
  
  def twin_tower_ecpm_calc(self, **kwargs):
    self._add_processor(TwinTowerEcpmCalcEnricher(kwargs))
    return self
  
  def twin_tower_ecpm_ensemble_sort(self, **kwargs):
    self._add_processor(TwinTowerEcpmEnsembleSortArranger(kwargs))
    return self

  def retrieve_by_ad_retr(self, **kwargs):
    self._add_processor(AdRetrRetriever(kwargs))
    return self

  def live_retrieve_by_ad_retr(self, **kwargs):
    self._add_processor(LiveAdRetrRetriever(kwargs))
    return self

  def load_score(self, **kwargs):
    self._add_processor(LoadScoreArranger(kwargs))
    return self

  def item_key_hash(self, **kwargs):
    self._add_processor(ItemKeyHashArranger(kwargs))
    return self

  def ad_kess_name_resolve(self, **kwargs):
    self._add_processor(AdKessNameResolveEnricher(kwargs))
    return self

  def enrich_by_redis(self, **kwargs):
    self._add_processor(RedisEnricher(kwargs))
    return self

  def enrich_by_target_search(self, **kwargs):
    self._add_processor(TargetSearchEnricher(kwargs))
    return self

  def enrich_by_common_trigger(self, **kwargs):
    self._add_processor(CommonTriggerEnricher(kwargs))
    return self

  def enrich_by_bidword_search(self, **kwargs):
    self._add_processor(BidwordSearchEnricher(kwargs))
    return self

  def retrieve_by_searcher_response(self, **kwargs):
    self._add_processor(SearcherResponseRetriever(kwargs))
    return self

  def match_server_response_pack(self, **kwargs):
    self._add_processor(MatchServerResponsePackEnricher(kwargs))
    return self

  def mingtou_sku_distinct(self, **kwargs):
    self._add_processor(MingtouSkuDistinctEnricher(kwargs))
    return self

  def select_bidword_photo_ads(self, **kwargs):
    self._add_processor(SelectBidwordPhotoAdsEnricher(kwargs))
    return self

  def select_bidword_live_ads(self, **kwargs):
    self._add_processor(SelectBidwordLiveAdsEnricher(kwargs))
    return self

  def match_server_init(self, **kwargs):
    self._add_processor(MatchServerInitEnricher(kwargs))
    return self

  def select_prepare(self, **kwargs):
    self._add_processor(SelectPrepareEnricher(kwargs))
    return self

  def select_photo_ads(self, **kwargs):
    self._add_processor(SelectPhotoAdsEnricher(kwargs))
    return self

  def select_live_ads(self, **kwargs):
    self._add_processor(SelectLiveAdsEnricher(kwargs))
    return self

  def select_fanstop_ads(self, **kwargs):
    self._add_processor(SelectFanstopAdsEnricher(kwargs))
    return self

  def append_ads(self, **kwargs):
    self._add_processor(AppendAdsEnricher(kwargs))
    return self
  
  def enrich_by_adx(self, **kwargs):
    self._add_processor(AdxEnricher(kwargs))
    return self

  def record_retrieve_item_key(self, **kwargs):
    self._add_processor(RecordRetrieveItemKeyEnricher(kwargs))
    return self
  
  def ad_server_trace_log(self, **kwargs):
    self._add_processor(AdServerTraceLogObserver(kwargs))
    return self
  
  def retrieve_by_adx(self, **kwargs):
    self._add_processor(AdxRetriever(kwargs))
    return self
  
  def adx_filter(self, **kwargs):
    self._add_processor(AdxFilterArranger(kwargs))
    return self

  def build_rewrite_query_table(self, **kwargs):
    self._add_processor(RewriteQueryRetriever(kwargs))
    return self

  def retrieve_by_tag_retrieval_like_redis(self, **kwargs):
    self._add_processor(TagRetrievalLikeRedisRetriever(kwargs))
    return self

  def rewrite_query_admit(self, **kwargs):
    self._add_processor(RewriteQueryAdmitEnricher(kwargs))
    return self

  def city_hash_64(self, **kwargs):
    self._add_processor(CityHash64Enricher(kwargs))
    return self

  def gen_section_slot_param(self, **kwargs):
    self._add_processor(GenSectionSlotParamEnricher(kwargs))
    return self