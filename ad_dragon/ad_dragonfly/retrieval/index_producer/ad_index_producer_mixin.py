#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from .ad_index_producer_enricher import *

class AdIndexProducerMixin(CommonLeafBaseMixin):
  def ad_table_print_observer(self, **kwargs):
    """
    AdTablePrintObserver
    """
    self._add_processor(AdTablePrintObserver(kwargs))
    return self

  def ad_zk_selector_mixer(self, **kwargs):
    """
    AdZKSelectorMixer
    """
    self._add_processor(AdZKSelectorMixer(kwargs))
    return self

  def generate_gc_list(self, **kwargs):
    """
    AdTableGCListMixer
    """
    self._add_processor(AdTableGCListMixer(kwargs))
    return self

  def gc_list_diff(self, **kwargs):
    """
    AdTableGCListMixer
    """
    self._add_processor(GCListDiffMixer(kwargs))
    return self

  def index_perf(self, **kwargs):
    """
    IndexPerfMixer
    """
    self._add_processor(IndexPerfMixer(kwargs))
    return self

  def get_gc_list_trace(self, **kwargs):
    """
    IndexPerfMixer
    """
    self._add_processor(GcListTraceMixer(kwargs))
    return self