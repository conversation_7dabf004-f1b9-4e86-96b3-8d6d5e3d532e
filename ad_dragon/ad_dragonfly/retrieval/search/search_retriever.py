import abc
import sys
import os

sys.path.append(os.path.normpath(os.path.dirname(os.path.realpath(__file__))+ "/../../../"))  # teams/ad/ad_dragon

from dragonfly.common_leaf_util import strict_types, check_arg, extract_attr_names, ArgumentError
from dragonfly.common_leaf_processor import LeafRetriever
from dragon_server.retrieval.search.src.schema_manager.item_attr_schema import ad_server_item_attrs_list
from dragon_server.retrieval.search.src.schema_manager.common_attr_schema import pos_manager_common_attrs

class BidwordRetrRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_bidword_retr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'search_info',
      'parsed_query',
      'rewrite_query_list_base64',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'bidword',
      'relevance_score',
      'multi_retrieval_tag',
      'match_type',
      'sub_match_type',
    }


class BidwordExtendRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_bidword_extend"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'query',
      'enable_search_query_retrieval_bidword_extend',
      'search_ad_bidword_extend_prefix',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'bidword',
      'relevance_score',
      'multi_retrieval_tag',
      'match_type',
      'sub_match_type',
    }

PHOTO_RETRIEVER_OUTPUT_ITEM_ATTRS = {
  'photo_id',
  'sub_recall_strategy_type',
  'rank_score',
  'relevance_score',
  'is_qr',
  'qr_score',
  'extend_type',
  'sub_extend_type',
  'rewrite_query_set',
}

LIVE_RETRIEVER_OUTPUT_ITEM_ATTRS = {
  'live_stream_id',
  'relevance_score',
  'sub_recall_strategy_type',
  'sub_match_type',
  'sku_id',
  'rank_score',
  'sku_id_vec',
  'sctr',
  'is_qr',
  'qr_score',
  'extend_type',
  'sub_extend_type',
  'rewrite_query_set',
  'item_type',
}


class TagRetrRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_tag_retr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("import_common_attr", []), "name")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("export_item_attr", []), "as")
    unexpected_attrs = attrs - PHOTO_RETRIEVER_OUTPUT_ITEM_ATTRS - LIVE_RETRIEVER_OUTPUT_ITEM_ATTRS
    if unexpected_attrs:
      raise ArgumentError(f"{self.get_type_alias()} 返回了非预期的 attr: {unexpected_attrs}")
    return attrs


class RedisUDF(abc.ABC):
  @abc.abstractmethod
  def class_name(self) -> str:
    ...

  @abc.abstractmethod
  def import_common_attr(self) -> list:
    """写法同 enrich_attr_by_light_function"""
    ...

  @abc.abstractmethod
  def export_common_attr(self) -> list:
    """写法同 enrich_attr_by_light_function"""
    ...

  @abc.abstractmethod
  def export_item_attr(self) -> list:
    """写法同 enrich_attr_by_light_function"""
    ...


class RedisRetriever(LeafRetriever):
  def __init__(self, config: dict):
    udf: RedisUDF = config.pop('udf', None)
    if udf is None:
      raise ArgumentError(f"{self.get_type_alias()} 没有配置 udf")
    config.update({
      'class_name': udf.class_name(),
      'import_common_attr': udf.import_common_attr(),
      'export_common_attr': udf.export_common_attr(),
      'export_item_attr': udf.export_item_attr(),
    })
    super(RedisRetriever, self).__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_retrieve_by_redis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("import_common_attr", []), "name")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("export_common_attr", []), "name")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("export_item_attr", []), "as")
    return attrs


class LocalUDF(abc.ABC):
  @abc.abstractmethod
  def class_name(self) -> str:
    ...

  @abc.abstractmethod
  def import_common_attr(self) -> list:
    """写法同 enrich_attr_by_light_function"""
    ...

  @abc.abstractmethod
  def export_item_attr(self) -> list:
    """写法同 enrich_attr_by_light_function"""
    ...


class AdRetrUDF(abc.ABC):
  @abc.abstractmethod
  def class_name(self) -> str:
    ...

  @abc.abstractmethod
  def import_common_attr(self) -> list:
    """写法同 enrich_attr_by_light_function"""
    ...

  @abc.abstractmethod
  def export_common_attr(self) -> list:
    """写法同 enrich_attr_by_light_function"""
    ...

  @abc.abstractmethod
  def export_item_attr(self) -> list:
    """写法同 enrich_attr_by_light_function"""
    ...


class LocalRetriever(LeafRetriever):
  def __init__(self, config: dict):
    udf: LocalUDF = config.pop('udf', None)
    if udf is None:
      raise ArgumentError(f"{self.get_type_alias()} 没有配置 udf")
    config.update({
      'class_name': udf.class_name(),
      'import_common_attr': udf.import_common_attr(),
      'export_item_attr': udf.export_item_attr(),
    })
    super(LocalRetriever, self).__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_retrieve_by_local"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("import_common_attr", []), "name")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("export_item_attr", []), "as")
    return attrs


class CommonAdRetrRetriever(LeafRetriever):
  def __init__(self, config: dict):
    udf: AdRetrUDF = config.pop('udf', None)
    if udf is None:
      raise ArgumentError(f"{self.get_type_alias()} 没有配置 udf")
    config.update({
      'class_name': udf.class_name(),
      'import_common_attr': udf.import_common_attr(),
      'export_common_attr': udf.export_common_attr(),
      'export_item_attr': udf.export_item_attr(),
    })
    super(CommonAdRetrRetriever, self).__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_common_ad_retr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("import_common_attr", []), "name")
    attrs.update({
      'search_info',
      'parsed_query',
    })
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("export_common_attr", []), "name")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("export_item_attr", []), "as")
    return attrs


class AdRetrRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_ad_retr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'search_info',
      'parsed_query',
      'ab_group_name',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return PHOTO_RETRIEVER_OUTPUT_ITEM_ATTRS


class LiveAdRetrRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_retrieve_by_ad_retr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'search_info',
      'parsed_query',
      self._config['search_section_slot_param']
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return LIVE_RETRIEVER_OUTPUT_ITEM_ATTRS


class SearcherResponseRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_searcher_response"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      self._config['response_attr'],
      self._config['ad_request_attr'],
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if (self._config['return_ad_server_attrs']):
      attrs = {item_attr for item_attr in ad_server_item_attrs_list}
    attrs.update({
      'target_shard_type',
      'creative_id',
      'ad_list_type',
      'campaign_type',
      'live_creative_type',
      'dup_photo_id',
      'photo_id',
      'live_stream_id',
      'city_product_id',
      'ocpx_action_type',
      'delivery_rate',
      'ecpm_score',
      'ensemble_score',
      'filter_reason',
      'quick_search',
      'item_id',
      'allow_app_card',
      'allow_form_card',
      'is_search_celebrity',
      'is_search_item_kbox',
      'is_search_live_kbox',
      'is_search_item_recall',
      'is_search_mid_page',
      'creative_material_type',
      'is_intervene',
      'is_bidword_backup_ad',
      'account_type',
    })
    return attrs

class AdxRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_adx"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      self._config['adx_response_attr'],
      self._config['ad_request_attr'],
      *pos_manager_common_attrs,
      'soft_n_support_industry_open',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if (self._config['return_ad_server_attrs']):
      attrs = {item_attr for item_attr in ad_server_item_attrs_list}
    attrs.update({
      'adx_creative_id_2_idx',
    })
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      'is_force_adx_ad',
      'is_adx_white_user',
    }



class RewriteQueryRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_rewrite_query_table"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'search_info',
      'source_query_set',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config['query_column'],
      self._config['is_qr_column'],
      self._config['qr_score_column'],
      self._config['extend_type_column'],
      self._config['sub_extend_type_column'],
    }


class TagRetrievalLikeRedisRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_tag_retrieval_like_redis"

  @property
  @strict_types
  def input_item_tables(self) -> set:
    return {self._config['input_table']}

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(f"{self._config['input_table']}::{self._config['key_input']}")
    if 'use_alt_reason_input' in self._config:
      attrs.add(f"{self._config['input_table']}::{self._config['use_alt_reason_input']}")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config['key_output'],
      self._config['trigger_id_output'],
      self._config['distance_output'],
    }
