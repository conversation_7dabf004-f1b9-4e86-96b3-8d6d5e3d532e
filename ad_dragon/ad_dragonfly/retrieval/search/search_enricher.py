import sys
import os

sys.path.append(os.path.normpath(os.path.dirname(os.path.realpath(__file__))+ "/../../../"))  # teams/ad/ad_dragon
from dragonfly.common_leaf_util import check_arg, extract_common_attrs, strict_types
from dragonfly.common_leaf_processor import <PERSON><PERSON>n<PERSON>er
from dragon_server.retrieval.search.src.schema_manager.item_attr_schema import ad_server_item_attrs_list
from dragon_server.retrieval.search.src.schema_manager.common_attr_schema import match_server_output_common_attrs_list
from dragon_server.retrieval.search.src.schema_manager.common_attr_schema import pos_manager_common_attrs


class PathQuotaInitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "path_quota_init"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'recall_node_quota_config_group',
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "node_quota_config",
    }


class QueryBlacklistInitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "query_blacklist_init"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      "query",
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "hit_query_blacklist_paths",
    }


class PathPrepareEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "path_prepare"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      "node_quota_config",
      "hit_query_blacklist_paths",
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "path_name",
      "total_quota",
      "sub_quota",
      "posterior_quota",
    }


class AdBoxPathPrepareEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_box_path_prepare"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'node_quota_config',
      'hit_query_blacklist_paths',
      'enable_search_ad_box_single_col_personal_rewrite',
      'search_ad_box_single_col_rewrite_sub_quota',
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      'path_name',
      'total_quota',
      'sub_quota',
      'posterior_quota',
    }

class GoodsTabQuotaExpandEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "goods_tab_quota_expand"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      "search_info",
      "total_quota",
      "sub_quota",
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "total_quota",
      "sub_quota",
    }


class QueryPreprocessEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "query_preprocess"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "org_query",
      "trimed_query",
      "norm_lower_query",
      "source_query_set",
      "is_char_num_query",
      "is_query_rewrite_list_empty",
      "is_timeout_query",
      "is_hot_query",
      "is_goods_tab",
    }


class MultiCounterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "multi_counter"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      "photo_id",
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      "counter_ecpm",
    }


class LiveStreamIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_stream_index"


class TwinTowerRelevanceInitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "twin_tower_relevance_init"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'search_query_transport_info',
      'twin_tower_rele_threshold',
      'twin_tower_trigger_list_thres',
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "query_rele_embedding",
      "query_rele_embedding_ver",
      "twin_tower_threshold_map",
      "twin_tower_trigger_list_thres",
    }

class TwinTowerEcpmInitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return 'twin_tower_ecpm_init'

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'search_query_transport_info',
      'enable_ecpm_twin_tower_in_trigger',
      'enable_ecpm_relevance_merge',
      'twin_tower_ecpm_ensemble_ratio_group',
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      'ecpm_user_embedding',
      'enable_ecpm_twin_tower_in_photo_trigger',
      'enable_ecpm_relevance_merge',
      'ecpm_twin_tower_ensemble_ratio_map'
    }


class AdKessNameResolveEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_kess_name_resolve"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      self._config['output_attr'],
    }


class RedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_redis"

  @strict_types
  def depend_on_items(self) -> bool:
    return not self._config.get("is_common_attr", True)

  def is_common(self):
    return self._config.get("is_common_attr", True)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      attrs.add(self._config["input_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      attrs.add(self._config["output_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      attrs.add(self._config["input_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      attrs.add(self._config["output_attr"])
    return attrs


class TargetSearchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_target_search"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'photo_trigger_resp',
      'live_trigger_resp',
      self._config['ad_request_attr'],
      'global_timeout_config_group_1',
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      self._config['target_response_attr'],
      self._config['time_cost_ms_attr'],
      self._config['timeout_flag_attr'],
    }


class CommonTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_common_trigger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'ad_request',
      'target_ab_group_name',
      'is_query_unable_live',
      'parsed_query_base64',
      self._config['time_out_attr'],
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      self._config['trigger_response_attr'],
      self._config['time_cost_ms_attr'],
      self._config['timeout_flag_attr'],
    }


class BidwordSearchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_bidword_search"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      self._config['ad_request_attr'],
      'llsid',
      'global_timeout_config_group_1',
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      self._config['target_response_attr'],
      self._config['time_cost_ms_attr'],
      self._config['timeout_flag_attr'],
    }


class MatchServerResponsePackEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "match_server_response_pack"

  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      self._config['target_search_response_attr'],
      self._config['target_search_time_cost_ms_attr'],
      self._config['target_search_timeout_flag_attr'],

      self._config['bidword_search_response_attr'],
      self._config['bidword_search_time_cost_ms_attr'],
      self._config['bidword_search_timeout_flag_attr'],

      self._config['photo_trigger_time_cost_ms_attr'],
      self._config['photo_trigger_timeout_flag_attr'],

      self._config['live_trigger_time_cost_ms_attr'],
      self._config['live_trigger_timeout_flag_attr'],
      self._config['adx_response_attr'],
      'fanstop_max_return_live',
      'enable_prophet_user_bonus',
      'enable_fanstop_pdd_user_package_redirect',
      'fanstop_user_tag_strategy_exp',
      'fanstop_user_tag_strategy_online',
      'global_timeout_config_group_1',
      'final_ad_list_size',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = {item_attr for item_attr in ad_server_item_attrs_list}
    attrs.update({
    'filter_reason',
    })
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = {item_attr for item_attr in match_server_output_common_attrs_list}
    attrs.update({
      self._config['match_server_response_attr'],
      self._config['rank_request_attr'],
    })
    return attrs


class SelectBidwordPhotoAdsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "select_bidword_photo_ads"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'search_candidate_num_per_key_bidword',
      'ad_search_bidword_merge_quota',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'target_shard_type',
      'ad_list_type',
      'campaign_type',
      'dup_photo_id',
      'photo_id',
      'city_product_id',
      'ocpx_action_type',
      'delivery_rate',
      'ecpm_score',
      'ensemble_score',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'filter_reason',
    }

class SelectBidwordLiveAdsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "select_bidword_live_ads"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'search_candidate_num_per_key_bidword',
      'ad_search_bidword_live_merge_quota',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'live_stream_id',
      'ocpx_action_type',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'filter_reason',
    }

class MatchServerInitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "match_server_init"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'at_request',
      'ad_server.raw_request',
      'enable_none_ecom_query_live',
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      'ad_request',
      'parsed_query_base64',
      'is_hot_query',
      'page_id',
      'sub_page_id',
      'target_ab_group_name',
      'live_stream_only',
      'photo_only',
      'is_query_unable_live',
      'norm_lower_query',
      'llsid',
    }

class TwinTowerEcpmCalcEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "twin_tower_ecpm_calc"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'path_name',
      'ecpm_user_embedding',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'photo_id'
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'twin_tower_ecpm_score'
    }


class TwinTowerRelevanceCalcEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "twin_tower_relevance_calc"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'path_name',
      'query_rele_embedding',
      'query_rele_embedding_ver',
    }
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'photo_id'
    }
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config['save_to_item_attr']
    }

class RelevanceThresholdEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_relevance_threshold_by_reason"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'search_info',
      'twin_tower_threshold_map',
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      self._config['save_to_common_attr'],
    }


class SelectPrepareEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "select_prepare"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'page_id',
      'sub_page_id',
      'is_hot_query',
      'ad_request',

      'search_rank_drop_down_ratio',
      'search_rank_live_drop_down_ratio',
      'search_rank_fanstop_drop_down_ratio',
      'search_ranking_hour_quota_factors',
      'search_rank_live_list_ratio',
      'search_rank_fanstop_list_ratio',
      'search_rank_photo_dynamic_quota_ratio',
      'search_rank_live_dynamic_quota_ratio',
      'search_rank_fanstop_dynamic_quota_ratio',
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      'dsp_ad_list_quota',
      'live_ad_list_quota',
      'fanstop_ad_list_quota',
      'photo_append_config',
      'live_append_config',
    }


class MingtouSkuDistinctEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mingtou_sku_distinct"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'ad_request',
      'search_ad_server_exp_name',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'target_shard_type',
      'item_id',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'filter_reason',
    }


class SelectPhotoAdsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "select_photo_ads"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'dsp_ad_list_quota',
      'photo_append_config',
      'search_ad_server_exp_name',
      'enable_search_prerank_sort_unify',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'quick_search',
      'target_shard_type',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'filter_reason',
    }


class SelectLiveAdsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "select_live_ads"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'live_ad_list_quota',
      'live_append_config',
      'search_ad_server_exp_name',
      'bidword_live_rank_search_degrade_quota',
      'bidword_live_rank_search_degrade_quota_ratio',
      'p2l_rank_search_degrade_quota',
      'p2l_rank_search_degrade_quota_ratio',
      'enable_fanstop_orientation_tag_ecpc',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'quick_search',
      'live_creative_type',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'filter_reason',
    }


class SelectFanstopAdsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "select_fanstop_ads"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'fanstop_ad_list_quota',
      'fanstop_p2l_rank_search_degrade_quota',
      'fanstop_p2l_rank_search_degrade_quota_ratio',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'live_creative_type',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'filter_reason',
    }


class AppendAdsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "append_ads"

  @strict_types
  def _check_config(self) -> None:
    check_arg(
      bool(self._config.get('quota_config')) ^ bool(self._config.get('quota')),
      "`quota_config` 和 `quota` 必须提供且仅提供一个"
    )

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    quota_config = self._config.get('quota_config')
    if quota_config:
      return {quota_config}

    attrs = set()
    attrs.update(extract_common_attrs(self._config['quota']))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    save_remaining_to = self._config.get('save_remaining_to')
    if save_remaining_to:
      attrs.add(save_remaining_to)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'filter_reason',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'filter_reason',
    }

class AdxEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_adx"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      self._config['ad_request_attr'],
      *pos_manager_common_attrs,
      'search_inner_stream_abtest_0719_client',
      'enable_adx_open_pos_id_5_req',
      'enable_adx_ad',
      'disable_adx_for_unlogin_user',
      'only_recall_small_shop_exp',
      'closed_personal_no_adx_ads',
      'enable_search_ad_box_thanos_req_adx',
      'enable_search_ad_box_feed_req_adx',
      'pos_manager_flow_type_flag',
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      self._config['adx_response_attr'],
      self._config['time_cost_ms_attr'],
      self._config['timeout_flag_attr'],
      self._config['adx_request_attr'],
      'adx_req_admit_fail_reason',
    }
  
class RecordRetrieveItemKeyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "record_retrieve_item_key"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      self._config['retrieve_item_keys'],
    }

class RewriteQueryAdmitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rewrite_query_admit"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(extract_common_attrs(self._config['config_key']))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      self._config['extend_type_column'],
      self._config['qr_score_column'],
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config['admit_column'],
    }


class CityHash64Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "city_hash_64"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      self._config['from_item_attr'],
    }
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config['to_item_attr'],
    }

class GenSectionSlotParamEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gen_section_slot_param"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(extract_common_attrs(self._config.get('sku_retrieval_slot_id')))
    attrs.update(extract_common_attrs(self._config.get('section_2_weight')))
    attrs.update(extract_common_attrs(self._config.get('drop_default_weight')))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      self._config['to_common_attr']
    }