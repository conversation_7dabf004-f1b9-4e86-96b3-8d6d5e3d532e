import sys
import os

sys.path.append(os.path.normpath(os.path.dirname(os.path.realpath(__file__))+ "/../../../"))  # teams/ad/ad_dragon
from dragonfly.common_leaf_util import check_arg, extract_common_attrs, strict_types
from dragonfly.common_leaf_processor import LeafObserver                  


class AdServerTraceLogObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_server_trace_log"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      self._config['ad_request_attr'],
      self._config['retrieve_item_keys'],
    }
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'filter_reason',
    }
