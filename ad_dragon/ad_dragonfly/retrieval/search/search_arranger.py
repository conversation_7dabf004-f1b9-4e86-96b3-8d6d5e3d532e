from dragonfly.common_leaf_util import strict_types
from dragonfly.common_leaf_processor import <PERSON><PERSON><PERSON><PERSON>
from dragon_server.retrieval.search.src.schema_manager.common_attr_schema import pos_manager_common_attrs


class BidwordMerge<PERSON>rranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bidword_merge"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'multi_retrieval_tag'
    }

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'retrival_bidword_size_ab',
    }


class PosteriorEcpmSortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sort_by_postierior_ecpm"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'search_info',
      'total_quota',
      'posterior_quota',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'photo_id',
      'counter_ecpm',
      'relevance_score',
    }


class TwinTowerRelevance<PERSON>rranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "twin_tower_relevance"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'path_name',
      'search_info',
      'twin_tower_threshold_map',
      'twin_tower_trigger_list_thres',
      'query_rele_embedding',
      'query_rele_embedding_ver',
      'enable_twin_tower_before_merge',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'photo_id',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'twin_tower_relevance_score',
    }

class TwinTowerEcpmEnsembleSortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "twin_tower_ecpm_ensemble_sort"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'path_name',
      'ecpm_twin_tower_ensemble_ratio_map'
    }
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'photo_id',
      'rank_score',
      'twin_tower_ecpm_score'
    }

class TwinTowerRelevanceEnsembleSortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "twin_tower_relevance_ensemble_sort"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'path_name',
      'query_rele_embedding',
      'query_rele_embedding_ver',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'photo_id',
      self._config.get('relevance_score_attr_name', 'twin_tower_relevance_score'),
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config.get('relevance_score_attr_name', 'twin_tower_relevance_score'),
    }


class PhotoValidCheckArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "photo_valid_check"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'path_name',
      'enable_photo_valid_check',
      'enable_photo_valid_check_use_ps',
      'e2e_twin_tower_config',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'photo_id',
    }

class LiveValidCheckArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_valid_check"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'path_name',
      'enable_live_valid_check',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'live_stream_id',
    }


class PhotoMergeArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "photo_merge"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'is_timeout_query',
      'is_hot_query',
      'ab_group_name',
      'org_query',
      'photo_ensemble_retr_group',
      'trigger_merge_limit',
      'enable_target_timeout_query_dynamic_quota',
      'enable_target_hot_query_dynamic_quota',
      'timeout_query_target_photo_ratio',
      'hot_query_target_photo_ratio',
      'photo_trigger_merge_quota_expand',
      'enable_relevance_ensemble_strategy_wise',
      'enable_relevance_photo_ensemble_sort',
      'enable_norm_photo_ensemble_sort',
      'enable_merge_dup_sort',
      'search_merge_dup_step',
      'disable_reciprocity_score',
      'enable_search_new_retrieval_weight',
      'search_info',
      'search_request_source',
      'enable_search_ad_box_single_col_config',
      'search_ad_box_single_col_config_group',
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      'photo_cnt_before_merge',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'photo_id',
      'counter_ecpm',
      'twin_tower_relevance_score',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'retrieval_weight',
      'recall_tags',
      'ensemble_score',
    }

class PhotoEcpmReleMergeArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "photo_ecpm_rele_merge"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'search_info',
      'is_timeout_query',
      'is_hot_query',
      'ab_group_name',
      'org_query',
      'photo_ensemble_retr_group',
      'trigger_merge_limit',
      'enable_target_timeout_query_dynamic_quota',
      'enable_target_hot_query_dynamic_quota',
      'timeout_query_target_photo_ratio',
      'hot_query_target_photo_ratio',
      'photo_trigger_merge_quota_expand',
      'search_ecpm_rele_weight',
      'search_merge_decay_ratio',
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      'photo_cnt_before_merge',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'photo_id',
      'counter_ecpm',
      'twin_tower_relevance_score',
      'twin_tower_ecpm_score',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'retrieval_weight',
      'recall_tags',
    }

class LiveMergeArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_merge"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'ab_group_name',
      'trigger_merge_limit',
      'search_info',
      'enable_search_bigv_card_in_shop_tab',
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      'live_cnt_before_merge',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'live_stream_id',
      'item_type',
      'sub_recall_strategy_type',
      'relevance_score',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'recall_tags',
    }


class AuthorMergeArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "author_merge"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'ab_group_name',
    }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      'author_cnt_before_merge',
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'user_id',
      'item_type',
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'retrieval_weight',
      'recall_tags',
      'ensemble_score',
    }

class PathLimitArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "path_limit"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      'search_info',
      'path_name',
      'total_quota',
    }


class LoadScoreArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "load_score"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      self._config['item_attr_name'],
    }

class AdxFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adx_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      self._config['ad_request_attr'],
      'max_searc_adx_result_num',
      'need_check_industry',
      'need_check_product',
      'need_check_account',
      'enable_adx_reco_negative_filter',
      'is_force_adx_ad',
      'adx_soft_max_creative_num',
      'adx_cpm_max_creative_num',
      *pos_manager_common_attrs,
    }
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      'ad_source_type',
      'adx_source_type',
      'bid_type',
      'filter_reason',
      'large_amount_creative',
      'creative_id',
      'industry_id_v3',
      'city_product_id',
      'author_id',
      'photo_id',
      'dup_photo_id',
      'cover_id',
      'dup_cover_id',
      'account_id',
    }
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      'filter_reason',
    }

class ItemKeyHashArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "item_key_hash"
