
#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from .adtarget_mixer import *


class AdTargetMixin(CommonLeafBaseMixin):
  def ad_target_process_prepare(self, **kwargs):
    """
    AdTargetProcessPrepare
    召回图化 Request 处理部分
    """
    self._add_processor(AdTargetProcessPrepareMixer(kwargs))
    return self

  def ad_target_get_ads_mixer(self, **kwargs):
    """
    AdTargetGetAdsMixer
    召回 pslab 未迁出部分
    """
    self._add_processor(AdTargetGetAdsMixer(kwargs))
    return self

  def ad_target_observe_mixer(self, **kwargs):
    """
    AdTargetObserveMixer
    response 返回后日志 打点
    """
    self._add_processor(AdTargetObserveMixer(kwargs))
    return self

  def ad_target_context_init(self, **kwargs):
    self._add_processor(ContextInit(kwargs))
    return self

  def ad_target_multi_param_parse(self, **kwargs):
    self._add_processor(MultiParamParse(kwargs))
    return self

  def ad_target_user_mining(self, **kwargs):
    self._add_processor(UserMining(kwargs))
    return self

  def ad_target_invert_target(self, **kwargs):
    self._add_processor(InvertTargeting(kwargs))
    return self

  def ad_target_recall(self, **kwargs):
    self._add_processor(Recall(kwargs))
    return self

  def ad_target_forward_search(self, **kwargs):
    self._add_processor(ForwardSearch(kwargs))
    return self

  def ad_target_rule_filter(self, **kwargs):
    self._add_processor(RuleFilter(kwargs))
    return self

  def ad_target_ranking(self, **kwargs):
    self._add_processor(Ranking(kwargs))
    return self

  def ad_target_dragon_bid_handler(self, **kwargs):
    self._add_processor(DragonBidHandler(kwargs))
    return self

  def ad_target_dragon_bid_handler_sync(self, **kwargs):
    self._add_processor(SyncDragonBidHandler(kwargs))
    return self

  def ad_target_preranking_prepare(self, **kwargs):
    self._add_processor(PreRankingPrepare(kwargs))
    return self

  def ad_target_preranking(self, **kwargs):
    self._add_processor(PreRanking(kwargs))
    return self

  def ad_target_retrieval_data_post_proc(self, **kwargs):
    self._add_processor(RetrievalDataPostproc(kwargs))
    return self

  def ad_target_tsm(self, **kwargs):
    self._add_processor(Tsm(kwargs))
    return self

  def ad_target_inner_merger(self, **kwargs):
    self._add_processor(InnerLoopMerge(kwargs))
    return self

  def ad_target_path_tag_enricher(self, **kwargs):
    self._add_processor(PathTagEnricher(kwargs))
    return self

  def ad_target_forward_tag_enricher(self, **kwargs):
    self._add_processor(ForwardTagEnricher(kwargs))
    return self

  def ad_target_inner_rule_filter(self, **kwargs):
    self._add_processor(InnerLoopRuleFilter(kwargs))
    return self

  def ad_target_amd_ranking(self, **kwargs):
    self._add_processor(AmdRanking(kwargs))
    return self

  def ad_target_inner_prerank_predict(self, **kwargs):
    self._add_processor(InnerLoopPrerankPredict(kwargs))
    return self

  def ad_target_inner_live_data_proc(self, **kwargs):
    self._add_processor(InnerLoopLivePostProc(kwargs))
    return self

  def ad_target_inner_bid_handler(self, **kwargs):
    self._add_processor(DragonInnerLoopBidHandler(kwargs))
    return self

  def ad_target_inner_prerank_strategy(self, **kwargs):
    self._add_processor(InnerLoopPrerankStrategy(kwargs))
    return self
  
  def ad_target_intersect_analysis(self, **kwargs):
    self._add_processor(IntersectAnalysis(kwargs))
    return self

  def universe_debug_invert_target(self, **kwargs):
    self._add_processor(UniverseDebugInvertTarget(kwargs))
    return self

  def ad_target_newbid_handler(self, **kwargs):
    self._add_processor(NewBidHandler(kwargs))
    return self

  def universe_preranking_prepare(self, **kwargs):
    self._add_processor(UniversePreRankingPrepare(kwargs))
    return self
  
  def universe_preranking_prepare_async(self, **kwargs):
    self._add_processor(UniversePreRankingPrepareAsync(kwargs))
    return self

  def universe_preranking(self, **kwargs):
    self._add_processor(UniversePreranking(kwargs))
    return self

  def archimedes_forward_search(self, **kwargs):
    self._add_processor(ArchimedesForwardSearch(kwargs))
    return self

  def archimedes_rule_filter(self, **kwargs):
    self._add_processor(ArchimedesRuleFilter(kwargs))
    return self

  def archimedes_prerank_strategy(self, **kwargs):
    self._add_processor(ArchimedesPrerankStrategy(kwargs))
    return self

  def archimedes_prerank_predict(self, **kwargs):
    self._add_processor(ArchimedesPrerankPredict(kwargs))
    return self

  def splash_dragon_bid_handler(self, **kwargs):
    self._add_processor(SplashDragonBidHandler(kwargs))
    return self

  def splash_check(self, **kwargs):
    self._add_processor(SplashCheck(kwargs))
    return self