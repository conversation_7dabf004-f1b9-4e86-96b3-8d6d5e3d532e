#!/usr/bin/env python3
# coding=utf-8
import os
import sys

from dragonfly.common_leaf_processor import LeafMixer
from dragonfly.common_leaf_util import check_arg, strict_types


class ParamPrepareMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tsm_param_prepare"

class PathCheckMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tsm_path_check"


class AutoParamMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tsm_auto_param"


class QuotaLimitMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tsm_quota_limit"
