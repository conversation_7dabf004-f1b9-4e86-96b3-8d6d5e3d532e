#!/usr/bin/env python3
# coding=utf-8
import os
import sys

from dragonfly.common_leaf_processor import LeafMix<PERSON>
from dragonfly.common_leaf_util import check_arg, strict_types


class AdTargetProcessPrepareMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_process_prepare"

# 创新业务线删除不了先留着
class AdTargetGetAdsMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_get_ads_mixer"


class AdTargetObserveMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_observe_mixer"


class ContextInit(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_context_init"


class MultiParamParse(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_multi_param_parse"


class UserMining(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_user_mining"


class InvertTargeting(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_invert_target"


class Recall(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_recall"


class ForwardSearch(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_forward_search"


class RuleFilter(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_rule_filter"


class Ranking(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_ranking"


class DragonBidHandler(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_dragon_bid_handler"
  
class SyncDragonBidHandler(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_dragon_bid_handler_sync"

class PreRankingPrepare(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_preranking_prepare"


class PreRanking(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_preranking"


class RetrievalDataPostproc(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_retrieval_data_post_proc"

class Tsm(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_tsm"

class InnerLoopMerge(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_inner_merger"

class PathTagEnricher(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_path_tag_enricher"

class ForwardTagEnricher(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_forward_tag_enricher"

class InnerLoopRuleFilter(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_inner_rule_filter"


class AmdRanking(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_amd_ranking"

class InnerLoopPrerankPredict(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_inner_prerank_predict"

class InnerLoopPrerankStrategy(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_inner_prerank_strategy"

class DragonInnerLoopBidHandler(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_inner_bid_handler" 

class InnerLoopLivePostProc(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_inner_live_data_proc" 

class IntersectAnalysis(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_intersect_analysis"

class UniverseDebugInvertTarget(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_debug_invert_target"

class NewBidHandler(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_target_newbid_handler"

class UniversePreRankingPrepare(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_preranking_prepare"
  
class UniversePreRankingPrepareAsync(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_preranking_prepare_async"

class UniversePreranking(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_preranking"

class ArchimedesForwardSearch(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "archimedes_forward_search"

class ArchimedesRuleFilter(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "archimedes_rule_filter"

class ArchimedesPrerankStrategy(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "archimedes_prerank_strategy"

class ArchimedesPrerankPredict(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "archimedes_prerank_predict"
  
class SplashCheck(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "splash_check"
  
class SplashDragonBidHandler(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "splash_dragon_bid_handler"
