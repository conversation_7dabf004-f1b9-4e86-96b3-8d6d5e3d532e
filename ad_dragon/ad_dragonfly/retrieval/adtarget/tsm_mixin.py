
#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from .tsm_mixer import *


class AdTsmMixin(CommonLeafBaseMixin):
  def tsm_param_prepare(self, **kwargs):
    self._add_processor(ParamPrepareMixer(kwargs))
    return self

  def tsm_path_check(self, **kwargs):
    self._add_processor(PathCheckMixer(kwargs))
    return self

  def tsm_auto_param(self, **kwargs):
    self._add_processor(AutoParamMixer(kwargs))
    return self

  def tsm_quota_limit(self, **kwargs):
    self._add_processor(QuotaLimitMixer(kwargs))
    return self
