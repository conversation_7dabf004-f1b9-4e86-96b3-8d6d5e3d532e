{"_DRAGONFLY_VERSION": "0.7.17", "_DRAGONFLY_CREATE_TIME": "2023-09-23 00:16:55", "pipeline_manager_config": {"base_pipeline": {"type_name": "CommonRecoPipeline", "processor": {"calc_time_cost_s": {"save_elapsed_time_to_attr": "TimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["TimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_log_prepare::calc_time_cost_s": {"save_elapsed_time_to_attr": "ad_log_prepareTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["ad_log_prepareTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_log_prepare::calc_time_cost_e": {"save_elapsed_time_to_attr": "ad_log_prepareTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["ad_log_prepareTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_log_prepare::calc_time_cost": {"import_common_attr": ["ad_log_prepareTimeCostEnd", "ad_log_prepareTimeCostStart"], "export_common_attr": ["ad_log_prepareTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (ad_log_prepareTimeCostEnd - ad_log_prepareTimeCostStart) end", "$metadata": {"$input_common_attrs": ["ad_log_prepareTimeCostEnd", "ad_log_prepareTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["ad_log_prepareTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "ad_log_prepare::perf_time_cost": {"check_point": "module:ad_log_prepare", "perf_base": 1, "common_attrs": ["ad_log_prepareTimeCost"], "$metadata": {"$input_common_attrs": ["ad_log_prepareTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "ad_log_retrieve::calc_time_cost_s": {"save_elapsed_time_to_attr": "ad_log_retrieveTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["ad_log_retrieveTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_log_retrieve::fectchDataFrameFromKafka": {"kafka_consume_kconf_path": "adI18n.model_calibrate.adLogKafkaConsumeConfig", "use_shard": true, "kafka_tag_kconf_path": "adI18n.model_calibrate.adLogKafkaTagConfig", "out_column": "ad_log_dataframe_proto", "msg_timestamp_column": "kafka_msg_fetch_timestamp", "master_flag_column": "zk_master_stat", "msg_type": "ad_log_msg", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["ad_log_dataframe_proto"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdKafkaMessageFetchEnricher"}, "ad_log_retrieve::retrievalDataFrame": {"item_table": "triggerTable", "from_column": "ad_log_dataframe_proto", "msg_timestamp_column": "kafka_msg_fetch_timestamp", "column_extract": [{"origin": "key_prefix", "target": "key_prefix"}, {"origin": "key_info", "target": "key_info"}, {"origin": "key_hash", "target": "key_hash"}, {"origin": "cmd_id", "target": "cmd_id"}, {"origin": "ocpx_action_type", "target": "ocpx_action_type"}, {"origin": "app_id", "target": "app_id"}, {"origin": "page_id", "target": "page_id"}, {"origin": "dimension_type", "target": "dimension_type"}, {"origin": "dimension_id", "target": "dimension_id"}, {"origin": "sample_type", "target": "sample_type"}, {"origin": "event_type", "target": "event_type"}, {"origin": "ad_item_impression", "target": "ad_item_impression"}, {"origin": "ad_item_click", "target": "ad_item_click"}, {"origin": "has_ad_click", "target": "has_ad_click"}, {"origin": "impression_timestamp", "target": "impression_timestamp"}, {"origin": "event_server_timestamp", "target": "event_server_timestamp"}, {"origin": "event_server_time_window", "target": "event_server_time_window"}, {"origin": "start_event_timestamp", "target": "start_event_timestamp"}, {"origin": "start_event_time_window", "target": "start_event_time_window"}, {"origin": "start_event_num", "target": "start_event_num"}, {"origin": "end_event_num", "target": "end_event_num"}, {"origin": "conv_rate_sum", "target": "conv_rate_sum"}, {"origin": "bucket_index_array", "target": "bucket_index_array"}, {"origin": "bucket_start_event_num_array", "target": "bucket_start_event_num_array"}, {"origin": "bucket_end_event_num_array", "target": "bucket_end_event_num_array"}, {"origin": "bucket_conv_rate_sum_array", "target": "bucket_conv_rate_sum_array"}], "$metadata": {"$input_common_attrs": ["ad_log_dataframe_proto", "kafka_msg_fetch_timestamp", "triggerTable"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": ["triggerTable::ad_item_click", "triggerTable::ad_item_impression", "triggerTable::app_id", "triggerTable::bucket_conv_rate_sum_array", "triggerTable::bucket_end_event_num_array", "triggerTable::bucket_index_array", "triggerTable::bucket_start_event_num_array", "triggerTable::cmd_id", "triggerTable::conv_rate_sum", "triggerTable::dimension_id", "triggerTable::dimension_type", "triggerTable::end_event_num", "triggerTable::event_server_time_window", "triggerTable::event_server_timestamp", "triggerTable::event_type", "triggerTable::has_ad_click", "triggerTable::impression_timestamp", "triggerTable::key_hash", "triggerTable::key_info", "triggerTable::key_prefix", "triggerTable::ocpx_action_type", "triggerTable::page_id", "triggerTable::sample_type", "triggerTable::start_event_num", "triggerTable::start_event_time_window", "triggerTable::start_event_timestamp"], "$modify_item_tables": []}, "type_name": "AdDeserializeProtoDataframeRetriever"}, "ad_log_retrieve::setDefaultValue": {"item_table": "triggerTable", "item_attrs": [{"name": "trigger_type", "type": "int", "value": 1}], "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": ["trigger_type"], "$modify_item_tables": []}, "type_name": "CommonRecoAttrDefaultValueEnricher"}, "ad_log_retrieve::calc_time_cost_e": {"save_elapsed_time_to_attr": "ad_log_retrieveTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["ad_log_retrieveTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_log_retrieve::calc_time_cost": {"import_common_attr": ["ad_log_retrieveTimeCostEnd", "ad_log_retrieveTimeCostStart"], "export_common_attr": ["ad_log_retrieveTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (ad_log_retrieveTimeCostEnd - ad_log_retrieveTimeCostStart) end", "$metadata": {"$input_common_attrs": ["ad_log_retrieveTimeCostEnd", "ad_log_retrieveTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["ad_log_retrieveTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "ad_log_retrieve::perf_time_cost": {"check_point": "module:ad_log_retrieve", "perf_base": 1, "common_attrs": ["ad_log_retrieveTimeCost"], "$metadata": {"$input_common_attrs": ["ad_log_retrieveTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "ad_log_filter::calc_time_cost_s": {"save_elapsed_time_to_attr": "ad_log_filterTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["ad_log_filterTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_log_filter::calc_time_cost_e": {"save_elapsed_time_to_attr": "ad_log_filterTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["ad_log_filterTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_log_filter::calc_time_cost": {"import_common_attr": ["ad_log_filterTimeCostEnd", "ad_log_filterTimeCostStart"], "export_common_attr": ["ad_log_filterTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (ad_log_filterTimeCostEnd - ad_log_filterTimeCostStart) end", "$metadata": {"$input_common_attrs": ["ad_log_filterTimeCostEnd", "ad_log_filterTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["ad_log_filterTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "ad_log_filter::perf_time_cost": {"check_point": "module:ad_log_filter", "perf_base": 1, "common_attrs": ["ad_log_filterTimeCost"], "$metadata": {"$input_common_attrs": ["ad_log_filterTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "ad_log_dispatcher::calc_time_cost_s": {"save_elapsed_time_to_attr": "ad_log_dispatcherTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["ad_log_dispatcherTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_log_dispatcher::_branch_controller_7329C5E1": {"import_common_attr": ["zk_master_stat"], "export_common_attr": ["_if_control_attr_1"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (zk_master_stat == 1) then return false else return true end end", "for_branch_control": true, "$branch_start": "ad_log_dispatcher::_branch_controller_7329C5E1", "$code_info": "[if] 4A2C857D base_module.py:44 in process(): self.flow.if_(self.enable_condition)", "$metadata": {"$input_common_attrs": ["zk_master_stat"], "$input_item_attrs": [], "$output_common_attrs": ["_if_control_attr_1"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "ad_log_dispatcher::_branch_controller_582BDAB9": {"import_common_attr": ["_if_control_attr_1", "check_table_empty"], "export_common_attr": ["_if_control_attr_2"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (_if_control_attr_1 == 0 and (check_table_empty == 1)) then return false else return true end end", "for_branch_control": true, "$branch_start": "ad_log_dispatcher::_branch_controller_582BDAB9", "$code_info": "[if] EC261A6F ad_log_dispatcher_module.py:38 in _calc_shard_id(): .if_(\"check_table_empty == 1\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "check_table_empty"], "$input_item_attrs": [], "$output_common_attrs": ["_if_control_attr_2"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "ad_log_dispatcher::calc_time_cost_e": {"save_elapsed_time_to_attr": "ad_log_dispatcherTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["ad_log_dispatcherTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_log_dispatcher::calc_time_cost": {"import_common_attr": ["ad_log_dispatcherTimeCostEnd", "ad_log_dispatcherTimeCostStart"], "export_common_attr": ["ad_log_dispatcherTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (ad_log_dispatcherTimeCostEnd - ad_log_dispatcherTimeCostStart) end", "$metadata": {"$input_common_attrs": ["ad_log_dispatcherTimeCostEnd", "ad_log_dispatcherTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["ad_log_dispatcherTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "ad_log_dispatcher::perf_time_cost": {"check_point": "module:ad_log_dispatcher", "perf_base": 1, "common_attrs": ["ad_log_dispatcherTimeCost"], "$metadata": {"$input_common_attrs": ["ad_log_dispatcherTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "calc_time_cost_e": {"save_elapsed_time_to_attr": "TimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["TimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "calc_time_cost": {"import_common_attr": ["TimeCostEnd", "TimeCostStart"], "export_common_attr": ["TimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (TimeCostEnd - TimeCostStart) end", "$metadata": {"$input_common_attrs": ["TimeCostEnd", "TimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["TimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "perf_time_cost": {"check_point": "module:", "perf_base": 1, "common_attrs": ["TimeCost"], "$metadata": {"$input_common_attrs": ["TimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "ad_log_dispatcher::ad_message_queue_dispatch_mixer_EB5445": {"task_queue_id": "{{subflow_shard_id}}", "message_queue_type": 2, "packed_common_attrs": ["diff_test_env", "current_shard_id", "zk_backup_stat", "zk_master_stat", "tag_index_column", "total_tag_num_column", "current_tag_num_column", "ad_log_dataframe_proto", "kafka_msg_fetch_timestamp", "cur_time_ms", "subflow_shard_id", "check_table_empty", "message_enqueue_success"], "packed_table_columns": [{"table_name": "triggerTable", "columns": ["key_prefix", "key_info", "key_hash", "cmd_id", "ocpx_action_type", "app_id", "page_id", "dimension_type", "dimension_id", "sample_type", "event_type", "ad_item_impression", "ad_item_click", "has_ad_click", "impression_timestamp", "event_server_timestamp", "event_server_time_window", "start_event_timestamp", "start_event_time_window", "start_event_num", "end_event_num", "conv_rate_sum", "bucket_index_array", "bucket_start_event_num_array", "bucket_end_event_num_array", "bucket_conv_rate_sum_array", "trigger_type", "invalid_data"]}], "skip": "{{_if_control_attr_1}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "ad_log_dataframe_proto", "check_table_empty", "cur_time_ms", "current_shard_id", "current_tag_num_column", "diff_test_env", "kafka_msg_fetch_timestamp", "message_enqueue_success", "subflow_shard_id", "tag_index_column", "total_tag_num_column", "zk_backup_stat", "zk_master_stat"], "$input_item_attrs": ["triggerTable::ad_item_click", "triggerTable::ad_item_impression", "triggerTable::app_id", "triggerTable::bucket_conv_rate_sum_array", "triggerTable::bucket_end_event_num_array", "triggerTable::bucket_index_array", "triggerTable::bucket_start_event_num_array", "triggerTable::cmd_id", "triggerTable::conv_rate_sum", "triggerTable::dimension_id", "triggerTable::dimension_type", "triggerTable::end_event_num", "triggerTable::event_server_time_window", "triggerTable::event_server_timestamp", "triggerTable::event_type", "triggerTable::has_ad_click", "triggerTable::impression_timestamp", "triggerTable::invalid_data", "triggerTable::key_hash", "triggerTable::key_info", "triggerTable::key_prefix", "triggerTable::ocpx_action_type", "triggerTable::page_id", "triggerTable::sample_type", "triggerTable::start_event_num", "triggerTable::start_event_time_window", "triggerTable::start_event_timestamp", "triggerTable::trigger_type"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "MessageQueueDispatchMixer"}, "ad_log_dispatcher::return__6A0259": {"status_code": 0, "skip": "{{_if_control_attr_2}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_2"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoExecutionStatusEnricher"}, "ad_log_dispatcher::ad_subflow_shard_id_enricher_4BA531": {"item_table": "triggerTable", "mod_dividend_column": "start_event_timestamp", "mod_base": 100, "output_column": "subflow_shard_id", "check_table_empty": "check_table_empty", "ad_log_ocpm_trigger": false, "skip": "{{_if_control_attr_1}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": ["start_event_timestamp"], "$output_common_attrs": ["subflow_shard_id"], "$output_item_attrs": ["subflow_shard_id"], "$modify_item_tables": []}, "type_name": "AdSubflowShardIdEnricher"}, "ad_log_filter::filter_by_rule_1AA725": {"item_table": "triggerTable", "rule": {"attr_name": "invalid_data", "remove_if": "==", "compare_to": 1}, "$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["invalid_data"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": ["triggerTable"]}, "type_name": "CommonRecoRuleFilterArranger"}, "ad_log_filter::enrich_attr_by_lua_108350": {"item_table": "triggerTable", "import_common_attr": ["cur_time_ms"], "import_item_attr": ["start_event_timestamp"], "function_for_item": "calculate", "export_item_attr": ["invalid_data"], "lua_script": "function calculate()\n\t\t\t\t\t\t\t\t\tlocal max_timestamp = cur_time_ms  / 1000 - 216000\n\t\t\t\t\t\t\t\t\tlocal invalid_data = max_timestamp > start_event_timestamp\n\t\t\t\t\t\t\t\t\treturn invalid_data\n\t\t\t\t\t\t\t\tend", "$metadata": {"$input_common_attrs": ["cur_time_ms"], "$input_item_attrs": ["start_event_timestamp"], "$output_common_attrs": [], "$output_item_attrs": ["invalid_data"], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "ad_log_filter::copy_user_meta_info_23DDF3": {"save_current_time_ms_to_attr": "cur_time_ms", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["cur_time_ms"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_log_prepare::ad_runtime_env_enricher_399F20": {"run_env_out_column": "diff_test_env", "shard_id_out_column": "current_shard_id", "zk_configs": [{"dynamic_config_node": "zk_model_calibrate_server_main", "out_column": "zk_master_stat", "config_node_concat_shard_id": true}], "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["current_shard_id"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdRuntimeEnvEnricher"}, "retrieval::calc_time_cost_s": {"save_elapsed_time_to_attr": "retrievalTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["retrievalTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "retrieval::message_queue_retrieval::calc_time_cost_s": {"save_elapsed_time_to_attr": "retrieval__message_queue_retrievalTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["retrieval__message_queue_retrievalTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "retrieval::message_queue_retrieval::calc_time_cost_e": {"save_elapsed_time_to_attr": "retrieval__message_queue_retrievalTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["retrieval__message_queue_retrievalTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "retrieval::message_queue_retrieval::calc_time_cost": {"import_common_attr": ["retrieval__message_queue_retrievalTimeCostEnd", "retrieval__message_queue_retrievalTimeCostStart"], "export_common_attr": ["retrieval__message_queue_retrievalTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (retrieval__message_queue_retrievalTimeCostEnd - retrieval__message_queue_retrievalTimeCostStart) end", "$metadata": {"$input_common_attrs": ["retrieval__message_queue_retrievalTimeCostEnd", "retrieval__message_queue_retrievalTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["retrieval__message_queue_retrievalTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "retrieval::message_queue_retrieval::perf_time_cost": {"check_point": "module:retrieval__message_queue_retrieval", "perf_base": 1, "common_attrs": ["retrieval__message_queue_retrievalTimeCost"], "$metadata": {"$input_common_attrs": ["retrieval__message_queue_retrievalTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "retrieval::calc_time_cost_e": {"save_elapsed_time_to_attr": "retrievalTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["retrievalTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "retrieval::calc_time_cost": {"import_common_attr": ["retrievalTimeCostEnd", "retrievalTimeCostStart"], "export_common_attr": ["retrievalTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (retrievalTimeCostEnd - retrievalTimeCostStart) end", "$metadata": {"$input_common_attrs": ["retrievalTimeCostEnd", "retrievalTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["retrievalTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "retrieval::perf_time_cost": {"check_point": "module:retrieval", "perf_base": 1, "common_attrs": ["retrievalTimeCost"], "$metadata": {"$input_common_attrs": ["retrievalTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::calc_time_cost_s": {"save_elapsed_time_to_attr": "trainingTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["trainingTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__ad_log_trainingTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_trainingTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::training_data_read::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__ad_log_training__training_data_readTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_readTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::training_data_read::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__ad_log_training__training_data_readTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_readTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::training_data_read::calc_time_cost": {"import_common_attr": ["training__ad_log_training__training_data_readTimeCostEnd", "training__ad_log_training__training_data_readTimeCostStart"], "export_common_attr": ["training__ad_log_training__training_data_readTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__ad_log_training__training_data_readTimeCostEnd - training__ad_log_training__training_data_readTimeCostStart) end", "$metadata": {"$input_common_attrs": ["training__ad_log_training__training_data_readTimeCostEnd", "training__ad_log_training__training_data_readTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_readTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::training_data_read::perf_time_cost": {"check_point": "module:training__ad_log_training__training_data_read", "perf_base": 1, "common_attrs": ["training__ad_log_training__training_data_readTimeCost"], "$metadata": {"$input_common_attrs": ["training__ad_log_training__training_data_readTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::ad_log_training::delay_training_data_accumulator::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__ad_log_training__delay_training_data_accumulatorTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__delay_training_data_accumulatorTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::delay_training_data_accumulator::_branch_controller_11463360": {"import_common_attr": [], "export_common_attr": ["_if_control_attr_4"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (true) then return false else return true end end", "for_branch_control": true, "$branch_start": "training::ad_log_training::delay_training_data_accumulator::_branch_controller_11463360", "$code_info": "[if] B326B506 base_module.py:44 in process(): self.flow.if_(self.enable_condition)", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["_if_control_attr_4"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::delay_training_data_accumulator::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__ad_log_training__delay_training_data_accumulatorTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__delay_training_data_accumulatorTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::delay_training_data_accumulator::calc_time_cost": {"import_common_attr": ["training__ad_log_training__delay_training_data_accumulatorTimeCostEnd", "training__ad_log_training__delay_training_data_accumulatorTimeCostStart"], "export_common_attr": ["training__ad_log_training__delay_training_data_accumulatorTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__ad_log_training__delay_training_data_accumulatorTimeCostEnd - training__ad_log_training__delay_training_data_accumulatorTimeCostStart) end", "$metadata": {"$input_common_attrs": ["training__ad_log_training__delay_training_data_accumulatorTimeCostEnd", "training__ad_log_training__delay_training_data_accumulatorTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__delay_training_data_accumulatorTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::delay_training_data_accumulator::perf_time_cost": {"check_point": "module:training__ad_log_training__delay_training_data_accumulator", "perf_base": 1, "common_attrs": ["training__ad_log_training__delay_training_data_accumulatorTimeCost"], "$metadata": {"$input_common_attrs": ["training__ad_log_training__delay_training_data_accumulatorTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::ad_log_training::simple_training_data_accumulator::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__ad_log_training__simple_training_data_accumulatorTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__simple_training_data_accumulatorTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::simple_training_data_accumulator::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__ad_log_training__simple_training_data_accumulatorTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__simple_training_data_accumulatorTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::simple_training_data_accumulator::calc_time_cost": {"import_common_attr": ["training__ad_log_training__simple_training_data_accumulatorTimeCostEnd", "training__ad_log_training__simple_training_data_accumulatorTimeCostStart"], "export_common_attr": ["training__ad_log_training__simple_training_data_accumulatorTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__ad_log_training__simple_training_data_accumulatorTimeCostEnd - training__ad_log_training__simple_training_data_accumulatorTimeCostStart) end", "$metadata": {"$input_common_attrs": ["training__ad_log_training__simple_training_data_accumulatorTimeCostEnd", "training__ad_log_training__simple_training_data_accumulatorTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__simple_training_data_accumulatorTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::simple_training_data_accumulator::perf_time_cost": {"check_point": "module:training__ad_log_training__simple_training_data_accumulator", "perf_base": 1, "common_attrs": ["training__ad_log_training__simple_training_data_accumulatorTimeCost"], "$metadata": {"$input_common_attrs": ["training__ad_log_training__simple_training_data_accumulatorTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::ad_log_training::training_data_expired::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__ad_log_training__training_data_expiredTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_expiredTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::training_data_expired::get_kconf_params": {"kconf_configs": [{"kconf_key": "adI18n.model_calibrate.enableClearPeriod", "export_common_attr": "enable_clear_period", "value_type": "bool"}, {"kconf_key": "adI18n.model_calibrate.clearStartTimeSec", "export_common_attr": "start_time_sec", "default_value": -1, "value_type": "int"}, {"kconf_key": "adI18n.model_calibrate.clearEndTimeSec", "export_common_attr": "end_time_sec", "default_value": -1, "value_type": "int"}], "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["enable_clear_period", "end_time_sec", "start_time_sec"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoKconfCommonAttrEnricher"}, "training::ad_log_training::training_data_expired::_branch_controller_75EBBAEA": {"import_common_attr": [], "export_common_attr": ["_if_control_attr_5"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (true) then return false else return true end end", "for_branch_control": true, "$branch_start": "training::ad_log_training::training_data_expired::_branch_controller_75EBBAEA", "$code_info": "[if] B326B506 base_module.py:44 in process(): self.flow.if_(self.enable_condition)", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["_if_control_attr_5"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::training_data_expired::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__ad_log_training__training_data_expiredTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_expiredTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::training_data_expired::calc_time_cost": {"import_common_attr": ["training__ad_log_training__training_data_expiredTimeCostEnd", "training__ad_log_training__training_data_expiredTimeCostStart"], "export_common_attr": ["training__ad_log_training__training_data_expiredTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__ad_log_training__training_data_expiredTimeCostEnd - training__ad_log_training__training_data_expiredTimeCostStart) end", "$metadata": {"$input_common_attrs": ["training__ad_log_training__training_data_expiredTimeCostEnd", "training__ad_log_training__training_data_expiredTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_expiredTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::training_data_expired::perf_time_cost": {"check_point": "module:training__ad_log_training__training_data_expired", "perf_base": 1, "common_attrs": ["training__ad_log_training__training_data_expiredTimeCost"], "$metadata": {"$input_common_attrs": ["training__ad_log_training__training_data_expiredTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::ad_log_training::training_data_check::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__ad_log_training__training_data_checkTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_checkTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::training_data_check::get_kconf_params": {"kconf_configs": [{"kconf_key": "adI18n.model_calibrate.inputNumberTriggerSwitch", "export_common_attr": "input_number_trigger_switch", "value_type": "bool"}, {"kconf_key": "adI18n.model_calibrate.inputMaxImpressionNum", "export_common_attr": "input_max_impression_num", "value_type": "int"}, {"kconf_key": "adI18n.model_calibrate.inputMaxConvNum", "export_common_attr": "input_max_conv_num", "value_type": "int"}], "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["input_max_conv_num", "input_max_impression_num", "input_number_trigger_switch"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoKconfCommonAttrEnricher"}, "training::ad_log_training::training_data_check::_branch_controller_36F9D8B0": {"import_common_attr": [], "export_common_attr": ["_if_control_attr_6"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (true) then return false else return true end end", "for_branch_control": true, "$branch_start": "training::ad_log_training::training_data_check::_branch_controller_36F9D8B0", "$code_info": "[if] B326B506 base_module.py:44 in process(): self.flow.if_(self.enable_condition)", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["_if_control_attr_6"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::training_data_check::_branch_controller_C564371C": {"import_common_attr": ["_if_control_attr_6", "item_num"], "export_common_attr": ["_if_control_attr_7"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (_if_control_attr_6 == 0 and (item_num == 0)) then return false else return true end end", "for_branch_control": true, "$branch_start": "training::ad_log_training::training_data_check::_branch_controller_C564371C", "$code_info": "[if] 0023D917 training_data_check_module.py:26 in _process(): .if_('item_num == 0')", "$metadata": {"$input_common_attrs": ["_if_control_attr_6", "item_num"], "$input_item_attrs": [], "$output_common_attrs": ["_if_control_attr_7"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::training_data_check::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__ad_log_training__training_data_checkTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_checkTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::training_data_check::calc_time_cost": {"import_common_attr": ["training__ad_log_training__training_data_checkTimeCostEnd", "training__ad_log_training__training_data_checkTimeCostStart"], "export_common_attr": ["training__ad_log_training__training_data_checkTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__ad_log_training__training_data_checkTimeCostEnd - training__ad_log_training__training_data_checkTimeCostStart) end", "$metadata": {"$input_common_attrs": ["training__ad_log_training__training_data_checkTimeCostEnd", "training__ad_log_training__training_data_checkTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_checkTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::training_data_check::perf_time_cost": {"check_point": "module:training__ad_log_training__training_data_check", "perf_base": 1, "common_attrs": ["training__ad_log_training__training_data_checkTimeCost"], "$metadata": {"$input_common_attrs": ["training__ad_log_training__training_data_checkTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::ad_log_training::delay_training_data_calculate::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__ad_log_training__delay_training_data_calculateTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__delay_training_data_calculateTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::delay_training_data_calculate::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__ad_log_training__delay_training_data_calculateTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__delay_training_data_calculateTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::delay_training_data_calculate::calc_time_cost": {"import_common_attr": ["training__ad_log_training__delay_training_data_calculateTimeCostEnd", "training__ad_log_training__delay_training_data_calculateTimeCostStart"], "export_common_attr": ["training__ad_log_training__delay_training_data_calculateTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__ad_log_training__delay_training_data_calculateTimeCostEnd - training__ad_log_training__delay_training_data_calculateTimeCostStart) end", "$metadata": {"$input_common_attrs": ["training__ad_log_training__delay_training_data_calculateTimeCostEnd", "training__ad_log_training__delay_training_data_calculateTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__delay_training_data_calculateTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::delay_training_data_calculate::perf_time_cost": {"check_point": "module:training__ad_log_training__delay_training_data_calculate", "perf_base": 1, "common_attrs": ["training__ad_log_training__delay_training_data_calculateTimeCost"], "$metadata": {"$input_common_attrs": ["training__ad_log_training__delay_training_data_calculateTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::ad_log_training::simple_training_data_calculate::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__ad_log_training__simple_training_data_calculateTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__simple_training_data_calculateTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::simple_training_data_calculate::get_kconf_params": {"kconf_configs": [{"kconf_key": "adI18n.model_calibrate.maxCxrScoreInfoListSize", "export_common_attr": "max_cxr_score_info_list_size", "default_value": 20, "value_type": "int"}, {"kconf_key": "adI18n.model_calibrate.confidenceThreshold", "export_common_attr": "confidence_threshold", "default_value": 20, "value_type": "int"}], "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["confidence_threshold", "max_cxr_score_info_list_size"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoKconfCommonAttrEnricher"}, "training::ad_log_training::simple_training_data_calculate::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__ad_log_training__simple_training_data_calculateTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__simple_training_data_calculateTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::simple_training_data_calculate::calc_time_cost": {"import_common_attr": ["training__ad_log_training__simple_training_data_calculateTimeCostEnd", "training__ad_log_training__simple_training_data_calculateTimeCostStart"], "export_common_attr": ["training__ad_log_training__simple_training_data_calculateTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__ad_log_training__simple_training_data_calculateTimeCostEnd - training__ad_log_training__simple_training_data_calculateTimeCostStart) end", "$metadata": {"$input_common_attrs": ["training__ad_log_training__simple_training_data_calculateTimeCostEnd", "training__ad_log_training__simple_training_data_calculateTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__simple_training_data_calculateTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::simple_training_data_calculate::perf_time_cost": {"check_point": "module:training__ad_log_training__simple_training_data_calculate", "perf_base": 1, "common_attrs": ["training__ad_log_training__simple_training_data_calculateTimeCost"], "$metadata": {"$input_common_attrs": ["training__ad_log_training__simple_training_data_calculateTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::ad_log_training::training_data_send::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__ad_log_training__training_data_sendTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_sendTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::training_data_send::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__ad_log_training__training_data_sendTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_sendTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::training_data_send::calc_time_cost": {"import_common_attr": ["training__ad_log_training__training_data_sendTimeCostEnd", "training__ad_log_training__training_data_sendTimeCostStart"], "export_common_attr": ["training__ad_log_training__training_data_sendTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__ad_log_training__training_data_sendTimeCostEnd - training__ad_log_training__training_data_sendTimeCostStart) end", "$metadata": {"$input_common_attrs": ["training__ad_log_training__training_data_sendTimeCostEnd", "training__ad_log_training__training_data_sendTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_sendTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::training_data_send::perf_time_cost": {"check_point": "module:training__ad_log_training__training_data_send", "perf_base": 1, "common_attrs": ["training__ad_log_training__training_data_sendTimeCost"], "$metadata": {"$input_common_attrs": ["training__ad_log_training__training_data_sendTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::ad_log_training::training_data_save::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__ad_log_training__training_data_saveTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_saveTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::training_data_save::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__ad_log_training__training_data_saveTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_saveTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::training_data_save::calc_time_cost": {"import_common_attr": ["training__ad_log_training__training_data_saveTimeCostEnd", "training__ad_log_training__training_data_saveTimeCostStart"], "export_common_attr": ["training__ad_log_training__training_data_saveTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__ad_log_training__training_data_saveTimeCostEnd - training__ad_log_training__training_data_saveTimeCostStart) end", "$metadata": {"$input_common_attrs": ["training__ad_log_training__training_data_saveTimeCostEnd", "training__ad_log_training__training_data_saveTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_training__training_data_saveTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::training_data_save::perf_time_cost": {"check_point": "module:training__ad_log_training__training_data_save", "perf_base": 1, "common_attrs": ["training__ad_log_training__training_data_saveTimeCost"], "$metadata": {"$input_common_attrs": ["training__ad_log_training__training_data_saveTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::ad_log_training::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__ad_log_trainingTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_trainingTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::ad_log_training::calc_time_cost": {"import_common_attr": ["training__ad_log_trainingTimeCostEnd", "training__ad_log_trainingTimeCostStart"], "export_common_attr": ["training__ad_log_trainingTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__ad_log_trainingTimeCostEnd - training__ad_log_trainingTimeCostStart) end", "$metadata": {"$input_common_attrs": ["training__ad_log_trainingTimeCostEnd", "training__ad_log_trainingTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__ad_log_trainingTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::perf_time_cost": {"check_point": "module:training__ad_log_training", "perf_base": 1, "common_attrs": ["training__ad_log_trainingTimeCost"], "$metadata": {"$input_common_attrs": ["training__ad_log_trainingTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::update_training::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__update_trainingTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__update_trainingTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::update_training::_branch_controller_49A07694": {"import_common_attr": ["ad_calibrate_trigger_type"], "export_common_attr": ["_if_control_attr_8"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (ad_calibrate_trigger_type == 2) then return false else return true end end", "for_branch_control": true, "$branch_start": "training::update_training::_branch_controller_49A07694", "$code_info": "[if] 57D06463 base_module.py:44 in process(): self.flow.if_(self.enable_condition)", "$metadata": {"$input_common_attrs": ["ad_calibrate_trigger_type"], "$input_item_attrs": [], "$output_common_attrs": ["_if_control_attr_8"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::update_training::training_data_read::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__update_training__training_data_readTimeCostStart", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__training_data_readTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::update_training::training_data_read::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__update_training__training_data_readTimeCostEnd", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__training_data_readTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::update_training::training_data_read::calc_time_cost": {"import_common_attr": ["training__update_training__training_data_readTimeCostEnd", "training__update_training__training_data_readTimeCostStart"], "export_common_attr": ["training__update_training__training_data_readTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__update_training__training_data_readTimeCostEnd - training__update_training__training_data_readTimeCostStart) end", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "training__update_training__training_data_readTimeCostEnd", "training__update_training__training_data_readTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__training_data_readTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::update_training::training_data_read::perf_time_cost": {"check_point": "module:training__update_training__training_data_read", "perf_base": 1, "common_attrs": ["training__update_training__training_data_readTimeCost"], "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "training__update_training__training_data_readTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::update_training::training_data_expired::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__update_training__training_data_expiredTimeCostStart", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__training_data_expiredTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::update_training::training_data_expired::get_kconf_params": {"kconf_configs": [{"kconf_key": "adI18n.model_calibrate.enableClearPeriod", "export_common_attr": "enable_clear_period", "value_type": "bool"}, {"kconf_key": "adI18n.model_calibrate.clearStartTimeSec", "export_common_attr": "start_time_sec", "default_value": -1, "value_type": "int"}, {"kconf_key": "adI18n.model_calibrate.clearEndTimeSec", "export_common_attr": "end_time_sec", "default_value": -1, "value_type": "int"}], "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$output_common_attrs": ["enable_clear_period", "end_time_sec", "start_time_sec"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoKconfCommonAttrEnricher"}, "training::update_training::training_data_expired::_branch_controller_2952577C": {"import_common_attr": ["_if_control_attr_8"], "export_common_attr": ["_if_control_attr_9"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (_if_control_attr_8 == 0 and (true)) then return false else return true end end", "for_branch_control": true, "$branch_start": "training::update_training::training_data_expired::_branch_controller_2952577C", "$code_info": "[if] E04E77B3 base_module.py:44 in process(): self.flow.if_(self.enable_condition)", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$output_common_attrs": ["_if_control_attr_9"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::update_training::training_data_expired::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__update_training__training_data_expiredTimeCostEnd", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__training_data_expiredTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::update_training::training_data_expired::calc_time_cost": {"import_common_attr": ["training__update_training__training_data_expiredTimeCostEnd", "training__update_training__training_data_expiredTimeCostStart"], "export_common_attr": ["training__update_training__training_data_expiredTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__update_training__training_data_expiredTimeCostEnd - training__update_training__training_data_expiredTimeCostStart) end", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "training__update_training__training_data_expiredTimeCostEnd", "training__update_training__training_data_expiredTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__training_data_expiredTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::update_training::training_data_expired::perf_time_cost": {"check_point": "module:training__update_training__training_data_expired", "perf_base": 1, "common_attrs": ["training__update_training__training_data_expiredTimeCost"], "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "training__update_training__training_data_expiredTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::update_training::delay_training_data_calculate::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__update_training__delay_training_data_calculateTimeCostStart", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__delay_training_data_calculateTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::update_training::delay_training_data_calculate::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__update_training__delay_training_data_calculateTimeCostEnd", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__delay_training_data_calculateTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::update_training::delay_training_data_calculate::calc_time_cost": {"import_common_attr": ["training__update_training__delay_training_data_calculateTimeCostEnd", "training__update_training__delay_training_data_calculateTimeCostStart"], "export_common_attr": ["training__update_training__delay_training_data_calculateTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__update_training__delay_training_data_calculateTimeCostEnd - training__update_training__delay_training_data_calculateTimeCostStart) end", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "training__update_training__delay_training_data_calculateTimeCostEnd", "training__update_training__delay_training_data_calculateTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__delay_training_data_calculateTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::update_training::delay_training_data_calculate::perf_time_cost": {"check_point": "module:training__update_training__delay_training_data_calculate", "perf_base": 1, "common_attrs": ["training__update_training__delay_training_data_calculateTimeCost"], "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "training__update_training__delay_training_data_calculateTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::update_training::simple_training_data_calculate::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__update_training__simple_training_data_calculateTimeCostStart", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__simple_training_data_calculateTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::update_training::simple_training_data_calculate::get_kconf_params": {"kconf_configs": [{"kconf_key": "adI18n.model_calibrate.maxCxrScoreInfoListSize", "export_common_attr": "max_cxr_score_info_list_size", "default_value": 20, "value_type": "int"}, {"kconf_key": "adI18n.model_calibrate.confidenceThreshold", "export_common_attr": "confidence_threshold", "default_value": 20, "value_type": "int"}], "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$output_common_attrs": ["confidence_threshold", "max_cxr_score_info_list_size"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoKconfCommonAttrEnricher"}, "training::update_training::simple_training_data_calculate::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__update_training__simple_training_data_calculateTimeCostEnd", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__simple_training_data_calculateTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::update_training::simple_training_data_calculate::calc_time_cost": {"import_common_attr": ["training__update_training__simple_training_data_calculateTimeCostEnd", "training__update_training__simple_training_data_calculateTimeCostStart"], "export_common_attr": ["training__update_training__simple_training_data_calculateTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__update_training__simple_training_data_calculateTimeCostEnd - training__update_training__simple_training_data_calculateTimeCostStart) end", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "training__update_training__simple_training_data_calculateTimeCostEnd", "training__update_training__simple_training_data_calculateTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__simple_training_data_calculateTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::update_training::simple_training_data_calculate::perf_time_cost": {"check_point": "module:training__update_training__simple_training_data_calculate", "perf_base": 1, "common_attrs": ["training__update_training__simple_training_data_calculateTimeCost"], "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "training__update_training__simple_training_data_calculateTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::update_training::training_data_send::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__update_training__training_data_sendTimeCostStart", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__training_data_sendTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::update_training::training_data_send::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__update_training__training_data_sendTimeCostEnd", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__training_data_sendTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::update_training::training_data_send::calc_time_cost": {"import_common_attr": ["training__update_training__training_data_sendTimeCostEnd", "training__update_training__training_data_sendTimeCostStart"], "export_common_attr": ["training__update_training__training_data_sendTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__update_training__training_data_sendTimeCostEnd - training__update_training__training_data_sendTimeCostStart) end", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "training__update_training__training_data_sendTimeCostEnd", "training__update_training__training_data_sendTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_training__training_data_sendTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::update_training::training_data_send::perf_time_cost": {"check_point": "module:training__update_training__training_data_send", "perf_base": 1, "common_attrs": ["training__update_training__training_data_sendTimeCost"], "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "training__update_training__training_data_sendTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::update_training::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__update_trainingTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__update_trainingTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::update_training::calc_time_cost": {"import_common_attr": ["training__update_trainingTimeCostEnd", "training__update_trainingTimeCostStart"], "export_common_attr": ["training__update_trainingTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__update_trainingTimeCostEnd - training__update_trainingTimeCostStart) end", "$metadata": {"$input_common_attrs": ["training__update_trainingTimeCostEnd", "training__update_trainingTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__update_trainingTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::update_training::perf_time_cost": {"check_point": "module:training__update_training", "perf_base": 1, "common_attrs": ["training__update_trainingTimeCost"], "$metadata": {"$input_common_attrs": ["training__update_trainingTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::expired_training::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__expired_trainingTimeCostStart", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__expired_trainingTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::expired_training::_branch_controller_A5673342": {"import_common_attr": ["ad_calibrate_trigger_type"], "export_common_attr": ["_if_control_attr_10"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (ad_calibrate_trigger_type == 3) then return false else return true end end", "for_branch_control": true, "$branch_start": "training::expired_training::_branch_controller_A5673342", "$code_info": "[if] CC5D2FCB base_module.py:44 in process(): self.flow.if_(self.enable_condition)", "$metadata": {"$input_common_attrs": ["ad_calibrate_trigger_type"], "$input_item_attrs": [], "$output_common_attrs": ["_if_control_attr_10"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::expired_training::training_data_read::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__expired_training__training_data_readTimeCostStart", "skip": "{{_if_control_attr_10}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": [], "$output_common_attrs": ["training__expired_training__training_data_readTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::expired_training::training_data_read::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__expired_training__training_data_readTimeCostEnd", "skip": "{{_if_control_attr_10}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": [], "$output_common_attrs": ["training__expired_training__training_data_readTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::expired_training::training_data_read::calc_time_cost": {"import_common_attr": ["training__expired_training__training_data_readTimeCostEnd", "training__expired_training__training_data_readTimeCostStart"], "export_common_attr": ["training__expired_training__training_data_readTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__expired_training__training_data_readTimeCostEnd - training__expired_training__training_data_readTimeCostStart) end", "skip": "{{_if_control_attr_10}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_10", "training__expired_training__training_data_readTimeCostEnd", "training__expired_training__training_data_readTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__expired_training__training_data_readTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::expired_training::training_data_read::perf_time_cost": {"check_point": "module:training__expired_training__training_data_read", "perf_base": 1, "common_attrs": ["training__expired_training__training_data_readTimeCost"], "skip": "{{_if_control_attr_10}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_10", "training__expired_training__training_data_readTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::expired_training::training_data_expired::calc_time_cost_s": {"save_elapsed_time_to_attr": "training__expired_training__training_data_expiredTimeCostStart", "skip": "{{_if_control_attr_10}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": [], "$output_common_attrs": ["training__expired_training__training_data_expiredTimeCostStart"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::expired_training::training_data_expired::get_kconf_params": {"kconf_configs": [{"kconf_key": "adI18n.model_calibrate.enableClearPeriod", "export_common_attr": "enable_clear_period", "value_type": "bool"}, {"kconf_key": "adI18n.model_calibrate.clearStartTimeSec", "export_common_attr": "start_time_sec", "default_value": -1, "value_type": "int"}, {"kconf_key": "adI18n.model_calibrate.clearEndTimeSec", "export_common_attr": "end_time_sec", "default_value": -1, "value_type": "int"}], "skip": "{{_if_control_attr_10}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": [], "$output_common_attrs": ["enable_clear_period", "end_time_sec", "start_time_sec"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoKconfCommonAttrEnricher"}, "training::expired_training::training_data_expired::_branch_controller_0A877908": {"import_common_attr": ["_if_control_attr_10"], "export_common_attr": ["_if_control_attr_11"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (_if_control_attr_10 == 0 and (true)) then return false else return true end end", "for_branch_control": true, "$branch_start": "training::expired_training::training_data_expired::_branch_controller_0A877908", "$code_info": "[if] 02A97BED base_module.py:44 in process(): self.flow.if_(self.enable_condition)", "$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": [], "$output_common_attrs": ["_if_control_attr_11"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::expired_training::training_data_expired::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__expired_training__training_data_expiredTimeCostEnd", "skip": "{{_if_control_attr_10}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": [], "$output_common_attrs": ["training__expired_training__training_data_expiredTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::expired_training::training_data_expired::calc_time_cost": {"import_common_attr": ["training__expired_training__training_data_expiredTimeCostEnd", "training__expired_training__training_data_expiredTimeCostStart"], "export_common_attr": ["training__expired_training__training_data_expiredTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__expired_training__training_data_expiredTimeCostEnd - training__expired_training__training_data_expiredTimeCostStart) end", "skip": "{{_if_control_attr_10}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_10", "training__expired_training__training_data_expiredTimeCostEnd", "training__expired_training__training_data_expiredTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__expired_training__training_data_expiredTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::expired_training::training_data_expired::perf_time_cost": {"check_point": "module:training__expired_training__training_data_expired", "perf_base": 1, "common_attrs": ["training__expired_training__training_data_expiredTimeCost"], "skip": "{{_if_control_attr_10}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_10", "training__expired_training__training_data_expiredTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::expired_training::calc_time_cost_e": {"save_elapsed_time_to_attr": "training__expired_trainingTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["training__expired_trainingTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::expired_training::calc_time_cost": {"import_common_attr": ["training__expired_trainingTimeCostEnd", "training__expired_trainingTimeCostStart"], "export_common_attr": ["training__expired_trainingTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (training__expired_trainingTimeCostEnd - training__expired_trainingTimeCostStart) end", "$metadata": {"$input_common_attrs": ["training__expired_trainingTimeCostEnd", "training__expired_trainingTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["training__expired_trainingTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::expired_training::perf_time_cost": {"check_point": "module:training__expired_training", "perf_base": 1, "common_attrs": ["training__expired_trainingTimeCost"], "$metadata": {"$input_common_attrs": ["training__expired_trainingTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::calc_time_cost_e": {"save_elapsed_time_to_attr": "trainingTimeCostEnd", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["trainingTimeCostEnd"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoUserMetaInfoEnricher"}, "training::calc_time_cost": {"import_common_attr": ["trainingTimeCostEnd", "trainingTimeCostStart"], "export_common_attr": ["trainingTimeCost"], "function_for_common": "gen_common_attrs", "lua_script": "function gen_common_attrs() return (trainingTimeCostEnd - trainingTimeCostStart) end", "$metadata": {"$input_common_attrs": ["trainingTimeCostEnd", "trainingTimeCostStart"], "$input_item_attrs": [], "$output_common_attrs": ["trainingTimeCost"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::perf_time_cost": {"check_point": "module:training", "perf_base": 1, "common_attrs": ["trainingTimeCost"], "$metadata": {"$input_common_attrs": ["trainingTimeCost"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoAttrValuePerflogObserver"}, "training::expired_training::training_data_expired::log_debug_info_3AA07E": {"for_debug_request_only": false, "print_all_item_keys": true, "print_all_item_attrs": true, "item_table": "msg_table", "common_attrs": ["end_time_sec", "start_time_sec", "enable_clear_period"], "skip": "{{_if_control_attr_11}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_11", "enable_clear_period", "end_time_sec", "start_time_sec"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoDebugInfoObserver"}, "training::expired_training::training_data_expired::enrich_attr_by_light_function_904D95": {"import_item_attr": ["key_hash"], "function_name": "ClearTrainInfoAndScoreInfo", "class_name": "AdLightFunctionSet", "item_table": "msg_table", "target_item": {"trigger_type": [3]}, "skip": "{{_if_control_attr_11}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_11"], "$input_item_attrs": ["key_hash", "trigger_type"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLightFunctionEnricher"}, "training::expired_training::training_data_expired::ad_calibrate_clear_redis_observer_74DDE7": {"kcc_cluster": "kcache_adI18nModelCalibrate", "timeout_ms": 10, "item_table": "msg_table", "input_item_attr": ["key_hash_acc_info", "key_hash_score_info", "key_hash_key_timestamp"], "target_item": {"trigger_type": [3]}, "skip": "{{_if_control_attr_11}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_11"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateClearRedisObserver"}, "training::expired_training::training_data_expired::ad_calibrate_table_clear_to_local_cache_enricher_5C8BB1": {"item_table": "msg_table", "input_ptr": "train_info_ptr", "enable_clear_period": "enable_clear_period", "start_time_sec": "start_time_sec", "end_time_sec": "end_time_sec", "now_time_sec": "now_time_sec", "target_item": {"trigger_type": [1, 2]}, "skip": "{{_if_control_attr_11}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_11", "enable_clear_period", "end_time_sec", "now_time_sec", "start_time_sec"], "$input_item_attrs": ["train_info_ptr", "trigger_type"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateTableClearToLocalCacheEnricher"}, "training::expired_training::training_data_read::log_debug_info_381C4D": {"for_debug_request_only": false, "print_all_item_keys": true, "print_all_item_attrs": true, "item_table": "msg_table", "skip": "{{_if_control_attr_10}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoDebugInfoObserver"}, "training::expired_training::training_data_read::ad_calibrate_table_fetch_from_redis_observer_04C931": {"kcc_cluster": "kcache_adI18nModelCalibrate", "input_item_accumulator_info_key": "key_hash_acc_info", "input_item_score_info_key": "key_hash_score_info", "input_item_key_timestamp_key": "key_hash_key_timestamp", "input_item_sample_type": "sample_type", "item_table": "msg_table", "target_item": {"output_item_status": 0}, "skip": "{{_if_control_attr_10}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_10", "kcache_adI18nModelCalibrate"], "$input_item_attrs": ["key_hash_acc_info", "key_hash_key_timestamp", "key_hash_score_info"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateTableFetchFromRedisObserver"}, "training::expired_training::training_data_read::enrich_attr_by_lua_095AF0": {"import_item_attr": ["key_hash"], "function_for_item": "calculate", "export_item_attr": ["key_hash_acc_info", "key_hash_score_info", "key_hash_key_timestamp"], "item_table": "msg_table", "lua_script": "function calculate()\n                        return \"ai_\" .. key_hash, \"si_\" .. key_hash, \"kt_\" .. key_hash\n                    end", "skip": "{{_if_control_attr_10}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": ["key_hash"], "$output_common_attrs": [], "$output_item_attrs": ["key_hash_acc_info", "key_hash_key_timestamp", "key_hash_score_info"], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::expired_training::training_data_read::ad_calibrate_table_fetch_from_cache_enricher_FF11B6": {"input_key": "key_hash", "output_attr": "train_info_ptr", "output_item_key_timestamp": "output_item_key_timestamp", "output_item_status": "output_item_status", "item_table": "msg_table", "skip": "{{_if_control_attr_10}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": ["key_hash"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateTableFetchFromCacheEnricher"}, "training::update_training::training_data_send::send_with_kafka_053B66": {"item_table": "msg_table", "common_attr": "common_train_info", "topic_name": "model_calibrate_train_result_i18n", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "common_train_info"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoKafkaObserver"}, "training::update_training::training_data_send::log_debug_info_5D1B6D": {"item_table": "msg_table", "for_debug_request_only": false, "print_all_item_keys": true, "print_all_item_attrs": true, "common_attrs": ["common_train_info", "input_number_trigger_switch", "input_max_impression_num", "input_max_conv_num"], "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "common_train_info", "input_max_conv_num", "input_max_impression_num", "input_number_trigger_switch"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoDebugInfoObserver"}, "training::update_training::training_data_send::enrich_attr_by_light_function_64AA0D": {"import_item_attr": ["item_train_info"], "export_common_attr": ["common_train_info"], "function_name": "CalibrateTrainInfoItemAttrToCommon", "class_name": "AdLightFunctionSet", "item_table": "msg_table", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": ["item_train_info"], "$output_common_attrs": ["common_train_info"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLightFunctionEnricher"}, "training::update_training::simple_training_data_calculate::ad_calibrate_simple_label_calculate_enricher_4902F8": {"item_table": "msg_table", "input_item_key_hash": "key_hash", "input_item_key_type": "dimension_type", "input_ptr": "train_info_ptr", "confidence_threshold": "confidence_threshold", "max_cxr_score_info_list_size": "max_cxr_score_info_list_size", "output_item_recall_ratio": "item_recall_ratio", "output_item_train_info": "item_train_info", "output_item_train_info_backup": "item_train_info_backup", "output_common_train_info": "common_train_info", "target_item": {"sample_type": 1}, "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "confidence_threshold", "max_cxr_score_info_list_size"], "$input_item_attrs": ["item_recall_ratio", "item_train_info", "sample_type", "train_info_ptr"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateSimpleLabelCalculateEnricher"}, "training::update_training::delay_training_data_calculate::ad_calibrate_delay_label_calculate_enricher_02E12E": {"item_table": "msg_table", "input_item_key_hash": "key_hash", "input_item_key_type": "dimension_type", "input_ptr": "train_info_ptr", "confidence_threshold": "confidence_threshold", "max_cxr_score_info_list_size": "max_cxr_score_info_list_size", "output_item_recall_ratio": "item_recall_ratio", "output_item_train_info": "item_train_info", "output_common_train_info": "common_train_info", "output_item_train_info_backup": "item_train_info_backup", "target_item": {"sample_type": 2}, "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "confidence_threshold", "max_cxr_score_info_list_size"], "$input_item_attrs": ["item_recall_ratio", "item_train_info", "sample_type", "train_info_ptr"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateDelayLabelCalculateEnricher"}, "training::update_training::training_data_expired::log_debug_info_CB677C": {"for_debug_request_only": false, "print_all_item_keys": true, "print_all_item_attrs": true, "item_table": "msg_table", "common_attrs": ["end_time_sec", "start_time_sec", "enable_clear_period"], "skip": "{{_if_control_attr_9}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_9", "enable_clear_period", "end_time_sec", "start_time_sec"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoDebugInfoObserver"}, "training::update_training::training_data_expired::enrich_attr_by_light_function_B0043E": {"import_item_attr": ["key_hash"], "function_name": "ClearTrainInfoAndScoreInfo", "class_name": "AdLightFunctionSet", "item_table": "msg_table", "target_item": {"trigger_type": [3]}, "skip": "{{_if_control_attr_9}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_9"], "$input_item_attrs": ["key_hash", "trigger_type"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLightFunctionEnricher"}, "training::update_training::training_data_expired::ad_calibrate_clear_redis_observer_2971C2": {"kcc_cluster": "kcache_adI18nModelCalibrate", "timeout_ms": 10, "item_table": "msg_table", "input_item_attr": ["key_hash_acc_info", "key_hash_score_info", "key_hash_key_timestamp"], "target_item": {"trigger_type": [3]}, "skip": "{{_if_control_attr_9}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_9"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateClearRedisObserver"}, "training::update_training::training_data_expired::ad_calibrate_table_clear_to_local_cache_enricher_24A6BD": {"item_table": "msg_table", "input_ptr": "train_info_ptr", "enable_clear_period": "enable_clear_period", "start_time_sec": "start_time_sec", "end_time_sec": "end_time_sec", "now_time_sec": "now_time_sec", "target_item": {"trigger_type": [1, 2]}, "skip": "{{_if_control_attr_9}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_9", "enable_clear_period", "end_time_sec", "now_time_sec", "start_time_sec"], "$input_item_attrs": ["train_info_ptr", "trigger_type"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateTableClearToLocalCacheEnricher"}, "training::update_training::training_data_read::log_debug_info_8E9DF8": {"for_debug_request_only": false, "print_all_item_keys": true, "print_all_item_attrs": true, "item_table": "msg_table", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoDebugInfoObserver"}, "training::update_training::training_data_read::ad_calibrate_table_fetch_from_redis_observer_AA82A0": {"kcc_cluster": "kcache_adI18nModelCalibrate", "input_item_accumulator_info_key": "key_hash_acc_info", "input_item_score_info_key": "key_hash_score_info", "input_item_key_timestamp_key": "key_hash_key_timestamp", "input_item_sample_type": "sample_type", "item_table": "msg_table", "target_item": {"output_item_status": 0}, "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8", "kcache_adI18nModelCalibrate"], "$input_item_attrs": ["key_hash_acc_info", "key_hash_key_timestamp", "key_hash_score_info"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateTableFetchFromRedisObserver"}, "training::update_training::training_data_read::enrich_attr_by_lua_6629E9": {"import_item_attr": ["key_hash"], "function_for_item": "calculate", "export_item_attr": ["key_hash_acc_info", "key_hash_score_info", "key_hash_key_timestamp"], "item_table": "msg_table", "lua_script": "function calculate()\n                        return \"ai_\" .. key_hash, \"si_\" .. key_hash, \"kt_\" .. key_hash\n                    end", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": ["key_hash"], "$output_common_attrs": [], "$output_item_attrs": ["key_hash_acc_info", "key_hash_key_timestamp", "key_hash_score_info"], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::update_training::training_data_read::ad_calibrate_table_fetch_from_cache_enricher_76EC34": {"input_key": "key_hash", "output_attr": "train_info_ptr", "output_item_key_timestamp": "output_item_key_timestamp", "output_item_status": "output_item_status", "item_table": "msg_table", "skip": "{{_if_control_attr_8}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": ["key_hash"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateTableFetchFromCacheEnricher"}, "training::ad_log_training::training_data_save::log_debug_info_D5B274": {"for_debug_request_only": false, "print_all_item_keys": true, "print_all_item_attrs": true, "item_table": "msg_table", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoDebugInfoObserver"}, "training::ad_log_training::training_data_save::write_to_redis_624E3D": {"item_table": "msg_table", "kcc_cluster": "kcache_adI18nModelCalibrate", "timeout_ms": 10, "key_from_item_attr": "key_hash_acc_info", "value_from_item_attr": "train_info_str", "expire_second": 604800, "$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["key_hash_acc_info", "train_info_str"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "downstream_processor": "__none", "type_name": "CommonRecoWriteToRedisObserver"}, "training::ad_log_training::training_data_save::enrich_attr_by_light_function_B8BD65": {"item_table": "msg_table", "import_item_attr": ["train_info_ptr", "sample_type", "key_hash"], "export_item_attr": ["train_info_str"], "function_name": "BuildCalibrateInfoByPtr", "class_name": "AdLightFunctionSet", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["key_hash", "sample_type", "train_info_ptr"], "$output_common_attrs": [], "$output_item_attrs": ["train_info_str"], "$modify_item_tables": []}, "type_name": "CommonRecoLightFunctionEnricher"}, "training::ad_log_training::training_data_save::write_to_redis_6CD62F": {"item_table": "msg_table", "kcc_cluster": "kcache_adI18nModelCalibrate", "timeout_ms": 10, "key_from_item_attr": "key_hash_score_info", "value_from_item_attr": "train_result_backup", "expire_second": 604800, "$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["key_hash_score_info", "train_result_backup"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "downstream_processor": "__none", "type_name": "CommonRecoWriteToRedisObserver"}, "training::ad_log_training::training_data_save::serialize_protobuf_message_513522": {"item_table": "msg_table", "from_item_attr": "item_train_info_backup", "serialize_to_item_attr": "train_result_backup", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["item_train_info_backup"], "$output_common_attrs": [], "$output_item_attrs": ["train_result_backup"], "$modify_item_tables": []}, "type_name": "CommonRecoProtobufSerializeAttrEnricher"}, "training::ad_log_training::training_data_send::send_with_kafka_F1C120": {"item_table": "msg_table", "common_attr": "common_train_info", "topic_name": "model_calibrate_train_result_i18n", "$metadata": {"$input_common_attrs": ["common_train_info"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoKafkaObserver"}, "training::ad_log_training::training_data_send::log_debug_info_74709A": {"item_table": "msg_table", "for_debug_request_only": false, "print_all_item_keys": true, "print_all_item_attrs": true, "common_attrs": ["common_train_info", "input_number_trigger_switch", "input_max_impression_num", "input_max_conv_num"], "$metadata": {"$input_common_attrs": ["common_train_info", "input_max_conv_num", "input_max_impression_num", "input_number_trigger_switch"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoDebugInfoObserver"}, "training::ad_log_training::training_data_send::enrich_attr_by_light_function_1F25F0": {"import_item_attr": ["item_train_info"], "export_common_attr": ["common_train_info"], "function_name": "CalibrateTrainInfoItemAttrToCommon", "class_name": "AdLightFunctionSet", "item_table": "msg_table", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["item_train_info"], "$output_common_attrs": ["common_train_info"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLightFunctionEnricher"}, "training::ad_log_training::simple_training_data_calculate::ad_calibrate_simple_label_calculate_enricher_3A2E5C": {"item_table": "msg_table", "input_item_key_hash": "key_hash", "input_item_key_type": "dimension_type", "input_ptr": "train_info_ptr", "confidence_threshold": "confidence_threshold", "max_cxr_score_info_list_size": "max_cxr_score_info_list_size", "output_item_recall_ratio": "item_recall_ratio", "output_item_train_info": "item_train_info", "output_item_train_info_backup": "item_train_info_backup", "output_common_train_info": "common_train_info", "target_item": {"sample_type": 1}, "$metadata": {"$input_common_attrs": ["confidence_threshold", "max_cxr_score_info_list_size"], "$input_item_attrs": ["item_recall_ratio", "item_train_info", "sample_type", "train_info_ptr"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateSimpleLabelCalculateEnricher"}, "training::ad_log_training::delay_training_data_calculate::ad_calibrate_delay_label_calculate_enricher_FBC9B3": {"item_table": "msg_table", "input_item_key_hash": "key_hash", "input_item_key_type": "dimension_type", "input_ptr": "train_info_ptr", "confidence_threshold": "confidence_threshold", "max_cxr_score_info_list_size": "max_cxr_score_info_list_size", "output_item_recall_ratio": "item_recall_ratio", "output_item_train_info": "item_train_info", "output_common_train_info": "common_train_info", "output_item_train_info_backup": "item_train_info_backup", "target_item": {"sample_type": 2}, "$metadata": {"$input_common_attrs": ["confidence_threshold", "max_cxr_score_info_list_size"], "$input_item_attrs": ["item_recall_ratio", "item_train_info", "sample_type", "train_info_ptr"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateDelayLabelCalculateEnricher"}, "training::ad_log_training::training_data_check::log_debug_info_F0C883": {"for_debug_request_only": false, "print_all_item_keys": true, "print_all_item_attrs": true, "item_table": "msg_table", "common_attrs": ["input_number_trigger_switch", "input_max_impression_num", "input_max_conv_num", "item_num"], "skip": "{{_if_control_attr_6}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_6", "input_max_conv_num", "input_max_impression_num", "input_number_trigger_switch", "item_num"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoDebugInfoObserver"}, "training::ad_log_training::training_data_check::return__B920B9": {"status_code": 0, "skip": "{{_if_control_attr_7}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_7"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoExecutionStatusEnricher"}, "training::ad_log_training::training_data_check::count_reco_result_741FEA": {"item_table": "msg_table", "save_result_size_to_common_attr": "item_num", "skip": "{{_if_control_attr_6}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_6"], "$input_item_attrs": [], "$output_common_attrs": ["item_num"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoCountRecoResultEnricher"}, "training::ad_log_training::training_data_check::filter_by_attr_A02021": {"item_table": "msg_table", "attr_name": "item_update_switch", "remove_if": "!=", "compare_to": 1, "remove_if_attr_missing": true, "skip": "{{_if_control_attr_6}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_6"], "$input_item_attrs": ["item_update_switch"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": ["msg_table"]}, "type_name": "CommonRecoAttrFilterArranger"}, "training::ad_log_training::training_data_check::ad_calibrate_common_info_checker_enricher_B852D5": {"item_table": "msg_table", "input_ptr": "train_info_ptr", "input_number_trigger_switch": "input_number_trigger_switch", "input_max_impression_num": "input_max_impression_num", "input_max_conv_num": "input_max_conv_num", "output_item_update_switch": "item_update_switch", "skip": "{{_if_control_attr_6}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_6"], "$input_item_attrs": ["input_max_conv_num", "input_max_impression_num", "input_number_trigger_switch", "train_info_ptr"], "$output_common_attrs": [], "$output_item_attrs": ["item_update_switch"], "$modify_item_tables": []}, "type_name": "AdCalibrateCommonInfoCheckerEnricher"}, "training::ad_log_training::training_data_expired::log_debug_info_756312": {"for_debug_request_only": false, "print_all_item_keys": true, "print_all_item_attrs": true, "item_table": "msg_table", "common_attrs": ["end_time_sec", "start_time_sec", "enable_clear_period"], "skip": "{{_if_control_attr_5}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_5", "enable_clear_period", "end_time_sec", "start_time_sec"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoDebugInfoObserver"}, "training::ad_log_training::training_data_expired::enrich_attr_by_light_function_10F19D": {"import_item_attr": ["key_hash"], "function_name": "ClearTrainInfoAndScoreInfo", "class_name": "AdLightFunctionSet", "item_table": "msg_table", "target_item": {"trigger_type": [3]}, "skip": "{{_if_control_attr_5}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_5"], "$input_item_attrs": ["key_hash", "trigger_type"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLightFunctionEnricher"}, "training::ad_log_training::training_data_expired::ad_calibrate_clear_redis_observer_0D5BC5": {"kcc_cluster": "kcache_adI18nModelCalibrate", "timeout_ms": 10, "item_table": "msg_table", "input_item_attr": ["key_hash_acc_info", "key_hash_score_info", "key_hash_key_timestamp"], "target_item": {"trigger_type": [3]}, "skip": "{{_if_control_attr_5}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_5"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateClearRedisObserver"}, "training::ad_log_training::training_data_expired::ad_calibrate_table_clear_to_local_cache_enricher_81E461": {"item_table": "msg_table", "input_ptr": "train_info_ptr", "enable_clear_period": "enable_clear_period", "start_time_sec": "start_time_sec", "end_time_sec": "end_time_sec", "now_time_sec": "now_time_sec", "target_item": {"trigger_type": [1, 2]}, "skip": "{{_if_control_attr_5}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_5", "enable_clear_period", "end_time_sec", "now_time_sec", "start_time_sec"], "$input_item_attrs": ["train_info_ptr", "trigger_type"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateTableClearToLocalCacheEnricher"}, "training::ad_log_training::simple_training_data_accumulator::log_debug_info_D5B274": {"for_debug_request_only": false, "print_all_item_keys": true, "print_all_item_attrs": true, "item_table": "msg_table", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoDebugInfoObserver"}, "training::ad_log_training::simple_training_data_accumulator::ad_calibrate_simple_training_label_calculate_enricher_FB6694": {"item_table": "msg_table", "input_ptr": "train_info_ptr", "start_event_ts": "start_event_timestamp", "has_ad_click": "has_ad_click", "event_server_ts": "event_server_timestamp", "bucket_index_list": "bucket_index_array", "start_event_num_list": "bucket_start_event_num_array", "ad_item_click_list": "ad_item_click_list", "end_event_num_list": "bucket_end_event_num_array", "conv_rate_sum_list": "bucket_conv_rate_sum_array", "start_event_num": "start_event_num", "target_item": {"sample_type": 1}, "$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["bucket_end_event_num_array", "bucket_index_array", "bucket_start_event_num_array", "event_server_timestamp", "sample_type", "start_event_timestamp", "train_info_ptr"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateSimpleTrainingDataAccumulatorEnricher"}, "training::ad_log_training::delay_training_data_accumulator::log_debug_info_A588EF": {"for_debug_request_only": false, "print_all_item_keys": true, "print_all_item_attrs": true, "item_table": "msg_table", "skip": "{{_if_control_attr_4}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_4"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoDebugInfoObserver"}, "training::ad_log_training::delay_training_data_accumulator::ad_calibrate_delay_training_data_accumulator_enricher_60F665": {"item_table": "msg_table", "input_ptr": "train_info_ptr", "start_event_ts": "start_event_timestamp", "has_ad_click": "has_ad_click", "event_server_ts": "event_server_timestamp", "bucket_index_list": "bucket_index_array", "start_event_num_list": "bucket_start_event_num_array", "ad_item_click_list": "ad_item_click_list", "end_event_num_list": "bucket_end_event_num_array", "conv_rate_sum_list": "bucket_conv_rate_sum_array", "start_event_num": "start_event_num", "target_item": {"sample_type": 2}, "skip": "{{_if_control_attr_4}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_4"], "$input_item_attrs": ["bucket_end_event_num_array", "bucket_index_array", "bucket_start_event_num_array", "event_server_timestamp", "sample_type", "start_event_timestamp", "train_info_ptr"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateDelayTrainingDataAccumulatorEnricher"}, "training::ad_log_training::training_data_read::log_debug_info_D5B274": {"for_debug_request_only": false, "print_all_item_keys": true, "print_all_item_attrs": true, "item_table": "msg_table", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoDebugInfoObserver"}, "training::ad_log_training::training_data_read::ad_calibrate_table_fetch_from_redis_observer_9089ED": {"kcc_cluster": "kcache_adI18nModelCalibrate", "input_item_accumulator_info_key": "key_hash_acc_info", "input_item_score_info_key": "key_hash_score_info", "input_item_key_timestamp_key": "key_hash_key_timestamp", "input_item_sample_type": "sample_type", "item_table": "msg_table", "target_item": {"output_item_status": 0}, "$metadata": {"$input_common_attrs": ["kcache_adI18nModelCalibrate"], "$input_item_attrs": ["key_hash_acc_info", "key_hash_key_timestamp", "key_hash_score_info"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateTableFetchFromRedisObserver"}, "training::ad_log_training::training_data_read::enrich_attr_by_lua_E39119": {"import_item_attr": ["key_hash"], "function_for_item": "calculate", "export_item_attr": ["key_hash_acc_info", "key_hash_score_info", "key_hash_key_timestamp"], "item_table": "msg_table", "lua_script": "function calculate()\n                        return \"ai_\" .. key_hash, \"si_\" .. key_hash, \"kt_\" .. key_hash\n                    end", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["key_hash"], "$output_common_attrs": [], "$output_item_attrs": ["key_hash_acc_info", "key_hash_key_timestamp", "key_hash_score_info"], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "training::ad_log_training::training_data_read::ad_calibrate_table_fetch_from_cache_enricher_538A15": {"input_key": "key_hash", "output_attr": "train_info_ptr", "output_item_key_timestamp": "output_item_key_timestamp", "output_item_status": "output_item_status", "item_table": "msg_table", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["key_hash"], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdCalibrateTableFetchFromCacheEnricher"}, "retrieval::message_queue_retrieval::log_debug_info_D5B274": {"for_debug_request_only": false, "print_all_item_keys": true, "print_all_item_attrs": true, "item_table": "msg_table", "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoDebugInfoObserver"}, "retrieval::message_queue_retrieval::message_queue_retriever_99D06F": {"max_queue_size": 1, "queue_number": 1, "message_queue_type": 2, "table_name_map": [{"origin": "triggerTable", "target": "msg_table"}], "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "MessageQueueRetriever"}}}, "pipeline_map": {"ad_log_trigger_flow": {"__PARENT": "base_pipeline", "pipeline": ["calc_time_cost_s", "ad_log_prepare::calc_time_cost_s", "ad_log_prepare::ad_runtime_env_enricher_399F20", "ad_log_prepare::calc_time_cost_e", "ad_log_prepare::calc_time_cost", "ad_log_prepare::perf_time_cost", "ad_log_retrieve::calc_time_cost_s", "ad_log_retrieve::fectchDataFrameFromKafka", "ad_log_retrieve::retrievalDataFrame", "ad_log_retrieve::setDefaultValue", "ad_log_retrieve::calc_time_cost_e", "ad_log_retrieve::calc_time_cost", "ad_log_retrieve::perf_time_cost", "ad_log_filter::calc_time_cost_s", "ad_log_filter::copy_user_meta_info_23DDF3", "ad_log_filter::enrich_attr_by_lua_108350", "ad_log_filter::filter_by_rule_1AA725", "ad_log_filter::calc_time_cost_e", "ad_log_filter::calc_time_cost", "ad_log_filter::perf_time_cost", "ad_log_dispatcher::calc_time_cost_s", "ad_log_dispatcher::_branch_controller_7329C5E1", "ad_log_dispatcher::ad_subflow_shard_id_enricher_4BA531", "ad_log_dispatcher::_branch_controller_582BDAB9", "ad_log_dispatcher::return__6A0259", "ad_log_dispatcher::ad_message_queue_dispatch_mixer_EB5445", "ad_log_dispatcher::calc_time_cost_e", "ad_log_dispatcher::calc_time_cost", "ad_log_dispatcher::perf_time_cost", "calc_time_cost_e", "calc_time_cost", "perf_time_cost"]}, "calibrate_training_flow": {"__PARENT": "base_pipeline", "pipeline": ["calc_time_cost_s", "retrieval::calc_time_cost_s", "retrieval::message_queue_retrieval::calc_time_cost_s", "retrieval::message_queue_retrieval::message_queue_retriever_99D06F", "retrieval::message_queue_retrieval::log_debug_info_D5B274", "retrieval::message_queue_retrieval::calc_time_cost_e", "retrieval::message_queue_retrieval::calc_time_cost", "retrieval::message_queue_retrieval::perf_time_cost", "retrieval::calc_time_cost_e", "retrieval::calc_time_cost", "retrieval::perf_time_cost", "training::calc_time_cost_s", "training::ad_log_training::calc_time_cost_s", "training::ad_log_training::training_data_read::calc_time_cost_s", "training::ad_log_training::training_data_read::ad_calibrate_table_fetch_from_cache_enricher_538A15", "training::ad_log_training::training_data_read::log_debug_info_D5B274", "training::ad_log_training::training_data_read::enrich_attr_by_lua_E39119", "training::ad_log_training::training_data_read::log_debug_info_D5B274", "training::ad_log_training::training_data_read::ad_calibrate_table_fetch_from_redis_observer_9089ED", "training::ad_log_training::training_data_read::log_debug_info_D5B274", "training::ad_log_training::training_data_read::calc_time_cost_e", "training::ad_log_training::training_data_read::calc_time_cost", "training::ad_log_training::training_data_read::perf_time_cost", "training::ad_log_training::delay_training_data_accumulator::calc_time_cost_s", "training::ad_log_training::delay_training_data_accumulator::_branch_controller_11463360", "training::ad_log_training::delay_training_data_accumulator::ad_calibrate_delay_training_data_accumulator_enricher_60F665", "training::ad_log_training::delay_training_data_accumulator::log_debug_info_A588EF", "training::ad_log_training::delay_training_data_accumulator::calc_time_cost_e", "training::ad_log_training::delay_training_data_accumulator::calc_time_cost", "training::ad_log_training::delay_training_data_accumulator::perf_time_cost", "training::ad_log_training::simple_training_data_accumulator::calc_time_cost_s", "training::ad_log_training::simple_training_data_accumulator::ad_calibrate_simple_training_label_calculate_enricher_FB6694", "training::ad_log_training::simple_training_data_accumulator::log_debug_info_D5B274", "training::ad_log_training::simple_training_data_accumulator::calc_time_cost_e", "training::ad_log_training::simple_training_data_accumulator::calc_time_cost", "training::ad_log_training::simple_training_data_accumulator::perf_time_cost", "training::ad_log_training::training_data_expired::calc_time_cost_s", "training::ad_log_training::training_data_expired::get_kconf_params", "training::ad_log_training::training_data_expired::_branch_controller_75EBBAEA", "training::ad_log_training::training_data_expired::ad_calibrate_table_clear_to_local_cache_enricher_81E461", "training::ad_log_training::training_data_expired::ad_calibrate_clear_redis_observer_0D5BC5", "training::ad_log_training::training_data_expired::enrich_attr_by_light_function_10F19D", "training::ad_log_training::training_data_expired::log_debug_info_756312", "training::ad_log_training::training_data_expired::calc_time_cost_e", "training::ad_log_training::training_data_expired::calc_time_cost", "training::ad_log_training::training_data_expired::perf_time_cost", "training::ad_log_training::training_data_check::calc_time_cost_s", "training::ad_log_training::training_data_check::get_kconf_params", "training::ad_log_training::training_data_check::_branch_controller_36F9D8B0", "training::ad_log_training::training_data_check::ad_calibrate_common_info_checker_enricher_B852D5", "training::ad_log_training::training_data_check::filter_by_attr_A02021", "training::ad_log_training::training_data_check::count_reco_result_741FEA", "training::ad_log_training::training_data_check::_branch_controller_C564371C", "training::ad_log_training::training_data_check::return__B920B9", "training::ad_log_training::training_data_check::log_debug_info_F0C883", "training::ad_log_training::training_data_check::calc_time_cost_e", "training::ad_log_training::training_data_check::calc_time_cost", "training::ad_log_training::training_data_check::perf_time_cost", "training::ad_log_training::delay_training_data_calculate::calc_time_cost_s", "training::ad_log_training::delay_training_data_calculate::ad_calibrate_delay_label_calculate_enricher_FBC9B3", "training::ad_log_training::delay_training_data_calculate::calc_time_cost_e", "training::ad_log_training::delay_training_data_calculate::calc_time_cost", "training::ad_log_training::delay_training_data_calculate::perf_time_cost", "training::ad_log_training::simple_training_data_calculate::calc_time_cost_s", "training::ad_log_training::simple_training_data_calculate::get_kconf_params", "training::ad_log_training::simple_training_data_calculate::ad_calibrate_simple_label_calculate_enricher_3A2E5C", "training::ad_log_training::simple_training_data_calculate::calc_time_cost_e", "training::ad_log_training::simple_training_data_calculate::calc_time_cost", "training::ad_log_training::simple_training_data_calculate::perf_time_cost", "training::ad_log_training::training_data_send::calc_time_cost_s", "training::ad_log_training::training_data_send::enrich_attr_by_light_function_1F25F0", "training::ad_log_training::training_data_send::log_debug_info_74709A", "training::ad_log_training::training_data_send::send_with_kafka_F1C120", "training::ad_log_training::training_data_send::calc_time_cost_e", "training::ad_log_training::training_data_send::calc_time_cost", "training::ad_log_training::training_data_send::perf_time_cost", "training::ad_log_training::training_data_save::calc_time_cost_s", "training::ad_log_training::training_data_save::serialize_protobuf_message_513522", "training::ad_log_training::training_data_save::write_to_redis_6CD62F", "training::ad_log_training::training_data_save::enrich_attr_by_light_function_B8BD65", "training::ad_log_training::training_data_save::write_to_redis_624E3D", "training::ad_log_training::training_data_save::log_debug_info_D5B274", "training::ad_log_training::training_data_save::calc_time_cost_e", "training::ad_log_training::training_data_save::calc_time_cost", "training::ad_log_training::training_data_save::perf_time_cost", "training::ad_log_training::calc_time_cost_e", "training::ad_log_training::calc_time_cost", "training::ad_log_training::perf_time_cost", "training::update_training::calc_time_cost_s", "training::update_training::_branch_controller_49A07694", "training::update_training::training_data_read::calc_time_cost_s", "training::update_training::training_data_read::ad_calibrate_table_fetch_from_cache_enricher_76EC34", "training::update_training::training_data_read::log_debug_info_8E9DF8", "training::update_training::training_data_read::enrich_attr_by_lua_6629E9", "training::update_training::training_data_read::log_debug_info_8E9DF8", "training::update_training::training_data_read::ad_calibrate_table_fetch_from_redis_observer_AA82A0", "training::update_training::training_data_read::log_debug_info_8E9DF8", "training::update_training::training_data_read::calc_time_cost_e", "training::update_training::training_data_read::calc_time_cost", "training::update_training::training_data_read::perf_time_cost", "training::update_training::training_data_expired::calc_time_cost_s", "training::update_training::training_data_expired::get_kconf_params", "training::update_training::training_data_expired::_branch_controller_2952577C", "training::update_training::training_data_expired::ad_calibrate_table_clear_to_local_cache_enricher_24A6BD", "training::update_training::training_data_expired::ad_calibrate_clear_redis_observer_2971C2", "training::update_training::training_data_expired::enrich_attr_by_light_function_B0043E", "training::update_training::training_data_expired::log_debug_info_CB677C", "training::update_training::training_data_expired::calc_time_cost_e", "training::update_training::training_data_expired::calc_time_cost", "training::update_training::training_data_expired::perf_time_cost", "training::update_training::delay_training_data_calculate::calc_time_cost_s", "training::update_training::delay_training_data_calculate::ad_calibrate_delay_label_calculate_enricher_02E12E", "training::update_training::delay_training_data_calculate::calc_time_cost_e", "training::update_training::delay_training_data_calculate::calc_time_cost", "training::update_training::delay_training_data_calculate::perf_time_cost", "training::update_training::simple_training_data_calculate::calc_time_cost_s", "training::update_training::simple_training_data_calculate::get_kconf_params", "training::update_training::simple_training_data_calculate::ad_calibrate_simple_label_calculate_enricher_4902F8", "training::update_training::simple_training_data_calculate::calc_time_cost_e", "training::update_training::simple_training_data_calculate::calc_time_cost", "training::update_training::simple_training_data_calculate::perf_time_cost", "training::update_training::training_data_send::calc_time_cost_s", "training::update_training::training_data_send::enrich_attr_by_light_function_64AA0D", "training::update_training::training_data_send::log_debug_info_5D1B6D", "training::update_training::training_data_send::send_with_kafka_053B66", "training::update_training::training_data_send::calc_time_cost_e", "training::update_training::training_data_send::calc_time_cost", "training::update_training::training_data_send::perf_time_cost", "training::update_training::calc_time_cost_e", "training::update_training::calc_time_cost", "training::update_training::perf_time_cost", "training::expired_training::calc_time_cost_s", "training::expired_training::_branch_controller_A5673342", "training::expired_training::training_data_read::calc_time_cost_s", "training::expired_training::training_data_read::ad_calibrate_table_fetch_from_cache_enricher_FF11B6", "training::expired_training::training_data_read::log_debug_info_381C4D", "training::expired_training::training_data_read::enrich_attr_by_lua_095AF0", "training::expired_training::training_data_read::log_debug_info_381C4D", "training::expired_training::training_data_read::ad_calibrate_table_fetch_from_redis_observer_04C931", "training::expired_training::training_data_read::log_debug_info_381C4D", "training::expired_training::training_data_read::calc_time_cost_e", "training::expired_training::training_data_read::calc_time_cost", "training::expired_training::training_data_read::perf_time_cost", "training::expired_training::training_data_expired::calc_time_cost_s", "training::expired_training::training_data_expired::get_kconf_params", "training::expired_training::training_data_expired::_branch_controller_0A877908", "training::expired_training::training_data_expired::ad_calibrate_table_clear_to_local_cache_enricher_5C8BB1", "training::expired_training::training_data_expired::ad_calibrate_clear_redis_observer_74DDE7", "training::expired_training::training_data_expired::enrich_attr_by_light_function_904D95", "training::expired_training::training_data_expired::log_debug_info_3AA07E", "training::expired_training::training_data_expired::calc_time_cost_e", "training::expired_training::training_data_expired::calc_time_cost", "training::expired_training::training_data_expired::perf_time_cost", "training::expired_training::calc_time_cost_e", "training::expired_training::calc_time_cost", "training::expired_training::perf_time_cost", "training::calc_time_cost_e", "training::calc_time_cost", "training::perf_time_cost", "calc_time_cost_e", "calc_time_cost", "perf_time_cost"]}}}, "runner_pipeline_group": {"adlog": {"pipeline": ["ad_log_trigger_flow"], "thread_num": 10, "core_num_thread_ratio": 0.0}, "training": {"pipeline": ["calibrate_training_flow"], "thread_num": 1, "core_num_thread_ratio": 0.0}}, "service_identifier": "ad-model-calibrate-server-i18n", "kess_config": {}, "subflow_worker_thread_task_queue_size": 100, "sub_flow_worker_num": 500, "shard_num": 4, "zk_model_calibrate_server_main": {"zk_host": "hive2redis.sgp.zk.internal:2181", "zk_path": "/ks2/ad_i18n/ad_model_calibrate_server_main/master"}, "zk_model_calibrate_server_main_shard0": {"zk_host": "hive2redis.sgp.zk.internal:2181", "zk_path": "/ks2/ad_i18n/ad_model_calibrate_server_main_0/master"}, "zk_model_calibrate_server_main_shard1": {"zk_host": "hive2redis.sgp.zk.internal:2181", "zk_path": "/ks2/ad_i18n/ad_model_calibrate_server_main_1/master"}, "zk_model_calibrate_server_main_shard2": {"zk_host": "hive2redis.sgp.zk.internal:2181", "zk_path": "/ks2/ad_i18n/ad_model_calibrate_server_main_2/master"}, "zk_model_calibrate_server_main_shard3": {"zk_host": "hive2redis.sgp.zk.internal:2181", "zk_path": "/ks2/ad_i18n/ad_model_calibrate_server_main_3/master"}, "zk_model_calibrate_server_main_shard4": {"zk_host": "hive2redis.sgp.zk.internal:2181", "zk_path": "/ks2/ad_i18n/ad_model_calibrate_server_main_4/master"}, "zk_model_calibrate_server_backup": {"zk_host": "hive2redis.sgp.zk.internal:2181", "zk_path": "/ks2/ad_i18n/ad_model_calibrate_server_backup/master"}, "zk_model_calibrate_server_main_test_shard0": {"zk_host": "hive2redis.sgp.zk.internal:2181", "zk_path": "/ks2/ad_i18n/zk_model_calibrate_server_main_test_shard0/master"}, "leaf_flow_create_order": ["adlog", "update", "expired", "training"], "common_attr_types": [{"attr_name": "ad_log_dataframe_proto", "attr_type": "extra"}, {"attr_name": "check_table_empty", "attr_type": "int"}, {"attr_name": "cur_time_ms", "attr_type": "int"}, {"attr_name": "current_shard_id", "attr_type": "int"}, {"attr_name": "current_tag_num_column", "attr_type": "int"}, {"attr_name": "diff_test_env", "attr_type": "int"}, {"attr_name": "kafka_msg_fetch_timestamp", "attr_type": "int"}, {"attr_name": "message_enqueue_success", "attr_type": "int"}, {"attr_name": "subflow_shard_id", "attr_type": "int"}, {"attr_name": "tag_index_column", "attr_type": "int"}, {"attr_name": "total_tag_num_column", "attr_type": "int"}, {"attr_name": "zk_backup_stat", "attr_type": "int"}, {"attr_name": "zk_master_stat", "attr_type": "int"}], "item_attr_types": [{"table_name": "triggerTable", "attr_name": "ad_item_click", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "ad_item_impression", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "app_id", "attr_type": "string"}, {"table_name": "triggerTable", "attr_name": "bucket_conv_rate_sum_array", "attr_type": "int_list"}, {"table_name": "triggerTable", "attr_name": "bucket_end_event_num_array", "attr_type": "int_list"}, {"table_name": "triggerTable", "attr_name": "bucket_index_array", "attr_type": "int_list"}, {"table_name": "triggerTable", "attr_name": "bucket_start_event_num_array", "attr_type": "int_list"}, {"table_name": "triggerTable", "attr_name": "cmd_id", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "conv_rate_sum", "attr_type": "double"}, {"table_name": "triggerTable", "attr_name": "dimension_id", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "dimension_type", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "end_event_num", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "event_server_time_window", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "event_server_timestamp", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "event_type", "attr_type": "string"}, {"table_name": "triggerTable", "attr_name": "has_ad_click", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "impression_timestamp", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "invalid_data", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "key_hash", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "key_info", "attr_type": "string"}, {"table_name": "triggerTable", "attr_name": "key_prefix", "attr_type": "string"}, {"table_name": "triggerTable", "attr_name": "ocpx_action_type", "attr_type": "string"}, {"table_name": "triggerTable", "attr_name": "page_id", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "sample_type", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "start_event_num", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "start_event_time_window", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "start_event_timestamp", "attr_type": "int"}, {"table_name": "triggerTable", "attr_name": "trigger_type", "attr_type": "int"}]}