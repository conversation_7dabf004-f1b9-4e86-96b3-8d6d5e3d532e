{"_DRAGONFLY_VERSION": "0.7.11", "_DRAGONFLY_CREATE_TIME": "2023-04-25 15:16:25", "pipeline_manager_config": {"base_pipeline": {"type_name": "CommonRecoPipeline", "processor": {"_branch_controller_3F21F243": {"import_common_attr": ["skip_step_two"], "export_common_attr": ["_if_control_attr_4"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (skip_step_two == 1) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_3F21F243", "$code_info": "[if] A37B6ADF ad_log_inner_flow.py:1415 in unit_ocpm_step_two_workflow(): self.if_(\"skip_step_two == 1\")", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["skip_step_two"], "$output_common_attrs": ["_if_control_attr_4"], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_61BF1DD5": {"import_common_attr": ["is_skip_update"], "export_common_attr": ["_if_control_attr_5"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (is_skip_update == 1) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_61BF1DD5", "$code_info": "[if] 78BE3EB3 ad_log_inner_flow.py:1421 in unit_ocpm_step_two_workflow(): .if_(\"is_skip_update == 1\")", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["is_skip_update"], "$output_common_attrs": ["_if_control_attr_5"], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "inner_loop_strategy_data_save_mixer_37FA8E": {"tables": [{"table_name": "UnitTagTable", "table_category": "UnitTagTable", "save_all_columns": true, "local_cache_key_id": "local_cache_key_id"}], "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopStrategyDataSaveMixer"}, "inner_loop_step_one_to_redis_mixer_927214": {"item_table": "UnitTagTable", "redis_name": "adEngineFlowProcess", "is_step_two": true, "ttl": 86400, "has_bid_state_info_ptr": {"item_table": "TriggerTable", "column": "has_bid_state_info_ptr"}, "bid_state_info_ptr": {"item_table": "TriggerTable", "column": "bid_state_info_ptr"}, "message_seq": {"item_table": "TriggerTable", "column": "message_seq"}, "event_server_timestamp": {"item_table": "TriggerTable", "column": "event_server_timestamp"}, "unit_id": {"item_table": "UnitTagTable", "column": "unit_id"}, "msg_attr_info": {"msg_campaign_type": {"item_table": "TriggerTable", "column": "campaign_type"}, "msg_ocpc_action_type": {"item_table": "TriggerTable", "column": "ocpc_action_type"}, "msg_promotion_type": {"item_table": "TriggerTable", "column": "promotion_type"}, "msg_item_type": {"item_table": "TriggerTable", "column": "item_type"}, "msg_author_id": {"item_table": "TriggerTable", "column": "author_id"}, "msg_account_id": {"item_table": "TriggerTable", "column": "account_id"}, "msg_live_stream_id": {"item_table": "TriggerTable", "column": "live_stream_id"}, "msg_unit_id": {"item_table": "TriggerTable", "column": "unit_id"}, "msg_conv_num": {"item_table": "TriggerTable", "column": "conv_num"}, "msg_campaign_id": {"item_table": "TriggerTable", "column": "campaign_id"}, "msg_item_type_num": {"item_table": "TriggerTable", "column": "item_type_num"}, "msg_cost": {"item_table": "TriggerTable", "column": "cost"}, "msg_gmv": {"item_table": "TriggerTable", "column": "gmv"}, "msg_roi_ratio": {"item_table": "TriggerTable", "column": "roi_ratio"}, "msg_action_type": {"item_table": "TriggerTable", "column": "action_type"}, "msg_group_tag": {"item_table": "TriggerTable", "column": "inner_group_tag"}, "msg_bid_type": {"item_table": "TriggerTable", "column": "bid_type"}, "msg_medium_attribute": {"item_table": "TriggerTable", "column": "medium_attribute"}, "msg_speed_type": {"item_table": "TriggerTable", "column": "speed_type"}, "msg_delivery_timestamp": {"item_table": "TriggerTable", "column": "delivery_timestamp"}, "msg_cpa_bid": {"item_table": "TriggerTable", "column": "cpa_bid"}, "msg_is_soft": {"item_table": "TriggerTable", "column": "is_soft"}, "msg_target_cost": {"item_table": "TriggerTable", "column": "target_cost"}, "msg_target_gmv": {"item_table": "TriggerTable", "column": "target_gmv"}, "msg_separate_gsp_price": {"item_table": "TriggerTable", "column": "separate_gsp_price"}, "msg_record_gsp_price": {"item_table": "TriggerTable", "column": "record_gsp_price"}, "msg_pred_conv": {"item_table": "TriggerTable", "column": "pred_conv"}, "msg_pred_cvr_sum": {"item_table": "TriggerTable", "column": "pred_cvr_sum"}, "msg_pred_ctr_sum": {"item_table": "TriggerTable", "column": "pred_ctr_sum"}, "msg_price_before_billing_separate": {"item_table": "TriggerTable", "column": "price_before_billing_separate"}, "msg_price_after_billing_separate": {"item_table": "TriggerTable", "column": "price_after_billing_separate"}, "msg_ecpm": {"item_table": "TriggerTable", "column": "ecpm"}, "msg_auction_bid": {"item_table": "TriggerTable", "column": "auction_bid"}, "msg_price_ratio": {"item_table": "TriggerTable", "column": "price_ratio"}}, "bid_ctx_attr_info": {"bid_ctx_bid_context_key": {"item_table": "UnitTagTable", "column": "bid_context_key"}, "bid_ctx_group_tag": {"item_table": "UnitTagTable", "column": "group_tag"}, "bid_ctx_campaign_type": {"item_table": "UnitTagTable", "column": "campaign_type"}, "bid_ctx_ocpc_action_type": {"item_table": "UnitTagTable", "column": "ocpc_action_type"}, "bid_ctx_promotion_type": {"item_table": "UnitTagTable", "column": "promotion_type"}, "bid_ctx_item_type": {"item_table": "UnitTagTable", "column": "item_type"}, "bid_ctx_first_industry_name": {"item_table": "UnitTagTable", "column": "first_industry_name"}, "bid_ctx_group_tag_num": {"item_table": "UnitTagTable", "column": "group_tag_num"}, "bid_ctx_unit_id": {"item_table": "UnitTagTable", "column": "unit_id"}, "bid_ctx_account_id": {"item_table": "UnitTagTable", "column": "account_id"}, "bid_ctx_author_id": {"item_table": "UnitTagTable", "column": "author_id"}, "bid_ctx_live_stream_id": {"item_table": "UnitTagTable", "column": "live_stream_id"}, "bid_ctx_item_type_num": {"item_table": "UnitTagTable", "column": "item_type_num"}, "bid_ctx_campaign_id": {"item_table": "UnitTagTable", "column": "campaign_id"}, "bid_ctx_price_before_billing_separate": {"item_table": "UnitTagTable", "column": "price_before_billing_separate"}, "bid_ctx_price_after_billing_separate": {"item_table": "UnitTagTable", "column": "price_after_billing_separate"}, "bid_ctx_cost": {"item_table": "UnitTagTable", "column": "cost"}, "bid_ctx_gmv": {"item_table": "UnitTagTable", "column": "gmv"}, "bid_ctx_conv_num": {"item_table": "UnitTagTable", "column": "conv_num"}, "bid_ctx_cost_count": {"item_table": "UnitTagTable", "column": "cost_count"}, "bid_ctx_gmv_count": {"item_table": "UnitTagTable", "column": "gmv_count"}, "bid_ctx_delivery_cnt": {"item_table": "UnitTagTable", "column": "delivery_cnt"}, "bid_ctx_cpa_bid": {"item_table": "UnitTagTable", "column": "cpa_bid"}, "bid_ctx_relax_cpa_bid": {"item_table": "UnitTagTable", "column": "relax_cpa_bid"}, "bid_ctx_roi_ratio": {"item_table": "UnitTagTable", "column": "roi_ratio"}, "bid_ctx_relax_roi_ratio": {"item_table": "UnitTagTable", "column": "relax_roi_ratio"}, "bid_ctx_rt_cpa_bid": {"item_table": "UnitTagTable", "column": "rt_cpa_bid"}, "bid_ctx_rt_roas": {"item_table": "UnitTagTable", "column": "rt_roas"}, "bid_ctx_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "auto_cpa_bid"}, "bid_ctx_auto_roi_ratio": {"item_table": "UnitTagTable", "column": "auto_roi_ratio"}, "bid_ctx_old_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "old_auto_cpa_bid"}, "bid_ctx_old_auto_roi_ratio": {"item_table": "UnitTagTable", "column": "old_auto_roi_ratio"}, "bid_ctx_adjust_rate": {"item_table": "UnitTagTable", "column": "adjust_rate"}, "bid_ctx_rt_cost_speed": {"item_table": "UnitTagTable", "column": "rt_cost_speed"}, "bid_ctx_rt_delivery_speed": {"item_table": "UnitTagTable", "column": "rt_delivery_speed"}, "bid_ctx_dynamic_lower_bound_roas": {"item_table": "UnitTagTable", "column": "dynamic_lower_bound_roas"}, "bid_ctx_dynamic_upper_bound_roas": {"item_table": "UnitTagTable", "column": "dynamic_upper_bound_roas"}, "bid_ctx_dynamic_lower_bound_cpa_bid": {"item_table": "UnitTagTable", "column": "dynamic_lower_bound_cpa_bid"}, "bid_ctx_dynamic_upper_bound_cpa_bid": {"item_table": "UnitTagTable", "column": "dynamic_upper_bound_cpa_bid"}, "bid_ctx_target_cost": {"item_table": "UnitTagTable", "column": "target_cost"}, "bid_ctx_target_gmv": {"item_table": "UnitTagTable", "column": "target_gmv"}, "bid_ctx_record_gsp_price": {"item_table": "UnitTagTable", "column": "record_gsp_price"}, "bid_ctx_last_update_cost": {"item_table": "UnitTagTable", "column": "last_update_cost"}, "bid_ctx_adjust_auto_atv_rate": {"item_table": "UnitTagTable", "column": "adjust_auto_atv_rate"}, "bid_ctx_old_adjust_auto_atv_rate": {"item_table": "UnitTagTable", "column": "old_adjust_auto_atv_rate"}, "bid_ctx_reach_lower_bound_timestamp": {"item_table": "UnitTagTable", "column": "reach_lower_bound_timestamp"}, "bid_ctx_reach_upper_bound_timestamp": {"item_table": "UnitTagTable", "column": "reach_upper_bound_timestamp"}, "bid_ctx_first_delivery_timestamp_ms": {"item_table": "UnitTagTable", "column": "first_delivery_timestamp_ms"}, "bid_ctx_last_delivery_timestamp_ms": {"item_table": "UnitTagTable", "column": "last_delivery_timestamp_ms"}, "is_touch_bound": {"item_table": "UnitTagTable", "column": "is_touch_bound"}, "bid_ctx_last_update_adjust_timestamp": {"item_table": "UnitTagTable", "column": "last_update_adjust_timestamp"}, "bid_ctx_cost_start_timestamp_ms": {"item_table": "UnitTagTable", "column": "cost_start_timestamp_ms"}, "bid_ctx_last_update_context_timestamp": {"item_table": "UnitTagTable", "column": "last_update_context_timestamp"}, "bid_ctx_last_sync_context_timestamp": {"item_table": "UnitTagTable", "column": "last_sync_context_timestamp"}, "bid_ctx_online": {"item_table": "UnitTagTable", "column": "online"}, "bid_ctx_is_cold_start": {"item_table": "UnitTagTable", "column": "is_cold_start"}, "bid_ctx_speed_type": {"item_table": "UnitTagTable", "column": "speed_type"}, "bid_ctx_context_start_timestamp": {"item_table": "UnitTagTable", "column": "context_start_timestamp"}, "bid_ctx_last_sync_result_timestamp": {"item_table": "UnitTagTable", "column": "last_sync_result_timestamp"}, "bid_ctx_adjust_auto_value_rate": {"item_table": "UnitTagTable", "column": "adjust_auto_value_rate"}, "bid_ctx_total_ecpm": {"item_table": "UnitTagTable", "column": "total_ecpm"}, "bid_ctx_separate_gsp_price": {"item_table": "UnitTagTable", "column": "separate_gsp_price"}, "bid_ctx_total_auction_bid": {"item_table": "UnitTagTable", "column": "total_auction_bid"}, "bid_ctx_dry_up_base_value": {"item_table": "UnitTagTable", "column": "dry_up_base_value"}, "bid_ctx_ctr_sum": {"item_table": "UnitTagTable", "column": "ctr_sum"}, "bid_ctx_cvr_sum": {"item_table": "UnitTagTable", "column": "cvr_sum"}, "bid_ctx_softad_cost": {"item_table": "UnitTagTable", "column": "softad_cost"}, "bid_ctx_softad_target_cost": {"item_table": "UnitTagTable", "column": "softad_target_cost"}, "bid_ctx_softad_diff_ratio": {"item_table": "UnitTagTable", "column": "softad_diff_ratio"}, "bid_ctx_last_monitor_timestamp": {"item_table": "UnitTagTable", "column": "last_monitor_timestamp"}, "bid_ctx_user_cost_prior_algo": {"item_table": "UnitTagTable", "column": "user_cost_prior_algo"}, "bid_ctx_ad_status_tag": {"item_table": "UnitTagTable", "column": "ad_status_tag"}, "bid_ctx_old_is_out_of_budget": {"item_table": "UnitTagTable", "column": "old_is_out_of_budget"}, "bid_ctx_last_is_ad_open": {"item_table": "UnitTagTable", "column": "last_is_ad_open"}, "bid_ctx_last_ad_valid": {"item_table": "UnitTagTable", "column": "last_ad_valid"}, "bid_ctx_sync_context_interval_batch_batch_cost": {"item_table": "UnitTagTable", "column": "sync_context_interval_batch_batch_cost"}, "bid_ctx_sync_context_interval_batch_batch_gmv": {"item_table": "UnitTagTable", "column": "sync_context_interval_batch_batch_gmv"}, "bid_ctx_sync_context_interval_batch_batch_start_timestamp": {"item_table": "UnitTagTable", "column": "sync_context_interval_batch_batch_start_timestamp"}, "bid_ctx_cost_speed_context_batch_start_timestamp_ms": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_start_timestamp_ms"}, "bid_ctx_cost_speed_context_batch_value_sum": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_value_sum"}, "bid_ctx_cost_speed_context_batch_count": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_count"}, "bid_ctx_cost_speed_context_batch_value_mean": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_value_mean"}, "bid_ctx_cost_speed_context_batch_value_speed": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_value_speed"}, "bid_ctx_delivery_speed_context_batch_start_timestamp_ms": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_start_timestamp_ms"}, "bid_ctx_delivery_speed_context_batch_value_sum": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_value_sum"}, "bid_ctx_delivery_speed_context_batch_count": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_count"}, "bid_ctx_delivery_speed_context_batch_value_mean": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_value_mean"}, "bid_ctx_delivery_speed_context_batch_value_speed": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_value_speed"}, "bid_ctx_conv_ratio_adjust_context_pred_conv": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_pred_conv"}, "bid_ctx_conv_ratio_adjust_context_old_auto_conv_ratio": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_old_auto_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_auto_conv_ratio": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_auto_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_real_conv": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_real_conv"}, "bid_ctx_conv_ratio_adjust_context_last_update_conv_ratio_timestamp": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_last_update_conv_ratio_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_r1_upper_bound_timestamp": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_reach_r1_upper_bound_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_r1_lower_bound_timestamp": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_reach_r1_lower_bound_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp"}, "bid_ctx_price_ratio_context_batch_start_timestamp_ms": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_start_timestamp_ms"}, "bid_ctx_price_ratio_context_batch_total_price": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_total_price"}, "bid_ctx_price_ratio_context_batch_total_ecpm": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_total_ecpm"}, "bid_ctx_price_ratio_context_batch_total_price_ratio": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_total_price_ratio"}, "bid_ctx_price_ratio_context_batch_count": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_count"}, "bid_ctx_price_ratio_context_batch_avg_price_ratio": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_avg_price_ratio"}, "bid_ctx_price_ratio_context_batch_price_ratio_mean": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_price_ratio_mean"}, "bid_ctx_item_type_info_photo_to_live_cnt": {"item_table": "UnitTagTable", "column": "item_type_info_photo_to_live_cnt"}, "bid_ctx_item_type_info_direct_live_cnt": {"item_table": "UnitTagTable", "column": "item_type_info_direct_live_cnt"}, "bid_ctx_item_type_info_photo_to_live_cost": {"item_table": "UnitTagTable", "column": "item_type_info_photo_to_live_cost"}, "bid_ctx_item_type_info_direct_live_cost": {"item_table": "UnitTagTable", "column": "item_type_info_direct_live_cost"}}, "process_utils_attr_info": {"process_utils_ad_off_target_cost": {"item_table": "UnitTagTable", "column": "util_vals_ad_off_target_cost"}, "process_utils_new_adjust_auto_value_rate": {"item_table": "UnitTagTable", "column": "util_vals_new_adjust_auto_value_rate"}, "process_utils_old_adjust_auto_value_rate": {"item_table": "UnitTagTable", "column": "util_vals_old_adjust_auto_value_rate"}, "process_utils_is_bid_ptr_null": {"item_table": "UnitTagTable", "column": "util_vals_is_bid_ptr_null"}, "process_utils_pro_ocpc_not_been_set": {"item_table": "UnitTagTable", "column": "util_vals_pro_ocpc_not_been_set"}, "process_utils_reset_pacing": {"item_table": "UnitTagTable", "column": "util_vals_reset_pacing"}, "process_utils_is_cost_target_msg": {"item_table": "UnitTagTable", "column": "util_vals_is_cost_target_msg"}, "process_utils_enable_cost_prior_algo": {"item_table": "UnitTagTable", "column": "util_vals_enable_cost_prior_algo"}, "process_utils_enable_linear_pacing_cold_start_low_bound": {"item_table": "UnitTagTable", "column": "util_vals_enable_linear_pacing_cold_start_low_bound"}, "process_utils_enable_linear_pacing_grid_correction": {"item_table": "UnitTagTable", "column": "util_vals_enable_linear_pacing_grid_correction"}, "process_utils_enable_linear_adaptive_update_time": {"item_table": "UnitTagTable", "column": "util_vals_enable_linear_adaptive_update_time"}, "process_utils_enable_linear_after_cold_start_no_conv_drop": {"item_table": "UnitTagTable", "column": "util_vals_enable_linear_after_cold_start_no_conv_drop"}, "process_utils_disable_day_reset_exp": {"item_table": "UnitTagTable", "column": "util_vals_disable_day_reset_exp"}, "process_utils_enable_univ_inner_ad_delivery_replace": {"item_table": "UnitTagTable", "column": "util_vals_enable_univ_inner_ad_delivery_replace"}, "process_utils_is_apply_adjust": {"item_table": "UnitTagTable", "column": "util_vals_is_apply_adjust"}, "process_utils_is_debug": {"item_table": "UnitTagTable", "column": "util_vals_is_debug"}, "process_utils_is_roas_sub_pacing": {"item_table": "UnitTagTable", "column": "util_vals_is_roas_sub_pacing"}, "process_utils_is_ocpm_bid_process": {"item_table": "UnitTagTable", "column": "util_vals_is_ocpm_bid_process"}, "process_utils_is_apply_operation_config": {"item_table": "UnitTagTable", "column": "util_vals_is_apply_operation_config"}, "process_utils_is_start_process": {"item_table": "UnitTagTable", "column": "util_vals_is_start_process"}, "process_utils_is_debug_on": {"item_table": "UnitTagTable", "column": "util_vals_is_debug_on"}, "process_utils_is_normal": {"item_table": "UnitTagTable", "column": "util_vals_is_normal"}, "process_utils_is_fanstop": {"item_table": "UnitTagTable", "column": "util_vals_is_fanstop"}, "process_utils_is_fanstop_ocpm": {"item_table": "UnitTagTable", "column": "util_vals_is_fanstop_ocpm"}, "process_utils_is_roas": {"item_table": "UnitTagTable", "column": "util_vals_is_roas"}, "process_utils_is_no_bid": {"item_table": "UnitTagTable", "column": "util_vals_is_no_bid"}, "process_utils_is_skip_update": {"item_table": "UnitTagTable", "column": "util_vals_is_skip_update"}, "process_utils_is_nothing_to_do": {"item_table": "UnitTagTable", "column": "util_vals_is_nothing_to_do"}, "process_utils_is_process": {"item_table": "UnitTagTable", "column": "util_vals_is_process"}, "process_utils_is_reset_context": {"item_table": "UnitTagTable", "column": "util_vals_is_reset_context"}, "process_utils_is_update_adjust": {"item_table": "UnitTagTable", "column": "util_vals_is_update_adjust"}, "process_utils_is_interval_cost": {"item_table": "UnitTagTable", "column": "util_vals_is_interval_cost"}, "process_utils_is_update_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_is_update_interval_ms"}, "process_utils_is_ad_open": {"item_table": "UnitTagTable", "column": "util_vals_is_ad_open"}, "process_utils_is_sync_to_dsp": {"item_table": "UnitTagTable", "column": "util_vals_is_sync_to_dsp"}, "process_utils_is_monitor": {"item_table": "UnitTagTable", "column": "util_vals_is_monitor"}, "process_utils_is_cold_start": {"item_table": "UnitTagTable", "column": "util_vals_is_cold_start"}, "process_utils_is_costcap_nobid": {"item_table": "UnitTagTable", "column": "util_vals_is_costcap_nobid"}, "process_utils_is_skip_cold_start": {"item_table": "UnitTagTable", "column": "util_vals_is_skip_cold_start"}, "process_utils_is_on_live": {"item_table": "UnitTagTable", "column": "util_vals_is_on_live"}, "process_utils_is_out_of_budget": {"item_table": "UnitTagTable", "column": "util_vals_is_out_of_budget"}, "process_utils_old_is_out_of_budget": {"item_table": "UnitTagTable", "column": "util_vals_old_is_out_of_budget"}, "process_utils_advertisable": {"item_table": "UnitTagTable", "column": "util_vals_advertisable"}, "process_utils_is_status_open": {"item_table": "UnitTagTable", "column": "util_vals_is_status_open"}, "process_utils_ad_status": {"item_table": "UnitTagTable", "column": "util_vals_ad_status"}, "process_utils_unit_id": {"item_table": "UnitTagTable", "column": "util_vals_unit_id"}, "process_utils_thread_id": {"item_table": "UnitTagTable", "column": "util_vals_thread_id"}, "process_utils_is_bid_info": {"item_table": "UnitTagTable", "column": "util_vals_is_bid_info"}, "process_utils_is_online": {"item_table": "UnitTagTable", "column": "util_vals_is_online"}, "process_utils_is_explore": {"item_table": "UnitTagTable", "column": "util_vals_is_explore"}, "process_utils_ab_exp_ratio": {"item_table": "UnitTagTable", "column": "util_vals_ab_exp_ratio"}, "process_utils_promotion_type_num": {"item_table": "UnitTagTable", "column": "util_vals_promotion_type_num"}, "process_utils_item_type_num": {"item_table": "UnitTagTable", "column": "util_vals_item_type_num"}, "process_utils_prior_cost": {"item_table": "UnitTagTable", "column": "util_vals_prior_cost"}, "process_utils_cold_start_cost_li_thr": {"item_table": "UnitTagTable", "column": "util_vals_cold_start_cost_li_thr"}, "process_utils_cold_start_low_bound": {"item_table": "UnitTagTable", "column": "util_vals_cold_start_low_bound"}, "process_utils_rt_cost_speed": {"item_table": "UnitTagTable", "column": "util_vals_rt_cost_speed"}, "process_utils_rt_delivery_speed": {"item_table": "UnitTagTable", "column": "util_vals_rt_delivery_speed"}, "process_utils_target_roas": {"item_table": "UnitTagTable", "column": "util_vals_target_roas"}, "process_utils_is_price_ratio_bound": {"item_table": "UnitTagTable", "column": "util_vals_is_price_ratio_bound"}, "process_utils_is_acc_explore_bid": {"item_table": "UnitTagTable", "column": "util_vals_is_acc_explore_bid"}, "process_utils_dynamic_bid_lower_bound_adjust_time_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms"}, "process_utils_dynamic_bid_upper_bound_adjust_time_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms"}, "process_utils_dynamic_bid_lower_bound_adjust_rate": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_rate"}, "process_utils_dynamic_bid_upper_bound_adjust_rate": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_rate"}, "process_utils_p_weight": {"item_table": "UnitTagTable", "column": "util_vals_p_weight"}, "process_utils_i_weight": {"item_table": "UnitTagTable", "column": "util_vals_i_weight"}, "process_utils_d_weight": {"item_table": "UnitTagTable", "column": "util_vals_d_weight"}, "process_utils_explore_p_weight": {"item_table": "UnitTagTable", "column": "util_vals_explore_p_weight"}, "process_utils_explore_i_weight": {"item_table": "UnitTagTable", "column": "util_vals_explore_i_weight"}, "process_utils_explore_d_weight": {"item_table": "UnitTagTable", "column": "util_vals_explore_d_weight"}, "process_utils_p_val": {"item_table": "UnitTagTable", "column": "util_vals_p_val"}, "process_utils_i_val": {"item_table": "UnitTagTable", "column": "util_vals_i_val"}, "process_utils_d_val": {"item_table": "UnitTagTable", "column": "util_vals_d_val"}, "process_utils_pacing_weight": {"item_table": "UnitTagTable", "column": "util_vals_pacing_weight"}, "process_utils_target_cost_speed": {"item_table": "UnitTagTable", "column": "util_vals_target_cost_speed"}, "process_utils_target_delivery_speed": {"item_table": "UnitTagTable", "column": "util_vals_target_delivery_speed"}, "process_utils_start_bid_rate": {"item_table": "UnitTagTable", "column": "util_vals_start_bid_rate"}, "process_utils_start_bid_rate_sub_pacing": {"item_table": "UnitTagTable", "column": "util_vals_start_bid_rate_sub_pacing"}, "process_utils_hard_upper_bound_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "util_vals_hard_upper_bound_auto_cpa_bid"}, "process_utils_hard_lower_bound_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "util_vals_hard_lower_bound_auto_cpa_bid"}, "process_utils_hard_lower_bound_auto_roas": {"item_table": "UnitTagTable", "column": "util_vals_hard_lower_bound_auto_roas"}, "process_utils_hard_upper_bound_auto_roas": {"item_table": "UnitTagTable", "column": "util_vals_hard_upper_bound_auto_roas"}, "process_utils_hard_lower_bound_auto_atv": {"item_table": "UnitTagTable", "column": "util_vals_hard_lower_bound_auto_atv"}, "process_utils_hard_upper_bound_auto_atv": {"item_table": "UnitTagTable", "column": "util_vals_hard_upper_bound_auto_atv"}, "process_utils_acc_explore_bid_target_cost_speed": {"item_table": "UnitTagTable", "column": "util_vals_acc_explore_bid_target_cost_speed"}, "process_utils_current_ms": {"item_table": "UnitTagTable", "column": "util_vals_current_ms"}, "process_utils_ad_short_type": {"item_table": "UnitTagTable", "column": "util_vals_ad_short_type"}, "process_utils_is_target_modify": {"item_table": "UnitTagTable", "column": "util_vals_is_target_modify"}, "process_utils_target_modify_ratio": {"item_table": "UnitTagTable", "column": "util_vals_target_modify_ratio"}, "process_utils_p_weight_conv": {"item_table": "UnitTagTable", "column": "util_vals_p_weight_conv"}, "process_utils_i_weight_conv": {"item_table": "UnitTagTable", "column": "util_vals_i_weight_conv"}, "process_utils_d_weight_conv": {"item_table": "UnitTagTable", "column": "util_vals_d_weight_conv"}, "process_utils_p_val_conv": {"item_table": "UnitTagTable", "column": "util_vals_p_val_conv"}, "process_utils_i_val_conv": {"item_table": "UnitTagTable", "column": "util_vals_i_val_conv"}, "process_utils_d_val_conv": {"item_table": "UnitTagTable", "column": "util_vals_d_val_conv"}, "process_utils_pacing_weight_conv": {"item_table": "UnitTagTable", "column": "util_vals_pacing_weight_conv"}, "process_utils_p_weight_cost": {"item_table": "UnitTagTable", "column": "util_vals_p_weight_cost"}, "process_utils_i_weight_cost": {"item_table": "UnitTagTable", "column": "util_vals_i_weight_cost"}, "process_utils_d_weight_cost": {"item_table": "UnitTagTable", "column": "util_vals_d_weight_cost"}, "process_utils_p_val_cost": {"item_table": "UnitTagTable", "column": "util_vals_p_val_cost"}, "process_utils_i_val_cost": {"item_table": "UnitTagTable", "column": "util_vals_i_val_cost"}, "process_utils_d_val_cost": {"item_table": "UnitTagTable", "column": "util_vals_d_val_cost"}, "process_utils_pacing_weight_cost": {"item_table": "UnitTagTable", "column": "util_vals_pacing_weight_cost"}, "process_utils_update_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_update_interval_ms"}, "process_utils_interval_cost": {"item_table": "UnitTagTable", "column": "util_vals_interval_cost"}, "process_utils_step_bid_rate_increase": {"item_table": "UnitTagTable", "column": "util_vals_step_bid_rate_increase"}, "process_utils_step_bid_rate_descent": {"item_table": "UnitTagTable", "column": "util_vals_step_bid_rate_descent"}, "process_utils_ad_status_tag": {"item_table": "UnitTagTable", "column": "util_vals_ad_status_tag"}, "process_utils_ad_status_tag_change": {"item_table": "UnitTagTable", "column": "util_vals_ad_status_tag_change"}, "process_utils_total_pred_conv": {"item_table": "UnitTagTable", "column": "util_vals_total_pred_conv"}, "process_utils_adjust_rate_lower_bound": {"item_table": "UnitTagTable", "column": "util_vals_adjust_rate_lower_bound"}, "process_utils_adjust_rate_upper_bound": {"item_table": "UnitTagTable", "column": "util_vals_adjust_rate_upper_bound"}, "process_utils_budget": {"item_table": "UnitTagTable", "column": "util_vals_budget"}, "process_utils_hard_bid_upper_bound_rate_monitor": {"item_table": "UnitTagTable", "column": "util_vals_hard_bid_upper_bound_rate_monitor"}, "process_utils_hard_bid_lower_bound_rate_monitor": {"item_table": "UnitTagTable", "column": "util_vals_hard_bid_lower_bound_rate_monitor"}, "process_utils_fanstop_adjust_rate_lower_bound_roi": {"item_table": "UnitTagTable", "column": "util_vals_fanstop_adjust_rate_lower_bound_roi"}, "process_utils_fanstop_adjust_rate_upper_bound_roi": {"item_table": "UnitTagTable", "column": "util_vals_fanstop_adjust_rate_upper_bound_roi"}, "process_utils_fanstop_adjust_rate_lower_bound_cpa": {"item_table": "UnitTagTable", "column": "util_vals_fanstop_adjust_rate_lower_bound_cpa"}, "process_utils_fanstop_adjust_rate_upper_bound_cpa": {"item_table": "UnitTagTable", "column": "util_vals_fanstop_adjust_rate_upper_bound_cpa"}, "process_utils_pacing_type": {"item_table": "UnitTagTable", "column": "util_vals_pacing_type"}, "process_utils_pacing_method_type": {"item_table": "UnitTagTable", "column": "util_vals_pacing_method_type"}}, "$metadata": {"$output_item_attrs": [], "$input_item_attrs": ["TriggerTable::account_id", "TriggerTable::action_type", "TriggerTable::auction_bid", "TriggerTable::author_id", "TriggerTable::bid_type", "TriggerTable::campaign_id", "TriggerTable::campaign_type", "TriggerTable::conv_num", "TriggerTable::cost", "TriggerTable::cpa_bid", "TriggerTable::delivery_timestamp", "TriggerTable::ecpm", "TriggerTable::event_server_timestamp", "TriggerTable::gmv", "TriggerTable::inner_group_tag", "TriggerTable::is_soft", "TriggerTable::item_type", "TriggerTable::item_type_num", "TriggerTable::live_stream_id", "TriggerTable::medium_attribute", "TriggerTable::message_seq", "TriggerTable::ocpc_action_type", "TriggerTable::pred_conv", "TriggerTable::pred_ctr_sum", "TriggerTable::pred_cvr_sum", "TriggerTable::price_after_billing_separate", "TriggerTable::price_before_billing_separate", "TriggerTable::price_ratio", "TriggerTable::promotion_type", "TriggerTable::record_gsp_price", "TriggerTable::roi_ratio", "TriggerTable::separate_gsp_price", "TriggerTable::speed_type", "TriggerTable::target_cost", "TriggerTable::target_gmv", "TriggerTable::unit_id", "UnitTagTable::account_id", "UnitTagTable::ad_status_tag", "UnitTagTable::adjust_auto_atv_rate", "UnitTagTable::adjust_auto_value_rate", "UnitTagTable::adjust_rate", "UnitTagTable::author_id", "UnitTagTable::auto_cpa_bid", "UnitTagTable::auto_roi_ratio", "UnitTagTable::bid_context_key", "UnitTagTable::bid_state_info_ptr", "UnitTagTable::campaign_id", "UnitTagTable::campaign_type", "UnitTagTable::context_start_timestamp", "UnitTagTable::conv_num", "UnitTagTable::conv_ratio_adjust_context_auto_conv_ratio", "UnitTagTable::conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio", "UnitTagTable::conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio", "UnitTagTable::conv_ratio_adjust_context_last_update_conv_ratio_timestamp", "UnitTagTable::conv_ratio_adjust_context_old_auto_conv_ratio", "UnitTagTable::conv_ratio_adjust_context_pred_conv", "UnitTagTable::conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp", "UnitTagTable::conv_ratio_adjust_context_reach_r1_lower_bound_timestamp", "UnitTagTable::conv_ratio_adjust_context_reach_r1_upper_bound_timestamp", "UnitTagTable::conv_ratio_adjust_context_real_conv", "UnitTagTable::cost", "UnitTagTable::cost_count", "UnitTagTable::cost_speed_context_batch_count", "UnitTagTable::cost_speed_context_batch_start_timestamp_ms", "UnitTagTable::cost_speed_context_batch_value_mean", "UnitTagTable::cost_speed_context_batch_value_speed", "UnitTagTable::cost_speed_context_batch_value_sum", "UnitTagTable::cost_start_timestamp_ms", "UnitTagTable::cpa_bid", "UnitTagTable::ctr_sum", "UnitTagTable::cvr_sum", "UnitTagTable::delivery_cnt", "UnitTagTable::delivery_speed_context_batch_count", "UnitTagTable::delivery_speed_context_batch_start_timestamp_ms", "UnitTagTable::delivery_speed_context_batch_value_mean", "UnitTagTable::delivery_speed_context_batch_value_speed", "UnitTagTable::delivery_speed_context_batch_value_sum", "UnitTagTable::dry_up_base_value", "UnitTagTable::dynamic_lower_bound_cpa_bid", "UnitTagTable::dynamic_lower_bound_roas", "UnitTagTable::dynamic_upper_bound_cpa_bid", "UnitTagTable::dynamic_upper_bound_roas", "UnitTagTable::first_delivery_timestamp_ms", "UnitTagTable::first_industry_name", "UnitTagTable::gmv", "UnitTagTable::gmv_count", "UnitTagTable::group_tag", "UnitTagTable::group_tag_num", "UnitTagTable::has_bid_state_info_ptr", "UnitTagTable::is_cold_start", "UnitTagTable::is_touch_bound", "UnitTagTable::item_type", "UnitTagTable::item_type_info_direct_live_cnt", "UnitTagTable::item_type_info_direct_live_cost", "UnitTagTable::item_type_info_photo_to_live_cnt", "UnitTagTable::item_type_info_photo_to_live_cost", "UnitTagTable::item_type_num", "UnitTagTable::last_ad_valid", "UnitTagTable::last_delivery_timestamp_ms", "UnitTagTable::last_is_ad_open", "UnitTagTable::last_monitor_timestamp", "UnitTagTable::last_sync_context_timestamp", "UnitTagTable::last_sync_result_timestamp", "UnitTagTable::last_update_adjust_timestamp", "UnitTagTable::last_update_context_timestamp", "UnitTagTable::last_update_cost", "UnitTagTable::live_stream_id", "UnitTagTable::ocpc_action_type", "UnitTagTable::old_adjust_auto_atv_rate", "UnitTagTable::old_auto_cpa_bid", "UnitTagTable::old_auto_roi_ratio", "UnitTagTable::old_is_out_of_budget", "UnitTagTable::online", "UnitTagTable::price_after_billing_separate", "UnitTagTable::price_before_billing_separate", "UnitTagTable::price_ratio_context_batch_avg_price_ratio", "UnitTagTable::price_ratio_context_batch_count", "UnitTagTable::price_ratio_context_batch_price_ratio_mean", "UnitTagTable::price_ratio_context_batch_start_timestamp_ms", "UnitTagTable::price_ratio_context_batch_total_ecpm", "UnitTagTable::price_ratio_context_batch_total_price", "UnitTagTable::price_ratio_context_batch_total_price_ratio", "UnitTagTable::promotion_type", "UnitTagTable::reach_lower_bound_timestamp", "UnitTagTable::reach_upper_bound_timestamp", "UnitTagTable::record_gsp_price", "UnitTagTable::relax_cpa_bid", "UnitTagTable::relax_roi_ratio", "UnitTagTable::roi_ratio", "UnitTagTable::rt_cost_speed", "UnitTagTable::rt_cpa_bid", "UnitTagTable::rt_delivery_speed", "UnitTagTable::rt_roas", "UnitTagTable::separate_gsp_price", "UnitTagTable::softad_cost", "UnitTagTable::softad_diff_ratio", "UnitTagTable::softad_target_cost", "UnitTagTable::speed_type", "UnitTagTable::sync_context_interval_batch_batch_cost", "UnitTagTable::sync_context_interval_batch_batch_gmv", "UnitTagTable::sync_context_interval_batch_batch_start_timestamp", "UnitTagTable::target_cost", "UnitTagTable::target_gmv", "UnitTagTable::total_auction_bid", "UnitTagTable::total_ecpm", "UnitTagTable::unit_id", "UnitTagTable::user_cost_prior_algo", "UnitTagTable::util_vals_ab_exp_ratio", "UnitTagTable::util_vals_acc_explore_bid_target_cost_speed", "UnitTagTable::util_vals_ad_off_target_cost", "UnitTagTable::util_vals_ad_short_type", "UnitTagTable::util_vals_ad_status", "UnitTagTable::util_vals_ad_status_tag", "UnitTagTable::util_vals_ad_status_tag_change", "UnitTagTable::util_vals_adjust_rate_lower_bound", "UnitTagTable::util_vals_adjust_rate_upper_bound", "UnitTagTable::util_vals_advertisable", "UnitTagTable::util_vals_budget", "UnitTagTable::util_vals_cold_start_cost_li_thr", "UnitTagTable::util_vals_cold_start_low_bound", "UnitTagTable::util_vals_current_ms", "UnitTagTable::util_vals_d_val", "UnitTagTable::util_vals_d_val_conv", "UnitTagTable::util_vals_d_val_cost", "UnitTagTable::util_vals_d_weight", "UnitTagTable::util_vals_d_weight_conv", "UnitTagTable::util_vals_d_weight_cost", "UnitTagTable::util_vals_disable_day_reset_exp", "UnitTagTable::util_vals_dynamic_bid_lower_bound_adjust_rate", "UnitTagTable::util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms", "UnitTagTable::util_vals_dynamic_bid_upper_bound_adjust_rate", "UnitTagTable::util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms", "UnitTagTable::util_vals_enable_cost_prior_algo", "UnitTagTable::util_vals_enable_linear_adaptive_update_time", "UnitTagTable::util_vals_enable_linear_after_cold_start_no_conv_drop", "UnitTagTable::util_vals_enable_linear_pacing_cold_start_low_bound", "UnitTagTable::util_vals_enable_linear_pacing_grid_correction", "UnitTagTable::util_vals_enable_univ_inner_ad_delivery_replace", "UnitTagTable::util_vals_explore_d_weight", "UnitTagTable::util_vals_explore_i_weight", "UnitTagTable::util_vals_explore_p_weight", "UnitTagTable::util_vals_fanstop_adjust_rate_lower_bound_cpa", "UnitTagTable::util_vals_fanstop_adjust_rate_lower_bound_roi", "UnitTagTable::util_vals_fanstop_adjust_rate_upper_bound_cpa", "UnitTagTable::util_vals_fanstop_adjust_rate_upper_bound_roi", "UnitTagTable::util_vals_hard_bid_lower_bound_rate_monitor", "UnitTagTable::util_vals_hard_bid_upper_bound_rate_monitor", "UnitTagTable::util_vals_hard_lower_bound_auto_atv", "UnitTagTable::util_vals_hard_lower_bound_auto_cpa_bid", "UnitTagTable::util_vals_hard_lower_bound_auto_roas", "UnitTagTable::util_vals_hard_upper_bound_auto_atv", "UnitTagTable::util_vals_hard_upper_bound_auto_cpa_bid", "UnitTagTable::util_vals_hard_upper_bound_auto_roas", "UnitTagTable::util_vals_i_val", "UnitTagTable::util_vals_i_val_conv", "UnitTagTable::util_vals_i_val_cost", "UnitTagTable::util_vals_i_weight", "UnitTagTable::util_vals_i_weight_conv", "UnitTagTable::util_vals_i_weight_cost", "UnitTagTable::util_vals_interval_cost", "UnitTagTable::util_vals_is_acc_explore_bid", "UnitTagTable::util_vals_is_ad_open", "UnitTagTable::util_vals_is_apply_adjust", "UnitTagTable::util_vals_is_apply_operation_config", "UnitTagTable::util_vals_is_bid_info", "UnitTagTable::util_vals_is_bid_ptr_null", "UnitTagTable::util_vals_is_cold_start", "UnitTagTable::util_vals_is_cost_target_msg", "UnitTagTable::util_vals_is_costcap_nobid", "UnitTagTable::util_vals_is_debug", "UnitTagTable::util_vals_is_debug_on", "UnitTagTable::util_vals_is_explore", "UnitTagTable::util_vals_is_fanstop", "UnitTagTable::util_vals_is_fanstop_ocpm", "UnitTagTable::util_vals_is_interval_cost", "UnitTagTable::util_vals_is_monitor", "UnitTagTable::util_vals_is_no_bid", "UnitTagTable::util_vals_is_normal", "UnitTagTable::util_vals_is_nothing_to_do", "UnitTagTable::util_vals_is_ocpm_bid_process", "UnitTagTable::util_vals_is_on_live", "UnitTagTable::util_vals_is_online", "UnitTagTable::util_vals_is_out_of_budget", "UnitTagTable::util_vals_is_price_ratio_bound", "UnitTagTable::util_vals_is_process", "UnitTagTable::util_vals_is_reset_context", "UnitTagTable::util_vals_is_roas", "UnitTagTable::util_vals_is_roas_sub_pacing", "UnitTagTable::util_vals_is_skip_cold_start", "UnitTagTable::util_vals_is_skip_update", "UnitTagTable::util_vals_is_start_process", "UnitTagTable::util_vals_is_status_open", "UnitTagTable::util_vals_is_sync_to_dsp", "UnitTagTable::util_vals_is_target_modify", "UnitTagTable::util_vals_is_update_adjust", "UnitTagTable::util_vals_is_update_interval_ms", "UnitTagTable::util_vals_item_type_num", "UnitTagTable::util_vals_new_adjust_auto_value_rate", "UnitTagTable::util_vals_old_adjust_auto_value_rate", "UnitTagTable::util_vals_old_is_out_of_budget", "UnitTagTable::util_vals_p_val", "UnitTagTable::util_vals_p_val_conv", "UnitTagTable::util_vals_p_val_cost", "UnitTagTable::util_vals_p_weight", "UnitTagTable::util_vals_p_weight_conv", "UnitTagTable::util_vals_p_weight_cost", "UnitTagTable::util_vals_pacing_method_type", "UnitTagTable::util_vals_pacing_type", "UnitTagTable::util_vals_pacing_weight", "UnitTagTable::util_vals_pacing_weight_conv", "UnitTagTable::util_vals_pacing_weight_cost", "UnitTagTable::util_vals_prior_cost", "UnitTagTable::util_vals_pro_ocpc_not_been_set", "UnitTagTable::util_vals_promotion_type_num", "UnitTagTable::util_vals_reset_pacing", "UnitTagTable::util_vals_rt_cost_speed", "UnitTagTable::util_vals_rt_delivery_speed", "UnitTagTable::util_vals_start_bid_rate", "UnitTagTable::util_vals_start_bid_rate_sub_pacing", "UnitTagTable::util_vals_step_bid_rate_descent", "UnitTagTable::util_vals_step_bid_rate_increase", "UnitTagTable::util_vals_target_cost_speed", "UnitTagTable::util_vals_target_delivery_speed", "UnitTagTable::util_vals_target_modify_ratio", "UnitTagTable::util_vals_target_roas", "UnitTagTable::util_vals_thread_id", "UnitTagTable::util_vals_total_pred_conv", "UnitTagTable::util_vals_unit_id", "UnitTagTable::util_vals_update_interval_ms"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopStepOneWriteToRedisMixer"}, "inner_loop_monitor_mixer_E92C81": {"item_table": "UnitTagTable", "bid_context_attr_info": {"unit_id": {"item_table": "UnitTagTable", "column": "unit_id"}, "account_id": {"item_table": "UnitTagTable", "column": "account_id"}, "author_id": {"item_table": "UnitTagTable", "column": "author_id"}, "campaign_type": {"item_table": "UnitTagTable", "column": "campaign_type"}, "ocpc_action_type": {"item_table": "UnitTagTable", "column": "ocpc_action_type"}, "group_tag": {"item_table": "UnitTagTable", "column": "group_tag"}, "item_type": {"item_table": "UnitTagTable", "column": "item_type"}, "msg_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "auto_cpa_bid"}, "speed_type": {"item_table": "UnitTagTable", "column": "speed_type"}, "last_monitor_timestamp": {"item_table": "UnitTagTable", "column": "last_monitor_timestamp"}, "auto_roi_ratio": {"item_table": "UnitTagTable", "column": "auto_roi_ratio"}, "auto_cpa_bid": {"item_table": "UnitTagTable", "column": "auto_cpa_bid"}, "adjust_auto_value_rate": {"item_table": "UnitTagTable", "column": "adjust_auto_value_rate"}, "cost": {"item_table": "UnitTagTable", "column": "cost"}, "target_cost": {"item_table": "UnitTagTable", "column": "target_cost"}, "price_before_billing_separate": {"item_table": "UnitTagTable", "column": "price_before_billing_separate"}, "price_after_billing_separate": {"item_table": "UnitTagTable", "column": "price_after_billing_separate"}, "roi_ratio": {"item_table": "UnitTagTable", "column": "roi_ratio"}, "cpa_bid": {"item_table": "UnitTagTable", "column": "cpa_bid"}}, "util_vals_attr_info": {"util_vals_is_monitor": {"item_table": "UnitTagTable", "column": "util_vals_is_monitor"}, "util_vals_is_update_adjust": {"item_table": "UnitTagTable", "column": "util_vals_is_update_adjust"}, "util_vals_ad_status_tag_change": {"item_table": "UnitTagTable", "column": "util_vals_ad_status_tag_change"}, "util_vals_ad_short_type": {"item_table": "UnitTagTable", "column": "util_vals_ad_short_type"}, "util_vals_is_debug": {"item_table": "UnitTagTable", "column": "util_vals_is_debug"}, "util_vals_is_debug_on": {"item_table": "UnitTagTable", "column": "util_vals_is_debug_on"}, "util_vals_is_roas": {"item_table": "UnitTagTable", "column": "util_vals_is_roas"}, "util_vals_hard_bid_upper_bound_rate_monitor": {"item_table": "UnitTagTable", "column": "util_vals_hard_bid_upper_bound_rate_monitor"}, "util_vals_hard_bid_lower_bound_rate_monitor": {"item_table": "UnitTagTable", "column": "util_vals_hard_bid_lower_bound_rate_monitor"}, "util_vals_adjust_rate_lower_bound": {"item_table": "UnitTagTable", "column": "util_vals_adjust_rate_lower_bound"}, "util_vals_adjust_rate_upper_bound": {"item_table": "UnitTagTable", "column": "util_vals_adjust_rate_upper_bound"}, "util_vals_dynamic_bid_upper_bound_adjust_rate": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_rate"}, "util_vals_dynamic_bid_lower_bound_adjust_rate": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_rate"}, "util_vals_thread_id": {"item_table": "UnitTagTable", "column": "util_vals_thread_id"}, "util_vals_p_val": {"item_table": "UnitTagTable", "column": "util_vals_p_val"}, "util_vals_i_val": {"item_table": "UnitTagTable", "column": "util_vals_i_val"}, "util_vals_d_val": {"item_table": "UnitTagTable", "column": "util_vals_d_val"}, "util_vals_new_adjust_auto_value_rate": {"item_table": "UnitTagTable", "column": "util_vals_new_adjust_auto_value_rate"}, "util_vals_budget": {"item_table": "UnitTagTable", "column": "util_vals_budget"}, "util_vals_is_ocpm_bid_process": {"item_table": "UnitTagTable", "column": "util_vals_is_ocpm_bid_process"}, "util_vals_is_cold_start": {"item_table": "UnitTagTable", "column": "util_vals_is_cold_start"}}, "$metadata": {"$output_item_attrs": [], "$input_item_attrs": ["UnitTagTable::account_id", "UnitTagTable::adjust_auto_value_rate", "UnitTagTable::author_id", "UnitTagTable::auto_cpa_bid", "UnitTagTable::auto_roi_ratio", "UnitTagTable::campaign_type", "UnitTagTable::cost", "UnitTagTable::cpa_bid", "UnitTagTable::group_tag", "UnitTagTable::item_type", "UnitTagTable::last_monitor_timestamp", "UnitTagTable::ocpc_action_type", "UnitTagTable::price_after_billing_separate", "UnitTagTable::price_before_billing_separate", "UnitTagTable::roi_ratio", "UnitTagTable::speed_type", "UnitTagTable::target_cost", "UnitTagTable::unit_id", "UnitTagTable::util_vals_ad_short_type", "UnitTagTable::util_vals_ad_status_tag_change", "UnitTagTable::util_vals_adjust_rate_lower_bound", "UnitTagTable::util_vals_adjust_rate_upper_bound", "UnitTagTable::util_vals_budget", "UnitTagTable::util_vals_d_val", "UnitTagTable::util_vals_dynamic_bid_lower_bound_adjust_rate", "UnitTagTable::util_vals_dynamic_bid_upper_bound_adjust_rate", "UnitTagTable::util_vals_hard_bid_lower_bound_rate_monitor", "UnitTagTable::util_vals_hard_bid_upper_bound_rate_monitor", "UnitTagTable::util_vals_i_val", "UnitTagTable::util_vals_is_cold_start", "UnitTagTable::util_vals_is_debug", "UnitTagTable::util_vals_is_debug_on", "UnitTagTable::util_vals_is_monitor", "UnitTagTable::util_vals_is_ocpm_bid_process", "UnitTagTable::util_vals_is_roas", "UnitTagTable::util_vals_is_update_adjust", "UnitTagTable::util_vals_new_adjust_auto_value_rate", "UnitTagTable::util_vals_p_val", "UnitTagTable::util_vals_thread_id"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerloopMonitorMixer"}, "innerloop_bid_result_store_96A878": {"item_table": "UnitTagTable", "redis_name": "BidServerGraphTest", "skip_wait_index": false, "candidate_redis_name": "BidServerGraphTest", "no_diff_redis": "adEngineFlowProcess", "topic": "ad_auto_bid_table_test", "trace_topic": "ad_auto_bid_table_test", "auto_bid_topic": "ad_auto_bid_table_test", "ttl": 86400, "bid_context_attr_info": {"campaign_id": {"item_table": "UnitTagTable", "column": "campaign_id"}, "account_id": {"item_table": "UnitTagTable", "column": "account_id"}, "unit_id": {"item_table": "UnitTagTable", "column": "unit_id"}, "is_debug": {"item_table": "UnitTagTable", "column": "is_debug"}, "thread_id": {"item_table": "UnitTagTable", "column": "thread_id"}, "group_tag": {"item_table": "UnitTagTable", "column": "group_tag"}, "event_server_timestamp": {"item_table": "UnitTagTable", "column": "event_server_timestamp"}, "update_timestamp": {"item_table": "UnitTagTable", "column": "update_timestamp"}, "last_sync_result_timestamp": {"item_table": "UnitTagTable", "column": "last_sync_result_timestamp"}, "campaign_type": {"item_table": "UnitTagTable", "column": "campaign_type"}, "ocpc_action_type": {"item_table": "UnitTagTable", "column": "ocpc_action_type"}, "bid_context_total_cv": {"item_table": "UnitTagTable", "column": "total_cv"}, "bid_context_has_risk": {"item_table": "UnitTagTable", "column": "has_risk"}, "bid_context_cpa_bid": {"item_table": "UnitTagTable", "column": "cpa_bid"}, "bid_context_auto_roi_ratio": {"item_table": "UnitTagTable", "column": "auto_roi_ratio"}, "bid_context_target_roas": {"item_table": "UnitTagTable", "column": "target_roas"}, "bid_context_total_pay": {"item_table": "UnitTagTable", "column": "total_pay"}, "bid_context_deep_flow_control_rate": {"item_table": "UnitTagTable", "column": "deep_flow_control_rate"}, "bid_context_current_deep_cpa": {"item_table": "UnitTagTable", "column": "current_deep_cpa"}, "bid_context_deep_threshold": {"item_table": "UnitTagTable", "column": "deep_threshold"}, "bid_context_deep_min_bid_coef": {"item_table": "UnitTagTable", "column": "deep_min_bid_coef"}, "bid_context_is_cold_start_explore": {"item_table": "UnitTagTable", "column": "is_cold_start_explore"}, "bid_context_acc_cold_start_explore_status": {"item_table": "UnitTagTable", "column": "acc_cold_start_explore_status"}, "bid_context_group_tag_exp_start_ts": {"item_table": "UnitTagTable", "column": "group_tag_exp_start_ts"}, "bid_context_cost_ratio_adjust_context_auto_cost_ratio": {"item_table": "UnitTagTable", "column": "cost_ratio_adjust_context_auto_cost_ratio"}, "bid_context_softad_diff_ratio": {"item_table": "UnitTagTable", "column": "softad_diff_ratio"}, "bid_context_conv_ratio_adjust_context_auto_conv_ratio": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_auto_conv_ratio"}, "bid_context_price_bid_ratio": {"item_table": "UnitTagTable", "column": "price_bid_ratio"}, "bid_context_second_coef": {"item_table": "UnitTagTable", "column": "second_coef"}, "bid_context_first_coef": {"item_table": "UnitTagTable", "column": "first_coef"}, "bid_context_r_calibration": {"item_table": "UnitTagTable", "column": "r_calibration"}, "bid_context_ocpm_inner_strategy_tag": {"item_table": "UnitTagTable", "column": "ocpm_inner_strategy_tag"}, "bid_context_auto_atv": {"item_table": "UnitTagTable", "column": "auto_atv"}, "bid_context_cost": {"item_table": "UnitTagTable", "column": "cost"}, "bid_context_target_cost": {"item_table": "UnitTagTable", "column": "target_cost"}, "bid_context_ad_off_target_cost": {"item_table": "UnitTagTable", "column": "ad_off_target_cost"}, "bid_context_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "auto_cpa_bid"}, "bid_context_roi_ratio": {"item_table": "UnitTagTable", "column": "roi_ratio"}}, "util_vals_attr_info": {"util_vals_ad_status_tag": {"item_table": "UnitTagTable", "column": "util_vals_ad_status_tag"}, "util_vals_is_update_adjust": {"item_table": "UnitTagTable", "column": "util_vals_is_update_adjust"}, "util_vals_is_no_bid": {"item_table": "UnitTagTable", "column": "util_vals_is_no_bid"}, "util_vals_is_sync_to_dsp": {"item_table": "UnitTagTable", "column": "util_vals_is_sync_to_dsp"}, "util_vals_ad_status_tag_change": {"item_table": "UnitTagTable", "column": "util_vals_ad_status_tag_change"}, "util_vals_ad_off_target_cost": {"item_table": "UnitTagTable", "column": "util_vals_ad_off_target_cost"}}, "lowest_cost_context": {"lowest_cost_context_current_budget": {"item_table": "UnitTagTable", "column": "lowest_cost_context_current_budget"}, "lowest_cost_context_past_pv_ratio": {"item_table": "UnitTagTable", "column": "lowest_cost_context_past_pv_ratio"}, "lowest_cost_context_past_cost_ratio": {"item_table": "UnitTagTable", "column": "lowest_cost_context_past_cost_ratio"}, "lowest_cost_context_is_explore_msg": {"item_table": "UnitTagTable", "column": "lowest_cost_context_is_explore_msg"}, "lowest_cost_context_is_campaign_bid": {"item_table": "UnitTagTable", "column": "lowest_cost_context_is_campaign_bid"}, "lowest_cost_context_first_log_timestamp": {"item_table": "UnitTagTable", "column": "lowest_cost_context_first_log_timestamp"}, "lowest_cost_context_schedule_time_type": {"item_table": "UnitTagTable", "column": "lowest_cost_context_schedule_time_type"}, "lowest_cost_context_is_same_date": {"item_table": "UnitTagTable", "column": "lowest_cost_context_is_same_date"}, "lowest_cost_context_total_cv": {"item_table": "UnitTagTable", "column": "lowest_cost_context_total_cv"}, "lowest_cost_context_target_cpa": {"item_table": "UnitTagTable", "column": "lowest_cost_context_target_cpa"}, "lowest_cost_context_expected_cost": {"item_table": "UnitTagTable", "column": "lowest_cost_context_expected_cost"}, "lowest_cost_context_is_init": {"item_table": "UnitTagTable", "column": "lowest_cost_context_is_init"}, "lowest_cost_context_allocated_budget_unify": {"item_table": "UnitTagTable", "column": "lowest_cost_context_allocated_budget_unify"}}, "bid_state_info": {"bid_state_info_online": {"item_table": "UnitTagTable", "column": "bid_state_info_online"}, "bid_state_info_advertisable": {"item_table": "UnitTagTable", "column": "bid_state_info_advertisable"}, "bid_state_info_ad_status": {"item_table": "UnitTagTable", "column": "bid_state_info_ad_status"}, "bid_state_info_is_live": {"item_table": "UnitTagTable", "column": "bid_state_info_is_live"}, "bid_state_info_is_status_open": {"item_table": "UnitTagTable", "column": "bid_state_info_is_status_open"}, "bid_state_info_campaign_id": {"item_table": "UnitTagTable", "column": "bid_state_info_campaign_id"}, "bid_state_info_campaign_type": {"item_table": "UnitTagTable", "column": "bid_state_info_campaign_type"}, "bid_state_info_begin_time": {"item_table": "UnitTagTable", "column": "bid_state_info_begin_time"}, "bid_state_info_end_time": {"item_table": "UnitTagTable", "column": "bid_state_info_end_time"}, "bid_state_info_speed_type": {"item_table": "UnitTagTable", "column": "bid_state_info_speed_type"}, "bid_state_info_promotion_type": {"item_table": "UnitTagTable", "column": "bid_state_info_promotion_type"}, "bid_state_info_account_type": {"item_table": "UnitTagTable", "column": "bid_state_info_account_type"}, "bid_state_info_bid_strategy": {"item_table": "UnitTagTable", "column": "bid_state_info_bid_strategy"}, "bid_state_info_live_launch_type": {"item_table": "UnitTagTable", "column": "bid_state_info_live_launch_type"}, "bid_state_info_author_id": {"item_table": "UnitTagTable", "column": "bid_state_info_author_id"}, "bid_state_info_day_budget": {"item_table": "UnitTagTable", "column": "bid_state_info_day_budget"}, "bid_state_info_budget": {"item_table": "UnitTagTable", "column": "bid_state_info_budget"}}, "$metadata": {"$output_item_attrs": [], "$input_item_attrs": ["UnitTagTable::acc_cold_start_explore_status", "UnitTagTable::account_id", "UnitTagTable::ad_off_target_cost", "UnitTagTable::auto_atv", "UnitTagTable::auto_cpa_bid", "UnitTagTable::auto_roi_ratio", "UnitTagTable::bid_state_info_account_type", "UnitTagTable::bid_state_info_ad_status", "UnitTagTable::bid_state_info_advertisable", "UnitTagTable::bid_state_info_author_id", "UnitTagTable::bid_state_info_begin_time", "UnitTagTable::bid_state_info_bid_strategy", "UnitTagTable::bid_state_info_budget", "UnitTagTable::bid_state_info_campaign_id", "UnitTagTable::bid_state_info_campaign_type", "UnitTagTable::bid_state_info_day_budget", "UnitTagTable::bid_state_info_end_time", "UnitTagTable::bid_state_info_is_live", "UnitTagTable::bid_state_info_is_status_open", "UnitTagTable::bid_state_info_live_launch_type", "UnitTagTable::bid_state_info_online", "UnitTagTable::bid_state_info_promotion_type", "UnitTagTable::bid_state_info_speed_type", "UnitTagTable::campaign_id", "UnitTagTable::campaign_type", "UnitTagTable::conv_ratio_adjust_context_auto_conv_ratio", "UnitTagTable::cost", "UnitTagTable::cost_ratio_adjust_context_auto_cost_ratio", "UnitTagTable::cpa_bid", "UnitTagTable::current_deep_cpa", "UnitTagTable::deep_flow_control_rate", "UnitTagTable::deep_min_bid_coef", "UnitTagTable::deep_threshold", "UnitTagTable::event_server_timestamp", "UnitTagTable::first_coef", "UnitTagTable::group_tag", "UnitTagTable::group_tag_exp_start_ts", "UnitTagTable::has_risk", "UnitTagTable::is_cold_start_explore", "UnitTagTable::is_debug", "UnitTagTable::last_sync_result_timestamp", "UnitTagTable::lowest_cost_context_allocated_budget_unify", "UnitTagTable::lowest_cost_context_current_budget", "UnitTagTable::lowest_cost_context_expected_cost", "UnitTagTable::lowest_cost_context_first_log_timestamp", "UnitTagTable::lowest_cost_context_is_campaign_bid", "UnitTagTable::lowest_cost_context_is_explore_msg", "UnitTagTable::lowest_cost_context_is_init", "UnitTagTable::lowest_cost_context_is_same_date", "UnitTagTable::lowest_cost_context_past_cost_ratio", "UnitTagTable::lowest_cost_context_past_pv_ratio", "UnitTagTable::lowest_cost_context_schedule_time_type", "UnitTagTable::lowest_cost_context_target_cpa", "UnitTagTable::lowest_cost_context_total_cv", "UnitTagTable::ocpc_action_type", "UnitTagTable::ocpm_inner_strategy_tag", "UnitTagTable::price_bid_ratio", "UnitTagTable::r_calibration", "UnitTagTable::roi_ratio", "UnitTagTable::second_coef", "UnitTagTable::softad_diff_ratio", "UnitTagTable::target_cost", "UnitTagTable::target_roas", "UnitTagTable::thread_id", "UnitTagTable::total_cv", "UnitTagTable::total_pay", "UnitTagTable::unit_id", "UnitTagTable::update_timestamp", "UnitTagTable::util_vals_ad_off_target_cost", "UnitTagTable::util_vals_ad_status_tag", "UnitTagTable::util_vals_ad_status_tag_change", "UnitTagTable::util_vals_is_no_bid", "UnitTagTable::util_vals_is_sync_to_dsp", "UnitTagTable::util_vals_is_update_adjust"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerloopBidResultStoreMix"}, "inner_loop_ocpm_calc_bid_mixer_B15F7B": {"item_table": "UnitTagTable", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": ["UnitTagTable::account_id", "UnitTagTable::adjust_auto_value_rate", "UnitTagTable::bid_state_info_account_type", "UnitTagTable::bid_state_info_bid_strategy", "UnitTagTable::bid_state_info_bid_type", "UnitTagTable::bid_state_info_live_launch_type", "UnitTagTable::campaign_id", "UnitTagTable::campaign_type", "UnitTagTable::cost", "UnitTagTable::group_tag", "UnitTagTable::has_bid_state_info", "UnitTagTable::ocpc_action_type", "UnitTagTable::old_auto_cpa_bid", "UnitTagTable::old_auto_roi_ratio", "UnitTagTable::relax_cpa_bid", "UnitTagTable::relax_roi_ratio", "UnitTagTable::target_cost", "UnitTagTable::unit_id", "UnitTagTable::util_vals_is_fanstop", "UnitTagTable::util_vals_is_roas"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopOcpmCalcBidMixer"}, "inner_loop_ocpm_check_pacing_rate_mixer_BB76FB": {"item_table": "UnitTagTable", "bid_context_attr_info": {"last_update_adjust_timestamp": {"item_table": "UnitTagTable", "column": "last_update_adjust_timestamp"}, "relax_roi_ratio": {"item_table": "UnitTagTable", "column": "relax_roi_ratio"}, "relax_cpa_bid": {"item_table": "UnitTagTable", "column": "relax_cpa_bid"}, "dynamic_lower_bound_roas": {"item_table": "UnitTagTable", "column": "dynamic_lower_bound_roas"}, "dynamic_upper_bound_roas": {"item_table": "UnitTagTable", "column": "dynamic_upper_bound_roas"}, "dynamic_lower_bound_cpa_bid": {"item_table": "UnitTagTable", "column": "dynamic_lower_bound_cpa_bid"}, "dynamic_upper_bound_cpa_bid": {"item_table": "UnitTagTable", "column": "dynamic_upper_bound_cpa_bid"}, "adjust_auto_value_rate": {"item_table": "UnitTagTable", "column": "adjust_auto_value_rate"}, "old_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "old_auto_cpa_bid"}, "auto_cpa_bid": {"item_table": "UnitTagTable", "column": "auto_cpa_bid"}, "reach_upper_bound_timestamp": {"item_table": "UnitTagTable", "column": "reach_upper_bound_timestamp"}, "reach_lower_bound_timestamp": {"item_table": "UnitTagTable", "column": "reach_lower_bound_timestamp"}, "old_auto_roi_ratio": {"item_table": "UnitTagTable", "column": "old_auto_roi_ratio"}, "auto_roi_ratio": {"item_table": "UnitTagTable", "column": "auto_roi_ratio"}, "is_touch_bound": {"item_table": "UnitTagTable", "column": "is_touch_bound"}, "unit_id": {"item_table": "UnitTagTable", "column": "unit_id"}, "campaign_type": {"item_table": "UnitTagTable", "column": "campaign_type"}, "ocpc_action_type": {"item_table": "UnitTagTable", "column": "ocpc_action_type"}, "group_tag": {"item_table": "UnitTagTable", "column": "group_tag"}}, "util_vals_attr_info": {"new_adjust_auto_value_rate": {"item_table": "UnitTagTable", "column": "util_vals_new_adjust_auto_value_rate"}, "is_roas": {"item_table": "UnitTagTable", "column": "util_vals_is_roas"}, "is_debug": {"item_table": "UnitTagTable", "column": "util_vals_is_debug"}, "is_roas_sub_pacing": {"item_table": "UnitTagTable", "column": "util_vals_is_roas_sub_pacing"}, "enable_linear_pacing_grid_correction": {"item_table": "UnitTagTable", "column": "util_vals_enable_linear_pacing_grid_correction"}, "enable_cost_prior_algo": {"item_table": "UnitTagTable", "column": "util_vals_enable_cost_prior_algo"}, "is_target_modify": {"item_table": "UnitTagTable", "column": "util_vals_is_target_modify"}, "target_modify_ratio": {"item_table": "UnitTagTable", "column": "util_vals_target_modify_ratio"}, "hard_upper_bound_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "util_vals_hard_upper_bound_auto_cpa_bid"}, "hard_lower_bound_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "util_vals_hard_lower_bound_auto_cpa_bid"}, "is_fanstop": {"item_table": "UnitTagTable", "column": "util_vals_is_fanstop"}, "dynamic_bid_upper_bound_adjust_rate": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_rate"}, "dynamic_bid_lower_bound_adjust_rate": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_rate"}, "hard_lower_bound_auto_roas": {"item_table": "UnitTagTable", "column": "util_vals_hard_lower_bound_auto_roas"}, "hard_upper_bound_auto_roas": {"item_table": "UnitTagTable", "column": "util_vals_hard_upper_bound_auto_roas"}, "dynamic_bid_lower_bound_adjust_time_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms"}, "dynamic_bid_upper_bound_adjust_time_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms"}, "is_acc_explore_bid": {"item_table": "UnitTagTable", "column": "util_vals_is_acc_explore_bid"}, "is_cold_start": {"item_table": "UnitTagTable", "column": "util_vals_is_cold_start"}, "is_explore": {"item_table": "UnitTagTable", "column": "util_vals_is_explore"}, "is_update_adjust": {"item_table": "UnitTagTable", "column": "util_vals_is_update_adjust"}, "is_process": {"item_table": "UnitTagTable", "column": "util_vals_is_process"}}, "output_attr_info": {"new_adjust_auto_value_rate": {"item_table": "UnitTagTable", "column": "util_vals_new_adjust_auto_value_rate"}, "is_process": {"item_table": "UnitTagTable", "column": "util_vals_is_process"}, "dynamic_bid_upper_bound_adjust_rate": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_rate"}, "dynamic_bid_lower_bound_adjust_rate": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_rate"}, "last_update_adjust_timestamp": {"item_table": "UnitTagTable", "column": "last_update_adjust_timestamp"}, "is_touch_bound": {"item_table": "UnitTagTable", "column": "is_touch_bound"}, "dynamic_upper_bound_roas": {"item_table": "UnitTagTable", "column": "dynamic_upper_bound_roas"}, "dynamic_lower_bound_roas": {"item_table": "UnitTagTable", "column": "dynamic_lower_bound_roas"}, "dynamic_upper_bound_cpa_bid": {"item_table": "UnitTagTable", "column": "dynamic_upper_bound_cpa_bid"}, "dynamic_lower_bound_cpa_bid": {"item_table": "UnitTagTable", "column": "dynamic_lower_bound_cpa_bid"}, "reach_upper_bound_timestamp": {"item_table": "UnitTagTable", "column": "reach_upper_bound_timestamp"}, "reach_lower_bound_timestamp": {"item_table": "UnitTagTable", "column": "reach_lower_bound_timestamp"}, "dynamic_bid_lower_bound_adjust_time_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms"}, "dynamic_bid_upper_bound_adjust_time_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms"}}, "$metadata": {"$output_item_attrs": ["UnitTagTable::dynamic_lower_bound_cpa_bid", "UnitTagTable::dynamic_lower_bound_roas", "UnitTagTable::dynamic_upper_bound_cpa_bid", "UnitTagTable::dynamic_upper_bound_roas", "UnitTagTable::is_touch_bound", "UnitTagTable::last_update_adjust_timestamp", "UnitTagTable::reach_lower_bound_timestamp", "UnitTagTable::reach_upper_bound_timestamp", "UnitTagTable::util_vals_dynamic_bid_lower_bound_adjust_rate", "UnitTagTable::util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms", "UnitTagTable::util_vals_dynamic_bid_upper_bound_adjust_rate", "UnitTagTable::util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms", "UnitTagTable::util_vals_is_process", "UnitTagTable::util_vals_new_adjust_auto_value_rate"], "$input_item_attrs": ["UnitTagTable::adjust_auto_value_rate", "UnitTagTable::auto_cpa_bid", "UnitTagTable::auto_roi_ratio", "UnitTagTable::campaign_type", "UnitTagTable::dynamic_lower_bound_cpa_bid", "UnitTagTable::dynamic_lower_bound_roas", "UnitTagTable::dynamic_upper_bound_cpa_bid", "UnitTagTable::dynamic_upper_bound_roas", "UnitTagTable::group_tag", "UnitTagTable::is_touch_bound", "UnitTagTable::last_update_adjust_timestamp", "UnitTagTable::ocpc_action_type", "UnitTagTable::old_auto_cpa_bid", "UnitTagTable::old_auto_roi_ratio", "UnitTagTable::reach_lower_bound_timestamp", "UnitTagTable::reach_upper_bound_timestamp", "UnitTagTable::relax_cpa_bid", "UnitTagTable::relax_roi_ratio", "UnitTagTable::unit_id", "UnitTagTable::util_vals_dynamic_bid_lower_bound_adjust_rate", "UnitTagTable::util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms", "UnitTagTable::util_vals_dynamic_bid_upper_bound_adjust_rate", "UnitTagTable::util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms", "UnitTagTable::util_vals_enable_cost_prior_algo", "UnitTagTable::util_vals_enable_linear_pacing_grid_correction", "UnitTagTable::util_vals_hard_lower_bound_auto_cpa_bid", "UnitTagTable::util_vals_hard_lower_bound_auto_roas", "UnitTagTable::util_vals_hard_upper_bound_auto_cpa_bid", "UnitTagTable::util_vals_hard_upper_bound_auto_roas", "UnitTagTable::util_vals_is_acc_explore_bid", "UnitTagTable::util_vals_is_cold_start", "UnitTagTable::util_vals_is_debug", "UnitTagTable::util_vals_is_explore", "UnitTagTable::util_vals_is_fanstop", "UnitTagTable::util_vals_is_process", "UnitTagTable::util_vals_is_roas", "UnitTagTable::util_vals_is_roas_sub_pacing", "UnitTagTable::util_vals_is_target_modify", "UnitTagTable::util_vals_is_update_adjust", "UnitTagTable::util_vals_new_adjust_auto_value_rate", "UnitTagTable::util_vals_target_modify_ratio"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerloopOcpmCheckPacingRateMixer"}, "inner_loop_ocpm_calc_pacing_rate_normal_mixer_FD617B": {"item_table": "UnitTagTable", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": ["UnitTagTable::account_id", "UnitTagTable::adjust_auto_value_rate", "UnitTagTable::author_id", "UnitTagTable::auto_cpa_bid", "UnitTagTable::bid_context_key", "UnitTagTable::bid_state_info_ad_status", "UnitTagTable::bid_state_info_advertisable", "UnitTagTable::bid_state_info_budget", "UnitTagTable::bid_state_info_explore_budget", "UnitTagTable::bid_state_info_explore_budget_start_time", "UnitTagTable::bid_state_info_explore_budget_status", "UnitTagTable::bid_state_info_explore_time_period", "UnitTagTable::bid_state_info_is_live", "UnitTagTable::bid_state_info_is_status_open", "UnitTagTable::bid_state_info_left_budget", "UnitTagTable::bid_state_info_online", "UnitTagTable::campaign_type", "UnitTagTable::cost", "UnitTagTable::cpa_bid", "UnitTagTable::first_industry_name", "UnitTagTable::group_tag", "UnitTagTable::has_bid_state_info_ptr", "UnitTagTable::last_update_adjust_timestamp", "UnitTagTable::ocpc_action_type", "UnitTagTable::opt_cost", "UnitTagTable::opt_target_cost", "UnitTagTable::promotion_type", "UnitTagTable::relax_cpa_bid", "UnitTagTable::target_atv", "UnitTagTable::target_cost", "UnitTagTable::unit_id", "UnitTagTable::unit_is_own_status", "UnitTagTable::util_vals_d_weight", "UnitTagTable::util_vals_enable_cost_prior_algo", "UnitTagTable::util_vals_i_weight", "UnitTagTable::util_vals_is_acc_explore_bid", "UnitTagTable::util_vals_is_cold_start", "UnitTagTable::util_vals_is_fanstop", "UnitTagTable::util_vals_is_fanstop_ocpm", "UnitTagTable::util_vals_is_ocpm_bid_process", "UnitTagTable::util_vals_is_roas", "UnitTagTable::util_vals_is_update_adjust", "UnitTagTable::util_vals_p_weight", "UnitTagTable::util_vals_pacing_type", "UnitTagTable::util_vals_prior_cost", "UnitTagTable::util_vals_pro_ocpc_not_been_set", "UnitTagTable::util_vals_reset_pacing"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopOcpmCalcPacingRateNormalMixer"}, "inner_loop_ocpm_calc_pacing_rate_acc_mixer_58FE8A": {"item_table": "UnitTagTable", "$metadata": {"$output_item_attrs": ["UnitTagTable::last_update_adjust_timestamp", "UnitTagTable::util_vals_is_acc_explore_bid", "UnitTagTable::util_vals_is_update_adjust"], "$input_item_attrs": ["UnitTagTable::adjust_auto_value_rate", "UnitTagTable::campaign_type", "UnitTagTable::group_tag", "UnitTagTable::ocpc_action_type", "UnitTagTable::relax_cpa_bid", "UnitTagTable::relax_roi_ratio", "UnitTagTable::rt_cost_speed", "UnitTagTable::unit_id", "UnitTagTable::util_vals_d_weight", "UnitTagTable::util_vals_i_weight", "UnitTagTable::util_vals_is_roas", "UnitTagTable::util_vals_p_weight", "UnitTagTable::util_vals_target_cost_speed"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopOcpmCalcPacingRateAccMixer"}, "return__D7A377": {"status_code": 0, "skip": "{{_if_control_attr_5}}", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["_if_control_attr_5"], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoExecutionStatusEnricher"}, "inner_loop_strategy_data_save_mixer_DA0772": {"tables": [{"table_name": "UnitTagTable", "table_category": "UnitTagTable", "save_all_columns": true, "local_cache_key_id": "local_cache_key_id"}], "skip": "{{_if_control_attr_5}}", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["_if_control_attr_5"], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopStrategyDataSaveMixer"}, "inner_loop_ocpm_calc_bid_prepare_mixer_0B7ACE": {"item_table": "UnitTagTable", "$metadata": {"$output_item_attrs": ["UnitTagTable::adjust_auto_value_rate", "UnitTagTable::util_vals_d_weight", "UnitTagTable::util_vals_i_weight", "UnitTagTable::util_vals_is_fanstop", "UnitTagTable::util_vals_is_fanstop_ocpm", "UnitTagTable::util_vals_is_ocpm_bid_process", "UnitTagTable::util_vals_is_roas", "UnitTagTable::util_vals_p_weight", "UnitTagTable::util_vals_pacing_type", "UnitTagTable::util_vals_reset_pacing"], "$input_item_attrs": ["UnitTagTable::author_id", "UnitTagTable::auto_cpa_bid", "UnitTagTable::auto_roi_ratio", "UnitTagTable::campaign_type", "UnitTagTable::conv_num", "UnitTagTable::cost", "UnitTagTable::cpa_bid", "UnitTagTable::dry_up_base_value", "UnitTagTable::first_delivery_timestamp_ms", "UnitTagTable::group_tag", "UnitTagTable::has_bid_state_info_ptr", "UnitTagTable::is_cold_start", "UnitTagTable::item_type", "UnitTagTable::last_delivery_timestamp_ms", "UnitTagTable::last_update_cost", "UnitTagTable::msg_conv_num", "UnitTagTable::msg_cost", "UnitTagTable::msg_price_ratio", "UnitTagTable::msg_target_cost", "UnitTagTable::ocpc_action_type", "UnitTagTable::old_is_out_of_budget", "UnitTagTable::promotion_type", "UnitTagTable::relax_cpa_bid", "UnitTagTable::relax_roi_ratio", "UnitTagTable::speed_type", "UnitTagTable::target_cost", "UnitTagTable::unit_id", "UnitTagTable::util_vals_ab_exp_ratio"], "$input_common_attrs": [], "$output_common_attrs": ["is_skip_update"], "$modify_item_tables": []}, "type_name": "InnerLoopOcpmCalcBidPrepareMixer"}, "return__04FED1": {"status_code": 0, "skip": "{{_if_control_attr_4}}", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["_if_control_attr_4"], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoExecutionStatusEnricher"}, "inner_loop_ocpm_trigger_mixer_85E8CF": {"item_table": "UnitTagTable", "key_id": {"item_table": "UnitTagTable", "column": "unit_id"}, "key_type": 0, "$metadata": {"$output_item_attrs": [], "$input_item_attrs": ["UnitTagTable::unit_id"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopTriggerMixer"}, "ad_data_frame_write_to_redis_observer_8422AB": {"enable_switch": true, "tables": [{"table_name": "UnitTagTable", "redis_name": "BidServerGraphInner", "candidate_redis_name": "BidServerGraphTest", "prefix": "inner_ctx_:", "ttl": 172800, "save_all_columns": true}], "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "AdDataFrameWriteToRedisObserver"}, "inner_loop_step_one_to_redis_mixer_66B10E": {"item_table": "UnitTagTable", "redis_name": "adEngineFlowProcess", "is_step_two": false, "ttl": 86400, "has_bid_state_info_ptr": {"item_table": "TriggerTable", "column": "has_bid_state_info_ptr"}, "bid_state_info_ptr": {"item_table": "TriggerTable", "column": "bid_state_info_ptr"}, "message_seq": {"item_table": "TriggerTable", "column": "message_seq"}, "event_server_timestamp": {"item_table": "TriggerTable", "column": "event_server_timestamp"}, "unit_id": {"item_table": "UnitTagTable", "column": "unit_id"}, "msg_attr_info": {"msg_campaign_type": {"item_table": "TriggerTable", "column": "campaign_type"}, "msg_ocpc_action_type": {"item_table": "TriggerTable", "column": "ocpc_action_type"}, "msg_promotion_type": {"item_table": "TriggerTable", "column": "promotion_type"}, "msg_item_type": {"item_table": "TriggerTable", "column": "item_type"}, "msg_author_id": {"item_table": "TriggerTable", "column": "author_id"}, "msg_account_id": {"item_table": "TriggerTable", "column": "account_id"}, "msg_live_stream_id": {"item_table": "TriggerTable", "column": "live_stream_id"}, "msg_unit_id": {"item_table": "TriggerTable", "column": "unit_id"}, "msg_conv_num": {"item_table": "TriggerTable", "column": "conv_num"}, "msg_campaign_id": {"item_table": "TriggerTable", "column": "campaign_id"}, "msg_item_type_num": {"item_table": "TriggerTable", "column": "item_type_num"}, "msg_cost": {"item_table": "TriggerTable", "column": "cost"}, "msg_gmv": {"item_table": "TriggerTable", "column": "gmv"}, "msg_roi_ratio": {"item_table": "TriggerTable", "column": "roi_ratio"}, "msg_action_type": {"item_table": "TriggerTable", "column": "action_type"}, "msg_group_tag": {"item_table": "TriggerTable", "column": "inner_group_tag"}, "msg_bid_type": {"item_table": "TriggerTable", "column": "bid_type"}, "msg_medium_attribute": {"item_table": "TriggerTable", "column": "medium_attribute"}, "msg_speed_type": {"item_table": "TriggerTable", "column": "speed_type"}, "msg_delivery_timestamp": {"item_table": "TriggerTable", "column": "delivery_timestamp"}, "msg_cpa_bid": {"item_table": "TriggerTable", "column": "cpa_bid"}, "msg_is_soft": {"item_table": "TriggerTable", "column": "is_soft"}, "msg_target_cost": {"item_table": "TriggerTable", "column": "target_cost"}, "msg_target_gmv": {"item_table": "TriggerTable", "column": "target_gmv"}, "msg_separate_gsp_price": {"item_table": "TriggerTable", "column": "separate_gsp_price"}, "msg_record_gsp_price": {"item_table": "TriggerTable", "column": "record_gsp_price"}, "msg_pred_conv": {"item_table": "TriggerTable", "column": "pred_conv"}, "msg_pred_cvr_sum": {"item_table": "TriggerTable", "column": "pred_cvr_sum"}, "msg_pred_ctr_sum": {"item_table": "TriggerTable", "column": "pred_ctr_sum"}, "msg_price_before_billing_separate": {"item_table": "TriggerTable", "column": "price_before_billing_separate"}, "msg_price_after_billing_separate": {"item_table": "TriggerTable", "column": "price_after_billing_separate"}, "msg_ecpm": {"item_table": "TriggerTable", "column": "ecpm"}, "msg_auction_bid": {"item_table": "TriggerTable", "column": "auction_bid"}, "msg_price_ratio": {"item_table": "TriggerTable", "column": "price_ratio"}, "msg_is_store_wide_roi_reco_conv": {"item_table": "TriggerTable", "column": "is_store_wide_roi_reco_conv"}}, "bid_ctx_attr_info": {"bid_ctx_bid_context_key": {"item_table": "UnitTagTable", "column": "bid_context_key"}, "bid_ctx_group_tag": {"item_table": "UnitTagTable", "column": "group_tag"}, "bid_ctx_campaign_type": {"item_table": "UnitTagTable", "column": "campaign_type"}, "bid_ctx_ocpc_action_type": {"item_table": "UnitTagTable", "column": "ocpc_action_type"}, "bid_ctx_promotion_type": {"item_table": "UnitTagTable", "column": "promotion_type"}, "bid_ctx_item_type": {"item_table": "UnitTagTable", "column": "item_type"}, "bid_ctx_first_industry_name": {"item_table": "UnitTagTable", "column": "first_industry_name"}, "bid_ctx_group_tag_num": {"item_table": "UnitTagTable", "column": "group_tag_num"}, "bid_ctx_unit_id": {"item_table": "UnitTagTable", "column": "unit_id"}, "bid_ctx_account_id": {"item_table": "UnitTagTable", "column": "account_id"}, "bid_ctx_author_id": {"item_table": "UnitTagTable", "column": "author_id"}, "bid_ctx_live_stream_id": {"item_table": "UnitTagTable", "column": "live_stream_id"}, "bid_ctx_item_type_num": {"item_table": "UnitTagTable", "column": "item_type_num"}, "bid_ctx_campaign_id": {"item_table": "UnitTagTable", "column": "campaign_id"}, "bid_ctx_price_before_billing_separate": {"item_table": "UnitTagTable", "column": "price_before_billing_separate"}, "bid_ctx_price_after_billing_separate": {"item_table": "UnitTagTable", "column": "price_after_billing_separate"}, "bid_ctx_cost": {"item_table": "UnitTagTable", "column": "cost"}, "bid_ctx_gmv": {"item_table": "UnitTagTable", "column": "gmv"}, "bid_ctx_conv_num": {"item_table": "UnitTagTable", "column": "conv_num"}, "bid_ctx_cost_count": {"item_table": "UnitTagTable", "column": "cost_count"}, "bid_ctx_gmv_count": {"item_table": "UnitTagTable", "column": "gmv_count"}, "bid_ctx_delivery_cnt": {"item_table": "UnitTagTable", "column": "delivery_cnt"}, "bid_ctx_cpa_bid": {"item_table": "UnitTagTable", "column": "cpa_bid"}, "bid_ctx_relax_cpa_bid": {"item_table": "UnitTagTable", "column": "relax_cpa_bid"}, "bid_ctx_roi_ratio": {"item_table": "UnitTagTable", "column": "roi_ratio"}, "bid_ctx_relax_roi_ratio": {"item_table": "UnitTagTable", "column": "relax_roi_ratio"}, "bid_ctx_rt_cpa_bid": {"item_table": "UnitTagTable", "column": "rt_cpa_bid"}, "bid_ctx_rt_roas": {"item_table": "UnitTagTable", "column": "rt_roas"}, "bid_ctx_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "auto_cpa_bid"}, "bid_ctx_auto_roi_ratio": {"item_table": "UnitTagTable", "column": "auto_roi_ratio"}, "bid_ctx_old_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "old_auto_cpa_bid"}, "bid_ctx_old_auto_roi_ratio": {"item_table": "UnitTagTable", "column": "old_auto_roi_ratio"}, "bid_ctx_adjust_rate": {"item_table": "UnitTagTable", "column": "adjust_rate"}, "bid_ctx_rt_cost_speed": {"item_table": "UnitTagTable", "column": "rt_cost_speed"}, "bid_ctx_rt_delivery_speed": {"item_table": "UnitTagTable", "column": "rt_delivery_speed"}, "bid_ctx_dynamic_lower_bound_roas": {"item_table": "UnitTagTable", "column": "dynamic_lower_bound_roas"}, "bid_ctx_dynamic_upper_bound_roas": {"item_table": "UnitTagTable", "column": "dynamic_upper_bound_roas"}, "bid_ctx_dynamic_lower_bound_cpa_bid": {"item_table": "UnitTagTable", "column": "dynamic_lower_bound_cpa_bid"}, "bid_ctx_dynamic_upper_bound_cpa_bid": {"item_table": "UnitTagTable", "column": "dynamic_upper_bound_cpa_bid"}, "bid_ctx_target_cost": {"item_table": "UnitTagTable", "column": "target_cost"}, "bid_ctx_target_gmv": {"item_table": "UnitTagTable", "column": "target_gmv"}, "bid_ctx_record_gsp_price": {"item_table": "UnitTagTable", "column": "record_gsp_price"}, "bid_ctx_last_update_cost": {"item_table": "UnitTagTable", "column": "last_update_cost"}, "bid_ctx_adjust_auto_atv_rate": {"item_table": "UnitTagTable", "column": "adjust_auto_atv_rate"}, "bid_ctx_old_adjust_auto_atv_rate": {"item_table": "UnitTagTable", "column": "old_adjust_auto_atv_rate"}, "bid_ctx_reach_lower_bound_timestamp": {"item_table": "UnitTagTable", "column": "reach_lower_bound_timestamp"}, "bid_ctx_reach_upper_bound_timestamp": {"item_table": "UnitTagTable", "column": "reach_upper_bound_timestamp"}, "bid_ctx_first_delivery_timestamp_ms": {"item_table": "UnitTagTable", "column": "first_delivery_timestamp_ms"}, "bid_ctx_last_delivery_timestamp_ms": {"item_table": "UnitTagTable", "column": "last_delivery_timestamp_ms"}, "bid_ctx_last_update_adjust_timestamp": {"item_table": "UnitTagTable", "column": "last_update_adjust_timestamp"}, "bid_ctx_cost_start_timestamp_ms": {"item_table": "UnitTagTable", "column": "cost_start_timestamp_ms"}, "bid_ctx_last_update_context_timestamp": {"item_table": "UnitTagTable", "column": "last_update_context_timestamp"}, "bid_ctx_last_sync_context_timestamp": {"item_table": "UnitTagTable", "column": "last_sync_context_timestamp"}, "bid_ctx_online": {"item_table": "UnitTagTable", "column": "online"}, "bid_ctx_is_cold_start": {"item_table": "UnitTagTable", "column": "is_cold_start"}, "bid_ctx_speed_type": {"item_table": "UnitTagTable", "column": "speed_type"}, "bid_ctx_context_start_timestamp": {"item_table": "UnitTagTable", "column": "context_start_timestamp"}, "bid_ctx_last_sync_result_timestamp": {"item_table": "UnitTagTable", "column": "last_sync_result_timestamp"}, "bid_ctx_adjust_auto_value_rate": {"item_table": "UnitTagTable", "column": "adjust_auto_value_rate"}, "bid_ctx_total_ecpm": {"item_table": "UnitTagTable", "column": "total_ecpm"}, "bid_ctx_separate_gsp_price": {"item_table": "UnitTagTable", "column": "separate_gsp_price"}, "bid_ctx_total_auction_bid": {"item_table": "UnitTagTable", "column": "total_auction_bid"}, "bid_ctx_dry_up_base_value": {"item_table": "UnitTagTable", "column": "dry_up_base_value"}, "bid_ctx_ctr_sum": {"item_table": "UnitTagTable", "column": "ctr_sum"}, "bid_ctx_cvr_sum": {"item_table": "UnitTagTable", "column": "cvr_sum"}, "bid_ctx_softad_cost": {"item_table": "UnitTagTable", "column": "softad_cost"}, "bid_ctx_softad_target_cost": {"item_table": "UnitTagTable", "column": "softad_target_cost"}, "bid_ctx_softad_diff_ratio": {"item_table": "UnitTagTable", "column": "softad_diff_ratio"}, "bid_ctx_last_monitor_timestamp": {"item_table": "UnitTagTable", "column": "last_monitor_timestamp"}, "bid_ctx_user_cost_prior_algo": {"item_table": "UnitTagTable", "column": "user_cost_prior_algo"}, "bid_ctx_ad_status_tag": {"item_table": "UnitTagTable", "column": "ad_status_tag"}, "bid_ctx_old_is_out_of_budget": {"item_table": "UnitTagTable", "column": "old_is_out_of_budget"}, "bid_ctx_last_is_ad_open": {"item_table": "UnitTagTable", "column": "last_is_ad_open"}, "bid_ctx_last_ad_valid": {"item_table": "UnitTagTable", "column": "last_ad_valid"}, "bid_ctx_sync_context_interval_batch_batch_cost": {"item_table": "UnitTagTable", "column": "sync_context_interval_batch_batch_cost"}, "bid_ctx_sync_context_interval_batch_batch_gmv": {"item_table": "UnitTagTable", "column": "sync_context_interval_batch_batch_gmv"}, "bid_ctx_sync_context_interval_batch_batch_start_timestamp": {"item_table": "UnitTagTable", "column": "sync_context_interval_batch_batch_start_timestamp"}, "bid_ctx_cost_speed_context_batch_start_timestamp_ms": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_start_timestamp_ms"}, "bid_ctx_cost_speed_context_batch_value_sum": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_value_sum"}, "bid_ctx_cost_speed_context_batch_count": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_count"}, "bid_ctx_cost_speed_context_batch_value_mean": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_value_mean"}, "bid_ctx_cost_speed_context_batch_value_speed": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_value_speed"}, "bid_ctx_delivery_speed_context_batch_start_timestamp_ms": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_start_timestamp_ms"}, "bid_ctx_delivery_speed_context_batch_value_sum": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_value_sum"}, "bid_ctx_delivery_speed_context_batch_count": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_count"}, "bid_ctx_delivery_speed_context_batch_value_mean": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_value_mean"}, "bid_ctx_delivery_speed_context_batch_value_speed": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_value_speed"}, "bid_ctx_conv_ratio_adjust_context_pred_conv": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_pred_conv"}, "bid_ctx_conv_ratio_adjust_context_old_auto_conv_ratio": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_old_auto_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_auto_conv_ratio": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_auto_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_real_conv": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_real_conv"}, "bid_ctx_conv_ratio_adjust_context_last_update_conv_ratio_timestamp": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_last_update_conv_ratio_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_r1_upper_bound_timestamp": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_reach_r1_upper_bound_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_r1_lower_bound_timestamp": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_reach_r1_lower_bound_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp"}, "bid_ctx_price_ratio_context_batch_start_timestamp_ms": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_start_timestamp_ms"}, "bid_ctx_price_ratio_context_batch_total_price": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_total_price"}, "bid_ctx_price_ratio_context_batch_total_ecpm": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_total_ecpm"}, "bid_ctx_price_ratio_context_batch_total_price_ratio": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_total_price_ratio"}, "bid_ctx_price_ratio_context_batch_count": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_count"}, "bid_ctx_price_ratio_context_batch_avg_price_ratio": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_avg_price_ratio"}, "bid_ctx_price_ratio_context_batch_price_ratio_mean": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_price_ratio_mean"}, "bid_ctx_item_type_info_photo_to_live_cnt": {"item_table": "UnitTagTable", "column": "item_type_info_photo_to_live_cnt"}, "bid_ctx_item_type_info_direct_live_cnt": {"item_table": "UnitTagTable", "column": "item_type_info_direct_live_cnt"}, "bid_ctx_item_type_info_photo_to_live_cost": {"item_table": "UnitTagTable", "column": "item_type_info_photo_to_live_cost"}, "bid_ctx_item_type_info_direct_live_cost": {"item_table": "UnitTagTable", "column": "item_type_info_direct_live_cost"}}, "process_utils_attr_info": {"process_utils_ad_off_target_cost": {"item_table": "UnitTagTable", "column": "util_vals_ad_off_target_cost"}, "process_utils_new_adjust_auto_value_rate": {"item_table": "UnitTagTable", "column": "util_vals_new_adjust_auto_value_rate"}, "process_utils_old_adjust_auto_value_rate": {"item_table": "UnitTagTable", "column": "util_vals_old_adjust_auto_value_rate"}, "process_utils_is_bid_ptr_null": {"item_table": "UnitTagTable", "column": "util_vals_is_bid_ptr_null"}, "process_utils_pro_ocpc_not_been_set": {"item_table": "UnitTagTable", "column": "util_vals_pro_ocpc_not_been_set"}, "process_utils_reset_pacing": {"item_table": "UnitTagTable", "column": "util_vals_reset_pacing"}, "process_utils_is_cost_target_msg": {"item_table": "UnitTagTable", "column": "util_vals_is_cost_target_msg"}, "process_utils_enable_cost_prior_algo": {"item_table": "UnitTagTable", "column": "util_vals_enable_cost_prior_algo"}, "process_utils_enable_linear_pacing_cold_start_low_bound": {"item_table": "UnitTagTable", "column": "util_vals_enable_linear_pacing_cold_start_low_bound"}, "process_utils_enable_linear_pacing_grid_correction": {"item_table": "UnitTagTable", "column": "util_vals_enable_linear_pacing_grid_correction"}, "process_utils_enable_linear_adaptive_update_time": {"item_table": "UnitTagTable", "column": "util_vals_enable_linear_adaptive_update_time"}, "process_utils_enable_linear_after_cold_start_no_conv_drop": {"item_table": "UnitTagTable", "column": "util_vals_enable_linear_after_cold_start_no_conv_drop"}, "process_utils_disable_day_reset_exp": {"item_table": "UnitTagTable", "column": "util_vals_disable_day_reset_exp"}, "process_utils_enable_univ_inner_ad_delivery_replace": {"item_table": "UnitTagTable", "column": "util_vals_enable_univ_inner_ad_delivery_replace"}, "process_utils_is_apply_adjust": {"item_table": "UnitTagTable", "column": "util_vals_is_apply_adjust"}, "process_utils_is_debug": {"item_table": "UnitTagTable", "column": "util_vals_is_debug"}, "process_utils_is_roas_sub_pacing": {"item_table": "UnitTagTable", "column": "util_vals_is_roas_sub_pacing"}, "process_utils_is_ocpm_bid_process": {"item_table": "UnitTagTable", "column": "util_vals_is_ocpm_bid_process"}, "process_utils_is_apply_operation_config": {"item_table": "UnitTagTable", "column": "util_vals_is_apply_operation_config"}, "process_utils_is_start_process": {"item_table": "UnitTagTable", "column": "util_vals_is_start_process"}, "process_utils_is_debug_on": {"item_table": "UnitTagTable", "column": "util_vals_is_debug_on"}, "process_utils_is_normal": {"item_table": "UnitTagTable", "column": "util_vals_is_normal"}, "process_utils_is_fanstop": {"item_table": "UnitTagTable", "column": "util_vals_is_fanstop"}, "process_utils_is_fanstop_ocpm": {"item_table": "UnitTagTable", "column": "util_vals_is_fanstop_ocpm"}, "process_utils_is_roas": {"item_table": "UnitTagTable", "column": "util_vals_is_roas"}, "process_utils_is_no_bid": {"item_table": "UnitTagTable", "column": "util_vals_is_no_bid"}, "process_utils_is_skip_update": {"item_table": "UnitTagTable", "column": "util_vals_is_skip_update"}, "process_utils_is_nothing_to_do": {"item_table": "UnitTagTable", "column": "util_vals_is_nothing_to_do"}, "process_utils_is_process": {"item_table": "UnitTagTable", "column": "util_vals_is_process"}, "process_utils_is_reset_context": {"item_table": "UnitTagTable", "column": "util_vals_is_reset_context"}, "process_utils_is_update_adjust": {"item_table": "UnitTagTable", "column": "util_vals_is_update_adjust"}, "process_utils_is_interval_cost": {"item_table": "UnitTagTable", "column": "util_vals_is_interval_cost"}, "process_utils_is_update_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_is_update_interval_ms"}, "process_utils_is_ad_open": {"item_table": "UnitTagTable", "column": "util_vals_is_ad_open"}, "process_utils_is_sync_to_dsp": {"item_table": "UnitTagTable", "column": "util_vals_is_sync_to_dsp"}, "process_utils_is_monitor": {"item_table": "UnitTagTable", "column": "util_vals_is_monitor"}, "process_utils_is_cold_start": {"item_table": "UnitTagTable", "column": "util_vals_is_cold_start"}, "process_utils_is_costcap_nobid": {"item_table": "UnitTagTable", "column": "util_vals_is_costcap_nobid"}, "process_utils_is_skip_cold_start": {"item_table": "UnitTagTable", "column": "util_vals_is_skip_cold_start"}, "process_utils_is_on_live": {"item_table": "UnitTagTable", "column": "util_vals_is_on_live"}, "process_utils_is_out_of_budget": {"item_table": "UnitTagTable", "column": "util_vals_is_out_of_budget"}, "process_utils_old_is_out_of_budget": {"item_table": "UnitTagTable", "column": "util_vals_old_is_out_of_budget"}, "process_utils_advertisable": {"item_table": "UnitTagTable", "column": "util_vals_advertisable"}, "process_utils_is_status_open": {"item_table": "UnitTagTable", "column": "util_vals_is_status_open"}, "process_utils_ad_status": {"item_table": "UnitTagTable", "column": "util_vals_ad_status"}, "process_utils_unit_id": {"item_table": "UnitTagTable", "column": "util_vals_unit_id"}, "process_utils_thread_id": {"item_table": "UnitTagTable", "column": "util_vals_thread_id"}, "process_utils_is_bid_info": {"item_table": "UnitTagTable", "column": "util_vals_is_bid_info"}, "process_utils_is_online": {"item_table": "UnitTagTable", "column": "util_vals_is_online"}, "process_utils_is_explore": {"item_table": "UnitTagTable", "column": "util_vals_is_explore"}, "process_utils_ab_exp_ratio": {"item_table": "UnitTagTable", "column": "util_vals_ab_exp_ratio"}, "process_utils_promotion_type_num": {"item_table": "UnitTagTable", "column": "util_vals_promotion_type_num"}, "process_utils_item_type_num": {"item_table": "UnitTagTable", "column": "util_vals_item_type_num"}, "process_utils_prior_cost": {"item_table": "UnitTagTable", "column": "util_vals_prior_cost"}, "process_utils_cold_start_cost_li_thr": {"item_table": "UnitTagTable", "column": "util_vals_cold_start_cost_li_thr"}, "process_utils_cold_start_low_bound": {"item_table": "UnitTagTable", "column": "util_vals_cold_start_low_bound"}, "process_utils_rt_cost_speed": {"item_table": "UnitTagTable", "column": "util_vals_rt_cost_speed"}, "process_utils_rt_delivery_speed": {"item_table": "UnitTagTable", "column": "util_vals_rt_delivery_speed"}, "process_utils_target_roas": {"item_table": "UnitTagTable", "column": "util_vals_target_roas"}, "process_utils_is_price_ratio_bound": {"item_table": "UnitTagTable", "column": "util_vals_is_price_ratio_bound"}, "process_utils_is_acc_explore_bid": {"item_table": "UnitTagTable", "column": "util_vals_is_acc_explore_bid"}, "process_utils_dynamic_bid_lower_bound_adjust_time_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms"}, "process_utils_dynamic_bid_upper_bound_adjust_time_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms"}, "process_utils_dynamic_bid_lower_bound_adjust_rate": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_rate"}, "process_utils_dynamic_bid_upper_bound_adjust_rate": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_rate"}, "process_utils_p_weight": {"item_table": "UnitTagTable", "column": "util_vals_p_weight"}, "process_utils_i_weight": {"item_table": "UnitTagTable", "column": "util_vals_i_weight"}, "process_utils_d_weight": {"item_table": "UnitTagTable", "column": "util_vals_d_weight"}, "process_utils_explore_p_weight": {"item_table": "UnitTagTable", "column": "util_vals_explore_p_weight"}, "process_utils_explore_i_weight": {"item_table": "UnitTagTable", "column": "util_vals_explore_i_weight"}, "process_utils_explore_d_weight": {"item_table": "UnitTagTable", "column": "util_vals_explore_d_weight"}, "process_utils_p_val": {"item_table": "UnitTagTable", "column": "util_vals_p_val"}, "process_utils_i_val": {"item_table": "UnitTagTable", "column": "util_vals_i_val"}, "process_utils_d_val": {"item_table": "UnitTagTable", "column": "util_vals_d_val"}, "process_utils_pacing_weight": {"item_table": "UnitTagTable", "column": "util_vals_pacing_weight"}, "process_utils_target_cost_speed": {"item_table": "UnitTagTable", "column": "util_vals_target_cost_speed"}, "process_utils_target_delivery_speed": {"item_table": "UnitTagTable", "column": "util_vals_target_delivery_speed"}, "process_utils_start_bid_rate": {"item_table": "UnitTagTable", "column": "util_vals_start_bid_rate"}, "process_utils_start_bid_rate_sub_pacing": {"item_table": "UnitTagTable", "column": "util_vals_start_bid_rate_sub_pacing"}, "process_utils_hard_upper_bound_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "util_vals_hard_upper_bound_auto_cpa_bid"}, "process_utils_hard_lower_bound_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "util_vals_hard_lower_bound_auto_cpa_bid"}, "process_utils_hard_lower_bound_auto_roas": {"item_table": "UnitTagTable", "column": "util_vals_hard_lower_bound_auto_roas"}, "process_utils_hard_upper_bound_auto_roas": {"item_table": "UnitTagTable", "column": "util_vals_hard_upper_bound_auto_roas"}, "process_utils_hard_lower_bound_auto_atv": {"item_table": "UnitTagTable", "column": "util_vals_hard_lower_bound_auto_atv"}, "process_utils_hard_upper_bound_auto_atv": {"item_table": "UnitTagTable", "column": "util_vals_hard_upper_bound_auto_atv"}, "process_utils_acc_explore_bid_target_cost_speed": {"item_table": "UnitTagTable", "column": "util_vals_acc_explore_bid_target_cost_speed"}, "process_utils_current_ms": {"item_table": "UnitTagTable", "column": "util_vals_current_ms"}, "process_utils_ad_short_type": {"item_table": "UnitTagTable", "column": "util_vals_ad_short_type"}, "process_utils_is_target_modify": {"item_table": "UnitTagTable", "column": "util_vals_is_target_modify"}, "process_utils_target_modify_ratio": {"item_table": "UnitTagTable", "column": "util_vals_target_modify_ratio"}, "process_utils_p_weight_conv": {"item_table": "UnitTagTable", "column": "util_vals_p_weight_conv"}, "process_utils_i_weight_conv": {"item_table": "UnitTagTable", "column": "util_vals_i_weight_conv"}, "process_utils_d_weight_conv": {"item_table": "UnitTagTable", "column": "util_vals_d_weight_conv"}, "process_utils_p_val_conv": {"item_table": "UnitTagTable", "column": "util_vals_p_val_conv"}, "process_utils_i_val_conv": {"item_table": "UnitTagTable", "column": "util_vals_i_val_conv"}, "process_utils_d_val_conv": {"item_table": "UnitTagTable", "column": "util_vals_d_val_conv"}, "process_utils_pacing_weight_conv": {"item_table": "UnitTagTable", "column": "util_vals_pacing_weight_conv"}, "process_utils_p_weight_cost": {"item_table": "UnitTagTable", "column": "util_vals_p_weight_cost"}, "process_utils_i_weight_cost": {"item_table": "UnitTagTable", "column": "util_vals_i_weight_cost"}, "process_utils_d_weight_cost": {"item_table": "UnitTagTable", "column": "util_vals_d_weight_cost"}, "process_utils_p_val_cost": {"item_table": "UnitTagTable", "column": "util_vals_p_val_cost"}, "process_utils_i_val_cost": {"item_table": "UnitTagTable", "column": "util_vals_i_val_cost"}, "process_utils_d_val_cost": {"item_table": "UnitTagTable", "column": "util_vals_d_val_cost"}, "process_utils_pacing_weight_cost": {"item_table": "UnitTagTable", "column": "util_vals_pacing_weight_cost"}, "process_utils_update_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_update_interval_ms"}, "process_utils_interval_cost": {"item_table": "UnitTagTable", "column": "util_vals_interval_cost"}, "process_utils_step_bid_rate_increase": {"item_table": "UnitTagTable", "column": "util_vals_step_bid_rate_increase"}, "process_utils_step_bid_rate_descent": {"item_table": "UnitTagTable", "column": "util_vals_step_bid_rate_descent"}, "process_utils_ad_status_tag": {"item_table": "UnitTagTable", "column": "util_vals_ad_status_tag"}, "process_utils_ad_status_tag_change": {"item_table": "UnitTagTable", "column": "util_vals_ad_status_tag_change"}, "process_utils_total_pred_conv": {"item_table": "UnitTagTable", "column": "util_vals_total_pred_conv"}, "process_utils_adjust_rate_lower_bound": {"item_table": "UnitTagTable", "column": "util_vals_adjust_rate_lower_bound"}, "process_utils_adjust_rate_upper_bound": {"item_table": "UnitTagTable", "column": "util_vals_adjust_rate_upper_bound"}, "process_utils_budget": {"item_table": "UnitTagTable", "column": "util_vals_budget"}, "process_utils_hard_bid_upper_bound_rate_monitor": {"item_table": "UnitTagTable", "column": "util_vals_hard_bid_upper_bound_rate_monitor"}, "process_utils_hard_bid_lower_bound_rate_monitor": {"item_table": "UnitTagTable", "column": "util_vals_hard_bid_lower_bound_rate_monitor"}, "process_utils_fanstop_adjust_rate_lower_bound_roi": {"item_table": "UnitTagTable", "column": "util_vals_fanstop_adjust_rate_lower_bound_roi"}, "process_utils_fanstop_adjust_rate_upper_bound_roi": {"item_table": "UnitTagTable", "column": "util_vals_fanstop_adjust_rate_upper_bound_roi"}, "process_utils_fanstop_adjust_rate_lower_bound_cpa": {"item_table": "UnitTagTable", "column": "util_vals_fanstop_adjust_rate_lower_bound_cpa"}, "process_utils_fanstop_adjust_rate_upper_bound_cpa": {"item_table": "UnitTagTable", "column": "util_vals_fanstop_adjust_rate_upper_bound_cpa"}, "process_utils_pacing_type": {"item_table": "UnitTagTable", "column": "util_vals_pacing_type"}, "process_utils_pacing_method_type": {"item_table": "UnitTagTable", "column": "util_vals_pacing_method_type"}}, "$metadata": {"$output_item_attrs": [], "$input_item_attrs": ["TriggerTable::account_id", "TriggerTable::action_type", "TriggerTable::auction_bid", "TriggerTable::author_id", "TriggerTable::bid_type", "TriggerTable::campaign_id", "TriggerTable::campaign_type", "TriggerTable::conv_num", "TriggerTable::cost", "TriggerTable::cpa_bid", "TriggerTable::delivery_timestamp", "TriggerTable::ecpm", "TriggerTable::event_server_timestamp", "TriggerTable::gmv", "TriggerTable::inner_group_tag", "TriggerTable::is_soft", "TriggerTable::is_store_wide_roi_reco_conv", "TriggerTable::item_type", "TriggerTable::item_type_num", "TriggerTable::live_stream_id", "TriggerTable::medium_attribute", "TriggerTable::message_seq", "TriggerTable::ocpc_action_type", "TriggerTable::pred_conv", "TriggerTable::pred_ctr_sum", "TriggerTable::pred_cvr_sum", "TriggerTable::price_after_billing_separate", "TriggerTable::price_before_billing_separate", "TriggerTable::price_ratio", "TriggerTable::promotion_type", "TriggerTable::record_gsp_price", "TriggerTable::roi_ratio", "TriggerTable::separate_gsp_price", "TriggerTable::speed_type", "TriggerTable::target_cost", "TriggerTable::target_gmv", "TriggerTable::unit_id", "UnitTagTable::account_id", "UnitTagTable::ad_status_tag", "UnitTagTable::adjust_auto_atv_rate", "UnitTagTable::adjust_auto_value_rate", "UnitTagTable::adjust_rate", "UnitTagTable::author_id", "UnitTagTable::auto_cpa_bid", "UnitTagTable::auto_roi_ratio", "UnitTagTable::bid_context_key", "UnitTagTable::bid_state_info_ptr", "UnitTagTable::campaign_id", "UnitTagTable::campaign_type", "UnitTagTable::context_start_timestamp", "UnitTagTable::conv_num", "UnitTagTable::conv_ratio_adjust_context_auto_conv_ratio", "UnitTagTable::conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio", "UnitTagTable::conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio", "UnitTagTable::conv_ratio_adjust_context_last_update_conv_ratio_timestamp", "UnitTagTable::conv_ratio_adjust_context_old_auto_conv_ratio", "UnitTagTable::conv_ratio_adjust_context_pred_conv", "UnitTagTable::conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp", "UnitTagTable::conv_ratio_adjust_context_reach_r1_lower_bound_timestamp", "UnitTagTable::conv_ratio_adjust_context_reach_r1_upper_bound_timestamp", "UnitTagTable::conv_ratio_adjust_context_real_conv", "UnitTagTable::cost", "UnitTagTable::cost_count", "UnitTagTable::cost_speed_context_batch_count", "UnitTagTable::cost_speed_context_batch_start_timestamp_ms", "UnitTagTable::cost_speed_context_batch_value_mean", "UnitTagTable::cost_speed_context_batch_value_speed", "UnitTagTable::cost_speed_context_batch_value_sum", "UnitTagTable::cost_start_timestamp_ms", "UnitTagTable::cpa_bid", "UnitTagTable::ctr_sum", "UnitTagTable::cvr_sum", "UnitTagTable::delivery_cnt", "UnitTagTable::delivery_speed_context_batch_count", "UnitTagTable::delivery_speed_context_batch_start_timestamp_ms", "UnitTagTable::delivery_speed_context_batch_value_mean", "UnitTagTable::delivery_speed_context_batch_value_speed", "UnitTagTable::delivery_speed_context_batch_value_sum", "UnitTagTable::dry_up_base_value", "UnitTagTable::dynamic_lower_bound_cpa_bid", "UnitTagTable::dynamic_lower_bound_roas", "UnitTagTable::dynamic_upper_bound_cpa_bid", "UnitTagTable::dynamic_upper_bound_roas", "UnitTagTable::first_delivery_timestamp_ms", "UnitTagTable::first_industry_name", "UnitTagTable::gmv", "UnitTagTable::gmv_count", "UnitTagTable::group_tag", "UnitTagTable::group_tag_num", "UnitTagTable::has_bid_state_info_ptr", "UnitTagTable::is_cold_start", "UnitTagTable::item_type", "UnitTagTable::item_type_info_direct_live_cnt", "UnitTagTable::item_type_info_direct_live_cost", "UnitTagTable::item_type_info_photo_to_live_cnt", "UnitTagTable::item_type_info_photo_to_live_cost", "UnitTagTable::item_type_num", "UnitTagTable::last_ad_valid", "UnitTagTable::last_delivery_timestamp_ms", "UnitTagTable::last_is_ad_open", "UnitTagTable::last_monitor_timestamp", "UnitTagTable::last_sync_context_timestamp", "UnitTagTable::last_sync_result_timestamp", "UnitTagTable::last_update_adjust_timestamp", "UnitTagTable::last_update_context_timestamp", "UnitTagTable::last_update_cost", "UnitTagTable::live_stream_id", "UnitTagTable::ocpc_action_type", "UnitTagTable::old_adjust_auto_atv_rate", "UnitTagTable::old_auto_cpa_bid", "UnitTagTable::old_auto_roi_ratio", "UnitTagTable::old_is_out_of_budget", "UnitTagTable::online", "UnitTagTable::price_after_billing_separate", "UnitTagTable::price_before_billing_separate", "UnitTagTable::price_ratio_context_batch_avg_price_ratio", "UnitTagTable::price_ratio_context_batch_count", "UnitTagTable::price_ratio_context_batch_price_ratio_mean", "UnitTagTable::price_ratio_context_batch_start_timestamp_ms", "UnitTagTable::price_ratio_context_batch_total_ecpm", "UnitTagTable::price_ratio_context_batch_total_price", "UnitTagTable::price_ratio_context_batch_total_price_ratio", "UnitTagTable::promotion_type", "UnitTagTable::reach_lower_bound_timestamp", "UnitTagTable::reach_upper_bound_timestamp", "UnitTagTable::record_gsp_price", "UnitTagTable::relax_cpa_bid", "UnitTagTable::relax_roi_ratio", "UnitTagTable::roi_ratio", "UnitTagTable::rt_cost_speed", "UnitTagTable::rt_cpa_bid", "UnitTagTable::rt_delivery_speed", "UnitTagTable::rt_roas", "UnitTagTable::separate_gsp_price", "UnitTagTable::softad_cost", "UnitTagTable::softad_diff_ratio", "UnitTagTable::softad_target_cost", "UnitTagTable::speed_type", "UnitTagTable::sync_context_interval_batch_batch_cost", "UnitTagTable::sync_context_interval_batch_batch_gmv", "UnitTagTable::sync_context_interval_batch_batch_start_timestamp", "UnitTagTable::target_cost", "UnitTagTable::target_gmv", "UnitTagTable::total_auction_bid", "UnitTagTable::total_ecpm", "UnitTagTable::unit_id", "UnitTagTable::user_cost_prior_algo", "UnitTagTable::util_vals_ab_exp_ratio", "UnitTagTable::util_vals_acc_explore_bid_target_cost_speed", "UnitTagTable::util_vals_ad_off_target_cost", "UnitTagTable::util_vals_ad_short_type", "UnitTagTable::util_vals_ad_status", "UnitTagTable::util_vals_ad_status_tag", "UnitTagTable::util_vals_ad_status_tag_change", "UnitTagTable::util_vals_adjust_rate_lower_bound", "UnitTagTable::util_vals_adjust_rate_upper_bound", "UnitTagTable::util_vals_advertisable", "UnitTagTable::util_vals_budget", "UnitTagTable::util_vals_cold_start_cost_li_thr", "UnitTagTable::util_vals_cold_start_low_bound", "UnitTagTable::util_vals_current_ms", "UnitTagTable::util_vals_d_val", "UnitTagTable::util_vals_d_val_conv", "UnitTagTable::util_vals_d_val_cost", "UnitTagTable::util_vals_d_weight", "UnitTagTable::util_vals_d_weight_conv", "UnitTagTable::util_vals_d_weight_cost", "UnitTagTable::util_vals_disable_day_reset_exp", "UnitTagTable::util_vals_dynamic_bid_lower_bound_adjust_rate", "UnitTagTable::util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms", "UnitTagTable::util_vals_dynamic_bid_upper_bound_adjust_rate", "UnitTagTable::util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms", "UnitTagTable::util_vals_enable_cost_prior_algo", "UnitTagTable::util_vals_enable_linear_adaptive_update_time", "UnitTagTable::util_vals_enable_linear_after_cold_start_no_conv_drop", "UnitTagTable::util_vals_enable_linear_pacing_cold_start_low_bound", "UnitTagTable::util_vals_enable_linear_pacing_grid_correction", "UnitTagTable::util_vals_enable_univ_inner_ad_delivery_replace", "UnitTagTable::util_vals_explore_d_weight", "UnitTagTable::util_vals_explore_i_weight", "UnitTagTable::util_vals_explore_p_weight", "UnitTagTable::util_vals_fanstop_adjust_rate_lower_bound_cpa", "UnitTagTable::util_vals_fanstop_adjust_rate_lower_bound_roi", "UnitTagTable::util_vals_fanstop_adjust_rate_upper_bound_cpa", "UnitTagTable::util_vals_fanstop_adjust_rate_upper_bound_roi", "UnitTagTable::util_vals_hard_bid_lower_bound_rate_monitor", "UnitTagTable::util_vals_hard_bid_upper_bound_rate_monitor", "UnitTagTable::util_vals_hard_lower_bound_auto_atv", "UnitTagTable::util_vals_hard_lower_bound_auto_cpa_bid", "UnitTagTable::util_vals_hard_lower_bound_auto_roas", "UnitTagTable::util_vals_hard_upper_bound_auto_atv", "UnitTagTable::util_vals_hard_upper_bound_auto_cpa_bid", "UnitTagTable::util_vals_hard_upper_bound_auto_roas", "UnitTagTable::util_vals_i_val", "UnitTagTable::util_vals_i_val_conv", "UnitTagTable::util_vals_i_val_cost", "UnitTagTable::util_vals_i_weight", "UnitTagTable::util_vals_i_weight_conv", "UnitTagTable::util_vals_i_weight_cost", "UnitTagTable::util_vals_interval_cost", "UnitTagTable::util_vals_is_acc_explore_bid", "UnitTagTable::util_vals_is_ad_open", "UnitTagTable::util_vals_is_apply_adjust", "UnitTagTable::util_vals_is_apply_operation_config", "UnitTagTable::util_vals_is_bid_info", "UnitTagTable::util_vals_is_bid_ptr_null", "UnitTagTable::util_vals_is_cold_start", "UnitTagTable::util_vals_is_cost_target_msg", "UnitTagTable::util_vals_is_costcap_nobid", "UnitTagTable::util_vals_is_debug", "UnitTagTable::util_vals_is_debug_on", "UnitTagTable::util_vals_is_explore", "UnitTagTable::util_vals_is_fanstop", "UnitTagTable::util_vals_is_fanstop_ocpm", "UnitTagTable::util_vals_is_interval_cost", "UnitTagTable::util_vals_is_monitor", "UnitTagTable::util_vals_is_no_bid", "UnitTagTable::util_vals_is_normal", "UnitTagTable::util_vals_is_nothing_to_do", "UnitTagTable::util_vals_is_ocpm_bid_process", "UnitTagTable::util_vals_is_on_live", "UnitTagTable::util_vals_is_online", "UnitTagTable::util_vals_is_out_of_budget", "UnitTagTable::util_vals_is_price_ratio_bound", "UnitTagTable::util_vals_is_process", "UnitTagTable::util_vals_is_reset_context", "UnitTagTable::util_vals_is_roas", "UnitTagTable::util_vals_is_roas_sub_pacing", "UnitTagTable::util_vals_is_skip_cold_start", "UnitTagTable::util_vals_is_skip_update", "UnitTagTable::util_vals_is_start_process", "UnitTagTable::util_vals_is_status_open", "UnitTagTable::util_vals_is_sync_to_dsp", "UnitTagTable::util_vals_is_target_modify", "UnitTagTable::util_vals_is_update_adjust", "UnitTagTable::util_vals_is_update_interval_ms", "UnitTagTable::util_vals_item_type_num", "UnitTagTable::util_vals_new_adjust_auto_value_rate", "UnitTagTable::util_vals_old_adjust_auto_value_rate", "UnitTagTable::util_vals_old_is_out_of_budget", "UnitTagTable::util_vals_p_val", "UnitTagTable::util_vals_p_val_conv", "UnitTagTable::util_vals_p_val_cost", "UnitTagTable::util_vals_p_weight", "UnitTagTable::util_vals_p_weight_conv", "UnitTagTable::util_vals_p_weight_cost", "UnitTagTable::util_vals_pacing_method_type", "UnitTagTable::util_vals_pacing_type", "UnitTagTable::util_vals_pacing_weight", "UnitTagTable::util_vals_pacing_weight_conv", "UnitTagTable::util_vals_pacing_weight_cost", "UnitTagTable::util_vals_prior_cost", "UnitTagTable::util_vals_pro_ocpc_not_been_set", "UnitTagTable::util_vals_promotion_type_num", "UnitTagTable::util_vals_reset_pacing", "UnitTagTable::util_vals_rt_cost_speed", "UnitTagTable::util_vals_rt_delivery_speed", "UnitTagTable::util_vals_start_bid_rate", "UnitTagTable::util_vals_start_bid_rate_sub_pacing", "UnitTagTable::util_vals_step_bid_rate_descent", "UnitTagTable::util_vals_step_bid_rate_increase", "UnitTagTable::util_vals_target_cost_speed", "UnitTagTable::util_vals_target_delivery_speed", "UnitTagTable::util_vals_target_modify_ratio", "UnitTagTable::util_vals_target_roas", "UnitTagTable::util_vals_thread_id", "UnitTagTable::util_vals_total_pred_conv", "UnitTagTable::util_vals_unit_id", "UnitTagTable::util_vals_update_interval_ms"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopStepOneWriteToRedisMixer"}, "inner_loop_ocpm_bid_context_mixer_28DDF2": {"item_table": "TriggerTable", "message_seq": {"item_table": "TriggerTable", "column": "message_seq"}, "event_server_timestamp": {"item_table": "TriggerTable", "column": "event_server_timestamp"}, "bid_data_level": 0, "bid_state_info": {"bid_state_info_has_bid_state_info_ptr": {"item_table": "UnitTagTable", "column": "has_bid_state_info_ptr"}, "bid_state_info_ptr": {"item_table": "UnitTagTable", "column": "bid_state_info_ptr"}, "bid_state_info_explore_time_period": {"item_table": "UnitTagTable", "column": "bid_state_info_explore_time_period"}, "bid_state_info_explore_budget_start_time": {"item_table": "UnitTagTable", "column": "bid_state_info_explore_budget_start_time"}, "bid_state_info_explore_budget_status": {"item_table": "UnitTagTable", "column": "bid_state_info_explore_budget_status"}, "bid_state_info_explore_budget": {"item_table": "UnitTagTable", "column": "bid_state_info_explore_budget"}, "bid_state_info_is_live": {"item_table": "UnitTagTable", "column": "bid_state_info_is_live"}, "bid_state_info_ad_status": {"item_table": "UnitTagTable", "column": "bid_state_info_ad_status"}, "bid_state_info_advertisable": {"item_table": "UnitTagTable", "column": "bid_state_info_advertisable"}, "bid_state_info_is_status_open": {"item_table": "UnitTagTable", "column": "bid_state_info_is_status_open"}, "bid_state_info_online": {"item_table": "UnitTagTable", "column": "bid_state_info_online"}, "bid_state_info_left_budget": {"item_table": "UnitTagTable", "column": "bid_state_info_left_budget"}, "bid_state_info_budget": {"item_table": "UnitTagTable", "column": "bid_state_info_budget"}, "bid_state_info_account_type": {"item_table": "UnitTagTable", "column": "bid_state_info_account_type"}, "bid_state_info_bid_strategy": {"item_table": "UnitTagTable", "column": "bid_state_info_bid_strategy"}, "bid_state_info_bid_type": {"item_table": "UnitTagTable", "column": "bid_state_info_bid_type"}, "bid_state_info_live_launch_type": {"item_table": "UnitTagTable", "column": "bid_state_info_live_launch_type"}, "bid_state_info_account_id": {"item_table": "UnitTagTable", "column": "bid_state_info_account_id"}, "bid_state_info_campaign_id": {"item_table": "UnitTagTable", "column": "bid_state_info_campaign_id"}, "bid_state_info_author_id": {"item_table": "UnitTagTable", "column": "bid_state_info_author_id"}, "bid_state_info_live_stream_id": {"item_table": "UnitTagTable", "column": "bid_state_info_live_stream_id"}, "bid_state_info_speed_type": {"item_table": "UnitTagTable", "column": "bid_state_info_speed_type"}, "bid_state_info_promotion_type": {"item_table": "UnitTagTable", "column": "bid_state_info_promotion_type"}, "bid_state_info_campaign_type": {"item_table": "UnitTagTable", "column": "bid_state_info_campaign_type"}, "bid_state_info_cpa_bid": {"item_table": "UnitTagTable", "column": "bid_state_info_cpa_bid"}, "bid_state_info_roi_ratio": {"item_table": "UnitTagTable", "column": "bid_state_info_roi_ratio"}}, "msg_attr_info": {"msg_campaign_type": {"item_table": "TriggerTable", "column": "campaign_type"}, "msg_ocpc_action_type": {"item_table": "TriggerTable", "column": "ocpc_action_type"}, "msg_promotion_type": {"item_table": "TriggerTable", "column": "promotion_type"}, "msg_item_type": {"item_table": "TriggerTable", "column": "item_type"}, "msg_author_id": {"item_table": "TriggerTable", "column": "author_id"}, "msg_account_id": {"item_table": "TriggerTable", "column": "account_id"}, "msg_live_stream_id": {"item_table": "TriggerTable", "column": "live_stream_id"}, "msg_unit_id": {"item_table": "TriggerTable", "column": "unit_id"}, "msg_conv_num": {"item_table": "TriggerTable", "column": "conv_num"}, "msg_campaign_id": {"item_table": "TriggerTable", "column": "campaign_id"}, "msg_item_type_num": {"item_table": "TriggerTable", "column": "item_type_num"}, "msg_cost": {"item_table": "TriggerTable", "column": "cost"}, "msg_gmv": {"item_table": "TriggerTable", "column": "gmv"}, "msg_roi_ratio": {"item_table": "TriggerTable", "column": "roi_ratio"}, "msg_action_type": {"item_table": "TriggerTable", "column": "action_type"}, "msg_group_tag": {"item_table": "TriggerTable", "column": "inner_group_tag"}, "msg_bid_type": {"item_table": "TriggerTable", "column": "bid_type"}, "msg_medium_attribute": {"item_table": "TriggerTable", "column": "medium_attribute"}, "msg_speed_type": {"item_table": "TriggerTable", "column": "speed_type"}, "msg_delivery_timestamp": {"item_table": "TriggerTable", "column": "delivery_timestamp"}, "msg_cpa_bid": {"item_table": "TriggerTable", "column": "cpa_bid"}, "msg_is_soft": {"item_table": "TriggerTable", "column": "is_soft"}, "msg_target_cost": {"item_table": "TriggerTable", "column": "target_cost"}, "msg_target_gmv": {"item_table": "TriggerTable", "column": "target_gmv"}, "msg_separate_gsp_price": {"item_table": "TriggerTable", "column": "separate_gsp_price"}, "msg_record_gsp_price": {"item_table": "TriggerTable", "column": "record_gsp_price"}, "msg_pred_conv": {"item_table": "TriggerTable", "column": "pred_conv"}, "msg_pred_cvr_sum": {"item_table": "TriggerTable", "column": "pred_cvr_sum"}, "msg_pred_ctr_sum": {"item_table": "TriggerTable", "column": "pred_ctr_sum"}, "msg_price_before_billing_separate": {"item_table": "TriggerTable", "column": "price_before_billing_separate"}, "msg_price_after_billing_separate": {"item_table": "TriggerTable", "column": "price_after_billing_separate"}, "msg_ecpm": {"item_table": "TriggerTable", "column": "ecpm"}, "msg_auction_bid": {"item_table": "TriggerTable", "column": "auction_bid"}, "msg_price_ratio": {"item_table": "TriggerTable", "column": "price_ratio"}, "msg_is_store_wide_roi_reco_conv": {"item_table": "TriggerTable", "column": "is_store_wide_roi_reco_conv"}}, "bid_ctx_attr_info": {"bid_ctx_bid_context_key": {"item_table": "UnitTagTable", "column": "bid_context_key"}, "bid_ctx_group_tag": {"item_table": "UnitTagTable", "column": "group_tag"}, "bid_ctx_campaign_type": {"item_table": "UnitTagTable", "column": "campaign_type"}, "bid_ctx_ocpc_action_type": {"item_table": "UnitTagTable", "column": "ocpc_action_type"}, "bid_ctx_promotion_type": {"item_table": "UnitTagTable", "column": "promotion_type"}, "bid_ctx_item_type": {"item_table": "UnitTagTable", "column": "item_type"}, "bid_ctx_first_industry_name": {"item_table": "UnitTagTable", "column": "first_industry_name"}, "bid_ctx_group_tag_num": {"item_table": "UnitTagTable", "column": "group_tag_num"}, "bid_ctx_unit_id": {"item_table": "UnitTagTable", "column": "unit_id"}, "bid_ctx_account_id": {"item_table": "UnitTagTable", "column": "account_id"}, "bid_ctx_author_id": {"item_table": "UnitTagTable", "column": "author_id"}, "bid_ctx_live_stream_id": {"item_table": "UnitTagTable", "column": "live_stream_id"}, "bid_ctx_item_type_num": {"item_table": "UnitTagTable", "column": "item_type_num"}, "bid_ctx_campaign_id": {"item_table": "UnitTagTable", "column": "campaign_id"}, "bid_ctx_price_before_billing_separate": {"item_table": "UnitTagTable", "column": "price_before_billing_separate"}, "bid_ctx_price_after_billing_separate": {"item_table": "UnitTagTable", "column": "price_after_billing_separate"}, "bid_ctx_cost": {"item_table": "UnitTagTable", "column": "cost"}, "bid_ctx_gmv": {"item_table": "UnitTagTable", "column": "gmv"}, "bid_ctx_conv_num": {"item_table": "UnitTagTable", "column": "conv_num"}, "bid_ctx_cost_count": {"item_table": "UnitTagTable", "column": "cost_count"}, "bid_ctx_gmv_count": {"item_table": "UnitTagTable", "column": "gmv_count"}, "bid_ctx_delivery_cnt": {"item_table": "UnitTagTable", "column": "delivery_cnt"}, "bid_ctx_cpa_bid": {"item_table": "UnitTagTable", "column": "cpa_bid"}, "bid_ctx_relax_cpa_bid": {"item_table": "UnitTagTable", "column": "relax_cpa_bid"}, "bid_ctx_roi_ratio": {"item_table": "UnitTagTable", "column": "roi_ratio"}, "bid_ctx_relax_roi_ratio": {"item_table": "UnitTagTable", "column": "relax_roi_ratio"}, "bid_ctx_rt_cpa_bid": {"item_table": "UnitTagTable", "column": "rt_cpa_bid"}, "bid_ctx_rt_roas": {"item_table": "UnitTagTable", "column": "rt_roas"}, "bid_ctx_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "auto_cpa_bid"}, "bid_ctx_auto_roi_ratio": {"item_table": "UnitTagTable", "column": "auto_roi_ratio"}, "bid_ctx_old_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "old_auto_cpa_bid"}, "bid_ctx_old_auto_roi_ratio": {"item_table": "UnitTagTable", "column": "old_auto_roi_ratio"}, "bid_ctx_adjust_rate": {"item_table": "UnitTagTable", "column": "adjust_rate"}, "bid_ctx_rt_cost_speed": {"item_table": "UnitTagTable", "column": "rt_cost_speed"}, "bid_ctx_rt_delivery_speed": {"item_table": "UnitTagTable", "column": "rt_delivery_speed"}, "bid_ctx_dynamic_lower_bound_roas": {"item_table": "UnitTagTable", "column": "dynamic_lower_bound_roas"}, "bid_ctx_dynamic_upper_bound_roas": {"item_table": "UnitTagTable", "column": "dynamic_upper_bound_roas"}, "bid_ctx_dynamic_lower_bound_cpa_bid": {"item_table": "UnitTagTable", "column": "dynamic_lower_bound_cpa_bid"}, "bid_ctx_dynamic_upper_bound_cpa_bid": {"item_table": "UnitTagTable", "column": "dynamic_upper_bound_cpa_bid"}, "bid_ctx_target_cost": {"item_table": "UnitTagTable", "column": "target_cost"}, "bid_ctx_target_gmv": {"item_table": "UnitTagTable", "column": "target_gmv"}, "bid_ctx_record_gsp_price": {"item_table": "UnitTagTable", "column": "record_gsp_price"}, "bid_ctx_reach_lower_bound_timestamp": {"item_table": "UnitTagTable", "column": "reach_lower_bound_timestamp"}, "bid_ctx_reach_upper_bound_timestamp": {"item_table": "UnitTagTable", "column": "reach_upper_bound_timestamp"}, "bid_ctx_first_delivery_timestamp_ms": {"item_table": "UnitTagTable", "column": "first_delivery_timestamp_ms"}, "bid_ctx_last_delivery_timestamp_ms": {"item_table": "UnitTagTable", "column": "last_delivery_timestamp_ms"}, "bid_ctx_last_update_adjust_timestamp": {"item_table": "UnitTagTable", "column": "last_update_adjust_timestamp"}, "bid_ctx_cost_start_timestamp_ms": {"item_table": "UnitTagTable", "column": "cost_start_timestamp_ms"}, "bid_ctx_last_update_context_timestamp": {"item_table": "UnitTagTable", "column": "last_update_context_timestamp"}, "bid_ctx_last_sync_context_timestamp": {"item_table": "UnitTagTable", "column": "last_sync_context_timestamp"}, "bid_ctx_online": {"item_table": "UnitTagTable", "column": "online"}, "bid_ctx_is_cold_start": {"item_table": "UnitTagTable", "column": "is_cold_start"}, "bid_ctx_speed_type": {"item_table": "UnitTagTable", "column": "speed_type"}, "bid_ctx_context_start_timestamp": {"item_table": "UnitTagTable", "column": "context_start_timestamp"}, "bid_ctx_last_sync_result_timestamp": {"item_table": "UnitTagTable", "column": "last_sync_result_timestamp"}, "bid_ctx_adjust_auto_value_rate": {"item_table": "UnitTagTable", "column": "adjust_auto_value_rate"}, "bid_ctx_total_ecpm": {"item_table": "UnitTagTable", "column": "total_ecpm"}, "bid_ctx_separate_gsp_price": {"item_table": "UnitTagTable", "column": "separate_gsp_price"}, "bid_ctx_total_auction_bid": {"item_table": "UnitTagTable", "column": "total_auction_bid"}, "bid_ctx_dry_up_base_value": {"item_table": "UnitTagTable", "column": "dry_up_base_value"}, "bid_ctx_ctr_sum": {"item_table": "UnitTagTable", "column": "ctr_sum"}, "bid_ctx_cvr_sum": {"item_table": "UnitTagTable", "column": "cvr_sum"}, "bid_ctx_softad_cost": {"item_table": "UnitTagTable", "column": "softad_cost"}, "bid_ctx_softad_target_cost": {"item_table": "UnitTagTable", "column": "softad_target_cost"}, "bid_ctx_softad_diff_ratio": {"item_table": "UnitTagTable", "column": "softad_diff_ratio"}, "bid_ctx_sync_context_interval_batch_batch_cost": {"item_table": "UnitTagTable", "column": "sync_context_interval_batch_batch_cost"}, "bid_ctx_sync_context_interval_batch_batch_gmv": {"item_table": "UnitTagTable", "column": "sync_context_interval_batch_batch_gmv"}, "bid_ctx_sync_context_interval_batch_batch_start_timestamp": {"item_table": "UnitTagTable", "column": "sync_context_interval_batch_batch_start_timestamp"}, "bid_ctx_cost_speed_context_batch_start_timestamp_ms": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_start_timestamp_ms"}, "bid_ctx_cost_speed_context_batch_value_sum": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_value_sum"}, "bid_ctx_cost_speed_context_batch_count": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_count"}, "bid_ctx_cost_speed_context_batch_value_mean": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_value_mean"}, "bid_ctx_cost_speed_context_batch_value_speed": {"item_table": "UnitTagTable", "column": "cost_speed_context_batch_value_speed"}, "bid_ctx_delivery_speed_context_batch_start_timestamp_ms": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_start_timestamp_ms"}, "bid_ctx_delivery_speed_context_batch_value_sum": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_value_sum"}, "bid_ctx_delivery_speed_context_batch_count": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_count"}, "bid_ctx_delivery_speed_context_batch_value_mean": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_value_mean"}, "bid_ctx_delivery_speed_context_batch_value_speed": {"item_table": "UnitTagTable", "column": "delivery_speed_context_batch_value_speed"}, "bid_ctx_conv_ratio_adjust_context_pred_conv": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_pred_conv"}, "bid_ctx_conv_ratio_adjust_context_old_auto_conv_ratio": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_old_auto_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_auto_conv_ratio": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_auto_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_real_conv": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_real_conv"}, "bid_ctx_conv_ratio_adjust_context_last_update_conv_ratio_timestamp": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_last_update_conv_ratio_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_r1_upper_bound_timestamp": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_reach_r1_upper_bound_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_r1_lower_bound_timestamp": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_reach_r1_lower_bound_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp": {"item_table": "UnitTagTable", "column": "conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp"}, "bid_ctx_price_ratio_context_batch_start_timestamp_ms": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_start_timestamp_ms"}, "bid_ctx_price_ratio_context_batch_total_price": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_total_price"}, "bid_ctx_price_ratio_context_batch_total_ecpm": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_total_ecpm"}, "bid_ctx_price_ratio_context_batch_total_price_ratio": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_total_price_ratio"}, "bid_ctx_price_ratio_context_batch_count": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_count"}, "bid_ctx_price_ratio_context_batch_avg_price_ratio": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_avg_price_ratio"}, "bid_ctx_price_ratio_context_batch_price_ratio_mean": {"item_table": "UnitTagTable", "column": "price_ratio_context_batch_price_ratio_mean"}, "bid_ctx_item_type_info_photo_to_live_cnt": {"item_table": "UnitTagTable", "column": "item_type_info_photo_to_live_cnt"}, "bid_ctx_item_type_info_direct_live_cnt": {"item_table": "UnitTagTable", "column": "item_type_info_direct_live_cnt"}, "bid_ctx_item_type_info_photo_to_live_cost": {"item_table": "UnitTagTable", "column": "item_type_info_photo_to_live_cost"}, "bid_ctx_item_type_info_direct_live_cost": {"item_table": "UnitTagTable", "column": "item_type_info_direct_live_cost"}}, "process_utils_attr_info": {"process_utils_ad_off_target_cost": {"item_table": "UnitTagTable", "column": "util_vals_ad_off_target_cost"}, "process_utils_new_adjust_auto_value_rate": {"item_table": "UnitTagTable", "column": "util_vals_new_adjust_auto_value_rate"}, "process_utils_old_adjust_auto_value_rate": {"item_table": "UnitTagTable", "column": "util_vals_old_adjust_auto_value_rate"}, "process_utils_is_bid_ptr_null": {"item_table": "UnitTagTable", "column": "util_vals_is_bid_ptr_null"}, "process_utils_pro_ocpc_not_been_set": {"item_table": "UnitTagTable", "column": "util_vals_pro_ocpc_not_been_set"}, "process_utils_reset_pacing": {"item_table": "UnitTagTable", "column": "util_vals_reset_pacing"}, "process_utils_is_cost_target_msg": {"item_table": "UnitTagTable", "column": "util_vals_is_cost_target_msg"}, "process_utils_enable_cost_prior_algo": {"item_table": "UnitTagTable", "column": "util_vals_enable_cost_prior_algo"}, "process_utils_enable_linear_pacing_cold_start_low_bound": {"item_table": "UnitTagTable", "column": "util_vals_enable_linear_pacing_cold_start_low_bound"}, "process_utils_enable_linear_pacing_grid_correction": {"item_table": "UnitTagTable", "column": "util_vals_enable_linear_pacing_grid_correction"}, "process_utils_enable_linear_adaptive_update_time": {"item_table": "UnitTagTable", "column": "util_vals_enable_linear_adaptive_update_time"}, "process_utils_enable_linear_after_cold_start_no_conv_drop": {"item_table": "UnitTagTable", "column": "util_vals_enable_linear_after_cold_start_no_conv_drop"}, "process_utils_disable_day_reset_exp": {"item_table": "UnitTagTable", "column": "util_vals_disable_day_reset_exp"}, "process_utils_enable_univ_inner_ad_delivery_replace": {"item_table": "UnitTagTable", "column": "util_vals_enable_univ_inner_ad_delivery_replace"}, "process_utils_is_apply_adjust": {"item_table": "UnitTagTable", "column": "util_vals_is_apply_adjust"}, "process_utils_is_debug": {"item_table": "UnitTagTable", "column": "util_vals_is_debug"}, "process_utils_is_roas_sub_pacing": {"item_table": "UnitTagTable", "column": "util_vals_is_roas_sub_pacing"}, "process_utils_is_ocpm_bid_process": {"item_table": "UnitTagTable", "column": "util_vals_is_ocpm_bid_process"}, "process_utils_is_apply_operation_config": {"item_table": "UnitTagTable", "column": "util_vals_is_apply_operation_config"}, "process_utils_is_start_process": {"item_table": "UnitTagTable", "column": "util_vals_is_start_process"}, "process_utils_is_debug_on": {"item_table": "UnitTagTable", "column": "util_vals_is_debug_on"}, "process_utils_is_normal": {"item_table": "UnitTagTable", "column": "util_vals_is_normal"}, "process_utils_is_fanstop": {"item_table": "UnitTagTable", "column": "util_vals_is_fanstop"}, "process_utils_is_fanstop_ocpm": {"item_table": "UnitTagTable", "column": "util_vals_is_fanstop_ocpm"}, "process_utils_is_roas": {"item_table": "UnitTagTable", "column": "util_vals_is_roas"}, "process_utils_is_no_bid": {"item_table": "UnitTagTable", "column": "util_vals_is_no_bid"}, "process_utils_is_skip_update": {"item_table": "UnitTagTable", "column": "util_vals_is_skip_update"}, "process_utils_is_nothing_to_do": {"item_table": "UnitTagTable", "column": "util_vals_is_nothing_to_do"}, "process_utils_is_process": {"item_table": "UnitTagTable", "column": "util_vals_is_process"}, "process_utils_is_reset_context": {"item_table": "UnitTagTable", "column": "util_vals_is_reset_context"}, "process_utils_is_update_adjust": {"item_table": "UnitTagTable", "column": "util_vals_is_update_adjust"}, "process_utils_is_interval_cost": {"item_table": "UnitTagTable", "column": "util_vals_is_interval_cost"}, "process_utils_is_update_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_is_update_interval_ms"}, "process_utils_is_ad_open": {"item_table": "UnitTagTable", "column": "util_vals_is_ad_open"}, "process_utils_is_sync_to_dsp": {"item_table": "UnitTagTable", "column": "util_vals_is_sync_to_dsp"}, "process_utils_is_monitor": {"item_table": "UnitTagTable", "column": "util_vals_is_monitor"}, "process_utils_is_cold_start": {"item_table": "UnitTagTable", "column": "util_vals_is_cold_start"}, "process_utils_is_costcap_nobid": {"item_table": "UnitTagTable", "column": "util_vals_is_costcap_nobid"}, "process_utils_is_skip_cold_start": {"item_table": "UnitTagTable", "column": "util_vals_is_skip_cold_start"}, "process_utils_is_on_live": {"item_table": "UnitTagTable", "column": "util_vals_is_on_live"}, "process_utils_is_out_of_budget": {"item_table": "UnitTagTable", "column": "util_vals_is_out_of_budget"}, "process_utils_old_is_out_of_budget": {"item_table": "UnitTagTable", "column": "util_vals_old_is_out_of_budget"}, "process_utils_advertisable": {"item_table": "UnitTagTable", "column": "util_vals_advertisable"}, "process_utils_is_status_open": {"item_table": "UnitTagTable", "column": "util_vals_is_status_open"}, "process_utils_ad_status": {"item_table": "UnitTagTable", "column": "util_vals_ad_status"}, "process_utils_unit_id": {"item_table": "UnitTagTable", "column": "util_vals_unit_id"}, "process_utils_thread_id": {"item_table": "UnitTagTable", "column": "util_vals_thread_id"}, "process_utils_is_bid_info": {"item_table": "UnitTagTable", "column": "util_vals_is_bid_info"}, "process_utils_is_online": {"item_table": "UnitTagTable", "column": "util_vals_is_online"}, "process_utils_is_explore": {"item_table": "UnitTagTable", "column": "util_vals_is_explore"}, "process_utils_ab_exp_ratio": {"item_table": "UnitTagTable", "column": "util_vals_ab_exp_ratio"}, "process_utils_promotion_type_num": {"item_table": "UnitTagTable", "column": "util_vals_promotion_type_num"}, "process_utils_item_type_num": {"item_table": "UnitTagTable", "column": "util_vals_item_type_num"}, "process_utils_prior_cost": {"item_table": "UnitTagTable", "column": "util_vals_prior_cost"}, "process_utils_cold_start_cost_li_thr": {"item_table": "UnitTagTable", "column": "util_vals_cold_start_cost_li_thr"}, "process_utils_cold_start_low_bound": {"item_table": "UnitTagTable", "column": "util_vals_cold_start_low_bound"}, "process_utils_rt_cost_speed": {"item_table": "UnitTagTable", "column": "util_vals_rt_cost_speed"}, "process_utils_rt_delivery_speed": {"item_table": "UnitTagTable", "column": "util_vals_rt_delivery_speed"}, "process_utils_target_roas": {"item_table": "UnitTagTable", "column": "util_vals_target_roas"}, "process_utils_is_price_ratio_bound": {"item_table": "UnitTagTable", "column": "util_vals_is_price_ratio_bound"}, "process_utils_is_acc_explore_bid": {"item_table": "UnitTagTable", "column": "util_vals_is_acc_explore_bid"}, "process_utils_dynamic_bid_lower_bound_adjust_time_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms"}, "process_utils_dynamic_bid_upper_bound_adjust_time_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms"}, "process_utils_dynamic_bid_lower_bound_adjust_rate": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_rate"}, "process_utils_dynamic_bid_upper_bound_adjust_rate": {"item_table": "UnitTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_rate"}, "process_utils_p_weight": {"item_table": "UnitTagTable", "column": "util_vals_p_weight"}, "process_utils_i_weight": {"item_table": "UnitTagTable", "column": "util_vals_i_weight"}, "process_utils_d_weight": {"item_table": "UnitTagTable", "column": "util_vals_d_weight"}, "process_utils_explore_p_weight": {"item_table": "UnitTagTable", "column": "util_vals_explore_p_weight"}, "process_utils_explore_i_weight": {"item_table": "UnitTagTable", "column": "util_vals_explore_i_weight"}, "process_utils_explore_d_weight": {"item_table": "UnitTagTable", "column": "util_vals_explore_d_weight"}, "process_utils_p_val": {"item_table": "UnitTagTable", "column": "util_vals_p_val"}, "process_utils_i_val": {"item_table": "UnitTagTable", "column": "util_vals_i_val"}, "process_utils_d_val": {"item_table": "UnitTagTable", "column": "util_vals_d_val"}, "process_utils_pacing_weight": {"item_table": "UnitTagTable", "column": "util_vals_pacing_weight"}, "process_utils_target_cost_speed": {"item_table": "UnitTagTable", "column": "util_vals_target_cost_speed"}, "process_utils_target_delivery_speed": {"item_table": "UnitTagTable", "column": "util_vals_target_delivery_speed"}, "process_utils_start_bid_rate": {"item_table": "UnitTagTable", "column": "util_vals_start_bid_rate"}, "process_utils_start_bid_rate_sub_pacing": {"item_table": "UnitTagTable", "column": "util_vals_start_bid_rate_sub_pacing"}, "process_utils_hard_upper_bound_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "util_vals_hard_upper_bound_auto_cpa_bid"}, "process_utils_hard_lower_bound_auto_cpa_bid": {"item_table": "UnitTagTable", "column": "util_vals_hard_lower_bound_auto_cpa_bid"}, "process_utils_hard_lower_bound_auto_roas": {"item_table": "UnitTagTable", "column": "util_vals_hard_lower_bound_auto_roas"}, "process_utils_hard_upper_bound_auto_roas": {"item_table": "UnitTagTable", "column": "util_vals_hard_upper_bound_auto_roas"}, "process_utils_hard_lower_bound_auto_atv": {"item_table": "UnitTagTable", "column": "util_vals_hard_lower_bound_auto_atv"}, "process_utils_hard_upper_bound_auto_atv": {"item_table": "UnitTagTable", "column": "util_vals_hard_upper_bound_auto_atv"}, "process_utils_acc_explore_bid_target_cost_speed": {"item_table": "UnitTagTable", "column": "util_vals_acc_explore_bid_target_cost_speed"}, "process_utils_current_ms": {"item_table": "UnitTagTable", "column": "util_vals_current_ms"}, "process_utils_ad_short_type": {"item_table": "UnitTagTable", "column": "util_vals_ad_short_type"}, "process_utils_is_target_modify": {"item_table": "UnitTagTable", "column": "util_vals_is_target_modify"}, "process_utils_target_modify_ratio": {"item_table": "UnitTagTable", "column": "util_vals_target_modify_ratio"}, "process_utils_p_weight_conv": {"item_table": "UnitTagTable", "column": "util_vals_p_weight_conv"}, "process_utils_i_weight_conv": {"item_table": "UnitTagTable", "column": "util_vals_i_weight_conv"}, "process_utils_d_weight_conv": {"item_table": "UnitTagTable", "column": "util_vals_d_weight_conv"}, "process_utils_p_val_conv": {"item_table": "UnitTagTable", "column": "util_vals_p_val_conv"}, "process_utils_i_val_conv": {"item_table": "UnitTagTable", "column": "util_vals_i_val_conv"}, "process_utils_d_val_conv": {"item_table": "UnitTagTable", "column": "util_vals_d_val_conv"}, "process_utils_pacing_weight_conv": {"item_table": "UnitTagTable", "column": "util_vals_pacing_weight_conv"}, "process_utils_p_weight_cost": {"item_table": "UnitTagTable", "column": "util_vals_p_weight_cost"}, "process_utils_i_weight_cost": {"item_table": "UnitTagTable", "column": "util_vals_i_weight_cost"}, "process_utils_d_weight_cost": {"item_table": "UnitTagTable", "column": "util_vals_d_weight_cost"}, "process_utils_p_val_cost": {"item_table": "UnitTagTable", "column": "util_vals_p_val_cost"}, "process_utils_i_val_cost": {"item_table": "UnitTagTable", "column": "util_vals_i_val_cost"}, "process_utils_d_val_cost": {"item_table": "UnitTagTable", "column": "util_vals_d_val_cost"}, "process_utils_pacing_weight_cost": {"item_table": "UnitTagTable", "column": "util_vals_pacing_weight_cost"}, "process_utils_update_interval_ms": {"item_table": "UnitTagTable", "column": "util_vals_update_interval_ms"}, "process_utils_interval_cost": {"item_table": "UnitTagTable", "column": "util_vals_interval_cost"}, "process_utils_step_bid_rate_increase": {"item_table": "UnitTagTable", "column": "util_vals_step_bid_rate_increase"}, "process_utils_step_bid_rate_descent": {"item_table": "UnitTagTable", "column": "util_vals_step_bid_rate_descent"}, "process_utils_ad_status_tag": {"item_table": "UnitTagTable", "column": "util_vals_ad_status_tag"}, "process_utils_ad_status_tag_change": {"item_table": "UnitTagTable", "column": "util_vals_ad_status_tag_change"}, "process_utils_total_pred_conv": {"item_table": "UnitTagTable", "column": "util_vals_total_pred_conv"}, "process_utils_adjust_rate_lower_bound": {"item_table": "UnitTagTable", "column": "util_vals_adjust_rate_lower_bound"}, "process_utils_adjust_rate_upper_bound": {"item_table": "UnitTagTable", "column": "util_vals_adjust_rate_upper_bound"}, "process_utils_budget": {"item_table": "UnitTagTable", "column": "util_vals_budget"}, "process_utils_hard_bid_upper_bound_rate_monitor": {"item_table": "UnitTagTable", "column": "util_vals_hard_bid_upper_bound_rate_monitor"}, "process_utils_hard_bid_lower_bound_rate_monitor": {"item_table": "UnitTagTable", "column": "util_vals_hard_bid_lower_bound_rate_monitor"}, "process_utils_fanstop_adjust_rate_lower_bound_roi": {"item_table": "UnitTagTable", "column": "util_vals_fanstop_adjust_rate_lower_bound_roi"}, "process_utils_fanstop_adjust_rate_upper_bound_roi": {"item_table": "UnitTagTable", "column": "util_vals_fanstop_adjust_rate_upper_bound_roi"}, "process_utils_fanstop_adjust_rate_lower_bound_cpa": {"item_table": "UnitTagTable", "column": "util_vals_fanstop_adjust_rate_lower_bound_cpa"}, "process_utils_fanstop_adjust_rate_upper_bound_cpa": {"item_table": "UnitTagTable", "column": "util_vals_fanstop_adjust_rate_upper_bound_cpa"}, "process_utils_pacing_type": {"item_table": "UnitTagTable", "column": "util_vals_pacing_type"}, "process_utils_pacing_method_type": {"item_table": "UnitTagTable", "column": "util_vals_pacing_method_type"}}, "$metadata": {"$output_item_attrs": ["TriggerTable::account_id", "TriggerTable::action_type", "TriggerTable::auction_bid", "TriggerTable::author_id", "TriggerTable::bid_type", "TriggerTable::campaign_id", "TriggerTable::campaign_type", "TriggerTable::conv_num", "TriggerTable::cost", "TriggerTable::cpa_bid", "TriggerTable::delivery_timestamp", "TriggerTable::ecpm", "TriggerTable::gmv", "TriggerTable::inner_group_tag", "TriggerTable::is_soft", "TriggerTable::is_store_wide_roi_reco_conv", "TriggerTable::item_type", "TriggerTable::item_type_num", "TriggerTable::live_stream_id", "TriggerTable::medium_attribute", "TriggerTable::ocpc_action_type", "TriggerTable::pred_conv", "TriggerTable::pred_ctr_sum", "TriggerTable::pred_cvr_sum", "TriggerTable::price_after_billing_separate", "TriggerTable::price_before_billing_separate", "TriggerTable::price_ratio", "TriggerTable::promotion_type", "TriggerTable::record_gsp_price", "TriggerTable::roi_ratio", "TriggerTable::separate_gsp_price", "TriggerTable::speed_type", "TriggerTable::target_cost", "TriggerTable::target_gmv", "TriggerTable::unit_id", "UnitTagTable::account_id", "UnitTagTable::adjust_auto_value_rate", "UnitTagTable::adjust_rate", "UnitTagTable::author_id", "UnitTagTable::auto_cpa_bid", "UnitTagTable::auto_roi_ratio", "UnitTagTable::bid_context_key", "UnitTagTable::campaign_id", "UnitTagTable::campaign_type", "UnitTagTable::context_start_timestamp", "UnitTagTable::conv_num", "UnitTagTable::conv_ratio_adjust_context_auto_conv_ratio", "UnitTagTable::conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio", "UnitTagTable::conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio", "UnitTagTable::conv_ratio_adjust_context_last_update_conv_ratio_timestamp", "UnitTagTable::conv_ratio_adjust_context_old_auto_conv_ratio", "UnitTagTable::conv_ratio_adjust_context_pred_conv", "UnitTagTable::conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp", "UnitTagTable::conv_ratio_adjust_context_reach_r1_lower_bound_timestamp", "UnitTagTable::conv_ratio_adjust_context_reach_r1_upper_bound_timestamp", "UnitTagTable::conv_ratio_adjust_context_real_conv", "UnitTagTable::cost", "UnitTagTable::cost_count", "UnitTagTable::cost_speed_context_batch_count", "UnitTagTable::cost_speed_context_batch_start_timestamp_ms", "UnitTagTable::cost_speed_context_batch_value_mean", "UnitTagTable::cost_speed_context_batch_value_speed", "UnitTagTable::cost_speed_context_batch_value_sum", "UnitTagTable::cost_start_timestamp_ms", "UnitTagTable::cpa_bid", "UnitTagTable::ctr_sum", "UnitTagTable::cvr_sum", "UnitTagTable::delivery_cnt", "UnitTagTable::delivery_speed_context_batch_count", "UnitTagTable::delivery_speed_context_batch_start_timestamp_ms", "UnitTagTable::delivery_speed_context_batch_value_mean", "UnitTagTable::delivery_speed_context_batch_value_speed", "UnitTagTable::delivery_speed_context_batch_value_sum", "UnitTagTable::dry_up_base_value", "UnitTagTable::dynamic_lower_bound_cpa_bid", "UnitTagTable::dynamic_lower_bound_roas", "UnitTagTable::dynamic_upper_bound_cpa_bid", "UnitTagTable::dynamic_upper_bound_roas", "UnitTagTable::first_delivery_timestamp_ms", "UnitTagTable::first_industry_name", "UnitTagTable::gmv", "UnitTagTable::gmv_count", "UnitTagTable::group_tag", "UnitTagTable::group_tag_num", "UnitTagTable::is_cold_start", "UnitTagTable::item_type", "UnitTagTable::item_type_info_direct_live_cnt", "UnitTagTable::item_type_info_direct_live_cost", "UnitTagTable::item_type_info_photo_to_live_cnt", "UnitTagTable::item_type_info_photo_to_live_cost", "UnitTagTable::item_type_num", "UnitTagTable::last_delivery_timestamp_ms", "UnitTagTable::last_sync_context_timestamp", "UnitTagTable::last_sync_result_timestamp", "UnitTagTable::last_update_adjust_timestamp", "UnitTagTable::last_update_context_timestamp", "UnitTagTable::live_stream_id", "UnitTagTable::ocpc_action_type", "UnitTagTable::old_auto_cpa_bid", "UnitTagTable::old_auto_roi_ratio", "UnitTagTable::online", "UnitTagTable::price_after_billing_separate", "UnitTagTable::price_before_billing_separate", "UnitTagTable::price_ratio_context_batch_avg_price_ratio", "UnitTagTable::price_ratio_context_batch_count", "UnitTagTable::price_ratio_context_batch_price_ratio_mean", "UnitTagTable::price_ratio_context_batch_start_timestamp_ms", "UnitTagTable::price_ratio_context_batch_total_ecpm", "UnitTagTable::price_ratio_context_batch_total_price", "UnitTagTable::price_ratio_context_batch_total_price_ratio", "UnitTagTable::promotion_type", "UnitTagTable::reach_lower_bound_timestamp", "UnitTagTable::reach_upper_bound_timestamp", "UnitTagTable::record_gsp_price", "UnitTagTable::relax_cpa_bid", "UnitTagTable::relax_roi_ratio", "UnitTagTable::roi_ratio", "UnitTagTable::rt_cost_speed", "UnitTagTable::rt_cpa_bid", "UnitTagTable::rt_delivery_speed", "UnitTagTable::rt_roas", "UnitTagTable::separate_gsp_price", "UnitTagTable::softad_cost", "UnitTagTable::softad_diff_ratio", "UnitTagTable::softad_target_cost", "UnitTagTable::speed_type", "UnitTagTable::sync_context_interval_batch_batch_cost", "UnitTagTable::sync_context_interval_batch_batch_gmv", "UnitTagTable::sync_context_interval_batch_batch_start_timestamp", "UnitTagTable::target_cost", "UnitTagTable::target_gmv", "UnitTagTable::total_auction_bid", "UnitTagTable::total_ecpm", "UnitTagTable::unit_id", "UnitTagTable::util_vals_ab_exp_ratio", "UnitTagTable::util_vals_acc_explore_bid_target_cost_speed", "UnitTagTable::util_vals_ad_off_target_cost", "UnitTagTable::util_vals_ad_short_type", "UnitTagTable::util_vals_ad_status", "UnitTagTable::util_vals_ad_status_tag", "UnitTagTable::util_vals_ad_status_tag_change", "UnitTagTable::util_vals_adjust_rate_lower_bound", "UnitTagTable::util_vals_adjust_rate_upper_bound", "UnitTagTable::util_vals_advertisable", "UnitTagTable::util_vals_budget", "UnitTagTable::util_vals_cold_start_cost_li_thr", "UnitTagTable::util_vals_cold_start_low_bound", "UnitTagTable::util_vals_current_ms", "UnitTagTable::util_vals_d_val", "UnitTagTable::util_vals_d_val_conv", "UnitTagTable::util_vals_d_val_cost", "UnitTagTable::util_vals_d_weight", "UnitTagTable::util_vals_d_weight_conv", "UnitTagTable::util_vals_d_weight_cost", "UnitTagTable::util_vals_disable_day_reset_exp", "UnitTagTable::util_vals_dynamic_bid_lower_bound_adjust_rate", "UnitTagTable::util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms", "UnitTagTable::util_vals_dynamic_bid_upper_bound_adjust_rate", "UnitTagTable::util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms", "UnitTagTable::util_vals_enable_cost_prior_algo", "UnitTagTable::util_vals_enable_linear_adaptive_update_time", "UnitTagTable::util_vals_enable_linear_after_cold_start_no_conv_drop", "UnitTagTable::util_vals_enable_linear_pacing_cold_start_low_bound", "UnitTagTable::util_vals_enable_linear_pacing_grid_correction", "UnitTagTable::util_vals_enable_univ_inner_ad_delivery_replace", "UnitTagTable::util_vals_explore_d_weight", "UnitTagTable::util_vals_explore_i_weight", "UnitTagTable::util_vals_explore_p_weight", "UnitTagTable::util_vals_fanstop_adjust_rate_lower_bound_cpa", "UnitTagTable::util_vals_fanstop_adjust_rate_lower_bound_roi", "UnitTagTable::util_vals_fanstop_adjust_rate_upper_bound_cpa", "UnitTagTable::util_vals_fanstop_adjust_rate_upper_bound_roi", "UnitTagTable::util_vals_hard_bid_lower_bound_rate_monitor", "UnitTagTable::util_vals_hard_bid_upper_bound_rate_monitor", "UnitTagTable::util_vals_hard_lower_bound_auto_atv", "UnitTagTable::util_vals_hard_lower_bound_auto_cpa_bid", "UnitTagTable::util_vals_hard_lower_bound_auto_roas", "UnitTagTable::util_vals_hard_upper_bound_auto_atv", "UnitTagTable::util_vals_hard_upper_bound_auto_cpa_bid", "UnitTagTable::util_vals_hard_upper_bound_auto_roas", "UnitTagTable::util_vals_i_val", "UnitTagTable::util_vals_i_val_conv", "UnitTagTable::util_vals_i_val_cost", "UnitTagTable::util_vals_i_weight", "UnitTagTable::util_vals_i_weight_conv", "UnitTagTable::util_vals_i_weight_cost", "UnitTagTable::util_vals_interval_cost", "UnitTagTable::util_vals_is_acc_explore_bid", "UnitTagTable::util_vals_is_ad_open", "UnitTagTable::util_vals_is_apply_adjust", "UnitTagTable::util_vals_is_apply_operation_config", "UnitTagTable::util_vals_is_bid_info", "UnitTagTable::util_vals_is_bid_ptr_null", "UnitTagTable::util_vals_is_cold_start", "UnitTagTable::util_vals_is_cost_target_msg", "UnitTagTable::util_vals_is_costcap_nobid", "UnitTagTable::util_vals_is_debug", "UnitTagTable::util_vals_is_debug_on", "UnitTagTable::util_vals_is_explore", "UnitTagTable::util_vals_is_fanstop", "UnitTagTable::util_vals_is_fanstop_ocpm", "UnitTagTable::util_vals_is_interval_cost", "UnitTagTable::util_vals_is_monitor", "UnitTagTable::util_vals_is_no_bid", "UnitTagTable::util_vals_is_normal", "UnitTagTable::util_vals_is_nothing_to_do", "UnitTagTable::util_vals_is_ocpm_bid_process", "UnitTagTable::util_vals_is_on_live", "UnitTagTable::util_vals_is_online", "UnitTagTable::util_vals_is_out_of_budget", "UnitTagTable::util_vals_is_price_ratio_bound", "UnitTagTable::util_vals_is_process", "UnitTagTable::util_vals_is_reset_context", "UnitTagTable::util_vals_is_roas", "UnitTagTable::util_vals_is_roas_sub_pacing", "UnitTagTable::util_vals_is_skip_cold_start", "UnitTagTable::util_vals_is_skip_update", "UnitTagTable::util_vals_is_start_process", "UnitTagTable::util_vals_is_status_open", "UnitTagTable::util_vals_is_sync_to_dsp", "UnitTagTable::util_vals_is_target_modify", "UnitTagTable::util_vals_is_update_adjust", "UnitTagTable::util_vals_is_update_interval_ms", "UnitTagTable::util_vals_item_type_num", "UnitTagTable::util_vals_new_adjust_auto_value_rate", "UnitTagTable::util_vals_old_adjust_auto_value_rate", "UnitTagTable::util_vals_old_is_out_of_budget", "UnitTagTable::util_vals_p_val", "UnitTagTable::util_vals_p_val_conv", "UnitTagTable::util_vals_p_val_cost", "UnitTagTable::util_vals_p_weight", "UnitTagTable::util_vals_p_weight_conv", "UnitTagTable::util_vals_p_weight_cost", "UnitTagTable::util_vals_pacing_method_type", "UnitTagTable::util_vals_pacing_type", "UnitTagTable::util_vals_pacing_weight", "UnitTagTable::util_vals_pacing_weight_conv", "UnitTagTable::util_vals_pacing_weight_cost", "UnitTagTable::util_vals_prior_cost", "UnitTagTable::util_vals_pro_ocpc_not_been_set", "UnitTagTable::util_vals_promotion_type_num", "UnitTagTable::util_vals_reset_pacing", "UnitTagTable::util_vals_rt_cost_speed", "UnitTagTable::util_vals_rt_delivery_speed", "UnitTagTable::util_vals_start_bid_rate", "UnitTagTable::util_vals_start_bid_rate_sub_pacing", "UnitTagTable::util_vals_step_bid_rate_descent", "UnitTagTable::util_vals_step_bid_rate_increase", "UnitTagTable::util_vals_target_cost_speed", "UnitTagTable::util_vals_target_delivery_speed", "UnitTagTable::util_vals_target_modify_ratio", "UnitTagTable::util_vals_target_roas", "UnitTagTable::util_vals_thread_id", "UnitTagTable::util_vals_total_pred_conv", "UnitTagTable::util_vals_unit_id", "UnitTagTable::util_vals_update_interval_ms"], "$input_item_attrs": ["TriggerTable::event_server_timestamp", "TriggerTable::message_seq", "UnitTagTable::bid_state_info_account_id", "UnitTagTable::bid_state_info_account_type", "UnitTagTable::bid_state_info_ad_status", "UnitTagTable::bid_state_info_advertisable", "UnitTagTable::bid_state_info_author_id", "UnitTagTable::bid_state_info_bid_strategy", "UnitTagTable::bid_state_info_bid_type", "UnitTagTable::bid_state_info_budget", "UnitTagTable::bid_state_info_campaign_id", "UnitTagTable::bid_state_info_campaign_type", "UnitTagTable::bid_state_info_cpa_bid", "UnitTagTable::bid_state_info_explore_budget", "UnitTagTable::bid_state_info_explore_budget_start_time", "UnitTagTable::bid_state_info_explore_budget_status", "UnitTagTable::bid_state_info_explore_time_period", "UnitTagTable::bid_state_info_is_live", "UnitTagTable::bid_state_info_is_status_open", "UnitTagTable::bid_state_info_left_budget", "UnitTagTable::bid_state_info_live_launch_type", "UnitTagTable::bid_state_info_live_stream_id", "UnitTagTable::bid_state_info_online", "UnitTagTable::bid_state_info_promotion_type", "UnitTagTable::bid_state_info_ptr", "UnitTagTable::bid_state_info_roi_ratio", "UnitTagTable::bid_state_info_speed_type", "UnitTagTable::has_bid_state_info_ptr"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerloopOcpmBidContextAccumulateMixer"}, "inner_loop_table_lite_mixer_C0E5EA": {"item_table": "TriggerTable", "skip_wait_index": false, "cache_key_id": {"item_table": "TriggerTable", "column": "unit_id"}, "unit_id": {"item_table": "TriggerTable", "column": "unit_id"}, "group_tag": {"item_table": "TriggerTable", "column": "inner_group_tag"}, "has_bid_state_info_ptr": {"item_table": "UnitTagTable", "column": "has_bid_state_info_ptr"}, "bid_state_info_ptr": {"item_table": "UnitTagTable", "column": "bid_state_info_ptr"}, "bid_state_info_explore_time_period": {"item_table": "UnitTagTable", "column": "bid_state_info_explore_time_period"}, "bid_state_info_explore_budget_start_time": {"item_table": "UnitTagTable", "column": "bid_state_info_explore_budget_start_time"}, "bid_state_info_explore_budget_status": {"item_table": "UnitTagTable", "column": "bid_state_info_explore_budget_status"}, "bid_state_info_explore_budget": {"item_table": "UnitTagTable", "column": "bid_state_info_explore_budget"}, "bid_state_info_is_live": {"item_table": "UnitTagTable", "column": "bid_state_info_is_live"}, "bid_state_info_ad_status": {"item_table": "UnitTagTable", "column": "bid_state_info_ad_status"}, "bid_state_info_advertisable": {"item_table": "UnitTagTable", "column": "bid_state_info_advertisable"}, "bid_state_info_is_status_open": {"item_table": "UnitTagTable", "column": "bid_state_info_is_status_open"}, "bid_state_info_online": {"item_table": "UnitTagTable", "column": "bid_state_info_online"}, "bid_state_info_left_budget": {"item_table": "UnitTagTable", "column": "bid_state_info_left_budget"}, "bid_state_info_budget": {"item_table": "UnitTagTable", "column": "bid_state_info_budget"}, "bid_state_info_account_type": {"item_table": "UnitTagTable", "column": "bid_state_info_account_type"}, "bid_state_info_bid_strategy": {"item_table": "UnitTagTable", "column": "bid_state_info_bid_strategy"}, "bid_state_info_bid_type": {"item_table": "UnitTagTable", "column": "bid_state_info_bid_type"}, "bid_state_info_live_launch_type": {"item_table": "UnitTagTable", "column": "bid_state_info_live_launch_type"}, "bid_state_info_account_id": {"item_table": "UnitTagTable", "column": "bid_state_info_account_id"}, "bid_state_info_campaign_id": {"item_table": "UnitTagTable", "column": "bid_state_info_campaign_id"}, "bid_state_info_author_id": {"item_table": "UnitTagTable", "column": "bid_state_info_author_id"}, "bid_state_info_live_stream_id": {"item_table": "UnitTagTable", "column": "bid_state_info_live_stream_id"}, "bid_state_info_speed_type": {"item_table": "UnitTagTable", "column": "bid_state_info_speed_type"}, "bid_state_info_promotion_type": {"item_table": "UnitTagTable", "column": "bid_state_info_promotion_type"}, "bid_state_info_campaign_type": {"item_table": "UnitTagTable", "column": "bid_state_info_campaign_type"}, "bid_state_info_cpa_bid": {"item_table": "UnitTagTable", "column": "bid_state_info_cpa_bid"}, "bid_state_info_roi_ratio": {"item_table": "UnitTagTable", "column": "bid_state_info_roi_ratio"}, "$metadata": {"$output_item_attrs": ["UnitTagTable::bid_state_info_account_id", "UnitTagTable::bid_state_info_ad_status", "UnitTagTable::bid_state_info_advertisable", "UnitTagTable::bid_state_info_author_id", "UnitTagTable::bid_state_info_budget", "UnitTagTable::bid_state_info_campaign_id", "UnitTagTable::bid_state_info_campaign_type", "UnitTagTable::bid_state_info_cpa_bid", "UnitTagTable::bid_state_info_explore_budget", "UnitTagTable::bid_state_info_explore_budget_start_time", "UnitTagTable::bid_state_info_explore_budget_status", "UnitTagTable::bid_state_info_explore_time_period", "UnitTagTable::bid_state_info_is_live", "UnitTagTable::bid_state_info_is_status_open", "UnitTagTable::bid_state_info_left_budget", "UnitTagTable::bid_state_info_live_stream_id", "UnitTagTable::bid_state_info_online", "UnitTagTable::bid_state_info_promotion_type", "UnitTagTable::bid_state_info_roi_ratio", "UnitTagTable::bid_state_info_speed_type", "UnitTagTable::has_bid_state_info_ptr"], "$input_item_attrs": ["group_tag", "unit_id"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopTableLiteMixer"}, "inner_loop_strategy_data_fetch_mixer_7342D7": {"item_table": "TriggerTable", "cluster_name": "BidServerGraphInner", "candidate_cluster_name": "BidServerGraphTest", "key_prefix": "inner_ctx_:", "output_table_name": "UnitTagTable", "local_cache_key_id": {"item_table": "UnitTagTable", "column": "local_cache_key_id"}, "cache_key_id": {"item_table": "TriggerTable", "column": "unit_id"}, "group_tag": {"item_table": "TriggerTable", "column": "inner_group_tag"}, "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopStrategyDataFetchMixer"}, "message_queue_retriever_E2E0AF": {"max_queue_size": 10000, "queue_number": 200, "message_queue_type": 0, "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "MessageQueueRetriever"}, "_branch_controller_EC717578": {"import_common_attr": ["skip_step_two"], "export_common_attr": ["_if_control_attr_6"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (skip_step_two == 1) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_EC717578", "$code_info": "[if] A37B6ADF ad_log_inner_flow.py:1621 in account_ocpm_step_two_workflow(): self.if_(\"skip_step_two == 1\")", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["skip_step_two"], "$output_common_attrs": ["_if_control_attr_6"], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "return__EDCBDA": {"status_code": 0, "skip": "{{_if_control_attr_6}}", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["_if_control_attr_6"], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoExecutionStatusEnricher"}, "inner_loop_ocpm_trigger_mixer_764052": {"item_table": "AccountTagTable", "key_id": {"item_table": "AccountTagTable", "column": "account_id"}, "key_type": 1, "$metadata": {"$output_item_attrs": [], "$input_item_attrs": ["AccountTagTable::account_id"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopTriggerMixer"}, "ad_data_frame_write_to_redis_observer_AC866C": {"enable_switch": true, "tables": [{"table_name": "AccountTagTable", "redis_name": "BidServerGraphInner", "candidate_redis_name": "BidServerGraphTest", "prefix": "inner_account_ctx_:", "ttl": 172800, "save_all_columns": true}], "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "AdDataFrameWriteToRedisObserver"}, "inner_loop_strategy_data_save_mixer_6F5C52": {"tables": [{"table_name": "AccountTagTable", "table_category": "AccountTagTable", "save_all_columns": true, "local_cache_key_id": "local_cache_key_id"}], "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopStrategyDataSaveMixer"}, "inner_loop_step_one_to_redis_mixer_615C81": {"item_table": "UnitTagTable", "redis_name": "adEngineFlowProcess", "is_step_two": false, "ttl": 86400, "has_bid_state_info_ptr": {"item_table": "TriggerTable", "column": "has_bid_state_info_ptr"}, "bid_state_info_ptr": {"item_table": "TriggerTable", "column": "bid_state_info_ptr"}, "message_seq": {"item_table": "TriggerTable", "column": "message_seq"}, "event_server_timestamp": {"item_table": "TriggerTable", "column": "event_server_timestamp"}, "unit_id": {"item_table": "UnitTagTable", "column": "unit_id"}, "msg_attr_info": {"msg_campaign_type": {"item_table": "TriggerTable", "column": "campaign_type"}, "msg_ocpc_action_type": {"item_table": "TriggerTable", "column": "ocpc_action_type"}, "msg_promotion_type": {"item_table": "TriggerTable", "column": "promotion_type"}, "msg_item_type": {"item_table": "TriggerTable", "column": "item_type"}, "msg_author_id": {"item_table": "TriggerTable", "column": "author_id"}, "msg_account_id": {"item_table": "TriggerTable", "column": "account_id"}, "msg_live_stream_id": {"item_table": "TriggerTable", "column": "live_stream_id"}, "msg_unit_id": {"item_table": "TriggerTable", "column": "unit_id"}, "msg_conv_num": {"item_table": "TriggerTable", "column": "conv_num"}, "msg_campaign_id": {"item_table": "TriggerTable", "column": "campaign_id"}, "msg_item_type_num": {"item_table": "TriggerTable", "column": "item_type_num"}, "msg_cost": {"item_table": "TriggerTable", "column": "cost"}, "msg_gmv": {"item_table": "TriggerTable", "column": "gmv"}, "msg_roi_ratio": {"item_table": "TriggerTable", "column": "roi_ratio"}, "msg_action_type": {"item_table": "TriggerTable", "column": "action_type"}, "msg_group_tag": {"item_table": "TriggerTable", "column": "inner_group_tag"}, "msg_bid_type": {"item_table": "TriggerTable", "column": "bid_type"}, "msg_medium_attribute": {"item_table": "TriggerTable", "column": "medium_attribute"}, "msg_speed_type": {"item_table": "TriggerTable", "column": "speed_type"}, "msg_delivery_timestamp": {"item_table": "TriggerTable", "column": "delivery_timestamp"}, "msg_cpa_bid": {"item_table": "TriggerTable", "column": "cpa_bid"}, "msg_is_soft": {"item_table": "TriggerTable", "column": "is_soft"}, "msg_target_cost": {"item_table": "TriggerTable", "column": "target_cost"}, "msg_target_gmv": {"item_table": "TriggerTable", "column": "target_gmv"}, "msg_separate_gsp_price": {"item_table": "TriggerTable", "column": "separate_gsp_price"}, "msg_record_gsp_price": {"item_table": "TriggerTable", "column": "record_gsp_price"}, "msg_pred_conv": {"item_table": "TriggerTable", "column": "pred_conv"}, "msg_pred_cvr_sum": {"item_table": "TriggerTable", "column": "pred_cvr_sum"}, "msg_pred_ctr_sum": {"item_table": "TriggerTable", "column": "pred_ctr_sum"}, "msg_price_before_billing_separate": {"item_table": "TriggerTable", "column": "price_before_billing_separate"}, "msg_price_after_billing_separate": {"item_table": "TriggerTable", "column": "price_after_billing_separate"}, "msg_ecpm": {"item_table": "TriggerTable", "column": "ecpm"}, "msg_auction_bid": {"item_table": "TriggerTable", "column": "auction_bid"}, "msg_price_ratio": {"item_table": "TriggerTable", "column": "price_ratio"}, "msg_is_store_wide_roi_reco_conv": {"item_table": "TriggerTable", "column": "is_store_wide_roi_reco_conv"}}, "bid_ctx_attr_info": {"bid_ctx_bid_context_key": {"item_table": "AccountTagTable", "column": "bid_context_key"}, "bid_ctx_group_tag": {"item_table": "AccountTagTable", "column": "group_tag"}, "bid_ctx_campaign_type": {"item_table": "AccountTagTable", "column": "campaign_type"}, "bid_ctx_ocpc_action_type": {"item_table": "AccountTagTable", "column": "ocpc_action_type"}, "bid_ctx_promotion_type": {"item_table": "AccountTagTable", "column": "promotion_type"}, "bid_ctx_item_type": {"item_table": "AccountTagTable", "column": "item_type"}, "bid_ctx_first_industry_name": {"item_table": "AccountTagTable", "column": "first_industry_name"}, "bid_ctx_group_tag_num": {"item_table": "AccountTagTable", "column": "group_tag_num"}, "bid_ctx_unit_id": {"item_table": "AccountTagTable", "column": "unit_id"}, "bid_ctx_account_id": {"item_table": "AccountTagTable", "column": "account_id"}, "bid_ctx_author_id": {"item_table": "AccountTagTable", "column": "author_id"}, "bid_ctx_live_stream_id": {"item_table": "AccountTagTable", "column": "live_stream_id"}, "bid_ctx_item_type_num": {"item_table": "AccountTagTable", "column": "item_type_num"}, "bid_ctx_campaign_id": {"item_table": "AccountTagTable", "column": "campaign_id"}, "bid_ctx_price_before_billing_separate": {"item_table": "AccountTagTable", "column": "price_before_billing_separate"}, "bid_ctx_price_after_billing_separate": {"item_table": "AccountTagTable", "column": "price_after_billing_separate"}, "bid_ctx_cost": {"item_table": "AccountTagTable", "column": "cost"}, "bid_ctx_gmv": {"item_table": "AccountTagTable", "column": "gmv"}, "bid_ctx_conv_num": {"item_table": "AccountTagTable", "column": "conv_num"}, "bid_ctx_cost_count": {"item_table": "AccountTagTable", "column": "cost_count"}, "bid_ctx_gmv_count": {"item_table": "AccountTagTable", "column": "gmv_count"}, "bid_ctx_delivery_cnt": {"item_table": "AccountTagTable", "column": "delivery_cnt"}, "bid_ctx_cpa_bid": {"item_table": "AccountTagTable", "column": "cpa_bid"}, "bid_ctx_relax_cpa_bid": {"item_table": "AccountTagTable", "column": "relax_cpa_bid"}, "bid_ctx_roi_ratio": {"item_table": "AccountTagTable", "column": "roi_ratio"}, "bid_ctx_relax_roi_ratio": {"item_table": "AccountTagTable", "column": "relax_roi_ratio"}, "bid_ctx_rt_cpa_bid": {"item_table": "AccountTagTable", "column": "rt_cpa_bid"}, "bid_ctx_rt_roas": {"item_table": "AccountTagTable", "column": "rt_roas"}, "bid_ctx_auto_cpa_bid": {"item_table": "AccountTagTable", "column": "auto_cpa_bid"}, "bid_ctx_auto_roi_ratio": {"item_table": "AccountTagTable", "column": "auto_roi_ratio"}, "bid_ctx_old_auto_cpa_bid": {"item_table": "AccountTagTable", "column": "old_auto_cpa_bid"}, "bid_ctx_old_auto_roi_ratio": {"item_table": "AccountTagTable", "column": "old_auto_roi_ratio"}, "bid_ctx_adjust_rate": {"item_table": "AccountTagTable", "column": "adjust_rate"}, "bid_ctx_rt_cost_speed": {"item_table": "AccountTagTable", "column": "rt_cost_speed"}, "bid_ctx_rt_delivery_speed": {"item_table": "AccountTagTable", "column": "rt_delivery_speed"}, "bid_ctx_dynamic_lower_bound_roas": {"item_table": "AccountTagTable", "column": "dynamic_lower_bound_roas"}, "bid_ctx_dynamic_upper_bound_roas": {"item_table": "AccountTagTable", "column": "dynamic_upper_bound_roas"}, "bid_ctx_dynamic_lower_bound_cpa_bid": {"item_table": "AccountTagTable", "column": "dynamic_lower_bound_cpa_bid"}, "bid_ctx_dynamic_upper_bound_cpa_bid": {"item_table": "AccountTagTable", "column": "dynamic_upper_bound_cpa_bid"}, "bid_ctx_target_cost": {"item_table": "AccountTagTable", "column": "target_cost"}, "bid_ctx_target_gmv": {"item_table": "AccountTagTable", "column": "target_gmv"}, "bid_ctx_record_gsp_price": {"item_table": "AccountTagTable", "column": "record_gsp_price"}, "bid_ctx_last_update_cost": {"item_table": "AccountTagTable", "column": "last_update_cost"}, "bid_ctx_adjust_auto_atv_rate": {"item_table": "AccountTagTable", "column": "adjust_auto_atv_rate"}, "bid_ctx_old_adjust_auto_atv_rate": {"item_table": "AccountTagTable", "column": "old_adjust_auto_atv_rate"}, "bid_ctx_reach_lower_bound_timestamp": {"item_table": "AccountTagTable", "column": "reach_lower_bound_timestamp"}, "bid_ctx_reach_upper_bound_timestamp": {"item_table": "AccountTagTable", "column": "reach_upper_bound_timestamp"}, "bid_ctx_first_delivery_timestamp_ms": {"item_table": "AccountTagTable", "column": "first_delivery_timestamp_ms"}, "bid_ctx_last_delivery_timestamp_ms": {"item_table": "AccountTagTable", "column": "last_delivery_timestamp_ms"}, "bid_ctx_last_update_adjust_timestamp": {"item_table": "AccountTagTable", "column": "last_update_adjust_timestamp"}, "bid_ctx_cost_start_timestamp_ms": {"item_table": "AccountTagTable", "column": "cost_start_timestamp_ms"}, "bid_ctx_last_update_context_timestamp": {"item_table": "AccountTagTable", "column": "last_update_context_timestamp"}, "bid_ctx_last_sync_context_timestamp": {"item_table": "AccountTagTable", "column": "last_sync_context_timestamp"}, "bid_ctx_online": {"item_table": "AccountTagTable", "column": "online"}, "bid_ctx_is_cold_start": {"item_table": "AccountTagTable", "column": "is_cold_start"}, "bid_ctx_speed_type": {"item_table": "AccountTagTable", "column": "speed_type"}, "bid_ctx_context_start_timestamp": {"item_table": "AccountTagTable", "column": "context_start_timestamp"}, "bid_ctx_last_sync_result_timestamp": {"item_table": "AccountTagTable", "column": "last_sync_result_timestamp"}, "bid_ctx_adjust_auto_value_rate": {"item_table": "AccountTagTable", "column": "adjust_auto_value_rate"}, "bid_ctx_total_ecpm": {"item_table": "AccountTagTable", "column": "total_ecpm"}, "bid_ctx_separate_gsp_price": {"item_table": "AccountTagTable", "column": "separate_gsp_price"}, "bid_ctx_total_auction_bid": {"item_table": "AccountTagTable", "column": "total_auction_bid"}, "bid_ctx_dry_up_base_value": {"item_table": "AccountTagTable", "column": "dry_up_base_value"}, "bid_ctx_ctr_sum": {"item_table": "AccountTagTable", "column": "ctr_sum"}, "bid_ctx_cvr_sum": {"item_table": "AccountTagTable", "column": "cvr_sum"}, "bid_ctx_softad_cost": {"item_table": "AccountTagTable", "column": "softad_cost"}, "bid_ctx_softad_target_cost": {"item_table": "AccountTagTable", "column": "softad_target_cost"}, "bid_ctx_softad_diff_ratio": {"item_table": "AccountTagTable", "column": "softad_diff_ratio"}, "bid_ctx_last_monitor_timestamp": {"item_table": "AccountTagTable", "column": "last_monitor_timestamp"}, "bid_ctx_user_cost_prior_algo": {"item_table": "AccountTagTable", "column": "user_cost_prior_algo"}, "bid_ctx_ad_status_tag": {"item_table": "AccountTagTable", "column": "ad_status_tag"}, "bid_ctx_old_is_out_of_budget": {"item_table": "AccountTagTable", "column": "old_is_out_of_budget"}, "bid_ctx_last_is_ad_open": {"item_table": "AccountTagTable", "column": "last_is_ad_open"}, "bid_ctx_last_ad_valid": {"item_table": "AccountTagTable", "column": "last_ad_valid"}, "bid_ctx_sync_context_interval_batch_batch_cost": {"item_table": "AccountTagTable", "column": "sync_context_interval_batch_batch_cost"}, "bid_ctx_sync_context_interval_batch_batch_gmv": {"item_table": "AccountTagTable", "column": "sync_context_interval_batch_batch_gmv"}, "bid_ctx_sync_context_interval_batch_batch_start_timestamp": {"item_table": "AccountTagTable", "column": "sync_context_interval_batch_batch_start_timestamp"}, "bid_ctx_cost_speed_context_batch_start_timestamp_ms": {"item_table": "AccountTagTable", "column": "cost_speed_context_batch_start_timestamp_ms"}, "bid_ctx_cost_speed_context_batch_value_sum": {"item_table": "AccountTagTable", "column": "cost_speed_context_batch_value_sum"}, "bid_ctx_cost_speed_context_batch_count": {"item_table": "AccountTagTable", "column": "cost_speed_context_batch_count"}, "bid_ctx_cost_speed_context_batch_value_mean": {"item_table": "AccountTagTable", "column": "cost_speed_context_batch_value_mean"}, "bid_ctx_cost_speed_context_batch_value_speed": {"item_table": "AccountTagTable", "column": "cost_speed_context_batch_value_speed"}, "bid_ctx_delivery_speed_context_batch_start_timestamp_ms": {"item_table": "AccountTagTable", "column": "delivery_speed_context_batch_start_timestamp_ms"}, "bid_ctx_delivery_speed_context_batch_value_sum": {"item_table": "AccountTagTable", "column": "delivery_speed_context_batch_value_sum"}, "bid_ctx_delivery_speed_context_batch_count": {"item_table": "AccountTagTable", "column": "delivery_speed_context_batch_count"}, "bid_ctx_delivery_speed_context_batch_value_mean": {"item_table": "AccountTagTable", "column": "delivery_speed_context_batch_value_mean"}, "bid_ctx_delivery_speed_context_batch_value_speed": {"item_table": "AccountTagTable", "column": "delivery_speed_context_batch_value_speed"}, "bid_ctx_conv_ratio_adjust_context_pred_conv": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_pred_conv"}, "bid_ctx_conv_ratio_adjust_context_old_auto_conv_ratio": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_old_auto_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_auto_conv_ratio": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_auto_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_real_conv": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_real_conv"}, "bid_ctx_conv_ratio_adjust_context_last_update_conv_ratio_timestamp": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_last_update_conv_ratio_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_r1_upper_bound_timestamp": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_reach_r1_upper_bound_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_r1_lower_bound_timestamp": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_reach_r1_lower_bound_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp"}, "bid_ctx_price_ratio_context_batch_start_timestamp_ms": {"item_table": "AccountTagTable", "column": "price_ratio_context_batch_start_timestamp_ms"}, "bid_ctx_price_ratio_context_batch_total_price": {"item_table": "AccountTagTable", "column": "price_ratio_context_batch_total_price"}, "bid_ctx_price_ratio_context_batch_total_ecpm": {"item_table": "AccountTagTable", "column": "price_ratio_context_batch_total_ecpm"}, "bid_ctx_price_ratio_context_batch_total_price_ratio": {"item_table": "AccountTagTable", "column": "price_ratio_context_batch_total_price_ratio"}, "bid_ctx_price_ratio_context_batch_count": {"item_table": "AccountTagTable", "column": "price_ratio_context_batch_count"}, "bid_ctx_price_ratio_context_batch_avg_price_ratio": {"item_table": "AccountTagTable", "column": "price_ratio_context_batch_avg_price_ratio"}, "bid_ctx_price_ratio_context_batch_price_ratio_mean": {"item_table": "AccountTagTable", "column": "price_ratio_context_batch_price_ratio_mean"}, "bid_ctx_item_type_info_photo_to_live_cnt": {"item_table": "AccountTagTable", "column": "item_type_info_photo_to_live_cnt"}, "bid_ctx_item_type_info_direct_live_cnt": {"item_table": "AccountTagTable", "column": "item_type_info_direct_live_cnt"}, "bid_ctx_item_type_info_photo_to_live_cost": {"item_table": "AccountTagTable", "column": "item_type_info_photo_to_live_cost"}, "bid_ctx_item_type_info_direct_live_cost": {"item_table": "AccountTagTable", "column": "item_type_info_direct_live_cost"}}, "process_utils_attr_info": {"process_utils_ad_off_target_cost": {"item_table": "AccountTagTable", "column": "util_vals_ad_off_target_cost"}, "process_utils_new_adjust_auto_value_rate": {"item_table": "AccountTagTable", "column": "util_vals_new_adjust_auto_value_rate"}, "process_utils_old_adjust_auto_value_rate": {"item_table": "AccountTagTable", "column": "util_vals_old_adjust_auto_value_rate"}, "process_utils_is_bid_ptr_null": {"item_table": "AccountTagTable", "column": "util_vals_is_bid_ptr_null"}, "process_utils_pro_ocpc_not_been_set": {"item_table": "AccountTagTable", "column": "util_vals_pro_ocpc_not_been_set"}, "process_utils_reset_pacing": {"item_table": "AccountTagTable", "column": "util_vals_reset_pacing"}, "process_utils_is_cost_target_msg": {"item_table": "AccountTagTable", "column": "util_vals_is_cost_target_msg"}, "process_utils_enable_cost_prior_algo": {"item_table": "AccountTagTable", "column": "util_vals_enable_cost_prior_algo"}, "process_utils_enable_linear_pacing_cold_start_low_bound": {"item_table": "AccountTagTable", "column": "util_vals_enable_linear_pacing_cold_start_low_bound"}, "process_utils_enable_linear_pacing_grid_correction": {"item_table": "AccountTagTable", "column": "util_vals_enable_linear_pacing_grid_correction"}, "process_utils_enable_linear_adaptive_update_time": {"item_table": "AccountTagTable", "column": "util_vals_enable_linear_adaptive_update_time"}, "process_utils_enable_linear_after_cold_start_no_conv_drop": {"item_table": "AccountTagTable", "column": "util_vals_enable_linear_after_cold_start_no_conv_drop"}, "process_utils_disable_day_reset_exp": {"item_table": "AccountTagTable", "column": "util_vals_disable_day_reset_exp"}, "process_utils_enable_univ_inner_ad_delivery_replace": {"item_table": "AccountTagTable", "column": "util_vals_enable_univ_inner_ad_delivery_replace"}, "process_utils_is_apply_adjust": {"item_table": "AccountTagTable", "column": "util_vals_is_apply_adjust"}, "process_utils_is_debug": {"item_table": "AccountTagTable", "column": "util_vals_is_debug"}, "process_utils_is_roas_sub_pacing": {"item_table": "AccountTagTable", "column": "util_vals_is_roas_sub_pacing"}, "process_utils_is_ocpm_bid_process": {"item_table": "AccountTagTable", "column": "util_vals_is_ocpm_bid_process"}, "process_utils_is_apply_operation_config": {"item_table": "AccountTagTable", "column": "util_vals_is_apply_operation_config"}, "process_utils_is_start_process": {"item_table": "AccountTagTable", "column": "util_vals_is_start_process"}, "process_utils_is_debug_on": {"item_table": "AccountTagTable", "column": "util_vals_is_debug_on"}, "process_utils_is_normal": {"item_table": "AccountTagTable", "column": "util_vals_is_normal"}, "process_utils_is_fanstop": {"item_table": "AccountTagTable", "column": "util_vals_is_fanstop"}, "process_utils_is_fanstop_ocpm": {"item_table": "AccountTagTable", "column": "util_vals_is_fanstop_ocpm"}, "process_utils_is_roas": {"item_table": "AccountTagTable", "column": "util_vals_is_roas"}, "process_utils_is_no_bid": {"item_table": "AccountTagTable", "column": "util_vals_is_no_bid"}, "process_utils_is_skip_update": {"item_table": "AccountTagTable", "column": "util_vals_is_skip_update"}, "process_utils_is_nothing_to_do": {"item_table": "AccountTagTable", "column": "util_vals_is_nothing_to_do"}, "process_utils_is_process": {"item_table": "AccountTagTable", "column": "util_vals_is_process"}, "process_utils_is_reset_context": {"item_table": "AccountTagTable", "column": "util_vals_is_reset_context"}, "process_utils_is_update_adjust": {"item_table": "AccountTagTable", "column": "util_vals_is_update_adjust"}, "process_utils_is_interval_cost": {"item_table": "AccountTagTable", "column": "util_vals_is_interval_cost"}, "process_utils_is_update_interval_ms": {"item_table": "AccountTagTable", "column": "util_vals_is_update_interval_ms"}, "process_utils_is_ad_open": {"item_table": "AccountTagTable", "column": "util_vals_is_ad_open"}, "process_utils_is_sync_to_dsp": {"item_table": "AccountTagTable", "column": "util_vals_is_sync_to_dsp"}, "process_utils_is_monitor": {"item_table": "AccountTagTable", "column": "util_vals_is_monitor"}, "process_utils_is_cold_start": {"item_table": "AccountTagTable", "column": "util_vals_is_cold_start"}, "process_utils_is_costcap_nobid": {"item_table": "AccountTagTable", "column": "util_vals_is_costcap_nobid"}, "process_utils_is_skip_cold_start": {"item_table": "AccountTagTable", "column": "util_vals_is_skip_cold_start"}, "process_utils_is_on_live": {"item_table": "AccountTagTable", "column": "util_vals_is_on_live"}, "process_utils_is_out_of_budget": {"item_table": "AccountTagTable", "column": "util_vals_is_out_of_budget"}, "process_utils_old_is_out_of_budget": {"item_table": "AccountTagTable", "column": "util_vals_old_is_out_of_budget"}, "process_utils_advertisable": {"item_table": "AccountTagTable", "column": "util_vals_advertisable"}, "process_utils_is_status_open": {"item_table": "AccountTagTable", "column": "util_vals_is_status_open"}, "process_utils_ad_status": {"item_table": "AccountTagTable", "column": "util_vals_ad_status"}, "process_utils_unit_id": {"item_table": "AccountTagTable", "column": "util_vals_unit_id"}, "process_utils_thread_id": {"item_table": "AccountTagTable", "column": "util_vals_thread_id"}, "process_utils_is_bid_info": {"item_table": "AccountTagTable", "column": "util_vals_is_bid_info"}, "process_utils_is_online": {"item_table": "AccountTagTable", "column": "util_vals_is_online"}, "process_utils_is_explore": {"item_table": "AccountTagTable", "column": "util_vals_is_explore"}, "process_utils_ab_exp_ratio": {"item_table": "AccountTagTable", "column": "util_vals_ab_exp_ratio"}, "process_utils_promotion_type_num": {"item_table": "AccountTagTable", "column": "util_vals_promotion_type_num"}, "process_utils_item_type_num": {"item_table": "AccountTagTable", "column": "util_vals_item_type_num"}, "process_utils_prior_cost": {"item_table": "AccountTagTable", "column": "util_vals_prior_cost"}, "process_utils_cold_start_cost_li_thr": {"item_table": "AccountTagTable", "column": "util_vals_cold_start_cost_li_thr"}, "process_utils_cold_start_low_bound": {"item_table": "AccountTagTable", "column": "util_vals_cold_start_low_bound"}, "process_utils_rt_cost_speed": {"item_table": "AccountTagTable", "column": "util_vals_rt_cost_speed"}, "process_utils_rt_delivery_speed": {"item_table": "AccountTagTable", "column": "util_vals_rt_delivery_speed"}, "process_utils_target_roas": {"item_table": "AccountTagTable", "column": "util_vals_target_roas"}, "process_utils_is_price_ratio_bound": {"item_table": "AccountTagTable", "column": "util_vals_is_price_ratio_bound"}, "process_utils_is_acc_explore_bid": {"item_table": "AccountTagTable", "column": "util_vals_is_acc_explore_bid"}, "process_utils_dynamic_bid_lower_bound_adjust_time_interval_ms": {"item_table": "AccountTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms"}, "process_utils_dynamic_bid_upper_bound_adjust_time_interval_ms": {"item_table": "AccountTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms"}, "process_utils_dynamic_bid_lower_bound_adjust_rate": {"item_table": "AccountTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_rate"}, "process_utils_dynamic_bid_upper_bound_adjust_rate": {"item_table": "AccountTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_rate"}, "process_utils_p_weight": {"item_table": "AccountTagTable", "column": "util_vals_p_weight"}, "process_utils_i_weight": {"item_table": "AccountTagTable", "column": "util_vals_i_weight"}, "process_utils_d_weight": {"item_table": "AccountTagTable", "column": "util_vals_d_weight"}, "process_utils_explore_p_weight": {"item_table": "AccountTagTable", "column": "util_vals_explore_p_weight"}, "process_utils_explore_i_weight": {"item_table": "AccountTagTable", "column": "util_vals_explore_i_weight"}, "process_utils_explore_d_weight": {"item_table": "AccountTagTable", "column": "util_vals_explore_d_weight"}, "process_utils_p_val": {"item_table": "AccountTagTable", "column": "util_vals_p_val"}, "process_utils_i_val": {"item_table": "AccountTagTable", "column": "util_vals_i_val"}, "process_utils_d_val": {"item_table": "AccountTagTable", "column": "util_vals_d_val"}, "process_utils_pacing_weight": {"item_table": "AccountTagTable", "column": "util_vals_pacing_weight"}, "process_utils_target_cost_speed": {"item_table": "AccountTagTable", "column": "util_vals_target_cost_speed"}, "process_utils_target_delivery_speed": {"item_table": "AccountTagTable", "column": "util_vals_target_delivery_speed"}, "process_utils_start_bid_rate": {"item_table": "AccountTagTable", "column": "util_vals_start_bid_rate"}, "process_utils_start_bid_rate_sub_pacing": {"item_table": "AccountTagTable", "column": "util_vals_start_bid_rate_sub_pacing"}, "process_utils_hard_upper_bound_auto_cpa_bid": {"item_table": "AccountTagTable", "column": "util_vals_hard_upper_bound_auto_cpa_bid"}, "process_utils_hard_lower_bound_auto_cpa_bid": {"item_table": "AccountTagTable", "column": "util_vals_hard_lower_bound_auto_cpa_bid"}, "process_utils_hard_lower_bound_auto_roas": {"item_table": "AccountTagTable", "column": "util_vals_hard_lower_bound_auto_roas"}, "process_utils_hard_upper_bound_auto_roas": {"item_table": "AccountTagTable", "column": "util_vals_hard_upper_bound_auto_roas"}, "process_utils_hard_lower_bound_auto_atv": {"item_table": "AccountTagTable", "column": "util_vals_hard_lower_bound_auto_atv"}, "process_utils_hard_upper_bound_auto_atv": {"item_table": "AccountTagTable", "column": "util_vals_hard_upper_bound_auto_atv"}, "process_utils_acc_explore_bid_target_cost_speed": {"item_table": "AccountTagTable", "column": "util_vals_acc_explore_bid_target_cost_speed"}, "process_utils_current_ms": {"item_table": "AccountTagTable", "column": "util_vals_current_ms"}, "process_utils_ad_short_type": {"item_table": "AccountTagTable", "column": "util_vals_ad_short_type"}, "process_utils_is_target_modify": {"item_table": "AccountTagTable", "column": "util_vals_is_target_modify"}, "process_utils_target_modify_ratio": {"item_table": "AccountTagTable", "column": "util_vals_target_modify_ratio"}, "process_utils_p_weight_conv": {"item_table": "AccountTagTable", "column": "util_vals_p_weight_conv"}, "process_utils_i_weight_conv": {"item_table": "AccountTagTable", "column": "util_vals_i_weight_conv"}, "process_utils_d_weight_conv": {"item_table": "AccountTagTable", "column": "util_vals_d_weight_conv"}, "process_utils_p_val_conv": {"item_table": "AccountTagTable", "column": "util_vals_p_val_conv"}, "process_utils_i_val_conv": {"item_table": "AccountTagTable", "column": "util_vals_i_val_conv"}, "process_utils_d_val_conv": {"item_table": "AccountTagTable", "column": "util_vals_d_val_conv"}, "process_utils_pacing_weight_conv": {"item_table": "AccountTagTable", "column": "util_vals_pacing_weight_conv"}, "process_utils_p_weight_cost": {"item_table": "AccountTagTable", "column": "util_vals_p_weight_cost"}, "process_utils_i_weight_cost": {"item_table": "AccountTagTable", "column": "util_vals_i_weight_cost"}, "process_utils_d_weight_cost": {"item_table": "AccountTagTable", "column": "util_vals_d_weight_cost"}, "process_utils_p_val_cost": {"item_table": "AccountTagTable", "column": "util_vals_p_val_cost"}, "process_utils_i_val_cost": {"item_table": "AccountTagTable", "column": "util_vals_i_val_cost"}, "process_utils_d_val_cost": {"item_table": "AccountTagTable", "column": "util_vals_d_val_cost"}, "process_utils_pacing_weight_cost": {"item_table": "AccountTagTable", "column": "util_vals_pacing_weight_cost"}, "process_utils_update_interval_ms": {"item_table": "AccountTagTable", "column": "util_vals_update_interval_ms"}, "process_utils_interval_cost": {"item_table": "AccountTagTable", "column": "util_vals_interval_cost"}, "process_utils_step_bid_rate_increase": {"item_table": "AccountTagTable", "column": "util_vals_step_bid_rate_increase"}, "process_utils_step_bid_rate_descent": {"item_table": "AccountTagTable", "column": "util_vals_step_bid_rate_descent"}, "process_utils_ad_status_tag": {"item_table": "AccountTagTable", "column": "util_vals_ad_status_tag"}, "process_utils_ad_status_tag_change": {"item_table": "AccountTagTable", "column": "util_vals_ad_status_tag_change"}, "process_utils_total_pred_conv": {"item_table": "AccountTagTable", "column": "util_vals_total_pred_conv"}, "process_utils_adjust_rate_lower_bound": {"item_table": "AccountTagTable", "column": "util_vals_adjust_rate_lower_bound"}, "process_utils_adjust_rate_upper_bound": {"item_table": "AccountTagTable", "column": "util_vals_adjust_rate_upper_bound"}, "process_utils_budget": {"item_table": "AccountTagTable", "column": "util_vals_budget"}, "process_utils_hard_bid_upper_bound_rate_monitor": {"item_table": "AccountTagTable", "column": "util_vals_hard_bid_upper_bound_rate_monitor"}, "process_utils_hard_bid_lower_bound_rate_monitor": {"item_table": "AccountTagTable", "column": "util_vals_hard_bid_lower_bound_rate_monitor"}, "process_utils_fanstop_adjust_rate_lower_bound_roi": {"item_table": "AccountTagTable", "column": "util_vals_fanstop_adjust_rate_lower_bound_roi"}, "process_utils_fanstop_adjust_rate_upper_bound_roi": {"item_table": "AccountTagTable", "column": "util_vals_fanstop_adjust_rate_upper_bound_roi"}, "process_utils_fanstop_adjust_rate_lower_bound_cpa": {"item_table": "AccountTagTable", "column": "util_vals_fanstop_adjust_rate_lower_bound_cpa"}, "process_utils_fanstop_adjust_rate_upper_bound_cpa": {"item_table": "AccountTagTable", "column": "util_vals_fanstop_adjust_rate_upper_bound_cpa"}, "process_utils_pacing_type": {"item_table": "AccountTagTable", "column": "util_vals_pacing_type"}, "process_utils_pacing_method_type": {"item_table": "AccountTagTable", "column": "util_vals_pacing_method_type"}}, "$metadata": {"$output_item_attrs": [], "$input_item_attrs": ["AccountTagTable::account_id", "AccountTagTable::ad_status_tag", "AccountTagTable::adjust_auto_atv_rate", "AccountTagTable::adjust_auto_value_rate", "AccountTagTable::adjust_rate", "AccountTagTable::author_id", "AccountTagTable::auto_cpa_bid", "AccountTagTable::auto_roi_ratio", "AccountTagTable::bid_context_key", "AccountTagTable::campaign_id", "AccountTagTable::campaign_type", "AccountTagTable::context_start_timestamp", "AccountTagTable::conv_num", "AccountTagTable::conv_ratio_adjust_context_auto_conv_ratio", "AccountTagTable::conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio", "AccountTagTable::conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio", "AccountTagTable::conv_ratio_adjust_context_last_update_conv_ratio_timestamp", "AccountTagTable::conv_ratio_adjust_context_old_auto_conv_ratio", "AccountTagTable::conv_ratio_adjust_context_pred_conv", "AccountTagTable::conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp", "AccountTagTable::conv_ratio_adjust_context_reach_r1_lower_bound_timestamp", "AccountTagTable::conv_ratio_adjust_context_reach_r1_upper_bound_timestamp", "AccountTagTable::conv_ratio_adjust_context_real_conv", "AccountTagTable::cost", "AccountTagTable::cost_count", "AccountTagTable::cost_speed_context_batch_count", "AccountTagTable::cost_speed_context_batch_start_timestamp_ms", "AccountTagTable::cost_speed_context_batch_value_mean", "AccountTagTable::cost_speed_context_batch_value_speed", "AccountTagTable::cost_speed_context_batch_value_sum", "AccountTagTable::cost_start_timestamp_ms", "AccountTagTable::cpa_bid", "AccountTagTable::ctr_sum", "AccountTagTable::cvr_sum", "AccountTagTable::delivery_cnt", "AccountTagTable::delivery_speed_context_batch_count", "AccountTagTable::delivery_speed_context_batch_start_timestamp_ms", "AccountTagTable::delivery_speed_context_batch_value_mean", "AccountTagTable::delivery_speed_context_batch_value_speed", "AccountTagTable::delivery_speed_context_batch_value_sum", "AccountTagTable::dry_up_base_value", "AccountTagTable::dynamic_lower_bound_cpa_bid", "AccountTagTable::dynamic_lower_bound_roas", "AccountTagTable::dynamic_upper_bound_cpa_bid", "AccountTagTable::dynamic_upper_bound_roas", "AccountTagTable::first_delivery_timestamp_ms", "AccountTagTable::first_industry_name", "AccountTagTable::gmv", "AccountTagTable::gmv_count", "AccountTagTable::group_tag", "AccountTagTable::group_tag_num", "AccountTagTable::is_cold_start", "AccountTagTable::item_type", "AccountTagTable::item_type_info_direct_live_cnt", "AccountTagTable::item_type_info_direct_live_cost", "AccountTagTable::item_type_info_photo_to_live_cnt", "AccountTagTable::item_type_info_photo_to_live_cost", "AccountTagTable::item_type_num", "AccountTagTable::last_ad_valid", "AccountTagTable::last_delivery_timestamp_ms", "AccountTagTable::last_is_ad_open", "AccountTagTable::last_monitor_timestamp", "AccountTagTable::last_sync_context_timestamp", "AccountTagTable::last_sync_result_timestamp", "AccountTagTable::last_update_adjust_timestamp", "AccountTagTable::last_update_context_timestamp", "AccountTagTable::last_update_cost", "AccountTagTable::live_stream_id", "AccountTagTable::ocpc_action_type", "AccountTagTable::old_adjust_auto_atv_rate", "AccountTagTable::old_auto_cpa_bid", "AccountTagTable::old_auto_roi_ratio", "AccountTagTable::old_is_out_of_budget", "AccountTagTable::online", "AccountTagTable::price_after_billing_separate", "AccountTagTable::price_before_billing_separate", "AccountTagTable::price_ratio_context_batch_avg_price_ratio", "AccountTagTable::price_ratio_context_batch_count", "AccountTagTable::price_ratio_context_batch_price_ratio_mean", "AccountTagTable::price_ratio_context_batch_start_timestamp_ms", "AccountTagTable::price_ratio_context_batch_total_ecpm", "AccountTagTable::price_ratio_context_batch_total_price", "AccountTagTable::price_ratio_context_batch_total_price_ratio", "AccountTagTable::promotion_type", "AccountTagTable::reach_lower_bound_timestamp", "AccountTagTable::reach_upper_bound_timestamp", "AccountTagTable::record_gsp_price", "AccountTagTable::relax_cpa_bid", "AccountTagTable::relax_roi_ratio", "AccountTagTable::roi_ratio", "AccountTagTable::rt_cost_speed", "AccountTagTable::rt_cpa_bid", "AccountTagTable::rt_delivery_speed", "AccountTagTable::rt_roas", "AccountTagTable::separate_gsp_price", "AccountTagTable::softad_cost", "AccountTagTable::softad_diff_ratio", "AccountTagTable::softad_target_cost", "AccountTagTable::speed_type", "AccountTagTable::sync_context_interval_batch_batch_cost", "AccountTagTable::sync_context_interval_batch_batch_gmv", "AccountTagTable::sync_context_interval_batch_batch_start_timestamp", "AccountTagTable::target_cost", "AccountTagTable::target_gmv", "AccountTagTable::total_auction_bid", "AccountTagTable::total_ecpm", "AccountTagTable::unit_id", "AccountTagTable::user_cost_prior_algo", "AccountTagTable::util_vals_ab_exp_ratio", "AccountTagTable::util_vals_acc_explore_bid_target_cost_speed", "AccountTagTable::util_vals_ad_off_target_cost", "AccountTagTable::util_vals_ad_short_type", "AccountTagTable::util_vals_ad_status", "AccountTagTable::util_vals_ad_status_tag", "AccountTagTable::util_vals_ad_status_tag_change", "AccountTagTable::util_vals_adjust_rate_lower_bound", "AccountTagTable::util_vals_adjust_rate_upper_bound", "AccountTagTable::util_vals_advertisable", "AccountTagTable::util_vals_budget", "AccountTagTable::util_vals_cold_start_cost_li_thr", "AccountTagTable::util_vals_cold_start_low_bound", "AccountTagTable::util_vals_current_ms", "AccountTagTable::util_vals_d_val", "AccountTagTable::util_vals_d_val_conv", "AccountTagTable::util_vals_d_val_cost", "AccountTagTable::util_vals_d_weight", "AccountTagTable::util_vals_d_weight_conv", "AccountTagTable::util_vals_d_weight_cost", "AccountTagTable::util_vals_disable_day_reset_exp", "AccountTagTable::util_vals_dynamic_bid_lower_bound_adjust_rate", "AccountTagTable::util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms", "AccountTagTable::util_vals_dynamic_bid_upper_bound_adjust_rate", "AccountTagTable::util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms", "AccountTagTable::util_vals_enable_cost_prior_algo", "AccountTagTable::util_vals_enable_linear_adaptive_update_time", "AccountTagTable::util_vals_enable_linear_after_cold_start_no_conv_drop", "AccountTagTable::util_vals_enable_linear_pacing_cold_start_low_bound", "AccountTagTable::util_vals_enable_linear_pacing_grid_correction", "AccountTagTable::util_vals_enable_univ_inner_ad_delivery_replace", "AccountTagTable::util_vals_explore_d_weight", "AccountTagTable::util_vals_explore_i_weight", "AccountTagTable::util_vals_explore_p_weight", "AccountTagTable::util_vals_fanstop_adjust_rate_lower_bound_cpa", "AccountTagTable::util_vals_fanstop_adjust_rate_lower_bound_roi", "AccountTagTable::util_vals_fanstop_adjust_rate_upper_bound_cpa", "AccountTagTable::util_vals_fanstop_adjust_rate_upper_bound_roi", "AccountTagTable::util_vals_hard_bid_lower_bound_rate_monitor", "AccountTagTable::util_vals_hard_bid_upper_bound_rate_monitor", "AccountTagTable::util_vals_hard_lower_bound_auto_atv", "AccountTagTable::util_vals_hard_lower_bound_auto_cpa_bid", "AccountTagTable::util_vals_hard_lower_bound_auto_roas", "AccountTagTable::util_vals_hard_upper_bound_auto_atv", "AccountTagTable::util_vals_hard_upper_bound_auto_cpa_bid", "AccountTagTable::util_vals_hard_upper_bound_auto_roas", "AccountTagTable::util_vals_i_val", "AccountTagTable::util_vals_i_val_conv", "AccountTagTable::util_vals_i_val_cost", "AccountTagTable::util_vals_i_weight", "AccountTagTable::util_vals_i_weight_conv", "AccountTagTable::util_vals_i_weight_cost", "AccountTagTable::util_vals_interval_cost", "AccountTagTable::util_vals_is_acc_explore_bid", "AccountTagTable::util_vals_is_ad_open", "AccountTagTable::util_vals_is_apply_adjust", "AccountTagTable::util_vals_is_apply_operation_config", "AccountTagTable::util_vals_is_bid_info", "AccountTagTable::util_vals_is_bid_ptr_null", "AccountTagTable::util_vals_is_cold_start", "AccountTagTable::util_vals_is_cost_target_msg", "AccountTagTable::util_vals_is_costcap_nobid", "AccountTagTable::util_vals_is_debug", "AccountTagTable::util_vals_is_debug_on", "AccountTagTable::util_vals_is_explore", "AccountTagTable::util_vals_is_fanstop", "AccountTagTable::util_vals_is_fanstop_ocpm", "AccountTagTable::util_vals_is_interval_cost", "AccountTagTable::util_vals_is_monitor", "AccountTagTable::util_vals_is_no_bid", "AccountTagTable::util_vals_is_normal", "AccountTagTable::util_vals_is_nothing_to_do", "AccountTagTable::util_vals_is_ocpm_bid_process", "AccountTagTable::util_vals_is_on_live", "AccountTagTable::util_vals_is_online", "AccountTagTable::util_vals_is_out_of_budget", "AccountTagTable::util_vals_is_price_ratio_bound", "AccountTagTable::util_vals_is_process", "AccountTagTable::util_vals_is_reset_context", "AccountTagTable::util_vals_is_roas", "AccountTagTable::util_vals_is_roas_sub_pacing", "AccountTagTable::util_vals_is_skip_cold_start", "AccountTagTable::util_vals_is_skip_update", "AccountTagTable::util_vals_is_start_process", "AccountTagTable::util_vals_is_status_open", "AccountTagTable::util_vals_is_sync_to_dsp", "AccountTagTable::util_vals_is_target_modify", "AccountTagTable::util_vals_is_update_adjust", "AccountTagTable::util_vals_is_update_interval_ms", "AccountTagTable::util_vals_item_type_num", "AccountTagTable::util_vals_new_adjust_auto_value_rate", "AccountTagTable::util_vals_old_adjust_auto_value_rate", "AccountTagTable::util_vals_old_is_out_of_budget", "AccountTagTable::util_vals_p_val", "AccountTagTable::util_vals_p_val_conv", "AccountTagTable::util_vals_p_val_cost", "AccountTagTable::util_vals_p_weight", "AccountTagTable::util_vals_p_weight_conv", "AccountTagTable::util_vals_p_weight_cost", "AccountTagTable::util_vals_pacing_method_type", "AccountTagTable::util_vals_pacing_type", "AccountTagTable::util_vals_pacing_weight", "AccountTagTable::util_vals_pacing_weight_conv", "AccountTagTable::util_vals_pacing_weight_cost", "AccountTagTable::util_vals_prior_cost", "AccountTagTable::util_vals_pro_ocpc_not_been_set", "AccountTagTable::util_vals_promotion_type_num", "AccountTagTable::util_vals_reset_pacing", "AccountTagTable::util_vals_rt_cost_speed", "AccountTagTable::util_vals_rt_delivery_speed", "AccountTagTable::util_vals_start_bid_rate", "AccountTagTable::util_vals_start_bid_rate_sub_pacing", "AccountTagTable::util_vals_step_bid_rate_descent", "AccountTagTable::util_vals_step_bid_rate_increase", "AccountTagTable::util_vals_target_cost_speed", "AccountTagTable::util_vals_target_delivery_speed", "AccountTagTable::util_vals_target_modify_ratio", "AccountTagTable::util_vals_target_roas", "AccountTagTable::util_vals_thread_id", "AccountTagTable::util_vals_total_pred_conv", "AccountTagTable::util_vals_unit_id", "AccountTagTable::util_vals_update_interval_ms", "TriggerTable::account_id", "TriggerTable::action_type", "TriggerTable::auction_bid", "TriggerTable::author_id", "TriggerTable::bid_type", "TriggerTable::campaign_id", "TriggerTable::campaign_type", "TriggerTable::conv_num", "TriggerTable::cost", "TriggerTable::cpa_bid", "TriggerTable::delivery_timestamp", "TriggerTable::ecpm", "TriggerTable::event_server_timestamp", "TriggerTable::gmv", "TriggerTable::inner_group_tag", "TriggerTable::is_soft", "TriggerTable::is_store_wide_roi_reco_conv", "TriggerTable::item_type", "TriggerTable::item_type_num", "TriggerTable::live_stream_id", "TriggerTable::medium_attribute", "TriggerTable::message_seq", "TriggerTable::ocpc_action_type", "TriggerTable::pred_conv", "TriggerTable::pred_ctr_sum", "TriggerTable::pred_cvr_sum", "TriggerTable::price_after_billing_separate", "TriggerTable::price_before_billing_separate", "TriggerTable::price_ratio", "TriggerTable::promotion_type", "TriggerTable::record_gsp_price", "TriggerTable::roi_ratio", "TriggerTable::separate_gsp_price", "TriggerTable::speed_type", "TriggerTable::target_cost", "TriggerTable::target_gmv", "TriggerTable::unit_id", "UnitTagTable::bid_state_info_ptr", "UnitTagTable::has_bid_state_info_ptr", "UnitTagTable::unit_id"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopStepOneWriteToRedisMixer"}, "inner_loop_ocpm_bid_context_mixer_172558": {"item_table": "TriggerTable", "message_seq": {"item_table": "TriggerTable", "column": "message_seq"}, "event_server_timestamp": {"item_table": "TriggerTable", "column": "event_server_timestamp"}, "bid_data_level": 1, "bid_state_info": {"bid_state_info_has_bid_state_info_ptr": {"item_table": "AccountTagTable", "column": "has_bid_state_info_ptr"}, "bid_state_info_ptr": {"item_table": "AccountTagTable", "column": "bid_state_info_ptr"}, "bid_state_info_explore_time_period": {"item_table": "AccountTagTable", "column": "bid_state_info_explore_time_period"}, "bid_state_info_explore_budget_start_time": {"item_table": "AccountTagTable", "column": "bid_state_info_explore_budget_start_time"}, "bid_state_info_explore_budget_status": {"item_table": "AccountTagTable", "column": "bid_state_info_explore_budget_status"}, "bid_state_info_explore_budget": {"item_table": "AccountTagTable", "column": "bid_state_info_explore_budget"}, "bid_state_info_is_live": {"item_table": "AccountTagTable", "column": "bid_state_info_is_live"}, "bid_state_info_ad_status": {"item_table": "AccountTagTable", "column": "bid_state_info_ad_status"}, "bid_state_info_advertisable": {"item_table": "AccountTagTable", "column": "bid_state_info_advertisable"}, "bid_state_info_is_status_open": {"item_table": "AccountTagTable", "column": "bid_state_info_is_status_open"}, "bid_state_info_online": {"item_table": "AccountTagTable", "column": "bid_state_info_online"}, "bid_state_info_left_budget": {"item_table": "AccountTagTable", "column": "bid_state_info_left_budget"}, "bid_state_info_budget": {"item_table": "AccountTagTable", "column": "bid_state_info_budget"}, "bid_state_info_account_type": {"item_table": "AccountTagTable", "column": "bid_state_info_account_type"}, "bid_state_info_bid_strategy": {"item_table": "AccountTagTable", "column": "bid_state_info_bid_strategy"}, "bid_state_info_bid_type": {"item_table": "AccountTagTable", "column": "bid_state_info_bid_type"}, "bid_state_info_live_launch_type": {"item_table": "AccountTagTable", "column": "bid_state_info_live_launch_type"}, "bid_state_info_account_id": {"item_table": "AccountTagTable", "column": "bid_state_info_account_id"}, "bid_state_info_campaign_id": {"item_table": "AccountTagTable", "column": "bid_state_info_campaign_id"}, "bid_state_info_author_id": {"item_table": "AccountTagTable", "column": "bid_state_info_author_id"}, "bid_state_info_live_stream_id": {"item_table": "AccountTagTable", "column": "bid_state_info_live_stream_id"}, "bid_state_info_speed_type": {"item_table": "AccountTagTable", "column": "bid_state_info_speed_type"}, "bid_state_info_promotion_type": {"item_table": "AccountTagTable", "column": "bid_state_info_promotion_type"}, "bid_state_info_campaign_type": {"item_table": "AccountTagTable", "column": "bid_state_info_campaign_type"}, "bid_state_info_cpa_bid": {"item_table": "AccountTagTable", "column": "bid_state_info_cpa_bid"}, "bid_state_info_roi_ratio": {"item_table": "AccountTagTable", "column": "bid_state_info_roi_ratio"}}, "msg_attr_info": {"msg_campaign_type": {"item_table": "TriggerTable", "column": "campaign_type"}, "msg_ocpc_action_type": {"item_table": "TriggerTable", "column": "ocpc_action_type"}, "msg_promotion_type": {"item_table": "TriggerTable", "column": "promotion_type"}, "msg_item_type": {"item_table": "TriggerTable", "column": "item_type"}, "msg_author_id": {"item_table": "TriggerTable", "column": "author_id"}, "msg_account_id": {"item_table": "TriggerTable", "column": "account_id"}, "msg_live_stream_id": {"item_table": "TriggerTable", "column": "live_stream_id"}, "msg_unit_id": {"item_table": "TriggerTable", "column": "unit_id"}, "msg_conv_num": {"item_table": "TriggerTable", "column": "conv_num"}, "msg_campaign_id": {"item_table": "TriggerTable", "column": "campaign_id"}, "msg_item_type_num": {"item_table": "TriggerTable", "column": "item_type_num"}, "msg_cost": {"item_table": "TriggerTable", "column": "cost"}, "msg_gmv": {"item_table": "TriggerTable", "column": "gmv"}, "msg_roi_ratio": {"item_table": "TriggerTable", "column": "roi_ratio"}, "msg_action_type": {"item_table": "TriggerTable", "column": "action_type"}, "msg_group_tag": {"item_table": "TriggerTable", "column": "inner_group_tag"}, "msg_bid_type": {"item_table": "TriggerTable", "column": "bid_type"}, "msg_medium_attribute": {"item_table": "TriggerTable", "column": "medium_attribute"}, "msg_speed_type": {"item_table": "TriggerTable", "column": "speed_type"}, "msg_delivery_timestamp": {"item_table": "TriggerTable", "column": "delivery_timestamp"}, "msg_cpa_bid": {"item_table": "TriggerTable", "column": "cpa_bid"}, "msg_is_soft": {"item_table": "TriggerTable", "column": "is_soft"}, "msg_target_cost": {"item_table": "TriggerTable", "column": "target_cost"}, "msg_target_gmv": {"item_table": "TriggerTable", "column": "target_gmv"}, "msg_separate_gsp_price": {"item_table": "TriggerTable", "column": "separate_gsp_price"}, "msg_record_gsp_price": {"item_table": "TriggerTable", "column": "record_gsp_price"}, "msg_pred_conv": {"item_table": "TriggerTable", "column": "pred_conv"}, "msg_pred_cvr_sum": {"item_table": "TriggerTable", "column": "pred_cvr_sum"}, "msg_pred_ctr_sum": {"item_table": "TriggerTable", "column": "pred_ctr_sum"}, "msg_price_before_billing_separate": {"item_table": "TriggerTable", "column": "price_before_billing_separate"}, "msg_price_after_billing_separate": {"item_table": "TriggerTable", "column": "price_after_billing_separate"}, "msg_ecpm": {"item_table": "TriggerTable", "column": "ecpm"}, "msg_auction_bid": {"item_table": "TriggerTable", "column": "auction_bid"}, "msg_price_ratio": {"item_table": "TriggerTable", "column": "price_ratio"}, "msg_is_store_wide_roi_reco_conv": {"item_table": "TriggerTable", "column": "is_store_wide_roi_reco_conv"}}, "bid_ctx_attr_info": {"bid_ctx_bid_context_key": {"item_table": "AccountTagTable", "column": "bid_context_key"}, "bid_ctx_group_tag": {"item_table": "AccountTagTable", "column": "group_tag"}, "bid_ctx_campaign_type": {"item_table": "AccountTagTable", "column": "campaign_type"}, "bid_ctx_ocpc_action_type": {"item_table": "AccountTagTable", "column": "ocpc_action_type"}, "bid_ctx_promotion_type": {"item_table": "AccountTagTable", "column": "promotion_type"}, "bid_ctx_item_type": {"item_table": "AccountTagTable", "column": "item_type"}, "bid_ctx_first_industry_name": {"item_table": "AccountTagTable", "column": "first_industry_name"}, "bid_ctx_group_tag_num": {"item_table": "AccountTagTable", "column": "group_tag_num"}, "bid_ctx_unit_id": {"item_table": "AccountTagTable", "column": "unit_id"}, "bid_ctx_account_id": {"item_table": "AccountTagTable", "column": "account_id"}, "bid_ctx_author_id": {"item_table": "AccountTagTable", "column": "author_id"}, "bid_ctx_live_stream_id": {"item_table": "AccountTagTable", "column": "live_stream_id"}, "bid_ctx_item_type_num": {"item_table": "AccountTagTable", "column": "item_type_num"}, "bid_ctx_campaign_id": {"item_table": "AccountTagTable", "column": "campaign_id"}, "bid_ctx_price_before_billing_separate": {"item_table": "AccountTagTable", "column": "price_before_billing_separate"}, "bid_ctx_price_after_billing_separate": {"item_table": "AccountTagTable", "column": "price_after_billing_separate"}, "bid_ctx_cost": {"item_table": "AccountTagTable", "column": "cost"}, "bid_ctx_gmv": {"item_table": "AccountTagTable", "column": "gmv"}, "bid_ctx_conv_num": {"item_table": "AccountTagTable", "column": "conv_num"}, "bid_ctx_cost_count": {"item_table": "AccountTagTable", "column": "cost_count"}, "bid_ctx_gmv_count": {"item_table": "AccountTagTable", "column": "gmv_count"}, "bid_ctx_delivery_cnt": {"item_table": "AccountTagTable", "column": "delivery_cnt"}, "bid_ctx_cpa_bid": {"item_table": "AccountTagTable", "column": "cpa_bid"}, "bid_ctx_relax_cpa_bid": {"item_table": "AccountTagTable", "column": "relax_cpa_bid"}, "bid_ctx_roi_ratio": {"item_table": "AccountTagTable", "column": "roi_ratio"}, "bid_ctx_relax_roi_ratio": {"item_table": "AccountTagTable", "column": "relax_roi_ratio"}, "bid_ctx_rt_cpa_bid": {"item_table": "AccountTagTable", "column": "rt_cpa_bid"}, "bid_ctx_rt_roas": {"item_table": "AccountTagTable", "column": "rt_roas"}, "bid_ctx_auto_cpa_bid": {"item_table": "AccountTagTable", "column": "auto_cpa_bid"}, "bid_ctx_auto_roi_ratio": {"item_table": "AccountTagTable", "column": "auto_roi_ratio"}, "bid_ctx_old_auto_cpa_bid": {"item_table": "AccountTagTable", "column": "old_auto_cpa_bid"}, "bid_ctx_old_auto_roi_ratio": {"item_table": "AccountTagTable", "column": "old_auto_roi_ratio"}, "bid_ctx_adjust_rate": {"item_table": "AccountTagTable", "column": "adjust_rate"}, "bid_ctx_rt_cost_speed": {"item_table": "AccountTagTable", "column": "rt_cost_speed"}, "bid_ctx_rt_delivery_speed": {"item_table": "AccountTagTable", "column": "rt_delivery_speed"}, "bid_ctx_dynamic_lower_bound_roas": {"item_table": "AccountTagTable", "column": "dynamic_lower_bound_roas"}, "bid_ctx_dynamic_upper_bound_roas": {"item_table": "AccountTagTable", "column": "dynamic_upper_bound_roas"}, "bid_ctx_dynamic_lower_bound_cpa_bid": {"item_table": "AccountTagTable", "column": "dynamic_lower_bound_cpa_bid"}, "bid_ctx_dynamic_upper_bound_cpa_bid": {"item_table": "AccountTagTable", "column": "dynamic_upper_bound_cpa_bid"}, "bid_ctx_target_cost": {"item_table": "AccountTagTable", "column": "target_cost"}, "bid_ctx_target_gmv": {"item_table": "AccountTagTable", "column": "target_gmv"}, "bid_ctx_record_gsp_price": {"item_table": "AccountTagTable", "column": "record_gsp_price"}, "bid_ctx_reach_lower_bound_timestamp": {"item_table": "AccountTagTable", "column": "reach_lower_bound_timestamp"}, "bid_ctx_reach_upper_bound_timestamp": {"item_table": "AccountTagTable", "column": "reach_upper_bound_timestamp"}, "bid_ctx_first_delivery_timestamp_ms": {"item_table": "AccountTagTable", "column": "first_delivery_timestamp_ms"}, "bid_ctx_last_delivery_timestamp_ms": {"item_table": "AccountTagTable", "column": "last_delivery_timestamp_ms"}, "bid_ctx_last_update_adjust_timestamp": {"item_table": "AccountTagTable", "column": "last_update_adjust_timestamp"}, "bid_ctx_cost_start_timestamp_ms": {"item_table": "AccountTagTable", "column": "cost_start_timestamp_ms"}, "bid_ctx_last_update_context_timestamp": {"item_table": "AccountTagTable", "column": "last_update_context_timestamp"}, "bid_ctx_last_sync_context_timestamp": {"item_table": "AccountTagTable", "column": "last_sync_context_timestamp"}, "bid_ctx_online": {"item_table": "AccountTagTable", "column": "online"}, "bid_ctx_is_cold_start": {"item_table": "AccountTagTable", "column": "is_cold_start"}, "bid_ctx_speed_type": {"item_table": "AccountTagTable", "column": "speed_type"}, "bid_ctx_context_start_timestamp": {"item_table": "AccountTagTable", "column": "context_start_timestamp"}, "bid_ctx_last_sync_result_timestamp": {"item_table": "AccountTagTable", "column": "last_sync_result_timestamp"}, "bid_ctx_adjust_auto_value_rate": {"item_table": "AccountTagTable", "column": "adjust_auto_value_rate"}, "bid_ctx_total_ecpm": {"item_table": "AccountTagTable", "column": "total_ecpm"}, "bid_ctx_separate_gsp_price": {"item_table": "AccountTagTable", "column": "separate_gsp_price"}, "bid_ctx_total_auction_bid": {"item_table": "AccountTagTable", "column": "total_auction_bid"}, "bid_ctx_dry_up_base_value": {"item_table": "AccountTagTable", "column": "dry_up_base_value"}, "bid_ctx_ctr_sum": {"item_table": "AccountTagTable", "column": "ctr_sum"}, "bid_ctx_cvr_sum": {"item_table": "AccountTagTable", "column": "cvr_sum"}, "bid_ctx_softad_cost": {"item_table": "AccountTagTable", "column": "softad_cost"}, "bid_ctx_softad_target_cost": {"item_table": "AccountTagTable", "column": "softad_target_cost"}, "bid_ctx_softad_diff_ratio": {"item_table": "AccountTagTable", "column": "softad_diff_ratio"}, "bid_ctx_sync_context_interval_batch_batch_cost": {"item_table": "AccountTagTable", "column": "sync_context_interval_batch_batch_cost"}, "bid_ctx_sync_context_interval_batch_batch_gmv": {"item_table": "AccountTagTable", "column": "sync_context_interval_batch_batch_gmv"}, "bid_ctx_sync_context_interval_batch_batch_start_timestamp": {"item_table": "AccountTagTable", "column": "sync_context_interval_batch_batch_start_timestamp"}, "bid_ctx_cost_speed_context_batch_start_timestamp_ms": {"item_table": "AccountTagTable", "column": "cost_speed_context_batch_start_timestamp_ms"}, "bid_ctx_cost_speed_context_batch_value_sum": {"item_table": "AccountTagTable", "column": "cost_speed_context_batch_value_sum"}, "bid_ctx_cost_speed_context_batch_count": {"item_table": "AccountTagTable", "column": "cost_speed_context_batch_count"}, "bid_ctx_cost_speed_context_batch_value_mean": {"item_table": "AccountTagTable", "column": "cost_speed_context_batch_value_mean"}, "bid_ctx_cost_speed_context_batch_value_speed": {"item_table": "AccountTagTable", "column": "cost_speed_context_batch_value_speed"}, "bid_ctx_delivery_speed_context_batch_start_timestamp_ms": {"item_table": "AccountTagTable", "column": "delivery_speed_context_batch_start_timestamp_ms"}, "bid_ctx_delivery_speed_context_batch_value_sum": {"item_table": "AccountTagTable", "column": "delivery_speed_context_batch_value_sum"}, "bid_ctx_delivery_speed_context_batch_count": {"item_table": "AccountTagTable", "column": "delivery_speed_context_batch_count"}, "bid_ctx_delivery_speed_context_batch_value_mean": {"item_table": "AccountTagTable", "column": "delivery_speed_context_batch_value_mean"}, "bid_ctx_delivery_speed_context_batch_value_speed": {"item_table": "AccountTagTable", "column": "delivery_speed_context_batch_value_speed"}, "bid_ctx_conv_ratio_adjust_context_pred_conv": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_pred_conv"}, "bid_ctx_conv_ratio_adjust_context_old_auto_conv_ratio": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_old_auto_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_auto_conv_ratio": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_auto_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio"}, "bid_ctx_conv_ratio_adjust_context_real_conv": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_real_conv"}, "bid_ctx_conv_ratio_adjust_context_last_update_conv_ratio_timestamp": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_last_update_conv_ratio_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_r1_upper_bound_timestamp": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_reach_r1_upper_bound_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_r1_lower_bound_timestamp": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_reach_r1_lower_bound_timestamp"}, "bid_ctx_conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp": {"item_table": "AccountTagTable", "column": "conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp"}, "bid_ctx_price_ratio_context_batch_start_timestamp_ms": {"item_table": "AccountTagTable", "column": "price_ratio_context_batch_start_timestamp_ms"}, "bid_ctx_price_ratio_context_batch_total_price": {"item_table": "AccountTagTable", "column": "price_ratio_context_batch_total_price"}, "bid_ctx_price_ratio_context_batch_total_ecpm": {"item_table": "AccountTagTable", "column": "price_ratio_context_batch_total_ecpm"}, "bid_ctx_price_ratio_context_batch_total_price_ratio": {"item_table": "AccountTagTable", "column": "price_ratio_context_batch_total_price_ratio"}, "bid_ctx_price_ratio_context_batch_count": {"item_table": "AccountTagTable", "column": "price_ratio_context_batch_count"}, "bid_ctx_price_ratio_context_batch_avg_price_ratio": {"item_table": "AccountTagTable", "column": "price_ratio_context_batch_avg_price_ratio"}, "bid_ctx_price_ratio_context_batch_price_ratio_mean": {"item_table": "AccountTagTable", "column": "price_ratio_context_batch_price_ratio_mean"}, "bid_ctx_item_type_info_photo_to_live_cnt": {"item_table": "AccountTagTable", "column": "item_type_info_photo_to_live_cnt"}, "bid_ctx_item_type_info_direct_live_cnt": {"item_table": "AccountTagTable", "column": "item_type_info_direct_live_cnt"}, "bid_ctx_item_type_info_photo_to_live_cost": {"item_table": "AccountTagTable", "column": "item_type_info_photo_to_live_cost"}, "bid_ctx_item_type_info_direct_live_cost": {"item_table": "AccountTagTable", "column": "item_type_info_direct_live_cost"}}, "process_utils_attr_info": {"process_utils_ad_off_target_cost": {"item_table": "AccountTagTable", "column": "util_vals_ad_off_target_cost"}, "process_utils_new_adjust_auto_value_rate": {"item_table": "AccountTagTable", "column": "util_vals_new_adjust_auto_value_rate"}, "process_utils_old_adjust_auto_value_rate": {"item_table": "AccountTagTable", "column": "util_vals_old_adjust_auto_value_rate"}, "process_utils_is_bid_ptr_null": {"item_table": "AccountTagTable", "column": "util_vals_is_bid_ptr_null"}, "process_utils_pro_ocpc_not_been_set": {"item_table": "AccountTagTable", "column": "util_vals_pro_ocpc_not_been_set"}, "process_utils_reset_pacing": {"item_table": "AccountTagTable", "column": "util_vals_reset_pacing"}, "process_utils_is_cost_target_msg": {"item_table": "AccountTagTable", "column": "util_vals_is_cost_target_msg"}, "process_utils_enable_cost_prior_algo": {"item_table": "AccountTagTable", "column": "util_vals_enable_cost_prior_algo"}, "process_utils_enable_linear_pacing_cold_start_low_bound": {"item_table": "AccountTagTable", "column": "util_vals_enable_linear_pacing_cold_start_low_bound"}, "process_utils_enable_linear_pacing_grid_correction": {"item_table": "AccountTagTable", "column": "util_vals_enable_linear_pacing_grid_correction"}, "process_utils_enable_linear_adaptive_update_time": {"item_table": "AccountTagTable", "column": "util_vals_enable_linear_adaptive_update_time"}, "process_utils_enable_linear_after_cold_start_no_conv_drop": {"item_table": "AccountTagTable", "column": "util_vals_enable_linear_after_cold_start_no_conv_drop"}, "process_utils_disable_day_reset_exp": {"item_table": "AccountTagTable", "column": "util_vals_disable_day_reset_exp"}, "process_utils_enable_univ_inner_ad_delivery_replace": {"item_table": "AccountTagTable", "column": "util_vals_enable_univ_inner_ad_delivery_replace"}, "process_utils_is_apply_adjust": {"item_table": "AccountTagTable", "column": "util_vals_is_apply_adjust"}, "process_utils_is_debug": {"item_table": "AccountTagTable", "column": "util_vals_is_debug"}, "process_utils_is_roas_sub_pacing": {"item_table": "AccountTagTable", "column": "util_vals_is_roas_sub_pacing"}, "process_utils_is_ocpm_bid_process": {"item_table": "AccountTagTable", "column": "util_vals_is_ocpm_bid_process"}, "process_utils_is_apply_operation_config": {"item_table": "AccountTagTable", "column": "util_vals_is_apply_operation_config"}, "process_utils_is_start_process": {"item_table": "AccountTagTable", "column": "util_vals_is_start_process"}, "process_utils_is_debug_on": {"item_table": "AccountTagTable", "column": "util_vals_is_debug_on"}, "process_utils_is_normal": {"item_table": "AccountTagTable", "column": "util_vals_is_normal"}, "process_utils_is_fanstop": {"item_table": "AccountTagTable", "column": "util_vals_is_fanstop"}, "process_utils_is_fanstop_ocpm": {"item_table": "AccountTagTable", "column": "util_vals_is_fanstop_ocpm"}, "process_utils_is_roas": {"item_table": "AccountTagTable", "column": "util_vals_is_roas"}, "process_utils_is_no_bid": {"item_table": "AccountTagTable", "column": "util_vals_is_no_bid"}, "process_utils_is_skip_update": {"item_table": "AccountTagTable", "column": "util_vals_is_skip_update"}, "process_utils_is_nothing_to_do": {"item_table": "AccountTagTable", "column": "util_vals_is_nothing_to_do"}, "process_utils_is_process": {"item_table": "AccountTagTable", "column": "util_vals_is_process"}, "process_utils_is_reset_context": {"item_table": "AccountTagTable", "column": "util_vals_is_reset_context"}, "process_utils_is_update_adjust": {"item_table": "AccountTagTable", "column": "util_vals_is_update_adjust"}, "process_utils_is_interval_cost": {"item_table": "AccountTagTable", "column": "util_vals_is_interval_cost"}, "process_utils_is_update_interval_ms": {"item_table": "AccountTagTable", "column": "util_vals_is_update_interval_ms"}, "process_utils_is_ad_open": {"item_table": "AccountTagTable", "column": "util_vals_is_ad_open"}, "process_utils_is_sync_to_dsp": {"item_table": "AccountTagTable", "column": "util_vals_is_sync_to_dsp"}, "process_utils_is_monitor": {"item_table": "AccountTagTable", "column": "util_vals_is_monitor"}, "process_utils_is_cold_start": {"item_table": "AccountTagTable", "column": "util_vals_is_cold_start"}, "process_utils_is_costcap_nobid": {"item_table": "AccountTagTable", "column": "util_vals_is_costcap_nobid"}, "process_utils_is_skip_cold_start": {"item_table": "AccountTagTable", "column": "util_vals_is_skip_cold_start"}, "process_utils_is_on_live": {"item_table": "AccountTagTable", "column": "util_vals_is_on_live"}, "process_utils_is_out_of_budget": {"item_table": "AccountTagTable", "column": "util_vals_is_out_of_budget"}, "process_utils_old_is_out_of_budget": {"item_table": "AccountTagTable", "column": "util_vals_old_is_out_of_budget"}, "process_utils_advertisable": {"item_table": "AccountTagTable", "column": "util_vals_advertisable"}, "process_utils_is_status_open": {"item_table": "AccountTagTable", "column": "util_vals_is_status_open"}, "process_utils_ad_status": {"item_table": "AccountTagTable", "column": "util_vals_ad_status"}, "process_utils_unit_id": {"item_table": "AccountTagTable", "column": "util_vals_unit_id"}, "process_utils_thread_id": {"item_table": "AccountTagTable", "column": "util_vals_thread_id"}, "process_utils_is_bid_info": {"item_table": "AccountTagTable", "column": "util_vals_is_bid_info"}, "process_utils_is_online": {"item_table": "AccountTagTable", "column": "util_vals_is_online"}, "process_utils_is_explore": {"item_table": "AccountTagTable", "column": "util_vals_is_explore"}, "process_utils_ab_exp_ratio": {"item_table": "AccountTagTable", "column": "util_vals_ab_exp_ratio"}, "process_utils_promotion_type_num": {"item_table": "AccountTagTable", "column": "util_vals_promotion_type_num"}, "process_utils_item_type_num": {"item_table": "AccountTagTable", "column": "util_vals_item_type_num"}, "process_utils_prior_cost": {"item_table": "AccountTagTable", "column": "util_vals_prior_cost"}, "process_utils_cold_start_cost_li_thr": {"item_table": "AccountTagTable", "column": "util_vals_cold_start_cost_li_thr"}, "process_utils_cold_start_low_bound": {"item_table": "AccountTagTable", "column": "util_vals_cold_start_low_bound"}, "process_utils_rt_cost_speed": {"item_table": "AccountTagTable", "column": "util_vals_rt_cost_speed"}, "process_utils_rt_delivery_speed": {"item_table": "AccountTagTable", "column": "util_vals_rt_delivery_speed"}, "process_utils_target_roas": {"item_table": "AccountTagTable", "column": "util_vals_target_roas"}, "process_utils_is_price_ratio_bound": {"item_table": "AccountTagTable", "column": "util_vals_is_price_ratio_bound"}, "process_utils_is_acc_explore_bid": {"item_table": "AccountTagTable", "column": "util_vals_is_acc_explore_bid"}, "process_utils_dynamic_bid_lower_bound_adjust_time_interval_ms": {"item_table": "AccountTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms"}, "process_utils_dynamic_bid_upper_bound_adjust_time_interval_ms": {"item_table": "AccountTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms"}, "process_utils_dynamic_bid_lower_bound_adjust_rate": {"item_table": "AccountTagTable", "column": "util_vals_dynamic_bid_lower_bound_adjust_rate"}, "process_utils_dynamic_bid_upper_bound_adjust_rate": {"item_table": "AccountTagTable", "column": "util_vals_dynamic_bid_upper_bound_adjust_rate"}, "process_utils_p_weight": {"item_table": "AccountTagTable", "column": "util_vals_p_weight"}, "process_utils_i_weight": {"item_table": "AccountTagTable", "column": "util_vals_i_weight"}, "process_utils_d_weight": {"item_table": "AccountTagTable", "column": "util_vals_d_weight"}, "process_utils_explore_p_weight": {"item_table": "AccountTagTable", "column": "util_vals_explore_p_weight"}, "process_utils_explore_i_weight": {"item_table": "AccountTagTable", "column": "util_vals_explore_i_weight"}, "process_utils_explore_d_weight": {"item_table": "AccountTagTable", "column": "util_vals_explore_d_weight"}, "process_utils_p_val": {"item_table": "AccountTagTable", "column": "util_vals_p_val"}, "process_utils_i_val": {"item_table": "AccountTagTable", "column": "util_vals_i_val"}, "process_utils_d_val": {"item_table": "AccountTagTable", "column": "util_vals_d_val"}, "process_utils_pacing_weight": {"item_table": "AccountTagTable", "column": "util_vals_pacing_weight"}, "process_utils_target_cost_speed": {"item_table": "AccountTagTable", "column": "util_vals_target_cost_speed"}, "process_utils_target_delivery_speed": {"item_table": "AccountTagTable", "column": "util_vals_target_delivery_speed"}, "process_utils_start_bid_rate": {"item_table": "AccountTagTable", "column": "util_vals_start_bid_rate"}, "process_utils_start_bid_rate_sub_pacing": {"item_table": "AccountTagTable", "column": "util_vals_start_bid_rate_sub_pacing"}, "process_utils_hard_upper_bound_auto_cpa_bid": {"item_table": "AccountTagTable", "column": "util_vals_hard_upper_bound_auto_cpa_bid"}, "process_utils_hard_lower_bound_auto_cpa_bid": {"item_table": "AccountTagTable", "column": "util_vals_hard_lower_bound_auto_cpa_bid"}, "process_utils_hard_lower_bound_auto_roas": {"item_table": "AccountTagTable", "column": "util_vals_hard_lower_bound_auto_roas"}, "process_utils_hard_upper_bound_auto_roas": {"item_table": "AccountTagTable", "column": "util_vals_hard_upper_bound_auto_roas"}, "process_utils_hard_lower_bound_auto_atv": {"item_table": "AccountTagTable", "column": "util_vals_hard_lower_bound_auto_atv"}, "process_utils_hard_upper_bound_auto_atv": {"item_table": "AccountTagTable", "column": "util_vals_hard_upper_bound_auto_atv"}, "process_utils_acc_explore_bid_target_cost_speed": {"item_table": "AccountTagTable", "column": "util_vals_acc_explore_bid_target_cost_speed"}, "process_utils_current_ms": {"item_table": "AccountTagTable", "column": "util_vals_current_ms"}, "process_utils_ad_short_type": {"item_table": "AccountTagTable", "column": "util_vals_ad_short_type"}, "process_utils_is_target_modify": {"item_table": "AccountTagTable", "column": "util_vals_is_target_modify"}, "process_utils_target_modify_ratio": {"item_table": "AccountTagTable", "column": "util_vals_target_modify_ratio"}, "process_utils_p_weight_conv": {"item_table": "AccountTagTable", "column": "util_vals_p_weight_conv"}, "process_utils_i_weight_conv": {"item_table": "AccountTagTable", "column": "util_vals_i_weight_conv"}, "process_utils_d_weight_conv": {"item_table": "AccountTagTable", "column": "util_vals_d_weight_conv"}, "process_utils_p_val_conv": {"item_table": "AccountTagTable", "column": "util_vals_p_val_conv"}, "process_utils_i_val_conv": {"item_table": "AccountTagTable", "column": "util_vals_i_val_conv"}, "process_utils_d_val_conv": {"item_table": "AccountTagTable", "column": "util_vals_d_val_conv"}, "process_utils_pacing_weight_conv": {"item_table": "AccountTagTable", "column": "util_vals_pacing_weight_conv"}, "process_utils_p_weight_cost": {"item_table": "AccountTagTable", "column": "util_vals_p_weight_cost"}, "process_utils_i_weight_cost": {"item_table": "AccountTagTable", "column": "util_vals_i_weight_cost"}, "process_utils_d_weight_cost": {"item_table": "AccountTagTable", "column": "util_vals_d_weight_cost"}, "process_utils_p_val_cost": {"item_table": "AccountTagTable", "column": "util_vals_p_val_cost"}, "process_utils_i_val_cost": {"item_table": "AccountTagTable", "column": "util_vals_i_val_cost"}, "process_utils_d_val_cost": {"item_table": "AccountTagTable", "column": "util_vals_d_val_cost"}, "process_utils_pacing_weight_cost": {"item_table": "AccountTagTable", "column": "util_vals_pacing_weight_cost"}, "process_utils_update_interval_ms": {"item_table": "AccountTagTable", "column": "util_vals_update_interval_ms"}, "process_utils_interval_cost": {"item_table": "AccountTagTable", "column": "util_vals_interval_cost"}, "process_utils_step_bid_rate_increase": {"item_table": "AccountTagTable", "column": "util_vals_step_bid_rate_increase"}, "process_utils_step_bid_rate_descent": {"item_table": "AccountTagTable", "column": "util_vals_step_bid_rate_descent"}, "process_utils_ad_status_tag": {"item_table": "AccountTagTable", "column": "util_vals_ad_status_tag"}, "process_utils_ad_status_tag_change": {"item_table": "AccountTagTable", "column": "util_vals_ad_status_tag_change"}, "process_utils_total_pred_conv": {"item_table": "AccountTagTable", "column": "util_vals_total_pred_conv"}, "process_utils_adjust_rate_lower_bound": {"item_table": "AccountTagTable", "column": "util_vals_adjust_rate_lower_bound"}, "process_utils_adjust_rate_upper_bound": {"item_table": "AccountTagTable", "column": "util_vals_adjust_rate_upper_bound"}, "process_utils_budget": {"item_table": "AccountTagTable", "column": "util_vals_budget"}, "process_utils_hard_bid_upper_bound_rate_monitor": {"item_table": "AccountTagTable", "column": "util_vals_hard_bid_upper_bound_rate_monitor"}, "process_utils_hard_bid_lower_bound_rate_monitor": {"item_table": "AccountTagTable", "column": "util_vals_hard_bid_lower_bound_rate_monitor"}, "process_utils_fanstop_adjust_rate_lower_bound_roi": {"item_table": "AccountTagTable", "column": "util_vals_fanstop_adjust_rate_lower_bound_roi"}, "process_utils_fanstop_adjust_rate_upper_bound_roi": {"item_table": "AccountTagTable", "column": "util_vals_fanstop_adjust_rate_upper_bound_roi"}, "process_utils_fanstop_adjust_rate_lower_bound_cpa": {"item_table": "AccountTagTable", "column": "util_vals_fanstop_adjust_rate_lower_bound_cpa"}, "process_utils_fanstop_adjust_rate_upper_bound_cpa": {"item_table": "AccountTagTable", "column": "util_vals_fanstop_adjust_rate_upper_bound_cpa"}, "process_utils_pacing_type": {"item_table": "AccountTagTable", "column": "util_vals_pacing_type"}, "process_utils_pacing_method_type": {"item_table": "AccountTagTable", "column": "util_vals_pacing_method_type"}}, "$metadata": {"$output_item_attrs": ["AccountTagTable::account_id", "AccountTagTable::adjust_auto_value_rate", "AccountTagTable::adjust_rate", "AccountTagTable::author_id", "AccountTagTable::auto_cpa_bid", "AccountTagTable::auto_roi_ratio", "AccountTagTable::bid_context_key", "AccountTagTable::campaign_id", "AccountTagTable::campaign_type", "AccountTagTable::context_start_timestamp", "AccountTagTable::conv_num", "AccountTagTable::conv_ratio_adjust_context_auto_conv_ratio", "AccountTagTable::conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio", "AccountTagTable::conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio", "AccountTagTable::conv_ratio_adjust_context_last_update_conv_ratio_timestamp", "AccountTagTable::conv_ratio_adjust_context_old_auto_conv_ratio", "AccountTagTable::conv_ratio_adjust_context_pred_conv", "AccountTagTable::conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp", "AccountTagTable::conv_ratio_adjust_context_reach_r1_lower_bound_timestamp", "AccountTagTable::conv_ratio_adjust_context_reach_r1_upper_bound_timestamp", "AccountTagTable::conv_ratio_adjust_context_real_conv", "AccountTagTable::cost", "AccountTagTable::cost_count", "AccountTagTable::cost_speed_context_batch_count", "AccountTagTable::cost_speed_context_batch_start_timestamp_ms", "AccountTagTable::cost_speed_context_batch_value_mean", "AccountTagTable::cost_speed_context_batch_value_speed", "AccountTagTable::cost_speed_context_batch_value_sum", "AccountTagTable::cost_start_timestamp_ms", "AccountTagTable::cpa_bid", "AccountTagTable::ctr_sum", "AccountTagTable::cvr_sum", "AccountTagTable::delivery_cnt", "AccountTagTable::delivery_speed_context_batch_count", "AccountTagTable::delivery_speed_context_batch_start_timestamp_ms", "AccountTagTable::delivery_speed_context_batch_value_mean", "AccountTagTable::delivery_speed_context_batch_value_speed", "AccountTagTable::delivery_speed_context_batch_value_sum", "AccountTagTable::dry_up_base_value", "AccountTagTable::dynamic_lower_bound_cpa_bid", "AccountTagTable::dynamic_lower_bound_roas", "AccountTagTable::dynamic_upper_bound_cpa_bid", "AccountTagTable::dynamic_upper_bound_roas", "AccountTagTable::first_delivery_timestamp_ms", "AccountTagTable::first_industry_name", "AccountTagTable::gmv", "AccountTagTable::gmv_count", "AccountTagTable::group_tag", "AccountTagTable::group_tag_num", "AccountTagTable::is_cold_start", "AccountTagTable::item_type", "AccountTagTable::item_type_info_direct_live_cnt", "AccountTagTable::item_type_info_direct_live_cost", "AccountTagTable::item_type_info_photo_to_live_cnt", "AccountTagTable::item_type_info_photo_to_live_cost", "AccountTagTable::item_type_num", "AccountTagTable::last_delivery_timestamp_ms", "AccountTagTable::last_sync_context_timestamp", "AccountTagTable::last_sync_result_timestamp", "AccountTagTable::last_update_adjust_timestamp", "AccountTagTable::last_update_context_timestamp", "AccountTagTable::live_stream_id", "AccountTagTable::ocpc_action_type", "AccountTagTable::old_auto_cpa_bid", "AccountTagTable::old_auto_roi_ratio", "AccountTagTable::online", "AccountTagTable::price_after_billing_separate", "AccountTagTable::price_before_billing_separate", "AccountTagTable::price_ratio_context_batch_avg_price_ratio", "AccountTagTable::price_ratio_context_batch_count", "AccountTagTable::price_ratio_context_batch_price_ratio_mean", "AccountTagTable::price_ratio_context_batch_start_timestamp_ms", "AccountTagTable::price_ratio_context_batch_total_ecpm", "AccountTagTable::price_ratio_context_batch_total_price", "AccountTagTable::price_ratio_context_batch_total_price_ratio", "AccountTagTable::promotion_type", "AccountTagTable::reach_lower_bound_timestamp", "AccountTagTable::reach_upper_bound_timestamp", "AccountTagTable::record_gsp_price", "AccountTagTable::relax_cpa_bid", "AccountTagTable::relax_roi_ratio", "AccountTagTable::roi_ratio", "AccountTagTable::rt_cost_speed", "AccountTagTable::rt_cpa_bid", "AccountTagTable::rt_delivery_speed", "AccountTagTable::rt_roas", "AccountTagTable::separate_gsp_price", "AccountTagTable::softad_cost", "AccountTagTable::softad_diff_ratio", "AccountTagTable::softad_target_cost", "AccountTagTable::speed_type", "AccountTagTable::sync_context_interval_batch_batch_cost", "AccountTagTable::sync_context_interval_batch_batch_gmv", "AccountTagTable::sync_context_interval_batch_batch_start_timestamp", "AccountTagTable::target_cost", "AccountTagTable::target_gmv", "AccountTagTable::total_auction_bid", "AccountTagTable::total_ecpm", "AccountTagTable::unit_id", "AccountTagTable::util_vals_ab_exp_ratio", "AccountTagTable::util_vals_acc_explore_bid_target_cost_speed", "AccountTagTable::util_vals_ad_off_target_cost", "AccountTagTable::util_vals_ad_short_type", "AccountTagTable::util_vals_ad_status", "AccountTagTable::util_vals_ad_status_tag", "AccountTagTable::util_vals_ad_status_tag_change", "AccountTagTable::util_vals_adjust_rate_lower_bound", "AccountTagTable::util_vals_adjust_rate_upper_bound", "AccountTagTable::util_vals_advertisable", "AccountTagTable::util_vals_budget", "AccountTagTable::util_vals_cold_start_cost_li_thr", "AccountTagTable::util_vals_cold_start_low_bound", "AccountTagTable::util_vals_current_ms", "AccountTagTable::util_vals_d_val", "AccountTagTable::util_vals_d_val_conv", "AccountTagTable::util_vals_d_val_cost", "AccountTagTable::util_vals_d_weight", "AccountTagTable::util_vals_d_weight_conv", "AccountTagTable::util_vals_d_weight_cost", "AccountTagTable::util_vals_disable_day_reset_exp", "AccountTagTable::util_vals_dynamic_bid_lower_bound_adjust_rate", "AccountTagTable::util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms", "AccountTagTable::util_vals_dynamic_bid_upper_bound_adjust_rate", "AccountTagTable::util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms", "AccountTagTable::util_vals_enable_cost_prior_algo", "AccountTagTable::util_vals_enable_linear_adaptive_update_time", "AccountTagTable::util_vals_enable_linear_after_cold_start_no_conv_drop", "AccountTagTable::util_vals_enable_linear_pacing_cold_start_low_bound", "AccountTagTable::util_vals_enable_linear_pacing_grid_correction", "AccountTagTable::util_vals_enable_univ_inner_ad_delivery_replace", "AccountTagTable::util_vals_explore_d_weight", "AccountTagTable::util_vals_explore_i_weight", "AccountTagTable::util_vals_explore_p_weight", "AccountTagTable::util_vals_fanstop_adjust_rate_lower_bound_cpa", "AccountTagTable::util_vals_fanstop_adjust_rate_lower_bound_roi", "AccountTagTable::util_vals_fanstop_adjust_rate_upper_bound_cpa", "AccountTagTable::util_vals_fanstop_adjust_rate_upper_bound_roi", "AccountTagTable::util_vals_hard_bid_lower_bound_rate_monitor", "AccountTagTable::util_vals_hard_bid_upper_bound_rate_monitor", "AccountTagTable::util_vals_hard_lower_bound_auto_atv", "AccountTagTable::util_vals_hard_lower_bound_auto_cpa_bid", "AccountTagTable::util_vals_hard_lower_bound_auto_roas", "AccountTagTable::util_vals_hard_upper_bound_auto_atv", "AccountTagTable::util_vals_hard_upper_bound_auto_cpa_bid", "AccountTagTable::util_vals_hard_upper_bound_auto_roas", "AccountTagTable::util_vals_i_val", "AccountTagTable::util_vals_i_val_conv", "AccountTagTable::util_vals_i_val_cost", "AccountTagTable::util_vals_i_weight", "AccountTagTable::util_vals_i_weight_conv", "AccountTagTable::util_vals_i_weight_cost", "AccountTagTable::util_vals_interval_cost", "AccountTagTable::util_vals_is_acc_explore_bid", "AccountTagTable::util_vals_is_ad_open", "AccountTagTable::util_vals_is_apply_adjust", "AccountTagTable::util_vals_is_apply_operation_config", "AccountTagTable::util_vals_is_bid_info", "AccountTagTable::util_vals_is_bid_ptr_null", "AccountTagTable::util_vals_is_cold_start", "AccountTagTable::util_vals_is_cost_target_msg", "AccountTagTable::util_vals_is_costcap_nobid", "AccountTagTable::util_vals_is_debug", "AccountTagTable::util_vals_is_debug_on", "AccountTagTable::util_vals_is_explore", "AccountTagTable::util_vals_is_fanstop", "AccountTagTable::util_vals_is_fanstop_ocpm", "AccountTagTable::util_vals_is_interval_cost", "AccountTagTable::util_vals_is_monitor", "AccountTagTable::util_vals_is_no_bid", "AccountTagTable::util_vals_is_normal", "AccountTagTable::util_vals_is_nothing_to_do", "AccountTagTable::util_vals_is_ocpm_bid_process", "AccountTagTable::util_vals_is_on_live", "AccountTagTable::util_vals_is_online", "AccountTagTable::util_vals_is_out_of_budget", "AccountTagTable::util_vals_is_price_ratio_bound", "AccountTagTable::util_vals_is_process", "AccountTagTable::util_vals_is_reset_context", "AccountTagTable::util_vals_is_roas", "AccountTagTable::util_vals_is_roas_sub_pacing", "AccountTagTable::util_vals_is_skip_cold_start", "AccountTagTable::util_vals_is_skip_update", "AccountTagTable::util_vals_is_start_process", "AccountTagTable::util_vals_is_status_open", "AccountTagTable::util_vals_is_sync_to_dsp", "AccountTagTable::util_vals_is_target_modify", "AccountTagTable::util_vals_is_update_adjust", "AccountTagTable::util_vals_is_update_interval_ms", "AccountTagTable::util_vals_item_type_num", "AccountTagTable::util_vals_new_adjust_auto_value_rate", "AccountTagTable::util_vals_old_adjust_auto_value_rate", "AccountTagTable::util_vals_old_is_out_of_budget", "AccountTagTable::util_vals_p_val", "AccountTagTable::util_vals_p_val_conv", "AccountTagTable::util_vals_p_val_cost", "AccountTagTable::util_vals_p_weight", "AccountTagTable::util_vals_p_weight_conv", "AccountTagTable::util_vals_p_weight_cost", "AccountTagTable::util_vals_pacing_method_type", "AccountTagTable::util_vals_pacing_type", "AccountTagTable::util_vals_pacing_weight", "AccountTagTable::util_vals_pacing_weight_conv", "AccountTagTable::util_vals_pacing_weight_cost", "AccountTagTable::util_vals_prior_cost", "AccountTagTable::util_vals_pro_ocpc_not_been_set", "AccountTagTable::util_vals_promotion_type_num", "AccountTagTable::util_vals_reset_pacing", "AccountTagTable::util_vals_rt_cost_speed", "AccountTagTable::util_vals_rt_delivery_speed", "AccountTagTable::util_vals_start_bid_rate", "AccountTagTable::util_vals_start_bid_rate_sub_pacing", "AccountTagTable::util_vals_step_bid_rate_descent", "AccountTagTable::util_vals_step_bid_rate_increase", "AccountTagTable::util_vals_target_cost_speed", "AccountTagTable::util_vals_target_delivery_speed", "AccountTagTable::util_vals_target_modify_ratio", "AccountTagTable::util_vals_target_roas", "AccountTagTable::util_vals_thread_id", "AccountTagTable::util_vals_total_pred_conv", "AccountTagTable::util_vals_unit_id", "AccountTagTable::util_vals_update_interval_ms", "TriggerTable::account_id", "TriggerTable::action_type", "TriggerTable::auction_bid", "TriggerTable::author_id", "TriggerTable::bid_type", "TriggerTable::campaign_id", "TriggerTable::campaign_type", "TriggerTable::conv_num", "TriggerTable::cost", "TriggerTable::cpa_bid", "TriggerTable::delivery_timestamp", "TriggerTable::ecpm", "TriggerTable::gmv", "TriggerTable::inner_group_tag", "TriggerTable::is_soft", "TriggerTable::is_store_wide_roi_reco_conv", "TriggerTable::item_type", "TriggerTable::item_type_num", "TriggerTable::live_stream_id", "TriggerTable::medium_attribute", "TriggerTable::ocpc_action_type", "TriggerTable::pred_conv", "TriggerTable::pred_ctr_sum", "TriggerTable::pred_cvr_sum", "TriggerTable::price_after_billing_separate", "TriggerTable::price_before_billing_separate", "TriggerTable::price_ratio", "TriggerTable::promotion_type", "TriggerTable::record_gsp_price", "TriggerTable::roi_ratio", "TriggerTable::separate_gsp_price", "TriggerTable::speed_type", "TriggerTable::target_cost", "TriggerTable::target_gmv", "TriggerTable::unit_id"], "$input_item_attrs": ["AccountTagTable::bid_state_info_account_id", "AccountTagTable::bid_state_info_account_type", "AccountTagTable::bid_state_info_ad_status", "AccountTagTable::bid_state_info_advertisable", "AccountTagTable::bid_state_info_author_id", "AccountTagTable::bid_state_info_bid_strategy", "AccountTagTable::bid_state_info_bid_type", "AccountTagTable::bid_state_info_budget", "AccountTagTable::bid_state_info_campaign_id", "AccountTagTable::bid_state_info_campaign_type", "AccountTagTable::bid_state_info_cpa_bid", "AccountTagTable::bid_state_info_explore_budget", "AccountTagTable::bid_state_info_explore_budget_start_time", "AccountTagTable::bid_state_info_explore_budget_status", "AccountTagTable::bid_state_info_explore_time_period", "AccountTagTable::bid_state_info_is_live", "AccountTagTable::bid_state_info_is_status_open", "AccountTagTable::bid_state_info_left_budget", "AccountTagTable::bid_state_info_live_launch_type", "AccountTagTable::bid_state_info_live_stream_id", "AccountTagTable::bid_state_info_online", "AccountTagTable::bid_state_info_promotion_type", "AccountTagTable::bid_state_info_ptr", "AccountTagTable::bid_state_info_roi_ratio", "AccountTagTable::bid_state_info_speed_type", "AccountTagTable::has_bid_state_info_ptr", "TriggerTable::event_server_timestamp", "TriggerTable::message_seq"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerloopOcpmBidContextAccumulateMixer"}, "inner_loop_build_unit_list_mixer_76EC51": {"item_table": "TriggerTable", "account_id": {"item_table": "TriggerTable", "column": "account_id"}, "unit_id": {"item_table": "TriggerTable", "column": "unit_id"}, "group_tag": {"item_table": "TriggerTable", "column": "inner_group_tag"}, "account_unit_list": {"item_table": "AccountTagTable", "column": "unit_list"}, "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopBuildUnitListMixer"}, "inner_loop_table_lite_mixer_D1DC9A": {"item_table": "TriggerTable", "skip_wait_index": false, "cache_key_id": {"item_table": "TriggerTable", "column": "account_id"}, "unit_id": {"item_table": "TriggerTable", "column": "unit_id"}, "group_tag": {"item_table": "TriggerTable", "column": "inner_group_tag"}, "has_bid_state_info_ptr": {"item_table": "AccountTagTable", "column": "has_bid_state_info_ptr"}, "bid_state_info_ptr": {"item_table": "AccountTagTable", "column": "bid_state_info_ptr"}, "bid_state_info_explore_time_period": {"item_table": "AccountTagTable", "column": "bid_state_info_explore_time_period"}, "bid_state_info_explore_budget_start_time": {"item_table": "AccountTagTable", "column": "bid_state_info_explore_budget_start_time"}, "bid_state_info_explore_budget_status": {"item_table": "AccountTagTable", "column": "bid_state_info_explore_budget_status"}, "bid_state_info_explore_budget": {"item_table": "AccountTagTable", "column": "bid_state_info_explore_budget"}, "bid_state_info_is_live": {"item_table": "AccountTagTable", "column": "bid_state_info_is_live"}, "bid_state_info_ad_status": {"item_table": "AccountTagTable", "column": "bid_state_info_ad_status"}, "bid_state_info_advertisable": {"item_table": "AccountTagTable", "column": "bid_state_info_advertisable"}, "bid_state_info_is_status_open": {"item_table": "AccountTagTable", "column": "bid_state_info_is_status_open"}, "bid_state_info_online": {"item_table": "AccountTagTable", "column": "bid_state_info_online"}, "bid_state_info_left_budget": {"item_table": "AccountTagTable", "column": "bid_state_info_left_budget"}, "bid_state_info_budget": {"item_table": "AccountTagTable", "column": "bid_state_info_budget"}, "bid_state_info_account_type": {"item_table": "AccountTagTable", "column": "bid_state_info_account_type"}, "bid_state_info_bid_strategy": {"item_table": "AccountTagTable", "column": "bid_state_info_bid_strategy"}, "bid_state_info_bid_type": {"item_table": "AccountTagTable", "column": "bid_state_info_bid_type"}, "bid_state_info_live_launch_type": {"item_table": "AccountTagTable", "column": "bid_state_info_live_launch_type"}, "bid_state_info_account_id": {"item_table": "AccountTagTable", "column": "bid_state_info_account_id"}, "bid_state_info_campaign_id": {"item_table": "AccountTagTable", "column": "bid_state_info_campaign_id"}, "bid_state_info_author_id": {"item_table": "AccountTagTable", "column": "bid_state_info_author_id"}, "bid_state_info_live_stream_id": {"item_table": "AccountTagTable", "column": "bid_state_info_live_stream_id"}, "bid_state_info_speed_type": {"item_table": "AccountTagTable", "column": "bid_state_info_speed_type"}, "bid_state_info_promotion_type": {"item_table": "AccountTagTable", "column": "bid_state_info_promotion_type"}, "bid_state_info_campaign_type": {"item_table": "AccountTagTable", "column": "bid_state_info_campaign_type"}, "bid_state_info_cpa_bid": {"item_table": "AccountTagTable", "column": "bid_state_info_cpa_bid"}, "bid_state_info_roi_ratio": {"item_table": "AccountTagTable", "column": "bid_state_info_roi_ratio"}, "$metadata": {"$output_item_attrs": ["AccountTagTable::bid_state_info_account_id", "AccountTagTable::bid_state_info_ad_status", "AccountTagTable::bid_state_info_advertisable", "AccountTagTable::bid_state_info_author_id", "AccountTagTable::bid_state_info_budget", "AccountTagTable::bid_state_info_campaign_id", "AccountTagTable::bid_state_info_campaign_type", "AccountTagTable::bid_state_info_cpa_bid", "AccountTagTable::bid_state_info_explore_budget", "AccountTagTable::bid_state_info_explore_budget_start_time", "AccountTagTable::bid_state_info_explore_budget_status", "AccountTagTable::bid_state_info_explore_time_period", "AccountTagTable::bid_state_info_is_live", "AccountTagTable::bid_state_info_is_status_open", "AccountTagTable::bid_state_info_left_budget", "AccountTagTable::bid_state_info_live_stream_id", "AccountTagTable::bid_state_info_online", "AccountTagTable::bid_state_info_promotion_type", "AccountTagTable::bid_state_info_roi_ratio", "AccountTagTable::bid_state_info_speed_type", "AccountTagTable::has_bid_state_info_ptr"], "$input_item_attrs": ["group_tag", "unit_id"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopTableLiteMixer"}, "inner_loop_strategy_data_fetch_mixer_27A080": {"item_table": "TriggerTable", "cluster_name": "BidServerGraphInner", "candidate_cluster_name": "BidServerGraphTest", "key_prefix": "inner_account_ctx_:", "output_table_name": "AccountTagTable", "local_cache_key_id": {"item_table": "AccountTagTable", "column": "local_cache_key_id"}, "cache_key_id": {"item_table": "TriggerTable", "column": "account_id"}, "group_tag": {"item_table": "TriggerTable", "column": "inner_group_tag"}, "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopStrategyDataFetchMixer"}, "hot_message_sub_531C4B": {"item_table": "TriggerTable", "use_item_key": true, "freq_type": 2, "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "HotMessageSubMixer"}, "message_queue_retriever_5DEC82": {"max_queue_size": 10000, "queue_number": 200, "message_queue_type": 1, "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "MessageQueueRetriever"}, "_branch_controller_C183DC71": {"import_common_attr": ["diff_test_env"], "export_common_attr": ["_if_control_attr_1"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (diff_test_env ~= 1) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_C183DC71", "$code_info": "[if] 6B470B2E ad_log_inner_flow.py:89 in ad_log_flow_prepare_test(): .if_(\"diff_test_env ~= 1\")", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["diff_test_env"], "$output_common_attrs": ["_if_control_attr_1"], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_6FC38265": {"import_common_attr": ["check_table_empty"], "export_common_attr": ["_if_control_attr_2"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (check_table_empty == 1) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_6FC38265", "$code_info": "[if] C2A73F3F ad_log_inner_flow.py:174 in ad_log_flow(): .if_(\"check_table_empty == 1\")", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["check_table_empty"], "$output_common_attrs": ["_if_control_attr_2"], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_BE4773DF": {"import_common_attr": ["business_trigger_type"], "export_common_attr": ["_switch_control_attr_1"], "function_for_common": "evaluate", "lua_script": "function evaluate() if ((business_trigger_type) == 0) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_BE4773DF", "$code_info": "[case] BDF43C1C ad_log_inner_flow.py:178 in ad_log_flow(): .case_(0)", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["business_trigger_type"], "$output_common_attrs": ["_switch_control_attr_1"], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_6CBFD765": {"import_common_attr": ["_switch_control_attr_1", "message_enqueue_success"], "export_common_attr": ["_if_control_attr_3"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (_switch_control_attr_1 == 0 and (message_enqueue_success == 1)) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_6CBFD765", "$code_info": "[if] 95148D7E ad_log_inner_flow.py:205 in ad_log_flow(): .if_(\"message_enqueue_success == 1\")", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["_switch_control_attr_1", "message_enqueue_success"], "$output_common_attrs": ["_if_control_attr_3"], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_35CAD32E": {"import_common_attr": ["business_trigger_type"], "export_common_attr": ["_switch_control_attr_2"], "function_for_common": "evaluate", "lua_script": "function evaluate() if ((business_trigger_type) == 1) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_BE4773DF", "$code_info": "[case] C4EB8978 ad_log_inner_flow.py:212 in ad_log_flow(): .case_(1)", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["business_trigger_type"], "$output_common_attrs": ["_switch_control_attr_2"], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "ad_message_queue_dispatch_mixer_2D708A": {"task_queue_id": "{{subflow_shard_id}}", "message_queue_type": 1, "packed_common_attrs": ["subflow_shard_id", "total_msg_count"], "packed_table_columns": [{"table_name": "TriggerTable", "columns": ["ocpc_action_type", "action_type", "campaign_type", "promotion_type", "item_type", "author_id", "account_id", "live_stream_id", "unit_id", "campaign_id", "price", "cost_log", "callback_purchase_amount", "is_conversion", "is_single_commodity", "roi_ratio", "cpa_bid", "auto_roas", "delivery_timestamp", "bid_server_group_tag", "auto_cpa_bid", "medium_attribute", "cpm", "speed_type", "record_gsp_price", "separate_gsp_price", "auction_bid", "new_creative_tag", "pred_ctr_sum", "pred_cvr_sum", "ad_queue_type", "account_type", "bid_type", "bid_strategy", "payer_id", "log_process_timestamp", "is_spam_order", "charge_action_type", "is_for_report_engine", "gmv", "conv_num", "item_type_num", "inner_group_tag", "target_cost", "target_gmv", "price_ratio", "pred_conv", "ecpm", "cost", "message_seq", "event_server_timestamp", "price_before_billing_separate", "price_after_billing_separate", "is_soft", "is_store_wide_roi_reco_conv"]}], "skip": "{{_switch_control_attr_2}}", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": ["TriggerTable::account_id", "TriggerTable::account_type", "TriggerTable::action_type", "TriggerTable::ad_queue_type", "TriggerTable::auction_bid", "TriggerTable::author_id", "TriggerTable::auto_cpa_bid", "TriggerTable::auto_roas", "TriggerTable::bid_server_group_tag", "TriggerTable::bid_strategy", "TriggerTable::bid_type", "TriggerTable::callback_purchase_amount", "TriggerTable::campaign_id", "TriggerTable::campaign_type", "TriggerTable::charge_action_type", "TriggerTable::conv_num", "TriggerTable::cost", "TriggerTable::cost_log", "TriggerTable::cpa_bid", "TriggerTable::cpm", "TriggerTable::delivery_timestamp", "TriggerTable::ecpm", "TriggerTable::event_server_timestamp", "TriggerTable::gmv", "TriggerTable::inner_group_tag", "TriggerTable::is_conversion", "TriggerTable::is_for_report_engine", "TriggerTable::is_single_commodity", "TriggerTable::is_soft", "TriggerTable::is_spam_order", "TriggerTable::is_store_wide_roi_reco_conv", "TriggerTable::item_type", "TriggerTable::item_type_num", "TriggerTable::live_stream_id", "TriggerTable::log_process_timestamp", "TriggerTable::medium_attribute", "TriggerTable::message_seq", "TriggerTable::new_creative_tag", "TriggerTable::ocpc_action_type", "TriggerTable::payer_id", "TriggerTable::pred_conv", "TriggerTable::pred_ctr_sum", "TriggerTable::pred_cvr_sum", "TriggerTable::price", "TriggerTable::price_after_billing_separate", "TriggerTable::price_before_billing_separate", "TriggerTable::price_ratio", "TriggerTable::promotion_type", "TriggerTable::record_gsp_price", "TriggerTable::roi_ratio", "TriggerTable::separate_gsp_price", "TriggerTable::speed_type", "TriggerTable::target_cost", "TriggerTable::target_gmv", "TriggerTable::unit_id"], "$input_common_attrs": ["_switch_control_attr_2", "subflow_shard_id", "total_msg_count"], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "MessageQueueDispatchMixer"}, "hot_message_add_2A0E2E": {"item_table": "TriggerTable", "use_item_key": true, "freq_type": 2, "skip": "{{_if_control_attr_3}}", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["_if_control_attr_3"], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "HotMessageAddMixer"}, "ad_message_queue_dispatch_mixer_05340E": {"task_queue_id": "{{subflow_shard_id}}", "message_queue_type": 0, "packed_common_attrs": ["subflow_shard_id", "total_msg_count"], "packed_table_columns": [{"table_name": "TriggerTable", "columns": ["ocpc_action_type", "action_type", "campaign_type", "promotion_type", "item_type", "author_id", "account_id", "live_stream_id", "unit_id", "campaign_id", "price", "cost_log", "callback_purchase_amount", "is_conversion", "is_single_commodity", "roi_ratio", "cpa_bid", "auto_roas", "delivery_timestamp", "bid_server_group_tag", "auto_cpa_bid", "medium_attribute", "cpm", "speed_type", "record_gsp_price", "separate_gsp_price", "auction_bid", "new_creative_tag", "pred_ctr_sum", "pred_cvr_sum", "ad_queue_type", "account_type", "bid_type", "bid_strategy", "payer_id", "log_process_timestamp", "is_spam_order", "charge_action_type", "is_for_report_engine", "gmv", "conv_num", "item_type_num", "inner_group_tag", "target_cost", "target_gmv", "price_ratio", "pred_conv", "ecpm", "cost", "message_seq", "event_server_timestamp", "price_before_billing_separate", "price_after_billing_separate", "is_soft", "is_store_wide_roi_reco_conv"]}], "skip": "{{_switch_control_attr_1}}", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": ["TriggerTable::account_id", "TriggerTable::account_type", "TriggerTable::action_type", "TriggerTable::ad_queue_type", "TriggerTable::auction_bid", "TriggerTable::author_id", "TriggerTable::auto_cpa_bid", "TriggerTable::auto_roas", "TriggerTable::bid_server_group_tag", "TriggerTable::bid_strategy", "TriggerTable::bid_type", "TriggerTable::callback_purchase_amount", "TriggerTable::campaign_id", "TriggerTable::campaign_type", "TriggerTable::charge_action_type", "TriggerTable::conv_num", "TriggerTable::cost", "TriggerTable::cost_log", "TriggerTable::cpa_bid", "TriggerTable::cpm", "TriggerTable::delivery_timestamp", "TriggerTable::ecpm", "TriggerTable::event_server_timestamp", "TriggerTable::gmv", "TriggerTable::inner_group_tag", "TriggerTable::is_conversion", "TriggerTable::is_for_report_engine", "TriggerTable::is_single_commodity", "TriggerTable::is_soft", "TriggerTable::is_spam_order", "TriggerTable::is_store_wide_roi_reco_conv", "TriggerTable::item_type", "TriggerTable::item_type_num", "TriggerTable::live_stream_id", "TriggerTable::log_process_timestamp", "TriggerTable::medium_attribute", "TriggerTable::message_seq", "TriggerTable::new_creative_tag", "TriggerTable::ocpc_action_type", "TriggerTable::payer_id", "TriggerTable::pred_conv", "TriggerTable::pred_ctr_sum", "TriggerTable::pred_cvr_sum", "TriggerTable::price", "TriggerTable::price_after_billing_separate", "TriggerTable::price_before_billing_separate", "TriggerTable::price_ratio", "TriggerTable::promotion_type", "TriggerTable::record_gsp_price", "TriggerTable::roi_ratio", "TriggerTable::separate_gsp_price", "TriggerTable::speed_type", "TriggerTable::target_cost", "TriggerTable::target_gmv", "TriggerTable::unit_id"], "$input_common_attrs": ["_switch_control_attr_1", "subflow_shard_id", "total_msg_count"], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "MessageQueueDispatchMixer"}, "return__6A0259": {"status_code": 0, "skip": "{{_if_control_attr_2}}", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["_if_control_attr_2"], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoExecutionStatusEnricher"}, "ad_subflow_shard_id_enricher_16F9DE": {"item_table": "TriggerTable", "mod_dividend_column": "account_id", "mod_base": 1, "output_column": "subflow_shard_id", "check_table_empty": "check_table_empty", "$metadata": {"$output_item_attrs": ["subflow_shard_id"], "$input_item_attrs": ["account_id"], "$input_common_attrs": [], "$output_common_attrs": ["subflow_shard_id"], "$modify_item_tables": []}, "type_name": "AdSubflowShardIdEnricher"}, "msg_filter_mixer_D9C0BF": {"item_table": "TriggerTable", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": ["is_valid_message"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "inner_loop_msg_admit_835CEE": {"item_table": "TriggerTable", "business_trigger_type": "business_trigger_type", "$metadata": {"$output_item_attrs": ["is_valid_message", "message_seq"], "$input_item_attrs": ["account_id", "bid_server_group_tag", "campaign_type", "delivery_timestamp", "is_for_report_engine", "is_spam_order", "log_process_timestamp", "ocpc_action_type", "promotion_type", "speed_type", "unit_id"], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "InnerLoopMsgAdmitMixer"}, "ad_retrieval_dataframe_from_pb_E5D0CD": {"item_table": "TriggerTable", "from_column": "ad_log_dataframe_proto", "msg_timestamp_column": "kafka_msg_fetch_timestamp", "column_extract": [{"origin": "campaign_type", "target": "campaign_type"}, {"origin": "ocpc_action_type", "target": "ocpc_action_type"}, {"origin": "log_action_type", "target": "action_type"}, {"origin": "promotion_type", "target": "promotion_type"}, {"origin": "item_type", "target": "item_type"}, {"origin": "author_id", "target": "author_id"}, {"origin": "account_id", "target": "account_id"}, {"origin": "live_stream_id", "target": "live_stream_id"}, {"origin": "unit_id", "target": "unit_id"}, {"origin": "campaign_id", "target": "campaign_id"}, {"origin": "price", "target": "price"}, {"origin": "cost_log", "target": "cost_log"}, {"origin": "callback_purchase_amount", "target": "callback_purchase_amount"}, {"origin": "is_conversion", "target": "is_conversion"}, {"origin": "is_single_commodity", "target": "is_single_commodity"}, {"origin": "cpa_bid", "target": "cpa_bid"}, {"origin": "auto_roas", "target": "auto_roas"}, {"origin": "delivery_timestamp", "target": "delivery_timestamp"}, {"origin": "bid_server_group_tag", "target": "bid_server_group_tag"}, {"origin": "auto_cpa_bid", "target": "auto_cpa_bid"}, {"origin": "medium_attribute", "target": "medium_attribute"}, {"origin": "cpm", "target": "cpm"}, {"origin": "speed_type", "target": "speed_type"}, {"origin": "record_gsp_price", "target": "record_gsp_price"}, {"origin": "separate_gsp_price", "target": "separate_gsp_price"}, {"origin": "auction_bid", "target": "auction_bid"}, {"origin": "new_creative_tag", "target": "new_creative_tag"}, {"origin": "pred_ctr_sum", "target": "pred_ctr_sum"}, {"origin": "pred_cvr_sum", "target": "pred_cvr_sum"}, {"origin": "ad_queue_type", "target": "ad_queue_type"}, {"origin": "account_type", "target": "account_type"}, {"origin": "bid_type", "target": "bid_type"}, {"origin": "bid_strategy", "target": "bid_strategy"}, {"origin": "payer_id", "target": "payer_id"}, {"origin": "log_process_timestamp", "target": "log_process_timestamp"}, {"origin": "event_server_timestamp", "target": "event_server_timestamp"}, {"origin": "is_spam_order", "target": "is_spam_order"}, {"origin": "charge_action_type", "target": "charge_action_type"}, {"origin": "is_for_report_engine", "target": "is_for_report_engine"}, {"origin": "item_type_num", "target": "item_type_num"}, {"origin": "price_before_billing_separate", "target": "price_before_billing_separate"}, {"origin": "price_after_billing_separate", "target": "price_after_billing_separate"}, {"origin": "is_soft", "target": "is_soft"}, {"origin": "is_store_wide_roi_reco_conv", "target": "is_store_wide_roi_reco_conv"}, {"origin": "inner_cost", "target": "cost"}, {"origin": "inner_gmv", "target": "gmv"}, {"origin": "inner_conv_num", "target": "conv_num"}, {"origin": "inner_group_tag", "target": "inner_group_tag"}, {"origin": "inner_target_cost", "target": "target_cost"}, {"origin": "inner_target_gmv", "target": "target_gmv"}, {"origin": "inner_price_ratio", "target": "price_ratio"}, {"origin": "inner_record_gsp_price", "target": "record_gsp_price"}, {"origin": "inner_separate_gsp_price", "target": "separate_gsp_price"}, {"origin": "inner_pred_conv", "target": "pred_conv"}, {"origin": "inner_ecpm", "target": "ecpm"}, {"origin": "inner_roi_ratio", "target": "roi_ratio"}], "$metadata": {"$output_item_attrs": ["TriggerTable::account_id", "TriggerTable::account_type", "TriggerTable::action_type", "TriggerTable::ad_queue_type", "TriggerTable::auction_bid", "TriggerTable::author_id", "TriggerTable::auto_cpa_bid", "TriggerTable::auto_roas", "TriggerTable::bid_server_group_tag", "TriggerTable::bid_strategy", "TriggerTable::bid_type", "TriggerTable::callback_purchase_amount", "TriggerTable::campaign_id", "TriggerTable::campaign_type", "TriggerTable::charge_action_type", "TriggerTable::conv_num", "TriggerTable::cost", "TriggerTable::cost_log", "TriggerTable::cpa_bid", "TriggerTable::cpm", "TriggerTable::delivery_timestamp", "TriggerTable::ecpm", "TriggerTable::event_server_timestamp", "TriggerTable::gmv", "TriggerTable::inner_group_tag", "TriggerTable::is_conversion", "TriggerTable::is_for_report_engine", "TriggerTable::is_single_commodity", "TriggerTable::is_soft", "TriggerTable::is_spam_order", "TriggerTable::is_store_wide_roi_reco_conv", "TriggerTable::item_type", "TriggerTable::item_type_num", "TriggerTable::live_stream_id", "TriggerTable::log_process_timestamp", "TriggerTable::medium_attribute", "TriggerTable::new_creative_tag", "TriggerTable::ocpc_action_type", "TriggerTable::payer_id", "TriggerTable::pred_conv", "TriggerTable::pred_ctr_sum", "TriggerTable::pred_cvr_sum", "TriggerTable::price", "TriggerTable::price_after_billing_separate", "TriggerTable::price_before_billing_separate", "TriggerTable::price_ratio", "TriggerTable::promotion_type", "TriggerTable::record_gsp_price", "TriggerTable::roi_ratio", "TriggerTable::separate_gsp_price", "TriggerTable::speed_type", "TriggerTable::target_cost", "TriggerTable::target_gmv", "TriggerTable::unit_id"], "$input_item_attrs": [], "$input_common_attrs": ["TriggerTable", "ad_log_dataframe_proto", "kafka_msg_fetch_timestamp"], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "AdDeserializeProtoDataframeRetriever"}, "return__28C561": {"status_code": 0, "skip": "{{_if_control_attr_1}}", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": ["_if_control_attr_1"], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoExecutionStatusEnricher"}, "ad_kafka_message_fetch_enricher_CE4C37": {"use_shard": true, "kafka_consume_kconf_path": "ad.bidServer.dragonBidServerInnerLoopAdLogKafkaConfDiffTest", "kconf_path_kafka_start_ms": "ad.bidServer.noDiffAdLogKafkaStartTimestamp", "kconf_path_skip_flag": "ad.bidServer.noDiffAdInnerlogTail", "msg_timestamp_column": "kafka_msg_fetch_timestamp", "skip_tail": true, "out_column": "ad_log_dataframe_proto", "master_flag_column": "diff_test_env", "msg_type": "ad_log_msg", "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": [], "$output_common_attrs": ["ad_log_dataframe_proto"], "$modify_item_tables": []}, "type_name": "AdKafkaMessageFetchEnricher"}, "ad_runtime_env_enricher_E94D6B": {"run_env_out_column": "diff_test_env", "shard_id_out_column": "current_shard_id", "zk_configs": [{"dynamic_config_node": "zk_dragon_bidserver_inner_loop_main_test", "out_column": "zk_master_test_stat", "config_node_concat_shard_id": false}], "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": [], "$output_common_attrs": ["current_shard_id"], "$modify_item_tables": []}, "type_name": "AdRuntimeEnvEnricher"}, "sleep_859CB1": {"sleep_ms": 100000000, "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoSleepObserver"}, "data_prepare_init_mixter_FEAE49": {"use_bid_index": true, "p2p_name_attrs": ["conv_ratio_author_post_data"], "$metadata": {"$output_item_attrs": [], "$input_item_attrs": [], "$input_common_attrs": [], "$output_common_attrs": [], "$modify_item_tables": []}, "type_name": "DataPrepareInitMixer"}}}, "pipeline_map": {"unit_ocpm_workflow": {"__PARENT": "base_pipeline", "pipeline": ["message_queue_retriever_E2E0AF", "inner_loop_strategy_data_fetch_mixer_7342D7", "inner_loop_table_lite_mixer_C0E5EA", "inner_loop_ocpm_bid_context_mixer_28DDF2", "inner_loop_step_one_to_redis_mixer_66B10E", "inner_loop_strategy_data_save_mixer_37FA8E", "ad_data_frame_write_to_redis_observer_8422AB", "inner_loop_ocpm_trigger_mixer_85E8CF", "_branch_controller_3F21F243", "return__04FED1", "inner_loop_ocpm_calc_bid_prepare_mixer_0B7ACE", "_branch_controller_61BF1DD5", "inner_loop_strategy_data_save_mixer_DA0772", "return__D7A377", "inner_loop_ocpm_calc_pacing_rate_acc_mixer_58FE8A", "inner_loop_ocpm_calc_pacing_rate_normal_mixer_FD617B", "inner_loop_ocpm_check_pacing_rate_mixer_BB76FB", "inner_loop_ocpm_calc_bid_mixer_B15F7B", "innerloop_bid_result_store_96A878", "inner_loop_monitor_mixer_E92C81", "inner_loop_step_one_to_redis_mixer_927214", "inner_loop_strategy_data_save_mixer_37FA8E"]}, "account_ocpm_workflow": {"__PARENT": "base_pipeline", "pipeline": ["message_queue_retriever_5DEC82", "hot_message_sub_531C4B", "inner_loop_strategy_data_fetch_mixer_27A080", "inner_loop_strategy_data_fetch_mixer_7342D7", "inner_loop_table_lite_mixer_D1DC9A", "inner_loop_table_lite_mixer_C0E5EA", "inner_loop_build_unit_list_mixer_76EC51", "inner_loop_ocpm_bid_context_mixer_172558", "inner_loop_step_one_to_redis_mixer_615C81", "inner_loop_ocpm_bid_context_mixer_28DDF2", "inner_loop_step_one_to_redis_mixer_66B10E", "inner_loop_strategy_data_save_mixer_37FA8E", "inner_loop_strategy_data_save_mixer_6F5C52", "ad_data_frame_write_to_redis_observer_8422AB", "ad_data_frame_write_to_redis_observer_AC866C", "inner_loop_ocpm_trigger_mixer_764052", "_branch_controller_EC717578", "return__EDCBDA"]}, "adlog_flow": {"__PARENT": "base_pipeline", "pipeline": ["ad_runtime_env_enricher_E94D6B", "ad_kafka_message_fetch_enricher_CE4C37", "_branch_controller_C183DC71", "return__28C561", "ad_retrieval_dataframe_from_pb_E5D0CD", "inner_loop_msg_admit_835CEE", "msg_filter_mixer_D9C0BF", "ad_subflow_shard_id_enricher_16F9DE", "_branch_controller_6FC38265", "return__6A0259", "_branch_controller_BE4773DF", "ad_message_queue_dispatch_mixer_05340E", "_branch_controller_6CBFD765", "hot_message_add_2A0E2E", "_branch_controller_35CAD32E", "ad_message_queue_dispatch_mixer_2D708A"]}, "data_init": {"__PARENT": "base_pipeline", "pipeline": ["data_prepare_init_mixter_FEAE49", "sleep_859CB1"]}}}, "runner_pipeline_group": {"unit_ocpm": {"pipeline": ["unit_ocpm_workflow"], "thread_num": 1, "core_num_thread_ratio": 0.0}, "account_ocpm": {"pipeline": ["account_ocpm_workflow"], "thread_num": 1, "core_num_thread_ratio": 0.0}, "adlog": {"pipeline": ["adlog_flow"], "thread_num": 1, "core_num_thread_ratio": 0.0}, "data_init": {"pipeline": ["data_init"], "thread_num": 1, "core_num_thread_ratio": 0.0}}, "service_identifier": "ad-dragon-inner-loop-bid-server-test", "kess_config": {}, "subflow_worker_thread_task_queue_size": 1000, "sub_flow_worker_num": 500, "shard_num": 12, "zk_dragon_bidserver_inner_loop": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_inner_loop/master"}, "zk_dragon_bidserver_inner_loop_shard0": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_inner_loop_0/master"}, "zk_dragon_bidserver_inner_loop_shard1": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_inner_loop_1/master"}, "zk_dragon_bidserver_inner_loop_shard2": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_inner_loop_2/master"}, "zk_dragon_bidserver_inner_loop_shard3": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_inner_loop_3/master"}, "zk_dragon_bidserver_inner_loop_shard4": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_inner_loop_4/master"}, "zk_dragon_bidserver_inner_loop_shard5": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_inner_loop_5/master"}, "zk_dragon_bidserver_inner_loop_shard6": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_inner_loop_6/master"}, "zk_dragon_bidserver_inner_loop_shard7": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_inner_loop_7/master"}, "zk_dragon_bidserver_inner_loop_shard8": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_inner_loop_8/master"}, "zk_dragon_bidserver_inner_loop_shard9": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_inner_loop_9/master"}, "zk_dragon_bidserver_inner_loop_shard10": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_inner_loop_10/master"}, "zk_dragon_bidserver_inner_loop_shard11": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_inner_loop_11/master"}, "zk_dragon_bidserver_backup": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_backup/master"}, "zk_dragon_bidserver_inner_loop_test": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_bidServer_inner_loop_test/master"}, "leaf_flow_create_order": ["data_init", "reset", "unit_ocpm", "account_ocpm", "adlog"]}