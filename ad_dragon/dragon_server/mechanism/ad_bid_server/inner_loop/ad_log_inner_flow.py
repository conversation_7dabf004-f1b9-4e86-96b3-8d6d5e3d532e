#!/usr/bin/env python3
# coding=utf-8

import os
import sys
dragon_path=os.path.abspath('../../../../../../../dragon/')
ad_dragon_path=os.path.abspath('../../../../../ad_dragon/')
sys.path.append(dragon_path)
sys.path.append(ad_dragon_path)

from dragonfly.common_leaf_dsl import LeafFlow, OfflineRunner, LeafService, IndexSource
from ad_dragonfly.mechanism.ad_api_mixin import AdApiMixin

class AdLogInnerFlow(LeafFlow, AdApiMixin):
    def ad_log_flow_prepare(self):
        self.ad_get_runtime_env(
            no_check=True,
            run_env_out_column = "diff_test_env",
            shard_id_out_column = "current_shard_id",
            zk_configs = [
              dict(dynamic_config_node="zk_dragon_bidserver_inner_loop_main",
                    out_column="zk_master_stat",
                    config_node_concat_shard_id=True),
            ]) \
          .ad_fetch_dataframe_pb_from_kafka(
            kafka_consume_kconf_path = "ad.bidServer.dragonBidServerInnerLoopAdLogKafkaConf",
            use_shard = True,
            tag_config_test = dict (
              total_tag_num = 200,
              shard_0 = "0,100",
              shard_1 = "1,2,3,4,101,102,103,104",
              shard_2 = "5,6,7,8,9,10,11,12,13,105,106,107,108,109,110,111,112,113",
              shard_3 = "14,15,16,17,18,19,20,21,22,114,115,116,117,118,119,120,121,122",
              shard_4 = "23,24,25,26,27,28,29,30,31,123,124,125,126,127,128,129,130,131",
              shard_5 = "32,33,34,35,36,37,38,39,40,132,133,134,135,136,137,138,139,140",
              shard_6 = "41,42,43,44,45,46,47,48,49,141,142,143,144,145,146,147,148,149",
              shard_7 = "50,51,52,53,54,55,56,57,58,59,150,151,152,153,154,155,156,157,158,159",
              shard_8 = "60,61,62,63,64,65,66,67,68,69,160,161,162,163,164,165,166,167,168,169",
              shard_9 = "70,71,72,73,74,75,76,77,78,79,170,171,172,173,174,175,176,177,178,179",
              shard_10 = "80,81,82,83,84,85,86,87,88,89,180,181,182,183,184,185,186,187,188,189",
              shard_11 = "90,91,92,93,94,95,96,97,98,99,190,191,192,193,194,195,196,197,198,199",
            ),
            tag_config = dict (
              total_tag_num = 200,
              shard_0 = "0,100",
              shard_1 = "1,2,3,4,101,102,103,104",
              shard_2 = "5,6,7,8,9,10,11,12,13,105,106,107,108,109,110,111,112,113",
              shard_3 = "14,15,16,17,18,19,20,21,22,114,115,116,117,118,119,120,121,122",
              shard_4 = "23,24,25,26,27,28,29,30,31,123,124,125,126,127,128,129,130,131",
              shard_5 = "32,33,34,35,36,37,38,39,40,132,133,134,135,136,137,138,139,140",
              shard_6 = "41,42,43,44,45,46,47,48,49,141,142,143,144,145,146,147,148,149",
              shard_7 = "50,51,52,53,54,55,56,57,58,59,150,151,152,153,154,155,156,157,158,159",
              shard_8 = "60,61,62,63,64,65,66,67,68,69,160,161,162,163,164,165,166,167,168,169",
              shard_9 = "70,71,72,73,74,75,76,77,78,79,170,171,172,173,174,175,176,177,178,179",
              shard_10 = "80,81,82,83,84,85,86,87,88,89,180,181,182,183,184,185,186,187,188,189",
              shard_11 = "90,91,92,93,94,95,96,97,98,99,190,191,192,193,194,195,196,197,198,199",
            ),
            out_column = "ad_log_dataframe_proto",
            msg_timestamp_column = "kafka_msg_fetch_timestamp",
            master_flag_column = "zk_master_stat",
            msg_type = "ad_log_msg"
          )\
          .if_("zk_master_stat ~= 1") \
            .return_() \
          .end_if_()
        return self
    def ad_log_flow_prepare_test(self):
        self.ad_get_runtime_env(
            no_check=True,
            run_env_out_column = "diff_test_env",
            shard_id_out_column = "current_shard_id",
            zk_configs = [
              dict(dynamic_config_node="zk_dragon_bidserver_inner_loop_main_test",
                    out_column="zk_master_test_stat",
                    config_node_concat_shard_id=False),
            ]) \
          .ad_fetch_dataframe_pb_from_kafka(
            use_shard = True,
            kafka_consume_kconf_path = "ad.bidServer.dragonBidServerInnerLoopAdLogKafkaConfDiffTest",
            #kafka_start_ms = 1669273137000,
            kconf_path_kafka_start_ms = "ad.bidServer.noDiffAdLogKafkaStartTimestamp",
            kconf_path_skip_flag = "ad.bidServer.noDiffAdInnerlogTail",
            msg_timestamp_column = "kafka_msg_fetch_timestamp",
            skip_tail = True,
            out_column = "ad_log_dataframe_proto",
            master_flag_column = "diff_test_env",
            msg_type = "ad_log_msg"
        ) \
        .if_("diff_test_env ~= 1") \
          .return_() \
        .end_if_() 
        return self
    def ad_log_flow(self, message_queue_num):
        os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
        self.ad_retrieval_dataframe_from_pb(
            no_check=True,
            item_table = "TriggerTable",
            from_column = "ad_log_dataframe_proto",
            msg_timestamp_column = "kafka_msg_fetch_timestamp",
            column_extract = [
              dict(origin="campaign_type", target="campaign_type"),
              dict(origin="ocpc_action_type", target="ocpc_action_type"),
              dict(origin="log_action_type", target="action_type"),
              dict(origin="promotion_type", target="promotion_type"),
              dict(origin="item_type", target="item_type"),
              dict(origin="author_id", target="author_id"),
              dict(origin="account_id", target="account_id"),
              dict(origin="live_stream_id", target="live_stream_id"),
              dict(origin="unit_id", target="unit_id"),
              dict(origin="campaign_id", target="campaign_id"),
              dict(origin="price", target="price"),
              dict(origin="cost_log", target="cost_log"),
              dict(origin="callback_purchase_amount", target="callback_purchase_amount"),
              dict(origin="is_conversion", target="is_conversion"),
              dict(origin="is_single_commodity", target="is_single_commodity"),
              dict(origin="cpa_bid", target="cpa_bid"),
              dict(origin="auto_roas", target="auto_roas"),
              dict(origin="delivery_timestamp", target="delivery_timestamp"),
              dict(origin="bid_server_group_tag", target="bid_server_group_tag"),
              dict(origin="auto_cpa_bid", target="auto_cpa_bid"),
              dict(origin="medium_attribute", target="medium_attribute"),
              dict(origin="cpm", target="cpm"),
              dict(origin="speed_type", target="speed_type"),
              dict(origin="record_gsp_price", target="record_gsp_price"),
              dict(origin="separate_gsp_price", target="separate_gsp_price"),
              dict(origin="auction_bid", target="auction_bid"),
              dict(origin="new_creative_tag", target="new_creative_tag"),
              dict(origin="pred_ctr_sum", target="pred_ctr_sum"),
              dict(origin="pred_cvr_sum", target="pred_cvr_sum"),
              dict(origin="ad_queue_type", target="ad_queue_type"),
              dict(origin="account_type", target="account_type"),
              dict(origin="bid_type", target="bid_type"),
              dict(origin="bid_strategy", target="bid_strategy"),
              dict(origin="payer_id", target="payer_id"),
              dict(origin="log_process_timestamp", target="log_process_timestamp"),
              dict(origin="event_server_timestamp", target="event_server_timestamp"),
              dict(origin="is_spam_order", target="is_spam_order"),
              dict(origin="charge_action_type", target="charge_action_type"),
              dict(origin="is_for_report_engine", target="is_for_report_engine"),
              dict(origin="item_type_num", target="item_type_num"),
              dict(origin="price_before_billing_separate", target="price_before_billing_separate"),
              dict(origin="price_after_billing_separate", target="price_after_billing_separate"),
              dict(origin="is_soft", target="is_soft"),
              dict(origin="is_store_wide_roi_reco_conv", target="is_store_wide_roi_reco_conv"),

              dict(origin="inner_cost", target="cost"),
              dict(origin="inner_gmv", target="gmv"),
              dict(origin="inner_conv_num", target="conv_num"),
              dict(origin="inner_group_tag", target="inner_group_tag"),
              dict(origin="inner_target_cost", target="target_cost"),
              dict(origin="inner_target_gmv", target="target_gmv"),
              dict(origin="inner_price_ratio", target="price_ratio"),
              dict(origin="inner_record_gsp_price", target="record_gsp_price"),
              dict(origin="inner_separate_gsp_price", target="separate_gsp_price"),  
              dict(origin="inner_pred_conv", target="pred_conv"),
              dict(origin="inner_ecpm", target="ecpm"),
              dict(origin="inner_roi_ratio", target="roi_ratio"),
            ]) \
          .inner_loop_msg_admit_mixer(
            item_table = "TriggerTable",
            business_trigger_type = "business_trigger_type"
          ) \
          .msg_filter_mixer(
            item_table = "TriggerTable",
          ) \
          .ad_subflow_shard_id_enrich(
            item_table = "TriggerTable",
            mod_dividend_column = "account_id",
            mod_base = message_queue_num,
            output_column = "subflow_shard_id",
            check_table_empty = "check_table_empty",
            no_check=True
          ) \
          .if_("check_table_empty == 1") \
            .return_() \
          .end_if_() \
          .switch_("business_trigger_type") \
            .case_(0) \
              .ad_message_queue_dispatch_mixer( 
                task_queue_id="{{subflow_shard_id}}",
                message_queue_type=0,
                packed_common_attrs = [
                  "subflow_shard_id",
                  "total_msg_count"
                ],
                packed_table_columns = [
                  {
                    "table_name": "TriggerTable",
                    "columns": [
                        "ocpc_action_type", "action_type", "campaign_type", "promotion_type", "item_type",
                        "author_id", "account_id", "live_stream_id", "unit_id", "campaign_id", "price",
                        "cost_log", "callback_purchase_amount", "is_conversion", "is_single_commodity",
                        "roi_ratio", "cpa_bid", "auto_roas", "delivery_timestamp", "bid_server_group_tag",
                        "auto_cpa_bid", "medium_attribute", "cpm", "speed_type",
                        "record_gsp_price", "separate_gsp_price", "auction_bid", "new_creative_tag",
                        "pred_ctr_sum", "pred_cvr_sum", "ad_queue_type", "account_type",
                        "bid_type", "bid_strategy", "payer_id", "log_process_timestamp", "is_spam_order",
                        "charge_action_type", "is_for_report_engine", "gmv", "conv_num", "item_type_num",
                        "inner_group_tag", "target_cost", "target_gmv", "price_ratio", "pred_conv",
                        "ecpm", "cost", "message_seq", "event_server_timestamp","price_before_billing_separate",
                        "price_after_billing_separate", "is_soft", "is_store_wide_roi_reco_conv"],
                  }
                ]
              )\
              .if_("message_enqueue_success == 1") \
                .hot_message_add(
                  item_table = "TriggerTable",
                  use_item_key = True,
                  freq_type = 2,
                ) \
              .end_if_() \
            .case_(1) \
              .ad_message_queue_dispatch_mixer( 
                task_queue_id="{{subflow_shard_id}}",
                message_queue_type=1,
                packed_common_attrs = [
                  "subflow_shard_id",
                  "total_msg_count"
                ],
                packed_table_columns = [
                  {
                    "table_name": "TriggerTable",
                    "columns": [
                        "ocpc_action_type", "action_type", "campaign_type", "promotion_type", "item_type",
                        "author_id", "account_id", "live_stream_id", "unit_id", "campaign_id", "price",
                        "cost_log", "callback_purchase_amount", "is_conversion", "is_single_commodity",
                        "roi_ratio", "cpa_bid", "auto_roas", "delivery_timestamp", "bid_server_group_tag",
                        "auto_cpa_bid", "medium_attribute", "cpm", "speed_type",
                        "record_gsp_price", "separate_gsp_price", "auction_bid", "new_creative_tag",
                        "pred_ctr_sum", "pred_cvr_sum", "ad_queue_type", "account_type",
                        "bid_type", "bid_strategy", "payer_id", "log_process_timestamp", "is_spam_order",
                        "charge_action_type", "is_for_report_engine", "gmv", "conv_num", "item_type_num",
                        "inner_group_tag", "target_cost", "target_gmv", "price_ratio", "pred_conv",
                        "ecpm", "cost", "message_seq", "event_server_timestamp","price_before_billing_separate",
                        "price_after_billing_separate", "is_soft", "is_store_wide_roi_reco_conv"],
                  }
                ]
              )\
          .end_()
        return self
    def ad_ocpm_check_pacing(self):
      os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
      self.inner_loop_ocpm_check_pacing(
        item_table = "UnitTagTable",
        bid_context_attr_info = {
          "last_update_adjust_timestamp" : {"item_table" : "UnitTagTable", "column" :"last_update_adjust_timestamp"},
          "relax_roi_ratio" : {"item_table" : "UnitTagTable", "column" :"relax_roi_ratio"},
          "relax_cpa_bid" : {"item_table" : "UnitTagTable", "column" :"relax_cpa_bid"},
          "dynamic_lower_bound_roas" : {"item_table" : "UnitTagTable", "column" :"dynamic_lower_bound_roas"},
          "dynamic_upper_bound_roas" : {"item_table" : "UnitTagTable", "column" :"dynamic_upper_bound_roas"},
          "dynamic_lower_bound_cpa_bid" : {"item_table" : "UnitTagTable", "column" :"dynamic_lower_bound_cpa_bid"},
          "dynamic_upper_bound_cpa_bid" : {"item_table" : "UnitTagTable", "column" :"dynamic_upper_bound_cpa_bid"},
          "adjust_auto_value_rate" : {"item_table" : "UnitTagTable", "column" :"adjust_auto_value_rate"},
          "old_auto_cpa_bid" : {"item_table" : "UnitTagTable", "column" :"old_auto_cpa_bid"},
          "auto_cpa_bid" : {"item_table" : "UnitTagTable", "column" :"auto_cpa_bid"},
          "reach_upper_bound_timestamp" : {"item_table" : "UnitTagTable", "column" :"reach_upper_bound_timestamp"},
          "reach_lower_bound_timestamp" : {"item_table" : "UnitTagTable", "column" :"reach_lower_bound_timestamp"},
          "old_auto_roi_ratio" : {"item_table" : "UnitTagTable", "column" :"old_auto_roi_ratio"},
          "auto_roi_ratio" : {"item_table" : "UnitTagTable", "column" :"auto_roi_ratio"},
          "is_touch_bound" : {"item_table" : "UnitTagTable", "column" :"is_touch_bound"},
          "unit_id" : {"item_table" : "UnitTagTable", "column" :"unit_id"},
          "campaign_type" : {"item_table" : "UnitTagTable", "column" :"campaign_type"},
          "ocpc_action_type" : {"item_table" : "UnitTagTable", "column" :"ocpc_action_type"},
          "group_tag" : {"item_table" : "UnitTagTable", "column" :"group_tag"},
        },
        util_vals_attr_info = {
          "new_adjust_auto_value_rate" : {"item_table" : "UnitTagTable", "column" :"util_vals_new_adjust_auto_value_rate"},
          "is_roas" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_roas"},
          "is_debug" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_debug"},
          "is_roas_sub_pacing" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_roas_sub_pacing"},
          "enable_linear_pacing_grid_correction" : {"item_table" : "UnitTagTable", "column" :"util_vals_enable_linear_pacing_grid_correction"},
          "enable_cost_prior_algo" : {"item_table" : "UnitTagTable", "column" :"util_vals_enable_cost_prior_algo"},
          "is_target_modify" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_target_modify"},
          "target_modify_ratio" : {"item_table" : "UnitTagTable", "column" :"util_vals_target_modify_ratio"},
          "hard_upper_bound_auto_cpa_bid" : {"item_table" : "UnitTagTable", "column" :"util_vals_hard_upper_bound_auto_cpa_bid"},
          "hard_lower_bound_auto_cpa_bid" : {"item_table" : "UnitTagTable", "column" :"util_vals_hard_lower_bound_auto_cpa_bid"},
          "is_fanstop" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_fanstop"},
          "dynamic_bid_upper_bound_adjust_rate" : {"item_table" : "UnitTagTable", "column" :"util_vals_dynamic_bid_upper_bound_adjust_rate"},
          "dynamic_bid_lower_bound_adjust_rate" : {"item_table" : "UnitTagTable", "column" :"util_vals_dynamic_bid_lower_bound_adjust_rate"},
          "hard_lower_bound_auto_roas" : {"item_table" : "UnitTagTable", "column" :"util_vals_hard_lower_bound_auto_roas"},
          "hard_upper_bound_auto_roas" : {"item_table" : "UnitTagTable", "column" :"util_vals_hard_upper_bound_auto_roas"},
          "dynamic_bid_lower_bound_adjust_time_interval_ms" : {"item_table" : "UnitTagTable", "column" :"util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms"},
          "dynamic_bid_upper_bound_adjust_time_interval_ms" : {"item_table" : "UnitTagTable", "column" :"util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms"},
          "is_acc_explore_bid" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_acc_explore_bid"},
          "is_cold_start" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_cold_start"},
          "is_explore" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_explore"},
          "is_update_adjust" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_update_adjust"},
          "is_process" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_process"},
        },
        output_attr_info = {
          "new_adjust_auto_value_rate" : {"item_table" : "UnitTagTable", "column" :"util_vals_new_adjust_auto_value_rate"},
          "is_process" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_process"},
          "dynamic_bid_upper_bound_adjust_rate" : {"item_table" : "UnitTagTable", "column" :"util_vals_dynamic_bid_upper_bound_adjust_rate"},
          "dynamic_bid_lower_bound_adjust_rate" : {"item_table" : "UnitTagTable", "column" :"util_vals_dynamic_bid_lower_bound_adjust_rate"},

          "last_update_adjust_timestamp" : {"item_table" : "UnitTagTable", "column" :"last_update_adjust_timestamp"},
          "is_touch_bound" : {"item_table" : "UnitTagTable", "column" :"is_touch_bound"},
          "dynamic_upper_bound_roas" : {"item_table" : "UnitTagTable", "column" :"dynamic_upper_bound_roas"},
          "dynamic_lower_bound_roas" : {"item_table" : "UnitTagTable", "column" :"dynamic_lower_bound_roas"},
          "dynamic_upper_bound_cpa_bid" : {"item_table" : "UnitTagTable", "column" :"dynamic_upper_bound_cpa_bid"},
          "dynamic_lower_bound_cpa_bid" : {"item_table" : "UnitTagTable", "column" :"dynamic_lower_bound_cpa_bid"},
          "reach_upper_bound_timestamp" : {"item_table" : "UnitTagTable", "column" :"reach_upper_bound_timestamp"},
          "reach_lower_bound_timestamp" : {"item_table" : "UnitTagTable", "column" :"reach_lower_bound_timestamp"},
          "dynamic_bid_lower_bound_adjust_time_interval_ms" : {"item_table" : "UnitTagTable", "column" :"util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms"},
          "dynamic_bid_upper_bound_adjust_time_interval_ms" : {"item_table" : "UnitTagTable", "column" :"util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms"},
        }
      ) 
      return self

    def ad_ocpm_monitor_mixer(self):
      os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
      self.inner_loop_monitor_mixer(
        item_table = "UnitTagTable",
        bid_context_attr_info = {
          "unit_id" : {"item_table" : "UnitTagTable", "column" :"unit_id"},
          "account_id" : {"item_table" : "UnitTagTable", "column" :"account_id"},
          "author_id" : {"item_table" : "UnitTagTable", "column" :"author_id"},
          "campaign_type" : {"item_table" : "UnitTagTable", "column" :"campaign_type"},
          "ocpc_action_type" : {"item_table" : "UnitTagTable", "column" :"ocpc_action_type"},
          "group_tag" : {"item_table" : "UnitTagTable", "column" :"group_tag"},
          "item_type" : {"item_table" : "UnitTagTable", "column" :"item_type"},
          "msg_auto_cpa_bid" : {"item_table" : "UnitTagTable", "column" :"auto_cpa_bid"},
          "speed_type" : {"item_table" : "UnitTagTable", "column" :"speed_type"},
          "last_monitor_timestamp" : {"item_table" : "UnitTagTable", "column" :"last_monitor_timestamp"},
          "auto_roi_ratio" : {"item_table" : "UnitTagTable", "column" :"auto_roi_ratio"},
          "auto_cpa_bid" : {"item_table" : "UnitTagTable", "column" :"auto_cpa_bid"},
          "adjust_auto_value_rate" : {"item_table" : "UnitTagTable", "column" :"adjust_auto_value_rate"},
          "cost" : {"item_table" : "UnitTagTable", "column" :"cost"},
          "target_cost" : {"item_table" : "UnitTagTable", "column" :"target_cost"},
          "price_before_billing_separate" : {"item_table" : "UnitTagTable", "column" :"price_before_billing_separate"},
          "price_after_billing_separate" : {"item_table" : "UnitTagTable", "column" :"price_after_billing_separate"},
          "roi_ratio" : {"item_table" : "UnitTagTable", "column" :"roi_ratio"},
          "cpa_bid" : {"item_table" : "UnitTagTable", "column" :"cpa_bid"},
        },
        util_vals_attr_info = {
          "util_vals_is_monitor" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_monitor"},
          "util_vals_is_update_adjust" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_update_adjust"},
          "util_vals_ad_status_tag_change" : {"item_table" : "UnitTagTable", "column" :"util_vals_ad_status_tag_change"},
          "util_vals_ad_short_type" : {"item_table" : "UnitTagTable", "column" :"util_vals_ad_short_type"},
          "util_vals_is_debug" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_debug"},
          "util_vals_is_debug_on" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_debug_on"},
          "util_vals_is_roas" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_roas"},
          "util_vals_hard_bid_upper_bound_rate_monitor" : {"item_table" : "UnitTagTable", "column" :"util_vals_hard_bid_upper_bound_rate_monitor"},
          "util_vals_hard_bid_lower_bound_rate_monitor" : {"item_table" : "UnitTagTable", "column" :"util_vals_hard_bid_lower_bound_rate_monitor"},
          "util_vals_adjust_rate_lower_bound" : {"item_table" : "UnitTagTable", "column" :"util_vals_adjust_rate_lower_bound"},
          "util_vals_adjust_rate_upper_bound" : {"item_table" : "UnitTagTable", "column" :"util_vals_adjust_rate_upper_bound"},
          "util_vals_dynamic_bid_upper_bound_adjust_rate" : {"item_table" : "UnitTagTable", "column" :"util_vals_dynamic_bid_upper_bound_adjust_rate"},
          "util_vals_dynamic_bid_lower_bound_adjust_rate" : {"item_table" : "UnitTagTable", "column" :"util_vals_dynamic_bid_lower_bound_adjust_rate"},
          "util_vals_thread_id" : {"item_table" : "UnitTagTable", "column" :"util_vals_thread_id"},
          "util_vals_p_val" : {"item_table" : "UnitTagTable", "column" :"util_vals_p_val"},
          "util_vals_i_val" : {"item_table" : "UnitTagTable", "column" :"util_vals_i_val"},
          "util_vals_d_val" : {"item_table" : "UnitTagTable", "column" :"util_vals_d_val"},
          "util_vals_new_adjust_auto_value_rate" : {"item_table" : "UnitTagTable", "column" :"util_vals_new_adjust_auto_value_rate"},
          "util_vals_budget" : {"item_table" : "UnitTagTable", "column" :"util_vals_budget"},
          "util_vals_is_ocpm_bid_process" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_ocpm_bid_process"},
          "util_vals_is_cold_start" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_cold_start"},
        },
      ) 
      return self

    def ad_bid_result_store_mix(self):
      os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
      self.inner_loop_bid_result_store_mix(
        item_table = "UnitTagTable",
        redis_name = "BidServerGraphTest",
        # 测试跳过
        skip_wait_index = False,
        candidate_redis_name = "BidServerGraphTest",
        no_diff_redis = "adEngineFlowProcess",
        topic = "ad_auto_bid_table_test",
        trace_topic = "ad_auto_bid_table_test",
        auto_bid_topic = "ad_auto_bid_table_test",
        ttl = 86400,
        bid_context_attr_info = {
          "campaign_id" : {"item_table" : "UnitTagTable", "column" :"campaign_id"},
          "account_id" : {"item_table" : "UnitTagTable", "column" :"account_id"},
          "unit_id" : {"item_table" : "UnitTagTable", "column" :"unit_id"},
          "is_debug" : {"item_table" : "UnitTagTable", "column" :"is_debug"},
          "thread_id" : {"item_table" : "UnitTagTable", "column" :"thread_id"},
          "group_tag" : {"item_table" : "UnitTagTable", "column" :"group_tag"},
          "event_server_timestamp" : {"item_table" : "UnitTagTable", "column" :"event_server_timestamp"},
          "update_timestamp" : {"item_table" : "UnitTagTable", "column" :"update_timestamp"},
          "last_sync_result_timestamp" : {"item_table" : "UnitTagTable", "column" :"last_sync_result_timestamp"},
          "campaign_type" : {"item_table" : "UnitTagTable", "column" :"campaign_type"},
          "ocpc_action_type" : {"item_table" : "UnitTagTable", "column" :"ocpc_action_type"},
          "bid_context_total_cv" : {"item_table" : "UnitTagTable", "column" :"total_cv"},
          "bid_context_has_risk" : {"item_table" : "UnitTagTable", "column" :"has_risk"},
          "bid_context_cpa_bid" : {"item_table" : "UnitTagTable", "column" :"cpa_bid"},
          "bid_context_auto_roi_ratio" : {"item_table" : "UnitTagTable", "column" :"auto_roi_ratio"},
          "bid_context_target_roas" : {"item_table" : "UnitTagTable", "column" :"target_roas"},
          "bid_context_total_pay" : {"item_table" : "UnitTagTable", "column" :"total_pay"},
          "bid_context_deep_flow_control_rate" : {"item_table" : "UnitTagTable", "column" :"deep_flow_control_rate"},
          "bid_context_current_deep_cpa" : {"item_table" : "UnitTagTable", "column" :"current_deep_cpa"},
          "bid_context_deep_threshold" : {"item_table" : "UnitTagTable", "column" :"deep_threshold"},
          "bid_context_deep_min_bid_coef" : {"item_table" : "UnitTagTable", "column" :"deep_min_bid_coef"},
          "bid_context_is_cold_start_explore" : {"item_table" : "UnitTagTable", "column" :"is_cold_start_explore"},
          "bid_context_acc_cold_start_explore_status" : {"item_table" : "UnitTagTable", "column" :"acc_cold_start_explore_status"},
          "bid_context_group_tag_exp_start_ts" : {"item_table" : "UnitTagTable", "column" :"group_tag_exp_start_ts"},
          "bid_context_cost_ratio_adjust_context_auto_cost_ratio" : {"item_table" : "UnitTagTable", "column" :"cost_ratio_adjust_context_auto_cost_ratio"},
          "bid_context_softad_diff_ratio" : {"item_table" : "UnitTagTable", "column" :"softad_diff_ratio"},
          "bid_context_conv_ratio_adjust_context_auto_conv_ratio" : {"item_table" : "UnitTagTable", "column" :"conv_ratio_adjust_context_auto_conv_ratio"},
          "bid_context_price_bid_ratio" : {"item_table" : "UnitTagTable", "column" :"price_bid_ratio"},
          "bid_context_second_coef" : {"item_table" : "UnitTagTable", "column" :"second_coef"},
          "bid_context_first_coef" : {"item_table" : "UnitTagTable", "column" :"first_coef"},
          "bid_context_r_calibration" : {"item_table" : "UnitTagTable", "column" :"r_calibration"},
          "bid_context_ocpm_inner_strategy_tag" : {"item_table" : "UnitTagTable", "column" :"ocpm_inner_strategy_tag"},
          "bid_context_auto_atv" : {"item_table" : "UnitTagTable", "column" :"auto_atv"},
          "bid_context_cost" : {"item_table" : "UnitTagTable", "column" :"cost"},
          "bid_context_target_cost" : {"item_table" : "UnitTagTable", "column" :"target_cost"},
          "bid_context_ad_off_target_cost" : {"item_table" : "UnitTagTable", "column" :"ad_off_target_cost"},
          "bid_context_auto_cpa_bid" : {"item_table" : "UnitTagTable", "column" :"auto_cpa_bid"},
          "bid_context_roi_ratio" : {"item_table" : "UnitTagTable", "column" :"roi_ratio"},
        },
        util_vals_attr_info = {
          "util_vals_ad_status_tag" : {"item_table" : "UnitTagTable", "column" :"util_vals_ad_status_tag"},
          "util_vals_is_update_adjust" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_update_adjust"},
          "util_vals_is_no_bid" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_no_bid"},
          "util_vals_is_sync_to_dsp" : {"item_table" : "UnitTagTable", "column" :"util_vals_is_sync_to_dsp"},
          "util_vals_ad_status_tag_change" : {"item_table" : "UnitTagTable", "column" :"util_vals_ad_status_tag_change"},
          "util_vals_ad_off_target_cost" : {"item_table" : "UnitTagTable", "column" :"util_vals_ad_off_target_cost"},
        },
        lowest_cost_context = {
          "lowest_cost_context_current_budget" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_current_budget"},
          "lowest_cost_context_past_pv_ratio" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_past_pv_ratio"},
          "lowest_cost_context_past_cost_ratio" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_past_cost_ratio"},
          "lowest_cost_context_is_explore_msg" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_is_explore_msg"},
          "lowest_cost_context_is_campaign_bid" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_is_campaign_bid"},
          "lowest_cost_context_first_log_timestamp" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_first_log_timestamp"},
          "lowest_cost_context_schedule_time_type" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_schedule_time_type"},
          "lowest_cost_context_is_same_date" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_is_same_date"},
          "lowest_cost_context_total_cv" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_total_cv"},
          "lowest_cost_context_target_cpa" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_target_cpa"},
          "lowest_cost_context_expected_cost" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_expected_cost"},
          "lowest_cost_context_is_init" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_is_init"},
          "lowest_cost_context_allocated_budget_unify" : {"item_table" : "UnitTagTable", "column" :"lowest_cost_context_allocated_budget_unify"},
        },
        bid_state_info = {
          "bid_state_info_online" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_online"},
          "bid_state_info_advertisable" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_advertisable"},
          "bid_state_info_ad_status" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_ad_status"},
          "bid_state_info_is_live" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_is_live"},
          "bid_state_info_is_status_open" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_is_status_open"},
          "bid_state_info_campaign_id" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_campaign_id"},
          "bid_state_info_campaign_type" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_campaign_type"},
          "bid_state_info_begin_time" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_begin_time"},
          "bid_state_info_end_time" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_end_time"},
          "bid_state_info_speed_type" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_speed_type"},
          "bid_state_info_promotion_type" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_promotion_type"},
          "bid_state_info_account_type" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_account_type"},
          "bid_state_info_bid_strategy" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_bid_strategy"},
          "bid_state_info_live_launch_type" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_live_launch_type"},
          "bid_state_info_author_id" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_author_id"},
          "bid_state_info_day_budget" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_day_budget"},
          "bid_state_info_budget" : {"item_table" : "UnitTagTable", "column" :"bid_state_info_budget"},
        },
      ) 
      return self

    def ad_step_two_to_redis(self, tag_table_name):
      os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
      self.inner_loop_step_one_write_to_redis(
          item_table = tag_table_name,
          redis_name = "adEngineFlowProcess",
          is_step_two = True,
          ttl = 86400,
          #mock
          has_bid_state_info_ptr = {"item_table" : "TriggerTable", "column" : "has_bid_state_info_ptr"},
          bid_state_info_ptr = {"item_table" : "TriggerTable", "column" : "bid_state_info_ptr"},
          message_seq = {"item_table" : "TriggerTable", "column" : "message_seq"},
          event_server_timestamp = {"item_table" : "TriggerTable", "column" : "event_server_timestamp"},
          unit_id = {"item_table" : tag_table_name, "column" : "unit_id"},
          msg_attr_info = {
          "msg_campaign_type" : {"item_table" : "TriggerTable", "column" :"campaign_type"},
          "msg_ocpc_action_type" : {"item_table" : "TriggerTable", "column" :"ocpc_action_type"},
          "msg_promotion_type" : {"item_table" : "TriggerTable", "column" :"promotion_type"},
          "msg_item_type" : {"item_table" : "TriggerTable", "column" :"item_type"},
          "msg_author_id" : {"item_table" : "TriggerTable", "column" :"author_id"},
          "msg_account_id" : {"item_table" : "TriggerTable", "column" :"account_id"},
          "msg_live_stream_id" : {"item_table" : "TriggerTable", "column" :"live_stream_id"},
          "msg_unit_id" : {"item_table" : "TriggerTable", "column" :"unit_id"},
          "msg_conv_num" : {"item_table" : "TriggerTable", "column" :"conv_num"},
          "msg_campaign_id" : {"item_table" : "TriggerTable", "column" :"campaign_id"},
          "msg_item_type_num" : {"item_table" : "TriggerTable", "column" :"item_type_num"},
          "msg_cost" : {"item_table" : "TriggerTable", "column" :"cost"},
          "msg_gmv" : {"item_table" : "TriggerTable", "column" :"gmv"},
          "msg_roi_ratio" : {"item_table" : "TriggerTable", "column" :"roi_ratio"},
          "msg_action_type" : {"item_table" : "TriggerTable", "column" :"action_type"},
          "msg_group_tag" : {"item_table" : "TriggerTable", "column" :"inner_group_tag"},
          "msg_bid_type" : {"item_table" : "TriggerTable", "column" :"bid_type"},
          "msg_medium_attribute" : {"item_table" : "TriggerTable", "column" :"medium_attribute"},
          "msg_speed_type" : {"item_table" : "TriggerTable", "column" :"speed_type"},
          "msg_delivery_timestamp" : {"item_table" : "TriggerTable", "column" :"delivery_timestamp"},
          "msg_cpa_bid" : {"item_table" : "TriggerTable", "column" :"cpa_bid"},
          "msg_is_soft" : {"item_table" : "TriggerTable", "column" :"is_soft"},
          "msg_target_cost" : {"item_table" : "TriggerTable", "column" :"target_cost"},
          "msg_target_gmv" : {"item_table" : "TriggerTable", "column" :"target_gmv"},
          "msg_separate_gsp_price" : {"item_table" : "TriggerTable", "column" :"separate_gsp_price"},
          "msg_record_gsp_price" : {"item_table" : "TriggerTable", "column" :"record_gsp_price"},
          "msg_pred_conv" : {"item_table" : "TriggerTable", "column" :"pred_conv"},
          "msg_pred_cvr_sum" : {"item_table" : "TriggerTable", "column" :"pred_cvr_sum"},
          "msg_pred_ctr_sum" : {"item_table" : "TriggerTable", "column" :"pred_ctr_sum"},
          "msg_price_before_billing_separate" : {"item_table" : "TriggerTable", "column" :"price_before_billing_separate"},
          "msg_price_after_billing_separate" : {"item_table" : "TriggerTable", "column" :"price_after_billing_separate"},
          "msg_ecpm" : {"item_table" : "TriggerTable", "column" :"ecpm"},
          "msg_auction_bid" : {"item_table" : "TriggerTable", "column" :"auction_bid"},
          "msg_price_ratio" : {"item_table" : "TriggerTable", "column" :"price_ratio"},
        },
        bid_ctx_attr_info = {
          "bid_ctx_bid_context_key" : {"item_table" : tag_table_name, "column" :"bid_context_key"},
          "bid_ctx_group_tag" : {"item_table" : tag_table_name, "column" :"group_tag"},
          "bid_ctx_campaign_type" : {"item_table" : tag_table_name, "column" :"campaign_type"},
          "bid_ctx_ocpc_action_type" : {"item_table" : tag_table_name, "column" :"ocpc_action_type"},
          "bid_ctx_promotion_type" : {"item_table" : tag_table_name, "column" :"promotion_type"},
          "bid_ctx_item_type" : {"item_table" : tag_table_name, "column" :"item_type"},
          "bid_ctx_first_industry_name" : {"item_table" : tag_table_name, "column" :"first_industry_name"},
          "bid_ctx_group_tag_num" : {"item_table" : tag_table_name, "column" :"group_tag_num"},
          "bid_ctx_unit_id" : {"item_table" : tag_table_name, "column" :"unit_id"},
          "bid_ctx_account_id" : {"item_table" : tag_table_name, "column" :"account_id"},
          "bid_ctx_author_id" : {"item_table" : tag_table_name, "column" :"author_id"},
          "bid_ctx_live_stream_id" : {"item_table" : tag_table_name, "column" :"live_stream_id"},
          "bid_ctx_item_type_num" : {"item_table" : tag_table_name, "column" :"item_type_num"},
          "bid_ctx_campaign_id" : {"item_table" : tag_table_name, "column" :"campaign_id"},
          "bid_ctx_price_before_billing_separate" : {"item_table" : tag_table_name, "column" :"price_before_billing_separate"},
          "bid_ctx_price_after_billing_separate" : {"item_table" : tag_table_name, "column" :"price_after_billing_separate"},
          "bid_ctx_cost" : {"item_table" : tag_table_name, "column" :"cost"},
          "bid_ctx_gmv" : {"item_table" : tag_table_name, "column" :"gmv"},
          "bid_ctx_conv_num" : {"item_table" : tag_table_name, "column" :"conv_num"},
          "bid_ctx_cost_count" : {"item_table" : tag_table_name, "column" :"cost_count"},
          "bid_ctx_gmv_count" : {"item_table" : tag_table_name, "column" :"gmv_count"},
          "bid_ctx_delivery_cnt" : {"item_table" : tag_table_name, "column" :"delivery_cnt"},
          "bid_ctx_cpa_bid" : {"item_table" : tag_table_name, "column" :"cpa_bid"},
          "bid_ctx_relax_cpa_bid" : {"item_table" : tag_table_name, "column" :"relax_cpa_bid"},
          "bid_ctx_roi_ratio" : {"item_table" : tag_table_name, "column" :"roi_ratio"},
          "bid_ctx_relax_roi_ratio" : {"item_table" : tag_table_name, "column" :"relax_roi_ratio"},
          "bid_ctx_rt_cpa_bid" : {"item_table" : tag_table_name, "column" :"rt_cpa_bid"},
          "bid_ctx_rt_roas" : {"item_table" : tag_table_name, "column" :"rt_roas"},
          "bid_ctx_auto_cpa_bid" : {"item_table" : tag_table_name, "column" :"auto_cpa_bid"},
          "bid_ctx_auto_roi_ratio" : {"item_table" : tag_table_name, "column" :"auto_roi_ratio"},
          "bid_ctx_old_auto_cpa_bid" : {"item_table" : tag_table_name, "column" :"old_auto_cpa_bid"},
          "bid_ctx_old_auto_roi_ratio" : {"item_table" : tag_table_name, "column" :"old_auto_roi_ratio"},
          "bid_ctx_adjust_rate" : {"item_table" : tag_table_name, "column" :"adjust_rate"},
          "bid_ctx_rt_cost_speed" : {"item_table" : tag_table_name, "column" :"rt_cost_speed"},
          "bid_ctx_rt_delivery_speed" : {"item_table" : tag_table_name, "column" :"rt_delivery_speed"},
          "bid_ctx_dynamic_lower_bound_roas" : {"item_table" : tag_table_name, "column" :"dynamic_lower_bound_roas"},
          "bid_ctx_dynamic_upper_bound_roas" : {"item_table" : tag_table_name, "column" :"dynamic_upper_bound_roas"},
          "bid_ctx_dynamic_lower_bound_cpa_bid" : {"item_table" : tag_table_name, "column" :"dynamic_lower_bound_cpa_bid"},
          "bid_ctx_dynamic_upper_bound_cpa_bid" : {"item_table" : tag_table_name, "column" :"dynamic_upper_bound_cpa_bid"},
          "bid_ctx_target_cost" : {"item_table" : tag_table_name, "column" :"target_cost"},
          "bid_ctx_target_gmv" : {"item_table" : tag_table_name, "column" :"target_gmv"},
          "bid_ctx_record_gsp_price" : {"item_table" : tag_table_name, "column" :"record_gsp_price"},
          "bid_ctx_last_update_cost" : {"item_table" : tag_table_name, "column" :"last_update_cost"},
          "bid_ctx_adjust_auto_atv_rate" : {"item_table" : tag_table_name, "column" :"adjust_auto_atv_rate"},
          "bid_ctx_old_adjust_auto_atv_rate" : {"item_table" : tag_table_name, "column" :"old_adjust_auto_atv_rate"},
          "bid_ctx_reach_lower_bound_timestamp" : {"item_table" : tag_table_name, "column" :"reach_lower_bound_timestamp"},
          "bid_ctx_reach_upper_bound_timestamp" : {"item_table" : tag_table_name, "column" :"reach_upper_bound_timestamp"},
          "bid_ctx_first_delivery_timestamp_ms" : {"item_table" : tag_table_name, "column" :"first_delivery_timestamp_ms"},
          "bid_ctx_last_delivery_timestamp_ms" : {"item_table" : tag_table_name, "column" :"last_delivery_timestamp_ms"},
          "is_touch_bound" : {"item_table" : tag_table_name, "column" :"is_touch_bound"},
          "bid_ctx_last_update_adjust_timestamp" : {"item_table" : tag_table_name, "column" :"last_update_adjust_timestamp"},
          "bid_ctx_cost_start_timestamp_ms" : {"item_table" : tag_table_name, "column" :"cost_start_timestamp_ms"},
          "bid_ctx_last_update_context_timestamp" : {"item_table" : tag_table_name, "column" :"last_update_context_timestamp"},
          "bid_ctx_last_sync_context_timestamp" : {"item_table" : tag_table_name, "column" :"last_sync_context_timestamp"},
          "bid_ctx_online" : {"item_table" : tag_table_name, "column" :"online"},
          "bid_ctx_is_cold_start" : {"item_table" : tag_table_name, "column" :"is_cold_start"},
          "bid_ctx_speed_type" : {"item_table" : tag_table_name, "column" :"speed_type"},
          "bid_ctx_context_start_timestamp" : {"item_table" : tag_table_name, "column" :"context_start_timestamp"},
          "bid_ctx_last_sync_result_timestamp" : {"item_table" : tag_table_name, "column" :"last_sync_result_timestamp"},
          "bid_ctx_adjust_auto_value_rate" : {"item_table" : tag_table_name, "column" :"adjust_auto_value_rate"},
          "bid_ctx_total_ecpm" : {"item_table" : tag_table_name, "column" :"total_ecpm"},
          "bid_ctx_separate_gsp_price" : {"item_table" : tag_table_name, "column" :"separate_gsp_price"},
          "bid_ctx_total_auction_bid" : {"item_table" : tag_table_name, "column" :"total_auction_bid"},
          "bid_ctx_dry_up_base_value" : {"item_table" : tag_table_name, "column" :"dry_up_base_value"},
          "bid_ctx_ctr_sum" : {"item_table" : tag_table_name, "column" :"ctr_sum"},
          "bid_ctx_cvr_sum" : {"item_table" : tag_table_name, "column" :"cvr_sum"},
          "bid_ctx_softad_cost" : {"item_table" : tag_table_name, "column" :"softad_cost"},
          "bid_ctx_softad_target_cost" : {"item_table" : tag_table_name, "column" :"softad_target_cost"},
          "bid_ctx_softad_diff_ratio" : {"item_table" : tag_table_name, "column" :"softad_diff_ratio"},
          "bid_ctx_last_monitor_timestamp" : {"item_table" : tag_table_name, "column" :"last_monitor_timestamp"},
          "bid_ctx_user_cost_prior_algo" : {"item_table" : tag_table_name, "column" :"user_cost_prior_algo"},
          "bid_ctx_ad_status_tag" : {"item_table" : tag_table_name, "column" :"ad_status_tag"},
          "bid_ctx_old_is_out_of_budget" : {"item_table" : tag_table_name, "column" :"old_is_out_of_budget"},
          "bid_ctx_last_is_ad_open" : {"item_table" : tag_table_name, "column" :"last_is_ad_open"},
          "bid_ctx_last_ad_valid" : {"item_table" : tag_table_name, "column" :"last_ad_valid"},
          
          "bid_ctx_sync_context_interval_batch_batch_cost" : {"item_table" : tag_table_name, "column" :"sync_context_interval_batch_batch_cost"},
          "bid_ctx_sync_context_interval_batch_batch_gmv" : {"item_table" : tag_table_name, "column" :"sync_context_interval_batch_batch_gmv"},
          "bid_ctx_sync_context_interval_batch_batch_start_timestamp" : {"item_table" : tag_table_name, "column" :"sync_context_interval_batch_batch_start_timestamp"},
          "bid_ctx_cost_speed_context_batch_start_timestamp_ms" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_start_timestamp_ms"},
          "bid_ctx_cost_speed_context_batch_value_sum" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_value_sum"},
          "bid_ctx_cost_speed_context_batch_count" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_count"},
          "bid_ctx_cost_speed_context_batch_value_mean" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_value_mean"},
          "bid_ctx_cost_speed_context_batch_value_speed" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_value_speed"},
          "bid_ctx_delivery_speed_context_batch_start_timestamp_ms" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_start_timestamp_ms"},
          "bid_ctx_delivery_speed_context_batch_value_sum" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_value_sum"},
          "bid_ctx_delivery_speed_context_batch_count" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_count"},
          "bid_ctx_delivery_speed_context_batch_value_mean" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_value_mean"},
          "bid_ctx_delivery_speed_context_batch_value_speed" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_value_speed"},
          "bid_ctx_conv_ratio_adjust_context_pred_conv" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_pred_conv"},
          "bid_ctx_conv_ratio_adjust_context_old_auto_conv_ratio" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_old_auto_conv_ratio"},
          "bid_ctx_conv_ratio_adjust_context_auto_conv_ratio" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_auto_conv_ratio"},
          "bid_ctx_conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio"},
          "bid_ctx_conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio"},
          "bid_ctx_conv_ratio_adjust_context_real_conv" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_real_conv"},
          "bid_ctx_conv_ratio_adjust_context_last_update_conv_ratio_timestamp" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_last_update_conv_ratio_timestamp"},
          "bid_ctx_conv_ratio_adjust_context_reach_r1_upper_bound_timestamp" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_reach_r1_upper_bound_timestamp"},
          "bid_ctx_conv_ratio_adjust_context_reach_r1_lower_bound_timestamp" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_reach_r1_lower_bound_timestamp"},
          "bid_ctx_conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp"},
          "bid_ctx_price_ratio_context_batch_start_timestamp_ms" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_start_timestamp_ms"},
          "bid_ctx_price_ratio_context_batch_total_price" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_total_price"},
          "bid_ctx_price_ratio_context_batch_total_ecpm" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_total_ecpm"},
          "bid_ctx_price_ratio_context_batch_total_price_ratio" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_total_price_ratio"},
          "bid_ctx_price_ratio_context_batch_count" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_count"},
          "bid_ctx_price_ratio_context_batch_avg_price_ratio" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_avg_price_ratio"},
          "bid_ctx_price_ratio_context_batch_price_ratio_mean" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_price_ratio_mean"},
          "bid_ctx_item_type_info_photo_to_live_cnt" : {"item_table" : tag_table_name, "column" :"item_type_info_photo_to_live_cnt"},
          "bid_ctx_item_type_info_direct_live_cnt" : {"item_table" : tag_table_name, "column" :"item_type_info_direct_live_cnt"},
          "bid_ctx_item_type_info_photo_to_live_cost" : {"item_table" : tag_table_name, "column" :"item_type_info_photo_to_live_cost"},
          "bid_ctx_item_type_info_direct_live_cost" : {"item_table" : tag_table_name, "column" :"item_type_info_direct_live_cost"},
        },
        process_utils_attr_info = {
          "process_utils_ad_off_target_cost" : {"item_table" : tag_table_name, "column" :"util_vals_ad_off_target_cost"},
          "process_utils_new_adjust_auto_value_rate" : {"item_table" : tag_table_name, "column" :"util_vals_new_adjust_auto_value_rate"},
          "process_utils_old_adjust_auto_value_rate" : {"item_table" : tag_table_name, "column" :"util_vals_old_adjust_auto_value_rate"},
          "process_utils_is_bid_ptr_null" : {"item_table" : tag_table_name, "column" :"util_vals_is_bid_ptr_null"},
          "process_utils_pro_ocpc_not_been_set" : {"item_table" : tag_table_name, "column" :"util_vals_pro_ocpc_not_been_set"},
          "process_utils_reset_pacing" : {"item_table" : tag_table_name, "column" :"util_vals_reset_pacing"},
          "process_utils_is_cost_target_msg" : {"item_table" : tag_table_name, "column" :"util_vals_is_cost_target_msg"},
          "process_utils_enable_cost_prior_algo" : {"item_table" : tag_table_name, "column" :"util_vals_enable_cost_prior_algo"},
          "process_utils_enable_linear_pacing_cold_start_low_bound" : {"item_table" : tag_table_name, "column" :"util_vals_enable_linear_pacing_cold_start_low_bound"},
          "process_utils_enable_linear_pacing_grid_correction" : {"item_table" : tag_table_name, "column" :"util_vals_enable_linear_pacing_grid_correction"},
          "process_utils_enable_linear_adaptive_update_time" : {"item_table" : tag_table_name, "column" :"util_vals_enable_linear_adaptive_update_time"},
          "process_utils_enable_linear_after_cold_start_no_conv_drop" : {"item_table" : tag_table_name, "column" :"util_vals_enable_linear_after_cold_start_no_conv_drop"},
          "process_utils_disable_day_reset_exp" : {"item_table" : tag_table_name, "column" :"util_vals_disable_day_reset_exp"},
          "process_utils_enable_univ_inner_ad_delivery_replace" : {"item_table" : tag_table_name, "column" :"util_vals_enable_univ_inner_ad_delivery_replace"},
          "process_utils_is_apply_adjust" : {"item_table" : tag_table_name, "column" :"util_vals_is_apply_adjust"},
          "process_utils_is_debug" : {"item_table" : tag_table_name, "column" :"util_vals_is_debug"},
          "process_utils_is_roas_sub_pacing" : {"item_table" : tag_table_name, "column" :"util_vals_is_roas_sub_pacing"},
          "process_utils_is_ocpm_bid_process" : {"item_table" : tag_table_name, "column" :"util_vals_is_ocpm_bid_process"},
          "process_utils_is_apply_operation_config" : {"item_table" : tag_table_name, "column" :"util_vals_is_apply_operation_config"},
          "process_utils_is_start_process" : {"item_table" : tag_table_name, "column" :"util_vals_is_start_process"},
          "process_utils_is_debug_on" : {"item_table" : tag_table_name, "column" :"util_vals_is_debug_on"},
          "process_utils_is_normal" : {"item_table" : tag_table_name, "column" :"util_vals_is_normal"},
          "process_utils_is_fanstop" : {"item_table" : tag_table_name, "column" :"util_vals_is_fanstop"},
          "process_utils_is_fanstop_ocpm" : {"item_table" : tag_table_name, "column" :"util_vals_is_fanstop_ocpm"},
          "process_utils_is_roas" : {"item_table" : tag_table_name, "column" :"util_vals_is_roas"},
          "process_utils_is_no_bid" : {"item_table" : tag_table_name, "column" :"util_vals_is_no_bid"},
          "process_utils_is_skip_update" : {"item_table" : tag_table_name, "column" :"util_vals_is_skip_update"},
          "process_utils_is_nothing_to_do" : {"item_table" : tag_table_name, "column" :"util_vals_is_nothing_to_do"},
          "process_utils_is_process" : {"item_table" : tag_table_name, "column" :"util_vals_is_process"},
          "process_utils_is_reset_context" : {"item_table" : tag_table_name, "column" :"util_vals_is_reset_context"},
          "process_utils_is_update_adjust" : {"item_table" : tag_table_name, "column" :"util_vals_is_update_adjust"},
          "process_utils_is_interval_cost" : {"item_table" : tag_table_name, "column" :"util_vals_is_interval_cost"},
          "process_utils_is_update_interval_ms" : {"item_table" : tag_table_name, "column" :"util_vals_is_update_interval_ms"},
          "process_utils_is_ad_open" : {"item_table" : tag_table_name, "column" :"util_vals_is_ad_open"},
          "process_utils_is_sync_to_dsp" : {"item_table" : tag_table_name, "column" :"util_vals_is_sync_to_dsp"},
          "process_utils_is_monitor" : {"item_table" : tag_table_name, "column" :"util_vals_is_monitor"},
          "process_utils_is_cold_start" : {"item_table" : tag_table_name, "column" :"util_vals_is_cold_start"},
          "process_utils_is_costcap_nobid" : {"item_table" : tag_table_name, "column" :"util_vals_is_costcap_nobid"},
          "process_utils_is_skip_cold_start" : {"item_table" : tag_table_name, "column" :"util_vals_is_skip_cold_start"},
          "process_utils_is_on_live" : {"item_table" : tag_table_name, "column" :"util_vals_is_on_live"},
          "process_utils_is_out_of_budget" : {"item_table" : tag_table_name, "column" :"util_vals_is_out_of_budget"},
          "process_utils_old_is_out_of_budget" : {"item_table" : tag_table_name, "column" :"util_vals_old_is_out_of_budget"},
          "process_utils_advertisable" : {"item_table" : tag_table_name, "column" :"util_vals_advertisable"},
          "process_utils_is_status_open" : {"item_table" : tag_table_name, "column" :"util_vals_is_status_open"},
          "process_utils_ad_status" : {"item_table" : tag_table_name, "column" :"util_vals_ad_status"},
          "process_utils_unit_id" : {"item_table" : tag_table_name, "column" :"util_vals_unit_id"},
          "process_utils_thread_id" : {"item_table" : tag_table_name, "column" :"util_vals_thread_id"},
          "process_utils_is_bid_info" : {"item_table" : tag_table_name, "column" :"util_vals_is_bid_info"},
          "process_utils_is_online" : {"item_table" : tag_table_name, "column" :"util_vals_is_online"},
          "process_utils_is_explore" : {"item_table" : tag_table_name, "column" :"util_vals_is_explore"},
          "process_utils_ab_exp_ratio" : {"item_table" : tag_table_name, "column" :"util_vals_ab_exp_ratio"},
          "process_utils_promotion_type_num" : {"item_table" : tag_table_name, "column" :"util_vals_promotion_type_num"},
          "process_utils_item_type_num" : {"item_table" : tag_table_name, "column" :"util_vals_item_type_num"},
          "process_utils_prior_cost" : {"item_table" : tag_table_name, "column" :"util_vals_prior_cost"},
          "process_utils_cold_start_cost_li_thr" : {"item_table" : tag_table_name, "column" :"util_vals_cold_start_cost_li_thr"},
          "process_utils_cold_start_low_bound" : {"item_table" : tag_table_name, "column" :"util_vals_cold_start_low_bound"},
          "process_utils_rt_cost_speed" : {"item_table" : tag_table_name, "column" :"util_vals_rt_cost_speed"},
          "process_utils_rt_delivery_speed" : {"item_table" : tag_table_name, "column" :"util_vals_rt_delivery_speed"},
          "process_utils_target_roas" : {"item_table" : tag_table_name, "column" :"util_vals_target_roas"},
          "process_utils_is_price_ratio_bound" : {"item_table" : tag_table_name, "column" :"util_vals_is_price_ratio_bound"},
          "process_utils_is_acc_explore_bid" : {"item_table" : tag_table_name, "column" :"util_vals_is_acc_explore_bid"},
          "process_utils_dynamic_bid_lower_bound_adjust_time_interval_ms" : {"item_table" : tag_table_name, "column" :"util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms"},
          "process_utils_dynamic_bid_upper_bound_adjust_time_interval_ms" : {"item_table" : tag_table_name, "column" :"util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms"},
          "process_utils_dynamic_bid_lower_bound_adjust_rate" : {"item_table" : tag_table_name, "column" :"util_vals_dynamic_bid_lower_bound_adjust_rate"},
          "process_utils_dynamic_bid_upper_bound_adjust_rate" : {"item_table" : tag_table_name, "column" :"util_vals_dynamic_bid_upper_bound_adjust_rate"},
          "process_utils_p_weight" : {"item_table" : tag_table_name, "column" :"util_vals_p_weight"},
          "process_utils_i_weight" : {"item_table" : tag_table_name, "column" :"util_vals_i_weight"},
          "process_utils_d_weight" : {"item_table" : tag_table_name, "column" :"util_vals_d_weight"},
          "process_utils_explore_p_weight" : {"item_table" : tag_table_name, "column" :"util_vals_explore_p_weight"},
          "process_utils_explore_i_weight" : {"item_table" : tag_table_name, "column" :"util_vals_explore_i_weight"},
          "process_utils_explore_d_weight" : {"item_table" : tag_table_name, "column" :"util_vals_explore_d_weight"},
          "process_utils_p_val" : {"item_table" : tag_table_name, "column" :"util_vals_p_val"},
          "process_utils_i_val" : {"item_table" : tag_table_name, "column" :"util_vals_i_val"},
          "process_utils_d_val" : {"item_table" : tag_table_name, "column" :"util_vals_d_val"},
          "process_utils_pacing_weight" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_weight"},
          "process_utils_target_cost_speed" : {"item_table" : tag_table_name, "column" :"util_vals_target_cost_speed"},
          "process_utils_target_delivery_speed" : {"item_table" : tag_table_name, "column" :"util_vals_target_delivery_speed"},
          "process_utils_start_bid_rate" : {"item_table" : tag_table_name, "column" :"util_vals_start_bid_rate"},
          "process_utils_start_bid_rate_sub_pacing" : {"item_table" : tag_table_name, "column" :"util_vals_start_bid_rate_sub_pacing"},
          "process_utils_hard_upper_bound_auto_cpa_bid" : {"item_table" : tag_table_name, "column" :"util_vals_hard_upper_bound_auto_cpa_bid"},
          "process_utils_hard_lower_bound_auto_cpa_bid" : {"item_table" : tag_table_name, "column" :"util_vals_hard_lower_bound_auto_cpa_bid"},
          "process_utils_hard_lower_bound_auto_roas" : {"item_table" : tag_table_name, "column" :"util_vals_hard_lower_bound_auto_roas"},
          "process_utils_hard_upper_bound_auto_roas" : {"item_table" : tag_table_name, "column" :"util_vals_hard_upper_bound_auto_roas"},
          "process_utils_hard_lower_bound_auto_atv" : {"item_table" : tag_table_name, "column" :"util_vals_hard_lower_bound_auto_atv"},
          "process_utils_hard_upper_bound_auto_atv" : {"item_table" : tag_table_name, "column" :"util_vals_hard_upper_bound_auto_atv"},
          "process_utils_acc_explore_bid_target_cost_speed" : {"item_table" : tag_table_name, "column" :"util_vals_acc_explore_bid_target_cost_speed"},
          "process_utils_current_ms" : {"item_table" : tag_table_name, "column" :"util_vals_current_ms"},
          "process_utils_ad_short_type" : {"item_table" : tag_table_name, "column" :"util_vals_ad_short_type"},
          "process_utils_is_target_modify" : {"item_table" : tag_table_name, "column" :"util_vals_is_target_modify"},
          "process_utils_target_modify_ratio" : {"item_table" : tag_table_name, "column" :"util_vals_target_modify_ratio"},
          "process_utils_p_weight_conv" : {"item_table" : tag_table_name, "column" :"util_vals_p_weight_conv"},
          "process_utils_i_weight_conv" : {"item_table" : tag_table_name, "column" :"util_vals_i_weight_conv"},
          "process_utils_d_weight_conv" : {"item_table" : tag_table_name, "column" :"util_vals_d_weight_conv"},
          "process_utils_p_val_conv" : {"item_table" : tag_table_name, "column" :"util_vals_p_val_conv"},
          "process_utils_i_val_conv" : {"item_table" : tag_table_name, "column" :"util_vals_i_val_conv"},
          "process_utils_d_val_conv" : {"item_table" : tag_table_name, "column" :"util_vals_d_val_conv"},
          "process_utils_pacing_weight_conv" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_weight_conv"},
          "process_utils_p_weight_cost" : {"item_table" : tag_table_name, "column" :"util_vals_p_weight_cost"},
          "process_utils_i_weight_cost" : {"item_table" : tag_table_name, "column" :"util_vals_i_weight_cost"},
          "process_utils_d_weight_cost" : {"item_table" : tag_table_name, "column" :"util_vals_d_weight_cost"},
          "process_utils_p_val_cost" : {"item_table" : tag_table_name, "column" :"util_vals_p_val_cost"},
          "process_utils_i_val_cost" : {"item_table" : tag_table_name, "column" :"util_vals_i_val_cost"},
          "process_utils_d_val_cost" : {"item_table" : tag_table_name, "column" :"util_vals_d_val_cost"},
          "process_utils_pacing_weight_cost" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_weight_cost"},
          "process_utils_update_interval_ms" : {"item_table" : tag_table_name, "column" :"util_vals_update_interval_ms"},
          "process_utils_interval_cost" : {"item_table" : tag_table_name, "column" :"util_vals_interval_cost"},
          "process_utils_step_bid_rate_increase" : {"item_table" : tag_table_name, "column" :"util_vals_step_bid_rate_increase"},
          "process_utils_step_bid_rate_descent" : {"item_table" : tag_table_name, "column" :"util_vals_step_bid_rate_descent"},
          "process_utils_ad_status_tag" : {"item_table" : tag_table_name, "column" :"util_vals_ad_status_tag"},
          "process_utils_ad_status_tag_change" : {"item_table" : tag_table_name, "column" :"util_vals_ad_status_tag_change"},
          "process_utils_total_pred_conv" : {"item_table" : tag_table_name, "column" :"util_vals_total_pred_conv"},
          "process_utils_adjust_rate_lower_bound" : {"item_table" : tag_table_name, "column" :"util_vals_adjust_rate_lower_bound"},
          "process_utils_adjust_rate_upper_bound" : {"item_table" : tag_table_name, "column" :"util_vals_adjust_rate_upper_bound"},
          "process_utils_budget" : {"item_table" : tag_table_name, "column" :"util_vals_budget"},
          "process_utils_hard_bid_upper_bound_rate_monitor" : {"item_table" : tag_table_name, "column" :"util_vals_hard_bid_upper_bound_rate_monitor"},
          "process_utils_hard_bid_lower_bound_rate_monitor" : {"item_table" : tag_table_name, "column" :"util_vals_hard_bid_lower_bound_rate_monitor"},
          "process_utils_fanstop_adjust_rate_lower_bound_roi" : {"item_table" : tag_table_name, "column" :"util_vals_fanstop_adjust_rate_lower_bound_roi"},
          "process_utils_fanstop_adjust_rate_upper_bound_roi" : {"item_table" : tag_table_name, "column" :"util_vals_fanstop_adjust_rate_upper_bound_roi"},
          "process_utils_fanstop_adjust_rate_lower_bound_cpa" : {"item_table" : tag_table_name, "column" :"util_vals_fanstop_adjust_rate_lower_bound_cpa"},
          "process_utils_fanstop_adjust_rate_upper_bound_cpa" : {"item_table" : tag_table_name, "column" :"util_vals_fanstop_adjust_rate_upper_bound_cpa"},
          "process_utils_pacing_type" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_type"},
          "process_utils_pacing_method_type" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_method_type"},
        },
      )
      return self

    def ad_bid_ctx_accumulate(self, tag_table_name, data_level):
      os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
      self.inner_loop_ocpm_bid_context_accumulate(
        item_table = "TriggerTable",
        message_seq = {"item_table" : "TriggerTable", "column" : "message_seq"},
        event_server_timestamp = {"item_table" : "TriggerTable", "column" : "event_server_timestamp"},
        bid_data_level = data_level,
        bid_state_info = {
          "bid_state_info_has_bid_state_info_ptr" : {"item_table" : tag_table_name, "column" : "has_bid_state_info_ptr"},
          "bid_state_info_ptr" :  {"item_table" : tag_table_name, "column" : "bid_state_info_ptr"},
          "bid_state_info_explore_time_period" :  {"item_table" : tag_table_name, "column" : "bid_state_info_explore_time_period"},
          "bid_state_info_explore_budget_start_time" :  {"item_table" : tag_table_name, "column" : "bid_state_info_explore_budget_start_time"},
          "bid_state_info_explore_budget_status" :  {"item_table" : tag_table_name, "column" : "bid_state_info_explore_budget_status"},
          "bid_state_info_explore_budget" :  {"item_table" : tag_table_name, "column" : "bid_state_info_explore_budget"},
          "bid_state_info_is_live" :  {"item_table" : tag_table_name, "column" : "bid_state_info_is_live"},
          "bid_state_info_ad_status" : {"item_table" : tag_table_name, "column" : "bid_state_info_ad_status"},
          "bid_state_info_advertisable" :  {"item_table" : tag_table_name, "column" : "bid_state_info_advertisable"},
          "bid_state_info_is_status_open" :  {"item_table" : tag_table_name, "column" : "bid_state_info_is_status_open"},
          "bid_state_info_online" :  {"item_table" : tag_table_name, "column" : "bid_state_info_online"},
          "bid_state_info_left_budget" :  {"item_table" : tag_table_name, "column" : "bid_state_info_left_budget"},
          "bid_state_info_budget" :  {"item_table" : tag_table_name, "column" : "bid_state_info_budget"},
          "bid_state_info_account_type" :  {"item_table" : tag_table_name, "column" : "bid_state_info_account_type"},
          "bid_state_info_bid_strategy" :  {"item_table" : tag_table_name, "column" : "bid_state_info_bid_strategy"},
          "bid_state_info_bid_type" :  {"item_table" : tag_table_name, "column" : "bid_state_info_bid_type"},
          "bid_state_info_live_launch_type" :  {"item_table" : tag_table_name, "column" : "bid_state_info_live_launch_type"},

          "bid_state_info_account_id" :  {"item_table" : tag_table_name, "column" : "bid_state_info_account_id"},
          "bid_state_info_campaign_id" :  {"item_table" : tag_table_name, "column" : "bid_state_info_campaign_id"},
          "bid_state_info_author_id" : {"item_table" : tag_table_name, "column" : "bid_state_info_author_id"},
          "bid_state_info_live_stream_id" :  {"item_table" : tag_table_name, "column" : "bid_state_info_live_stream_id"},
          "bid_state_info_speed_type" :  {"item_table" : tag_table_name, "column" : "bid_state_info_speed_type"},
          "bid_state_info_promotion_type" :  {"item_table" : tag_table_name, "column" : "bid_state_info_promotion_type"},
          "bid_state_info_campaign_type" : {"item_table" : tag_table_name, "column" : "bid_state_info_campaign_type"},
          "bid_state_info_cpa_bid" :  {"item_table" : tag_table_name, "column" : "bid_state_info_cpa_bid"},
          "bid_state_info_roi_ratio" :  {"item_table" : tag_table_name, "column" : "bid_state_info_roi_ratio"},
        },
        msg_attr_info = {
          "msg_campaign_type" : {"item_table" : "TriggerTable", "column" :"campaign_type"},
          "msg_ocpc_action_type" : {"item_table" : "TriggerTable", "column" :"ocpc_action_type"},
          "msg_promotion_type" : {"item_table" : "TriggerTable", "column" :"promotion_type"},
          "msg_item_type" : {"item_table" : "TriggerTable", "column" :"item_type"},
          "msg_author_id" : {"item_table" : "TriggerTable", "column" :"author_id"},
          "msg_account_id" : {"item_table" : "TriggerTable", "column" :"account_id"},
          "msg_live_stream_id" : {"item_table" : "TriggerTable", "column" :"live_stream_id"},
          "msg_unit_id" : {"item_table" : "TriggerTable", "column" :"unit_id"},
          "msg_conv_num" : {"item_table" : "TriggerTable", "column" :"conv_num"},
          "msg_campaign_id" : {"item_table" : "TriggerTable", "column" :"campaign_id"},
          "msg_item_type_num" : {"item_table" : "TriggerTable", "column" :"item_type_num"},
          "msg_cost" : {"item_table" : "TriggerTable", "column" :"cost"},
          "msg_gmv" : {"item_table" : "TriggerTable", "column" :"gmv"},
          "msg_roi_ratio" : {"item_table" : "TriggerTable", "column" :"roi_ratio"},
          "msg_action_type" : {"item_table" : "TriggerTable", "column" :"action_type"},
          "msg_group_tag" : {"item_table" : "TriggerTable", "column" :"inner_group_tag"},
          "msg_bid_type" : {"item_table" : "TriggerTable", "column" :"bid_type"},
          "msg_medium_attribute" : {"item_table" : "TriggerTable", "column" :"medium_attribute"},
          "msg_speed_type" : {"item_table" : "TriggerTable", "column" :"speed_type"},
          "msg_delivery_timestamp" : {"item_table" : "TriggerTable", "column" :"delivery_timestamp"},
          "msg_cpa_bid" : {"item_table" : "TriggerTable", "column" :"cpa_bid"},
          "msg_is_soft" : {"item_table" : "TriggerTable", "column" :"is_soft"},
          "msg_target_cost" : {"item_table" : "TriggerTable", "column" :"target_cost"},
          "msg_target_gmv" : {"item_table" : "TriggerTable", "column" :"target_gmv"},
          "msg_separate_gsp_price" : {"item_table" : "TriggerTable", "column" :"separate_gsp_price"},
          "msg_record_gsp_price" : {"item_table" : "TriggerTable", "column" :"record_gsp_price"},
          "msg_pred_conv" : {"item_table" : "TriggerTable", "column" :"pred_conv"},
          "msg_pred_cvr_sum" : {"item_table" : "TriggerTable", "column" :"pred_cvr_sum"},
          "msg_pred_ctr_sum" : {"item_table" : "TriggerTable", "column" :"pred_ctr_sum"},
          "msg_price_before_billing_separate" : {"item_table" : "TriggerTable", "column" :"price_before_billing_separate"},
          "msg_price_after_billing_separate" : {"item_table" : "TriggerTable", "column" :"price_after_billing_separate"},
          "msg_ecpm" : {"item_table" : "TriggerTable", "column" :"ecpm"},
          "msg_auction_bid" : {"item_table" : "TriggerTable", "column" :"auction_bid"},
          "msg_price_ratio" : {"item_table" : "TriggerTable", "column" :"price_ratio"},
          "msg_is_store_wide_roi_reco_conv" : {"item_table" : "TriggerTable", "column" :"is_store_wide_roi_reco_conv"},
        },
        bid_ctx_attr_info = {
          "bid_ctx_bid_context_key" : {"item_table" : tag_table_name, "column" :"bid_context_key"},
          "bid_ctx_group_tag" : {"item_table" : tag_table_name, "column" :"group_tag"},
          "bid_ctx_campaign_type" : {"item_table" : tag_table_name, "column" :"campaign_type"},
          "bid_ctx_ocpc_action_type" : {"item_table" : tag_table_name, "column" :"ocpc_action_type"},
          "bid_ctx_promotion_type" : {"item_table" : tag_table_name, "column" :"promotion_type"},
          "bid_ctx_item_type" : {"item_table" : tag_table_name, "column" :"item_type"},
          "bid_ctx_first_industry_name" : {"item_table" : tag_table_name, "column" :"first_industry_name"},
          "bid_ctx_group_tag_num" : {"item_table" : tag_table_name, "column" :"group_tag_num"},
          "bid_ctx_unit_id" : {"item_table" : tag_table_name, "column" :"unit_id"},
          "bid_ctx_account_id" : {"item_table" : tag_table_name, "column" :"account_id"},
          "bid_ctx_author_id" : {"item_table" : tag_table_name, "column" :"author_id"},
          "bid_ctx_live_stream_id" : {"item_table" : tag_table_name, "column" :"live_stream_id"},
          "bid_ctx_item_type_num" : {"item_table" : tag_table_name, "column" :"item_type_num"},
          "bid_ctx_campaign_id" : {"item_table" : tag_table_name, "column" :"campaign_id"},
          "bid_ctx_price_before_billing_separate" : {"item_table" : tag_table_name, "column" :"price_before_billing_separate"},
          "bid_ctx_price_after_billing_separate" : {"item_table" : tag_table_name, "column" :"price_after_billing_separate"},
          "bid_ctx_cost" : {"item_table" : tag_table_name, "column" :"cost"},
          "bid_ctx_gmv" : {"item_table" : tag_table_name, "column" :"gmv"},
          "bid_ctx_conv_num" : {"item_table" : tag_table_name, "column" :"conv_num"},
          "bid_ctx_cost_count" : {"item_table" : tag_table_name, "column" :"cost_count"},
          "bid_ctx_gmv_count" : {"item_table" : tag_table_name, "column" :"gmv_count"},
          "bid_ctx_delivery_cnt" : {"item_table" : tag_table_name, "column" :"delivery_cnt"},
          "bid_ctx_cpa_bid" : {"item_table" : tag_table_name, "column" :"cpa_bid"},
          "bid_ctx_relax_cpa_bid" : {"item_table" : tag_table_name, "column" :"relax_cpa_bid"},
          "bid_ctx_roi_ratio" : {"item_table" : tag_table_name, "column" :"roi_ratio"},
          "bid_ctx_relax_roi_ratio" : {"item_table" : tag_table_name, "column" :"relax_roi_ratio"},
          "bid_ctx_rt_cpa_bid" : {"item_table" : tag_table_name, "column" :"rt_cpa_bid"},
          "bid_ctx_rt_roas" : {"item_table" : tag_table_name, "column" :"rt_roas"},
          "bid_ctx_auto_cpa_bid" : {"item_table" : tag_table_name, "column" :"auto_cpa_bid"},
          "bid_ctx_auto_roi_ratio" : {"item_table" : tag_table_name, "column" :"auto_roi_ratio"},
          "bid_ctx_old_auto_cpa_bid" : {"item_table" : tag_table_name, "column" :"old_auto_cpa_bid"},
          "bid_ctx_old_auto_roi_ratio" : {"item_table" : tag_table_name, "column" :"old_auto_roi_ratio"},
          "bid_ctx_adjust_rate" : {"item_table" : tag_table_name, "column" :"adjust_rate"},
          "bid_ctx_rt_cost_speed" : {"item_table" : tag_table_name, "column" :"rt_cost_speed"},
          "bid_ctx_rt_delivery_speed" : {"item_table" : tag_table_name, "column" :"rt_delivery_speed"},
          "bid_ctx_dynamic_lower_bound_roas" : {"item_table" : tag_table_name, "column" :"dynamic_lower_bound_roas"},
          "bid_ctx_dynamic_upper_bound_roas" : {"item_table" : tag_table_name, "column" :"dynamic_upper_bound_roas"},
          "bid_ctx_dynamic_lower_bound_cpa_bid" : {"item_table" : tag_table_name, "column" :"dynamic_lower_bound_cpa_bid"},
          "bid_ctx_dynamic_upper_bound_cpa_bid" : {"item_table" : tag_table_name, "column" :"dynamic_upper_bound_cpa_bid"},
          "bid_ctx_target_cost" : {"item_table" : tag_table_name, "column" :"target_cost"},
          "bid_ctx_target_gmv" : {"item_table" : tag_table_name, "column" :"target_gmv"},
          "bid_ctx_record_gsp_price" : {"item_table" : tag_table_name, "column" :"record_gsp_price"},
          "bid_ctx_reach_lower_bound_timestamp" : {"item_table" : tag_table_name, "column" :"reach_lower_bound_timestamp"},
          "bid_ctx_reach_upper_bound_timestamp" : {"item_table" : tag_table_name, "column" :"reach_upper_bound_timestamp"},
          "bid_ctx_first_delivery_timestamp_ms" : {"item_table" : tag_table_name, "column" :"first_delivery_timestamp_ms"},
          "bid_ctx_last_delivery_timestamp_ms" : {"item_table" : tag_table_name, "column" :"last_delivery_timestamp_ms"},
          "bid_ctx_last_update_adjust_timestamp" : {"item_table" : tag_table_name, "column" :"last_update_adjust_timestamp"},
          "bid_ctx_cost_start_timestamp_ms" : {"item_table" : tag_table_name, "column" :"cost_start_timestamp_ms"},
          "bid_ctx_last_update_context_timestamp" : {"item_table" : tag_table_name, "column" :"last_update_context_timestamp"},
          "bid_ctx_last_sync_context_timestamp" : {"item_table" : tag_table_name, "column" :"last_sync_context_timestamp"},
          "bid_ctx_online" : {"item_table" : tag_table_name, "column" :"online"},
          "bid_ctx_is_cold_start" : {"item_table" : tag_table_name, "column" :"is_cold_start"},
          "bid_ctx_speed_type" : {"item_table" : tag_table_name, "column" :"speed_type"},
          "bid_ctx_context_start_timestamp" : {"item_table" : tag_table_name, "column" :"context_start_timestamp"},
          "bid_ctx_last_sync_result_timestamp" : {"item_table" : tag_table_name, "column" :"last_sync_result_timestamp"},
          "bid_ctx_adjust_auto_value_rate" : {"item_table" : tag_table_name, "column" :"adjust_auto_value_rate"},
          "bid_ctx_total_ecpm" : {"item_table" : tag_table_name, "column" :"total_ecpm"},
          "bid_ctx_separate_gsp_price" : {"item_table" : tag_table_name, "column" :"separate_gsp_price"},
          "bid_ctx_total_auction_bid" : {"item_table" : tag_table_name, "column" :"total_auction_bid"},
          "bid_ctx_dry_up_base_value" : {"item_table" : tag_table_name, "column" :"dry_up_base_value"},
          "bid_ctx_ctr_sum" : {"item_table" : tag_table_name, "column" :"ctr_sum"},
          "bid_ctx_cvr_sum" : {"item_table" : tag_table_name, "column" :"cvr_sum"},
          "bid_ctx_softad_cost" : {"item_table" : tag_table_name, "column" :"softad_cost"},
          "bid_ctx_softad_target_cost" : {"item_table" : tag_table_name, "column" :"softad_target_cost"},
          "bid_ctx_softad_diff_ratio" : {"item_table" : tag_table_name, "column" :"softad_diff_ratio"},
          
          "bid_ctx_sync_context_interval_batch_batch_cost" : {"item_table" : tag_table_name, "column" :"sync_context_interval_batch_batch_cost"},
          "bid_ctx_sync_context_interval_batch_batch_gmv" : {"item_table" : tag_table_name, "column" :"sync_context_interval_batch_batch_gmv"},
          "bid_ctx_sync_context_interval_batch_batch_start_timestamp" : {"item_table" : tag_table_name, "column" :"sync_context_interval_batch_batch_start_timestamp"},
          "bid_ctx_cost_speed_context_batch_start_timestamp_ms" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_start_timestamp_ms"},
          "bid_ctx_cost_speed_context_batch_value_sum" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_value_sum"},
          "bid_ctx_cost_speed_context_batch_count" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_count"},
          "bid_ctx_cost_speed_context_batch_value_mean" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_value_mean"},
          "bid_ctx_cost_speed_context_batch_value_speed" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_value_speed"},
          "bid_ctx_delivery_speed_context_batch_start_timestamp_ms" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_start_timestamp_ms"},
          "bid_ctx_delivery_speed_context_batch_value_sum" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_value_sum"},
          "bid_ctx_delivery_speed_context_batch_count" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_count"},
          "bid_ctx_delivery_speed_context_batch_value_mean" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_value_mean"},
          "bid_ctx_delivery_speed_context_batch_value_speed" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_value_speed"},
          "bid_ctx_conv_ratio_adjust_context_pred_conv" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_pred_conv"},
          "bid_ctx_conv_ratio_adjust_context_old_auto_conv_ratio" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_old_auto_conv_ratio"},
          "bid_ctx_conv_ratio_adjust_context_auto_conv_ratio" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_auto_conv_ratio"},
          "bid_ctx_conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio"},
          "bid_ctx_conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio"},
          "bid_ctx_conv_ratio_adjust_context_real_conv" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_real_conv"},
          "bid_ctx_conv_ratio_adjust_context_last_update_conv_ratio_timestamp" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_last_update_conv_ratio_timestamp"},
          "bid_ctx_conv_ratio_adjust_context_reach_r1_upper_bound_timestamp" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_reach_r1_upper_bound_timestamp"},
          "bid_ctx_conv_ratio_adjust_context_reach_r1_lower_bound_timestamp" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_reach_r1_lower_bound_timestamp"},
          "bid_ctx_conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp"},
          "bid_ctx_price_ratio_context_batch_start_timestamp_ms" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_start_timestamp_ms"},
          "bid_ctx_price_ratio_context_batch_total_price" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_total_price"},
          "bid_ctx_price_ratio_context_batch_total_ecpm" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_total_ecpm"},
          "bid_ctx_price_ratio_context_batch_total_price_ratio" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_total_price_ratio"},
          "bid_ctx_price_ratio_context_batch_count" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_count"},
          "bid_ctx_price_ratio_context_batch_avg_price_ratio" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_avg_price_ratio"},
          "bid_ctx_price_ratio_context_batch_price_ratio_mean" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_price_ratio_mean"},
          "bid_ctx_item_type_info_photo_to_live_cnt" : {"item_table" : tag_table_name, "column" :"item_type_info_photo_to_live_cnt"},
          "bid_ctx_item_type_info_direct_live_cnt" : {"item_table" : tag_table_name, "column" :"item_type_info_direct_live_cnt"},
          "bid_ctx_item_type_info_photo_to_live_cost" : {"item_table" : tag_table_name, "column" :"item_type_info_photo_to_live_cost"},
          "bid_ctx_item_type_info_direct_live_cost" : {"item_table" : tag_table_name, "column" :"item_type_info_direct_live_cost"},
        },
        process_utils_attr_info = {
          "process_utils_ad_off_target_cost" : {"item_table" : tag_table_name, "column" :"util_vals_ad_off_target_cost"},
          "process_utils_new_adjust_auto_value_rate" : {"item_table" : tag_table_name, "column" :"util_vals_new_adjust_auto_value_rate"},
          "process_utils_old_adjust_auto_value_rate" : {"item_table" : tag_table_name, "column" :"util_vals_old_adjust_auto_value_rate"},
          "process_utils_is_bid_ptr_null" : {"item_table" : tag_table_name, "column" :"util_vals_is_bid_ptr_null"},
          "process_utils_pro_ocpc_not_been_set" : {"item_table" : tag_table_name, "column" :"util_vals_pro_ocpc_not_been_set"},
          "process_utils_reset_pacing" : {"item_table" : tag_table_name, "column" :"util_vals_reset_pacing"},
          "process_utils_is_cost_target_msg" : {"item_table" : tag_table_name, "column" :"util_vals_is_cost_target_msg"},
          "process_utils_enable_cost_prior_algo" : {"item_table" : tag_table_name, "column" :"util_vals_enable_cost_prior_algo"},
          "process_utils_enable_linear_pacing_cold_start_low_bound" : {"item_table" : tag_table_name, "column" :"util_vals_enable_linear_pacing_cold_start_low_bound"},
          "process_utils_enable_linear_pacing_grid_correction" : {"item_table" : tag_table_name, "column" :"util_vals_enable_linear_pacing_grid_correction"},
          "process_utils_enable_linear_adaptive_update_time" : {"item_table" : tag_table_name, "column" :"util_vals_enable_linear_adaptive_update_time"},
          "process_utils_enable_linear_after_cold_start_no_conv_drop" : {"item_table" : tag_table_name, "column" :"util_vals_enable_linear_after_cold_start_no_conv_drop"},
          "process_utils_disable_day_reset_exp" : {"item_table" : tag_table_name, "column" :"util_vals_disable_day_reset_exp"},
          "process_utils_enable_univ_inner_ad_delivery_replace" : {"item_table" : tag_table_name, "column" :"util_vals_enable_univ_inner_ad_delivery_replace"},
          "process_utils_is_apply_adjust" : {"item_table" : tag_table_name, "column" :"util_vals_is_apply_adjust"},
          "process_utils_is_debug" : {"item_table" : tag_table_name, "column" :"util_vals_is_debug"},
          "process_utils_is_roas_sub_pacing" : {"item_table" : tag_table_name, "column" :"util_vals_is_roas_sub_pacing"},
          "process_utils_is_ocpm_bid_process" : {"item_table" : tag_table_name, "column" :"util_vals_is_ocpm_bid_process"},
          "process_utils_is_apply_operation_config" : {"item_table" : tag_table_name, "column" :"util_vals_is_apply_operation_config"},
          "process_utils_is_start_process" : {"item_table" : tag_table_name, "column" :"util_vals_is_start_process"},
          "process_utils_is_debug_on" : {"item_table" : tag_table_name, "column" :"util_vals_is_debug_on"},
          "process_utils_is_normal" : {"item_table" : tag_table_name, "column" :"util_vals_is_normal"},
          "process_utils_is_fanstop" : {"item_table" : tag_table_name, "column" :"util_vals_is_fanstop"},
          "process_utils_is_fanstop_ocpm" : {"item_table" : tag_table_name, "column" :"util_vals_is_fanstop_ocpm"},
          "process_utils_is_roas" : {"item_table" : tag_table_name, "column" :"util_vals_is_roas"},
          "process_utils_is_no_bid" : {"item_table" : tag_table_name, "column" :"util_vals_is_no_bid"},
          "process_utils_is_skip_update" : {"item_table" : tag_table_name, "column" :"util_vals_is_skip_update"},
          "process_utils_is_nothing_to_do" : {"item_table" : tag_table_name, "column" :"util_vals_is_nothing_to_do"},
          "process_utils_is_process" : {"item_table" : tag_table_name, "column" :"util_vals_is_process"},
          "process_utils_is_reset_context" : {"item_table" : tag_table_name, "column" :"util_vals_is_reset_context"},
          "process_utils_is_update_adjust" : {"item_table" : tag_table_name, "column" :"util_vals_is_update_adjust"},
          "process_utils_is_interval_cost" : {"item_table" : tag_table_name, "column" :"util_vals_is_interval_cost"},
          "process_utils_is_update_interval_ms" : {"item_table" : tag_table_name, "column" :"util_vals_is_update_interval_ms"},
          "process_utils_is_ad_open" : {"item_table" : tag_table_name, "column" :"util_vals_is_ad_open"},
          "process_utils_is_sync_to_dsp" : {"item_table" : tag_table_name, "column" :"util_vals_is_sync_to_dsp"},
          "process_utils_is_monitor" : {"item_table" : tag_table_name, "column" :"util_vals_is_monitor"},
          "process_utils_is_cold_start" : {"item_table" : tag_table_name, "column" :"util_vals_is_cold_start"},
          "process_utils_is_costcap_nobid" : {"item_table" : tag_table_name, "column" :"util_vals_is_costcap_nobid"},
          "process_utils_is_skip_cold_start" : {"item_table" : tag_table_name, "column" :"util_vals_is_skip_cold_start"},
          "process_utils_is_on_live" : {"item_table" : tag_table_name, "column" :"util_vals_is_on_live"},
          "process_utils_is_out_of_budget" : {"item_table" : tag_table_name, "column" :"util_vals_is_out_of_budget"},
          "process_utils_old_is_out_of_budget" : {"item_table" : tag_table_name, "column" :"util_vals_old_is_out_of_budget"},
          "process_utils_advertisable" : {"item_table" : tag_table_name, "column" :"util_vals_advertisable"},
          "process_utils_is_status_open" : {"item_table" : tag_table_name, "column" :"util_vals_is_status_open"},
          "process_utils_ad_status" : {"item_table" : tag_table_name, "column" :"util_vals_ad_status"},
          "process_utils_unit_id" : {"item_table" : tag_table_name, "column" :"util_vals_unit_id"},
          "process_utils_thread_id" : {"item_table" : tag_table_name, "column" :"util_vals_thread_id"},
          "process_utils_is_bid_info" : {"item_table" : tag_table_name, "column" :"util_vals_is_bid_info"},
          "process_utils_is_online" : {"item_table" : tag_table_name, "column" :"util_vals_is_online"},
          "process_utils_is_explore" : {"item_table" : tag_table_name, "column" :"util_vals_is_explore"},
          "process_utils_ab_exp_ratio" : {"item_table" : tag_table_name, "column" :"util_vals_ab_exp_ratio"},
          "process_utils_promotion_type_num" : {"item_table" : tag_table_name, "column" :"util_vals_promotion_type_num"},
          "process_utils_item_type_num" : {"item_table" : tag_table_name, "column" :"util_vals_item_type_num"},
          "process_utils_prior_cost" : {"item_table" : tag_table_name, "column" :"util_vals_prior_cost"},
          "process_utils_cold_start_cost_li_thr" : {"item_table" : tag_table_name, "column" :"util_vals_cold_start_cost_li_thr"},
          "process_utils_cold_start_low_bound" : {"item_table" : tag_table_name, "column" :"util_vals_cold_start_low_bound"},
          "process_utils_rt_cost_speed" : {"item_table" : tag_table_name, "column" :"util_vals_rt_cost_speed"},
          "process_utils_rt_delivery_speed" : {"item_table" : tag_table_name, "column" :"util_vals_rt_delivery_speed"},
          "process_utils_target_roas" : {"item_table" : tag_table_name, "column" :"util_vals_target_roas"},
          "process_utils_is_price_ratio_bound" : {"item_table" : tag_table_name, "column" :"util_vals_is_price_ratio_bound"},
          "process_utils_is_acc_explore_bid" : {"item_table" : tag_table_name, "column" :"util_vals_is_acc_explore_bid"},
          "process_utils_dynamic_bid_lower_bound_adjust_time_interval_ms" : {"item_table" : tag_table_name, "column" :"util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms"},
          "process_utils_dynamic_bid_upper_bound_adjust_time_interval_ms" : {"item_table" : tag_table_name, "column" :"util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms"},
          "process_utils_dynamic_bid_lower_bound_adjust_rate" : {"item_table" : tag_table_name, "column" :"util_vals_dynamic_bid_lower_bound_adjust_rate"},
          "process_utils_dynamic_bid_upper_bound_adjust_rate" : {"item_table" : tag_table_name, "column" :"util_vals_dynamic_bid_upper_bound_adjust_rate"},
          "process_utils_p_weight" : {"item_table" : tag_table_name, "column" :"util_vals_p_weight"},
          "process_utils_i_weight" : {"item_table" : tag_table_name, "column" :"util_vals_i_weight"},
          "process_utils_d_weight" : {"item_table" : tag_table_name, "column" :"util_vals_d_weight"},
          "process_utils_explore_p_weight" : {"item_table" : tag_table_name, "column" :"util_vals_explore_p_weight"},
          "process_utils_explore_i_weight" : {"item_table" : tag_table_name, "column" :"util_vals_explore_i_weight"},
          "process_utils_explore_d_weight" : {"item_table" : tag_table_name, "column" :"util_vals_explore_d_weight"},
          "process_utils_p_val" : {"item_table" : tag_table_name, "column" :"util_vals_p_val"},
          "process_utils_i_val" : {"item_table" : tag_table_name, "column" :"util_vals_i_val"},
          "process_utils_d_val" : {"item_table" : tag_table_name, "column" :"util_vals_d_val"},
          "process_utils_pacing_weight" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_weight"},
          "process_utils_target_cost_speed" : {"item_table" : tag_table_name, "column" :"util_vals_target_cost_speed"},
          "process_utils_target_delivery_speed" : {"item_table" : tag_table_name, "column" :"util_vals_target_delivery_speed"},
          "process_utils_start_bid_rate" : {"item_table" : tag_table_name, "column" :"util_vals_start_bid_rate"},
          "process_utils_start_bid_rate_sub_pacing" : {"item_table" : tag_table_name, "column" :"util_vals_start_bid_rate_sub_pacing"},
          "process_utils_hard_upper_bound_auto_cpa_bid" : {"item_table" : tag_table_name, "column" :"util_vals_hard_upper_bound_auto_cpa_bid"},
          "process_utils_hard_lower_bound_auto_cpa_bid" : {"item_table" : tag_table_name, "column" :"util_vals_hard_lower_bound_auto_cpa_bid"},
          "process_utils_hard_lower_bound_auto_roas" : {"item_table" : tag_table_name, "column" :"util_vals_hard_lower_bound_auto_roas"},
          "process_utils_hard_upper_bound_auto_roas" : {"item_table" : tag_table_name, "column" :"util_vals_hard_upper_bound_auto_roas"},
          "process_utils_hard_lower_bound_auto_atv" : {"item_table" : tag_table_name, "column" :"util_vals_hard_lower_bound_auto_atv"},
          "process_utils_hard_upper_bound_auto_atv" : {"item_table" : tag_table_name, "column" :"util_vals_hard_upper_bound_auto_atv"},
          "process_utils_acc_explore_bid_target_cost_speed" : {"item_table" : tag_table_name, "column" :"util_vals_acc_explore_bid_target_cost_speed"},
          "process_utils_current_ms" : {"item_table" : tag_table_name, "column" :"util_vals_current_ms"},
          "process_utils_ad_short_type" : {"item_table" : tag_table_name, "column" :"util_vals_ad_short_type"},
          "process_utils_is_target_modify" : {"item_table" : tag_table_name, "column" :"util_vals_is_target_modify"},
          "process_utils_target_modify_ratio" : {"item_table" : tag_table_name, "column" :"util_vals_target_modify_ratio"},
          "process_utils_p_weight_conv" : {"item_table" : tag_table_name, "column" :"util_vals_p_weight_conv"},
          "process_utils_i_weight_conv" : {"item_table" : tag_table_name, "column" :"util_vals_i_weight_conv"},
          "process_utils_d_weight_conv" : {"item_table" : tag_table_name, "column" :"util_vals_d_weight_conv"},
          "process_utils_p_val_conv" : {"item_table" : tag_table_name, "column" :"util_vals_p_val_conv"},
          "process_utils_i_val_conv" : {"item_table" : tag_table_name, "column" :"util_vals_i_val_conv"},
          "process_utils_d_val_conv" : {"item_table" : tag_table_name, "column" :"util_vals_d_val_conv"},
          "process_utils_pacing_weight_conv" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_weight_conv"},
          "process_utils_p_weight_cost" : {"item_table" : tag_table_name, "column" :"util_vals_p_weight_cost"},
          "process_utils_i_weight_cost" : {"item_table" : tag_table_name, "column" :"util_vals_i_weight_cost"},
          "process_utils_d_weight_cost" : {"item_table" : tag_table_name, "column" :"util_vals_d_weight_cost"},
          "process_utils_p_val_cost" : {"item_table" : tag_table_name, "column" :"util_vals_p_val_cost"},
          "process_utils_i_val_cost" : {"item_table" : tag_table_name, "column" :"util_vals_i_val_cost"},
          "process_utils_d_val_cost" : {"item_table" : tag_table_name, "column" :"util_vals_d_val_cost"},
          "process_utils_pacing_weight_cost" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_weight_cost"},
          "process_utils_update_interval_ms" : {"item_table" : tag_table_name, "column" :"util_vals_update_interval_ms"},
          "process_utils_interval_cost" : {"item_table" : tag_table_name, "column" :"util_vals_interval_cost"},
          "process_utils_step_bid_rate_increase" : {"item_table" : tag_table_name, "column" :"util_vals_step_bid_rate_increase"},
          "process_utils_step_bid_rate_descent" : {"item_table" : tag_table_name, "column" :"util_vals_step_bid_rate_descent"},
          "process_utils_ad_status_tag" : {"item_table" : tag_table_name, "column" :"util_vals_ad_status_tag"},
          "process_utils_ad_status_tag_change" : {"item_table" : tag_table_name, "column" :"util_vals_ad_status_tag_change"},
          "process_utils_total_pred_conv" : {"item_table" : tag_table_name, "column" :"util_vals_total_pred_conv"},
          "process_utils_adjust_rate_lower_bound" : {"item_table" : tag_table_name, "column" :"util_vals_adjust_rate_lower_bound"},
          "process_utils_adjust_rate_upper_bound" : {"item_table" : tag_table_name, "column" :"util_vals_adjust_rate_upper_bound"},
          "process_utils_budget" : {"item_table" : tag_table_name, "column" :"util_vals_budget"},
          "process_utils_hard_bid_upper_bound_rate_monitor" : {"item_table" : tag_table_name, "column" :"util_vals_hard_bid_upper_bound_rate_monitor"},
          "process_utils_hard_bid_lower_bound_rate_monitor" : {"item_table" : tag_table_name, "column" :"util_vals_hard_bid_lower_bound_rate_monitor"},
          "process_utils_fanstop_adjust_rate_lower_bound_roi" : {"item_table" : tag_table_name, "column" :"util_vals_fanstop_adjust_rate_lower_bound_roi"},
          "process_utils_fanstop_adjust_rate_upper_bound_roi" : {"item_table" : tag_table_name, "column" :"util_vals_fanstop_adjust_rate_upper_bound_roi"},
          "process_utils_fanstop_adjust_rate_lower_bound_cpa" : {"item_table" : tag_table_name, "column" :"util_vals_fanstop_adjust_rate_lower_bound_cpa"},
          "process_utils_fanstop_adjust_rate_upper_bound_cpa" : {"item_table" : tag_table_name, "column" :"util_vals_fanstop_adjust_rate_upper_bound_cpa"},
          "process_utils_pacing_type" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_type"},
          "process_utils_pacing_method_type" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_method_type"},
        },
      ) 
      return self
    def ad_step_one_to_redis(self, tag_table_name):
      os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
      self.inner_loop_step_one_write_to_redis(
          item_table = "UnitTagTable",
          redis_name = "adEngineFlowProcess",
          is_step_two = False,
          ttl = 86400,
          #mock
          has_bid_state_info_ptr = {"item_table" : "TriggerTable", "column" : "has_bid_state_info_ptr"},
          bid_state_info_ptr = {"item_table" : "TriggerTable", "column" : "bid_state_info_ptr"},
          message_seq = {"item_table" : "TriggerTable", "column" : "message_seq"},
          event_server_timestamp = {"item_table" : "TriggerTable", "column" : "event_server_timestamp"},
          unit_id = {"item_table" : "UnitTagTable", "column" :"unit_id"},
          msg_attr_info = {
          "msg_campaign_type" : {"item_table" : "TriggerTable", "column" :"campaign_type"},
          "msg_ocpc_action_type" : {"item_table" : "TriggerTable", "column" :"ocpc_action_type"},
          "msg_promotion_type" : {"item_table" : "TriggerTable", "column" :"promotion_type"},
          "msg_item_type" : {"item_table" : "TriggerTable", "column" :"item_type"},
          "msg_author_id" : {"item_table" : "TriggerTable", "column" :"author_id"},
          "msg_account_id" : {"item_table" : "TriggerTable", "column" :"account_id"},
          "msg_live_stream_id" : {"item_table" : "TriggerTable", "column" :"live_stream_id"},
          "msg_unit_id" : {"item_table" : "TriggerTable", "column" :"unit_id"},
          "msg_conv_num" : {"item_table" : "TriggerTable", "column" :"conv_num"},
          "msg_campaign_id" : {"item_table" : "TriggerTable", "column" :"campaign_id"},
          "msg_item_type_num" : {"item_table" : "TriggerTable", "column" :"item_type_num"},
          "msg_cost" : {"item_table" : "TriggerTable", "column" :"cost"},
          "msg_gmv" : {"item_table" : "TriggerTable", "column" :"gmv"},
          "msg_roi_ratio" : {"item_table" : "TriggerTable", "column" :"roi_ratio"},
          "msg_action_type" : {"item_table" : "TriggerTable", "column" :"action_type"},
          "msg_group_tag" : {"item_table" : "TriggerTable", "column" :"inner_group_tag"},
          "msg_bid_type" : {"item_table" : "TriggerTable", "column" :"bid_type"},
          "msg_medium_attribute" : {"item_table" : "TriggerTable", "column" :"medium_attribute"},
          "msg_speed_type" : {"item_table" : "TriggerTable", "column" :"speed_type"},
          "msg_delivery_timestamp" : {"item_table" : "TriggerTable", "column" :"delivery_timestamp"},
          "msg_cpa_bid" : {"item_table" : "TriggerTable", "column" :"cpa_bid"},
          "msg_is_soft" : {"item_table" : "TriggerTable", "column" :"is_soft"},
          "msg_target_cost" : {"item_table" : "TriggerTable", "column" :"target_cost"},
          "msg_target_gmv" : {"item_table" : "TriggerTable", "column" :"target_gmv"},
          "msg_separate_gsp_price" : {"item_table" : "TriggerTable", "column" :"separate_gsp_price"},
          "msg_record_gsp_price" : {"item_table" : "TriggerTable", "column" :"record_gsp_price"},
          "msg_pred_conv" : {"item_table" : "TriggerTable", "column" :"pred_conv"},
          "msg_pred_cvr_sum" : {"item_table" : "TriggerTable", "column" :"pred_cvr_sum"},
          "msg_pred_ctr_sum" : {"item_table" : "TriggerTable", "column" :"pred_ctr_sum"},
          "msg_price_before_billing_separate" : {"item_table" : "TriggerTable", "column" :"price_before_billing_separate"},
          "msg_price_after_billing_separate" : {"item_table" : "TriggerTable", "column" :"price_after_billing_separate"},
          "msg_ecpm" : {"item_table" : "TriggerTable", "column" :"ecpm"},
          "msg_auction_bid" : {"item_table" : "TriggerTable", "column" :"auction_bid"},
          "msg_price_ratio" : {"item_table" : "TriggerTable", "column" :"price_ratio"},
          "msg_is_store_wide_roi_reco_conv" : {"item_table" : "TriggerTable", "column" :"is_store_wide_roi_reco_conv"},
        },
        bid_ctx_attr_info = {
          "bid_ctx_bid_context_key" : {"item_table" : tag_table_name, "column" :"bid_context_key"},
          "bid_ctx_group_tag" : {"item_table" : tag_table_name, "column" :"group_tag"},
          "bid_ctx_campaign_type" : {"item_table" : tag_table_name, "column" :"campaign_type"},
          "bid_ctx_ocpc_action_type" : {"item_table" : tag_table_name, "column" :"ocpc_action_type"},
          "bid_ctx_promotion_type" : {"item_table" : tag_table_name, "column" :"promotion_type"},
          "bid_ctx_item_type" : {"item_table" : tag_table_name, "column" :"item_type"},
          "bid_ctx_first_industry_name" : {"item_table" : tag_table_name, "column" :"first_industry_name"},
          "bid_ctx_group_tag_num" : {"item_table" : tag_table_name, "column" :"group_tag_num"},
          "bid_ctx_unit_id" : {"item_table" : tag_table_name, "column" :"unit_id"},
          "bid_ctx_account_id" : {"item_table" : tag_table_name, "column" :"account_id"},
          "bid_ctx_author_id" : {"item_table" : tag_table_name, "column" :"author_id"},
          "bid_ctx_live_stream_id" : {"item_table" : tag_table_name, "column" :"live_stream_id"},
          "bid_ctx_item_type_num" : {"item_table" : tag_table_name, "column" :"item_type_num"},
          "bid_ctx_campaign_id" : {"item_table" : tag_table_name, "column" :"campaign_id"},
          "bid_ctx_price_before_billing_separate" : {"item_table" : tag_table_name, "column" :"price_before_billing_separate"},
          "bid_ctx_price_after_billing_separate" : {"item_table" : tag_table_name, "column" :"price_after_billing_separate"},
          "bid_ctx_cost" : {"item_table" : tag_table_name, "column" :"cost"},
          "bid_ctx_gmv" : {"item_table" : tag_table_name, "column" :"gmv"},
          "bid_ctx_conv_num" : {"item_table" : tag_table_name, "column" :"conv_num"},
          "bid_ctx_cost_count" : {"item_table" : tag_table_name, "column" :"cost_count"},
          "bid_ctx_gmv_count" : {"item_table" : tag_table_name, "column" :"gmv_count"},
          "bid_ctx_delivery_cnt" : {"item_table" : tag_table_name, "column" :"delivery_cnt"},
          "bid_ctx_cpa_bid" : {"item_table" : tag_table_name, "column" :"cpa_bid"},
          "bid_ctx_relax_cpa_bid" : {"item_table" : tag_table_name, "column" :"relax_cpa_bid"},
          "bid_ctx_roi_ratio" : {"item_table" : tag_table_name, "column" :"roi_ratio"},
          "bid_ctx_relax_roi_ratio" : {"item_table" : tag_table_name, "column" :"relax_roi_ratio"},
          "bid_ctx_rt_cpa_bid" : {"item_table" : tag_table_name, "column" :"rt_cpa_bid"},
          "bid_ctx_rt_roas" : {"item_table" : tag_table_name, "column" :"rt_roas"},
          "bid_ctx_auto_cpa_bid" : {"item_table" : tag_table_name, "column" :"auto_cpa_bid"},
          "bid_ctx_auto_roi_ratio" : {"item_table" : tag_table_name, "column" :"auto_roi_ratio"},
          "bid_ctx_old_auto_cpa_bid" : {"item_table" : tag_table_name, "column" :"old_auto_cpa_bid"},
          "bid_ctx_old_auto_roi_ratio" : {"item_table" : tag_table_name, "column" :"old_auto_roi_ratio"},
          "bid_ctx_adjust_rate" : {"item_table" : tag_table_name, "column" :"adjust_rate"},
          "bid_ctx_rt_cost_speed" : {"item_table" : tag_table_name, "column" :"rt_cost_speed"},
          "bid_ctx_rt_delivery_speed" : {"item_table" : tag_table_name, "column" :"rt_delivery_speed"},
          "bid_ctx_dynamic_lower_bound_roas" : {"item_table" : tag_table_name, "column" :"dynamic_lower_bound_roas"},
          "bid_ctx_dynamic_upper_bound_roas" : {"item_table" : tag_table_name, "column" :"dynamic_upper_bound_roas"},
          "bid_ctx_dynamic_lower_bound_cpa_bid" : {"item_table" : tag_table_name, "column" :"dynamic_lower_bound_cpa_bid"},
          "bid_ctx_dynamic_upper_bound_cpa_bid" : {"item_table" : tag_table_name, "column" :"dynamic_upper_bound_cpa_bid"},
          "bid_ctx_target_cost" : {"item_table" : tag_table_name, "column" :"target_cost"},
          "bid_ctx_target_gmv" : {"item_table" : tag_table_name, "column" :"target_gmv"},
          "bid_ctx_record_gsp_price" : {"item_table" : tag_table_name, "column" :"record_gsp_price"},
          "bid_ctx_last_update_cost" : {"item_table" : tag_table_name, "column" :"last_update_cost"},
          "bid_ctx_adjust_auto_atv_rate" : {"item_table" : tag_table_name, "column" :"adjust_auto_atv_rate"},
          "bid_ctx_old_adjust_auto_atv_rate" : {"item_table" : tag_table_name, "column" :"old_adjust_auto_atv_rate"},
          "bid_ctx_reach_lower_bound_timestamp" : {"item_table" : tag_table_name, "column" :"reach_lower_bound_timestamp"},
          "bid_ctx_reach_upper_bound_timestamp" : {"item_table" : tag_table_name, "column" :"reach_upper_bound_timestamp"},
          "bid_ctx_first_delivery_timestamp_ms" : {"item_table" : tag_table_name, "column" :"first_delivery_timestamp_ms"},
          "bid_ctx_last_delivery_timestamp_ms" : {"item_table" : tag_table_name, "column" :"last_delivery_timestamp_ms"},
          "bid_ctx_last_update_adjust_timestamp" : {"item_table" : tag_table_name, "column" :"last_update_adjust_timestamp"},
          "bid_ctx_cost_start_timestamp_ms" : {"item_table" : tag_table_name, "column" :"cost_start_timestamp_ms"},
          "bid_ctx_last_update_context_timestamp" : {"item_table" : tag_table_name, "column" :"last_update_context_timestamp"},
          "bid_ctx_last_sync_context_timestamp" : {"item_table" : tag_table_name, "column" :"last_sync_context_timestamp"},
          "bid_ctx_online" : {"item_table" : tag_table_name, "column" :"online"},
          "bid_ctx_is_cold_start" : {"item_table" : tag_table_name, "column" :"is_cold_start"},
          "bid_ctx_speed_type" : {"item_table" : tag_table_name, "column" :"speed_type"},
          "bid_ctx_context_start_timestamp" : {"item_table" : tag_table_name, "column" :"context_start_timestamp"},
          "bid_ctx_last_sync_result_timestamp" : {"item_table" : tag_table_name, "column" :"last_sync_result_timestamp"},
          "bid_ctx_adjust_auto_value_rate" : {"item_table" : tag_table_name, "column" :"adjust_auto_value_rate"},
          "bid_ctx_total_ecpm" : {"item_table" : tag_table_name, "column" :"total_ecpm"},
          "bid_ctx_separate_gsp_price" : {"item_table" : tag_table_name, "column" :"separate_gsp_price"},
          "bid_ctx_total_auction_bid" : {"item_table" : tag_table_name, "column" :"total_auction_bid"},
          "bid_ctx_dry_up_base_value" : {"item_table" : tag_table_name, "column" :"dry_up_base_value"},
          "bid_ctx_ctr_sum" : {"item_table" : tag_table_name, "column" :"ctr_sum"},
          "bid_ctx_cvr_sum" : {"item_table" : tag_table_name, "column" :"cvr_sum"},
          "bid_ctx_softad_cost" : {"item_table" : tag_table_name, "column" :"softad_cost"},
          "bid_ctx_softad_target_cost" : {"item_table" : tag_table_name, "column" :"softad_target_cost"},
          "bid_ctx_softad_diff_ratio" : {"item_table" : tag_table_name, "column" :"softad_diff_ratio"},
          "bid_ctx_last_monitor_timestamp" : {"item_table" : tag_table_name, "column" :"last_monitor_timestamp"},
          "bid_ctx_user_cost_prior_algo" : {"item_table" : tag_table_name, "column" :"user_cost_prior_algo"},
          "bid_ctx_ad_status_tag" : {"item_table" : tag_table_name, "column" :"ad_status_tag"},
          "bid_ctx_old_is_out_of_budget" : {"item_table" : tag_table_name, "column" :"old_is_out_of_budget"},
          "bid_ctx_last_is_ad_open" : {"item_table" : tag_table_name, "column" :"last_is_ad_open"},
          "bid_ctx_last_ad_valid" : {"item_table" : tag_table_name, "column" :"last_ad_valid"},          
          "bid_ctx_sync_context_interval_batch_batch_cost" : {"item_table" : tag_table_name, "column" :"sync_context_interval_batch_batch_cost"},
          "bid_ctx_sync_context_interval_batch_batch_gmv" : {"item_table" : tag_table_name, "column" :"sync_context_interval_batch_batch_gmv"},
          "bid_ctx_sync_context_interval_batch_batch_start_timestamp" : {"item_table" : tag_table_name, "column" :"sync_context_interval_batch_batch_start_timestamp"},
          "bid_ctx_cost_speed_context_batch_start_timestamp_ms" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_start_timestamp_ms"},
          "bid_ctx_cost_speed_context_batch_value_sum" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_value_sum"},
          "bid_ctx_cost_speed_context_batch_count" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_count"},
          "bid_ctx_cost_speed_context_batch_value_mean" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_value_mean"},
          "bid_ctx_cost_speed_context_batch_value_speed" : {"item_table" : tag_table_name, "column" :"cost_speed_context_batch_value_speed"},
          "bid_ctx_delivery_speed_context_batch_start_timestamp_ms" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_start_timestamp_ms"},
          "bid_ctx_delivery_speed_context_batch_value_sum" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_value_sum"},
          "bid_ctx_delivery_speed_context_batch_count" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_count"},
          "bid_ctx_delivery_speed_context_batch_value_mean" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_value_mean"},
          "bid_ctx_delivery_speed_context_batch_value_speed" : {"item_table" : tag_table_name, "column" :"delivery_speed_context_batch_value_speed"},
          "bid_ctx_conv_ratio_adjust_context_pred_conv" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_pred_conv"},
          "bid_ctx_conv_ratio_adjust_context_old_auto_conv_ratio" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_old_auto_conv_ratio"},
          "bid_ctx_conv_ratio_adjust_context_auto_conv_ratio" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_auto_conv_ratio"},
          "bid_ctx_conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_dynamic_upper_bound_conv_ratio"},
          "bid_ctx_conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_dynamic_lower_bound_conv_ratio"},
          "bid_ctx_conv_ratio_adjust_context_real_conv" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_real_conv"},
          "bid_ctx_conv_ratio_adjust_context_last_update_conv_ratio_timestamp" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_last_update_conv_ratio_timestamp"},
          "bid_ctx_conv_ratio_adjust_context_reach_r1_upper_bound_timestamp" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_reach_r1_upper_bound_timestamp"},
          "bid_ctx_conv_ratio_adjust_context_reach_r1_lower_bound_timestamp" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_reach_r1_lower_bound_timestamp"},
          "bid_ctx_conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp" : {"item_table" : tag_table_name, "column" :"conv_ratio_adjust_context_reach_conv_ratio_hard_lower_bound_timestamp"},
          "bid_ctx_price_ratio_context_batch_start_timestamp_ms" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_start_timestamp_ms"},
          "bid_ctx_price_ratio_context_batch_total_price" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_total_price"},
          "bid_ctx_price_ratio_context_batch_total_ecpm" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_total_ecpm"},
          "bid_ctx_price_ratio_context_batch_total_price_ratio" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_total_price_ratio"},
          "bid_ctx_price_ratio_context_batch_count" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_count"},
          "bid_ctx_price_ratio_context_batch_avg_price_ratio" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_avg_price_ratio"},
          "bid_ctx_price_ratio_context_batch_price_ratio_mean" : {"item_table" : tag_table_name, "column" :"price_ratio_context_batch_price_ratio_mean"},
          "bid_ctx_item_type_info_photo_to_live_cnt" : {"item_table" : tag_table_name, "column" :"item_type_info_photo_to_live_cnt"},
          "bid_ctx_item_type_info_direct_live_cnt" : {"item_table" : tag_table_name, "column" :"item_type_info_direct_live_cnt"},
          "bid_ctx_item_type_info_photo_to_live_cost" : {"item_table" : tag_table_name, "column" :"item_type_info_photo_to_live_cost"},
          "bid_ctx_item_type_info_direct_live_cost" : {"item_table" : tag_table_name, "column" :"item_type_info_direct_live_cost"},
        },
        process_utils_attr_info = {
          "process_utils_ad_off_target_cost" : {"item_table" : tag_table_name, "column" :"util_vals_ad_off_target_cost"},
          "process_utils_new_adjust_auto_value_rate" : {"item_table" : tag_table_name, "column" :"util_vals_new_adjust_auto_value_rate"},
          "process_utils_old_adjust_auto_value_rate" : {"item_table" : tag_table_name, "column" :"util_vals_old_adjust_auto_value_rate"},
          "process_utils_is_bid_ptr_null" : {"item_table" : tag_table_name, "column" :"util_vals_is_bid_ptr_null"},
          "process_utils_pro_ocpc_not_been_set" : {"item_table" : tag_table_name, "column" :"util_vals_pro_ocpc_not_been_set"},
          "process_utils_reset_pacing" : {"item_table" : tag_table_name, "column" :"util_vals_reset_pacing"},
          "process_utils_is_cost_target_msg" : {"item_table" : tag_table_name, "column" :"util_vals_is_cost_target_msg"},
          "process_utils_enable_cost_prior_algo" : {"item_table" : tag_table_name, "column" :"util_vals_enable_cost_prior_algo"},
          "process_utils_enable_linear_pacing_cold_start_low_bound" : {"item_table" : tag_table_name, "column" :"util_vals_enable_linear_pacing_cold_start_low_bound"},
          "process_utils_enable_linear_pacing_grid_correction" : {"item_table" : tag_table_name, "column" :"util_vals_enable_linear_pacing_grid_correction"},
          "process_utils_enable_linear_adaptive_update_time" : {"item_table" : tag_table_name, "column" :"util_vals_enable_linear_adaptive_update_time"},
          "process_utils_enable_linear_after_cold_start_no_conv_drop" : {"item_table" : tag_table_name, "column" :"util_vals_enable_linear_after_cold_start_no_conv_drop"},
          "process_utils_disable_day_reset_exp" : {"item_table" : tag_table_name, "column" :"util_vals_disable_day_reset_exp"},
          "process_utils_enable_univ_inner_ad_delivery_replace" : {"item_table" : tag_table_name, "column" :"util_vals_enable_univ_inner_ad_delivery_replace"},
          "process_utils_is_apply_adjust" : {"item_table" : tag_table_name, "column" :"util_vals_is_apply_adjust"},
          "process_utils_is_debug" : {"item_table" : tag_table_name, "column" :"util_vals_is_debug"},
          "process_utils_is_roas_sub_pacing" : {"item_table" : tag_table_name, "column" :"util_vals_is_roas_sub_pacing"},
          "process_utils_is_ocpm_bid_process" : {"item_table" : tag_table_name, "column" :"util_vals_is_ocpm_bid_process"},
          "process_utils_is_apply_operation_config" : {"item_table" : tag_table_name, "column" :"util_vals_is_apply_operation_config"},
          "process_utils_is_start_process" : {"item_table" : tag_table_name, "column" :"util_vals_is_start_process"},
          "process_utils_is_debug_on" : {"item_table" : tag_table_name, "column" :"util_vals_is_debug_on"},
          "process_utils_is_normal" : {"item_table" : tag_table_name, "column" :"util_vals_is_normal"},
          "process_utils_is_fanstop" : {"item_table" : tag_table_name, "column" :"util_vals_is_fanstop"},
          "process_utils_is_fanstop_ocpm" : {"item_table" : tag_table_name, "column" :"util_vals_is_fanstop_ocpm"},
          "process_utils_is_roas" : {"item_table" : tag_table_name, "column" :"util_vals_is_roas"},
          "process_utils_is_no_bid" : {"item_table" : tag_table_name, "column" :"util_vals_is_no_bid"},
          "process_utils_is_skip_update" : {"item_table" : tag_table_name, "column" :"util_vals_is_skip_update"},
          "process_utils_is_nothing_to_do" : {"item_table" : tag_table_name, "column" :"util_vals_is_nothing_to_do"},
          "process_utils_is_process" : {"item_table" : tag_table_name, "column" :"util_vals_is_process"},
          "process_utils_is_reset_context" : {"item_table" : tag_table_name, "column" :"util_vals_is_reset_context"},
          "process_utils_is_update_adjust" : {"item_table" : tag_table_name, "column" :"util_vals_is_update_adjust"},
          "process_utils_is_interval_cost" : {"item_table" : tag_table_name, "column" :"util_vals_is_interval_cost"},
          "process_utils_is_update_interval_ms" : {"item_table" : tag_table_name, "column" :"util_vals_is_update_interval_ms"},
          "process_utils_is_ad_open" : {"item_table" : tag_table_name, "column" :"util_vals_is_ad_open"},
          "process_utils_is_sync_to_dsp" : {"item_table" : tag_table_name, "column" :"util_vals_is_sync_to_dsp"},
          "process_utils_is_monitor" : {"item_table" : tag_table_name, "column" :"util_vals_is_monitor"},
          "process_utils_is_cold_start" : {"item_table" : tag_table_name, "column" :"util_vals_is_cold_start"},
          "process_utils_is_costcap_nobid" : {"item_table" : tag_table_name, "column" :"util_vals_is_costcap_nobid"},
          "process_utils_is_skip_cold_start" : {"item_table" : tag_table_name, "column" :"util_vals_is_skip_cold_start"},
          "process_utils_is_on_live" : {"item_table" : tag_table_name, "column" :"util_vals_is_on_live"},
          "process_utils_is_out_of_budget" : {"item_table" : tag_table_name, "column" :"util_vals_is_out_of_budget"},
          "process_utils_old_is_out_of_budget" : {"item_table" : tag_table_name, "column" :"util_vals_old_is_out_of_budget"},
          "process_utils_advertisable" : {"item_table" : tag_table_name, "column" :"util_vals_advertisable"},
          "process_utils_is_status_open" : {"item_table" : tag_table_name, "column" :"util_vals_is_status_open"},
          "process_utils_ad_status" : {"item_table" : tag_table_name, "column" :"util_vals_ad_status"},
          "process_utils_unit_id" : {"item_table" : tag_table_name, "column" :"util_vals_unit_id"},
          "process_utils_thread_id" : {"item_table" : tag_table_name, "column" :"util_vals_thread_id"},
          "process_utils_is_bid_info" : {"item_table" : tag_table_name, "column" :"util_vals_is_bid_info"},
          "process_utils_is_online" : {"item_table" : tag_table_name, "column" :"util_vals_is_online"},
          "process_utils_is_explore" : {"item_table" : tag_table_name, "column" :"util_vals_is_explore"},
          "process_utils_ab_exp_ratio" : {"item_table" : tag_table_name, "column" :"util_vals_ab_exp_ratio"},
          "process_utils_promotion_type_num" : {"item_table" : tag_table_name, "column" :"util_vals_promotion_type_num"},
          "process_utils_item_type_num" : {"item_table" : tag_table_name, "column" :"util_vals_item_type_num"},
          "process_utils_prior_cost" : {"item_table" : tag_table_name, "column" :"util_vals_prior_cost"},
          "process_utils_cold_start_cost_li_thr" : {"item_table" : tag_table_name, "column" :"util_vals_cold_start_cost_li_thr"},
          "process_utils_cold_start_low_bound" : {"item_table" : tag_table_name, "column" :"util_vals_cold_start_low_bound"},
          "process_utils_rt_cost_speed" : {"item_table" : tag_table_name, "column" :"util_vals_rt_cost_speed"},
          "process_utils_rt_delivery_speed" : {"item_table" : tag_table_name, "column" :"util_vals_rt_delivery_speed"},
          "process_utils_target_roas" : {"item_table" : tag_table_name, "column" :"util_vals_target_roas"},
          "process_utils_is_price_ratio_bound" : {"item_table" : tag_table_name, "column" :"util_vals_is_price_ratio_bound"},
          "process_utils_is_acc_explore_bid" : {"item_table" : tag_table_name, "column" :"util_vals_is_acc_explore_bid"},
          "process_utils_dynamic_bid_lower_bound_adjust_time_interval_ms" : {"item_table" : tag_table_name, "column" :"util_vals_dynamic_bid_lower_bound_adjust_time_interval_ms"},
          "process_utils_dynamic_bid_upper_bound_adjust_time_interval_ms" : {"item_table" : tag_table_name, "column" :"util_vals_dynamic_bid_upper_bound_adjust_time_interval_ms"},
          "process_utils_dynamic_bid_lower_bound_adjust_rate" : {"item_table" : tag_table_name, "column" :"util_vals_dynamic_bid_lower_bound_adjust_rate"},
          "process_utils_dynamic_bid_upper_bound_adjust_rate" : {"item_table" : tag_table_name, "column" :"util_vals_dynamic_bid_upper_bound_adjust_rate"},
          "process_utils_p_weight" : {"item_table" : tag_table_name, "column" :"util_vals_p_weight"},
          "process_utils_i_weight" : {"item_table" : tag_table_name, "column" :"util_vals_i_weight"},
          "process_utils_d_weight" : {"item_table" : tag_table_name, "column" :"util_vals_d_weight"},
          "process_utils_explore_p_weight" : {"item_table" : tag_table_name, "column" :"util_vals_explore_p_weight"},
          "process_utils_explore_i_weight" : {"item_table" : tag_table_name, "column" :"util_vals_explore_i_weight"},
          "process_utils_explore_d_weight" : {"item_table" : tag_table_name, "column" :"util_vals_explore_d_weight"},
          "process_utils_p_val" : {"item_table" : tag_table_name, "column" :"util_vals_p_val"},
          "process_utils_i_val" : {"item_table" : tag_table_name, "column" :"util_vals_i_val"},
          "process_utils_d_val" : {"item_table" : tag_table_name, "column" :"util_vals_d_val"},
          "process_utils_pacing_weight" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_weight"},
          "process_utils_target_cost_speed" : {"item_table" : tag_table_name, "column" :"util_vals_target_cost_speed"},
          "process_utils_target_delivery_speed" : {"item_table" : tag_table_name, "column" :"util_vals_target_delivery_speed"},
          "process_utils_start_bid_rate" : {"item_table" : tag_table_name, "column" :"util_vals_start_bid_rate"},
          "process_utils_start_bid_rate_sub_pacing" : {"item_table" : tag_table_name, "column" :"util_vals_start_bid_rate_sub_pacing"},
          "process_utils_hard_upper_bound_auto_cpa_bid" : {"item_table" : tag_table_name, "column" :"util_vals_hard_upper_bound_auto_cpa_bid"},
          "process_utils_hard_lower_bound_auto_cpa_bid" : {"item_table" : tag_table_name, "column" :"util_vals_hard_lower_bound_auto_cpa_bid"},
          "process_utils_hard_lower_bound_auto_roas" : {"item_table" : tag_table_name, "column" :"util_vals_hard_lower_bound_auto_roas"},
          "process_utils_hard_upper_bound_auto_roas" : {"item_table" : tag_table_name, "column" :"util_vals_hard_upper_bound_auto_roas"},
          "process_utils_hard_lower_bound_auto_atv" : {"item_table" : tag_table_name, "column" :"util_vals_hard_lower_bound_auto_atv"},
          "process_utils_hard_upper_bound_auto_atv" : {"item_table" : tag_table_name, "column" :"util_vals_hard_upper_bound_auto_atv"},
          "process_utils_acc_explore_bid_target_cost_speed" : {"item_table" : tag_table_name, "column" :"util_vals_acc_explore_bid_target_cost_speed"},
          "process_utils_current_ms" : {"item_table" : tag_table_name, "column" :"util_vals_current_ms"},
          "process_utils_ad_short_type" : {"item_table" : tag_table_name, "column" :"util_vals_ad_short_type"},
          "process_utils_is_target_modify" : {"item_table" : tag_table_name, "column" :"util_vals_is_target_modify"},
          "process_utils_target_modify_ratio" : {"item_table" : tag_table_name, "column" :"util_vals_target_modify_ratio"},
          "process_utils_p_weight_conv" : {"item_table" : tag_table_name, "column" :"util_vals_p_weight_conv"},
          "process_utils_i_weight_conv" : {"item_table" : tag_table_name, "column" :"util_vals_i_weight_conv"},
          "process_utils_d_weight_conv" : {"item_table" : tag_table_name, "column" :"util_vals_d_weight_conv"},
          "process_utils_p_val_conv" : {"item_table" : tag_table_name, "column" :"util_vals_p_val_conv"},
          "process_utils_i_val_conv" : {"item_table" : tag_table_name, "column" :"util_vals_i_val_conv"},
          "process_utils_d_val_conv" : {"item_table" : tag_table_name, "column" :"util_vals_d_val_conv"},
          "process_utils_pacing_weight_conv" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_weight_conv"},
          "process_utils_p_weight_cost" : {"item_table" : tag_table_name, "column" :"util_vals_p_weight_cost"},
          "process_utils_i_weight_cost" : {"item_table" : tag_table_name, "column" :"util_vals_i_weight_cost"},
          "process_utils_d_weight_cost" : {"item_table" : tag_table_name, "column" :"util_vals_d_weight_cost"},
          "process_utils_p_val_cost" : {"item_table" : tag_table_name, "column" :"util_vals_p_val_cost"},
          "process_utils_i_val_cost" : {"item_table" : tag_table_name, "column" :"util_vals_i_val_cost"},
          "process_utils_d_val_cost" : {"item_table" : tag_table_name, "column" :"util_vals_d_val_cost"},
          "process_utils_pacing_weight_cost" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_weight_cost"},
          "process_utils_update_interval_ms" : {"item_table" : tag_table_name, "column" :"util_vals_update_interval_ms"},
          "process_utils_interval_cost" : {"item_table" : tag_table_name, "column" :"util_vals_interval_cost"},
          "process_utils_step_bid_rate_increase" : {"item_table" : tag_table_name, "column" :"util_vals_step_bid_rate_increase"},
          "process_utils_step_bid_rate_descent" : {"item_table" : tag_table_name, "column" :"util_vals_step_bid_rate_descent"},
          "process_utils_ad_status_tag" : {"item_table" : tag_table_name, "column" :"util_vals_ad_status_tag"},
          "process_utils_ad_status_tag_change" : {"item_table" : tag_table_name, "column" :"util_vals_ad_status_tag_change"},
          "process_utils_total_pred_conv" : {"item_table" : tag_table_name, "column" :"util_vals_total_pred_conv"},
          "process_utils_adjust_rate_lower_bound" : {"item_table" : tag_table_name, "column" :"util_vals_adjust_rate_lower_bound"},
          "process_utils_adjust_rate_upper_bound" : {"item_table" : tag_table_name, "column" :"util_vals_adjust_rate_upper_bound"},
          "process_utils_budget" : {"item_table" : tag_table_name, "column" :"util_vals_budget"},
          "process_utils_hard_bid_upper_bound_rate_monitor" : {"item_table" : tag_table_name, "column" :"util_vals_hard_bid_upper_bound_rate_monitor"},
          "process_utils_hard_bid_lower_bound_rate_monitor" : {"item_table" : tag_table_name, "column" :"util_vals_hard_bid_lower_bound_rate_monitor"},
          "process_utils_fanstop_adjust_rate_lower_bound_roi" : {"item_table" : tag_table_name, "column" :"util_vals_fanstop_adjust_rate_lower_bound_roi"},
          "process_utils_fanstop_adjust_rate_upper_bound_roi" : {"item_table" : tag_table_name, "column" :"util_vals_fanstop_adjust_rate_upper_bound_roi"},
          "process_utils_fanstop_adjust_rate_lower_bound_cpa" : {"item_table" : tag_table_name, "column" :"util_vals_fanstop_adjust_rate_lower_bound_cpa"},
          "process_utils_fanstop_adjust_rate_upper_bound_cpa" : {"item_table" : tag_table_name, "column" :"util_vals_fanstop_adjust_rate_upper_bound_cpa"},
          "process_utils_pacing_type" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_type"},
          "process_utils_pacing_method_type" : {"item_table" : tag_table_name, "column" :"util_vals_pacing_method_type"},
        },
      )
      return self
    def unit_ocpm_step_one_workflow(self):
        os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
        self.ad_message_queue_retrieve(max_queue_size=10000, queue_number = 200, message_queue_type=0) \
        .inner_loop_strategy_data_fetch(
          item_table = "TriggerTable",
          cluster_name = "BidServerGraphInner",
          candidate_cluster_name = "BidServerGraphTest",
          key_prefix = "inner_ctx_:",
          output_table_name = "UnitTagTable",
          local_cache_key_id = {"item_table" : "UnitTagTable", "column" : "local_cache_key_id"},
          cache_key_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
          group_tag = {"item_table" : "TriggerTable", "column" : "inner_group_tag"},
        ) \
        .inner_loop_table_lite_enrich(
          item_table = "TriggerTable",
          # 测试先关闭
          skip_wait_index = False,
          cache_key_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
          unit_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
          group_tag = {"item_table" : "TriggerTable", "column" : "inner_group_tag"},
          has_bid_state_info_ptr = {"item_table" : "UnitTagTable", "column" : "has_bid_state_info_ptr"},
          bid_state_info_ptr = {"item_table" : "UnitTagTable", "column" : "bid_state_info_ptr"},
          bid_state_info_explore_time_period = {"item_table" : "UnitTagTable", "column" : "bid_state_info_explore_time_period"},
          bid_state_info_explore_budget_start_time = {"item_table" : "UnitTagTable", "column" : "bid_state_info_explore_budget_start_time"},
          bid_state_info_explore_budget_status = {"item_table" : "UnitTagTable", "column" : "bid_state_info_explore_budget_status"},
          bid_state_info_explore_budget = {"item_table" : "UnitTagTable", "column" : "bid_state_info_explore_budget"},
          bid_state_info_is_live = {"item_table" : "UnitTagTable", "column" : "bid_state_info_is_live"},
          bid_state_info_ad_status = {"item_table" : "UnitTagTable", "column" : "bid_state_info_ad_status"},
          bid_state_info_advertisable = {"item_table" : "UnitTagTable", "column" : "bid_state_info_advertisable"},
          bid_state_info_is_status_open = {"item_table" : "UnitTagTable", "column" : "bid_state_info_is_status_open"},
          bid_state_info_online = {"item_table" : "UnitTagTable", "column" : "bid_state_info_online"},
          bid_state_info_left_budget = {"item_table" : "UnitTagTable", "column" : "bid_state_info_left_budget"},
          bid_state_info_budget = {"item_table" : "UnitTagTable", "column" : "bid_state_info_budget"},
          bid_state_info_account_type = {"item_table" : "UnitTagTable", "column" : "bid_state_info_account_type"},
          bid_state_info_bid_strategy = {"item_table" : "UnitTagTable", "column" : "bid_state_info_bid_strategy"},
          bid_state_info_bid_type = {"item_table" : "UnitTagTable", "column" : "bid_state_info_bid_type"},
          bid_state_info_live_launch_type = {"item_table" : "UnitTagTable", "column" : "bid_state_info_live_launch_type"},

          bid_state_info_account_id = {"item_table" : "UnitTagTable", "column" : "bid_state_info_account_id"},
          bid_state_info_campaign_id = {"item_table" : "UnitTagTable", "column" : "bid_state_info_campaign_id"},
          bid_state_info_author_id = {"item_table" : "UnitTagTable", "column" : "bid_state_info_author_id"},
          bid_state_info_live_stream_id = {"item_table" : "UnitTagTable", "column" : "bid_state_info_live_stream_id"},
          bid_state_info_speed_type = {"item_table" : "UnitTagTable", "column" : "bid_state_info_speed_type"},
          bid_state_info_promotion_type = {"item_table" : "UnitTagTable", "column" : "bid_state_info_promotion_type"},
          bid_state_info_campaign_type = {"item_table" : "UnitTagTable", "column" : "bid_state_info_campaign_type"},
          bid_state_info_cpa_bid = {"item_table" : "UnitTagTable", "column" : "bid_state_info_cpa_bid"},
          bid_state_info_roi_ratio = {"item_table" : "UnitTagTable", "column" : "bid_state_info_roi_ratio"},
        )\
        .ad_bid_ctx_accumulate("UnitTagTable", 0) \
        .ad_step_one_to_redis("UnitTagTable") \
        .inner_loop_strategy_data_save(
          tables = [
            {
              "table_name": "UnitTagTable",
              "table_category": "UnitTagTable",
              "save_all_columns": True,
              "local_cache_key_id": "local_cache_key_id"
            }
          ]
        ) \
        .ad_data_frame_write_to_redis(
          enable_switch = True,
          tables = [
            {
              "table_name": "UnitTagTable",
              "redis_name": "BidServerGraphInner",
              "candidate_redis_name": "BidServerGraphTest",
              "prefix": "inner_ctx_:",
              "ttl": 48 * 3600,
              "save_all_columns": True,
            }
          ]
        )\
        .inner_loop_ocpm_trigger(
          item_table = "UnitTagTable",
          key_id = {"item_table" : "UnitTagTable", "column" : "unit_id"},
          key_type = 0,
        )
        return self
    def unit_ocpm_step_two_workflow(self):
        os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
        self.if_("skip_step_two == 1") \
          .return_() \
        .end_if_() \
        .inner_loop_ocpm_calc_bid_prepare_mixer(
          item_table = "UnitTagTable",
        ) \
        .if_("is_skip_update == 1") \
          .inner_loop_strategy_data_save(
            tables = [
              {
                "table_name": "UnitTagTable",
                "table_category": "UnitTagTable",
                "save_all_columns": True,
                "local_cache_key_id": "local_cache_key_id"
              }
            ]
          )\
          .return_() \
        .end_if_() \
        .inner_loop_ocpm_calc_pacing_rate_acc_mixer(
          item_table = "UnitTagTable",
        ) \
        .inner_loop_ocpm_calc_pacing_rate_normal_mixer(
          item_table = "UnitTagTable",
        ) \
        .ad_ocpm_check_pacing() \
        .inner_loop_ocpm_calc_bid_mixer(
          item_table = "UnitTagTable",
        ) \
        .ad_bid_result_store_mix() \
        .ad_ocpm_monitor_mixer() \
        .ad_step_two_to_redis("UnitTagTable") \
        .inner_loop_strategy_data_save(
          tables = [
            {
              "table_name": "UnitTagTable",
              "table_category": "UnitTagTable",
              "save_all_columns": True,
              "local_cache_key_id": "local_cache_key_id"
            }
          ]
        )
        return self
    def account_ocpm_step_one_workflow(self):
      os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
      self.ad_message_queue_retrieve(max_queue_size=10000, queue_number = 200, message_queue_type=1) \
        .hot_message_sub(
            item_table = "TriggerTable",
            use_item_key = True,
            freq_type = 2,
          ) \
        .inner_loop_strategy_data_fetch(
          item_table = "TriggerTable",
          cluster_name = "BidServerGraphInner",
          candidate_cluster_name = "BidServerGraphTest",
          key_prefix = "inner_account_ctx_:",
          output_table_name = "AccountTagTable",
          local_cache_key_id = {"item_table" : "AccountTagTable", "column" : "local_cache_key_id"},
          cache_key_id = {"item_table" : "TriggerTable", "column" : "account_id"},
          group_tag = {"item_table" : "TriggerTable", "column" : "inner_group_tag"},
        ) \
        .inner_loop_strategy_data_fetch(
          item_table = "TriggerTable",
          cluster_name = "BidServerGraphInner",
          candidate_cluster_name = "BidServerGraphTest",
          key_prefix = "inner_ctx_:",
          output_table_name = "UnitTagTable",
          local_cache_key_id = {"item_table" : "UnitTagTable", "column" : "local_cache_key_id"},
          cache_key_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
          group_tag = {"item_table" : "TriggerTable", "column" : "inner_group_tag"},
        ) \
        .inner_loop_table_lite_enrich(
          item_table = "TriggerTable",
          # 测试先关闭
          skip_wait_index = False,
          cache_key_id = {"item_table" : "TriggerTable", "column" : "account_id"},
          unit_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
          group_tag = {"item_table" : "TriggerTable", "column" : "inner_group_tag"},
          has_bid_state_info_ptr = {"item_table" : "AccountTagTable", "column" : "has_bid_state_info_ptr"},
          bid_state_info_ptr = {"item_table" : "AccountTagTable", "column" : "bid_state_info_ptr"},
          bid_state_info_explore_time_period = {"item_table" : "AccountTagTable", "column" : "bid_state_info_explore_time_period"},
          bid_state_info_explore_budget_start_time = {"item_table" : "AccountTagTable", "column" : "bid_state_info_explore_budget_start_time"},
          bid_state_info_explore_budget_status = {"item_table" : "AccountTagTable", "column" : "bid_state_info_explore_budget_status"},
          bid_state_info_explore_budget = {"item_table" : "AccountTagTable", "column" : "bid_state_info_explore_budget"},
          bid_state_info_is_live = {"item_table" : "AccountTagTable", "column" : "bid_state_info_is_live"},
          bid_state_info_ad_status = {"item_table" : "AccountTagTable", "column" : "bid_state_info_ad_status"},
          bid_state_info_advertisable = {"item_table" : "AccountTagTable", "column" : "bid_state_info_advertisable"},
          bid_state_info_is_status_open = {"item_table" : "AccountTagTable", "column" : "bid_state_info_is_status_open"},
          bid_state_info_online = {"item_table" : "AccountTagTable", "column" : "bid_state_info_online"},
          bid_state_info_left_budget = {"item_table" : "AccountTagTable", "column" : "bid_state_info_left_budget"},
          bid_state_info_budget = {"item_table" : "AccountTagTable", "column" : "bid_state_info_budget"},
          bid_state_info_account_type = {"item_table" : "AccountTagTable", "column" : "bid_state_info_account_type"},
          bid_state_info_bid_strategy = {"item_table" : "AccountTagTable", "column" : "bid_state_info_bid_strategy"},
          bid_state_info_bid_type = {"item_table" : "AccountTagTable", "column" : "bid_state_info_bid_type"},
          bid_state_info_live_launch_type = {"item_table" : "AccountTagTable", "column" : "bid_state_info_live_launch_type"},

          bid_state_info_account_id = {"item_table" : "AccountTagTable", "column" : "bid_state_info_account_id"},
          bid_state_info_campaign_id = {"item_table" : "AccountTagTable", "column" : "bid_state_info_campaign_id"},
          bid_state_info_author_id = {"item_table" : "AccountTagTable", "column" : "bid_state_info_author_id"},
          bid_state_info_live_stream_id = {"item_table" : "AccountTagTable", "column" : "bid_state_info_live_stream_id"},
          bid_state_info_speed_type = {"item_table" : "AccountTagTable", "column" : "bid_state_info_speed_type"},
          bid_state_info_promotion_type = {"item_table" : "AccountTagTable", "column" : "bid_state_info_promotion_type"},
          bid_state_info_campaign_type = {"item_table" : "AccountTagTable", "column" : "bid_state_info_campaign_type"},
          bid_state_info_cpa_bid = {"item_table" : "AccountTagTable", "column" : "bid_state_info_cpa_bid"},
          bid_state_info_roi_ratio = {"item_table" : "AccountTagTable", "column" : "bid_state_info_roi_ratio"},
        )\
        .inner_loop_table_lite_enrich(
          item_table = "TriggerTable",
          # 测试先关闭
          skip_wait_index = False,
          cache_key_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
          unit_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
          group_tag = {"item_table" : "TriggerTable", "column" : "inner_group_tag"},
          has_bid_state_info_ptr = {"item_table" : "UnitTagTable", "column" : "has_bid_state_info_ptr"},
          bid_state_info_ptr = {"item_table" : "UnitTagTable", "column" : "bid_state_info_ptr"},
          bid_state_info_explore_time_period = {"item_table" : "UnitTagTable", "column" : "bid_state_info_explore_time_period"},
          bid_state_info_explore_budget_start_time = {"item_table" : "UnitTagTable", "column" : "bid_state_info_explore_budget_start_time"},
          bid_state_info_explore_budget_status = {"item_table" : "UnitTagTable", "column" : "bid_state_info_explore_budget_status"},
          bid_state_info_explore_budget = {"item_table" : "UnitTagTable", "column" : "bid_state_info_explore_budget"},
          bid_state_info_is_live = {"item_table" : "UnitTagTable", "column" : "bid_state_info_is_live"},
          bid_state_info_ad_status = {"item_table" : "UnitTagTable", "column" : "bid_state_info_ad_status"},
          bid_state_info_advertisable = {"item_table" : "UnitTagTable", "column" : "bid_state_info_advertisable"},
          bid_state_info_is_status_open = {"item_table" : "UnitTagTable", "column" : "bid_state_info_is_status_open"},
          bid_state_info_online = {"item_table" : "UnitTagTable", "column" : "bid_state_info_online"},
          bid_state_info_left_budget = {"item_table" : "UnitTagTable", "column" : "bid_state_info_left_budget"},
          bid_state_info_budget = {"item_table" : "UnitTagTable", "column" : "bid_state_info_budget"},
          bid_state_info_account_type = {"item_table" : "UnitTagTable", "column" : "bid_state_info_account_type"},
          bid_state_info_bid_strategy = {"item_table" : "UnitTagTable", "column" : "bid_state_info_bid_strategy"},
          bid_state_info_bid_type = {"item_table" : "UnitTagTable", "column" : "bid_state_info_bid_type"},
          bid_state_info_live_launch_type = {"item_table" : "UnitTagTable", "column" : "bid_state_info_live_launch_type"},

          bid_state_info_account_id = {"item_table" : "UnitTagTable", "column" : "bid_state_info_account_id"},
          bid_state_info_campaign_id = {"item_table" : "UnitTagTable", "column" : "bid_state_info_campaign_id"},
          bid_state_info_author_id = {"item_table" : "UnitTagTable", "column" : "bid_state_info_author_id"},
          bid_state_info_live_stream_id = {"item_table" : "UnitTagTable", "column" : "bid_state_info_live_stream_id"},
          bid_state_info_speed_type = {"item_table" : "UnitTagTable", "column" : "bid_state_info_speed_type"},
          bid_state_info_promotion_type = {"item_table" : "UnitTagTable", "column" : "bid_state_info_promotion_type"},
          bid_state_info_campaign_type = {"item_table" : "UnitTagTable", "column" : "bid_state_info_campaign_type"},
          bid_state_info_cpa_bid = {"item_table" : "UnitTagTable", "column" : "bid_state_info_cpa_bid"},
          bid_state_info_roi_ratio = {"item_table" : "UnitTagTable", "column" : "bid_state_info_roi_ratio"},
        )\
        .inner_loop_build_unit_list(
          item_table = "TriggerTable",
          account_id = {"item_table" : "TriggerTable", "column" : "account_id"},
          unit_id = {"item_table" : "TriggerTable", "column" : "unit_id"},
          group_tag = {"item_table" : "TriggerTable", "column" : "inner_group_tag"},
          account_unit_list = {"item_table" : "AccountTagTable", "column" : "unit_list"}, 
        )\
        .ad_bid_ctx_accumulate("AccountTagTable", 1)\
        .ad_step_one_to_redis("AccountTagTable")\
        .ad_bid_ctx_accumulate("UnitTagTable", 0)\
        .ad_step_one_to_redis("UnitTagTable")\
        .inner_loop_strategy_data_save(
          tables = [
            {
              "table_name": "UnitTagTable",
              "table_category": "UnitTagTable",
              "save_all_columns": True,
              "local_cache_key_id": "local_cache_key_id"
            }
          ]
        ) \
        .inner_loop_strategy_data_save(
          tables = [
            {
              "table_name": "AccountTagTable",
              "table_category": "AccountTagTable",
              "save_all_columns": True,
              "local_cache_key_id": "local_cache_key_id"
            }
          ]
        ) \
        .ad_data_frame_write_to_redis(
          enable_switch = True,
          tables = [
            {
              "table_name": "UnitTagTable",
              "redis_name": "BidServerGraphInner",
              "candidate_redis_name": "BidServerGraphTest",
              "prefix": "inner_ctx_:",
              "ttl": 48 * 3600,
              "save_all_columns": True,
            }
          ]
        )\
        .ad_data_frame_write_to_redis(
          enable_switch = True,
          tables = [
            {
              "table_name": "AccountTagTable",
              "redis_name": "BidServerGraphInner",
              "candidate_redis_name": "BidServerGraphTest",
              "prefix": "inner_account_ctx_:",
              "ttl": 48 * 3600,
              "save_all_columns": True,
            }
          ]
        )\
        .inner_loop_ocpm_trigger(
          item_table = "AccountTagTable",
          key_id = {"item_table" : "AccountTagTable", "column" : "account_id"},
          key_type = 1,
        )
      return self
    def account_ocpm_step_two_workflow(self):
      os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
      self.if_("skip_step_two == 1") \
        .return_() \
      .end_if_()
      return self
    def reset_task_flow(self):
        self.sleep(10000)
        return self
    def data_init(self):
        self.data_prepare_init(
          use_bid_index = True,
          p2p_name_attrs = ["conv_ratio_author_post_data"],
        ) \
        .sleep(*********)
        return self
