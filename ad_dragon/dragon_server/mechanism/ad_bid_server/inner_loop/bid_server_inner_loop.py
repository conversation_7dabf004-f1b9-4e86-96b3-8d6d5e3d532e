#!/usr/bin/env python3
# coding=utf-8

import os
import sys
dragon_path=os.path.abspath('../../../../../../../dragon/')
ad_dragon_path=os.path.abspath('../../../../../ad_dragon/')
sys.path.append(dragon_path)
sys.path.append(ad_dragon_path)

from dragonfly.common_leaf_dsl import Leaf<PERSON>low, OfflineRunner, LeafService, IndexSource
from ad_dragonfly.mechanism.ad_api_mixin import AdApiMixin
from ad_log_inner_flow import AdLogInnerFlow

extra_config = dict(
  subflow_worker_thread_task_queue_size = 1000,
  sub_flow_worker_num = 500,

  shard_num = 12,

  zk_dragon_bidserver_inner_loop_main = dict(
    zk_host = "ad.zk.cluster.zw:2181",
    zk_path = "/ks2/ad/dragon_bidServer_inner_loop/master"
  ),

  zk_dragon_bidserver_inner_loop_main_shard0 = dict(
    zk_host = "ad.zk.cluster.zw:2181",
    zk_path = "/ks2/ad/dragon_bidServer_inner_loop_0/master"
  ),

  zk_dragon_bidserver_inner_loop_main_shard1 = dict(
    zk_host = "ad.zk.cluster.zw:2181",
    zk_path = "/ks2/ad/dragon_bidServer_inner_loop_1/master"
  ),

  zk_dragon_bidserver_inner_loop_main_shard2 = dict(
    zk_host = "ad.zk.cluster.zw:2181",
    zk_path = "/ks2/ad/dragon_bidServer_inner_loop_2/master"
  ),

  zk_dragon_bidserver_inner_loop_main_shard3 = dict(
    zk_host = "ad.zk.cluster.zw:2181",
    zk_path = "/ks2/ad/dragon_bidServer_inner_loop_3/master"
  ),

  zk_dragon_bidserver_inner_loop_main_shard4 = dict(
    zk_host = "ad.zk.cluster.zw:2181",
    zk_path = "/ks2/ad/dragon_bidServer_inner_loop_4/master"
  ),

  zk_dragon_bidserver_inner_loop_main_shard5 = dict(
    zk_host = "ad.zk.cluster.zw:2181",
    zk_path = "/ks2/ad/dragon_bidServer_inner_loop_5/master"
  ),

  zk_dragon_bidserver_inner_loop_main_shard6 = dict(
    zk_host = "ad.zk.cluster.zw:2181",
    zk_path = "/ks2/ad/dragon_bidServer_inner_loop_6/master"
  ),

  zk_dragon_bidserver_inner_loop_main_shard7 = dict(
    zk_host = "ad.zk.cluster.zw:2181",
    zk_path = "/ks2/ad/dragon_bidServer_inner_loop_7/master"
  ),

  zk_dragon_bidserver_inner_loop_main_shard8 = dict(
    zk_host = "ad.zk.cluster.zw:2181",
    zk_path = "/ks2/ad/dragon_bidServer_inner_loop_8/master"
  ),

  zk_dragon_bidserver_inner_loop_main_shard9 = dict(
    zk_host = "ad.zk.cluster.zw:2181",
    zk_path = "/ks2/ad/dragon_bidServer_inner_loop_9/master"
  ),

  zk_dragon_bidserver_inner_loop_main_shard10 = dict(
    zk_host = "ad.zk.cluster.zw:2181",
    zk_path = "/ks2/ad/dragon_bidServer_inner_loop_10/master"
  ),

  zk_dragon_bidserver_inner_loop_main_shard11 = dict(
    zk_host = "ad.zk.cluster.zw:2181",
    zk_path = "/ks2/ad/dragon_bidServer_inner_loop_11/master"
  ),

  zk_dragon_bidserver_inner_loop_main_test = dict(
    zk_host = "ad.zk.cluster.zw:2181",
    zk_path = "/ks2/ad/dragon_bidServer_inner_loop_test/master"
  ),

  leaf_flow_create_order = ["data_init", "reset", "unit_ocpm", "account_ocpm", "adlog"],
)

ocpm_flow_thread_num = 200
adlog_flow = AdLogInnerFlow(name="adlog_flow") \
                .ad_log_flow_prepare() \
                .ad_log_flow(ocpm_flow_thread_num)
unit_ocpm_workflow = AdLogInnerFlow(name='unit_ocpm_workflow') \
                .unit_ocpm_step_one_workflow() \
                .unit_ocpm_step_two_workflow()
account_ocpm_workflow = AdLogInnerFlow(name='account_ocpm_workflow') \
                .account_ocpm_step_one_workflow() \
                .account_ocpm_step_two_workflow()
reset_flow = AdLogInnerFlow(name='reset_flow').reset_task_flow()
datainit_flow = AdLogInnerFlow(name='data_init').data_init()

bid_server_runner = OfflineRunner("ad-dragon-inner-loop-bid-server")
bid_server_runner.add_leaf_flows(leaf_flows=[unit_ocpm_workflow], name="unit_ocpm", thread_num=ocpm_flow_thread_num)
bid_server_runner.add_leaf_flows(leaf_flows=[account_ocpm_workflow], name="account_ocpm", thread_num=ocpm_flow_thread_num)
bid_server_runner.add_leaf_flows(leaf_flows=[adlog_flow], name="adlog", thread_num=20)
bid_server_runner.add_leaf_flows(leaf_flows=[datainit_flow], name="data_init", thread_num=1)
bid_server_runner.add_leaf_flows(leaf_flows=[reset_flow], name="reset", thread_num=1)

current_dir = os.path.dirname(__file__)
bid_server_runner.build(output_file=os.path.join(current_dir, "../pub/ad-dragon-inner-loop-bid-server/config/dynamic_json_config.json"),
                        extra_fields=extra_config, sort_keys=False)

