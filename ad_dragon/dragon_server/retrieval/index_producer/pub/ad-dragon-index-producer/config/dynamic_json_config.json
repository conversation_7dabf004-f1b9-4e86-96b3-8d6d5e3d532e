{"_DRAGONFLY_VERSION": "0.7.20", "_DRAGONFLY_CREATE_TIME": "2023-12-13 14:54:25", "_CONFIG_VERSION": "5048363583d5eda71218b4bd0e030598_local", "pipeline_manager_config": {"base_pipeline": {"type_name": "CommonRecoPipeline", "processor": {"_branch_controller_4A2C857D": {"import_common_attr": ["zk_master_stat"], "export_common_attr": ["_if_control_attr_1"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (zk_master_stat == 1) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_4A2C857D", "$code_info": "[if] 4A2C857D filter_trace.py in trace_filter_reason(): if_(\"zk_master_stat == 1\").", "$metadata": {"$input_common_attrs": ["zk_master_stat"], "$input_item_attrs": [], "$output_common_attrs": ["_if_control_attr_1"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_DF718299": {"import_common_attr": ["_if_control_attr_1", "zk_node_name"], "export_common_attr": ["_switch_control_attr_1"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (_if_control_attr_1 == 0 and ((zk_node_name) == 'dragon_index_producer_external')) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_DF718299", "$code_info": "[case] DF718299 filter_trace.py in trace_filter_reason(): case_(\"dragon_index_producer_external\").", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "zk_node_name"], "$input_item_attrs": [], "$output_common_attrs": ["_switch_control_attr_1"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_D9171353": {"import_common_attr": ["_if_control_attr_1", "zk_node_name"], "export_common_attr": ["_switch_control_attr_2"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (_if_control_attr_1 == 0 and ((zk_node_name) == 'dragon_index_producer_inner_hard_photo')) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_DF718299", "$code_info": "[case] D9171353 filter_trace.py in trace_filter_reason(): case_(\"dragon_index_producer_inner_hard_photo\").", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "zk_node_name"], "$input_item_attrs": [], "$output_common_attrs": ["_switch_control_attr_2"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_65084186": {"import_common_attr": ["_if_control_attr_1", "zk_node_name"], "export_common_attr": ["_switch_control_attr_3"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (_if_control_attr_1 == 0 and ((zk_node_name) == 'dragon_index_producer_inner_soft_photo')) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_DF718299", "$code_info": "[case] 65084186 filter_trace.py in trace_filter_reason(): case_(\"dragon_index_producer_inner_soft_photo\").", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "zk_node_name"], "$input_item_attrs": [], "$output_common_attrs": ["_switch_control_attr_3"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_989F92EC": {"import_common_attr": ["_if_control_attr_1", "zk_node_name"], "export_common_attr": ["_switch_control_attr_4"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (_if_control_attr_1 == 0 and ((zk_node_name) == 'dragon_index_producer_live')) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_DF718299", "$code_info": "[case] 989F92EC filter_trace.py in trace_filter_reason(): case_(\"dragon_index_producer_live\").", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "zk_node_name"], "$input_item_attrs": [], "$output_common_attrs": ["_switch_control_attr_4"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_DF718299_default": {"import_common_attr": ["_if_control_attr_1", "_switch_control_attr_1", "_switch_control_attr_2", "_switch_control_attr_3", "_switch_control_attr_4"], "export_common_attr": ["_switch_control_attr_5"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (_if_control_attr_1 == 0 and (_switch_control_attr_1 == 1 and _switch_control_attr_2 == 1 and _switch_control_attr_3 == 1 and _switch_control_attr_4 == 1)) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_DF718299", "$code_info": "[default] FB52CA10 filter_trace.py in trace_filter_reason(): output_attr='ad_table_filter_reason'", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "_switch_control_attr_1", "_switch_control_attr_2", "_switch_control_attr_3", "_switch_control_attr_4"], "$input_item_attrs": [], "$output_common_attrs": ["_switch_control_attr_5"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_4A2C857D_else": {"import_common_attr": ["_if_control_attr_1"], "export_common_attr": ["_else_control_attr_2"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (_if_control_attr_1 == 1) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_4A2C857D", "$code_info": "[else] 2A2DDB45 filter_trace.py in trace_filter_reason(): sleep(600000).", "$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$output_common_attrs": ["_else_control_attr_2"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "sleep_4ABCFC": {"sleep_ms": 30000, "skip": "{{_else_control_attr_2}}", "$metadata": {"$input_common_attrs": ["_else_control_attr_2"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoSleepObserver"}, "sleep_C2B90F": {"sleep_ms": 600000, "skip": "{{_if_control_attr_1}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoSleepObserver"}, "send_with_kafka_4A9D85": {"common_attr": "ad_table_filter_reason", "user_params": "queue.buffering.max.messages=5000000;queue.buffering.max.kbytes=0x100000000", "topic_name": "index_data_trace_info", "skip": "{{_if_control_attr_1}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "ad_table_filter_reason"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoKafkaObserver"}, "sleep_BA6293": {"sleep_ms": 1000, "skip": "{{_switch_control_attr_5}}", "$metadata": {"$input_common_attrs": ["_switch_control_attr_5"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoSleepObserver"}, "ad_table_filter_reason_581FCE": {"trace_tag": 9, "output_attr": "ad_table_filter_reason", "skip": "{{_switch_control_attr_4}}", "$metadata": {"$input_common_attrs": ["_switch_control_attr_4"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdTaleFiltReasonMixer"}, "ad_table_filter_reason_2B3531": {"trace_tag": 8, "output_attr": "ad_table_filter_reason", "skip": "{{_switch_control_attr_3}}", "$metadata": {"$input_common_attrs": ["_switch_control_attr_3"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdTaleFiltReasonMixer"}, "ad_table_filter_reason_2EA9A6": {"trace_tag": 7, "output_attr": "ad_table_filter_reason", "skip": "{{_switch_control_attr_2}}", "$metadata": {"$input_common_attrs": ["_switch_control_attr_2"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdTaleFiltReasonMixer"}, "ad_table_filter_reason_2BBD4F": {"trace_tag": 6, "output_attr": "ad_table_filter_reason", "skip": "{{_switch_control_attr_1}}", "$metadata": {"$input_common_attrs": ["_switch_control_attr_1"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdTaleFiltReasonMixer"}, "ad_zk_selector_mixer_F34A75": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdZKSelectorMixer"}, "get_kconf_params_CF5E0E": {"kconf_configs": [{"kconf_key": "ad.ad_index_producer.zkNodeName", "value_type": "string", "export_common_attr": "zk_node_name"}], "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": ["zk_node_name"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoKconfCommonAttrEnricher"}, "_branch_controller_4A2C857D_1": {"import_common_attr": ["zk_master_stat"], "export_common_attr": ["_if_control_attr_3"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (zk_master_stat == 1) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_4A2C857D_1", "$code_info": "[if] 4A2C857D gclist_producer.py in generate_gc_list_flow(): if_(\"zk_master_stat == 1\").", "$metadata": {"$input_common_attrs": ["zk_master_stat"], "$input_item_attrs": [], "$output_common_attrs": ["_if_control_attr_3"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "sleep_5B96A1": {"sleep_ms": 30000, "$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoSleepObserver"}, "generate_gc_list_9BE491": {"skip": "{{_if_control_attr_3}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_3"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "AdTableGCListMixer"}, "index_perf_7EE65B": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "IndexPerfMixer"}, "_branch_controller_4A2C857D_2": {"import_common_attr": ["zk_master_stat"], "export_common_attr": ["_if_control_attr_4"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (zk_master_stat == 1) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_4A2C857D_2", "$code_info": "[if] 4A2C857D gclist_diff.py in gc_list_diff_flow(): if_(\"zk_master_stat == 1\").", "$metadata": {"$input_common_attrs": ["zk_master_stat"], "$input_item_attrs": [], "$output_common_attrs": ["_if_control_attr_4"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "gc_list_diff_B73B84": {"skip": "{{_if_control_attr_4}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_4"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "GCListDiffMixer"}, "_branch_controller_4A2C857D_3": {"import_common_attr": ["zk_master_stat"], "export_common_attr": ["_if_control_attr_5"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (zk_master_stat == 1) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_4A2C857D_3", "$code_info": "[if] 4A2C857D gc_list_trace.py in trace_gc_list(): if_(\"zk_master_stat == 1\").", "$metadata": {"$input_common_attrs": ["zk_master_stat"], "$input_item_attrs": [], "$output_common_attrs": ["_if_control_attr_5"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_4A2C857D_3_else": {"import_common_attr": ["_if_control_attr_5"], "export_common_attr": ["_else_control_attr_6"], "function_for_common": "evaluate", "lua_script": "function evaluate() if (_if_control_attr_5 == 1) then return false else return true end end", "for_branch_control": true, "$branch_start": "_branch_controller_4A2C857D_3", "$code_info": "[else] 7EB3622A gc_list_trace.py in trace_gc_list(): sleep(30000).", "$metadata": {"$input_common_attrs": ["_if_control_attr_5"], "$input_item_attrs": [], "$output_common_attrs": ["_else_control_attr_6"], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoLuaAttrEnricher"}, "sleep_B605EA": {"sleep_ms": 30000, "skip": "{{_else_control_attr_6}}", "$metadata": {"$input_common_attrs": ["_else_control_attr_6"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoSleepObserver"}, "sleep_EF8E20": {"sleep_ms": 30000, "skip": "{{_if_control_attr_5}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_5"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoSleepObserver"}, "send_with_kafka_414C7C": {"common_attr": "gc_list_trace_message", "user_params": "queue.buffering.max.messages=5000000;queue.buffering.max.kbytes=0x100000000", "topic_name": "ad_target_trace_gclist", "skip": "{{_if_control_attr_5}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_5", "gc_list_trace_message"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "CommonRecoKafkaObserver"}, "get_gc_list_trace_7483C5": {"output_attr": "gc_list_trace_message", "skip": "{{_if_control_attr_5}}", "$metadata": {"$input_common_attrs": ["_if_control_attr_5"], "$input_item_attrs": [], "$output_common_attrs": [], "$output_item_attrs": [], "$modify_item_tables": []}, "type_name": "GcListTraceMixer"}}}, "pipeline_map": {"trace_filter_reason_flow": {"__PARENT": "base_pipeline", "pipeline": ["get_kconf_params_CF5E0E", "ad_zk_selector_mixer_F34A75", "_branch_controller_4A2C857D", "_branch_controller_DF718299", "ad_table_filter_reason_2BBD4F", "_branch_controller_D9171353", "ad_table_filter_reason_2EA9A6", "_branch_controller_65084186", "ad_table_filter_reason_2B3531", "_branch_controller_989F92EC", "ad_table_filter_reason_581FCE", "_branch_controller_DF718299_default", "sleep_BA6293", "send_with_kafka_4A9D85", "sleep_C2B90F", "_branch_controller_4A2C857D_else", "sleep_4ABCFC"]}, "gc_list_flow": {"__PARENT": "base_pipeline", "pipeline": ["ad_zk_selector_mixer_F34A75", "_branch_controller_4A2C857D_1", "generate_gc_list_9BE491", "sleep_5B96A1"]}, "perf_index_flow": {"__PARENT": "base_pipeline", "pipeline": ["index_perf_7EE65B", "sleep_5B96A1"]}, "gc_list_diff_flow": {"__PARENT": "base_pipeline", "pipeline": ["ad_zk_selector_mixer_F34A75", "_branch_controller_4A2C857D_2", "gc_list_diff_B73B84", "sleep_5B96A1"]}, "gc_list_trace_flow": {"__PARENT": "base_pipeline", "pipeline": ["ad_zk_selector_mixer_F34A75", "_branch_controller_4A2C857D_3", "get_gc_list_trace_7483C5", "send_with_kafka_414C7C", "sleep_EF8E20", "_branch_controller_4A2C857D_3_else", "sleep_B605EA"]}}}, "runner_pipeline_group": {"trace_filter_reason_flow": {"pipeline": ["trace_filter_reason_flow"], "thread_num": 1, "core_num_thread_ratio": 0.0}, "gc_list_flow": {"pipeline": ["gc_list_flow"], "thread_num": 1, "core_num_thread_ratio": 0.0}, "perf_index_flow": {"pipeline": ["perf_index_flow"], "thread_num": 1, "core_num_thread_ratio": 0.0}, "gc_list_diff_flow": {"pipeline": ["gc_list_diff_flow"], "thread_num": 1, "core_num_thread_ratio": 0.0}, "gc_list_trace_flow": {"pipeline": ["gc_list_trace_flow"], "thread_num": 1, "core_num_thread_ratio": 0.0}}, "service_identifier": "ad-dragon-index-producer", "kess_config": {}, "dragon_index_producer_external": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_index_producer_external/master"}, "dragon_index_producer_inner_hard_photo": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_index_producer_inner_hard_photo/master"}, "dragon_index_producer_inner_soft_photo": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_index_producer_inner_soft_photo/master"}, "dragon_index_producer_live": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_index_producer_live/master"}, "dragon_index_producer_test": {"zk_host": "ad.zk.cluster.zw:2181", "zk_path": "/ks2/ad/dragon_index_producer_test/master"}, "leaf_flow_create_order": ["trace_filter_reason_flow", "gc_list_flow", "gc_list_diff_flow", "perf_index_flow", "gc_list_trace_flow"]}