#!/usr/bin/env python3
# coding=utf-8

import os
import sys
dragon_path=os.path.abspath('../../../../../../dragon/')
ad_dragon_path=os.path.abspath('../../../../ad_dragon/')
sys.path.append(dragon_path)
sys.path.append(ad_dragon_path)

from dragonfly.common_leaf_dsl import LeafFlow, OfflineRunner, LeafService, IndexSource
# 改成自己的 api定义文件
from ad_dragonfly.retrieval.index_producer.ad_index_producer_mixin import AdIndexProducerMixin

class GCListFlow(LeafFlow, AdIndexProducerMixin):
  def generate_gc_list_flow(self):
    self.ad_zk_selector_mixer().\
    if_("zk_master_stat == 1").\
      generate_gc_list().\
    end_if_().\
    sleep(30000)
    return self
