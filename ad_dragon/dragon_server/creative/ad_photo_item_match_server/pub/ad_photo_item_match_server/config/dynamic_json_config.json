{"_CONFIG_VERSION": "34275975ebce19e160774b0a297c3466_local", "_DRAGONFLY_CREATE_TIME": "2023-10-31 12:25:51", "_DRAGONFLY_VERSION": "0.7.19", "attrs_from_request": {"common_attr": ["item_id", "method", "photo_id", "user_id"]}, "default_request_type": "default", "grpc": {"client_map": {}, "server": {"grpc_cq_num": 8, "kcs_grpc_port_key": "AUTO_PORT1", "kess_name": "USE_KSN_AS_SERVICE", "port": 20182, "quit_wait_seconds": 85, "start_warmup_seconds": 10, "thread_num": 100}, "test": false}, "kess_config": {"service_name": "ad-photo-item-match-service"}, "pipeline_manager_config": {"base_pipeline": {"processor": {"_branch_controller_70801CA1": {"$branch_start": "_branch_controller_70801CA1", "$code_info": "[if] 70801CA1 ad_photo_item_match_server.py:333 in <module>(): .if_(\"method_int == 1\")", "$metadata": {"$input_common_attrs": ["method_int"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_3"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_3"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["method_int"], "lua_script": "function evaluate() if (method_int == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_EA09D36A": {"$branch_start": "_branch_controller_EA09D36A", "$code_info": "[if] EA09D36A ad_photo_item_match_server.py:336 in <module>(): .if_(\"method_int == 2\")", "$metadata": {"$input_common_attrs": ["method_int"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_5"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_5"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["method_int"], "lua_script": "function evaluate() if (method_int == 2) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_user_id::_branch_controller_C9263299": {"$branch_start": "get_user_id::_branch_controller_C9263299", "$code_info": "[if] C9263299 ad_photo_item_match_server.py:205 in get_user_id(): self.if_(\"user_id == 0 and photo_id ~= 0\")", "$metadata": {"$input_common_attrs": ["photo_id", "user_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_2"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_2"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["photo_id", "user_id"], "lua_script": "function evaluate() if (user_id == 0 and photo_id ~= 0) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_user_id::build_protobuf_74448D": {"$metadata": {"$input_common_attrs": ["_if_control_attr_2", "photo_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_info_request"], "$output_item_attrs": []}, "class_name": "kuaishou.negative.GetByPhotoIdsRequest", "inputs": [{"append": true, "common_attr": "photo_id", "path": "photo_id"}], "output_common_attr": "photo_info_request", "skip": "{{_if_control_attr_2}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_user_id::build_table_from_common_list_attr_1C4B2C": {"$metadata": {"$input_common_attrs": ["_if_control_attr_2"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "build_config": [{"dest": "photo_id", "src": "photo_ids"}, {"dest": "user_id", "src": "user_ids"}], "new_table": "photo_info_table", "skip": "{{_if_control_attr_2}}", "type_name": "BuildNewTableFromCommonListAttrRetriever"}, "get_user_id::calc_time_cost": {"$metadata": {"$input_common_attrs": ["get_user_idTimeCostEnd", "get_user_idTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_user_idTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["get_user_idTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["get_user_idTimeCostEnd", "get_user_idTimeCostStart"], "lua_script": "function gen_common_attrs() return (get_user_idTimeCostEnd - get_user_idTimeCostStart) end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_user_id::calc_time_cost_e": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_user_idTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_user_idTimeCostEnd", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_user_id::calc_time_cost_s": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_user_idTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_user_idTimeCostStart", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_user_id::copy_attr_E71EA4": {"$metadata": {"$input_common_attrs": ["_if_control_attr_2"], "$input_item_attrs": ["user_id"], "$modify_item_tables": [], "$output_common_attrs": ["user_id"], "$output_item_attrs": []}, "attrs": [{"from_item": "user_id", "to_common": "user_id"}], "item_table": "photo_info_table", "skip": "{{_if_control_attr_2}}", "type_name": "CommonRecoCopyAttrEnricher"}, "get_user_id::enrich_by_generic_grpc_51F939": {"$metadata": {"$input_common_attrs": ["_if_control_attr_2", "photo_info_request"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_info_response"], "$output_item_attrs": []}, "downstream_processor": "get_user_id::enrich_with_protobuf_538346", "kess_service": "grpc_apiCorePhotoService", "method_name": "/kuaishou.negative.PhotoServiceRpc/GetByIdsContainsDeleted", "request_attr": "photo_info_request", "response_attr": "photo_info_response", "response_class": "kuaishou.negative.PhotoMapResponse", "skip": "{{_if_control_attr_2}}", "timeout_ms": 1000, "type_name": "CommonRecoGenericGrpcEnricher"}, "get_user_id::enrich_with_protobuf_538346": {"$metadata": {"$input_common_attrs": ["_if_control_attr_2", "photo_info_response"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_ids", "user_ids"], "$output_item_attrs": []}, "attrs": [{"name": "photo_ids", "path": "photos.key"}, {"name": "user_ids", "path": "photos.value.userId"}], "from_extra_var": "photo_info_response", "skip": "{{_if_control_attr_2}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "get_user_id::perf_time_cost": {"$metadata": {"$input_common_attrs": ["get_user_idTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:get_user_id", "common_attrs": ["get_user_idTimeCost"], "perf_base": 1, "type_name": "CommonRecoAttrValuePerflogObserver"}, "photo_item_match::build_protobuf_403613": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5", "long_value_type", "photo_id_name"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_id_result_info"], "$output_item_attrs": []}, "class_name": "com.kuaishou.ad.creative.engine.proto.ResultInfo", "inputs": [{"common_attr": "photo_id_name", "path": "field_name"}, {"common_attr": "long_value_type", "path": "value_type"}], "output_common_attr": "photo_id_result_info", "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "photo_item_match::build_protobuf_794B25": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5", "item_id_name", "long_value_type"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_id_result_info"], "$output_item_attrs": []}, "class_name": "com.kuaishou.ad.creative.engine.proto.ResultInfo", "inputs": [{"common_attr": "item_id_name", "path": "field_name"}, {"common_attr": "long_value_type", "path": "value_type"}], "output_common_attr": "item_id_result_info", "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "photo_item_match::build_protobuf_7FF281": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5", "embedding_result_info", "photo_emb_table_name", "photo_id_result_info", "photo_search_prefix", "photo_search_truncate_num"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_search_request"], "$output_item_attrs": []}, "class_name": "com.kuaishou.ad.creative.engine.proto.ItemSearchRequest", "inputs": [{"common_attr": "photo_emb_table_name", "path": "table_name"}, {"common_attr": "photo_search_prefix", "path": "row_prefix"}, {"append": true, "common_attr": "embedding_result_info", "path": "result_infos"}, {"append": true, "common_attr": "photo_id_result_info", "path": "result_infos"}, {"common_attr": "photo_search_truncate_num", "path": "truncate_num"}], "output_common_attr": "photo_search_request", "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "photo_item_match::build_protobuf_84A2EC": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5", "embedding_result_info", "item_emb_table_name", "item_id_result_info", "item_search_prefix", "item_search_truncate_num", "update_time_result_info"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_search_request"], "$output_item_attrs": []}, "class_name": "com.kuaishou.ad.creative.engine.proto.ItemSearchRequest", "inputs": [{"common_attr": "item_emb_table_name", "path": "table_name"}, {"common_attr": "item_search_prefix", "path": "row_prefix"}, {"append": true, "common_attr": "item_id_result_info", "path": "result_infos"}, {"append": true, "common_attr": "embedding_result_info", "path": "result_infos"}, {"append": true, "common_attr": "update_time_result_info", "path": "result_infos"}, {"common_attr": "item_search_truncate_num", "path": "truncate_num"}], "output_common_attr": "item_search_request", "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "photo_item_match::build_protobuf_C1821A": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5", "embedding_name", "str_value_type"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_result_info"], "$output_item_attrs": []}, "class_name": "com.kuaishou.ad.creative.engine.proto.ResultInfo", "inputs": [{"common_attr": "embedding_name", "path": "field_name"}, {"common_attr": "str_value_type", "path": "value_type"}], "output_common_attr": "embedding_result_info", "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "photo_item_match::build_protobuf_F2D9B9": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5", "long_value_type", "update_time_name"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["update_time_result_info"], "$output_item_attrs": []}, "class_name": "com.kuaishou.ad.creative.engine.proto.ResultInfo", "inputs": [{"common_attr": "update_time_name", "path": "field_name"}, {"common_attr": "long_value_type", "path": "value_type"}], "output_common_attr": "update_time_result_info", "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "photo_item_match::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5", "photo_item_matchTimeCostEnd", "photo_item_matchTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_item_matchTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["photo_item_matchTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["photo_item_matchTimeCostEnd", "photo_item_matchTimeCostStart"], "lua_script": "function gen_common_attrs() return (photo_item_matchTimeCostEnd - photo_item_matchTimeCostStart) end", "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoLuaAttrEnricher"}, "photo_item_match::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_item_matchTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "photo_item_matchTimeCostEnd", "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "photo_item_match::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_item_matchTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "photo_item_matchTimeCostStart", "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "photo_item_match::enrich_attr_by_lua_3591DC": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5", "item_id", "photo_id", "user_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_search_prefix", "photo_search_prefix"], "$output_item_attrs": []}, "export_common_attr": ["item_search_prefix", "photo_search_prefix"], "function_for_common": "toString", "import_common_attr": ["item_id", "photo_id", "user_id"], "lua_script": "function toString()\n          return tostring(user_id)..\"_\"..tostring(item_id),tostring(user_id)..\"_\"..tostring(photo_id)\n        end", "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoLuaAttrEnricher"}, "photo_item_match::enrich_by_generic_grpc_170B4E": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5", "item_search_request"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_search_response"], "$output_item_attrs": []}, "downstream_processor": "photo_item_match::photo_item_inner_product_retrieve_B99069", "kess_service": "ad-creative-item-search-service", "method_name": "/com.kuaishou.ad.creative.engine.proto.AdCreativeItemSearchService/ItemSearch", "request_attr": "item_search_request", "response_attr": "item_search_response", "response_class": "com.kuaishou.ad.creative.engine.proto.ItemSearchResponse", "skip": "{{_if_control_attr_5}}", "timeout_ms": 10000, "type_name": "CommonRecoGenericGrpcEnricher"}, "photo_item_match::enrich_by_generic_grpc_ABA13E": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5", "photo_search_request"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_search_response"], "$output_item_attrs": []}, "downstream_processor": "photo_item_match::photo_item_inner_product_retrieve_B99069", "kess_service": "ad-creative-item-search-service", "method_name": "/com.kuaishou.ad.creative.engine.proto.AdCreativeItemSearchService/ItemSearch", "request_attr": "photo_search_request", "response_attr": "photo_search_response", "response_class": "com.kuaishou.ad.creative.engine.proto.ItemSearchResponse", "skip": "{{_if_control_attr_5}}", "timeout_ms": 10000, "type_name": "CommonRecoGenericGrpcEnricher"}, "photo_item_match::log_debug_info_2D6614": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5", "item_id", "photo_id", "user_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "common_attrs": ["item_id", "photo_id", "user_id"], "for_debug_request_only": false, "item_attrs": [""], "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoDebugInfoObserver"}, "photo_item_match::log_debug_info_898F72": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5", "item_id", "item_search_response", "photo_id", "photo_search_response", "user_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "common_attrs": ["item_search_response", "photo_search_response", "user_id", "item_id", "photo_id"], "for_debug_request_only": false, "item_attrs": [""], "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoDebugInfoObserver"}, "photo_item_match::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5", "photo_item_matchTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:photo_item_match", "common_attrs": ["photo_item_matchTimeCost"], "perf_base": 1, "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "photo_item_match::photo_item_inner_product_retrieve_B99069": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5", "item_search_response", "photo_search_response"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "embedding_column": "serialized_embedding", "item_response_column": "item_search_response", "photo_response_column": "photo_search_response", "skip": "{{_if_control_attr_5}}", "type_name": "PhotoItemInnerProductRetriever"}, "photo_item_match::sort_by_score_18CD92": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5"], "$input_item_attrs": [], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoScoreSortArranger"}, "photo_user_match::_branch_controller_E363CFBF": {"$branch_start": "photo_user_match::_branch_controller_E363CFBF", "$code_info": "[if] E363CFBF ad_photo_item_match_server.py:113 in photo_user_match(): ).if_(\"is_photo_valid == 0\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_3", "is_photo_valid"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_4"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_4"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_3", "is_photo_valid"], "lua_script": "function evaluate() if (_if_control_attr_3 == 0 and (is_photo_valid == 0)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "photo_user_match::build_protobuf_6D6C13": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3", "long_value_type", "update_time_name"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["update_time_result_info"], "$output_item_attrs": []}, "class_name": "com.kuaishou.ad.creative.engine.proto.ResultInfo", "inputs": [{"common_attr": "update_time_name", "path": "field_name"}, {"common_attr": "long_value_type", "path": "value_type"}], "output_common_attr": "update_time_result_info", "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "photo_user_match::build_protobuf_7D4935": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3", "embedding_result_info", "item_emb_table_name", "item_id_result_info", "item_search_prefix", "item_search_truncate_num", "update_time_result_info"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_search_request"], "$output_item_attrs": []}, "class_name": "com.kuaishou.ad.creative.engine.proto.ItemSearchRequest", "inputs": [{"common_attr": "item_emb_table_name", "path": "table_name"}, {"common_attr": "item_search_prefix", "path": "row_prefix"}, {"append": true, "common_attr": "item_id_result_info", "path": "result_infos"}, {"append": true, "common_attr": "embedding_result_info", "path": "result_infos"}, {"append": true, "common_attr": "update_time_result_info", "path": "result_infos"}, {"common_attr": "item_search_truncate_num", "path": "truncate_num"}], "output_common_attr": "item_search_request", "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "photo_user_match::build_protobuf_821929": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3", "long_value_type", "photo_id_name"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_id_result_info"], "$output_item_attrs": []}, "class_name": "com.kuaishou.ad.creative.engine.proto.ResultInfo", "inputs": [{"common_attr": "photo_id_name", "path": "field_name"}, {"common_attr": "long_value_type", "path": "value_type"}], "output_common_attr": "photo_id_result_info", "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "photo_user_match::build_protobuf_C8FFF9": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3", "embedding_result_info", "photo_emb_table_name", "photo_id_result_info", "photo_search_prefix", "photo_search_truncate_num"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_search_request"], "$output_item_attrs": []}, "class_name": "com.kuaishou.ad.creative.engine.proto.ItemSearchRequest", "inputs": [{"common_attr": "photo_emb_table_name", "path": "table_name"}, {"common_attr": "photo_search_prefix", "path": "row_prefix"}, {"append": true, "common_attr": "embedding_result_info", "path": "result_infos"}, {"append": true, "common_attr": "photo_id_result_info", "path": "result_infos"}, {"common_attr": "photo_search_truncate_num", "path": "truncate_num"}], "output_common_attr": "photo_search_request", "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "photo_user_match::build_protobuf_CFC644": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3", "item_id_name", "long_value_type"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_id_result_info"], "$output_item_attrs": []}, "class_name": "com.kuaishou.ad.creative.engine.proto.ResultInfo", "inputs": [{"common_attr": "item_id_name", "path": "field_name"}, {"common_attr": "long_value_type", "path": "value_type"}], "output_common_attr": "item_id_result_info", "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "photo_user_match::build_protobuf_DFB97F": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3", "embedding_name", "str_value_type"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_result_info"], "$output_item_attrs": []}, "class_name": "com.kuaishou.ad.creative.engine.proto.ResultInfo", "inputs": [{"common_attr": "embedding_name", "path": "field_name"}, {"common_attr": "str_value_type", "path": "value_type"}], "output_common_attr": "embedding_result_info", "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "photo_user_match::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3", "photo_user_matchTimeCostEnd", "photo_user_matchTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_user_matchTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["photo_user_matchTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["photo_user_matchTimeCostEnd", "photo_user_matchTimeCostStart"], "lua_script": "function gen_common_attrs() return (photo_user_matchTimeCostEnd - photo_user_matchTimeCostStart) end", "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoLuaAttrEnricher"}, "photo_user_match::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_user_matchTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "photo_user_matchTimeCostEnd", "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "photo_user_match::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_user_matchTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "photo_user_matchTimeCostStart", "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "photo_user_match::count_reco_result_E7EB16": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_size"], "$output_item_attrs": []}, "save_result_size_to_common_attr": "item_size", "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoCountRecoResultEnricher"}, "photo_user_match::enrich_attr_by_lua_7C069B": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3", "admit_ratio", "method", "photo_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["is_photo_valid"], "$output_item_attrs": []}, "export_common_attr": ["is_photo_valid"], "function_for_common": "checkPhotoId", "import_common_attr": ["method", "photo_id", "admit_ratio"], "lua_script": "function checkPhotoId()\n          if photo_id % 100 < admit_ratio then\n            return 1\n          else\n            return 0\n          end\n        end", "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoLuaAttrEnricher"}, "photo_user_match::enrich_attr_by_lua_FC6681": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3", "photo_id", "user_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_search_prefix", "photo_search_prefix"], "$output_item_attrs": []}, "export_common_attr": ["item_search_prefix", "photo_search_prefix"], "function_for_common": "toString", "import_common_attr": ["user_id", "photo_id"], "lua_script": "function toString()\n          return tostring(user_id),tostring(user_id)..\"_\"..tostring(photo_id)\n        end", "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoLuaAttrEnricher"}, "photo_user_match::enrich_by_generic_grpc_95C67A": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3", "photo_search_request"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_search_response"], "$output_item_attrs": []}, "downstream_processor": "photo_user_match::photo_item_inner_product_retrieve_320642", "kess_service": "ad-creative-item-search-service", "method_name": "/com.kuaishou.ad.creative.engine.proto.AdCreativeItemSearchService/ItemSearch", "request_attr": "photo_search_request", "response_attr": "photo_search_response", "response_class": "com.kuaishou.ad.creative.engine.proto.ItemSearchResponse", "skip": "{{_if_control_attr_3}}", "timeout_ms": 10000, "type_name": "CommonRecoGenericGrpcEnricher"}, "photo_user_match::enrich_by_generic_grpc_F5AD79": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3", "item_search_request"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_search_response"], "$output_item_attrs": []}, "downstream_processor": "photo_user_match::photo_item_inner_product_retrieve_320642", "kess_service": "ad-creative-item-search-service", "method_name": "/com.kuaishou.ad.creative.engine.proto.AdCreativeItemSearchService/ItemSearch", "request_attr": "item_search_request", "response_attr": "item_search_response", "response_class": "com.kuaishou.ad.creative.engine.proto.ItemSearchResponse", "skip": "{{_if_control_attr_3}}", "timeout_ms": 10000, "type_name": "CommonRecoGenericGrpcEnricher"}, "photo_user_match::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3", "photo_user_matchTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:photo_user_match", "common_attrs": ["photo_user_matchTimeCost"], "perf_base": 1, "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "photo_user_match::perflog_BC4B3AA2": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "photo_user_match_tail_exit", "mode": "count", "namespace": "ad.ad_photo_item_match_server", "skip": "{{_if_control_attr_4}}", "subtag": "admit", "type_name": "CommonRecoPerflogObserver"}, "photo_user_match::photo_item_inner_product_retrieve_320642": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3", "item_search_response", "photo_search_response"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "embedding_column": "serialized_embedding", "item_response_column": "item_search_response", "photo_response_column": "photo_search_response", "skip": "{{_if_control_attr_3}}", "type_name": "PhotoItemInnerProductRetriever"}, "photo_user_match::return__04FED1": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_4}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "photo_user_match::sort_by_score_B1CB5B": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3"], "$input_item_attrs": [], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoScoreSortArranger"}, "prepare::_branch_controller_B58E63CC": {"$branch_start": "prepare::_branch_controller_B58E63CC", "$code_info": "[if] B58E63CC ad_photo_item_match_server.py:88 in prepare(): ).if_(\"method_int == 0\")", "$metadata": {"$input_common_attrs": ["method_int"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_1"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_1"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["method_int"], "lua_script": "function evaluate() if (method_int == 0) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "prepare::calc_time_cost": {"$metadata": {"$input_common_attrs": ["prepareTimeCostEnd", "prepareTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["prepareTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["prepareTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["prepareTimeCostEnd", "prepareTimeCostStart"], "lua_script": "function gen_common_attrs() return (prepareTimeCostEnd - prepareTimeCostStart) end", "type_name": "CommonRecoLuaAttrEnricher"}, "prepare::calc_time_cost_e": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["prepareTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "prepareTimeCostEnd", "type_name": "CommonRecoUserMetaInfoEnricher"}, "prepare::calc_time_cost_s": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["prepareTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "prepareTimeCostStart", "type_name": "CommonRecoUserMetaInfoEnricher"}, "prepare::enrich_attr_by_lua_659A4B": {"$metadata": {"$input_common_attrs": ["method"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["method_int"], "$output_item_attrs": []}, "export_common_attr": ["method_int"], "function_for_common": "methodToInt", "import_common_attr": ["method"], "lua_script": "function methodToInt()\n            local a = \"TopItemSearchByPhotoId\"\n            local b = \"ScoreCalcForPhotoItem\"\n            if method == a then\n              return 1\n            elseif method == b then\n              return 2\n            else\n              return 0\n            end\n          end", "type_name": "CommonRecoLuaAttrEnricher"}, "prepare::get_kconf_params_7CFF3B": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["admit_ratio", "item_search_truncate_num", "photo_search_truncate_num", "time_out"], "$output_item_attrs": []}, "kconf_configs": [{"export_common_attr": "item_search_truncate_num", "json_path": "item_search_truncate_num", "kconf_key": "ad.adCreativeEngine.photoItemMatchConfig", "value_type": "json"}, {"export_common_attr": "photo_search_truncate_num", "json_path": "photo_search_truncate_num", "kconf_key": "ad.adCreativeEngine.photoItemMatchConfig", "value_type": "json"}, {"export_common_attr": "time_out", "json_path": "time_out", "kconf_key": "ad.adCreativeEngine.photoItemMatchConfig", "value_type": "json"}, {"export_common_attr": "admit_ratio", "json_path": "admit_ratio", "kconf_key": "ad.adCreativeEngine.photoItemMatchConfig", "value_type": "json"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "prepare::perf_time_cost": {"$metadata": {"$input_common_attrs": ["prepareTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:prepare", "common_attrs": ["prepareTimeCost"], "perf_base": 1, "type_name": "CommonRecoAttrValuePerflogObserver"}, "prepare::perflog_5C87221E": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "method_empty", "mode": "count", "namespace": "ad.ad_photo_item_match_server", "skip": "{{_if_control_attr_1}}", "subtag": "admit", "type_name": "CommonRecoPerflogObserver"}, "prepare::return__28C561": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_1}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "prepare::set_default_value_C8B6C6": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_name", "item_emb_table_name", "item_id_name", "long_value_type", "photo_emb_table_name", "photo_id_name", "str_value_type", "update_time_name"], "$output_item_attrs": []}, "common_attrs": [{"name": "item_emb_table_name", "type": "string", "value": "ad_creative_engine:item_goods_embedding_table"}, {"name": "photo_emb_table_name", "type": "string", "value": "ad_creative_engine:photo_goods_embedding_table"}, {"name": "update_time_name", "type": "string", "value": "update_time"}, {"name": "item_id_name", "type": "string", "value": "item_id"}, {"name": "photo_id_name", "type": "string", "value": "photo_id"}, {"name": "embedding_name", "type": "string", "value": "serialized_embedding"}, {"name": "long_value_type", "type": "int", "value": 2}, {"name": "str_value_type", "type": "int", "value": 3}], "type_name": "CommonRecoItemAttrDefaultValueEnricher"}}, "type_name": "CommonRecoPipeline"}, "pipeline_map": {"photo_item_match_flow": {"__PARENT": "base_pipeline", "pipeline": ["prepare::calc_time_cost_s", "prepare::get_kconf_params_7CFF3B", "prepare::set_default_value_C8B6C6", "prepare::enrich_attr_by_lua_659A4B", "prepare::_branch_controller_B58E63CC", "prepare::perflog_5C87221E", "prepare::return__28C561", "prepare::calc_time_cost_e", "prepare::calc_time_cost", "prepare::perf_time_cost", "get_user_id::calc_time_cost_s", "get_user_id::_branch_controller_C9263299", "get_user_id::build_protobuf_74448D", "get_user_id::enrich_by_generic_grpc_51F939", "get_user_id::enrich_with_protobuf_538346", "get_user_id::build_table_from_common_list_attr_1C4B2C", "get_user_id::copy_attr_E71EA4", "get_user_id::calc_time_cost_e", "get_user_id::calc_time_cost", "get_user_id::perf_time_cost", "_branch_controller_70801CA1", "photo_user_match::calc_time_cost_s", "photo_user_match::enrich_attr_by_lua_7C069B", "photo_user_match::_branch_controller_E363CFBF", "photo_user_match::perflog_BC4B3AA2", "photo_user_match::return__04FED1", "photo_user_match::enrich_attr_by_lua_FC6681", "photo_user_match::build_protobuf_CFC644", "photo_user_match::build_protobuf_DFB97F", "photo_user_match::build_protobuf_6D6C13", "photo_user_match::build_protobuf_7D4935", "photo_user_match::enrich_by_generic_grpc_F5AD79", "photo_user_match::build_protobuf_821929", "photo_user_match::build_protobuf_C8FFF9", "photo_user_match::enrich_by_generic_grpc_95C67A", "photo_user_match::photo_item_inner_product_retrieve_320642", "photo_user_match::sort_by_score_B1CB5B", "photo_user_match::count_reco_result_E7EB16", "photo_user_match::calc_time_cost_e", "photo_user_match::calc_time_cost", "photo_user_match::perf_time_cost", "_branch_controller_EA09D36A", "photo_item_match::calc_time_cost_s", "photo_item_match::enrich_attr_by_lua_3591DC", "photo_item_match::log_debug_info_2D6614", "photo_item_match::build_protobuf_794B25", "photo_item_match::build_protobuf_C1821A", "photo_item_match::build_protobuf_F2D9B9", "photo_item_match::build_protobuf_84A2EC", "photo_item_match::enrich_by_generic_grpc_170B4E", "photo_item_match::build_protobuf_403613", "photo_item_match::build_protobuf_7FF281", "photo_item_match::enrich_by_generic_grpc_ABA13E", "photo_item_match::photo_item_inner_product_retrieve_B99069", "photo_item_match::sort_by_score_18CD92", "photo_item_match::log_debug_info_898F72", "photo_item_match::calc_time_cost_e", "photo_item_match::calc_time_cost", "photo_item_match::perf_time_cost"]}}}, "request_type_config": {"default": ["photo_item_match_flow"]}}