#!/bin/bash

cd `dirname $0` || exit
absolute_path=`readlink -f .`
service_dir=`dirname $absolute_path`
program=`basename ${service_dir}`

function log() {
  echo "[$(date +%F\ %T)] $@"
}

SUPERVISOR_CONF_DIR="/data/web_server/supervisord/conf"
if [ $(id -u) -eq 0 ]; then
  SUPERVISOR_CONF_DIR="/home/<USER>/supervisord/conf"
fi
function check_supervisor_running() {
  pgrep supervisord >/dev/null
  if [ $? -ne 0 ]; then
    log "no supervisord service running, try start"
    # 未启动 supervisord, 使用 service 方式启动
    rm -f /etc/supervisord.conf
    ln -s ${SUPERVISOR_CONF_DIR}/supervisord.conf /etc/
    # 里面有一个相对路径， 所以这个地方需要写绝对路径
    supervisord -c ${SUPERVISOR_CONF_DIR}/supervisord.conf  >/dev/null
  fi
  pgrep supervisord >/dev/null
  if [ $? -ne 0 ]; then
    log "no supervisord service running, start failed"
    exit -1
  fi
}

function update_ad_qa() {
  local filter_str="xxxxxxxxxxxxxxx"
  if [ $(id -u) -eq 0 ]; then
    # 去掉用户
    filter_str="user="
  fi
  if [ -f ${supervisor_conf_dir}/ad_qa.conf ]; then
    return
  fi
  local supervisor_template_conf="../config/supervise_ad_qa.conf"
  if [ -f "ad_qa.py" -a -f "${supervisor_template_conf}" ]; then
    local supervisor_conf_dir="${SUPERVISOR_CONF_DIR}/conf.d"
    if [ ! -d "${supervisor_conf_dir}" ]; then
      return
    fi
    if [ ! -f "${supervisor_template_conf}" ]; then
      return
    fi
    cat ${supervisor_template_conf} | \
      grep -vi "${filter_str}" | \
      sed 's#{directory}#'${service_dir}'#g' \
    > ${supervisor_conf_dir}/ad_qa.conf
  fi
}

function update_supervisor_conf() {
  local filter_str="xxxxxxxxxxxxxxx"
  if [ $(id -u) -eq 0 ]; then
    check_supervisor_running
    # 去掉用户
    filter_str="user="
  fi
  local supervisor_conf_dir="${SUPERVISOR_CONF_DIR}/conf.d"
  if [ ! -d "${supervisor_conf_dir}" ]; then
    log "not exists dir ${supervisor_conf_dir}, abort"
    exit -1
  fi
  local supervisor_template_conf="../config/supervise_runner_template.conf"
  if [ ! -f "${supervisor_template_conf}" ]; then
    log "not exists ${supervisor_template_conf}, abort"
    exit -1
  fi
  cat ${supervisor_template_conf} | \
    grep -vi "${filter_str}" | \
    sed 's#{program}#'${program}'#g' | \
    sed 's#{directory}#'${service_dir}'#g' \
  > ${supervisor_conf_dir}/${program}.conf.new
  if [ -f ${supervisor_conf_dir}/${program}.conf ]; then
    if [ $(diff ${supervisor_conf_dir}/${program}.conf{,.new}|wc -l) -eq 0 ]; then
      rm -f ${supervisor_conf_dir}/${program}.conf.new
    fi
  fi
  if [ -f ${supervisor_conf_dir}/${program}.conf.new ]; then
     log "generate supervisor conf file to ${supervisor_conf_dir}/${program}.conf"
     mv ${supervisor_conf_dir}/${program}.conf{.new,}
     cat ${supervisor_conf_dir}/${program}.conf
     already_execute_update=1
     update_ad_qa
     supervisorctl update
  fi
}

function quick_kill_service() {
  if [ $(supervisorctl status ${program} | grep ${program} |grep "pid" | wc -l) -gt 0 ]; then
    supervisorctl signal 9 ${program}
  fi
}

###### Main #######
already_execute_update=0
case $1 in
start|load)
  update_supervisor_conf
  if [ ${already_execute_update} -eq 0 ]; then
    supervisorctl start ${program}
  fi
  ;;
reload|restart)
  update_supervisor_conf
  if [ ${already_execute_update} -eq 0 ]; then
    supervisorctl restart ${program}
  fi
  ;;
stop)
  supervisorctl stop ${program}
  ;;
kill) 
  quick_kill_service
  ;;
update_ad_qa)
  update_ad_qa
  log "call supervisorctl start ad_qa"
  ;;
*)
  log "usage: $0 start|stop|restart"
  ;;
esac
