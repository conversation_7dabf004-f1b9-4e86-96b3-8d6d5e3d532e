#!/usr/bin/env python3
# coding=utf-8

import os
import sys
dragon_path=os.path.abspath('../../../../../../dragon/')
ad_dragon_path=os.path.abspath('../../../../ad_dragon/')
sys.path.append(dragon_path)
sys.path.append(ad_dragon_path)

from dragonfly.common_leaf_dsl import LeafFlow, OfflineRunner, LeafService, IndexSource, current_flow
from ad_dragonfly.mechanism.ad_bid_service.ad_bid_service_mixin import AdBidServiceApiMixin
from ks_leaf_bootstrap.utils import gen_service_graph_def
from ks_leaf_functional.core.module import module
from dragon_server.creative.constants import *
from dragon_server.creative.common import AdCreativeCommonApiMixin
from ks_leaf_functional.core.data_manager import data_manager, ab_param as ab, kconf_param as kconf

# Flow definition
class AdPhotoItemMatchService(LeafFlow, AdCreativeCommonApiMixin):
  def __init__(self, name, item_table):
    LeafFlow.__init__(self, name=name, item_table=item_table)

  @module()
  def prepare(self):
    """
      -功能:
      -执行流程:
        get_kconf_params: 获取截断数、超时时间、准入比例等配置
        enrich_attr_by_lua: 根据photoId尾号和准入比例判断是否准入 如果不准入 丢弃
        set_attr_value: 一些固定值，如字段名、表名等的设置
        enrich_attr_by_lua: 将请求带来的method字段转为int 用于后续的判断

    """
    with data_manager:
      current_flow() \
      .get_kconf_params(   # 取kconf配置数据
        kconf_configs=[
          dict(kconf_key="ad.adCreativeEngine.photoItemMatchConfig",
            value_type="json",
            json_path="item_search_truncate_num",
            export_common_attr="item_search_truncate_num"	# item查询召回截断数
          ),
          dict(kconf_key="ad.adCreativeEngine.photoItemMatchConfig",
            value_type="json",
            json_path="photo_search_truncate_num",
            export_common_attr="photo_search_truncate_num"  # photo查询截断数
          ),
          dict(kconf_key="ad.adCreativeEngine.photoItemMatchConfig",
            value_type="json",
            json_path="time_out",
            export_common_attr="time_out"   # 超时时间设置
          ),
          dict(kconf_key="ad.adCreativeEngine.photoItemMatchConfig",
            value_type="json",
            json_path="admit_ratio",
            export_common_attr="admit_ratio"  # 准入比例 0-100 id % 100 在 [0, admit_ratio] 之间
          ),
        ]
      ).set_attr_value(
			  common_attrs=[
          {"name": "item_emb_table_name", "type": "string", "value": "ad_creative_engine:item_goods_embedding_table"},
          {"name": "photo_emb_table_name", "type": "string", "value": "ad_creative_engine:photo_goods_embedding_table"},
          {"name": "update_time_name", "type": "string", "value": "update_time"},
          {"name": "item_id_name", "type": "string", "value": "item_id"},
          {"name": "photo_id_name", "type": "string", "value": "photo_id"},
          {"name": "embedding_name", "type": "string", "value": "serialized_embedding"},
          {"name": "long_value_type", "type": "int", "value": 2},
          {"name": "str_value_type",  "type": "int", "value": 3}
			  ]
		  ).enrich_attr_by_lua(  # method string转为 int
        import_common_attr = ["method"],
        function_for_common = "methodToInt",
        export_common_attr = ["method_int"],
        lua_script = """
          function methodToInt()
            local a = "TopItemSearchByPhotoId"
            local b = "ScoreCalcForPhotoItem"
            if method == a then
              return 1
            elseif method == b then
              return 2
            else
              return 0
            end
          end
        """
      ).if_("method_int == 0") \
        .perflog(
          mode = "count",
          namespace = "ad.ad_photo_item_match_server",
          subtag = "admit",
          extra1 = "method_empty"
        ).return_() \
      .end_if_()
    return self

  @module()
  def photo_user_match(self):
    self.enrich_attr_by_lua(  # 按照 photo尾号丢弃消息
      import_common_attr = ["method", "photo_id", "admit_ratio"],
      function_for_common = "checkPhotoId",
      export_common_attr = ["is_photo_valid"],
      lua_script = """
        function checkPhotoId()
          if photo_id % 100 < admit_ratio then
            return 1
          else
            return 0
          end
        end
      """
    ).if_("is_photo_valid == 0") \
      .perflog(
        mode = "count",
        namespace = "ad.ad_photo_item_match_server",
        subtag = "admit",
        extra1 = "photo_user_match_tail_exit"
    ).return_().end_if_() \
     .enrich_attr_by_lua(  # user_id转string photo_id key拼接
      import_common_attr = ["user_id", "photo_id"],
      function_for_common = "toString",
      export_common_attr = ["item_search_prefix", "photo_search_prefix"],
      lua_script = """
        function toString()
          return tostring(user_id),tostring(user_id).."_"..tostring(photo_id)
        end
      """
    ).build_protobuf(   # 构造 result_info item_id信息
      class_name="com::kuaishou::ad::creative::engine::proto::ResultInfo",
      inputs=[
				{ "common_attr": "item_id_name", "path": "field_name" },
        { "common_attr": "long_value_type", "path": "value_type" }
      ],
      output_common_attr="item_id_result_info",
    ).build_protobuf(   # 构造 result_info embedding信息
      class_name="com::kuaishou::ad::creative::engine::proto::ResultInfo",
      inputs=[
				{ "common_attr": "embedding_name", "path": "field_name" },
        { "common_attr": "str_value_type", "path": "value_type" }
      ],
      output_common_attr="embedding_result_info",
    ).build_protobuf(   # 构造 result_info updateTime信息
      class_name="com::kuaishou::ad::creative::engine::proto::ResultInfo",
      inputs=[
				{ "common_attr": "update_time_name", "path": "field_name" },
        { "common_attr": "long_value_type", "path": "value_type" }
      ],
      output_common_attr="update_time_result_info",
    ).build_protobuf(   # 构造 ItemSearchRequest user查item emb
      class_name="com::kuaishou::ad::creative::engine::proto::ItemSearchRequest",
      inputs=[
				{ "common_attr": "item_emb_table_name", "path": "table_name" },
        { "common_attr": "item_search_prefix", "path": "row_prefix" },
        { "common_attr": "item_id_result_info", "path": "result_infos" , "append": True },
        { "common_attr": "embedding_result_info", "path": "result_infos" , "append": True },
        { "common_attr": "update_time_result_info", "path": "result_infos" , "append": True },
        { "common_attr": "item_search_truncate_num", "path": "truncate_num" },
      ],
      output_common_attr="item_search_request",
    ).enrich_by_generic_grpc(		# 请求item查询服务 获取embedding信息
      no_check=True,
      kess_service="ad-creative-item-search-service",
      timeout_ms= 10000,
      method_name="/com.kuaishou.ad.creative.engine.proto.AdCreativeItemSearchService/ItemSearch",
      request_attr= "item_search_request",
      response_attr= "item_search_response",
      response_class="com::kuaishou::ad::creative::engine::proto::ItemSearchResponse"
    ).build_protobuf(   # 构造 result_info photoId 信息
      class_name="com::kuaishou::ad::creative::engine::proto::ResultInfo",
      inputs=[
				{ "common_attr": "photo_id_name", "path": "field_name" },
        { "common_attr": "long_value_type", "path": "value_type" }
      ],
      output_common_attr="photo_id_result_info",
    ).build_protobuf(   # 构造 ItemSearchRequest photo查emb
      class_name="com::kuaishou::ad::creative::engine::proto::ItemSearchRequest",
      inputs=[
				{ "common_attr": "photo_emb_table_name", "path": "table_name" },
        { "common_attr": "photo_search_prefix", "path": "row_prefix" },
        { "common_attr": "embedding_result_info", "path": "result_infos" , "append": True },
        { "common_attr": "photo_id_result_info", "path": "result_infos" , "append": True },
        { "common_attr": "photo_search_truncate_num", "path": "truncate_num" },
      ],
      output_common_attr="photo_search_request",
    ).enrich_by_generic_grpc(		# 请求item查询服务 获取embedding信息
      kess_service="ad-creative-item-search-service",
      timeout_ms= 10000,
      method_name="/com.kuaishou.ad.creative.engine.proto.AdCreativeItemSearchService/ItemSearch",
      request_attr= "photo_search_request",
      response_attr= "photo_search_response",
      response_class="com::kuaishou::ad::creative::engine::proto::ItemSearchResponse"
    ).photo_item_inner_product_retrieve(
      photo_response_column = 'photo_search_response',
      item_response_column = 'item_search_response',
      embedding_column = 'serialized_embedding',
     ).sort(
     ).count_reco_result(
			  save_count_to="item_size"
     )
    return self
  
  @module()
  def get_user_id(self):  # 当 photoId 存在 无userId时
    self.if_("user_id == 0 and photo_id ~= 0") \
      .build_protobuf(  # 构造 kuaishou::negative::GetByPhotoIdsRequest
        class_name="kuaishou::negative::GetByPhotoIdsRequest",
        inputs=[
          { "common_attr": "photo_id", "path": "photo_id", "append": True },
        ],
        output_common_attr= "photo_info_request",
      ).enrich_by_generic_grpc(		# 请求 photoService 服务
        kess_service="grpc_apiCorePhotoService",
        timeout_ms= 1000,
        method_name="/kuaishou.negative.PhotoServiceRpc/GetByIdsContainsDeleted",
        request_attr= "photo_info_request",
        response_attr= "photo_info_response",
        response_class="kuaishou::negative::PhotoMapResponse"
      ).enrich_with_protobuf(
        from_extra_var = "photo_info_response",
        attrs = [
          dict(name="photo_ids", path="photos.key"),
          dict(name="user_ids", path="photos.value.userId")
        ]
      ).build_table_from_common_list_attr(  # 构造一张表
        new_table = "photo_info_table",
        build_config = [  # 第一行默认为主键
          dict(src = "photo_ids", dest = "photo_id"),
          dict(src = "user_ids", dest = "user_id"),
        ]
      ).copy_attr(    # 将 photo_trigger_info copy 到 item_attr 补数据
        item_table = "photo_info_table",
        no_check = True,
        attrs=[{
          "from_item": "user_id",
          "to_common": "user_id"
        }]
      ).end_if_()
    return self
    

  @module()
  def photo_item_match(self):
    self.enrich_attr_by_lua(  # 拼接 key
      import_common_attr = ["item_id", "photo_id", "user_id"],
      function_for_common = "toString",
      export_common_attr = ["item_search_prefix", "photo_search_prefix"],
      lua_script = """
        function toString()
          return tostring(user_id).."_"..tostring(item_id),tostring(user_id).."_"..tostring(photo_id)
        end
      """
    ).debug_log(
      common_attrs = ["item_id", "photo_id", "user_id"]
    ).build_protobuf(   # 构造 result_info item_id信息
      class_name="com::kuaishou::ad::creative::engine::proto::ResultInfo",
      inputs=[
				{ "common_attr": "item_id_name", "path": "field_name" },
        { "common_attr": "long_value_type", "path": "value_type" }
      ],
      output_common_attr="item_id_result_info",
    ).build_protobuf(   # 构造 result_info embedding信息
      class_name="com::kuaishou::ad::creative::engine::proto::ResultInfo",
      inputs=[
				{ "common_attr": "embedding_name", "path": "field_name" },
        { "common_attr": "str_value_type", "path": "value_type" }
      ],
      output_common_attr="embedding_result_info",
    ).build_protobuf(   # 构造 result_info updateTime信息
      class_name="com::kuaishou::ad::creative::engine::proto::ResultInfo",
      inputs=[
				{ "common_attr": "update_time_name", "path": "field_name" },
        { "common_attr": "long_value_type", "path": "value_type" }
      ],
      output_common_attr="update_time_result_info",
    ).build_protobuf(   # 构造 ItemSearchRequest user查item emb
      class_name="com::kuaishou::ad::creative::engine::proto::ItemSearchRequest",
      inputs=[
				{ "common_attr": "item_emb_table_name", "path": "table_name" },
        { "common_attr": "item_search_prefix", "path": "row_prefix" },
        { "common_attr": "item_id_result_info", "path": "result_infos" , "append": True },
        { "common_attr": "embedding_result_info", "path": "result_infos" , "append": True },
        { "common_attr": "update_time_result_info", "path": "result_infos" , "append": True },
        { "common_attr": "item_search_truncate_num", "path": "truncate_num" },
      ],
      output_common_attr="item_search_request",
    ).enrich_by_generic_grpc(		# 请求item查询服务 获取embedding信息
      no_check=True,
      kess_service="ad-creative-item-search-service",
      timeout_ms= 10000,
      method_name="/com.kuaishou.ad.creative.engine.proto.AdCreativeItemSearchService/ItemSearch",
      request_attr= "item_search_request",
      response_attr= "item_search_response",
      response_class="com::kuaishou::ad::creative::engine::proto::ItemSearchResponse"
    ).build_protobuf(   # 构造 result_info photoId 信息
      class_name="com::kuaishou::ad::creative::engine::proto::ResultInfo",
      inputs=[
				{ "common_attr": "photo_id_name", "path": "field_name" },
        { "common_attr": "long_value_type", "path": "value_type" }
      ],
      output_common_attr="photo_id_result_info",
    ).build_protobuf(   # 构造 ItemSearchRequest photo查emb
      class_name="com::kuaishou::ad::creative::engine::proto::ItemSearchRequest",
      inputs=[
				{ "common_attr": "photo_emb_table_name", "path": "table_name" },
        { "common_attr": "photo_search_prefix", "path": "row_prefix" },
        { "common_attr": "embedding_result_info", "path": "result_infos" , "append": True },
        { "common_attr": "photo_id_result_info", "path": "result_infos" , "append": True },
        { "common_attr": "photo_search_truncate_num", "path": "truncate_num" },
      ],
      output_common_attr="photo_search_request",
    ).enrich_by_generic_grpc(		# 请求item查询服务 获取embedding信息
      kess_service="ad-creative-item-search-service",
      timeout_ms= 10000,
      method_name="/com.kuaishou.ad.creative.engine.proto.AdCreativeItemSearchService/ItemSearch",
      request_attr= "photo_search_request",
      response_attr= "photo_search_response",
      response_class="com::kuaishou::ad::creative::engine::proto::ItemSearchResponse"
    ).photo_item_inner_product_retrieve(
      photo_response_column = 'photo_search_response',
      item_response_column = 'item_search_response',
      embedding_column = 'serialized_embedding',
     ).sort(
     ).debug_log(
      common_attrs = ["item_search_response", "photo_search_response", "user_id", "item_id", "photo_id"]
    )
    return self
#===================================================== Flow Define Start =====================================================
photo_item_match_flow = AdPhotoItemMatchService(name="photo_item_match_flow", item_table="")
with photo_item_match_flow, data_manager:
  photo_item_match_flow.prepare() \
    .get_user_id() \
    .if_("method_int == 1") \
       .photo_user_match() \
     .end_if_() \
     .if_("method_int == 2") \
       .photo_item_match() \
     .end_if_()

service = LeafService(kess_name="ad-photo-item-match-service")
service.CHECK_UNUSED_ATTR = False
service.common_attrs_from_request = ["method", "user_id", "photo_id", "item_id"]
service.return_item_attrs([])
service.add_leaf_flows(leaf_flows=[photo_item_match_flow], request_type="default")
service.draw()
gen_service_graph_def(service, mod = "remote")
ad_config = {
  "grpc" : {
    "test" : False,
    "server" : {
      "kess_name" : "USE_KSN_AS_SERVICE",
      "port" : 20182,
      "grpc_cq_num": 8,
      "kcs_grpc_port_key" : "AUTO_PORT1",
      "thread_num": 100,  ##  default cpu num
      "quit_wait_seconds" : 85,
      "start_warmup_seconds" : 10, #默认 10s
    },
    "client_map" : {
    }
  }
}
current_folder = os.path.dirname(os.path.abspath(__file__))
service.build(output_file=os.path.join(current_folder, "pub/ad_photo_item_match_server/config/dynamic_json_config.json"),
                extra_fields=ad_config)
