{"_CONFIG_VERSION": "0fe30b04da3d5ede3d26ad41bc1df242_local", "_DRAGONFLY_CREATE_TIME": "2025-02-12 20:03:32", "_DRAGONFLY_VERSION": "0.8.3", "attrs_from_request": {"common_attr": ["data_type", "enable_use_cache", "photo_create_time", "photo_id", "valid_duration"]}, "default_request_type": "default", "grpc": {"client_map": {"AdDpaProductServer": "grpc_adDpaProductServerImpl", "AdMaterialBaseInfoService": "grpc_adMaterialBaseInfoService", "CmdLineExecutorService": "grpc_editorSdkServiceForKaiYanCopy", "CreativeInformationGetterService": "grpc_adCreativeInformationGetterService", "CreativeProcessService": "ad-alliance-creative-process-uni-service", "DpOneServiceMmuHigh": "grpc_dpOneServiceMmuHigh", "DpaProductSearchDealer": "grpc_ad_product_search", "EcomItemInfoService": "grpc_adEcomProductItemInfoRpcService", "KwaiShopProductService": "kwaishop-product-detail-service", "KwaiShopProductServiceTest": "grpc_kwaishop-product-detail-service", "LiveCurrentLivingService": "grpc_liveExposeCurrentLivingRpcService", "LiveReservationReadServiceRpc": "grpc_liveReservationReadServiceRpc", "LiveStremInfoService": "grpc_adLiveInfoGetRpcService", "MediaProcessingJobServiceKey": "grpc_mediaProcessingJobService", "MmuPhotoMd5QueryServiceKey": "grpc_mmuPhotoMd5QueryService", "OcrDetectService": "grpc_ztOcrDetectService", "PhotoServiceKey": "grpc_apiCorePhotoService", "SellerTagInfoService": "grpc_kwaishopProductListAggregationService", "UniverseGenOptCtrInfService": "grpc_UniverseGenOptCTRInfRpcService", "UserTopPhotoService": "grpc_apiCoreUserTopPhotoService", "adLiveClipSelectVersionServiceKey": "grpc_adLiveClipOptSelectVersionService", "adTagRetrieveServerOfflineKey": "grpc_adTagRetrieveServer_offline", "forward_index_client": "ad-forward-index", "mmuAdEmbeddingServiceKey": "grpc_mmuAdEmbeddingService", "mmuDlFeatureCalculateServiceKey": "grpc_mmuDlFeatureCalculateService", "wideTableProductServiceKey": "wide-table-product-service"}, "server": {"kcs_grpc_port_key": "AUTO_PORT1", "kess_name": "USE_KSN_AS_SERVICE", "port": 20012, "quit_wait_seconds": 120, "thread_num": 400}, "test": false}, "kess_config": {"service_name": "ad-dup-info-query-service"}, "pipeline_manager_config": {"base_pipeline": {"processor": {"_branch_controller_67F8EDDC": {"$branch_start": "_branch_controller_67F8EDDC", "$code_info": "[if] 67F8EDDC ad_dup_info_query_server.py in query_photo_frame_get(): ).if_(\"frame_status == 0\")", "$metadata": {"$input_common_attrs": ["frame_status"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_20"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_20"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["frame_status"], "lua_script": "function evaluate() if (frame_status == 0) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_67F8EDDC_1": {"$branch_start": "_branch_controller_67F8EDDC_1", "$code_info": "[if] 67F8EDDC ad_dup_info_query_server.py in target_photo_frame_get(): ).if_(\"frame_status == 0\")", "$metadata": {"$input_common_attrs": ["frame_status"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_21"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_21"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["frame_status"], "lua_script": "function evaluate() if (frame_status == 0) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_840F3A8C": {"$branch_start": "_branch_controller_B8B1C5B0", "$code_info": "[else_if] 840F3A8C ad_dup_info_query_server.py in <module>(): .else_if_(\"data_type_int == 2\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_4", "data_type_int"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_elseif_control_attr_14"], "$output_item_attrs": []}, "export_common_attr": ["_elseif_control_attr_14"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_4", "data_type_int"], "lua_script": "function evaluate() if (_if_control_attr_4 == 1 and (data_type_int == 2)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_B8B1C5B0": {"$branch_start": "_branch_controller_B8B1C5B0", "$code_info": "[if] B8B1C5B0 ad_dup_info_query_server.py in <module>(): .if_(\"data_type_int == 1\")", "$metadata": {"$input_common_attrs": ["data_type_int"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_4"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_4"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["data_type_int"], "lua_script": "function evaluate() if (data_type_int == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_ED7FB7D3": {"$branch_start": "_branch_controller_B8B1C5B0", "$code_info": "[else_if] ED7FB7D3 ad_dup_info_query_server.py in <module>(): .else_if_(\"data_type_int == 3\")", "$metadata": {"$input_common_attrs": ["_elseif_control_attr_14", "_if_control_attr_4", "data_type_int"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_elseif_control_attr_18"], "$output_item_attrs": []}, "export_common_attr": ["_elseif_control_attr_18"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_elseif_control_attr_14", "_if_control_attr_4", "data_type_int"], "lua_script": "function evaluate() if (_if_control_attr_4 == 1 and _elseif_control_attr_14 == 1 and (data_type_int == 3)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "blobstore_download_enricher_D5D2AC": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["frame_keys"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["frame_infos"]}, "bucket_name": "ad-creative-engine", "item_table": "query_photo_info_table", "keys_column": "frame_keys", "type_name": "BlobstoreDownloadEnricher", "values_column": "frame_infos"}, "blobstore_download_enricher_E2A344": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["frame_keys"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["frame_infos"]}, "bucket_name": "ad-creative-engine", "item_table": "", "keys_column": "frame_keys", "type_name": "BlobstoreDownloadEnricher", "values_column": "frame_infos"}, "copy_attr_49B4B0": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["frame_status"], "$modify_item_tables": [], "$output_common_attrs": ["frame_status"], "$output_item_attrs": []}, "attrs": [{"from_item": "frame_status", "to_common": "frame_status"}], "item_table": "query_photo_info_table", "type_name": "CommonRecoCopyAttrEnricher"}, "copy_attr_BFB8CD": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["frame_status"], "$modify_item_tables": [], "$output_common_attrs": ["frame_status"], "$output_item_attrs": []}, "attrs": [{"from_item": "frame_status", "to_common": "frame_status"}], "item_table": "", "type_name": "CommonRecoCopyAttrEnricher"}, "filter_by_attr_BEBD4D": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["frame_height"], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": []}, "attr_name": "frame_height", "compare_to": 0, "item_table": "", "remove_if": "<=", "remove_if_attr_missing": true, "type_name": "CommonRecoAttrFilterArranger"}, "filter_by_attr_C00341": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["frame_height"], "$modify_item_tables": ["query_photo_info_table"], "$output_common_attrs": [], "$output_item_attrs": []}, "attr_name": "frame_height", "compare_to": 0, "item_table": "query_photo_info_table", "remove_if": "<=", "remove_if_attr_missing": true, "type_name": "CommonRecoAttrFilterArranger"}, "get_dup_photo_realtime_similar_list::_branch_controller_04D22478": {"$branch_start": "get_dup_photo_realtime_similar_list::_branch_controller_04D22478", "$code_info": "[if] 04D22478 ad_dup_info_query_server.py in photo_sim_score_get(): ).if_(\"target_item_after_rank == 0 or query_item_after_rank == 0\")", "$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "query_item_after_rank", "target_item_after_rank"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_19"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_19"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_elseif_control_attr_18", "query_item_after_rank", "target_item_after_rank"], "lua_script": "function evaluate() if (_elseif_control_attr_18 == 0 and (target_item_after_rank == 0 or query_item_after_rank == 0)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_photo_realtime_similar_list::arrange_by_sub_flow_80FBB9": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": ["frame_status", "photo_id"], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": ["feature", "frame_keys", "frame_size", "frame_status"]}, "expected_partition_size": 1, "flow_name": "target_photo_frame_get", "item_table": "", "merge_item_attrs": ["feature", "frame_keys", "frame_status", "frame_size"], "pass_common_attrs": [""], "pass_item_attrs": ["photo_id", "frame_status"], "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoPipeline<PERSON>nger"}, "get_dup_photo_realtime_similar_list::build_query_photo_table::build_table_from_common_list_attr_1F595F": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "photo_id_list"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["photo_id"]}, "build_config": [{"dest": "photo_id", "src": "photo_id_list"}], "item_table": "query_photo_info_table", "new_table": "query_photo_info_table", "skip": "{{_elseif_control_attr_18}}", "type_name": "BuildNewTableFromCommonListAttrRetriever"}, "get_dup_photo_realtime_similar_list::build_query_photo_table::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "get_dup_photo_realtime_similar_list__build_query_photo_tableTimeCostEnd", "get_dup_photo_realtime_similar_list__build_query_photo_tableTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_list__build_query_photo_tableTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["get_dup_photo_realtime_similar_list__build_query_photo_tableTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["get_dup_photo_realtime_similar_list__build_query_photo_tableTimeCostEnd", "get_dup_photo_realtime_similar_list__build_query_photo_tableTimeCostStart"], "lua_script": "function gen_common_attrs() return (get_dup_photo_realtime_similar_list__build_query_photo_tableTimeCostEnd - get_dup_photo_realtime_similar_list__build_query_photo_tableTimeCostStart) end", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_photo_realtime_similar_list::build_query_photo_table::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_list__build_query_photo_tableTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_photo_realtime_similar_list__build_query_photo_tableTimeCostEnd", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_photo_realtime_similar_list::build_query_photo_table::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_list__build_query_photo_tableTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_photo_realtime_similar_list__build_query_photo_tableTimeCostStart", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_photo_realtime_similar_list::build_query_photo_table::enrich_attr_by_lua_6955A6": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "photo_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_id_list"], "$output_item_attrs": []}, "export_common_attr": ["photo_id_list"], "function_for_common": "buildPhotoIdList", "import_common_attr": ["photo_id"], "lua_script": "function buildPhotoIdList()\n          local photo_id_list = {}\n          photo_id_list[1] = photo_id\n          return photo_id_list\n        end", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_photo_realtime_similar_list::build_query_photo_table::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "get_dup_photo_realtime_similar_list__build_query_photo_tableTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:get_dup_photo_realtime_similar_list__build_query_photo_table", "common_attrs": ["get_dup_photo_realtime_similar_list__build_query_photo_tableTimeCost"], "perf_base": 1, "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "get_dup_photo_realtime_similar_list::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "get_dup_photo_realtime_similar_listTimeCostEnd", "get_dup_photo_realtime_similar_listTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_listTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["get_dup_photo_realtime_similar_listTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["get_dup_photo_realtime_similar_listTimeCostEnd", "get_dup_photo_realtime_similar_listTimeCostStart"], "lua_script": "function gen_common_attrs() return (get_dup_photo_realtime_similar_listTimeCostEnd - get_dup_photo_realtime_similar_listTimeCostStart) end", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_photo_realtime_similar_list::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_listTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_photo_realtime_similar_listTimeCostEnd", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_photo_realtime_similar_list::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_listTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_photo_realtime_similar_listTimeCostStart", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_photo_realtime_similar_list::copy_attr_21A02F": {"$metadata": {"$downstream_item_attrs": ["query_photo_info_table::feature", "query_photo_info_table::frame_keys", "query_photo_info_table::frame_size", "query_photo_info_table::frame_status"], "$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": ["feature", "frame_size"], "$modify_item_tables": [], "$output_common_attrs": ["main_feature", "main_frame_size"], "$output_item_attrs": []}, "attrs": [{"from_item": "feature", "to_common": "main_feature"}, {"from_item": "frame_size", "to_common": "main_frame_size"}], "item_table": "query_photo_info_table", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoCopyAttrEnricher"}, "get_dup_photo_realtime_similar_list::count_reco_result_318B60": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["query_item_after_rank"], "$output_item_attrs": []}, "item_table": "query_photo_info_table", "save_result_size_to_common_attr": "query_item_after_rank", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoCountRecoResultEnricher"}, "get_dup_photo_realtime_similar_list::count_reco_result_66FE6A": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["target_item_after_rank"], "$output_item_attrs": []}, "item_table": "", "save_result_size_to_common_attr": "target_item_after_rank", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoCountRecoResultEnricher"}, "get_dup_photo_realtime_similar_list::count_reco_result_98B483": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["rank_item_num"], "$output_item_attrs": []}, "item_table": "", "save_result_size_to_common_attr": "rank_item_num", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoCountRecoResultEnricher"}, "get_dup_photo_realtime_similar_list::enrich_attr_by_lua_03DDE8": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "photo_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_id_str"], "$output_item_attrs": []}, "export_common_attr": ["photo_id_str"], "function_for_common": "GetPhotoIdStr", "import_common_attr": ["photo_id"], "lua_script": "function GetPhotoIdStr()\n          return tostring(photo_id)\n        end", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_photo_realtime_similar_list::enrich_by_sub_flow_1B2E7F": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": ["photo_id"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["feature", "frame_keys", "frame_size", "frame_status"]}, "deep_copy": true, "downstream_processor": "get_dup_photo_realtime_similar_list::copy_attr_21A02F", "flow_name": "query_photo_feature_get", "item_table": "query_photo_info_table", "merge_item_attrs": ["feature", "frame_keys", "frame_status", "frame_size"], "pass_item_attrs": ["photo_id"], "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoPipelineEnricher"}, "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::build_protobuf_1B558A": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "biz", "cal_by_video_byte", "photo_id_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_req"], "$output_item_attrs": []}, "class_name": "mmu.video.DlFeatureRequest", "inputs": [{"common_attr": "photo_id_str", "path": "id"}, {"common_attr": "cal_by_video_byte", "path": "cal_by_video_byte"}, {"common_attr": "biz", "path": "source_from"}], "output_common_attr": "embedding_req", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "get_dup_photo_realtime_similar_list__get_embedding_from_rpcTimeCostEnd", "get_dup_photo_realtime_similar_list__get_embedding_from_rpcTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_list__get_embedding_from_rpcTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["get_dup_photo_realtime_similar_list__get_embedding_from_rpcTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["get_dup_photo_realtime_similar_list__get_embedding_from_rpcTimeCostEnd", "get_dup_photo_realtime_similar_list__get_embedding_from_rpcTimeCostStart"], "lua_script": "function gen_common_attrs() return (get_dup_photo_realtime_similar_list__get_embedding_from_rpcTimeCostEnd - get_dup_photo_realtime_similar_list__get_embedding_from_rpcTimeCostStart) end", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_list__get_embedding_from_rpcTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_photo_realtime_similar_list__get_embedding_from_rpcTimeCostEnd", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_list__get_embedding_from_rpcTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_photo_realtime_similar_list__get_embedding_from_rpcTimeCostStart", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::embedding_normalize_enricher_894DC7": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "avg_emb"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["avg_emb_normal"], "$output_item_attrs": []}, "is_common_attr": true, "photo_embedding_column": "avg_emb", "photo_normal_embedding_column": "avg_emb_normal", "skip": "{{_elseif_control_attr_18}}", "type_name": "EmbeddingNormalizeEnricher"}, "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::enrich_attr_by_lua_5E9F7B": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "avg_emb_normal"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["self_inner_product"], "$output_item_attrs": []}, "export_common_attr": ["self_inner_product"], "function_for_common": "innerProduct", "import_common_attr": ["avg_emb_normal"], "lua_script": "function innerProduct()\n            local val = 0.0\n            for i = 1, #avg_emb_normal do\n              val = val + avg_emb_normal[i] * avg_emb_normal[i]\n            end\n            return val\n          end", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::enrich_attr_by_lua_F10FAF": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "embedding_list"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["avg_emb", "emb_num", "is_emb_valid"], "$output_item_attrs": []}, "export_common_attr": ["is_emb_valid", "avg_emb", "emb_num"], "function_for_common": "embCheckAndAvg", "import_common_attr": ["embedding_list"], "lua_script": "function embCheckAndAvg()\n            local len = #embedding_list\n            local emb_num = len / 128\n            local avg_emb = {}\n            for i = 1, 128 do\n              avg_emb[i] = 0.0\n            end\n            if len % 128 ~= 0 or len > 1280 then\n              return 0, avg_emb, math.floor(emb_num)\n            else\n              for i = 1, len do\n                local idx  = i - 1\n                avg_emb[idx % 128 + 1] = avg_emb[idx % 128 + 1] + embedding_list[i]\n              end\n              for i = 1, #avg_emb do\n                avg_emb[i] = avg_emb[i] / emb_num\n              end\n              return 1, avg_emb, math.floor(emb_num)\n            end\n          end", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::enrich_by_generic_grpc_1644C6": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "embedding_req"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_resp"], "$output_item_attrs": []}, "downstream_processor": "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::enrich_with_protobuf_8618E7", "kess_service": "grpc_mmuDlFeatureCalculateService", "method_name": "/mmu.video.DlFeatureCalculateService/CalculateFeature", "request_attr": "embedding_req", "response_attr": "embedding_resp", "response_class": "mmu.video.DlFeatureResponse", "skip": "{{_elseif_control_attr_18}}", "timeout_ms": 10000, "type_name": "CommonRecoGenericGrpcEnricher"}, "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::enrich_with_protobuf_8618E7": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "embedding_resp"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_list"], "$output_item_attrs": []}, "attrs": [{"name": "embedding_list", "path": "feature.features.value.feature"}], "from_extra_var": "embedding_resp", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "get_dup_photo_realtime_similar_list__get_embedding_from_rpcTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:get_dup_photo_realtime_similar_list__get_embedding_from_rpc", "common_attrs": ["get_dup_photo_realtime_similar_list__get_embedding_from_rpcTimeCost"], "perf_base": 1, "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::build_protobuf_872C8C": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "avg_emb_normal"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.Rttr<PERSON>", "inputs": [{"append": true, "common_attr": "avg_emb_normal", "path": "value"}], "output_common_attr": "embedding", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::build_protobuf_A3F6FC": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "embedding_request", "user_info"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ann_request"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.AnnRequest", "inputs": [{"common_attr": "user_info", "path": "user_info"}, {"append": true, "common_attr": "embedding_request", "path": "embedding_request"}], "output_common_attr": "ann_request", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::build_protobuf_B64067": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "field_name"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["user_embedding_source"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.UserEmbeddingPicassoSourceInfo", "inputs": [{"common_attr": "field_name", "path": "field"}], "output_common_attr": "user_embedding_source", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::build_protobuf_BFD938": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "dimension", "embedding", "index_name", "recall_num", "user_embedding_source"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_request"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.EmbeddingRequest", "inputs": [{"common_attr": "recall_num", "path": "search_num"}, {"append": true, "common_attr": "embedding", "path": "query_vec"}, {"common_attr": "index_name", "path": "index_name"}, {"common_attr": "dimension", "path": "dimension"}, {"append": true, "common_attr": "user_embedding_source", "path": "user_embedding_source"}], "item_table": "new_clip_info_table", "output_common_attr": "embedding_request", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::build_protobuf_D5ADBE": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "photo_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["user_info"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.AdUserInfo", "inputs": [{"common_attr": "photo_id", "path": "id"}], "output_common_attr": "user_info", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::build_table_from_common_list_attr_4A937E": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "distance_list", "photo_id_list"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["photo_id", "score"]}, "build_config": [{"dest": "photo_id", "src": "photo_id_list"}, {"dest": "score", "src": "distance_list"}], "new_table": "", "skip": "{{_elseif_control_attr_18}}", "type_name": "BuildNewTableFromCommonListAttrRetriever"}, "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpcTimeCostEnd", "get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpcTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpcTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpcTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpcTimeCostEnd", "get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpcTimeCostStart"], "lua_script": "function gen_common_attrs() return (get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpcTimeCostEnd - get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpcTimeCostStart) end", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpcTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpcTimeCostEnd", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpcTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpcTimeCostStart", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::enrich_by_generic_grpc_B4BCDE": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "ann_request"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ann_response"], "$output_item_attrs": []}, "downstream_processor": "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::enrich_with_protobuf_97C9BE", "kess_service": "grpc_adTagRetrieveServer_offline", "method_name": "/kuaishou.ad.AdEmbeddingRetrGrpcService/Search", "request_attr": "ann_request", "response_attr": "ann_response", "response_class": "kuaishou.ad.AnnResponse", "skip": "{{_elseif_control_attr_18}}", "timeout_ms": 1000, "type_name": "CommonRecoGenericGrpcEnricher"}, "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::enrich_with_protobuf_97C9BE": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "ann_response"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["distance_list", "photo_id_list"], "$output_item_attrs": []}, "attrs": [{"name": "distance_list", "path": "embedding_result.result_item.distance"}, {"name": "photo_id_list", "path": "embedding_result.result_item.embedding_item_id"}], "from_extra_var": "ann_response", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpcTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpc", "common_attrs": ["get_dup_photo_realtime_similar_list__get_retrieval_list_from_rpcTimeCost"], "perf_base": 1, "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::set_attr_value_CF5AA8": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["dimension", "field_name"], "$output_item_attrs": []}, "common_attrs": [{"name": "dimension", "type": "int", "value": 128}, {"name": "field_name", "type": "string", "value": "test"}], "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoItemAttrDefaultValueEnricher"}, "get_dup_photo_realtime_similar_list::log_debug_info_29C567": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": ["feature", "frame_keys", "frame_size", "photo_id"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "for_debug_request_only": false, "item_attrs": ["photo_id", "frame_keys", "frame_size", "feature"], "item_table": "query_photo_info_table", "respect_sample_logging": false, "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoDebugInfoObserver"}, "get_dup_photo_realtime_similar_list::log_debug_info_BB27A5": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "avg_emb_normal", "data_type", "embedding_req", "photo_create_time", "photo_id", "self_inner_product"], "$input_item_attrs": ["photo_id", "score", "similar_score"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "common_attrs": ["data_type", "photo_id", "photo_create_time", "embedding_req", "avg_emb_normal", "self_inner_product"], "for_debug_request_only": false, "item_attrs": ["photo_id", "score", "similar_score"], "item_table": "", "respect_sample_logging": false, "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoDebugInfoObserver"}, "get_dup_photo_realtime_similar_list::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "get_dup_photo_realtime_similar_listTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:get_dup_photo_realtime_similar_list", "common_attrs": ["get_dup_photo_realtime_similar_listTimeCost"], "perf_base": 1, "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "get_dup_photo_realtime_similar_list::perflog_22FB0E69": {"$metadata": {"$input_common_attrs": ["_if_control_attr_19"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "target_or_query_item_empty", "mode": "count", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_if_control_attr_19}}", "subtag": "rank", "type_name": "CommonRecoPerflogObserver"}, "get_dup_photo_realtime_similar_list::perflog_BA399402": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "rank_item_num"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "mode": "interval", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_elseif_control_attr_18}}", "subtag": "rank_item_num", "type_name": "CommonRecoPerflogObserver", "value": "{{rank_item_num}}"}, "get_dup_photo_realtime_similar_list::photo_frame_key_enricher_5EE206": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": ["photo_id"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["frame_keys", "frame_status"]}, "frame_keys_column": "frame_keys", "frame_status_column": "frame_status", "item_table": "", "key_prefix": "nn_pkf_", "photo_id_column": "photo_id", "redis_cluster_name": "adDupPhotoInfoCache", "skip": "{{_elseif_control_attr_18}}", "type_name": "PhotoFrame<PERSON>ey<PERSON>nricher"}, "get_dup_photo_realtime_similar_list::photo_frame_key_sample_enricher_6FD6CB": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": ["frame_keys"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "frame_keys_column": "frame_keys", "frame_status_column": "frame_status", "item_table": "", "skip": "{{_elseif_control_attr_18}}", "type_name": "PhotoFrameKeySampleEnricher"}, "get_dup_photo_realtime_similar_list::photo_rank_sim_info_enricher_8B47BA": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "main_feature", "main_frame_size"], "$input_item_attrs": ["feature", "frame_size"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["similar_info"]}, "feature_column": "feature", "frame_size_column": "frame_size", "item_table": "", "main_feature_column": "main_feature", "main_frame_size_column": "main_frame_size", "similar_info_column": "similar_info", "skip": "{{_elseif_control_attr_18}}", "type_name": "PhotoRankSimInfoEnricher"}, "get_dup_photo_realtime_similar_list::photo_rank_sim_score_calc_enricher_B708C3": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "main_frame_size"], "$input_item_attrs": ["frame_size", "similar_info"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "frame_size_column": "frame_size", "item_table": "", "main_frame_size_column": "main_frame_size", "similar_info_column": "similar_info", "similar_score_column": "similar_score", "skip": "{{_elseif_control_attr_18}}", "type_name": "PhotoRankSimScoreCalcEnricher"}, "get_dup_photo_realtime_similar_list::retrieval_photo_filter::batch_svalue_get_from_redis_enricher_5EA822": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": ["photo_id"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["create_time"]}, "input_data_type": "int", "item_table": "", "key_column": "photo_id", "key_prefix": "n4ew_pdpc_", "output_data_type": "int", "redis_cluster_name": "adDupPhotoInfoCache", "skip": "{{_elseif_control_attr_18}}", "type_name": "BatchSvalueGetFromRedisEnricher", "value_column": "create_time"}, "get_dup_photo_realtime_similar_list::retrieval_photo_filter::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "get_dup_photo_realtime_similar_list__retrieval_photo_filterTimeCostEnd", "get_dup_photo_realtime_similar_list__retrieval_photo_filterTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_list__retrieval_photo_filterTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["get_dup_photo_realtime_similar_list__retrieval_photo_filterTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["get_dup_photo_realtime_similar_list__retrieval_photo_filterTimeCostEnd", "get_dup_photo_realtime_similar_list__retrieval_photo_filterTimeCostStart"], "lua_script": "function gen_common_attrs() return (get_dup_photo_realtime_similar_list__retrieval_photo_filterTimeCostEnd - get_dup_photo_realtime_similar_list__retrieval_photo_filterTimeCostStart) end", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_photo_realtime_similar_list::retrieval_photo_filter::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_list__retrieval_photo_filterTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_photo_realtime_similar_list__retrieval_photo_filterTimeCostEnd", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_photo_realtime_similar_list::retrieval_photo_filter::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_photo_realtime_similar_list__retrieval_photo_filterTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_photo_realtime_similar_list__retrieval_photo_filterTimeCostStart", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_photo_realtime_similar_list::retrieval_photo_filter::count_reco_result_4F23DD": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["target_item_num"], "$output_item_attrs": []}, "item_table": "", "save_result_size_to_common_attr": "target_item_num", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoCountRecoResultEnricher"}, "get_dup_photo_realtime_similar_list::retrieval_photo_filter::deduplicate_C2A551": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": []}, "item_table": "", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoResultsDeduplicateArranger"}, "get_dup_photo_realtime_similar_list::retrieval_photo_filter::filter_by_attr_2BA713": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "photo_id"], "$input_item_attrs": ["photo_id"], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": []}, "attr_name": "photo_id", "compare_to": "{{photo_id}}", "item_table": "", "remove_if": "==", "remove_if_attr_missing": true, "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoAttrFilterArranger"}, "get_dup_photo_realtime_similar_list::retrieval_photo_filter::filter_by_attr_685CE0": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "photo_create_time"], "$input_item_attrs": ["create_time"], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": []}, "attr_name": "create_time", "compare_to": "{{photo_create_time}}", "item_table": "", "remove_if": ">", "remove_if_attr_missing": true, "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoAttrFilterArranger"}, "get_dup_photo_realtime_similar_list::retrieval_photo_filter::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "get_dup_photo_realtime_similar_list__retrieval_photo_filterTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:get_dup_photo_realtime_similar_list__retrieval_photo_filter", "common_attrs": ["get_dup_photo_realtime_similar_list__retrieval_photo_filterTimeCost"], "perf_base": 1, "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "get_dup_photo_realtime_similar_list::retrieval_photo_filter::perflog_D71528D6": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "target_item_num"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "mode": "interval", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_elseif_control_attr_18}}", "subtag": "target_item_num", "type_name": "CommonRecoPerflogObserver", "value": "{{target_item_num}}"}, "get_dup_photo_realtime_similar_list::retrieval_photo_filter::sort_by_score_570F89": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": ["score"], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": []}, "item_table": "", "score_from_attr": "score", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoScoreSortArranger"}, "get_dup_photo_realtime_similar_list::retrieval_photo_filter::truncate_46957C": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18", "rank_num"], "$input_item_attrs": [], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": []}, "item_table": "", "size_limit": "{{rank_num}}", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoTruncateArranger"}, "get_dup_photo_realtime_similar_list::return__73B321": {"$metadata": {"$input_common_attrs": ["_if_control_attr_19"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_19}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "get_dup_photo_realtime_similar_list::set_attr_value_BC0278": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["biz", "cal_by_video_byte", "index_name", "rank_num", "recall_num"], "$output_item_attrs": []}, "common_attrs": [{"name": "biz", "type": "string", "value": "AD"}, {"name": "cal_by_video_byte", "type": "int", "value": 0}, {"name": "index_name", "type": "string", "value": "ann_dup_photo_new_arch"}, {"name": "recall_num", "type": "int", "value": 200}, {"name": "rank_num", "type": "int", "value": 30}], "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoItemAttrDefaultValueEnricher"}, "get_dup_photo_realtime_similar_list::sort_by_score_6B9A50": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_18"], "$input_item_attrs": ["similar_score"], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": []}, "item_table": "", "score_from_attr": "similar_score", "skip": "{{_elseif_control_attr_18}}", "type_name": "CommonRecoScoreSortArranger"}, "get_dup_rank_list::_branch_controller_052B7973": {"$branch_start": "get_dup_rank_list::_branch_controller_49DD7909", "$code_info": "[else_if] 052B7973 ad_dup_info_query_server.py in get_dup_rank_list(): .else_if_(\"#rank_list_info_str ~= 0\")", "$metadata": {"$input_common_attrs": ["_elseif_control_attr_14", "_if_control_attr_15", "rank_list_info_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_elseif_control_attr_16"], "$output_item_attrs": []}, "export_common_attr": ["_elseif_control_attr_16"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_elseif_control_attr_14", "_if_control_attr_15", "rank_list_info_str"], "lua_script": "function evaluate() if (_elseif_control_attr_14 == 0 and (_if_control_attr_15 == 1 and (#rank_list_info_str ~= 0))) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_rank_list::_branch_controller_49DD7909": {"$branch_start": "get_dup_rank_list::_branch_controller_49DD7909", "$code_info": "[if] 49DD7909 ad_dup_info_query_server.py in get_dup_rank_list(): ).if_(\"#rank_list_info_str == 0 and batch_rank_list_info_str == 0\")", "$metadata": {"$input_common_attrs": ["_elseif_control_attr_14", "batch_rank_list_info_str", "rank_list_info_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_15"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_15"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_elseif_control_attr_14", "batch_rank_list_info_str", "rank_list_info_str"], "lua_script": "function evaluate() if (_elseif_control_attr_14 == 0 and (#rank_list_info_str == 0 and batch_rank_list_info_str == 0)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_rank_list::_branch_controller_49DD7909_else": {"$branch_start": "get_dup_rank_list::_branch_controller_49DD7909", "$code_info": "[else] BDD38903 ad_dup_info_query_server.py in get_dup_rank_list(): dict(src = \"rank_scores\", dest = \"score\")", "$metadata": {"$input_common_attrs": ["_elseif_control_attr_14", "_elseif_control_attr_16", "_if_control_attr_15"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_else_control_attr_17"], "$output_item_attrs": []}, "export_common_attr": ["_else_control_attr_17"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_elseif_control_attr_14", "_elseif_control_attr_16", "_if_control_attr_15"], "lua_script": "function evaluate() if (_elseif_control_attr_14 == 0 and (_if_control_attr_15 == 1 and _elseif_control_attr_16 == 1)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_rank_list::batch_svalue_get_from_redis_enricher_117336": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14"], "$input_item_attrs": ["photo_id"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["create_time"]}, "input_data_type": "int", "item_table": "", "key_column": "photo_id", "key_prefix": "n4ew_pdpc_", "output_data_type": "int", "redis_cluster_name": "adDupPhotoInfoCache", "skip": "{{_elseif_control_attr_14}}", "type_name": "BatchSvalueGetFromRedisEnricher", "value_column": "create_time"}, "get_dup_rank_list::build_table_from_common_list_attr_701354": {"$metadata": {"$input_common_attrs": ["_else_control_attr_17", "rank_photo_ids", "rank_scores"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["photo_id", "score"]}, "build_config": [{"dest": "photo_id", "src": "rank_photo_ids"}, {"dest": "score", "src": "rank_scores"}], "new_table": "", "skip": "{{_else_control_attr_17}}", "type_name": "BuildNewTableFromCommonListAttrRetriever"}, "get_dup_rank_list::build_table_from_common_list_attr_CEC685": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_16", "rank_photo_ids", "rank_scores"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["photo_id", "score"]}, "build_config": [{"dest": "photo_id", "src": "rank_photo_ids"}, {"dest": "score", "src": "rank_scores"}], "new_table": "", "skip": "{{_elseif_control_attr_16}}", "type_name": "BuildNewTableFromCommonListAttrRetriever"}, "get_dup_rank_list::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14", "get_dup_rank_listTimeCostEnd", "get_dup_rank_listTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_rank_listTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["get_dup_rank_listTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["get_dup_rank_listTimeCostEnd", "get_dup_rank_listTimeCostStart"], "lua_script": "function gen_common_attrs() return (get_dup_rank_listTimeCostEnd - get_dup_rank_listTimeCostStart) end", "skip": "{{_elseif_control_attr_14}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_rank_list::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_rank_listTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_rank_listTimeCostEnd", "skip": "{{_elseif_control_attr_14}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_rank_list::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_rank_listTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_rank_listTimeCostStart", "skip": "{{_elseif_control_attr_14}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_rank_list::count_reco_result_D8121F": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["rank_list_size"], "$output_item_attrs": []}, "item_table": "", "save_result_size_to_common_attr": "rank_list_size", "skip": "{{_elseif_control_attr_14}}", "type_name": "CommonRecoCountRecoResultEnricher"}, "get_dup_rank_list::enrich_attr_by_lua_6E7C25": {"$metadata": {"$input_common_attrs": ["_else_control_attr_17", "rank_info_list"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["rank_photo_ids", "rank_scores"], "$output_item_attrs": []}, "export_common_attr": ["rank_photo_ids", "rank_scores"], "function_for_common": "parseRankListInfo", "import_common_attr": ["rank_info_list"], "lua_script": "function parseRankListInfo()\n            reps = \":\"\n            local rank_photo_ids = {}\n            local rank_scores = {}\n            local j = 1\n            for i = 1, #rank_info_list do\n              string.gsub(rank_info_list[i],'[^'..reps..']+',function (w)\n                if j % 2 == 1 then\n                  rank_photo_ids[i] = tonumber(w)\n                else \n                  rank_scores[i] = tonumber(w)\n                end\n                j = j + 1\n                end)\n            end\n            return rank_photo_ids, rank_scores\n          end", "skip": "{{_else_control_attr_17}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_rank_list::enrich_attr_by_lua_96B4C0": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14", "valid_duration"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["time_stamp_threshold_extra"], "$output_item_attrs": []}, "export_common_attr": ["time_stamp_threshold_extra"], "function_for_common": "getTimeStamp", "import_common_attr": ["valid_duration"], "lua_script": "function getTimeStamp()\n          return util.GetTimestamp() // 1000 - valid_duration * 86400000\n          end", "skip": "{{_elseif_control_attr_14}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_rank_list::enrich_attr_by_lua_D7DD78": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14", "batch_rank_list_key_prefix", "photo_id", "rank_list_key_prefix"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["batch_rank_list_cache_key", "rank_list_cache_key"], "$output_item_attrs": []}, "export_common_attr": ["rank_list_cache_key", "batch_rank_list_cache_key"], "function_for_common": "getRankListCache<PERSON>ey", "import_common_attr": ["photo_id", "rank_list_key_prefix", "batch_rank_list_key_prefix"], "lua_script": "function getRankListCacheKey()\n          return rank_list_key_prefix..tostring(photo_id), batch_rank_list_key_prefix..tostring(photo_id) \n        end", "skip": "{{_elseif_control_attr_14}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_rank_list::enrich_attr_by_lua_DB8CC3": {"$metadata": {"$input_common_attrs": ["_else_control_attr_17", "batch_rank_list_info_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["rank_info_list"], "$output_item_attrs": []}, "export_common_attr": ["rank_info_list"], "function_for_common": "parseRankListInfo", "import_common_attr": ["batch_rank_list_info_str"], "lua_script": "function parseRankListInfo()\n            reps = \",\"\n            local rank_info_list = {}\n            local i = 1\n            string.gsub(batch_rank_list_info_str,'[^'..reps..']+',function (w)\n              rank_info_list[i] = w\n              i = i + 1\n            end)\n            return rank_info_list\n          end", "skip": "{{_else_control_attr_17}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_rank_list::enrich_with_protobuf_E1963F": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_16", "rank_list_pb"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["rank_photo_ids", "rank_scores"], "$output_item_attrs": []}, "attrs": [{"name": "rank_photo_ids", "path": "id"}, {"name": "rank_scores", "path": "score"}], "from_extra_var": "rank_list_pb", "skip": "{{_elseif_control_attr_16}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "get_dup_rank_list::filter_by_attr_E220C7": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14", "time_stamp_threshold_extra"], "$input_item_attrs": ["create_time"], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": []}, "attr_name": "create_time", "compare_to": "{{time_stamp_threshold_extra}}", "item_table": "", "remove_if": "<", "remove_if_attr_missing": true, "skip": "{{_elseif_control_attr_14}}", "type_name": "CommonRecoAttrFilterArranger"}, "get_dup_rank_list::get_common_attr_from_redis_2ACE76": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14", "batch_rank_list_cache_key", "rank_list_cache_key"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["batch_rank_list_info_str", "rank_list_info_str"], "$output_item_attrs": []}, "cluster_name": "adDupPhotoRetrievalAndRankCache", "redis_params": [{"output_attr_name": "rank_list_info_str", "output_attr_type": "string", "redis_key": "{{rank_list_cache_key}}", "redis_value_type": "string"}, {"output_attr_name": "batch_rank_list_info_str", "output_attr_type": "string", "redis_key": "{{batch_rank_list_cache_key}}", "redis_value_type": "string"}], "skip": "{{_elseif_control_attr_14}}", "type_name": "CommonRecoRedisCommonAttrEnricher"}, "get_dup_rank_list::log_debug_info_1DEA69": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14", "data_type", "enable_use_cache", "photo_id", "rank_list_cache_key", "rank_list_size", "rank_photo_ids", "rank_scores", "time_stamp_threshold_extra", "valid_duration"], "$input_item_attrs": ["create_time", "photo_id", "score"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "common_attrs": ["time_stamp_threshold_extra", "data_type", "photo_id", "enable_use_cache", "valid_duration", "rank_list_cache_key", "rank_list_size", "rank_photo_ids", "rank_scores"], "for_debug_request_only": false, "item_attrs": ["photo_id", "score", "create_time"], "item_table": "", "skip": "{{_elseif_control_attr_14}}", "type_name": "CommonRecoDebugInfoObserver"}, "get_dup_rank_list::pack_item_attr_EB7BDF": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14"], "$input_item_attrs": ["photo_id", "score"], "$modify_item_tables": [], "$output_common_attrs": ["rank_photo_ids", "rank_scores"], "$output_item_attrs": []}, "item_source": {"reco_results": true}, "item_table": "", "mappings": [{"from_item_attr": "photo_id", "to_common_attr": "rank_photo_ids"}, {"from_item_attr": "score", "to_common_attr": "rank_scores"}], "skip": "{{_elseif_control_attr_14}}", "type_name": "CommonRecoItemAttrPackEnricher"}, "get_dup_rank_list::parse_protobuf_from_string_AB339C": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_16", "rank_list_info_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["rank_list_pb"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.algorithm.DupPhotoIdList", "input_attr": "rank_list_info_str", "output_attr": "rank_list_pb", "skip": "{{_elseif_control_attr_16}}", "type_name": "CommonRecoProtobufParseAttrEnricher"}, "get_dup_rank_list::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14", "get_dup_rank_listTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:get_dup_rank_list", "common_attrs": ["get_dup_rank_listTimeCost"], "perf_base": 1, "skip": "{{_elseif_control_attr_14}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "get_dup_rank_list::perflog_1C973F5F": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14", "rank_list_size"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "mode": "interval", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_elseif_control_attr_14}}", "subtag": "rank_list_size", "type_name": "CommonRecoPerflogObserver", "value": "{{rank_list_size}}"}, "get_dup_rank_list::perflog_3562033D": {"$metadata": {"$input_common_attrs": ["_else_control_attr_17"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "get_rank_list_from_batch_data", "mode": "count", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_else_control_attr_17}}", "subtag": "rank_list", "type_name": "CommonRecoPerflogObserver"}, "get_dup_rank_list::perflog_79D85911": {"$metadata": {"$input_common_attrs": ["_if_control_attr_15"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "get_rank_list_fail", "mode": "count", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_if_control_attr_15}}", "subtag": "rank_list", "type_name": "CommonRecoPerflogObserver"}, "get_dup_rank_list::perflog_968130C6": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_16"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "get_rank_list_from_inc_data", "mode": "count", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_elseif_control_attr_16}}", "subtag": "rank_list", "type_name": "CommonRecoPerflogObserver"}, "get_dup_rank_list::return__0E1FE6": {"$metadata": {"$input_common_attrs": ["_if_control_attr_15"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_15}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "get_dup_rank_list::set_attr_value_15FE61": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["batch_rank_list_info_str", "batch_rank_list_key_prefix", "rank_list_info_str", "rank_list_key_prefix"], "$output_item_attrs": []}, "common_attrs": [{"name": "rank_list_key_prefix", "type": "string", "value": "ralkp_"}, {"name": "rank_list_info_str", "type": "string", "value": ""}, {"name": "batch_rank_list_key_prefix", "type": "string", "value": "cralkp_"}, {"name": "batch_rank_list_info_str", "type": "string", "value": ""}], "skip": "{{_elseif_control_attr_14}}", "type_name": "CommonRecoItemAttrDefaultValueEnricher"}, "get_dup_rank_list::sort_by_score_00AF7D": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_14"], "$input_item_attrs": ["score"], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": []}, "item_table": "", "score_from_attr": "score", "skip": "{{_elseif_control_attr_14}}", "type_name": "CommonRecoScoreSortArranger"}, "get_dup_retrieval_list::_branch_controller_4A40A029": {"$branch_start": "get_dup_retrieval_list::_branch_controller_D8FE9BC5", "$code_info": "[else_if] 4A40A029 ad_dup_info_query_server.py in get_dup_retrieval_list(): .else_if_(\"#embedding_str_from_old_cache ~= 0\")", "$metadata": {"$input_common_attrs": ["_else_control_attr_8", "_if_control_attr_9", "embedding_str_from_old_cache"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_elseif_control_attr_12"], "$output_item_attrs": []}, "export_common_attr": ["_elseif_control_attr_12"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_else_control_attr_8", "_if_control_attr_9", "embedding_str_from_old_cache"], "lua_script": "function evaluate() if (_else_control_attr_8 == 0 and (_if_control_attr_9 == 1 and (#embedding_str_from_old_cache ~= 0))) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::_branch_controller_60F69F55": {"$branch_start": "get_dup_retrieval_list::_branch_controller_60F69F55", "$code_info": "[if] 60F69F55 ad_dup_info_query_server.py in get_dup_retrieval_list(): ).if_(\"enable_realtime_get == 1\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_9", "enable_realtime_get"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_10"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_10"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_9", "enable_realtime_get"], "lua_script": "function evaluate() if (_if_control_attr_9 == 0 and (enable_realtime_get == 1)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::_branch_controller_60F69F55_else": {"$branch_start": "get_dup_retrieval_list::_branch_controller_60F69F55", "$code_info": "[else] 083060E8 ad_dup_info_query_server.py in get_dup_retrieval_list(): extra1 = \"get_embedding_from_rpc\"", "$metadata": {"$input_common_attrs": ["_if_control_attr_10", "_if_control_attr_9"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_else_control_attr_11"], "$output_item_attrs": []}, "export_common_attr": ["_else_control_attr_11"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_10", "_if_control_attr_9"], "lua_script": "function evaluate() if (_if_control_attr_9 == 0 and (_if_control_attr_10 == 1)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::_branch_controller_B1FE6D5E": {"$branch_start": "get_dup_retrieval_list::_branch_controller_B1FE6D5E", "$code_info": "[if] B1FE6D5E ad_dup_info_query_server.py in get_dup_retrieval_list(): \t\t).if_(\"_REQ_NUM_ > 200\")", "$metadata": {"$input_common_attrs": ["_REQ_NUM_", "_if_control_attr_4"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_5"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_5"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_REQ_NUM_", "_if_control_attr_4"], "lua_script": "function evaluate() if (_if_control_attr_4 == 0 and (_REQ_NUM_ > 200)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::_branch_controller_CC524E36": {"$branch_start": "get_dup_retrieval_list::_branch_controller_CC524E36", "$code_info": "[if] CC524E36 ad_dup_info_query_server.py in get_dup_retrieval_list(): ).if_(\"#retrieval_list_str ~= 0 and enable_use_cache == 1\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_4", "enable_use_cache", "retrieval_list_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_7"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_7"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_4", "enable_use_cache", "retrieval_list_str"], "lua_script": "function evaluate() if (_if_control_attr_4 == 0 and (#retrieval_list_str ~= 0 and enable_use_cache == 1)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::_branch_controller_CC524E36_else": {"$branch_start": "get_dup_retrieval_list::_branch_controller_CC524E36", "$code_info": "[else] 09F91562 ad_dup_info_query_server.py in get_dup_retrieval_list(): dict(src = \"retrieval_scores\", dest = \"score\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_4", "_if_control_attr_7"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_else_control_attr_8"], "$output_item_attrs": []}, "export_common_attr": ["_else_control_attr_8"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_4", "_if_control_attr_7"], "lua_script": "function evaluate() if (_if_control_attr_4 == 0 and (_if_control_attr_7 == 1)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::_branch_controller_D8FE9BC5": {"$branch_start": "get_dup_retrieval_list::_branch_controller_D8FE9BC5", "$code_info": "[if] D8FE9BC5 ad_dup_info_query_server.py in get_dup_retrieval_list(): ).if_(\"#embedding_str_from_old_cache == 0 and #embedding_str_from_self_cache == 0\")", "$metadata": {"$input_common_attrs": ["_else_control_attr_8", "embedding_str_from_old_cache", "embedding_str_from_self_cache"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_9"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_9"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_else_control_attr_8", "embedding_str_from_old_cache", "embedding_str_from_self_cache"], "lua_script": "function evaluate() if (_else_control_attr_8 == 0 and (#embedding_str_from_old_cache == 0 and #embedding_str_from_self_cache == 0)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::_branch_controller_D8FE9BC5_else": {"$branch_start": "get_dup_retrieval_list::_branch_controller_D8FE9BC5", "$code_info": "[else] A4BE7B44 ad_dup_info_query_server.py in get_dup_retrieval_list(): photo_normal_embedding_column = \"avg_emb_normal\"", "$metadata": {"$input_common_attrs": ["_else_control_attr_8", "_elseif_control_attr_12", "_if_control_attr_9"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_else_control_attr_13"], "$output_item_attrs": []}, "export_common_attr": ["_else_control_attr_13"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_else_control_attr_8", "_elseif_control_attr_12", "_if_control_attr_9"], "lua_script": "function evaluate() if (_else_control_attr_8 == 0 and (_if_control_attr_9 == 1 and _elseif_control_attr_12 == 1)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::_branch_controller_E25DE4CD": {"$branch_start": "get_dup_retrieval_list::_branch_controller_E25DE4CD", "$code_info": "[if] E25DE4CD ad_dup_info_query_server.py in get_dup_retrieval_list(): .if_(\"recall_num >= 500\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_4", "recall_num"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_6"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_6"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_4", "recall_num"], "lua_script": "function evaluate() if (_if_control_attr_4 == 0 and (recall_num >= 500)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::batch_svalue_get_from_redis_enricher_1E4B71": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4"], "$input_item_attrs": ["photo_id"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["create_time"]}, "input_data_type": "int", "item_table": "", "key_column": "photo_id", "key_prefix": "n4ew_pdpc_", "output_data_type": "int", "redis_cluster_name": "adDupPhotoInfoCache", "skip": "{{_if_control_attr_4}}", "type_name": "BatchSvalueGetFromRedisEnricher", "value_column": "create_time"}, "get_dup_retrieval_list::build_table_from_common_list_attr_21836D": {"$metadata": {"$input_common_attrs": ["_if_control_attr_7", "retrieval_photo_ids", "retrieval_scores"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["photo_id", "score"]}, "build_config": [{"dest": "photo_id", "src": "retrieval_photo_ids"}, {"dest": "score", "src": "retrieval_scores"}], "new_table": "", "skip": "{{_if_control_attr_7}}", "type_name": "BuildNewTableFromCommonListAttrRetriever"}, "get_dup_retrieval_list::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4", "get_dup_retrieval_listTimeCostEnd", "get_dup_retrieval_listTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_listTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["get_dup_retrieval_listTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["get_dup_retrieval_listTimeCostEnd", "get_dup_retrieval_listTimeCostStart"], "lua_script": "function gen_common_attrs() return (get_dup_retrieval_listTimeCostEnd - get_dup_retrieval_listTimeCostStart) end", "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_listTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_retrieval_listTimeCostEnd", "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_retrieval_list::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_listTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_retrieval_listTimeCostStart", "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_retrieval_list::copy_attr_63A069": {"$metadata": {"$input_common_attrs": ["_REQ_NUM_", "_if_control_attr_5"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["recall_num"], "$output_item_attrs": []}, "attrs": [{"from_common": "_REQ_NUM_", "to_common": "recall_num"}], "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoCopyAttrEnricher"}, "get_dup_retrieval_list::count_reco_result_2289A2": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["retrieval_list_size"], "$output_item_attrs": []}, "item_table": "", "save_result_size_to_common_attr": "retrieval_list_size", "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoCountRecoResultEnricher"}, "get_dup_retrieval_list::embedding_normalize_enricher_04E9DF": {"$metadata": {"$input_common_attrs": ["_else_control_attr_13", "avg_emb"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["avg_emb_normal"], "$output_item_attrs": []}, "is_common_attr": true, "photo_embedding_column": "avg_emb", "photo_normal_embedding_column": "avg_emb_normal", "skip": "{{_else_control_attr_13}}", "type_name": "EmbeddingNormalizeEnricher"}, "get_dup_retrieval_list::embedding_normalize_enricher_2903BE": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_12", "avg_emb"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["avg_emb_normal"], "$output_item_attrs": []}, "is_common_attr": true, "photo_embedding_column": "avg_emb", "photo_normal_embedding_column": "avg_emb_normal", "skip": "{{_elseif_control_attr_12}}", "type_name": "EmbeddingNormalizeEnricher"}, "get_dup_retrieval_list::enrich_attr_by_lua_1DE67B": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4", "emb_key_prefix", "extra_emb_key_prefix", "photo_id", "retrieval_list_key_prefix"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["emb_cache_key", "extra_emb_cache_key", "photo_id_str", "retrieval_list_cache_key"], "$output_item_attrs": []}, "export_common_attr": ["retrieval_list_cache_key", "emb_cache_key", "extra_emb_cache_key", "photo_id_str"], "function_for_common": "getRetrievalListCache<PERSON>ey", "import_common_attr": ["photo_id", "retrieval_list_key_prefix", "emb_key_prefix", "extra_emb_key_prefix"], "lua_script": "function getRetrievalListCache<PERSON>ey()\n          return retrieval_list_key_prefix..tostring(photo_id), emb_key_prefix..tostring(photo_id), extra_emb_key_prefix..tostring(photo_id), tostring(photo_id)\n        end", "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::enrich_attr_by_lua_B311F6": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4", "valid_duration"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["time_stamp_threshold"], "$output_item_attrs": []}, "export_common_attr": ["time_stamp_threshold"], "function_for_common": "getTimeStamp", "import_common_attr": ["valid_duration"], "lua_script": "function getTimeStamp()\n            return util.GetTimestamp() // 1000 - valid_duration * 86400000\n          end", "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::enrich_with_protobuf_573742": {"$metadata": {"$input_common_attrs": ["_else_control_attr_13", "embedding_proto"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["avg_emb"], "$output_item_attrs": []}, "attrs": [{"name": "avg_emb", "path": "value"}], "from_extra_var": "embedding_proto", "skip": "{{_else_control_attr_13}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "get_dup_retrieval_list::enrich_with_protobuf_8822DC": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_12", "embedding_proto"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["avg_emb"], "$output_item_attrs": []}, "attrs": [{"name": "avg_emb", "path": "value"}], "from_extra_var": "embedding_proto", "skip": "{{_elseif_control_attr_12}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "get_dup_retrieval_list::enrich_with_protobuf_A1756B": {"$metadata": {"$input_common_attrs": ["_if_control_attr_7", "retrieval_list_pb"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["retrieval_photo_ids", "retrieval_scores"], "$output_item_attrs": []}, "attrs": [{"name": "retrieval_photo_ids", "path": "id"}, {"name": "retrieval_scores", "path": "score"}], "from_extra_var": "retrieval_list_pb", "skip": "{{_if_control_attr_7}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "get_dup_retrieval_list::filter_by_attr_450371": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4", "time_stamp_threshold"], "$input_item_attrs": ["create_time"], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": []}, "attr_name": "create_time", "compare_to": "{{time_stamp_threshold}}", "item_table": "", "remove_if": "<", "remove_if_attr_missing": true, "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoAttrFilterArranger"}, "get_dup_retrieval_list::filter_by_attr_E5783F": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4", "photo_id"], "$input_item_attrs": ["photo_id"], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": []}, "attr_name": "photo_id", "compare_to": "{{photo_id}}", "item_table": "", "remove_if": "==", "remove_if_attr_missing": true, "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoAttrFilterArranger"}, "get_dup_retrieval_list::get_common_attr_from_redis_147008": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "extra_emb_cache_key"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_str_from_self_cache"], "$output_item_attrs": []}, "cluster_name": "adDupPhotoRetrievalAndRankCache", "redis_params": [{"output_attr_name": "embedding_str_from_self_cache", "output_attr_type": "string", "redis_key": "{{extra_emb_cache_key}}", "redis_value_type": "string"}], "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoRedisCommonAttrEnricher"}, "get_dup_retrieval_list::get_common_attr_from_redis_744C73": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4", "retrieval_list_cache_key"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["retrieval_list_str"], "$output_item_attrs": []}, "cluster_name": "adDupPhotoRetrievalAndRankCache", "redis_params": [{"output_attr_name": "retrieval_list_str", "output_attr_type": "string", "redis_key": "{{retrieval_list_cache_key}}", "redis_value_type": "string"}], "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoRedisCommonAttrEnricher"}, "get_dup_retrieval_list::get_common_attr_from_redis_BA754A": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "emb_cache_key"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_str_from_old_cache"], "$output_item_attrs": []}, "cluster_name": "adDupPhotoInfoCache", "redis_params": [{"output_attr_name": "embedding_str_from_old_cache", "output_attr_type": "string", "redis_key": "{{emb_cache_key}}", "redis_value_type": "string"}], "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoRedisCommonAttrEnricher"}, "get_dup_retrieval_list::get_embedding_from_rpc::build_protobuf_7ECDC5": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10", "biz", "cal_by_video_byte", "photo_id_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_req"], "$output_item_attrs": []}, "class_name": "mmu.video.DlFeatureRequest", "inputs": [{"common_attr": "photo_id_str", "path": "id"}, {"common_attr": "cal_by_video_byte", "path": "cal_by_video_byte"}, {"common_attr": "biz", "path": "source_from"}], "output_common_attr": "embedding_req", "skip": "{{_if_control_attr_10}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_dup_retrieval_list::get_embedding_from_rpc::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10", "get_dup_retrieval_list__get_embedding_from_rpcTimeCostEnd", "get_dup_retrieval_list__get_embedding_from_rpcTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_list__get_embedding_from_rpcTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["get_dup_retrieval_list__get_embedding_from_rpcTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["get_dup_retrieval_list__get_embedding_from_rpcTimeCostEnd", "get_dup_retrieval_list__get_embedding_from_rpcTimeCostStart"], "lua_script": "function gen_common_attrs() return (get_dup_retrieval_list__get_embedding_from_rpcTimeCostEnd - get_dup_retrieval_list__get_embedding_from_rpcTimeCostStart) end", "skip": "{{_if_control_attr_10}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::get_embedding_from_rpc::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_list__get_embedding_from_rpcTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_retrieval_list__get_embedding_from_rpcTimeCostEnd", "skip": "{{_if_control_attr_10}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_retrieval_list::get_embedding_from_rpc::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_list__get_embedding_from_rpcTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_retrieval_list__get_embedding_from_rpcTimeCostStart", "skip": "{{_if_control_attr_10}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_retrieval_list::get_embedding_from_rpc::embedding_normalize_enricher_7DDF6F": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10", "avg_emb"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["avg_emb_normal"], "$output_item_attrs": []}, "is_common_attr": true, "photo_embedding_column": "avg_emb", "photo_normal_embedding_column": "avg_emb_normal", "skip": "{{_if_control_attr_10}}", "type_name": "EmbeddingNormalizeEnricher"}, "get_dup_retrieval_list::get_embedding_from_rpc::enrich_attr_by_lua_0A0819": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10", "avg_emb_normal"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["self_inner_product"], "$output_item_attrs": []}, "export_common_attr": ["self_inner_product"], "function_for_common": "innerProduct", "import_common_attr": ["avg_emb_normal"], "lua_script": "function innerProduct()\n            local val = 0.0\n            for i = 1, #avg_emb_normal do\n              val = val + avg_emb_normal[i] * avg_emb_normal[i]\n            end\n            return val\n          end", "skip": "{{_if_control_attr_10}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::get_embedding_from_rpc::enrich_attr_by_lua_69AE09": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10", "embedding_list"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["avg_emb", "emb_num", "is_emb_valid"], "$output_item_attrs": []}, "export_common_attr": ["is_emb_valid", "avg_emb", "emb_num"], "function_for_common": "embCheckAndAvg", "import_common_attr": ["embedding_list"], "lua_script": "function embCheckAndAvg()\n            local len = #embedding_list\n            local emb_num = len / 128\n            local avg_emb = {}\n            for i = 1, 128 do\n              avg_emb[i] = 0.0\n            end\n            if len % 128 ~= 0 or len > 1280 then\n              return 0, avg_emb, math.floor(emb_num)\n            else\n              for i = 1, len do\n                local idx  = i - 1\n                avg_emb[idx % 128 + 1] = avg_emb[idx % 128 + 1] + embedding_list[i]\n              end\n              for i = 1, #avg_emb do\n                avg_emb[i] = avg_emb[i] / emb_num\n              end\n              return 1, avg_emb, math.floor(emb_num)\n            end\n          end", "skip": "{{_if_control_attr_10}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::get_embedding_from_rpc::enrich_by_generic_grpc_F550B6": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10", "embedding_req"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_resp"], "$output_item_attrs": []}, "downstream_processor": "get_dup_retrieval_list::get_embedding_from_rpc::enrich_with_protobuf_657EE1", "kess_service": "grpc_mmuDlFeatureCalculateService", "method_name": "/mmu.video.DlFeatureCalculateService/CalculateFeature", "request_attr": "embedding_req", "response_attr": "embedding_resp", "response_class": "mmu.video.DlFeatureResponse", "skip": "{{_if_control_attr_10}}", "timeout_ms": 10000, "type_name": "CommonRecoGenericGrpcEnricher"}, "get_dup_retrieval_list::get_embedding_from_rpc::enrich_with_protobuf_657EE1": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10", "embedding_resp"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_list"], "$output_item_attrs": []}, "attrs": [{"name": "embedding_list", "path": "feature.features.value.feature"}], "from_extra_var": "embedding_resp", "skip": "{{_if_control_attr_10}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "get_dup_retrieval_list::get_embedding_from_rpc::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10", "get_dup_retrieval_list__get_embedding_from_rpcTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:get_dup_retrieval_list__get_embedding_from_rpc", "common_attrs": ["get_dup_retrieval_list__get_embedding_from_rpcTimeCost"], "perf_base": 1, "skip": "{{_if_control_attr_10}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "get_dup_retrieval_list::get_retrieval_list_from_rpc::build_protobuf_03BA6C": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "photo_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["user_info"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.AdUserInfo", "inputs": [{"common_attr": "photo_id", "path": "id"}], "output_common_attr": "user_info", "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_dup_retrieval_list::get_retrieval_list_from_rpc::build_protobuf_04830B": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "embedding_request", "user_info"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ann_request"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.AnnRequest", "inputs": [{"common_attr": "user_info", "path": "user_info"}, {"append": true, "common_attr": "embedding_request", "path": "embedding_request"}], "output_common_attr": "ann_request", "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_dup_retrieval_list::get_retrieval_list_from_rpc::build_protobuf_3BDCBB": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "field_name"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["user_embedding_source"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.UserEmbeddingPicassoSourceInfo", "inputs": [{"common_attr": "field_name", "path": "field"}], "output_common_attr": "user_embedding_source", "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_dup_retrieval_list::get_retrieval_list_from_rpc::build_protobuf_504A30": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "dimension", "embedding", "index_name", "recall_num", "user_embedding_source"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_request"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.EmbeddingRequest", "inputs": [{"common_attr": "recall_num", "path": "search_num"}, {"append": true, "common_attr": "embedding", "path": "query_vec"}, {"common_attr": "index_name", "path": "index_name"}, {"common_attr": "dimension", "path": "dimension"}, {"append": true, "common_attr": "user_embedding_source", "path": "user_embedding_source"}], "item_table": "new_clip_info_table", "output_common_attr": "embedding_request", "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_dup_retrieval_list::get_retrieval_list_from_rpc::build_protobuf_D474B7": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "avg_emb_normal"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.Rttr<PERSON>", "inputs": [{"append": true, "common_attr": "avg_emb_normal", "path": "value"}], "output_common_attr": "embedding", "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_dup_retrieval_list::get_retrieval_list_from_rpc::build_table_from_common_list_attr_70204B": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "distance_list", "photo_id_list"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["photo_id", "score"]}, "build_config": [{"dest": "photo_id", "src": "photo_id_list"}, {"dest": "score", "src": "distance_list"}], "new_table": "", "skip": "{{_else_control_attr_8}}", "type_name": "BuildNewTableFromCommonListAttrRetriever"}, "get_dup_retrieval_list::get_retrieval_list_from_rpc::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "get_dup_retrieval_list__get_retrieval_list_from_rpcTimeCostEnd", "get_dup_retrieval_list__get_retrieval_list_from_rpcTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_list__get_retrieval_list_from_rpcTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["get_dup_retrieval_list__get_retrieval_list_from_rpcTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["get_dup_retrieval_list__get_retrieval_list_from_rpcTimeCostEnd", "get_dup_retrieval_list__get_retrieval_list_from_rpcTimeCostStart"], "lua_script": "function gen_common_attrs() return (get_dup_retrieval_list__get_retrieval_list_from_rpcTimeCostEnd - get_dup_retrieval_list__get_retrieval_list_from_rpcTimeCostStart) end", "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::get_retrieval_list_from_rpc::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_list__get_retrieval_list_from_rpcTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_retrieval_list__get_retrieval_list_from_rpcTimeCostEnd", "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_retrieval_list::get_retrieval_list_from_rpc::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_list__get_retrieval_list_from_rpcTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_retrieval_list__get_retrieval_list_from_rpcTimeCostStart", "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_retrieval_list::get_retrieval_list_from_rpc::enrich_by_generic_grpc_74F088": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "ann_request"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ann_response"], "$output_item_attrs": []}, "downstream_processor": "get_dup_retrieval_list::get_retrieval_list_from_rpc::enrich_with_protobuf_4A066E", "kess_service": "grpc_adTagRetrieveServer_offline", "method_name": "/kuaishou.ad.AdEmbeddingRetrGrpcService/Search", "request_attr": "ann_request", "response_attr": "ann_response", "response_class": "kuaishou.ad.AnnResponse", "skip": "{{_else_control_attr_8}}", "timeout_ms": 1000, "type_name": "CommonRecoGenericGrpcEnricher"}, "get_dup_retrieval_list::get_retrieval_list_from_rpc::enrich_with_protobuf_4A066E": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "ann_response"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["distance_list", "photo_id_list"], "$output_item_attrs": []}, "attrs": [{"name": "distance_list", "path": "embedding_result.result_item.distance"}, {"name": "photo_id_list", "path": "embedding_result.result_item.embedding_item_id"}], "from_extra_var": "ann_response", "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "get_dup_retrieval_list::get_retrieval_list_from_rpc::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "get_dup_retrieval_list__get_retrieval_list_from_rpcTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:get_dup_retrieval_list__get_retrieval_list_from_rpc", "common_attrs": ["get_dup_retrieval_list__get_retrieval_list_from_rpcTimeCost"], "perf_base": 1, "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "get_dup_retrieval_list::get_retrieval_list_from_rpc::set_attr_value_E975CA": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["dimension", "field_name"], "$output_item_attrs": []}, "common_attrs": [{"name": "dimension", "type": "int", "value": 128}, {"name": "field_name", "type": "string", "value": "test"}], "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoItemAttrDefaultValueEnricher"}, "get_dup_retrieval_list::log_debug_info_EBF98C": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4", "avg_emb_normal", "data_type", "emb_cache_key", "embedding_str_from_old_cache", "enable_use_cache", "recall_num", "retrieval_photo_ids", "retrieval_scores"], "$input_item_attrs": ["create_time", "photo_id", "score"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "common_attrs": ["data_type", "enable_use_cache", "recall_num", "retrieval_photo_ids", "retrieval_scores", "avg_emb_normal", "emb_cache_key", "embedding_str_from_old_cache"], "for_debug_request_only": false, "item_attrs": ["photo_id", "score", "create_time"], "item_table": "", "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoDebugInfoObserver"}, "get_dup_retrieval_list::lookup_kconf_684677": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "photo_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["enable_realtime_get"], "$output_item_attrs": []}, "kconf_configs": [{"kconf_key": "ad.materialGenerationServer.dupInfoQueryRealtimeEmbRatio", "lookup_attr": "photo_id", "output_attr": "enable_realtime_get", "value_type": "tail_number"}], "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoKconfLookupEnricher"}, "get_dup_retrieval_list::pack_item_attr_170164": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4"], "$input_item_attrs": ["photo_id", "score"], "$modify_item_tables": [], "$output_common_attrs": ["retrieval_photo_ids", "retrieval_scores"], "$output_item_attrs": []}, "item_source": {"reco_results": true}, "item_table": "", "mappings": [{"from_item_attr": "photo_id", "to_common_attr": "retrieval_photo_ids"}, {"from_item_attr": "score", "to_common_attr": "retrieval_scores"}], "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoItemAttrPackEnricher"}, "get_dup_retrieval_list::parse_protobuf_from_string_0D1866": {"$metadata": {"$input_common_attrs": ["_else_control_attr_13", "embedding_str_from_self_cache"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_proto"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.Rttr<PERSON>", "input_attr": "embedding_str_from_self_cache", "output_attr": "embedding_proto", "skip": "{{_else_control_attr_13}}", "type_name": "CommonRecoProtobufParseAttrEnricher"}, "get_dup_retrieval_list::parse_protobuf_from_string_5C9B9C": {"$metadata": {"$input_common_attrs": ["_if_control_attr_7", "retrieval_list_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["retrieval_list_pb"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.algorithm.DupPhotoIdList", "input_attr": "retrieval_list_str", "output_attr": "retrieval_list_pb", "skip": "{{_if_control_attr_7}}", "type_name": "CommonRecoProtobufParseAttrEnricher"}, "get_dup_retrieval_list::parse_protobuf_from_string_79042D": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_12", "embedding_str_from_old_cache"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_proto"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.Rttr<PERSON>", "input_attr": "embedding_str_from_old_cache", "output_attr": "embedding_proto", "skip": "{{_elseif_control_attr_12}}", "type_name": "CommonRecoProtobufParseAttrEnricher"}, "get_dup_retrieval_list::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4", "get_dup_retrieval_listTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:get_dup_retrieval_list", "common_attrs": ["get_dup_retrieval_listTimeCost"], "perf_base": 1, "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "get_dup_retrieval_list::perflog_119BB1C6": {"$metadata": {"$input_common_attrs": ["_else_control_attr_13"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "get_embedding_from_self_redis", "mode": "count", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_else_control_attr_13}}", "subtag": "retrieval_list", "type_name": "CommonRecoPerflogObserver"}, "get_dup_retrieval_list::perflog_345C6A12": {"$metadata": {"$input_common_attrs": ["_else_control_attr_11"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "skip_embedding_from_rpc", "mode": "count", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_else_control_attr_11}}", "subtag": "retrieval_list", "type_name": "CommonRecoPerflogObserver"}, "get_dup_retrieval_list::perflog_397F60EC": {"$metadata": {"$input_common_attrs": ["_if_control_attr_7"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "get_retrieval_list_from_redis", "mode": "count", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_if_control_attr_7}}", "subtag": "retrieval_list", "type_name": "CommonRecoPerflogObserver"}, "get_dup_retrieval_list::perflog_85937C5A": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4", "retrieval_list_size"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "mode": "interval", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_if_control_attr_4}}", "subtag": "retrieval_list_size", "type_name": "CommonRecoPerflogObserver", "value": "{{retrieval_list_size}}"}, "get_dup_retrieval_list::perflog_8E3BCDFE": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "get_retrieval_list_from_ann", "mode": "count", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_else_control_attr_8}}", "subtag": "retrieval_list", "type_name": "CommonRecoPerflogObserver"}, "get_dup_retrieval_list::perflog_A70048E9": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "get_embedding_from_rpc", "mode": "count", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_if_control_attr_10}}", "subtag": "retrieval_list", "type_name": "CommonRecoPerflogObserver"}, "get_dup_retrieval_list::perflog_F238D975": {"$metadata": {"$input_common_attrs": ["_elseif_control_attr_12"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "get_embedding_from_old_redis", "mode": "count", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_elseif_control_attr_12}}", "subtag": "retrieval_list", "type_name": "CommonRecoPerflogObserver"}, "get_dup_retrieval_list::retrieval_list_cache::build_protobuf_BF753F": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "distance_list", "photo_id_list"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["retrieval_list_str"], "$output_item_attrs": []}, "as_string": true, "class_name": "kuaishou.ad.algorithm.DupPhotoIdList", "inputs": [{"append": true, "common_attr": "photo_id_list", "path": "id"}, {"append": true, "common_attr": "distance_list", "path": "score"}], "output_common_attr": "retrieval_list_str", "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_dup_retrieval_list::retrieval_list_cache::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "get_dup_retrieval_list__retrieval_list_cacheTimeCostEnd", "get_dup_retrieval_list__retrieval_list_cacheTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_list__retrieval_list_cacheTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["get_dup_retrieval_list__retrieval_list_cacheTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["get_dup_retrieval_list__retrieval_list_cacheTimeCostEnd", "get_dup_retrieval_list__retrieval_list_cacheTimeCostStart"], "lua_script": "function gen_common_attrs() return (get_dup_retrieval_list__retrieval_list_cacheTimeCostEnd - get_dup_retrieval_list__retrieval_list_cacheTimeCostStart) end", "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::retrieval_list_cache::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_list__retrieval_list_cacheTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_retrieval_list__retrieval_list_cacheTimeCostEnd", "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_retrieval_list::retrieval_list_cache::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_list__retrieval_list_cacheTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_retrieval_list__retrieval_list_cacheTimeCostStart", "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_retrieval_list::retrieval_list_cache::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "get_dup_retrieval_list__retrieval_list_cacheTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:get_dup_retrieval_list__retrieval_list_cache", "common_attrs": ["get_dup_retrieval_list__retrieval_list_cacheTimeCost"], "perf_base": 1, "skip": "{{_else_control_attr_8}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "get_dup_retrieval_list::retrieval_list_cache::write_to_redis_DAF9EE": {"$metadata": {"$input_common_attrs": ["_else_control_attr_8", "retrieval_list_cache_key", "retrieval_list_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "downstream_processor": "__none", "expire_second": 2592000, "kcc_cluster": "adDupPhotoRetrievalAndRankCache", "key": "{{retrieval_list_cache_key}}", "skip": "{{_else_control_attr_8}}", "timeout_ms": 10, "type_name": "CommonRecoWriteToRedisObserver", "value": "{{retrieval_list_str}}"}, "get_dup_retrieval_list::set_attr_value_0C35C6": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["biz", "cal_by_video_byte", "emb_key_prefix", "embedding_str_from_old_cache", "embedding_str_from_self_cache", "extra_emb_key_prefix", "index_name", "recall_num", "retrieval_list_key_prefix", "retrieval_list_str"], "$output_item_attrs": []}, "common_attrs": [{"name": "retrieval_list_key_prefix", "type": "string", "value": "n3rlkp_"}, {"name": "emb_key_prefix", "type": "string", "value": "avgemb_"}, {"name": "extra_emb_key_prefix", "type": "string", "value": "n2eavgemb_"}, {"name": "retrieval_list_str", "type": "string", "value": ""}, {"name": "embedding_str_from_old_cache", "type": "string", "value": ""}, {"name": "embedding_str_from_self_cache", "type": "string", "value": ""}, {"name": "biz", "type": "string", "value": "AD"}, {"name": "cal_by_video_byte", "type": "int", "value": 0}, {"name": "index_name", "type": "string", "value": "ann_dup_photo_new_arch"}, {"name": "recall_num", "type": "int", "value": 200}], "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoItemAttrDefaultValueEnricher"}, "get_dup_retrieval_list::set_attr_value_319983": {"$metadata": {"$input_common_attrs": ["_if_control_attr_6"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["recall_num"], "$output_item_attrs": []}, "common_attrs": [{"name": "recall_num", "type": "int", "value": 500}], "skip": "{{_if_control_attr_6}}", "type_name": "CommonRecoItemAttrDefaultValueEnricher"}, "get_dup_retrieval_list::sort_by_score_7FA9E4": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4"], "$input_item_attrs": ["score"], "$modify_item_tables": [""], "$output_common_attrs": [], "$output_item_attrs": []}, "item_table": "", "score_from_attr": "score", "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoScoreSortArranger"}, "get_dup_retrieval_list::write_embedding_cache::build_protobuf_44A262": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10", "avg_emb_normal"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["embedding_str"], "$output_item_attrs": []}, "as_string": true, "class_name": "kuaishou.ad.Rttr<PERSON>", "inputs": [{"append": true, "common_attr": "avg_emb_normal", "path": "value"}], "output_common_attr": "embedding_str", "skip": "{{_if_control_attr_10}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_dup_retrieval_list::write_embedding_cache::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10", "get_dup_retrieval_list__write_embedding_cacheTimeCostEnd", "get_dup_retrieval_list__write_embedding_cacheTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_list__write_embedding_cacheTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["get_dup_retrieval_list__write_embedding_cacheTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["get_dup_retrieval_list__write_embedding_cacheTimeCostEnd", "get_dup_retrieval_list__write_embedding_cacheTimeCostStart"], "lua_script": "function gen_common_attrs() return (get_dup_retrieval_list__write_embedding_cacheTimeCostEnd - get_dup_retrieval_list__write_embedding_cacheTimeCostStart) end", "skip": "{{_if_control_attr_10}}", "type_name": "CommonRecoLuaAttrEnricher"}, "get_dup_retrieval_list::write_embedding_cache::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_list__write_embedding_cacheTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_retrieval_list__write_embedding_cacheTimeCostEnd", "skip": "{{_if_control_attr_10}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_retrieval_list::write_embedding_cache::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_dup_retrieval_list__write_embedding_cacheTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_dup_retrieval_list__write_embedding_cacheTimeCostStart", "skip": "{{_if_control_attr_10}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_dup_retrieval_list::write_embedding_cache::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10", "get_dup_retrieval_list__write_embedding_cacheTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:get_dup_retrieval_list__write_embedding_cache", "common_attrs": ["get_dup_retrieval_list__write_embedding_cacheTimeCost"], "perf_base": 1, "skip": "{{_if_control_attr_10}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "get_dup_retrieval_list::write_embedding_cache::write_to_redis_1D56BE": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10", "embedding_str", "extra_emb_cache_key"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "downstream_processor": "__none", "kcc_cluster": "adDupPhotoRetrievalAndRankCache", "key": "{{extra_emb_cache_key}}", "skip": "{{_if_control_attr_10}}", "timeout_ms": 10, "type_name": "CommonRecoWriteToRedisObserver", "value": "{{embedding_str}}"}, "get_kconf_params_C6DF2E": {"$metadata": {"$input_common_attrs": ["_if_control_attr_21"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["frame_extract_timeout"], "$output_item_attrs": []}, "kconf_configs": [{"export_common_attr": "frame_extract_timeout", "json_path": "frame_extract_timeout", "kconf_key": "ad.materialGenerationServer.dupPhotoCommonConf", "value_type": "json"}], "skip": "{{_if_control_attr_21}}", "type_name": "CommonRecoKconfCommonAttrEnricher"}, "get_kconf_params_E87655": {"$metadata": {"$input_common_attrs": ["_if_control_attr_20"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["frame_extract_timeout"], "$output_item_attrs": []}, "kconf_configs": [{"export_common_attr": "frame_extract_timeout", "json_path": "frame_extract_timeout", "kconf_key": "ad.materialGenerationServer.dupPhotoCommonConf", "value_type": "json"}], "skip": "{{_if_control_attr_20}}", "type_name": "CommonRecoKconfCommonAttrEnricher"}, "image_resize_and_crop_enricher_36E52B": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["frame_infos"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["frame_height", "frame_infos", "frame_width"]}, "frame_height_column": "frame_height", "frame_size_column": "frame_size", "frame_width_column": "frame_width", "images_column": "frame_infos", "item_table": "", "type_name": "ImageResizeAndCropEnricher"}, "image_resize_and_crop_enricher_F7C27E": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["frame_infos"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["frame_height", "frame_infos", "frame_width"]}, "frame_height_column": "frame_height", "frame_size_column": "frame_size", "frame_width_column": "frame_width", "images_column": "frame_infos", "item_table": "query_photo_info_table", "type_name": "ImageResizeAndCropEnricher"}, "photo_frame_extract_enricher_054F75": {"$metadata": {"$input_common_attrs": ["_if_control_attr_21"], "$input_item_attrs": ["photo_height", "photo_id", "photo_width"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["frame_keys"]}, "frame_extract_timeout_column": "frame_extract_timeout", "frame_keys_column": "frame_keys", "frame_status_column": "frame_status", "item_table": "", "photo_height_column": "photo_height", "photo_id_column": "photo_id", "photo_width_column": "photo_width", "redis_cluster_name": "adDupPhotoInfoCache", "req_type": "async_and_wait", "retry_time": 0, "skip": "{{_if_control_attr_21}}", "type_name": "PhotoFrameExtractEnricher"}, "photo_frame_extract_enricher_87716D": {"$metadata": {"$input_common_attrs": ["_if_control_attr_20"], "$input_item_attrs": ["photo_height", "photo_id", "photo_width"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["frame_keys"]}, "frame_extract_timeout_column": "frame_extract_timeout", "frame_keys_column": "frame_keys", "frame_status_column": "frame_status", "item_table": "query_photo_info_table", "photo_height_column": "photo_height", "photo_id_column": "photo_id", "photo_width_column": "photo_width", "redis_cluster_name": "adDupPhotoInfoCache", "req_type": "async_and_wait", "retry_time": 0, "skip": "{{_if_control_attr_20}}", "type_name": "PhotoFrameExtractEnricher"}, "photo_frame_key_enricher_D720C3": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["photo_id"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["frame_keys", "frame_status"]}, "frame_keys_column": "frame_keys", "frame_status_column": "frame_status", "item_table": "query_photo_info_table", "key_prefix": "nn_pkf_", "photo_id_column": "photo_id", "redis_cluster_name": "adDupPhotoInfoCache", "type_name": "PhotoFrame<PERSON>ey<PERSON>nricher"}, "photo_frame_key_sample_enricher_5A9423": {"$metadata": {"$input_common_attrs": ["_if_control_attr_21"], "$input_item_attrs": ["frame_keys"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "frame_keys_column": "frame_keys", "frame_status_column": "frame_status", "item_table": "", "skip": "{{_if_control_attr_21}}", "type_name": "PhotoFrameKeySampleEnricher"}, "photo_frame_key_sample_enricher_840657": {"$metadata": {"$input_common_attrs": ["_if_control_attr_20"], "$input_item_attrs": ["frame_keys"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "frame_keys_column": "frame_keys", "frame_status_column": "frame_status", "item_table": "query_photo_info_table", "skip": "{{_if_control_attr_20}}", "type_name": "PhotoFrameKeySampleEnricher"}, "photo_frame_key_sample_enricher_E84E99": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["frame_keys"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "frame_keys_column": "frame_keys", "frame_status_column": "frame_status", "item_table": "query_photo_info_table", "type_name": "PhotoFrameKeySampleEnricher"}, "photo_rank_feature_enricher_3D018D": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["frame_height", "frame_infos", "frame_width"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["feature"]}, "feature_column": "feature", "frame_height_column": "frame_height", "frame_width_column": "frame_width", "images_column": "frame_infos", "item_table": "query_photo_info_table", "type_name": "PhotoRankFeatureEnricher"}, "photo_rank_feature_enricher_D70214": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["frame_height", "frame_infos", "frame_width"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["feature"]}, "feature_column": "feature", "frame_height_column": "frame_height", "frame_width_column": "frame_width", "images_column": "frame_infos", "item_table": "", "type_name": "PhotoRankFeatureEnricher"}, "photo_size_info_enricher_55A19C": {"$metadata": {"$input_common_attrs": ["_if_control_attr_21"], "$input_item_attrs": ["frame_keys", "photo_id"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["photo_height", "photo_width"]}, "frame_keys_column": "frame_keys", "frame_status_column": "frame_status", "item_table": "", "photo_height_column": "photo_height", "photo_id_column": "photo_id", "photo_width_column": "photo_width", "skip": "{{_if_control_attr_21}}", "type_name": "PhotoSizeInfoEnricher"}, "photo_size_info_enricher_ABE76A": {"$metadata": {"$input_common_attrs": ["_if_control_attr_20"], "$input_item_attrs": ["frame_keys", "photo_id"], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["photo_height", "photo_width"]}, "frame_keys_column": "frame_keys", "frame_status_column": "frame_status", "item_table": "query_photo_info_table", "photo_height_column": "photo_height", "photo_id_column": "photo_id", "photo_width_column": "photo_width", "skip": "{{_if_control_attr_20}}", "type_name": "PhotoSizeInfoEnricher"}, "prepare::_branch_controller_539CE0DC": {"$branch_start": "prepare::_branch_controller_539CE0DC", "$code_info": "[if] 539CE0DC ad_dup_info_query_server.py in prepare(): .if_(\"enable_use_cache ~= 0 and enable_use_cache ~= 1\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_2", "enable_use_cache"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_3"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_3"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_2", "enable_use_cache"], "lua_script": "function evaluate() if (_if_control_attr_2 == 0 and (enable_use_cache ~= 0 and enable_use_cache ~= 1)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "prepare::_branch_controller_8420ECD7": {"$branch_start": "prepare::_branch_controller_8420ECD7", "$code_info": "[if] 8420ECD7 ad_dup_info_query_server.py in prepare(): .if_(\"data_type_int == 1 or data_type_int == 2\")", "$metadata": {"$input_common_attrs": ["data_type_int"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_2"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_2"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["data_type_int"], "lua_script": "function evaluate() if (data_type_int == 1 or data_type_int == 2) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "prepare::_branch_controller_96777CE6": {"$branch_start": "prepare::_branch_controller_96777CE6", "$code_info": "[if] 96777CE6 ad_dup_info_query_server.py in prepare(): ).if_(\"data_type_int == 0\")", "$metadata": {"$input_common_attrs": ["data_type_int"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_1"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_1"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["data_type_int"], "lua_script": "function evaluate() if (data_type_int == 0) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "prepare::calc_time_cost": {"$metadata": {"$input_common_attrs": ["prepareTimeCostEnd", "prepareTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["prepareTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["prepareTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["prepareTimeCostEnd", "prepareTimeCostStart"], "lua_script": "function gen_common_attrs() return (prepareTimeCostEnd - prepareTimeCostStart) end", "type_name": "CommonRecoLuaAttrEnricher"}, "prepare::calc_time_cost_e": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["prepareTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "prepareTimeCostEnd", "type_name": "CommonRecoUserMetaInfoEnricher"}, "prepare::calc_time_cost_s": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["prepareTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "prepareTimeCostStart", "type_name": "CommonRecoUserMetaInfoEnricher"}, "prepare::enrich_attr_by_lua_7FC3F9": {"$metadata": {"$input_common_attrs": ["data_type", "enable_use_cache"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["data_type_int", "enable_use_cache_str"], "$output_item_attrs": []}, "export_common_attr": ["data_type_int", "enable_use_cache_str"], "function_for_common": "dataTypedToInt", "import_common_attr": ["data_type", "enable_use_cache"], "lua_script": "function dataTypedToInt()\n            local a = \"RetrievalList\"\n            local b = \"RankList\"\n            local c = \"DupPhotoRealtimeSimilarList\"\n            if data_type == a then\n              return 1, tostring(enable_use_cache)\n            elseif data_type == b then\n              return 2, tostring(enable_use_cache)\n            elseif data_type == c then\n              return 3, tostring(enable_use_cache)\n            else\n              return 0, tostring(enable_use_cache)\n            end\n          end", "type_name": "CommonRecoLuaAttrEnricher"}, "prepare::get_kconf_params_B1DE59": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["rank_truncate_num", "recall_truncate_num"], "$output_item_attrs": []}, "kconf_configs": [{"export_common_attr": "recall_truncate_num", "json_path": "recall_truncate_num", "kconf_key": "ad.adCreativeEngine.dupInfoQueryConfig", "value_type": "json"}, {"export_common_attr": "rank_truncate_num", "json_path": "rank_truncate_num", "kconf_key": "ad.adCreativeEngine.dupInfoQueryConfig", "value_type": "json"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "prepare::perf_time_cost": {"$metadata": {"$input_common_attrs": ["prepareTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:prepare", "common_attrs": ["prepareTimeCost"], "perf_base": 1, "type_name": "CommonRecoAttrValuePerflogObserver"}, "prepare::perflog_0F98E38A": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "enable_use_cache_error", "mode": "count", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_if_control_attr_3}}", "subtag": "admit", "type_name": "CommonRecoPerflogObserver"}, "prepare::perflog_1B8B2DA9": {"$metadata": {"$input_common_attrs": ["data_type", "enable_use_cache_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "data_type", "extra2": "{{data_type}}", "extra3": "{{enable_use_cache_str}}", "mode": "count", "namespace": "ad.ad_dup_info_query_server", "subtag": "admit", "type_name": "CommonRecoPerflogObserver"}, "prepare::perflog_A054D0E8": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "data_type_empty", "mode": "count", "namespace": "ad.ad_dup_info_query_server", "skip": "{{_if_control_attr_1}}", "subtag": "admit", "type_name": "CommonRecoPerflogObserver"}, "prepare::return__16C414": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_3}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "prepare::return__28C561": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_1}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}}, "type_name": "CommonRecoPipeline"}, "pipeline_map": {"dup_info_query_flow": {"__PARENT": "base_pipeline", "pipeline": ["prepare::calc_time_cost_s", "prepare::get_kconf_params_B1DE59", "prepare::enrich_attr_by_lua_7FC3F9", "prepare::perflog_1B8B2DA9", "prepare::_branch_controller_96777CE6", "prepare::perflog_A054D0E8", "prepare::return__28C561", "prepare::_branch_controller_8420ECD7", "prepare::_branch_controller_539CE0DC", "prepare::perflog_0F98E38A", "prepare::return__16C414", "prepare::calc_time_cost_e", "prepare::calc_time_cost", "prepare::perf_time_cost", "_branch_controller_B8B1C5B0", "get_dup_retrieval_list::calc_time_cost_s", "get_dup_retrieval_list::set_attr_value_0C35C6", "get_dup_retrieval_list::_branch_controller_B1FE6D5E", "get_dup_retrieval_list::copy_attr_63A069", "get_dup_retrieval_list::_branch_controller_E25DE4CD", "get_dup_retrieval_list::set_attr_value_319983", "get_dup_retrieval_list::enrich_attr_by_lua_1DE67B", "get_dup_retrieval_list::get_common_attr_from_redis_744C73", "get_dup_retrieval_list::_branch_controller_CC524E36", "get_dup_retrieval_list::perflog_397F60EC", "get_dup_retrieval_list::parse_protobuf_from_string_5C9B9C", "get_dup_retrieval_list::enrich_with_protobuf_A1756B", "get_dup_retrieval_list::build_table_from_common_list_attr_21836D", "get_dup_retrieval_list::_branch_controller_CC524E36_else", "get_dup_retrieval_list::perflog_8E3BCDFE", "get_dup_retrieval_list::get_common_attr_from_redis_147008", "get_dup_retrieval_list::get_common_attr_from_redis_BA754A", "get_dup_retrieval_list::_branch_controller_D8FE9BC5", "get_dup_retrieval_list::lookup_kconf_684677", "get_dup_retrieval_list::_branch_controller_60F69F55", "get_dup_retrieval_list::perflog_A70048E9", "get_dup_retrieval_list::get_embedding_from_rpc::calc_time_cost_s", "get_dup_retrieval_list::get_embedding_from_rpc::build_protobuf_7ECDC5", "get_dup_retrieval_list::get_embedding_from_rpc::enrich_by_generic_grpc_F550B6", "get_dup_retrieval_list::get_embedding_from_rpc::enrich_with_protobuf_657EE1", "get_dup_retrieval_list::get_embedding_from_rpc::enrich_attr_by_lua_69AE09", "get_dup_retrieval_list::get_embedding_from_rpc::embedding_normalize_enricher_7DDF6F", "get_dup_retrieval_list::get_embedding_from_rpc::enrich_attr_by_lua_0A0819", "get_dup_retrieval_list::get_embedding_from_rpc::calc_time_cost_e", "get_dup_retrieval_list::get_embedding_from_rpc::calc_time_cost", "get_dup_retrieval_list::get_embedding_from_rpc::perf_time_cost", "get_dup_retrieval_list::write_embedding_cache::calc_time_cost_s", "get_dup_retrieval_list::write_embedding_cache::build_protobuf_44A262", "get_dup_retrieval_list::write_embedding_cache::write_to_redis_1D56BE", "get_dup_retrieval_list::write_embedding_cache::calc_time_cost_e", "get_dup_retrieval_list::write_embedding_cache::calc_time_cost", "get_dup_retrieval_list::write_embedding_cache::perf_time_cost", "get_dup_retrieval_list::_branch_controller_60F69F55_else", "get_dup_retrieval_list::perflog_345C6A12", "get_dup_retrieval_list::_branch_controller_4A40A029", "get_dup_retrieval_list::perflog_F238D975", "get_dup_retrieval_list::parse_protobuf_from_string_79042D", "get_dup_retrieval_list::enrich_with_protobuf_8822DC", "get_dup_retrieval_list::embedding_normalize_enricher_2903BE", "get_dup_retrieval_list::_branch_controller_D8FE9BC5_else", "get_dup_retrieval_list::perflog_119BB1C6", "get_dup_retrieval_list::parse_protobuf_from_string_0D1866", "get_dup_retrieval_list::enrich_with_protobuf_573742", "get_dup_retrieval_list::embedding_normalize_enricher_04E9DF", "get_dup_retrieval_list::get_retrieval_list_from_rpc::calc_time_cost_s", "get_dup_retrieval_list::get_retrieval_list_from_rpc::set_attr_value_E975CA", "get_dup_retrieval_list::get_retrieval_list_from_rpc::build_protobuf_3BDCBB", "get_dup_retrieval_list::get_retrieval_list_from_rpc::build_protobuf_D474B7", "get_dup_retrieval_list::get_retrieval_list_from_rpc::build_protobuf_504A30", "get_dup_retrieval_list::get_retrieval_list_from_rpc::build_protobuf_03BA6C", "get_dup_retrieval_list::get_retrieval_list_from_rpc::build_protobuf_04830B", "get_dup_retrieval_list::get_retrieval_list_from_rpc::enrich_by_generic_grpc_74F088", "get_dup_retrieval_list::get_retrieval_list_from_rpc::enrich_with_protobuf_4A066E", "get_dup_retrieval_list::get_retrieval_list_from_rpc::build_table_from_common_list_attr_70204B", "get_dup_retrieval_list::get_retrieval_list_from_rpc::calc_time_cost_e", "get_dup_retrieval_list::get_retrieval_list_from_rpc::calc_time_cost", "get_dup_retrieval_list::get_retrieval_list_from_rpc::perf_time_cost", "get_dup_retrieval_list::retrieval_list_cache::calc_time_cost_s", "get_dup_retrieval_list::retrieval_list_cache::build_protobuf_BF753F", "get_dup_retrieval_list::retrieval_list_cache::write_to_redis_DAF9EE", "get_dup_retrieval_list::retrieval_list_cache::calc_time_cost_e", "get_dup_retrieval_list::retrieval_list_cache::calc_time_cost", "get_dup_retrieval_list::retrieval_list_cache::perf_time_cost", "get_dup_retrieval_list::batch_svalue_get_from_redis_enricher_1E4B71", "get_dup_retrieval_list::enrich_attr_by_lua_B311F6", "get_dup_retrieval_list::filter_by_attr_450371", "get_dup_retrieval_list::filter_by_attr_E5783F", "get_dup_retrieval_list::sort_by_score_7FA9E4", "get_dup_retrieval_list::count_reco_result_2289A2", "get_dup_retrieval_list::perflog_85937C5A", "get_dup_retrieval_list::pack_item_attr_170164", "get_dup_retrieval_list::log_debug_info_EBF98C", "get_dup_retrieval_list::calc_time_cost_e", "get_dup_retrieval_list::calc_time_cost", "get_dup_retrieval_list::perf_time_cost", "_branch_controller_840F3A8C", "get_dup_rank_list::calc_time_cost_s", "get_dup_rank_list::set_attr_value_15FE61", "get_dup_rank_list::enrich_attr_by_lua_D7DD78", "get_dup_rank_list::get_common_attr_from_redis_2ACE76", "get_dup_rank_list::_branch_controller_49DD7909", "get_dup_rank_list::perflog_79D85911", "get_dup_rank_list::return__0E1FE6", "get_dup_rank_list::_branch_controller_052B7973", "get_dup_rank_list::perflog_968130C6", "get_dup_rank_list::parse_protobuf_from_string_AB339C", "get_dup_rank_list::enrich_with_protobuf_E1963F", "get_dup_rank_list::build_table_from_common_list_attr_CEC685", "get_dup_rank_list::_branch_controller_49DD7909_else", "get_dup_rank_list::perflog_3562033D", "get_dup_rank_list::enrich_attr_by_lua_DB8CC3", "get_dup_rank_list::enrich_attr_by_lua_6E7C25", "get_dup_rank_list::build_table_from_common_list_attr_701354", "get_dup_rank_list::batch_svalue_get_from_redis_enricher_117336", "get_dup_rank_list::enrich_attr_by_lua_96B4C0", "get_dup_rank_list::filter_by_attr_E220C7", "get_dup_rank_list::sort_by_score_00AF7D", "get_dup_rank_list::count_reco_result_D8121F", "get_dup_rank_list::perflog_1C973F5F", "get_dup_rank_list::pack_item_attr_EB7BDF", "get_dup_rank_list::log_debug_info_1DEA69", "get_dup_rank_list::calc_time_cost_e", "get_dup_rank_list::calc_time_cost", "get_dup_rank_list::perf_time_cost", "_branch_controller_ED7FB7D3", "get_dup_photo_realtime_similar_list::calc_time_cost_s", "get_dup_photo_realtime_similar_list::set_attr_value_BC0278", "get_dup_photo_realtime_similar_list::enrich_attr_by_lua_03DDE8", "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::calc_time_cost_s", "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::build_protobuf_1B558A", "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::enrich_by_generic_grpc_1644C6", "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::enrich_with_protobuf_8618E7", "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::enrich_attr_by_lua_F10FAF", "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::embedding_normalize_enricher_894DC7", "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::enrich_attr_by_lua_5E9F7B", "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::calc_time_cost_e", "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::calc_time_cost", "get_dup_photo_realtime_similar_list::get_embedding_from_rpc::perf_time_cost", "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::calc_time_cost_s", "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::set_attr_value_CF5AA8", "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::build_protobuf_B64067", "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::build_protobuf_872C8C", "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::build_protobuf_BFD938", "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::build_protobuf_D5ADBE", "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::build_protobuf_A3F6FC", "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::enrich_by_generic_grpc_B4BCDE", "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::enrich_with_protobuf_97C9BE", "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::build_table_from_common_list_attr_4A937E", "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::calc_time_cost_e", "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::calc_time_cost", "get_dup_photo_realtime_similar_list::get_retrieval_list_from_rpc::perf_time_cost", "get_dup_photo_realtime_similar_list::retrieval_photo_filter::calc_time_cost_s", "get_dup_photo_realtime_similar_list::retrieval_photo_filter::batch_svalue_get_from_redis_enricher_5EA822", "get_dup_photo_realtime_similar_list::retrieval_photo_filter::filter_by_attr_685CE0", "get_dup_photo_realtime_similar_list::retrieval_photo_filter::filter_by_attr_2BA713", "get_dup_photo_realtime_similar_list::retrieval_photo_filter::sort_by_score_570F89", "get_dup_photo_realtime_similar_list::retrieval_photo_filter::deduplicate_C2A551", "get_dup_photo_realtime_similar_list::retrieval_photo_filter::count_reco_result_4F23DD", "get_dup_photo_realtime_similar_list::retrieval_photo_filter::perflog_D71528D6", "get_dup_photo_realtime_similar_list::retrieval_photo_filter::truncate_46957C", "get_dup_photo_realtime_similar_list::retrieval_photo_filter::calc_time_cost_e", "get_dup_photo_realtime_similar_list::retrieval_photo_filter::calc_time_cost", "get_dup_photo_realtime_similar_list::retrieval_photo_filter::perf_time_cost", "get_dup_photo_realtime_similar_list::build_query_photo_table::calc_time_cost_s", "get_dup_photo_realtime_similar_list::build_query_photo_table::enrich_attr_by_lua_6955A6", "get_dup_photo_realtime_similar_list::build_query_photo_table::build_table_from_common_list_attr_1F595F", "get_dup_photo_realtime_similar_list::build_query_photo_table::calc_time_cost_e", "get_dup_photo_realtime_similar_list::build_query_photo_table::calc_time_cost", "get_dup_photo_realtime_similar_list::build_query_photo_table::perf_time_cost", "get_dup_photo_realtime_similar_list::enrich_by_sub_flow_1B2E7F", "get_dup_photo_realtime_similar_list::photo_frame_key_enricher_5EE206", "get_dup_photo_realtime_similar_list::photo_frame_key_sample_enricher_6FD6CB", "get_dup_photo_realtime_similar_list::arrange_by_sub_flow_80FBB9", "get_dup_photo_realtime_similar_list::copy_attr_21A02F", "get_dup_photo_realtime_similar_list::count_reco_result_66FE6A", "get_dup_photo_realtime_similar_list::count_reco_result_318B60", "get_dup_photo_realtime_similar_list::_branch_controller_04D22478", "get_dup_photo_realtime_similar_list::perflog_22FB0E69", "get_dup_photo_realtime_similar_list::return__73B321", "get_dup_photo_realtime_similar_list::photo_rank_sim_info_enricher_8B47BA", "get_dup_photo_realtime_similar_list::photo_rank_sim_score_calc_enricher_B708C3", "get_dup_photo_realtime_similar_list::sort_by_score_6B9A50", "get_dup_photo_realtime_similar_list::count_reco_result_98B483", "get_dup_photo_realtime_similar_list::perflog_BA399402", "get_dup_photo_realtime_similar_list::log_debug_info_BB27A5", "get_dup_photo_realtime_similar_list::log_debug_info_29C567", "get_dup_photo_realtime_similar_list::calc_time_cost_e", "get_dup_photo_realtime_similar_list::calc_time_cost", "get_dup_photo_realtime_similar_list::perf_time_cost"]}, "query_photo_feature_get": {"__PARENT": "base_pipeline", "item_table": "query_photo_info_table", "pipeline": ["photo_frame_key_enricher_D720C3", "photo_frame_key_sample_enricher_E84E99", "copy_attr_49B4B0", "_branch_controller_67F8EDDC", "photo_size_info_enricher_ABE76A", "get_kconf_params_E87655", "photo_frame_extract_enricher_87716D", "photo_frame_key_sample_enricher_840657", "blobstore_download_enricher_D5D2AC", "image_resize_and_crop_enricher_F7C27E", "photo_rank_feature_enricher_3D018D", "filter_by_attr_C00341"]}, "target_photo_frame_get": {"__PARENT": "base_pipeline", "pipeline": ["copy_attr_BFB8CD", "_branch_controller_67F8EDDC_1", "photo_size_info_enricher_55A19C", "get_kconf_params_C6DF2E", "photo_frame_extract_enricher_054F75", "photo_frame_key_sample_enricher_5A9423", "blobstore_download_enricher_E2A344", "image_resize_and_crop_enricher_36E52B", "photo_rank_feature_enricher_D70214", "filter_by_attr_BEBD4D"]}}}, "request_type_config": {"default": ["dup_info_query_flow"]}}