#!/usr/bin/env python3
# coding=utf-8

import os
import sys
dragon_path=os.path.abspath('../../../../../../dragon/')
ad_dragon_path=os.path.abspath('../../../../ad_dragon/')
sys.path.append(dragon_path)
sys.path.append(ad_dragon_path)

from dragonfly.common_leaf_dsl import LeafFlow, OfflineRunner, LeafService, IndexSource, current_flow
from ad_dragonfly.mechanism.ad_bid_service.ad_bid_service_mixin import AdBidServiceApiMixin
from ks_leaf_bootstrap.utils import gen_service_graph_def
from ks_leaf_functional.core.module import module
from dragon_server.creative.constants import *
from dragon_server.creative.common import AdCreativeCommonApiMixin
from ks_leaf_functional.core.data_manager import data_manager, ab_param as ab, kconf_param as kconf

#Constant definition
retrieval_list_key_prefix = 'n3rlkp_'
rank_list_key_prefix = 'ralkp_'
batch_rank_list_key_prefix = 'cralkp_'
cache_cluster = 'adDupPhotoRetrievalAndRankCache'
emb_cache_cluster = 'adDupPhotoInfoCache'
emb_key_prefix_error = ''    # 为了测试
emb_key_prefix = 'avgemb_'
extra_emb_key_prefix = 'n2eavgemb_'  # 本服务请求emb rpc服务得到的结果 单独存储 避免污染主流程
ns = "ad.ad_dup_info_query_server"

query_photo_table = "query_photo_info_table"
target_photo_table = ""
# Flow definition
class AdDupInfoQueryService(LeafFlow, AdCreativeCommonApiMixin):
  def __init__(self, name, item_table):
    LeafFlow.__init__(self, name=name, item_table=item_table)

  @module()
  def prepare(self):
    """
      -功能:
      -执行流程:
    """
    self.get_kconf_params(   # 取kconf配置数据
        kconf_configs=[
          dict(kconf_key="ad.adCreativeEngine.dupInfoQueryConfig",
            value_type="json",
            json_path="recall_truncate_num",
            export_common_attr="recall_truncate_num"	# 召回截断数
          ),
          dict(kconf_key="ad.adCreativeEngine.dupInfoQueryConfig",
            value_type="json",
            json_path="rank_truncate_num",
            export_common_attr="rank_truncate_num"  # 精排截断数
          ),
        ]
      ).enrich_attr_by_lua(  # method string转为 int
        import_common_attr = ["data_type", "enable_use_cache"],
        function_for_common = "dataTypedToInt",
        export_common_attr = ["data_type_int", "enable_use_cache_str"],
        lua_script = """
          function dataTypedToInt()
            local a = "RetrievalList"
            local b = "RankList"
            local c = "DupPhotoRealtimeSimilarList"
            if data_type == a then
              return 1, tostring(enable_use_cache)
            elseif data_type == b then
              return 2, tostring(enable_use_cache)
            elseif data_type == c then
              return 3, tostring(enable_use_cache)
            else
              return 0, tostring(enable_use_cache)
            end
          end
        """
      ).perflog(
          mode = "count",
          namespace = ns,
          subtag = "admit",
          extra1 = "data_type",
          extra2 = "{{data_type}}",
          extra3 = "{{enable_use_cache_str}}"
      ).if_("data_type_int == 0") \
        .perflog(
          mode = "count",
          namespace = ns,
          subtag = "admit",
          extra1 = "data_type_empty"
        ).return_() \
      .end_if_() \
      .if_("data_type_int == 1 or data_type_int == 2") \
        .if_("enable_use_cache ~= 0 and enable_use_cache ~= 1") \
          .perflog(
            mode = "count",
            namespace = ns,
            subtag = "admit",
            extra1 = "enable_use_cache_error"
          ).return_() \
        .end_if_() \
      .end_if_()
    return self

  @module()
  def get_embedding_from_rpc(self):
    '''
      请求mmu rpc接口 得到 avg_emb_normal
    '''
    self.build_protobuf( # 获取emb缓存失败时 需要请求mmu获取emb 构造生产md5请求
        class_name="mmu::video::DlFeatureRequest",
        inputs=[
          { "common_attr": "photo_id_str", "path": "id" },
          { "common_attr": "cal_by_video_byte", "path": "cal_by_video_byte" },
          { "common_attr": "biz", "path": "source_from"},
        ],
        output_common_attr= "embedding_req",
      ).enrich_by_generic_grpc(		# 请求 md5生成服务
        kess_service="grpc_mmuDlFeatureCalculateService",
        timeout_ms= 10000,
        method_name="/mmu.video.DlFeatureCalculateService/CalculateFeature",
        request_attr= "embedding_req",
        response_attr= "embedding_resp",
        response_class="mmu::video::DlFeatureResponse"
      ).enrich_with_protobuf(   # 获取字段
        from_extra_var = "embedding_resp",
        attrs = [
          dict(name="embedding_list", path="feature.features.value.feature")
        ]
      ).enrich_attr_by_lua(   # emb 校验 1 size<=1280 2 size%128=0 3 求avg 
        import_common_attr = ["embedding_list"],
        no_check = True,
        function_for_common = "embCheckAndAvg",
        export_common_attr = [ "is_emb_valid", "avg_emb", "emb_num"],
        lua_script = """
          function embCheckAndAvg()
            local len = #embedding_list
            local emb_num = len / 128
            local avg_emb = {}
            for i = 1, 128 do
              avg_emb[i] = 0.0
            end
            if len % 128 ~= 0 or len > 1280 then
              return 0, avg_emb, math.floor(emb_num)
            else
              for i = 1, len do
                local idx  = i - 1
                avg_emb[idx % 128 + 1] = avg_emb[idx % 128 + 1] + embedding_list[i]
              end
              for i = 1, #avg_emb do
                avg_emb[i] = avg_emb[i] / emb_num
              end
              return 1, avg_emb, math.floor(emb_num)
            end
          end
        """
      ).embedding_normalize_enricher(   # 向量归一化
        is_common_attr = True,
        photo_embedding_column = "avg_emb",
        photo_normal_embedding_column = "avg_emb_normal"
      ).enrich_attr_by_lua(   # emb 正确性校验  TODO(wanglei10): 写缓存
        import_common_attr = ["avg_emb_normal"],
        no_check = True,
        function_for_common = "innerProduct",
        export_common_attr = [ "self_inner_product"],
        lua_script = """
          function innerProduct()
            local val = 0.0
            for i = 1, #avg_emb_normal do
              val = val + avg_emb_normal[i] * avg_emb_normal[i]
            end
            return val
          end
        """
      )
    return self

  @module()
  def get_retrieval_list_from_rpc(self):
    self.set_attr_value(
        common_attrs=[
          {"name": "dimension", "type": "int", "value": 128},
          {"name": "field_name", "type": "string", "value": "test"},
        ]
		  ).build_protobuf(  # 构造 AnnRequest.EmbeddingRequest.QueryVec
        class_name="kuaishou::ad::UserEmbeddingPicassoSourceInfo",
        inputs=[
          { "common_attr": "field_name", "path": "field"}
        ],
        output_common_attr= "user_embedding_source",
      ).build_protobuf(  # 构造 AnnRequest.EmbeddingRequest.QueryVec
        class_name="kuaishou::ad::RttrEmbedding",
        inputs=[
          { "common_attr": "avg_emb_normal", "path": "value", "append": True }
        ],
        output_common_attr= "embedding",
      ).build_protobuf(  # 构造 AnnRequest.EmbeddingRequest
        item_table = "new_clip_info_table",
        class_name="kuaishou::ad::EmbeddingRequest",
        inputs=[
          { "common_attr": "recall_num", "path": "search_num" },
          { "common_attr": "embedding", "path": "query_vec", "append": True },
          { "common_attr": "index_name", "path": "index_name"},
          { "common_attr": "dimension", "path": "dimension"},
          { "common_attr": "user_embedding_source", "path": "user_embedding_source", "append": True },
        ],
        output_common_attr= "embedding_request",
      ).build_protobuf(   # 构造 AdUserInfo
        class_name="kuaishou::ad::AdUserInfo",
        inputs=[
          { "common_attr": "photo_id", "path": "id" },
        ],
        output_common_attr="user_info",
      ).build_protobuf(   # 构造 AnnRequest
        class_name="kuaishou::ad::AnnRequest",
        inputs=[
          { "common_attr": "user_info", "path": "user_info" },
          { "common_attr": "embedding_request", "path": "embedding_request", "append": True }
        ],
        output_common_attr="ann_request",
      ).enrich_by_generic_grpc(		# 请求 ann 服务
        kess_service= "grpc_adTagRetrieveServer_offline",
        timeout_ms= 1000,
        method_name="/kuaishou.ad.AdEmbeddingRetrGrpcService/Search",
        request_attr= "ann_request",
        response_attr= "ann_response",
        response_class="kuaishou.ad.AnnResponse"
      ).enrich_with_protobuf(
        from_extra_var = "ann_response",
        attrs = [
          dict(name="distance_list", path="embedding_result.result_item.distance"),
          dict(name="photo_id_list", path="embedding_result.result_item.embedding_item_id"),
        ]
      ).build_table_from_common_list_attr(
        new_table = "",
        build_config = [  # 第一行默认为主键
          dict(src = "photo_id_list", dest = "photo_id"),
          dict(src = "distance_list", dest = "score")
        ]
      )
    return self

  @module()
  def retrieval_list_cache(self):
    self.build_protobuf(
      no_check = True,
      class_name="kuaishou::ad::algorithm::DupPhotoIdList",
        inputs=[
          { "common_attr": "photo_id_list", "path": "id", "append": True},
          { "common_attr": "distance_list", "path": "score", "append": True},
        ],
        as_string = True,
        output_common_attr= "retrieval_list_str",
      ).write_to_redis(
        kcc_cluster= cache_cluster,
        timeout_ms=10,
        key= "{{retrieval_list_cache_key}}",
        value= "{{retrieval_list_str}}",
        expire_second = 86400 * 30
      )
    return self
  
  @module()
  def write_embedding_cache(self):
    self.build_protobuf(
      no_check = True,
      class_name="kuaishou::ad::RttrEmbedding",
        inputs=[
          { "common_attr": "avg_emb_normal", "path": "value", "append": True},
        ],
        as_string = True,
        output_common_attr= "embedding_str",
      ).write_to_redis(
        kcc_cluster= cache_cluster,
        timeout_ms=10,
        key= "{{extra_emb_cache_key}}",
        value= "{{embedding_str}}"
      )
    return self
  

  @module()
  def get_dup_retrieval_list(self):
    self.set_attr_value(   # 设置 retrieval prefix 到 commonattr
			common_attrs=[
        {"name": "retrieval_list_key_prefix", "type": "string", "value": retrieval_list_key_prefix},
        {"name": "emb_key_prefix", "type": "string", "value": emb_key_prefix},
        {"name": "extra_emb_key_prefix", "type": "string", "value": extra_emb_key_prefix},
        {"name": "retrieval_list_str", "type": "string", "value": ""},
        {"name": "embedding_str_from_old_cache", "type": "string", "value": ""},
        {"name": "embedding_str_from_self_cache", "type": "string", "value": ""},
        {"name": "biz", "type": "string", "value": "AD"},
        {"name": "cal_by_video_byte", "type": "int", "value": 0},
        {"name": "index_name", "type": "string", "value": "ann_dup_photo_new_arch"},
        {"name": "recall_num", "type": "int", "value": 200},
      ]
		).if_("_REQ_NUM_ > 200") \
      .copy_attr(
        attrs=[
          {
            "from_common": "_REQ_NUM_",
            "to_common": "recall_num"
          }
        ]
      ) \
    .end_if_() \
    .if_("recall_num >= 500") \
      .set_attr_value(
        common_attrs=[
          {"name": "recall_num", "type": "int", "value": 500},
        ]
      ) \
    .end_if_() \
    .enrich_attr_by_lua(  # 构造 retrieval_list key
      import_common_attr = ["photo_id", "retrieval_list_key_prefix", "emb_key_prefix", "extra_emb_key_prefix"],
      function_for_common = "getRetrievalListCacheKey",
      export_common_attr = ["retrieval_list_cache_key", "emb_cache_key", "extra_emb_cache_key", "photo_id_str"],
      lua_script = """
        function getRetrievalListCacheKey()
          return retrieval_list_key_prefix..tostring(photo_id), emb_key_prefix..tostring(photo_id), extra_emb_key_prefix..tostring(photo_id), tostring(photo_id)
        end
      """
    ).get_common_attr_from_redis(   # 获取召回列表缓存
      cluster_name = cache_cluster,
      redis_params = [
        { "redis_key": "{{retrieval_list_cache_key}}", "redis_value_type": "string", "output_attr_name": "retrieval_list_str", "output_attr_type": "string"}
      ]
    ).if_("#retrieval_list_str ~= 0 and enable_use_cache == 1") \
      .perflog(
          mode = "count",
          namespace = ns,
          subtag = "retrieval_list",
          extra1 = "get_retrieval_list_from_redis"
      ).parse_protobuf_from_string(   # 解析pb
        input_attr="retrieval_list_str",
        output_attr="retrieval_list_pb",
        class_name="kuaishou.ad.algorithm.DupPhotoIdList",
      ).enrich_with_protobuf(   # 获取photoid
        from_extra_var = "retrieval_list_pb",
        attrs = [
          dict(name="retrieval_photo_ids", path="id"),
          dict(name="retrieval_scores", path="score"),
        ]
      ).build_table_from_common_list_attr(
        new_table = "",
        build_config = [  # 第一行默认为主键
          dict(src = "retrieval_photo_ids", dest = "photo_id"),
          dict(src = "retrieval_scores", dest = "score")
        ]
      ) \
     .else_() \
      .perflog(
        mode = "count",
        namespace = ns,
        subtag = "retrieval_list",
        extra1 = "get_retrieval_list_from_ann"
      ).get_common_attr_from_redis(   # 获取emb缓存
        cluster_name = cache_cluster,
        redis_params = [
          { "redis_key": "{{extra_emb_cache_key}}", "redis_value_type": "string", "output_attr_name": "embedding_str_from_self_cache", "output_attr_type": "string"}
        ]
      ).get_common_attr_from_redis(   # 获取emb缓存
        cluster_name = emb_cache_cluster,
        redis_params = [
          { "redis_key": "{{emb_cache_key}}", "redis_value_type": "string", "output_attr_name": "embedding_str_from_old_cache", "output_attr_type": "string"},
        ]
      ).if_("#embedding_str_from_old_cache == 0 and #embedding_str_from_self_cache == 0") \
        .lookup_kconf(
          kconf_configs = [{
            "kconf_key": "ad.materialGenerationServer.dupInfoQueryRealtimeEmbRatio",
            "value_type": "tail_number",
            "lookup_attr": "photo_id",
            "output_attr": "enable_realtime_get",
          }]
        ).if_("enable_realtime_get == 1") \
          .perflog(
            mode = "count",
            namespace = ns,
            subtag = "retrieval_list",
            extra1 = "get_embedding_from_rpc"
          ).get_embedding_from_rpc() \
          .write_embedding_cache() \
          .else_() \
            .perflog(
              mode = "count",
              namespace = ns,
              subtag = "retrieval_list",
              extra1 = "skip_embedding_from_rpc"
            ) \
          .end_if_() \
       .else_if_("#embedding_str_from_old_cache ~= 0") \
        .perflog(
            mode = "count",
            namespace = ns,
            subtag = "retrieval_list",
            extra1 = "get_embedding_from_old_redis",
        ).parse_protobuf_from_string(   # 解析pb
          input_attr="embedding_str_from_old_cache",
          output_attr="embedding_proto",
          class_name="kuaishou.ad.RttrEmbedding",
        ).enrich_with_protobuf(   # 获取字段
          from_extra_var = "embedding_proto",
          attrs = [
            dict(name="avg_emb", path="value"),
          ]
        ).embedding_normalize_enricher(   # 向量归一化
          is_common_attr = True,
          photo_embedding_column = "avg_emb",
          photo_normal_embedding_column = "avg_emb_normal"
        ) \
       .else_() \
        .perflog(
            mode = "count",
            namespace = ns,
            subtag = "retrieval_list",
            extra1 = "get_embedding_from_self_redis",
        ).parse_protobuf_from_string(   # 解析pb
          input_attr="embedding_str_from_self_cache",
          output_attr="embedding_proto",
          class_name="kuaishou.ad.RttrEmbedding",
        ).enrich_with_protobuf(   # 获取字段
          from_extra_var = "embedding_proto",
          attrs = [
            dict(name="avg_emb", path="value"),
          ]
        ).embedding_normalize_enricher(   # 向量归一化
          is_common_attr = True,
          photo_embedding_column = "avg_emb",
          photo_normal_embedding_column = "avg_emb_normal"
        ) \
       .end_if_() \
       .get_retrieval_list_from_rpc() \
       .retrieval_list_cache() \
      .end_if_() \
      .batch_svalue_get_from_redis_enricher(   # 取 photo 创建时间
        item_table = "",
        input_data_type = "int",
        output_data_type = "int",
        key_prefix = "n4ew_pdpc_",
        key_column = "photo_id",
        value_column = "create_time",
        redis_cluster_name = emb_cache_cluster
      ).enrich_attr_by_lua(  # 获取当前时间戳
        import_common_attr = ["valid_duration"],
        function_for_common = "getTimeStamp",
        export_common_attr = ["time_stamp_threshold"],
        lua_script = """
          function getTimeStamp()
            return util.GetTimestamp() // 1000 - valid_duration * 86400000
          end
        """
      ).filter_by_attr(   # 创建时间过滤
        item_table = "",
        attr_name="create_time",
        remove_if="<",
        compare_to= "{{time_stamp_threshold}}",
        remove_if_attr_missing=True,
      ).filter_by_attr(   # 将 id 等于 query photo id 的 photo 过滤掉
        item_table = "",
        attr_name="photo_id",
        remove_if="==",
        compare_to="{{photo_id}}",
        remove_if_attr_missing=True,
      ).sort(
        item_table = "",
        score_from_attr= "score"
      ).count_reco_result( # 统计 item数
        item_table = "",
        save_count_to = "retrieval_list_size"
      ).perflog(
        mode = "interval",
        value = "{{retrieval_list_size}}",
        namespace = ns,
        subtag = "retrieval_list_size"
      ).pack_item_attr(   # item_id 和 score 转 commonAttr
        item_table = "",
        item_source = {
          "reco_results": True
        },
        mappings = [
          {
            "from_item_attr": "photo_id",
            "to_common_attr": "retrieval_photo_ids",
          },
          {
            "from_item_attr": "score",
            "to_common_attr": "retrieval_scores",
          }
        ]
      ).debug_log(
        item_table = "",
        item_attrs = ["photo_id", "score", "create_time"],
        common_attrs = ["data_type", "enable_use_cache", "recall_num", "retrieval_photo_ids", "retrieval_scores", "avg_emb_normal", "emb_cache_key", "embedding_str_from_old_cache"]
      )
    return self

  @module()
  def get_dup_rank_list(self):
    self.set_attr_value(   # 设置 rank prefix 到 commonattr
			common_attrs=[
        {"name": "rank_list_key_prefix", "type": "string", "value": rank_list_key_prefix},
        {"name": "rank_list_info_str", "type": "string", "value": ""},
        {"name": "batch_rank_list_key_prefix", "type": "string", "value": batch_rank_list_key_prefix},
        {"name": "batch_rank_list_info_str", "type": "string", "value": ""},
      ]
		).enrich_attr_by_lua(  # 构造 rank_list key
      import_common_attr = ["photo_id", "rank_list_key_prefix", "batch_rank_list_key_prefix"],
      function_for_common = "getRankListCacheKey",
      export_common_attr = ["rank_list_cache_key", "batch_rank_list_cache_key"],
      lua_script = """
        function getRankListCacheKey()
          return rank_list_key_prefix..tostring(photo_id), batch_rank_list_key_prefix..tostring(photo_id) 
        end
      """
    ).get_common_attr_from_redis(   # 获取召回列表缓存
      cluster_name = cache_cluster,
      redis_params = [
        { "redis_key": "{{rank_list_cache_key}}", "redis_value_type": "string", "output_attr_name": "rank_list_info_str", "output_attr_type": "string"},
        { "redis_key": "{{batch_rank_list_cache_key}}", "redis_value_type": "string", "output_attr_name": "batch_rank_list_info_str", "output_attr_type": "string"},
      ]
    ).if_("#rank_list_info_str == 0 and batch_rank_list_info_str == 0") \
      .perflog(
        mode = "count",
        namespace = ns,
        subtag = "rank_list",
        extra1 = "get_rank_list_fail"
      ).return_() \
     .else_if_("#rank_list_info_str ~= 0") \
      .perflog(
          mode = "count",
          namespace = ns,
          subtag = "rank_list",
          extra1 = "get_rank_list_from_inc_data"
      ).parse_protobuf_from_string(   # 解析pb
        input_attr="rank_list_info_str",
        output_attr="rank_list_pb",
        class_name="kuaishou.ad.algorithm.DupPhotoIdList",
      ).enrich_with_protobuf(   # 获取photoid
        from_extra_var = "rank_list_pb",
        attrs = [
          dict(name="rank_photo_ids", path="id"),
          dict(name="rank_scores", path="score"),
        ]
      ).build_table_from_common_list_attr(
        new_table = "",
        build_config = [  # 第一行默认为主键
          dict(src = "rank_photo_ids", dest = "photo_id"),
          dict(src = "rank_scores", dest = "score")
        ]
      ) \
     .else_() \
      .perflog(
        mode = "count",
        namespace = ns,
        subtag = "rank_list",
        extra1 = "get_rank_list_from_batch_data"
      ).enrich_attr_by_lua(
        import_common_attr = ["batch_rank_list_info_str"],
        function_for_common = "parseRankListInfo",
        export_common_attr = ["rank_info_list"],
        lua_script = """
          function parseRankListInfo()
            reps = ","
            local rank_info_list = {}
            local i = 1
            string.gsub(batch_rank_list_info_str,'[^'..reps..']+',function (w)
              rank_info_list[i] = w
              i = i + 1
            end)
            return rank_info_list
          end
        """
      ).enrich_attr_by_lua(
        import_common_attr = ["rank_info_list"],
        function_for_common = "parseRankListInfo",
        export_common_attr = ["rank_photo_ids", "rank_scores"],
        lua_script = """
          function parseRankListInfo()
            reps = ":"
            local rank_photo_ids = {}
            local rank_scores = {}
            local j = 1
            for i = 1, #rank_info_list do
              string.gsub(rank_info_list[i],'[^'..reps..']+',function (w)
                if j % 2 == 1 then
                  rank_photo_ids[i] = tonumber(w)
                else 
                  rank_scores[i] = tonumber(w)
                end
                j = j + 1
                end)
            end
            return rank_photo_ids, rank_scores
          end
        """
      ).build_table_from_common_list_attr(
        new_table = "",
        build_config = [  # 第一行默认为主键
          dict(src = "rank_photo_ids", dest = "photo_id"),
          dict(src = "rank_scores", dest = "score")
        ]
      ) \
      .end_if_() \
      .batch_svalue_get_from_redis_enricher(   # 取 photo 创建时间
        item_table = "",
        input_data_type = "int",
        output_data_type = "int",
        key_prefix = "n4ew_pdpc_",
        key_column = "photo_id",
        value_column = "create_time",
        redis_cluster_name = emb_cache_cluster
      ).enrich_attr_by_lua(  # 获取当前时间戳
        import_common_attr = ["valid_duration"],
        function_for_common = "getTimeStamp",
        export_common_attr = ["time_stamp_threshold_extra"],
        lua_script = """
          function getTimeStamp()
          return util.GetTimestamp() // 1000 - valid_duration * 86400000
          end
        """
      ).filter_by_attr(   # 创建时间过滤
        item_table = "",
        attr_name="create_time",
        remove_if="<",
        compare_to= "{{time_stamp_threshold_extra}}",
        remove_if_attr_missing=True,
      ).sort(
        item_table = "",
        score_from_attr= "score"
      ).count_reco_result( # 统计 item数
        item_table = "",
        save_count_to = "rank_list_size"
      ).perflog(
        mode = "interval",
        value = "{{rank_list_size}}",
        namespace = ns,
        subtag = "rank_list_size"
      ).pack_item_attr(   # item_id 和 score 转 commonAttr
        item_table = "",
        item_source = {
          "reco_results": True
        },
        mappings = [
          {
            "from_item_attr": "photo_id",
            "to_common_attr": "rank_photo_ids",
          },
          {
            "from_item_attr": "score",
            "to_common_attr": "rank_scores",
          }
        ]
      ).debug_log(
        item_table = "",
        item_attrs = ["photo_id", "score", "create_time"],
        common_attrs = ["time_stamp_threshold_extra", "data_type", "photo_id", "enable_use_cache", "valid_duration", "rank_list_cache_key",
                        "rank_list_size", "rank_photo_ids", "rank_scores"]
      )
    return self

  @module()
  def retrieval_photo_filter(self):
    self.batch_svalue_get_from_redis_enricher(   # 取 photo 创建时间
      item_table = target_photo_table,
      input_data_type = "int",
      output_data_type = "int",
      key_prefix = "n4ew_pdpc_",
      key_column = "photo_id",
      value_column = "create_time",
      redis_cluster_name = "adDupPhotoInfoCache"
    ).filter_by_attr(   # 将 创建时间晚于主 photo 的过滤掉
      item_table = target_photo_table,
      attr_name="create_time",
      remove_if=">",
      compare_to="{{photo_create_time}}",
      remove_if_attr_missing=True,
    ).filter_by_attr(   # 将 id 等于 query photo id 的 photo 过滤掉
      item_table = target_photo_table,
      attr_name="photo_id",
      remove_if="==",
      compare_to="{{photo_id}}",
      remove_if_attr_missing=True,
    ).sort(
      item_table = target_photo_table,
      score_from_attr= "score"
    ).deduplicate(
      item_table = target_photo_table,
    ).count_reco_result( # 统计 item数
      item_table = target_photo_table,
      save_count_to = "target_item_num"
    ).perflog(
      mode = "interval",
      value = "{{target_item_num}}",
      namespace = ns,
      subtag = "target_item_num"
    ).truncate(
      item_table = target_photo_table,
      size_limit="{{rank_num}}"
    )
    return self
  
  @module()
  def build_query_photo_table(self):
    self.enrich_attr_by_lua(   # photo_id 转 photo_id_list
      no_check=True,
      import_common_attr = ["photo_id"],
      function_for_common = "buildPhotoIdList",
      export_common_attr = ["photo_id_list"],
      lua_script = """
        function buildPhotoIdList()
          local photo_id_list = {}
          photo_id_list[1] = photo_id
          return photo_id_list
        end
      """
    ).build_table_from_common_list_attr(
      item_table = query_photo_table,
      new_table = query_photo_table,
      build_config = [  # 第一行默认为主键
        dict(src = "photo_id_list", dest = "photo_id")
      ]
    )
    return self
  
  def get_photo_frame_key(self, table_name):
    self.photo_frame_key_enricher(   # 取 photo 抽帧 key
      item_table = table_name,
      redis_cluster_name = "adDupPhotoInfoCache",
      key_prefix = "nn_pkf_",
      photo_id_column = "photo_id",
      frame_keys_column = "frame_keys",
      frame_status_column = "frame_status"
    ).photo_frame_key_sample_enricher(  # blobkey 前密后疏采样
      item_table = table_name,
      frame_keys_column = "frame_keys",
      frame_status_column = "frame_status"
    )
    return self
  
  def query_photo_frame_get(self):
    self.get_photo_frame_key(query_photo_table) \
    .copy_attr(   # 把frameStatus copy到 commonAttr
      item_table = query_photo_table,
      attrs=[
        {
          "from_item": "frame_status",
          "to_common": "frame_status"
        }
      ]
    ).if_("frame_status == 0") \
      .photo_extract(query_photo_table) \
    .end_if_()
    return self

  def target_photo_frame_get(self):
    self.copy_attr(
      item_table = target_photo_table,
      attrs=[{
        "from_item": "frame_status", 
        "to_common": "frame_status"   
      }]
    ).if_("frame_status == 0") \
      .photo_extract(target_photo_table) \
    .end_if_()
    return self
  
  def photo_extract(self, table_name):
    self.photo_size_info_enricher(
      no_check=True,
      item_table = table_name,
      photo_id_column = "photo_id",
      frame_keys_column = "frame_keys",
      photo_width_column = "photo_width",
      photo_height_column = "photo_height",
      frame_status_column = "frame_status"
    ).get_kconf_params(   # 取抽帧超时时间
      no_check=True,
      kconf_configs=[
        dict(kconf_key="ad.materialGenerationServer.dupPhotoCommonConf",
           value_type="json",
           json_path="frame_extract_timeout",
           export_common_attr="frame_extract_timeout"
        )
      ]
    ).photo_frame_extract_enricher(
      item_table = table_name,
      no_check=True,
      retry_time = 0, # 重试次数
      frame_extract_timeout_column = "frame_extract_timeout",
      photo_id_column = "photo_id",
      frame_keys_column = "frame_keys",
      photo_width_column = "photo_width",
      photo_height_column = "photo_height",
      frame_status_column = "frame_status",  # 为false才执行
      req_type="async_and_wait",
      redis_cluster_name="adDupPhotoInfoCache"
    ).photo_frame_key_sample_enricher(
      item_table = table_name,
      frame_keys_column = "frame_keys",
      frame_status_column = "frame_status"
    )
    return self
  
  def get_photo_rank_feats(self, table_name):
    self.blobstore_download_enricher(    # 请求 blobstore 获取抽帧信息
      item_table = table_name,
      bucket_name = "ad-creative-engine",
      keys_column = "frame_keys",
      values_column = "frame_infos",
    ).image_resize_and_crop_enricher(   # 对每一帧按短边256 先压缩再 center crop
      item_table = table_name,
      images_column = "frame_infos",
      frame_height_column = "frame_height",
      frame_width_column = "frame_width",
      frame_size_column = "frame_size"
    ).photo_rank_feature_enricher(    # 获取 photo feature
      item_table = table_name,
      images_column = "frame_infos",
      frame_height_column = "frame_height",
      frame_width_column = "frame_width",
      feature_column = "feature"
    ).filter_by_attr(   # 过滤掉获取抽帧失败的 photo frame_height为0说明当前photo抽帧结果、feature获取失败 需要过滤
      item_table = table_name,
      attr_name="frame_height",
      remove_if="<=",
      compare_to=0,
      remove_if_attr_missing=True,
    )
    return self

  def photo_sim_score_get(self):
    self.copy_attr(
      item_table = query_photo_table,
      attrs=[
        { "from_item": "feature", "to_common": "main_feature" },
        { "from_item": "frame_size", "to_common": "main_frame_size" },
      ]
    ).count_reco_result(
      item_table = target_photo_table,
      save_count_to = "target_item_after_rank"
    ).count_reco_result(
      item_table = query_photo_table,
      save_count_to = "query_item_after_rank"
    ).if_("target_item_after_rank == 0 or query_item_after_rank == 0") \
      .perflog(
        mode = "count",
        namespace = ns,
        subtag = "rank",
        extra1 = "target_or_query_item_empty"
      ).return_() \
     .end_if_() \
     .photo_rank_sim_info_enricher(
      item_table = target_photo_table,
      main_feature_column = "main_feature",
      main_frame_size_column = "main_frame_size",
      feature_column = "feature",
      frame_size_column = "frame_size",
      similar_info_column = "similar_info"
    ).photo_rank_sim_score_calc_enricher(
      item_table = target_photo_table,
      main_frame_size_column = "main_frame_size",
      frame_size_column = "frame_size",
      similar_info_column = "similar_info",
      similar_score_column = "similar_score"
    ).sort(
      no_check=True,
      item_table = target_photo_table,
      score_from_attr= "similar_score"
     ).count_reco_result( # 统计 item数
      item_table = target_photo_table,
      save_count_to = "rank_item_num"
    ).perflog(
      mode = "interval",
      value = "{{rank_item_num}}",
      namespace = ns,
      subtag = "rank_item_num"
    )
    return self

  @module()
  def get_dup_photo_realtime_similar_list(self):
    self.set_attr_value(   # 设置 retrieval prefix 到 commonattr
      common_attrs=[
        {"name": "biz", "type": "string", "value": "AD"},
        {"name": "cal_by_video_byte", "type": "int", "value": 0},
        {"name": "index_name", "type": "string", "value": "ann_dup_photo_new_arch"},
        {"name": "recall_num", "type": "int", "value": 200},
        {"name": "rank_num", "type": "int", "value": 30},
      ]
		).enrich_attr_by_lua(  # 构造 photo_id_str
      import_common_attr = ["photo_id"],
      function_for_common = "GetPhotoIdStr",
      export_common_attr = ["photo_id_str"],
      lua_script = """
        function GetPhotoIdStr()
          return tostring(photo_id)
        end
      """
    ).get_embedding_from_rpc() \
    .get_retrieval_list_from_rpc() \
    .retrieval_photo_filter() \
    .build_query_photo_table() \
    .enrich_by_sub_flow(
      no_check=True,
      sub_flow = query_photo_feature_get_flow,
      item_table = query_photo_table,
      pass_item_attrs= ["photo_id"],
      merge_item_attrs= ["feature", "frame_keys", "frame_status", "frame_size"],
      deep_copy=True
    ).get_photo_frame_key(target_photo_table) \
    .arrange_by_sub_flow(
      item_table = target_photo_table,
      sub_flow = target_photo_frame_get_flow, 
      expected_partition_size=1,
      pass_common_attrs = [""],
      pass_item_attrs = ["photo_id", "frame_status"],
      merge_item_attrs = ["feature","frame_keys", "frame_status", "frame_size"],
      no_check = True
    ).photo_sim_score_get() \
    .debug_log(
      item_table = "",
      no_check=True,
      respect_sample_logging = False,
      item_attrs = ["photo_id", "score", "similar_score"],
      common_attrs = ["data_type", "photo_id", "photo_create_time", "embedding_req", "avg_emb_normal","self_inner_product"]
    ).debug_log(
      item_table = query_photo_table,
      respect_sample_logging = False,
      item_attrs = ["photo_id", "frame_keys", "frame_size", "feature"],
    )
    return self;

#===================================================== Flow Define Start =====================================================
dup_info_query_flow = AdDupInfoQueryService(name="dup_info_query_flow", item_table=target_photo_table)
query_photo_feature_get_flow = AdDupInfoQueryService("query_photo_feature_get", item_table = query_photo_table)
target_photo_frame_get_flow = AdDupInfoQueryService("target_photo_frame_get", item_table = target_photo_table)

with dup_info_query_flow, data_manager:
  dup_info_query_flow.prepare() \
    .if_("data_type_int == 1") \
       .get_dup_retrieval_list() \
     .else_if_("data_type_int == 2") \
       .get_dup_rank_list() \
     .else_if_("data_type_int == 3") \
       .get_dup_photo_realtime_similar_list() \
     .end_if_()

with query_photo_feature_get_flow, data_manager:
  query_photo_feature_get_flow.query_photo_frame_get().get_photo_rank_feats(query_photo_table)

with target_photo_frame_get_flow, data_manager:
  target_photo_frame_get_flow.target_photo_frame_get().get_photo_rank_feats(target_photo_table)

service = LeafService(kess_name="ad-dup-info-query-service")
service.CHECK_UNUSED_ATTR = False
service.common_attrs_from_request = ["data_type", "photo_id", "enable_use_cache", "valid_duration", "photo_create_time"]
service.return_item_attrs([])
service.add_leaf_flows(leaf_flows=[dup_info_query_flow], request_type="default")
service.draw()
gen_service_graph_def(service, mod = "remote")
ad_config = {
  "grpc" : {
    "test" : False,
    "server" : {
      "kess_name" : "USE_KSN_AS_SERVICE",
      "port" : 20182,
      "grpc_cq_num": 8,
      "kcs_grpc_port_key" : "AUTO_PORT1",
      "thread_num": 100,  ##  default cpu num
      "quit_wait_seconds" : 85,
      "start_warmup_seconds" : 10, #默认 10s
    },
    "client_map" : {
    }
  }
}
current_folder = os.path.dirname(os.path.abspath(__file__))
service.build(output_file=os.path.join(current_folder, "pub/ad_dup_info_query_server/config/dynamic_json_config.json"),
                extra_fields=config)
"""
  
  
  
"""
