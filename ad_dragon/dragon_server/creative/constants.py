#============================================================== 常量定义 =================================================================================
# 追加 user_info 到 制作信息pb的配置
user_info_fill_config = [{ "item_attr": "nick_name", "path": "nick_name" },
                         { "item_attr": "head_image", "path": "head_image" },
                         { "item_attr": "fans_number", "path": "fans_number" }]
# 追加 user_info 到 制作信息pb的配置
kwai_shop_info_fill_config = [{ "item_attr": "brand_second_images", "path": "infos.brand_second_images", "append": True}]
kwai_shop_info_fill_config_universal = [
    { "item_attr": "brand_names", "path": "brand_names", "append": True},
    { "item_attr": "short_brand_names", "path": "short_brand_names", "append": True},
    { "item_attr": "trust_icon", "path": "trust_icon"},
    { "item_attr": "sale_volume", "path": "sale_volume"},
    { "item_attr": "coupon", "path": "coupon"},
    { "item_attr": "positive_info", "path": "positive_info"},
    { "item_attr": "brand_prices", "path": "brand_prices", "append": True},
    { "item_attr": "brand_second_images", "path": "infos.brand_second_images", "append": True}
]

# 追加 直播预约信息 到 制作信息pb的配置
reservation_info_fill_config = [{ "item_attr": "living_notice", "path": "living_notice" },
                                { "item_attr": "live_start_time", "path": "live_stream_start_time" }]
# 追加 PEC 激励信息 到 制作信息 pb 的配置
pec_reward_info_fill_config = [{ "item_attr": "equity_picture_url", "path": "equity_picture.url" }]
pec_reward_info_fill_config_universal = [
    { "item_attr": "main_title", "path": "main_title" },
    { "item_attr": "sub_title", "path": "sub_title" },
    { "item_attr": "discount_price", "path": "discount_price" },
    { "item_attr": "equity_picture_url", "path": "equity_picture.url" }
]

# redis获取抽帧信息 从json_field到attr_name的映射 url 放在最后
simple_frame_config = [{ "src_field_name": "height", "type": "int", "dest_column_name": "image_frame_height" },
                       { "src_field_name": "width", "type": "int", "dest_column_name": "image_frame_width" },
                       { "src_field_name": "url", "type": "string", "dest_column_name": "image_frame_url" }]
# redis获取抽帧信息 从json_field到attr_name的映射 url 放在最后
multi_frame_config = [{ "src_field_name": "height", "type": "int", "dest_column_name": "image_frame_height" },
                      { "src_field_name": "width", "type": "int", "dest_column_name": "image_frame_width" },
                      { "src_field_name": "frames", "type": "string_list_to_string", "dest_column_name": "image_frame_url" }]

# redis获取视频信息 从json_field到attr_name的映射
simple_photo_config = [{ "src_field_name": "cut_time", "type": "int", "dest_column_name": "slice_end_time" },
                       { "src_field_name": "video_key", "type": "string", "dest_column_name": "resource_id" },
                       { "src_field_name": "height", "type": "int", "dest_column_name": "photo_height" },
                       { "src_field_name": "width", "type": "int", "dest_column_name": "photo_width" },
                       { "src_field_name": "duration", "type": "int", "dest_column_name": "photo_duration" },
                       { "src_field_name": "live_id", "type": "int", "dest_column_name": "live_id" },] # 重写
# redis获取抽帧信息 用新增attr覆盖原有值
frame_copy_config = [
    dict(dest_column_name = "image_url", src_column_name = "image_frame_url", overwrite_if_src_not_empty = True, type = "string"),
    dict(dest_column_name = "image_height", src_column_name = "image_frame_height", overwrite_if_src_not_empty = True, type = "int"),
    dict(dest_column_name = "image_width", src_column_name = "image_frame_width", overwrite_if_src_not_empty = True, type = "int"),
]

# redis 获取到的涨粉抽帧信息写入 context
fans_gain_frame_copy_config = [
    dict(dest_column_name = "fans_gain_image_url", src_column_name = "image_frame_url", overwrite_if_src_not_empty = True, type = "string"),
    dict(dest_column_name = "fans_gain_image_height", src_column_name = "image_frame_height", overwrite_if_src_not_empty = True, type = "int"),
    dict(dest_column_name = "fans_gain_image_width", src_column_name = "image_frame_width", overwrite_if_src_not_empty = True, type = "int"),
]

# redis获取5s剪裁视频 从json_field到attr_name的映射
multi_clip_5s_config = [
  { "src_field_name": "json_array", "type": "array_to_object", "dest_column_name": "json_object" },
  { "src_field_name": "width", "type": "int", "dest_column_name": "clip_5s_photo_width" },
  { "src_field_name": "height", "type": "int", "dest_column_name": "clip_5s_photo_height" },
  { "src_field_name": "resource_id", "type": "string", "dest_column_name": "clip_5s_resource_id" },
  { "src_field_name": "url", "type": "string", "dest_column_name": "clip_5s_photo_url" },
]
# redis获取5s剪裁视频 用新增attr覆盖原有值
clip_5s_copy_config = [
  dict(dest_column_name = "resource_id", src_column_name = "clip_5s_resource_id", overwrite_if_src_not_empty = True, type = "string"),
  dict(dest_column_name = "photo_url", src_column_name = "clip_5s_photo_url", overwrite_if_src_not_empty = True, type = "string"),
  dict(dest_column_name = "photo_width", src_column_name = "clip_5s_photo_width", overwrite_if_src_not_empty = True, type = "int"),
  dict(dest_column_name = "photo_height", src_column_name = "clip_5s_photo_height", overwrite_if_src_not_empty = True, type = "int"),
]
# 生产策略绑定维度配置
strategy_bind_info = {
  'PHOTO_GRANULARITY_TYPE': ["PHOTO_GRANULARITY_TYPE", 5, 5, 1, "photo_id", "photo_id", "user_id"],
  'UNIT_GRANULARITY_TYPE': ["UNIT_GRANULARITY_TYPE", 3, 3, 3, "unit_id", "unit_id", "campaign_id"],
  'CAMPAIGN_GRANULARITY_TYPE': ["CAMPAIGN_GRANULARITY_TYPE", 2, 2, 2, "campaign_id", "campaign_id", "account_id"],
  'APP_GRANULARITY_TYPE': ["APP_GRANULARITY_TYPE", 7, 6, 4, "app_hash_id", "app_hash_id", "unit_id"],
  'PICTURE_GRANULARITY_TYPE': ["PICTURE_GRANULARITY_TYPE", 8, 8, 1, "pic_id", "pic_id", "user_id"],
  'PRODUCT_GRANULARITY_TYPE': ["PRODUCT_GRANULARITY_TYPE", 10, 10, 7, "product_hash_id", "product_hash_id", "second_industry_id"]
}

# 直播剪裁策略维度字段传参
live_cut_video_strategy_info = ["UNKNOWN_GRANULARITY_TYPE", 6, 7, 1, "author_id", "live_id", "author_id"]
live_cut_img_strategy_info = ["UNKNOWN_GRANULARITY_TYPE", 6, 7, 1, "author_id", "live_id", "author_id"]

# 参与去重key拼接的兜底field列表
check_fields = ["bind_level", "bind_level_ids", "adapter_material_type", "derive_material_type",
                   "make_strategy", "data_source", "biz_enum", "source_id", "source_type",
                   "make_info.rule_id","make_info.online_rule_ids"]
# 参与去重key拼接的field列表
strategy_dedup_info = {
  "strategy_photo_normal_extract_frame": ["bind_level", "bind_level_ids", "source_id", "source_type", "make_strategy", "biz_enum", "data_source", "derive_material_type",
                                          "adapter_material_type", "make_info.rule_id", "make_info.online_rule_ids", "make_info.preonline_rule_ids"],
  "strategy_app_reward_induce": ["bind_level", "bind_level_ids", "source_id", "source_type", "make_strategy", "biz_enum", "data_source", "derive_material_type",
                                "adapter_material_type",  "app_name", "icon_url", "make_info.online_rule_ids"],
  "strategy_unit_small_shop": [ "bind_level", "bind_level_ids", "source_id", "source_type", "make_strategy", "biz_enum", "data_source", "derive_material_type",
                                "adapter_material_type", "make_info.rule_id", "make_info.online_rule_ids"],
  "strategy_unit_direct_ecom": ["bind_level", "bind_level_ids", "source_id", "source_type", "make_strategy", "biz_enum", "data_source", "derive_material_type",
                                "adapter_material_type", "make_info.rule_id", "make_info.online_rule_ids"],
  "strategy_photo_5s_screen": ["bind_level", "bind_level_ids", "source_id", "source_type", "make_strategy", "biz_enum", "data_source", "derive_material_type",
                                          "adapter_material_type", "make_info.rule_id", "make_info.online_rule_ids", "make_info.preonline_rule_ids"],
  "strategy_live_stream_image": ["bind_level", "adapter_material_type", "derive_material_type", "make_strategy", "data_source", "biz_enum", "source_id",
                                   "source_type", "make_info.rule_id", "make_info.online_rule_ids", "make_info.preonline_rule_ids"],
  "strategy_unit_video_fans": ["bind_level", "adapter_material_type", "derive_material_type", "make_strategy", "data_source", "biz_enum", "source_id", "source_type",
                               "unit_id", "photo_id", "make_info.rule_id", "make_info.online_rule_ids"],
  "strategy_unit_simple_element": ["bind_level", "bind_level_ids", "adapter_material_type", "derive_material_type", "make_strategy", "data_source", "biz_enum", "source_id",
                                   "source_type", "make_info.rule_id", "make_info.online_rule_ids"],
  "strategy_photo_live_reservation": ["bind_level", "bind_level_ids", "adapter_material_type", "derive_material_type", "make_strategy", "data_source", "biz_enum", "source_id",
                                      "source_type", "make_info.rule_id", "make_info.online_rule_ids"],
  "strategy_live_stream_cut": ["bind_level", "bind_level_ids", "live_stream_make_info.author_id", "live_stream_make_info.live_stream_id", "live_stream_make_info.live_start_time",
                               "live_stream_make_info.slice_start_time", "live_stream_make_info.slice_end_time", "adapter_material_type", "make_info.rule_id",
                               "make_info.preonline_rule_ids","make_info.exp_tag", "derive_material_type", "make_strategy", "data_source", "biz_enum", "source_id", "source_type"],
  "strategy_photo_small_image": ["bind_level", "bind_level_ids", "adapter_material_type", "derive_material_type", "make_strategy", "data_source", "biz_enum", "source_id",
                                "source_type", "make_info.rule_id", "make_info.online_rule_ids"],
  "strategy_unit_group_image": ["bind_level", "adapter_material_type", "derive_material_type", "make_strategy", "data_source", "biz_enum", "source_id", "source_type",
                               "unit_id", "make_info.rule_id", "make_info.online_rule_ids"],
  "strategy_unit_universal": ["bind_level", "adapter_material_type", "derive_material_type", "make_strategy", "data_source", "biz_enum", "source_id", "source_type",
                              "make_info.online_rule_ids", "unit_id"],
  "strategy_universal": ["adapter_material_type", "derive_material_type", "make_strategy", "data_source", "biz_enum", "source_id", "source_type", "make_info.online_rule_ids"] # 需要根据模板绑定维度再拼接对应字段
}
# kessClient 配置
config = {
  "grpc" : {
    "test" : False,
    "server" : {
      "kess_name" : "USE_KSN_AS_SERVICE",  # kess_name 可以自动扩展为 kess_name + "_" + ksp 分组, default 不扩展
      "port" : 20012,
      "kcs_grpc_port_key" : "AUTO_PORT1",  # 支持服务部署到容器上， 此时 port 参数失效
      "quit_wait_seconds" : 120, # 默认 60s
      "thread_num": 400  ## default cpu num
    },
    "client_map" : {
      "CreativeInformationGetterService": "grpc_adCreativeInformationGetterService",
      "CreativeProcessService": "ad-alliance-creative-process-uni-service",
      "DpaProductSearchDealer": "grpc_ad_product_search",
      "UniverseGenOptCtrInfService": "grpc_UniverseGenOptCTRInfRpcService",
      "LiveCurrentLivingService" : "grpc_liveExposeCurrentLivingRpcService",
      "EcomItemInfoService" : "grpc_adEcomProductItemInfoRpcService",
      "LiveStremInfoService" : "grpc_adLiveInfoGetRpcService",
      "UserTopPhotoService" : "grpc_apiCoreUserTopPhotoService",
      "AdMaterialBaseInfoService" : "grpc_adMaterialBaseInfoService",
      "KwaiShopProductService" : "kwaishop-product-detail-service",
      "KwaiShopProductServiceTest" : "grpc_kwaishop-product-detail-service",
      "LiveReservationReadServiceRpc" : "grpc_liveReservationReadServiceRpc",
      "CmdLineExecutorService": "grpc_editorSdkServiceForKaiYanCopy",
      "AdDpaProductServer" : "grpc_adDpaProductServerImpl",
      "DpOneServiceMmuHigh" : "grpc_dpOneServiceMmuHigh",
      "OcrDetectService" : "grpc_ztOcrDetectService",
      "forward_index_client": "ad-forward-index", #"grpc_adForwardIndexService",
      "SellerTagInfoService" : "grpc_kwaishopProductListAggregationService",
      "MmuPhotoMd5QueryServiceKey": "grpc_mmuPhotoMd5QueryService",
      "mmuAdEmbeddingServiceKey": "grpc_mmuAdEmbeddingService",
      "adTagRetrieveServerOfflineKey": "grpc_adTagRetrieveServer_offline",
      "PhotoServiceKey": "grpc_apiCorePhotoService",
      "MediaProcessingJobServiceKey": "grpc_mediaProcessingJobService",
      "mmuDlFeatureCalculateServiceKey": "grpc_mmuDlFeatureCalculateService",
      "adLiveClipSelectVersionServiceKey": "grpc_adLiveClipOptSelectVersionService",
      "wideTableProductServiceKey": "wide-table-product-service"
    }
  }
}

dsp_pic_parse_config = [
  { "src_field_name": "height", "type": "int", "dest_column_name": "pic_height" },
  {	"src_field_name": "width", "type": "int", "dest_column_name": "pic_width" },
  { "src_field_name": "pic_url", "type": "string", "dest_column_name": "pic_url" }
]

dsp_pic_copy_config = [
  dict(dest_column_name = "image_url", src_column_name = "pic_url", overwrite_if_src_not_empty = True, type = "string"),
	dict(dest_column_name = "image_height", src_column_name = "pic_height", overwrite_if_src_not_empty = True, type = "int"),
	dict(dest_column_name = "image_width", src_column_name = "pic_width", overwrite_if_src_not_empty = True, type = "int")
]

# 通用生产策略中, 元素名、准入条件、输出列。
# 白名单: [[白名单项1], [白名单项2]...], 单个白名单项内取交集，各个白名单项取并集; 白名单如果不配置，代表所有都准入(除黑名单)。
# 黑名单: [维度1, 维度2, ...], 各个维度取并集。
universal_strategy_element_admit_config = {
    "ad_user_info": {
        "black_list" : [

        ],
        "white_list" : [
          [
            dict(column_name="first_industry_id", value_list = [1004], type = "int"),
            dict(column_name="campaign_type", value_list = [13], type = "int"),
            dict(column_name="ocpx_action_type", value_list = [2, 72], type = "int")
          ],
          [
            dict(column_name="first_industry_id", value_list = [1004], type = "int"),
            dict(column_name="live_creative_type", value_list = [2], type = "int")
          ]
        ],
        "output_column" : "is_need_ad_user_info",
        "fill_info_config" : user_info_fill_config
    },

    "live_reservation_info": {
        "black_list" : [

        ],
        "white_list" : [
          [
            dict(column_name="first_industry_id", value_list = [1004], type = "int"),
            dict(column_name="campaign_type", value_list = [13], type = "int"),
            dict(column_name="ocpx_action_type", value_list = [2], type = "int")
          ]
        ],
        "output_column" : "is_need_live_reservation_info",
        "fill_info_config" : reservation_info_fill_config
    },

    "kwai_shop_item_info": {
        "black_list" : [

        ],
        "white_list" : [
          [
            dict(column_name="first_industry_id", value_list = [1004], type = "int"),
            dict(column_name="campaign_type", value_list = [13], type = "int"),
            dict(column_name="ocpx_action_type", value_list = [395, 192], type = "int"),
            dict(column_name="live_creative_type", value_list = [0], type = "int")
          ]
        ],
        "output_column" : "is_need_kwai_shop_item_info",
        "fill_info_config" : kwai_shop_info_fill_config_universal
    },

    "pec_reward_info": {
        "black_list" : [

        ],
        "white_list" : [

        ],
        "output_column" : "is_need_pec_reward_info",
        "fill_info_config" : pec_reward_info_fill_config_universal
    },

    "dsp_pic_info": {
        "black_list" : [

        ],
        "white_list" : [
          [
            dict(column_name="first_industry_id", value_list = [1001, 1002, 1010, 1021], type = "int"),
            dict(column_name="material_type", value_list = [5,6], type = "int")
          ]
        ],
        "output_column" : "is_need_dsp_pic_info",
        "fill_info_config" : []
    },

    "algo_extract_frame_info": {
        "black_list" : [

        ],
        "white_list" : [

        ],
        "output_column" : "is_need_algo_extract_frame_info",
        "fill_info_config" : []
    },

    "fans_gain_info" : {
        "black_list" : [

        ],
        "white_list" : [
          [
            dict(column_name="first_industry_id", value_list = [1004], type = "int"),
            dict(column_name="campaign_type", value_list = [13], type = "int"),
            dict(column_name="ocpx_action_type", value_list = [72], type = "int")
          ]
        ],
        "output_column" : "is_need_fans_gain_info",
        "fill_info_config" : []
    }
}
