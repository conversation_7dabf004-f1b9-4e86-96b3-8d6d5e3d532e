<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.30.1 (20180420.1509)
 -->
<!-- Title: DAG&#45;ad&#45;creative&#45;data&#45;collection&#45;server&#45;get_top_item_for_photo Pages: 1 -->
<svg width="516pt" height="1566pt"
 viewBox="0.00 0.00 516.00 1566.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1562)">
<title>DAG&#45;ad&#45;creative&#45;data&#45;collection&#45;server&#45;get_top_item_for_photo</title>
<polygon fill="white" stroke="white" points="-4,5 -4,-1562 513,-1562 513,5 -4,5"/>
<text text-anchor="middle" x="254" y="-100" font-family="Times,serif" font-size="20.00">DAG drawn by Dragonfly v0.7.18</text>
<text text-anchor="middle" x="254" y="-78" font-family="Times,serif" font-size="20.00">Service: ad&#45;creative&#45;data&#45;collection&#45;server</text>
<text text-anchor="middle" x="254" y="-56" font-family="Times,serif" font-size="20.00">RequestType: get_top_item_for_photo</text>
<text text-anchor="middle" x="254" y="-34" font-family="Times,serif" font-size="20.00">Date: 2023&#45;09&#45;16 10:32:35</text>
<g id="clust1" class="cluster"><title>cluster_0</title>
<polygon fill="none" stroke="grey" points="8,-216 8,-1492 500,-1492 500,-216 8,-216"/>
<text text-anchor="middle" x="111" y="-1472" font-family="Times,serif" font-size="20.00">get_top_item_for_photo</text>
</g>
<!-- START -->
<g id="node1" class="node"><title>START</title>
<polygon fill="lightgray" stroke="black" points="283,-1558 225,-1558 225,-1500 283,-1500 283,-1558"/>
<polyline fill="none" stroke="black" points="237,-1558 225,-1546 "/>
<polyline fill="none" stroke="black" points="225,-1512 237,-1500 "/>
<polyline fill="none" stroke="black" points="271,-1500 283,-1512 "/>
<polyline fill="none" stroke="black" points="283,-1546 271,-1558 "/>
<text text-anchor="middle" x="254" y="-1525.3" font-family="Times,serif" font-size="14.00">START</text>
</g>
<!-- flow_start&#45;get_top_item_for_photo_0 -->
<g id="node3" class="node"><title>flow_start&#45;get_top_item_for_photo_0</title>
<ellipse fill="grey" stroke="grey" cx="254" cy="-1448" rx="5.76" ry="5.76"/>
</g>
<!-- START&#45;&gt;flow_start&#45;get_top_item_for_photo_0 -->
<g id="edge1" class="edge"><title>START&#45;&gt;flow_start&#45;get_top_item_for_photo_0</title>
<path fill="none" stroke="black" d="M254,-1499.93C254,-1487.82 254,-1474.14 254,-1464.02"/>
<polygon fill="black" stroke="black" points="257.5,-1463.76 254,-1453.76 250.5,-1463.76 257.5,-1463.76"/>
</g>
<!-- END -->
<g id="node2" class="node"><title>END</title>
<polygon fill="lightgray" stroke="black" points="276,-188 232,-188 232,-144 276,-144 276,-188"/>
<polyline fill="none" stroke="black" points="244,-188 232,-176 "/>
<polyline fill="none" stroke="black" points="232,-156 244,-144 "/>
<polyline fill="none" stroke="black" points="264,-144 276,-156 "/>
<polyline fill="none" stroke="black" points="276,-176 264,-188 "/>
<text text-anchor="middle" x="254" y="-162.3" font-family="Times,serif" font-size="14.00">END</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;0 -->
<g id="node5" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;0</title>
<polygon fill="white" stroke="black" points="378,-1406 130,-1406 130,-1370 378,-1370 378,-1406"/>
<text text-anchor="middle" x="254" y="-1384.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::calc_time_cost_s</text>
</g>
<!-- flow_start&#45;get_top_item_for_photo_0&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;0 -->
<g id="edge2" class="edge"><title>flow_start&#45;get_top_item_for_photo_0&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;0</title>
<path fill="none" stroke="black" d="M254,-1442.05C254,-1436.2 254,-1425.99 254,-1416.07"/>
<polygon fill="black" stroke="black" points="257.5,-1416.05 254,-1406.05 250.5,-1416.05 257.5,-1416.05"/>
</g>
<!-- flow_end&#45;get_top_item_for_photo_0 -->
<g id="node4" class="node"><title>flow_end&#45;get_top_item_for_photo_0</title>
<ellipse fill="grey" stroke="grey" cx="254" cy="-230" rx="5.76" ry="5.76"/>
</g>
<!-- flow_end&#45;get_top_item_for_photo_0&#45;&gt;END -->
<g id="edge19" class="edge"><title>flow_end&#45;get_top_item_for_photo_0&#45;&gt;END</title>
<path fill="none" stroke="black" d="M254,-224.135C254,-218.414 254,-208.42 254,-198.373"/>
<polygon fill="black" stroke="black" points="257.5,-198.061 254,-188.061 250.5,-198.061 257.5,-198.061"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;1 -->
<g id="node6" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;1</title>
<polygon fill="white" stroke="black" points="491.25,-1334 16.75,-1334 16.75,-1298 491.25,-1298 491.25,-1334"/>
<text text-anchor="middle" x="254" y="-1312.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::ad_material_fetch_message_from_kafka_enricher_F048F1</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;0&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;1 -->
<g id="edge3" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;0&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;1</title>
<path fill="none" stroke="black" d="M254,-1369.7C254,-1361.98 254,-1352.71 254,-1344.11"/>
<polygon fill="black" stroke="black" points="257.5,-1344.1 254,-1334.1 250.5,-1344.1 257.5,-1344.1"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;2 -->
<g id="node7" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;2</title>
<polygon fill="white" stroke="black" points="436.25,-1262 71.75,-1262 71.75,-1226 436.25,-1226 436.25,-1262"/>
<text text-anchor="middle" x="254" y="-1240.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::parse_protobuf_from_string_B6DAD0</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;1&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;2 -->
<g id="edge4" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;1&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;2</title>
<path fill="none" stroke="black" d="M254,-1297.7C254,-1289.98 254,-1280.71 254,-1272.11"/>
<polygon fill="black" stroke="black" points="257.5,-1272.1 254,-1262.1 250.5,-1272.1 257.5,-1272.1"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;3 -->
<g id="node8" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;3</title>
<polygon fill="white" stroke="black" points="417,-1190 91,-1190 91,-1154 417,-1154 417,-1190"/>
<text text-anchor="middle" x="254" y="-1168.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::enrich_with_protobuf_2BA64B</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;2&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;3 -->
<g id="edge5" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;2&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;3</title>
<path fill="none" stroke="black" d="M254,-1225.7C254,-1217.98 254,-1208.71 254,-1200.11"/>
<polygon fill="black" stroke="black" points="257.5,-1200.1 254,-1190.1 250.5,-1200.1 257.5,-1200.1"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;4 -->
<g id="node9" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;4</title>
<polygon fill="white" stroke="black" points="402,-1118 106,-1118 106,-1082 402,-1082 402,-1118"/>
<text text-anchor="middle" x="254" y="-1096.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::set_default_value_559930</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;3&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;4 -->
<g id="edge6" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;3&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;4</title>
<path fill="none" stroke="black" d="M254,-1153.7C254,-1145.98 254,-1136.71 254,-1128.11"/>
<polygon fill="black" stroke="black" points="257.5,-1128.1 254,-1118.1 250.5,-1128.1 257.5,-1128.1"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;5 -->
<g id="node10" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;5</title>
<polygon fill="white" stroke="black" points="396,-1046 112,-1046 112,-1010 396,-1010 396,-1046"/>
<text text-anchor="middle" x="254" y="-1024.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::build_protobuf_2E9758</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;4&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;5 -->
<g id="edge7" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;4&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;5</title>
<path fill="none" stroke="black" d="M254,-1081.7C254,-1073.98 254,-1064.71 254,-1056.11"/>
<polygon fill="black" stroke="black" points="257.5,-1056.1 254,-1046.1 250.5,-1056.1 257.5,-1056.1"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;6 -->
<g id="node11" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;6</title>
<polygon fill="white" stroke="black" points="397.25,-974 110.75,-974 110.75,-938 397.25,-938 397.25,-974"/>
<text text-anchor="middle" x="254" y="-952.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::build_protobuf_2B757C</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;5&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;6 -->
<g id="edge8" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;5&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;6</title>
<path fill="none" stroke="black" d="M254,-1009.7C254,-1001.98 254,-992.712 254,-984.112"/>
<polygon fill="black" stroke="black" points="257.5,-984.104 254,-974.104 250.5,-984.104 257.5,-984.104"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;7 -->
<g id="node12" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;7</title>
<polygon fill="white" stroke="black" points="399,-902 109,-902 109,-866 399,-866 399,-902"/>
<text text-anchor="middle" x="254" y="-880.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::build_protobuf_A38D1E</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;6&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;7 -->
<g id="edge9" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;6&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;7</title>
<path fill="none" stroke="black" d="M254,-937.697C254,-929.983 254,-920.712 254,-912.112"/>
<polygon fill="black" stroke="black" points="257.5,-912.104 254,-902.104 250.5,-912.104 257.5,-912.104"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;8 -->
<g id="node13" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;8</title>
<polygon fill="white" stroke="black" points="400,-830 108,-830 108,-794 400,-794 400,-830"/>
<text text-anchor="middle" x="254" y="-808.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::build_protobuf_AA662D</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;7&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;8 -->
<g id="edge10" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;7&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;8</title>
<path fill="none" stroke="black" d="M254,-865.697C254,-857.983 254,-848.712 254,-840.112"/>
<polygon fill="black" stroke="black" points="257.5,-840.104 254,-830.104 250.5,-840.104 257.5,-840.104"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;9 -->
<g id="node14" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;9</title>
<polygon fill="white" stroke="black" points="410.25,-758 97.75,-758 97.75,-722 410.25,-722 410.25,-758"/>
<text text-anchor="middle" x="254" y="-736.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::enrich_attr_by_lua_A0B3CA</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;8&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;9 -->
<g id="edge11" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;8&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;9</title>
<path fill="none" stroke="black" d="M254,-793.697C254,-785.983 254,-776.712 254,-768.112"/>
<polygon fill="black" stroke="black" points="257.5,-768.104 254,-758.104 250.5,-768.104 257.5,-768.104"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;10 -->
<g id="node15" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;10</title>
<ellipse fill="lightgrey" stroke="black" cx="254" cy="-659" rx="227.532" ry="26.7407"/>
<text text-anchor="middle" x="254" y="-662.8" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::_branch_controller_09979E9B</text>
<text text-anchor="middle" x="254" y="-647.8" font-family="Times,serif" font-size="14.00">(is_photo_valid == 1)</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;9&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;10 -->
<g id="edge12" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;9&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;10</title>
<path fill="none" stroke="black" d="M254,-721.858C254,-714.356 254,-705.25 254,-696.358"/>
<polygon fill="black" stroke="black" points="257.5,-696.126 254,-686.126 250.5,-696.126 257.5,-696.126"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;11 -->
<g id="node16" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;11</title>
<polygon fill="#088cdb" fill-opacity="0.301961" stroke="black" points="422.25,-596 85.75,-596 85.75,-560 422.25,-560 422.25,-596"/>
<text text-anchor="middle" x="254" y="-574.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::enrich_by_generic_grpc_C7A655</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;10&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;11 -->
<g id="edge13" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;10&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;11</title>
<path fill="none" stroke="black" d="M254,-631.694C254,-623.58 254,-614.626 254,-606.438"/>
<polygon fill="black" stroke="black" points="257.5,-606.248 254,-596.248 250.5,-606.248 257.5,-606.248"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;12 -->
<g id="node17" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;12</title>
<polygon fill="white" stroke="black" points="400,-524 108,-524 108,-488 400,-488 400,-524"/>
<text text-anchor="middle" x="254" y="-502.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::log_debug_info_E6C3E7</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;11&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;12 -->
<g id="edge14" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;11&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;12</title>
<path fill="none" stroke="black" d="M254,-559.697C254,-551.983 254,-542.712 254,-534.112"/>
<polygon fill="black" stroke="black" points="257.5,-534.104 254,-524.104 250.5,-534.104 257.5,-534.104"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;13 -->
<g id="node18" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;13</title>
<polygon fill="white" stroke="black" points="378.25,-452 129.75,-452 129.75,-416 378.25,-416 378.25,-452"/>
<text text-anchor="middle" x="254" y="-430.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::calc_time_cost_e</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;12&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;13 -->
<g id="edge15" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;12&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;13</title>
<path fill="none" stroke="black" d="M254,-487.697C254,-479.983 254,-470.712 254,-462.112"/>
<polygon fill="black" stroke="black" points="257.5,-462.104 254,-452.104 250.5,-462.104 257.5,-462.104"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;14 -->
<g id="node19" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;14</title>
<polygon fill="white" stroke="black" points="372,-380 136,-380 136,-344 372,-344 372,-380"/>
<text text-anchor="middle" x="254" y="-358.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::calc_time_cost</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;13&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;14 -->
<g id="edge16" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;13&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;14</title>
<path fill="none" stroke="black" d="M254,-415.697C254,-407.983 254,-398.712 254,-390.112"/>
<polygon fill="black" stroke="black" points="257.5,-390.104 254,-380.104 250.5,-390.104 257.5,-390.104"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;15 -->
<g id="node20" class="node"><title>proc&#45;get_top_item_for_photo_0&#45;15</title>
<polygon fill="white" stroke="black" points="372,-308 136,-308 136,-272 372,-272 372,-308"/>
<text text-anchor="middle" x="254" y="-286.3" font-family="Times,serif" font-size="14.00">get_top_item_for_photo::perf_time_cost</text>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;14&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;15 -->
<g id="edge17" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;14&#45;&gt;proc&#45;get_top_item_for_photo_0&#45;15</title>
<path fill="none" stroke="black" d="M254,-343.697C254,-335.983 254,-326.712 254,-318.112"/>
<polygon fill="black" stroke="black" points="257.5,-318.104 254,-308.104 250.5,-318.104 257.5,-318.104"/>
</g>
<!-- proc&#45;get_top_item_for_photo_0&#45;15&#45;&gt;flow_end&#45;get_top_item_for_photo_0 -->
<g id="edge18" class="edge"><title>proc&#45;get_top_item_for_photo_0&#45;15&#45;&gt;flow_end&#45;get_top_item_for_photo_0</title>
<path fill="none" stroke="black" d="M254,-271.912C254,-263.746 254,-254.055 254,-246.155"/>
<polygon fill="black" stroke="black" points="257.5,-245.97 254,-235.97 250.5,-245.97 257.5,-245.97"/>
</g>
</g>
</svg>
