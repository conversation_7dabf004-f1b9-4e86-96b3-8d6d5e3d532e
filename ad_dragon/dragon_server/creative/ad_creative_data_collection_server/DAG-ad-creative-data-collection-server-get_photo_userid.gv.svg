<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.30.1 (20180420.1509)
 -->
<!-- Title: DAG&#45;ad&#45;creative&#45;data&#45;collection&#45;server&#45;get_photo_userid Pages: 1 -->
<svg width="490pt" height="1548pt"
 viewBox="0.00 0.00 490.00 1548.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1544)">
<title>DAG&#45;ad&#45;creative&#45;data&#45;collection&#45;server&#45;get_photo_userid</title>
<polygon fill="white" stroke="white" points="-4,5 -4,-1544 487,-1544 487,5 -4,5"/>
<text text-anchor="middle" x="241" y="-100" font-family="Times,serif" font-size="20.00">DAG drawn by Dragonfly v0.7.20</text>
<text text-anchor="middle" x="241" y="-78" font-family="Times,serif" font-size="20.00">Service: ad&#45;creative&#45;data&#45;collection&#45;server</text>
<text text-anchor="middle" x="241" y="-56" font-family="Times,serif" font-size="20.00">RequestType: get_photo_userid</text>
<text text-anchor="middle" x="241" y="-34" font-family="Times,serif" font-size="20.00">Date: 2024&#45;01&#45;19 17:48:26</text>
<g id="clust1" class="cluster"><title>cluster_0</title>
<polygon fill="none" stroke="grey" points="8,-216 8,-1474 474,-1474 474,-216 8,-216"/>
<text text-anchor="middle" x="84" y="-1454" font-family="Times,serif" font-size="20.00">get_photo_userid</text>
</g>
<!-- START -->
<g id="node1" class="node"><title>START</title>
<polygon fill="lightgray" stroke="black" points="270,-1540 212,-1540 212,-1482 270,-1482 270,-1540"/>
<polyline fill="none" stroke="black" points="224,-1540 212,-1528 "/>
<polyline fill="none" stroke="black" points="212,-1494 224,-1482 "/>
<polyline fill="none" stroke="black" points="258,-1482 270,-1494 "/>
<polyline fill="none" stroke="black" points="270,-1528 258,-1540 "/>
<text text-anchor="middle" x="241" y="-1507.3" font-family="Times,serif" font-size="14.00">START</text>
</g>
<!-- flow_start&#45;get_photo_userid_0 -->
<g id="node3" class="node"><title>flow_start&#45;get_photo_userid_0</title>
<ellipse fill="grey" stroke="grey" cx="241" cy="-1430" rx="5.76" ry="5.76"/>
</g>
<!-- START&#45;&gt;flow_start&#45;get_photo_userid_0 -->
<g id="edge1" class="edge"><title>START&#45;&gt;flow_start&#45;get_photo_userid_0</title>
<path fill="none" stroke="black" d="M241,-1481.93C241,-1469.82 241,-1456.14 241,-1446.02"/>
<polygon fill="black" stroke="black" points="244.5,-1445.76 241,-1435.76 237.5,-1445.76 244.5,-1445.76"/>
</g>
<!-- END -->
<g id="node2" class="node"><title>END</title>
<polygon fill="lightgray" stroke="black" points="263,-188 219,-188 219,-144 263,-144 263,-188"/>
<polyline fill="none" stroke="black" points="231,-188 219,-176 "/>
<polyline fill="none" stroke="black" points="219,-156 231,-144 "/>
<polyline fill="none" stroke="black" points="251,-144 263,-156 "/>
<polyline fill="none" stroke="black" points="263,-176 251,-188 "/>
<text text-anchor="middle" x="241" y="-162.3" font-family="Times,serif" font-size="14.00">END</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;0 -->
<g id="node5" class="node"><title>proc&#45;get_photo_userid_0&#45;0</title>
<polygon fill="white" stroke="black" points="350,-1388 132,-1388 132,-1352 350,-1352 350,-1388"/>
<text text-anchor="middle" x="241" y="-1366.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::calc_time_cost_s</text>
</g>
<!-- flow_start&#45;get_photo_userid_0&#45;&gt;proc&#45;get_photo_userid_0&#45;0 -->
<g id="edge2" class="edge"><title>flow_start&#45;get_photo_userid_0&#45;&gt;proc&#45;get_photo_userid_0&#45;0</title>
<path fill="none" stroke="black" d="M241,-1424.05C241,-1418.2 241,-1407.99 241,-1398.07"/>
<polygon fill="black" stroke="black" points="244.5,-1398.05 241,-1388.05 237.5,-1398.05 244.5,-1398.05"/>
</g>
<!-- flow_end&#45;get_photo_userid_0 -->
<g id="node4" class="node"><title>flow_end&#45;get_photo_userid_0</title>
<ellipse fill="grey" stroke="grey" cx="241" cy="-230" rx="5.76" ry="5.76"/>
</g>
<!-- flow_end&#45;get_photo_userid_0&#45;&gt;END -->
<g id="edge19" class="edge"><title>flow_end&#45;get_photo_userid_0&#45;&gt;END</title>
<path fill="none" stroke="black" d="M241,-224.135C241,-218.414 241,-208.42 241,-198.373"/>
<polygon fill="black" stroke="black" points="244.5,-198.061 241,-188.061 237.5,-198.061 244.5,-198.061"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;1 -->
<g id="node6" class="node"><title>proc&#45;get_photo_userid_0&#45;1</title>
<polygon fill="white" stroke="black" points="465.25,-1316 16.75,-1316 16.75,-1280 465.25,-1280 465.25,-1316"/>
<text text-anchor="middle" x="241" y="-1294.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::ad_material_fetch_message_from_kafka_enricher_8C95EA</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;0&#45;&gt;proc&#45;get_photo_userid_0&#45;1 -->
<g id="edge3" class="edge"><title>proc&#45;get_photo_userid_0&#45;0&#45;&gt;proc&#45;get_photo_userid_0&#45;1</title>
<path fill="none" stroke="black" d="M241,-1351.7C241,-1343.98 241,-1334.71 241,-1326.11"/>
<polygon fill="black" stroke="black" points="244.5,-1326.1 241,-1316.1 237.5,-1326.1 244.5,-1326.1"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;2 -->
<g id="node7" class="node"><title>proc&#45;get_photo_userid_0&#45;2</title>
<polygon fill="white" stroke="black" points="408.25,-1244 73.75,-1244 73.75,-1208 408.25,-1208 408.25,-1244"/>
<text text-anchor="middle" x="241" y="-1222.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::parse_protobuf_from_string_B6DAD0</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;1&#45;&gt;proc&#45;get_photo_userid_0&#45;2 -->
<g id="edge4" class="edge"><title>proc&#45;get_photo_userid_0&#45;1&#45;&gt;proc&#45;get_photo_userid_0&#45;2</title>
<path fill="none" stroke="black" d="M241,-1279.7C241,-1271.98 241,-1262.71 241,-1254.11"/>
<polygon fill="black" stroke="black" points="244.5,-1254.1 241,-1244.1 237.5,-1254.1 244.5,-1254.1"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;3 -->
<g id="node8" class="node"><title>proc&#45;get_photo_userid_0&#45;3</title>
<polygon fill="white" stroke="black" points="388.25,-1172 93.75,-1172 93.75,-1136 388.25,-1136 388.25,-1172"/>
<text text-anchor="middle" x="241" y="-1150.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::enrich_with_protobuf_FA84A7</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;2&#45;&gt;proc&#45;get_photo_userid_0&#45;3 -->
<g id="edge5" class="edge"><title>proc&#45;get_photo_userid_0&#45;2&#45;&gt;proc&#45;get_photo_userid_0&#45;3</title>
<path fill="none" stroke="black" d="M241,-1207.7C241,-1199.98 241,-1190.71 241,-1182.11"/>
<polygon fill="black" stroke="black" points="244.5,-1182.1 241,-1172.1 237.5,-1182.1 244.5,-1182.1"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;4 -->
<g id="node9" class="node"><title>proc&#45;get_photo_userid_0&#45;4</title>
<polygon fill="white" stroke="black" points="367,-1100 115,-1100 115,-1064 367,-1064 367,-1100"/>
<text text-anchor="middle" x="241" y="-1078.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::build_protobuf_751164</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;3&#45;&gt;proc&#45;get_photo_userid_0&#45;4 -->
<g id="edge6" class="edge"><title>proc&#45;get_photo_userid_0&#45;3&#45;&gt;proc&#45;get_photo_userid_0&#45;4</title>
<path fill="none" stroke="black" d="M241,-1135.7C241,-1127.98 241,-1118.71 241,-1110.11"/>
<polygon fill="black" stroke="black" points="244.5,-1110.1 241,-1100.1 237.5,-1110.1 244.5,-1110.1"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;5 -->
<g id="node10" class="node"><title>proc&#45;get_photo_userid_0&#45;5</title>
<polygon fill="#088cdb" fill-opacity="0.301961" stroke="black" points="395,-1028 87,-1028 87,-992 395,-992 395,-1028"/>
<text text-anchor="middle" x="241" y="-1006.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::enrich_by_generic_grpc_9AC04C</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;4&#45;&gt;proc&#45;get_photo_userid_0&#45;5 -->
<g id="edge7" class="edge"><title>proc&#45;get_photo_userid_0&#45;4&#45;&gt;proc&#45;get_photo_userid_0&#45;5</title>
<path fill="none" stroke="black" d="M241,-1063.7C241,-1055.98 241,-1046.71 241,-1038.11"/>
<polygon fill="black" stroke="black" points="244.5,-1038.1 241,-1028.1 237.5,-1038.1 244.5,-1038.1"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;6 -->
<g id="node11" class="node"><title>proc&#45;get_photo_userid_0&#45;6</title>
<polygon fill="white" stroke="black" points="387,-956 95,-956 95,-920 387,-920 387,-956"/>
<text text-anchor="middle" x="241" y="-934.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::enrich_with_protobuf_973A08</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;5&#45;&gt;proc&#45;get_photo_userid_0&#45;6 -->
<g id="edge8" class="edge"><title>proc&#45;get_photo_userid_0&#45;5&#45;&gt;proc&#45;get_photo_userid_0&#45;6</title>
<path fill="none" stroke="black" d="M241,-991.697C241,-983.983 241,-974.712 241,-966.112"/>
<polygon fill="black" stroke="black" points="244.5,-966.104 241,-956.104 237.5,-966.104 244.5,-966.104"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;7 -->
<g id="node12" class="node"><title>proc&#45;get_photo_userid_0&#45;7</title>
<polygon fill="white" stroke="black" points="429,-884 53,-884 53,-848 429,-848 429,-884"/>
<text text-anchor="middle" x="241" y="-862.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::build_table_from_common_list_attr_EF3AC9</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;6&#45;&gt;proc&#45;get_photo_userid_0&#45;7 -->
<g id="edge9" class="edge"><title>proc&#45;get_photo_userid_0&#45;6&#45;&gt;proc&#45;get_photo_userid_0&#45;7</title>
<path fill="none" stroke="black" d="M241,-919.697C241,-911.983 241,-902.712 241,-894.112"/>
<polygon fill="black" stroke="black" points="244.5,-894.104 241,-884.104 237.5,-894.104 244.5,-894.104"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;8 -->
<g id="node13" class="node"><title>proc&#45;get_photo_userid_0&#45;8</title>
<polygon fill="white" stroke="black" points="353.25,-812 128.75,-812 128.75,-776 353.25,-776 353.25,-812"/>
<text text-anchor="middle" x="241" y="-790.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::copy_attr_7BF6F5</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;7&#45;&gt;proc&#45;get_photo_userid_0&#45;8 -->
<g id="edge10" class="edge"><title>proc&#45;get_photo_userid_0&#45;7&#45;&gt;proc&#45;get_photo_userid_0&#45;8</title>
<path fill="none" stroke="black" d="M241,-847.697C241,-839.983 241,-830.712 241,-822.112"/>
<polygon fill="black" stroke="black" points="244.5,-822.104 241,-812.104 237.5,-822.104 244.5,-822.104"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;9 -->
<g id="node14" class="node"><title>proc&#45;get_photo_userid_0&#45;9</title>
<polygon fill="white" stroke="black" points="406,-740 76,-740 76,-704 406,-704 406,-740"/>
<text text-anchor="middle" x="241" y="-718.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::fill_protobuf_by_attr_enrich_5FC1A2</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;8&#45;&gt;proc&#45;get_photo_userid_0&#45;9 -->
<g id="edge11" class="edge"><title>proc&#45;get_photo_userid_0&#45;8&#45;&gt;proc&#45;get_photo_userid_0&#45;9</title>
<path fill="none" stroke="black" d="M241,-775.697C241,-767.983 241,-758.712 241,-750.112"/>
<polygon fill="black" stroke="black" points="244.5,-750.104 241,-740.104 237.5,-750.104 244.5,-750.104"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;10 -->
<g id="node15" class="node"><title>proc&#45;get_photo_userid_0&#45;10</title>
<polygon fill="white" stroke="black" points="354.25,-668 127.75,-668 127.75,-632 354.25,-632 354.25,-668"/>
<text text-anchor="middle" x="241" y="-646.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::copy_attr_4D5C55</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;9&#45;&gt;proc&#45;get_photo_userid_0&#45;10 -->
<g id="edge12" class="edge"><title>proc&#45;get_photo_userid_0&#45;9&#45;&gt;proc&#45;get_photo_userid_0&#45;10</title>
<path fill="none" stroke="black" d="M241,-703.697C241,-695.983 241,-686.712 241,-678.112"/>
<polygon fill="black" stroke="black" points="244.5,-678.104 241,-668.104 237.5,-678.104 244.5,-678.104"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;11 -->
<g id="node16" class="node"><title>proc&#45;get_photo_userid_0&#45;11</title>
<polygon fill="white" stroke="black" points="403.25,-596 78.75,-596 78.75,-560 403.25,-560 403.25,-596"/>
<text text-anchor="middle" x="241" y="-574.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::serialize_protobuf_message_081D58</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;10&#45;&gt;proc&#45;get_photo_userid_0&#45;11 -->
<g id="edge13" class="edge"><title>proc&#45;get_photo_userid_0&#45;10&#45;&gt;proc&#45;get_photo_userid_0&#45;11</title>
<path fill="none" stroke="black" d="M241,-631.697C241,-623.983 241,-614.712 241,-606.112"/>
<polygon fill="black" stroke="black" points="244.5,-606.104 241,-596.104 237.5,-606.104 244.5,-606.104"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;12 -->
<g id="node17" class="node"><title>proc&#45;get_photo_userid_0&#45;12</title>
<polygon fill="white" stroke="black" points="367.25,-524 114.75,-524 114.75,-488 367.25,-488 367.25,-524"/>
<text text-anchor="middle" x="241" y="-502.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::send_to_kafka_7D2683</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;11&#45;&gt;proc&#45;get_photo_userid_0&#45;12 -->
<g id="edge14" class="edge"><title>proc&#45;get_photo_userid_0&#45;11&#45;&gt;proc&#45;get_photo_userid_0&#45;12</title>
<path fill="none" stroke="black" d="M241,-559.697C241,-551.983 241,-542.712 241,-534.112"/>
<polygon fill="black" stroke="black" points="244.5,-534.104 241,-524.104 237.5,-534.104 244.5,-534.104"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;13 -->
<g id="node18" class="node"><title>proc&#45;get_photo_userid_0&#45;13</title>
<polygon fill="white" stroke="black" points="350,-452 132,-452 132,-416 350,-416 350,-452"/>
<text text-anchor="middle" x="241" y="-430.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::calc_time_cost_e</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;12&#45;&gt;proc&#45;get_photo_userid_0&#45;13 -->
<g id="edge15" class="edge"><title>proc&#45;get_photo_userid_0&#45;12&#45;&gt;proc&#45;get_photo_userid_0&#45;13</title>
<path fill="none" stroke="black" d="M241,-487.697C241,-479.983 241,-470.712 241,-462.112"/>
<polygon fill="black" stroke="black" points="244.5,-462.104 241,-452.104 237.5,-462.104 244.5,-462.104"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;14 -->
<g id="node19" class="node"><title>proc&#45;get_photo_userid_0&#45;14</title>
<polygon fill="white" stroke="black" points="344,-380 138,-380 138,-344 344,-344 344,-380"/>
<text text-anchor="middle" x="241" y="-358.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::calc_time_cost</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;13&#45;&gt;proc&#45;get_photo_userid_0&#45;14 -->
<g id="edge16" class="edge"><title>proc&#45;get_photo_userid_0&#45;13&#45;&gt;proc&#45;get_photo_userid_0&#45;14</title>
<path fill="none" stroke="black" d="M241,-415.697C241,-407.983 241,-398.712 241,-390.112"/>
<polygon fill="black" stroke="black" points="244.5,-390.104 241,-380.104 237.5,-390.104 244.5,-390.104"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;15 -->
<g id="node20" class="node"><title>proc&#45;get_photo_userid_0&#45;15</title>
<polygon fill="white" stroke="black" points="344,-308 138,-308 138,-272 344,-272 344,-308"/>
<text text-anchor="middle" x="241" y="-286.3" font-family="Times,serif" font-size="14.00">get_photo_user_id::perf_time_cost</text>
</g>
<!-- proc&#45;get_photo_userid_0&#45;14&#45;&gt;proc&#45;get_photo_userid_0&#45;15 -->
<g id="edge17" class="edge"><title>proc&#45;get_photo_userid_0&#45;14&#45;&gt;proc&#45;get_photo_userid_0&#45;15</title>
<path fill="none" stroke="black" d="M241,-343.697C241,-335.983 241,-326.712 241,-318.112"/>
<polygon fill="black" stroke="black" points="244.5,-318.104 241,-308.104 237.5,-318.104 244.5,-318.104"/>
</g>
<!-- proc&#45;get_photo_userid_0&#45;15&#45;&gt;flow_end&#45;get_photo_userid_0 -->
<g id="edge18" class="edge"><title>proc&#45;get_photo_userid_0&#45;15&#45;&gt;flow_end&#45;get_photo_userid_0</title>
<path fill="none" stroke="black" d="M241,-271.912C241,-263.746 241,-254.055 241,-246.155"/>
<polygon fill="black" stroke="black" points="244.5,-245.97 241,-235.97 237.5,-245.97 244.5,-245.97"/>
</g>
</g>
</svg>
