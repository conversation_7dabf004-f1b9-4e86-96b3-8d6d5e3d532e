{"_CONFIG_VERSION": "26d599717ec84e8053e06465439ee704_local", "_DRAGONFLY_CREATE_TIME": "2024-01-19 17:48:26", "_DRAGONFLY_VERSION": "0.7.20", "grpc": {"client_map": {"AdDpaProductServer": "grpc_adDpaProductServerImpl", "AdMaterialBaseInfoService": "grpc_adMaterialBaseInfoService", "CmdLineExecutorService": "grpc_editorSdkServiceForKaiYanCopy", "CreativeInformationGetterService": "grpc_adCreativeInformationGetterService", "CreativeProcessService": "ad-alliance-creative-process-uni-service", "DpOneServiceMmuHigh": "grpc_dpOneServiceMmuHigh", "DpaProductSearchDealer": "grpc_ad_product_search", "EcomItemInfoService": "grpc_adEcomProductItemInfoRpcService", "KwaiShopProductService": "kwaishop-product-detail-service", "KwaiShopProductServiceTest": "grpc_kwaishop-product-detail-service", "LiveCurrentLivingService": "grpc_liveExposeCurrentLivingRpcService", "LiveReservationReadServiceRpc": "grpc_liveReservationReadServiceRpc", "LiveStremInfoService": "grpc_adLiveInfoGetRpcService", "MediaProcessingJobServiceKey": "grpc_mediaProcessingJobService", "MmuPhotoMd5QueryServiceKey": "grpc_mmuPhotoMd5QueryService", "OcrDetectService": "grpc_ztOcrDetectService", "PhotoServiceKey": "grpc_apiCorePhotoService", "SellerTagInfoService": "grpc_kwaishopProductListAggregationService", "UniverseGenOptCtrInfService": "grpc_UniverseGenOptCTRInfRpcService", "UserTopPhotoService": "grpc_apiCoreUserTopPhotoService", "adLiveClipSelectVersionServiceKey": "grpc_adLiveClipOptSelectVersionService", "adTagRetrieveServerOfflineKey": "grpc_adTagRetrieveServer_offline", "forward_index_client": "ad-forward-index", "mmuAdEmbeddingServiceKey": "grpc_mmuAdEmbeddingService", "mmuDlFeatureCalculateServiceKey": "grpc_mmuDlFeatureCalculateService", "wideTableProductServiceKey": "wide-table-product-service"}, "server": {"kcs_grpc_port_key": "AUTO_PORT1", "kess_name": "USE_KSN_AS_SERVICE", "port": 20012, "quit_wait_seconds": 120, "thread_num": 400}, "test": false}, "kess_config": {}, "pipeline_manager_config": {"base_pipeline": {"processor": {"ad_item_info_collection::_branch_controller_195AF713": {"$branch_start": "ad_item_info_collection::_branch_controller_195AF713", "$code_info": "[if] 195AF713 ad_creative_data_collection_server.py in ad_item_info_collection(): .if_(\"data_type == 17\")", "$metadata": {"$input_common_attrs": ["data_type"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_13"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_13"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["data_type"], "lua_script": "function evaluate() if (data_type == 17) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::_branch_controller_1CB4C1AD": {"$branch_start": "ad_item_info_collection::_branch_controller_1CB4C1AD", "$code_info": "[if] 1CB4C1AD ad_creative_data_collection_server.py in ad_item_info_collection(): ).if_(\"data_type == 4 and msg_type == 0\")", "$metadata": {"$input_common_attrs": ["data_type", "msg_type"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_1"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_1"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["data_type", "msg_type"], "lua_script": "function evaluate() if (data_type == 4 and msg_type == 0) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::_branch_controller_370EF81D": {"$branch_start": "ad_item_info_collection::_branch_controller_370EF81D", "$code_info": "[if] 370EF81D common.py in return_if_value_zero(): \t\tself.if_(f\"{common_attr_name} == 0\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "account_user_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_7"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_7"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_1", "account_user_id"], "lua_script": "function evaluate() if (_if_control_attr_1 == 0 and (account_user_id == 0)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::_branch_controller_********": {"$branch_start": "ad_item_info_collection::_branch_controller_********", "$code_info": "[if] ******** ad_creative_data_collection_server.py in photo_item_user_collection(): .if_(\"merchant_item_type ~= 0\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "merchant_item_type"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_6"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_6"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_1", "merchant_item_type"], "lua_script": "function evaluate() if (_if_control_attr_1 == 0 and (merchant_item_type ~= 0)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::_branch_controller_6C333340": {"$branch_start": "ad_item_info_collection::_branch_controller_6C333340", "$code_info": "[if] 6C333340 ad_creative_data_collection_server.py in ad_item_info_collection(): .if_(\"data_type == 3\")", "$metadata": {"$input_common_attrs": ["data_type"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_9"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_9"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["data_type"], "lua_script": "function evaluate() if (data_type == 3) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::_branch_controller_6C80B438": {"$branch_start": "ad_item_info_collection::_branch_controller_6C80B438", "$code_info": "[if] 6C80B438 ad_creative_data_collection_server.py in photo_item_user_collection(): ).if_(\"photo_id == 0\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "photo_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_2"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_2"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_1", "photo_id"], "lua_script": "function evaluate() if (_if_control_attr_1 == 0 and (photo_id == 0)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::_branch_controller_A72FEE86": {"$branch_start": "ad_item_info_collection::_branch_controller_A72FEE86", "$code_info": "[if] A72FEE86 ad_creative_data_collection_server.py in photo_item_user_collection(): ).if_(\"item_id == 0\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "item_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_5"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_5"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_1", "item_id"], "lua_script": "function evaluate() if (_if_control_attr_1 == 0 and (item_id == 0)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::_branch_controller_A7C8C385": {"$branch_start": "ad_item_info_collection::_branch_controller_A7C8C385", "$code_info": "[if] A7C8C385 ad_creative_data_collection_server.py in photo_item_user_collection(): .if_(\"unit_id == 0\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "unit_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_3"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_3"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_1", "unit_id"], "lua_script": "function evaluate() if (_if_control_attr_1 == 0 and (unit_id == 0)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::_branch_controller_C2420FF6": {"$branch_start": "ad_item_info_collection::_branch_controller_C2420FF6", "$code_info": "[if] C2420FF6 common.py in return_if_value_zero(): \t\tself.if_(f\"{common_attr_name} == 0\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "photo_user_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_8"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_8"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_1", "photo_user_id"], "lua_script": "function evaluate() if (_if_control_attr_1 == 0 and (photo_user_id == 0)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::ad_instance_convert_1C0248": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "ad_instance"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "ad_instance_column": "ad_instance", "output_column_": "creative_msg", "skip": "{{_if_control_attr_1}}", "type": "Creative", "type_name": "AdInstanceConvertEnricher"}, "ad_item_info_collection::ad_material_fetch_message_from_kafka_enricher_849D40": {"$metadata": {"$input_common_attrs": ["ad_creative_data_collection_test", "default_data_prod", "kuaishou.consume.delay.ms=120000;"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["kafka_msg_fetch_timestamp"], "$output_item_attrs": []}, "candidate_kafka_group": "ad_creative_data_collection_test", "kafka_group": "ad_creative_data_collection", "kafka_params": "kuaishou.consume.delay.ms=120000;", "kafka_topic": "default_data_prod", "msg_timestamp_column": "kafka_msg_fetch_timestamp", "out_string_column": "trigger_msg", "type_name": "AdMaterialFetchMessageFromKafkaEnricher"}, "ad_item_info_collection::build_protobuf_0EC329": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "account_user_id", "item_id", "photo_id", "photo_user_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["custom_item_data_str"], "$output_item_attrs": []}, "as_string": true, "class_name": "kuaishou.ad.algorithm.AdCreativeEngineCustomPhotoItemInfo", "inputs": [{"common_attr": "item_id", "path": "item_id"}, {"common_attr": "photo_id", "path": "photo_id"}, {"common_attr": "account_user_id", "path": "item_user_id"}, {"common_attr": "photo_user_id", "path": "photo_user_id"}], "output_common_attr": "custom_item_data_str", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "ad_item_info_collection::build_protobuf_148C5B": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "unit_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["unit_id_info"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.forward_index.IdInfo", "inputs": [{"common_attr": "unit_id", "path": "unit_id"}], "output_common_attr": "unit_id_info", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "ad_item_info_collection::build_protobuf_3C9242": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "account_id_info"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_account_req"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.forward_index.GetAccountReq", "inputs": [{"append": true, "common_attr": "account_id_info", "path": "id_info"}], "output_common_attr": "get_account_req", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "ad_item_info_collection::build_protobuf_C46FC3": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "photo_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_info_request"], "$output_item_attrs": []}, "class_name": "kuaishou.negative.GetByPhotoIdsRequest", "inputs": [{"append": true, "common_attr": "photo_id", "path": "photo_id"}], "output_common_attr": "photo_info_request", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "ad_item_info_collection::build_protobuf_DAB14F": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "unit_id_info"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_unit_req"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.forward_index.GetUnitReq", "inputs": [{"append": true, "common_attr": "unit_id_info", "path": "id_info"}], "output_common_attr": "get_unit_req", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "ad_item_info_collection::build_protobuf_F6CD79": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "account_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["account_id_info"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.forward_index.IdInfo", "inputs": [{"common_attr": "account_id", "path": "account_id"}], "output_common_attr": "account_id_info", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "ad_item_info_collection::cache_check::_branch_controller_1D0BEC3B": {"$branch_start": "ad_item_info_collection::cache_check::_branch_controller_1D0BEC3B", "$code_info": "[if] 1D0BEC3B ad_creative_data_collection_server.py in cache_check(): ).if_(f\"{default_value_name} ~= 0\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_1", "cache_photo_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_4"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_4"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_1", "cache_photo_id"], "lua_script": "function evaluate() if (_if_control_attr_1 == 0 and (cache_photo_id ~= 0)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::cache_check::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "ad_item_info_collection__cache_checkTimeCostEnd", "ad_item_info_collection__cache_checkTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collection__cache_checkTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["ad_item_info_collection__cache_checkTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["ad_item_info_collection__cache_checkTimeCostEnd", "ad_item_info_collection__cache_checkTimeCostStart"], "lua_script": "function gen_common_attrs() return (ad_item_info_collection__cache_checkTimeCostEnd - ad_item_info_collection__cache_checkTimeCostStart) end", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::cache_check::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collection__cache_checkTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "ad_item_info_collection__cache_checkTimeCostEnd", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_item_info_collection::cache_check::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collection__cache_checkTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "ad_item_info_collection__cache_checkTimeCostStart", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_item_info_collection::cache_check::enrich_attr_by_lua_B31FCE": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "acg_photo_", "photo_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_cache_key", "photo_id_str"], "$output_item_attrs": []}, "export_common_attr": ["photo_cache_key", "photo_id_str"], "function_for_common": "gen<PERSON><PERSON>", "import_common_attr": ["acg_photo_", "photo_id"], "lua_script": "function genKey()\n          return \"acg_photo_\"..tostring(photo_id), tostring(photo_id)\n        end", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::cache_check::get_common_attr_from_redis_287AA9": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "photo_cache_key"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["cache_photo_id"], "$output_item_attrs": []}, "cluster_name": "adLiveMerchantInfo", "redis_params": [{"output_attr_name": "cache_photo_id", "output_attr_type": "int", "redis_key": "{{photo_cache_key}}", "redis_value_type": "string"}], "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoRedisCommonAttrEnricher"}, "ad_item_info_collection::cache_check::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "ad_item_info_collection__cache_checkTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:ad_item_info_collection__cache_check", "common_attrs": ["ad_item_info_collection__cache_checkTimeCost"], "perf_base": 1, "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "ad_item_info_collection::cache_check::perflog_141FCF41": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "hit_cache", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_4}}", "subtag": "creative_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::cache_check::return__04FED1": {"$metadata": {"$input_common_attrs": ["_if_control_attr_4"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_4}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "ad_item_info_collection::cache_check::set_attr_value_FEA432": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["cache_photo_id"], "$output_item_attrs": []}, "common_attrs": [{"name": "cache_photo_id", "type": "int", "value": 0}], "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoItemAttrDefaultValueEnricher"}, "ad_item_info_collection::cache_check::write_to_redis_6F3F23": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "photo_cache_key", "photo_id_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "downstream_processor": "__none", "expire_second": 60, "kcc_cluster": "adLiveMerchantInfo", "key": "{{photo_cache_key}}", "skip": "{{_if_control_attr_1}}", "timeout_ms": 10, "type_name": "CommonRecoWriteToRedisObserver", "value": "{{photo_id_str}}"}, "ad_item_info_collection::calc_time_cost": {"$metadata": {"$input_common_attrs": ["ad_item_info_collectionTimeCostEnd", "ad_item_info_collectionTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collectionTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["ad_item_info_collectionTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["ad_item_info_collectionTimeCostEnd", "ad_item_info_collectionTimeCostStart"], "lua_script": "function gen_common_attrs() return (ad_item_info_collectionTimeCostEnd - ad_item_info_collectionTimeCostStart) end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::calc_time_cost_e": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collectionTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "ad_item_info_collectionTimeCostEnd", "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_item_info_collection::calc_time_cost_s": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collectionTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "ad_item_info_collectionTimeCostStart", "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_item_info_collection::enrich_attr_by_lua_25699A": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "photo_user_ids"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_user_id"], "$output_item_attrs": []}, "export_common_attr": ["photo_user_id"], "function_for_common": "getFirstItem", "import_common_attr": ["photo_user_ids"], "lua_script": "function getFirstItem()\n      \t\tlocal len = #photo_user_ids\n\t\t\t\t\tif (len > 0)\n\t\t\t\t\tthen\n\t\t\t\t\treturn photo_user_ids[len]\n\t\t\t\t\telse\n\t\t\t\t\treturn 0\n\t\t\t\t\tend\n\t\t\t\tend", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::enrich_attr_by_lua_831712": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "item_ids", "merchant_item_types"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_id", "merchant_item_type"], "$output_item_attrs": []}, "export_common_attr": ["item_id", "merchant_item_type"], "function_for_common": "getFirstItem", "import_common_attr": ["item_ids", "merchant_item_types"], "lua_script": "function getFirstItem()\n          local len1 = #item_ids\n          local len2 = #merchant_item_types\n          local item_id = 0\n          local merchant_item_type = 0\n          if (len1 > 0) then item_id = item_ids[len1] end\n          if (len2 > 0) then merchant_item_type = merchant_item_types[len2] end\n          return item_id, merchant_item_type\n        end", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::enrich_attr_by_lua_D03336": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "account_user_ids"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["account_user_id"], "$output_item_attrs": []}, "export_common_attr": ["account_user_id"], "function_for_common": "getFirstItem", "import_common_attr": ["account_user_ids"], "lua_script": "function getFirstItem()\n      \t\tlocal len = #account_user_ids\n\t\t\t\t\tif (len > 0)\n\t\t\t\t\tthen\n\t\t\t\t\treturn account_user_ids[len]\n\t\t\t\t\telse\n\t\t\t\t\treturn 0\n\t\t\t\t\tend\n\t\t\t\tend", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::enrich_by_generic_grpc_9B2BC9": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "photo_info_request"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_info_response"], "$output_item_attrs": []}, "downstream_processor": "ad_item_info_collection::enrich_with_protobuf_513708", "kess_service": "grpc_apiCorePhotoService", "method_name": "/kuaishou.negative.PhotoServiceRpc/GetByIdsContainsDeleted", "request_attr": "photo_info_request", "response_attr": "photo_info_response", "response_class": "kuaishou.negative.PhotoMapResponse", "skip": "{{_if_control_attr_1}}", "timeout_ms": 1000, "type_name": "CommonRecoGenericGrpcEnricher"}, "ad_item_info_collection::enrich_by_generic_grpc_B3A660": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "get_account_req"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_account_resp"], "$output_item_attrs": []}, "downstream_processor": "ad_item_info_collection::enrich_with_protobuf_2D31FE", "kess_service": "grpc_adForwardIndexService_algorithm", "method_name": "/kuaishou.ad.forward_index.AdForwardIndexService/GetAccount", "request_attr": "get_account_req", "response_attr": "get_account_resp", "response_class": "kuaishou.ad.forward_index.GetAccountResp", "skip": "{{_if_control_attr_1}}", "timeout_ms": 100, "type_name": "CommonRecoGenericGrpcEnricher"}, "ad_item_info_collection::enrich_by_generic_grpc_E2F6DB": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "get_unit_req"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_unit_resp"], "$output_item_attrs": []}, "downstream_processor": "ad_item_info_collection::enrich_with_protobuf_74A9DD", "kess_service": "grpc_adForwardIndexService_algorithm", "method_name": "/kuaishou.ad.forward_index.AdForwardIndexService/GetUnit", "request_attr": "get_unit_req", "response_attr": "get_unit_resp", "response_class": "kuaishou.ad.forward_index.GetUnitResp", "skip": "{{_if_control_attr_1}}", "timeout_ms": 100, "type_name": "CommonRecoGenericGrpcEnricher"}, "ad_item_info_collection::enrich_with_protobuf_2D31FE": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "get_account_resp"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["account_user_ids"], "$output_item_attrs": []}, "attrs": [{"name": "account_user_ids", "path": "items.account.user_id"}], "from_extra_var": "get_account_resp", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "ad_item_info_collection::enrich_with_protobuf_513708": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "photo_info_response"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_user_ids"], "$output_item_attrs": []}, "attrs": [{"name": "photo_user_ids", "path": "photos.value.userId"}], "from_extra_var": "photo_info_response", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "ad_item_info_collection::enrich_with_protobuf_64241D": {"$metadata": {"$input_common_attrs": ["ad_instance"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["data_type", "msg_type"], "$output_item_attrs": []}, "attrs": [{"name": "data_type", "path": "type"}, {"name": "msg_type", "path": "msg_type"}], "from_extra_var": "ad_instance", "type_name": "CommonRecoProtobufAttrEnricher"}, "ad_item_info_collection::enrich_with_protobuf_74A9DD": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "get_unit_resp"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_ids", "merchant_item_types"], "$output_item_attrs": []}, "attrs": [{"name": "item_ids", "path": "items.unit.unit_small_shop_merchant_support_info.item_id"}, {"name": "merchant_item_types", "path": "items.unit.unit_small_shop_merchant_support_info.merchant_item_type"}], "from_extra_var": "get_unit_resp", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "ad_item_info_collection::enrich_with_protobuf_DD0B79": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "creative_msg"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["account_id", "photo_id", "unit_id"], "$output_item_attrs": []}, "attrs": [{"name": "photo_id", "path": "photo_id"}, {"name": "unit_id", "path": "unit_id"}, {"name": "account_id", "path": "account_id"}], "from_extra_var": "creative_msg", "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "ad_item_info_collection::item_user_collection::_branch_controller_274393C0": {"$branch_start": "ad_item_info_collection::item_user_collection::_branch_controller_274393C0", "$code_info": "[if] 274393C0 ad_creative_data_collection_server.py in item_user_collection(): .if_(\"merchant_item_type ~= 0\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_9", "merchant_item_type"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_11"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_11"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_9", "merchant_item_type"], "lua_script": "function evaluate() if (_if_control_attr_9 == 0 and (merchant_item_type ~= 0)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::item_user_collection::_branch_controller_9B26B75A": {"$branch_start": "ad_item_info_collection::item_user_collection::_branch_controller_9B26B75A", "$code_info": "[if] 9B26B75A ad_creative_data_collection_server.py in item_user_collection(): ).if_(\"item_id == 0\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_9", "item_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_10"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_10"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_9", "item_id"], "lua_script": "function evaluate() if (_if_control_attr_9 == 0 and (item_id == 0)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::item_user_collection::ad_instance_convert_6224EC": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "ad_instance"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "ad_instance_column": "ad_instance", "output_column_": "unit_msg", "skip": "{{_if_control_attr_9}}", "type": "Unit", "type_name": "AdInstanceConvertEnricher"}, "ad_item_info_collection::item_user_collection::build_protobuf_3C06C4": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "account_id_info"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_account_req"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.forward_index.GetAccountReq", "inputs": [{"append": true, "common_attr": "account_id_info", "path": "id_info"}], "output_common_attr": "get_account_req", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "ad_item_info_collection::item_user_collection::build_protobuf_891FED": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "account_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["account_id_info"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.forward_index.IdInfo", "inputs": [{"common_attr": "account_id", "path": "account_id"}], "output_common_attr": "account_id_info", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "ad_item_info_collection::item_user_collection::build_protobuf_CFC896": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "unit_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_data_info"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.algorithm.AdCreativeEngineItemDataInfo", "inputs": [{"common_attr": "unit_id", "path": "unit_id"}], "output_common_attr": "item_data_info", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "ad_item_info_collection::item_user_collection::build_protobuf_D1733C": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "account_user_id", "item_data_info", "item_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_data_str"], "$output_item_attrs": []}, "as_string": true, "class_name": "kuaishou.ad.algorithm.AdCreativeEngineItemTriggerInfo", "inputs": [{"common_attr": "item_id", "path": "item_id"}, {"common_attr": "account_user_id", "path": "user_id"}, {"append": true, "common_attr": "item_data_info", "path": "item_data_info"}], "output_common_attr": "item_data_str", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "ad_item_info_collection::item_user_collection::cache_check::_branch_controller_14EB408E": {"$branch_start": "ad_item_info_collection::item_user_collection::cache_check::_branch_controller_14EB408E", "$code_info": "[if] 14EB408E ad_creative_data_collection_server.py in cache_check(): ).if_(f\"{default_value_name} ~= 0\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_9", "cache_item_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_12"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_12"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_9", "cache_item_id"], "lua_script": "function evaluate() if (_if_control_attr_9 == 0 and (cache_item_id ~= 0)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::item_user_collection::cache_check::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "ad_item_info_collection__item_user_collection__cache_checkTimeCostEnd", "ad_item_info_collection__item_user_collection__cache_checkTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collection__item_user_collection__cache_checkTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["ad_item_info_collection__item_user_collection__cache_checkTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["ad_item_info_collection__item_user_collection__cache_checkTimeCostEnd", "ad_item_info_collection__item_user_collection__cache_checkTimeCostStart"], "lua_script": "function gen_common_attrs() return (ad_item_info_collection__item_user_collection__cache_checkTimeCostEnd - ad_item_info_collection__item_user_collection__cache_checkTimeCostStart) end", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::item_user_collection::cache_check::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collection__item_user_collection__cache_checkTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "ad_item_info_collection__item_user_collection__cache_checkTimeCostEnd", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_item_info_collection::item_user_collection::cache_check::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collection__item_user_collection__cache_checkTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "ad_item_info_collection__item_user_collection__cache_checkTimeCostStart", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_item_info_collection::item_user_collection::cache_check::enrich_attr_by_lua_697A77": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "acg_item_", "item_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_cache_key", "item_id_str"], "$output_item_attrs": []}, "export_common_attr": ["item_cache_key", "item_id_str"], "function_for_common": "gen<PERSON><PERSON>", "import_common_attr": ["acg_item_", "item_id"], "lua_script": "function genKey()\n          return \"acg_item_\"..tostring(item_id), tostring(item_id)\n        end", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::item_user_collection::cache_check::get_common_attr_from_redis_D05816": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "item_cache_key"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["cache_item_id"], "$output_item_attrs": []}, "cluster_name": "adLiveMerchantInfo", "redis_params": [{"output_attr_name": "cache_item_id", "output_attr_type": "int", "redis_key": "{{item_cache_key}}", "redis_value_type": "string"}], "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoRedisCommonAttrEnricher"}, "ad_item_info_collection::item_user_collection::cache_check::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "ad_item_info_collection__item_user_collection__cache_checkTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:ad_item_info_collection__item_user_collection__cache_check", "common_attrs": ["ad_item_info_collection__item_user_collection__cache_checkTimeCost"], "perf_base": 1, "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "ad_item_info_collection::item_user_collection::cache_check::perflog_6500F7F7": {"$metadata": {"$input_common_attrs": ["_if_control_attr_12"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "hit_cache", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_12}}", "subtag": "item_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::item_user_collection::cache_check::return__E3598F": {"$metadata": {"$input_common_attrs": ["_if_control_attr_12"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_12}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "ad_item_info_collection::item_user_collection::cache_check::set_attr_value_E6E07B": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["cache_item_id"], "$output_item_attrs": []}, "common_attrs": [{"name": "cache_item_id", "type": "int", "value": 0}], "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoItemAttrDefaultValueEnricher"}, "ad_item_info_collection::item_user_collection::cache_check::write_to_redis_B5AFEC": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "item_cache_key", "item_id_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "downstream_processor": "__none", "expire_second": 60, "kcc_cluster": "adLiveMerchantInfo", "key": "{{item_cache_key}}", "skip": "{{_if_control_attr_9}}", "timeout_ms": 10, "type_name": "CommonRecoWriteToRedisObserver", "value": "{{item_id_str}}"}, "ad_item_info_collection::item_user_collection::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "ad_item_info_collection__item_user_collectionTimeCostEnd", "ad_item_info_collection__item_user_collectionTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collection__item_user_collectionTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["ad_item_info_collection__item_user_collectionTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["ad_item_info_collection__item_user_collectionTimeCostEnd", "ad_item_info_collection__item_user_collectionTimeCostStart"], "lua_script": "function gen_common_attrs() return (ad_item_info_collection__item_user_collectionTimeCostEnd - ad_item_info_collection__item_user_collectionTimeCostStart) end", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::item_user_collection::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collection__item_user_collectionTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "ad_item_info_collection__item_user_collectionTimeCostEnd", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_item_info_collection::item_user_collection::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collection__item_user_collectionTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "ad_item_info_collection__item_user_collectionTimeCostStart", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_item_info_collection::item_user_collection::enrich_attr_by_lua_D46A7D": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "account_user_id", "user_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["is_equal"], "$output_item_attrs": []}, "export_common_attr": ["is_equal"], "function_for_common": "isEqual", "import_common_attr": ["account_user_id", "user_id"], "lua_script": "function isEqual()\n          if (account_user_id == user_id)\n          then\n            return 1\n          else\n            return 0 \n          end\n        end", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::item_user_collection::enrich_attr_by_lua_E54A01": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "account_user_ids"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["account_user_id"], "$output_item_attrs": []}, "export_common_attr": ["account_user_id"], "function_for_common": "getFirstItem", "import_common_attr": ["account_user_ids"], "lua_script": "function getFirstItem()\n          local len = #account_user_ids\n          if (len > 0)\n          then\n            return account_user_ids[len]\n          else\n            return 0\n          end\n        end", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::item_user_collection::enrich_by_generic_grpc_EEE934": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "get_account_req"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_account_resp"], "$output_item_attrs": []}, "downstream_processor": "ad_item_info_collection::item_user_collection::enrich_with_protobuf_E1BE7C", "kess_service": "grpc_adForwardIndexService_algorithm", "method_name": "/kuaishou.ad.forward_index.AdForwardIndexService/GetAccount", "request_attr": "get_account_req", "response_attr": "get_account_resp", "response_class": "kuaishou.ad.forward_index.GetAccountResp", "skip": "{{_if_control_attr_9}}", "timeout_ms": 100, "type_name": "CommonRecoGenericGrpcEnricher"}, "ad_item_info_collection::item_user_collection::enrich_with_protobuf_83C10F": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "unit_msg"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["account_id", "item_id", "merchant_item_type", "unit_id", "user_id"], "$output_item_attrs": []}, "attrs": [{"name": "item_id", "path": "unit_small_shop_merchant_support_info.item_id"}, {"name": "unit_id", "path": "id"}, {"name": "account_id", "path": "account_id"}, {"name": "user_id", "path": "unit_support_info.live_user_id"}, {"name": "merchant_item_type", "path": "unit_small_shop_merchant_support_info.merchant_item_type"}], "from_extra_var": "unit_msg", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "ad_item_info_collection::item_user_collection::enrich_with_protobuf_E1BE7C": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "get_account_resp"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["account_user_ids"], "$output_item_attrs": []}, "attrs": [{"name": "account_user_ids", "path": "items.account.user_id"}], "from_extra_var": "get_account_resp", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "ad_item_info_collection::item_user_collection::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "ad_item_info_collection__item_user_collectionTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:ad_item_info_collection__item_user_collection", "common_attrs": ["ad_item_info_collection__item_user_collectionTimeCost"], "perf_base": 1, "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "ad_item_info_collection::item_user_collection::perflog_39234151": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "is_equal"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "user_id_equal", "extra2": "{{is_equal}}", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_9}}", "subtag": "item_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::item_user_collection::perflog_7EDF6DEC": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "item_id_zero", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_10}}", "subtag": "item_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::item_user_collection::perflog_814A6078": {"$metadata": {"$input_common_attrs": ["_if_control_attr_11"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "merchant_item_type_invalid", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_11}}", "subtag": "item_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::item_user_collection::perflog_90424AB3": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "output_num", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_9}}", "subtag": "item_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::item_user_collection::perflog_F57E9461": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "all_unit_inc_data", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_9}}", "subtag": "item_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::item_user_collection::return__56237E": {"$metadata": {"$input_common_attrs": ["_if_control_attr_11"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_11}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "ad_item_info_collection::item_user_collection::return__9215A4": {"$metadata": {"$input_common_attrs": ["_if_control_attr_10"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_10}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "ad_item_info_collection::item_user_collection::send_to_kafka_918FE1": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9", "item_data_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "is_common_attr": true, "message_column": "item_data_str", "skip": "{{_if_control_attr_9}}", "topic": "ad_creative_item_collection_message", "type_name": "SendToKafkaObserver"}, "ad_item_info_collection::item_user_collection::set_attr_value_EE28E3": {"$metadata": {"$input_common_attrs": ["_if_control_attr_9"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_id"], "$output_item_attrs": []}, "common_attrs": [{"name": "item_id", "type": "int", "value": 0}], "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoItemAttrDefaultValueEnricher"}, "ad_item_info_collection::new_photo_collection::_branch_controller_A362652F": {"$branch_start": "ad_item_info_collection::new_photo_collection::_branch_controller_A362652F", "$code_info": "[if] A362652F ad_creative_data_collection_server.py in new_photo_collection(): ).if_(\"photo_valid == 1\")", "$metadata": {"$input_common_attrs": ["_if_control_attr_13", "photo_valid"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["_if_control_attr_14"], "$output_item_attrs": []}, "export_common_attr": ["_if_control_attr_14"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_13", "photo_valid"], "lua_script": "function evaluate() if (_if_control_attr_13 == 0 and (photo_valid == 1)) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::new_photo_collection::ad_instance_convert_713BC9": {"$metadata": {"$input_common_attrs": ["_if_control_attr_13", "ad_instance"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "ad_instance_column": "ad_instance", "output_column_": "photo_msg", "skip": "{{_if_control_attr_13}}", "type": "Photo", "type_name": "AdInstanceConvertEnricher"}, "ad_item_info_collection::new_photo_collection::build_protobuf_1B1748": {"$metadata": {"$input_common_attrs": ["_if_control_attr_14", "create_time"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_dup_data_info"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.algorithm.AdDupDataInfo", "inputs": [{"common_attr": "create_time", "path": "create_time"}], "output_common_attr": "ad_dup_data_info", "skip": "{{_if_control_attr_14}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "ad_item_info_collection::new_photo_collection::build_protobuf_5AFE9D": {"$metadata": {"$input_common_attrs": ["_if_control_attr_14", "ad_dup_data_info", "photo_id"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_info_str"], "$output_item_attrs": []}, "as_string": true, "class_name": "kuaishou.ad.algorithm.AdDupPhotoBatchTriggerInfo", "inputs": [{"common_attr": "photo_id", "path": "photo_id"}, {"append": true, "common_attr": "ad_dup_data_info", "path": "ad_dup_data_info"}], "output_common_attr": "photo_info_str", "skip": "{{_if_control_attr_14}}", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "ad_item_info_collection::new_photo_collection::calc_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_13", "ad_item_info_collection__new_photo_collectionTimeCostEnd", "ad_item_info_collection__new_photo_collectionTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collection__new_photo_collectionTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["ad_item_info_collection__new_photo_collectionTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["ad_item_info_collection__new_photo_collectionTimeCostEnd", "ad_item_info_collection__new_photo_collectionTimeCostStart"], "lua_script": "function gen_common_attrs() return (ad_item_info_collection__new_photo_collectionTimeCostEnd - ad_item_info_collection__new_photo_collectionTimeCostStart) end", "skip": "{{_if_control_attr_13}}", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::new_photo_collection::calc_time_cost_e": {"$metadata": {"$input_common_attrs": ["_if_control_attr_13"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collection__new_photo_collectionTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "ad_item_info_collection__new_photo_collectionTimeCostEnd", "skip": "{{_if_control_attr_13}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_item_info_collection::new_photo_collection::calc_time_cost_s": {"$metadata": {"$input_common_attrs": ["_if_control_attr_13"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_item_info_collection__new_photo_collectionTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "ad_item_info_collection__new_photo_collectionTimeCostStart", "skip": "{{_if_control_attr_13}}", "type_name": "CommonRecoUserMetaInfoEnricher"}, "ad_item_info_collection::new_photo_collection::enrich_attr_by_lua_17697D": {"$metadata": {"$input_common_attrs": ["_if_control_attr_13", "first_audit_duration", "first_index_duration"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_valid"], "$output_item_attrs": []}, "export_common_attr": ["photo_valid"], "function_for_common": "<PERSON><PERSON><PERSON><PERSON>", "import_common_attr": ["first_audit_duration", "first_index_duration"], "lua_script": "function validCheck()\n          if first_audit_duration < 24 then\n            return 1\n          elseif first_index_duration < 24 then\n            return 1\n          else \n            return 0\n          end\n        end", "skip": "{{_if_control_attr_13}}", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::new_photo_collection::enrich_attr_by_lua_95C190": {"$metadata": {"$input_common_attrs": ["_if_control_attr_13", "first_audit_passtime", "first_index_time", "photo_upload_time", "recently_used_time"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["first_audit_duration", "first_index_duration", "photo_upload_duration", "recently_used_duration"], "$output_item_attrs": []}, "export_common_attr": ["first_audit_duration", "first_index_duration", "photo_upload_duration", "recently_used_duration"], "function_for_common": "getDuration", "import_common_attr": ["first_audit_passtime", "first_index_time", "photo_upload_time", "recently_used_time"], "lua_script": "function getDuration()\n          local now = util.GetTimestamp() // 1000\n          return (now - first_audit_passtime) // 3600000, (now - first_index_time) // 3600000, (now - photo_upload_time) // 3600000, (now - recently_used_time) // 3600000\n        end", "skip": "{{_if_control_attr_13}}", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::new_photo_collection::enrich_attr_by_lua_CD430F": {"$metadata": {"$input_common_attrs": ["_if_control_attr_14", "first_audit_passtime", "photo_upload_time"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["create_time"], "$output_item_attrs": []}, "export_common_attr": ["create_time"], "function_for_common": "getCreateTime", "import_common_attr": ["first_audit_passtime", "photo_upload_time"], "lua_script": "function getCreateTime()\n          if first_audit_passtime > 0 then\n            return first_audit_passtime\n          elseif photo_upload_time > 0 then\n            return photo_upload_time\n          else\n            return util.GetTimestamp() // 1000\n          end\n        end", "skip": "{{_if_control_attr_14}}", "type_name": "CommonRecoLuaAttrEnricher"}, "ad_item_info_collection::new_photo_collection::enrich_with_protobuf_97442D": {"$metadata": {"$input_common_attrs": ["_if_control_attr_13", "photo_msg"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["first_audit_passtime", "first_index_time", "photo_id", "photo_upload_time", "recently_used_time"], "$output_item_attrs": []}, "attrs": [{"name": "photo_id", "path": "photo_id"}, {"name": "first_audit_passtime", "path": "first_audit_passtime"}, {"name": "first_index_time", "path": "first_index_time"}, {"name": "photo_upload_time", "path": "photo_upload_time"}, {"name": "recently_used_time", "path": "recently_used_time"}], "from_extra_var": "photo_msg", "skip": "{{_if_control_attr_13}}", "type_name": "CommonRecoProtobufAttrEnricher"}, "ad_item_info_collection::new_photo_collection::log_debug_info_90C44E": {"$metadata": {"$input_common_attrs": ["_if_control_attr_14", "first_audit_duration", "first_audit_passtime", "first_index_duration", "first_index_time", "photo_id", "photo_info_str", "photo_upload_duration", "photo_upload_time", "photo_valid", "recently_used_duration", "recently_used_time"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "common_attrs": ["photo_id", "first_audit_passtime", "first_index_time", "photo_upload_time", "recently_used_time", "first_audit_duration", "first_index_duration", "recently_used_duration", "photo_upload_duration", "photo_valid", "photo_info_str"], "for_debug_request_only": false, "item_attrs": [""], "skip": "{{_if_control_attr_14}}", "type_name": "CommonRecoDebugInfoObserver"}, "ad_item_info_collection::new_photo_collection::perf_time_cost": {"$metadata": {"$input_common_attrs": ["_if_control_attr_13", "ad_item_info_collection__new_photo_collectionTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:ad_item_info_collection__new_photo_collection", "common_attrs": ["ad_item_info_collection__new_photo_collectionTimeCost"], "perf_base": 1, "skip": "{{_if_control_attr_13}}", "type_name": "CommonRecoAttrValuePerflogObserver"}, "ad_item_info_collection::new_photo_collection::perflog_38C6D571": {"$metadata": {"$input_common_attrs": ["_if_control_attr_13"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "all_photo_inc_data", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_13}}", "subtag": "photo_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::new_photo_collection::perflog_F6452FB4": {"$metadata": {"$input_common_attrs": ["_if_control_attr_14"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "valid_photo_num", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_14}}", "subtag": "photo_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::new_photo_collection::send_to_kafka_5DB1B4": {"$metadata": {"$input_common_attrs": ["_if_control_attr_14", "photo_info_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "is_common_attr": true, "message_column": "photo_info_str", "skip": "{{_if_control_attr_14}}", "topic": "ad_creative_audit_valid_photo_inc_message", "type_name": "SendToKafkaObserver"}, "ad_item_info_collection::parse_protobuf_from_string_E658E6": {"$metadata": {"$input_common_attrs": ["trigger_msg"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["ad_instance"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.AdInstance", "input_attr": "trigger_msg", "output_attr": "ad_instance", "type_name": "CommonRecoProtobufParseAttrEnricher"}, "ad_item_info_collection::perf_time_cost": {"$metadata": {"$input_common_attrs": ["ad_item_info_collectionTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:ad_item_info_collection", "common_attrs": ["ad_item_info_collectionTimeCost"], "perf_base": 1, "type_name": "CommonRecoAttrValuePerflogObserver"}, "ad_item_info_collection::perflog_039E7665": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "get_account_id_succ", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_1}}", "subtag": "creative_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::perflog_05FDB71B": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "photo_num_after_cache", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_1}}", "subtag": "creative_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::perflog_1A440E82": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "get_photo_id_succ", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_1}}", "subtag": "creative_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::perflog_2B40DF8D": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "get_item_id_succ", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_1}}", "subtag": "creative_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::perflog_2BC15FF3": {"$metadata": {"$input_common_attrs": ["_if_control_attr_6"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "merchant_item_type_invalid", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_6}}", "subtag": "creative_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::perflog_A0BF7AA4": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "unit_id_zero", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_3}}", "subtag": "creative_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::perflog_BABA8506": {"$metadata": {"$input_common_attrs": ["_if_control_attr_2"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "photo_id_zero", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_2}}", "subtag": "creative_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::perflog_BC187182": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "all_creative_inc_data", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_1}}", "subtag": "creative_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::perflog_C00ED652": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "extra1": "item_id_zero", "mode": "count", "namespace": "ad.ad_creative_data_collection_server", "skip": "{{_if_control_attr_5}}", "subtag": "creative_admit", "type_name": "CommonRecoPerflogObserver"}, "ad_item_info_collection::return__16C414": {"$metadata": {"$input_common_attrs": ["_if_control_attr_3"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_3}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "ad_item_info_collection::return__6A0259": {"$metadata": {"$input_common_attrs": ["_if_control_attr_2"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_2}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "ad_item_info_collection::return__70B3DF": {"$metadata": {"$input_common_attrs": ["_if_control_attr_8"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_8}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "ad_item_info_collection::return__B920B9": {"$metadata": {"$input_common_attrs": ["_if_control_attr_7"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_7}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "ad_item_info_collection::return__D7A377": {"$metadata": {"$input_common_attrs": ["_if_control_attr_5"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_5}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "ad_item_info_collection::return__EDCBDA": {"$metadata": {"$input_common_attrs": ["_if_control_attr_6"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "skip": "{{_if_control_attr_6}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "ad_item_info_collection::send_to_kafka_2395FA": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1", "custom_item_data_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "is_common_attr": true, "message_column": "custom_item_data_str", "skip": "{{_if_control_attr_1}}", "topic": "ad_creative_custom_photo_item_info", "type_name": "SendToKafkaObserver"}, "ad_item_info_collection::set_attr_value_B0F4D0": {"$metadata": {"$input_common_attrs": ["_if_control_attr_1"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["item_id", "merchant_item_type", "photo_id"], "$output_item_attrs": []}, "common_attrs": [{"name": "photo_id", "type": "int", "value": 0}, {"name": "item_id", "type": "int", "value": 0}, {"name": "merchant_item_type", "type": "int", "value": 0}], "skip": "{{_if_control_attr_1}}", "type_name": "CommonRecoItemAttrDefaultValueEnricher"}, "build_protobuf_1EE1A7": {"$metadata": {"$input_common_attrs": ["photo_attr", "photo_create_time_attr", "request_num", "similar_list_type_attr"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["similar_list_req"], "$output_item_attrs": []}, "class_name": "ks.platform.CommonRecoRequest", "inputs": [{"common_attr": "request_num", "path": "request_num"}, {"append": true, "common_attr": "photo_attr", "path": "common_attr"}, {"append": true, "common_attr": "similar_list_type_attr", "path": "common_attr"}, {"append": true, "common_attr": "photo_create_time_attr", "path": "common_attr"}], "output_common_attr": "similar_list_req", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "build_protobuf_6834AA": {"$metadata": {"$input_common_attrs": ["enable_use_cache_attr", "photo_attr", "request_num", "retrieval_data_type_attr", "valid_duration_attr"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["retrieval_req"], "$output_item_attrs": []}, "class_name": "ks.platform.CommonRecoRequest", "inputs": [{"common_attr": "request_num", "path": "request_num"}, {"append": true, "common_attr": "photo_attr", "path": "common_attr"}, {"append": true, "common_attr": "retrieval_data_type_attr", "path": "common_attr"}, {"append": true, "common_attr": "enable_use_cache_attr", "path": "common_attr"}, {"append": true, "common_attr": "valid_duration_attr", "path": "common_attr"}], "output_common_attr": "retrieval_req", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "build_protobuf_75BDB6": {"$metadata": {"$input_common_attrs": ["int_type_num", "photo_id", "photo_id_name"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_attr"], "$output_item_attrs": []}, "class_name": "kuiba.SampleAttr", "inputs": [{"common_attr": "int_type_num", "path": "type"}, {"common_attr": "photo_id_name", "path": "name"}, {"common_attr": "photo_id", "path": "int_value"}], "output_common_attr": "photo_attr", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "build_protobuf_A3032B": {"$metadata": {"$input_common_attrs": ["enable_use_cache_attr", "photo_attr", "rank_data_type_attr", "request_num", "valid_duration_attr"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["rank_req"], "$output_item_attrs": []}, "class_name": "ks.platform.CommonRecoRequest", "inputs": [{"common_attr": "request_num", "path": "request_num"}, {"append": true, "common_attr": "photo_attr", "path": "common_attr"}, {"append": true, "common_attr": "rank_data_type_attr", "path": "common_attr"}, {"append": true, "common_attr": "enable_use_cache_attr", "path": "common_attr"}, {"append": true, "common_attr": "valid_duration_attr", "path": "common_attr"}], "output_common_attr": "rank_req", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "build_protobuf_AB201A": {"$metadata": {"$input_common_attrs": ["enable_use_cache", "enable_use_cache_name", "int_type_num"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["enable_use_cache_attr"], "$output_item_attrs": []}, "class_name": "kuiba.SampleAttr", "inputs": [{"common_attr": "int_type_num", "path": "type"}, {"common_attr": "enable_use_cache_name", "path": "name"}, {"common_attr": "enable_use_cache", "path": "int_value"}], "output_common_attr": "enable_use_cache_attr", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "build_protobuf_B75AB6": {"$metadata": {"$input_common_attrs": ["data_type_name", "rank_list_name", "string_type_num"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["rank_data_type_attr"], "$output_item_attrs": []}, "class_name": "kuiba.SampleAttr", "inputs": [{"common_attr": "string_type_num", "path": "type"}, {"common_attr": "data_type_name", "path": "name"}, {"common_attr": "rank_list_name", "path": "string_value"}], "output_common_attr": "rank_data_type_attr", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "build_protobuf_D94392": {"$metadata": {"$input_common_attrs": ["data_type_name", "retrieval_list_name", "string_type_num"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["retrieval_data_type_attr"], "$output_item_attrs": []}, "class_name": "kuiba.SampleAttr", "inputs": [{"common_attr": "string_type_num", "path": "type"}, {"common_attr": "data_type_name", "path": "name"}, {"common_attr": "retrieval_list_name", "path": "string_value"}], "output_common_attr": "retrieval_data_type_attr", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "build_protobuf_DACA72": {"$metadata": {"$input_common_attrs": ["data_type_name", "similar_list_name", "string_type_num"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["similar_list_type_attr"], "$output_item_attrs": []}, "class_name": "kuiba.SampleAttr", "inputs": [{"common_attr": "string_type_num", "path": "type"}, {"common_attr": "data_type_name", "path": "name"}, {"common_attr": "similar_list_name", "path": "string_value"}], "output_common_attr": "similar_list_type_attr", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "build_protobuf_DB58DD": {"$metadata": {"$input_common_attrs": ["int_type_num", "valid_duration", "valid_duration_name"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["valid_duration_attr"], "$output_item_attrs": []}, "class_name": "kuiba.SampleAttr", "inputs": [{"common_attr": "int_type_num", "path": "type"}, {"common_attr": "valid_duration_name", "path": "name"}, {"common_attr": "valid_duration", "path": "int_value"}], "output_common_attr": "valid_duration_attr", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "build_protobuf_E904A1": {"$metadata": {"$input_common_attrs": ["int_type_num", "photo_create_time", "photo_create_time_name"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_create_time_attr"], "$output_item_attrs": []}, "class_name": "kuiba.SampleAttr", "inputs": [{"common_attr": "int_type_num", "path": "type"}, {"common_attr": "photo_create_time_name", "path": "name"}, {"common_attr": "photo_create_time", "path": "int_value"}], "output_common_attr": "photo_create_time_attr", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "enrich_attr_by_lua_097CC9": {"$metadata": {"$input_common_attrs": ["photo_id_list"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_id"], "$output_item_attrs": []}, "export_common_attr": ["photo_id"], "function_for_common": "getRandomPhotoId", "import_common_attr": ["photo_id_list"], "lua_script": "function getRandomPhotoId()\n          local idx = math.floor(#photo_id_list * util.Random())\n          if idx == 0 and #photo_id_list > 0 then \n            return photo_id_list[#photo_id_list]\n          else \n            return photo_id_list[idx]\n          end\n        end", "type_name": "CommonRecoLuaAttrEnricher"}, "enrich_attr_by_lua_8708F3": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_create_time"], "$output_item_attrs": []}, "export_common_attr": ["photo_create_time"], "function_for_common": "getTimeStamp", "lua_script": "function getTimeStamp()\n            return util.GetTimestamp() // 1000\n          end", "type_name": "CommonRecoLuaAttrEnricher"}, "enrich_by_generic_grpc_2AD8C9": {"$metadata": {"$input_common_attrs": ["retrieval_req"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["retrieval_resp"], "$output_item_attrs": []}, "downstream_processor": "log_debug_info_BB7CD8", "kess_service": "ad-dup-info-query-server", "method_name": "/ks.platform.CommonRecoLeafService/Recommend", "request_attr": "retrieval_req", "response_attr": "retrieval_resp", "response_class": "ks.platform.CommonRecoResponse", "timeout_ms": 10000, "type_name": "CommonRecoGenericGrpcEnricher"}, "enrich_by_generic_grpc_6A38B4": {"$metadata": {"$input_common_attrs": ["rank_req"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["rank_resp"], "$output_item_attrs": []}, "downstream_processor": "log_debug_info_BB7CD8", "kess_service": "ad-dup-info-query-server", "method_name": "/ks.platform.CommonRecoLeafService/Recommend", "request_attr": "rank_req", "response_attr": "rank_resp", "response_class": "ks.platform.CommonRecoResponse", "timeout_ms": 10000, "type_name": "CommonRecoGenericGrpcEnricher"}, "enrich_by_generic_grpc_961F78": {"$metadata": {"$input_common_attrs": ["similar_list_req"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["similar_list_resp"], "$output_item_attrs": []}, "downstream_processor": "log_debug_info_EC01A7", "kess_service": "ad-dup-info-query-server", "method_name": "/ks.platform.CommonRecoLeafService/Recommend", "request_attr": "similar_list_req", "response_attr": "similar_list_resp", "response_class": "ks.platform.CommonRecoResponse", "timeout_ms": 10000, "type_name": "CommonRecoGenericGrpcEnricher"}, "get_kconf_params_47BE2A": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_id_list"], "$output_item_attrs": []}, "kconf_configs": [{"export_common_attr": "photo_id_list", "kconf_key": "ad.adCreativeEngine.debugDupInfoQueryPhotoIdList", "value_type": "list_int64"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "get_photo_user_id::ad_material_fetch_message_from_kafka_enricher_8C95EA": {"$metadata": {"$input_common_attrs": ["ad_creative_data_collection_test", "dragon_dup_photo_batch_trigger_mq", "kuaishou.consume.delay.ms=0;"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["kafka_msg_fetch_timestamp"], "$output_item_attrs": []}, "candidate_kafka_group": "ad_creative_data_collection_test", "kafka_group": "ad_creative_data_collection", "kafka_params": "kuaishou.consume.delay.ms=0;", "kafka_topic": "dragon_dup_photo_batch_trigger_mq", "msg_timestamp_column": "kafka_msg_fetch_timestamp", "out_string_column": "trigger_msg", "type_name": "AdMaterialFetchMessageFromKafkaEnricher"}, "get_photo_user_id::build_protobuf_751164": {"$metadata": {"$input_common_attrs": ["photo_id", "timeout"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_info_request"], "$output_item_attrs": []}, "class_name": "kuaishou.negative.GetByPhotoIdsRequest", "inputs": [{"append": true, "common_attr": "photo_id", "path": "photo_id"}, {"common_attr": "timeout", "path": "timeout"}], "output_common_attr": "photo_info_request", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "get_photo_user_id::build_table_from_common_list_attr_EF3AC9": {"$metadata": {"$input_common_attrs": ["photo_ids", "user_ids"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["photo_id", "user_id"]}, "build_config": [{"dest": "photo_id", "src": "photo_ids"}, {"dest": "user_id", "src": "user_ids"}], "new_table": "photo_info_table", "type_name": "BuildNewTableFromCommonListAttrRetriever"}, "get_photo_user_id::calc_time_cost": {"$metadata": {"$input_common_attrs": ["get_photo_user_idTimeCostEnd", "get_photo_user_idTimeCostStart"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_photo_user_idTimeCost"], "$output_item_attrs": []}, "export_common_attr": ["get_photo_user_idTimeCost"], "function_for_common": "gen_common_attrs", "import_common_attr": ["get_photo_user_idTimeCostEnd", "get_photo_user_idTimeCostStart"], "lua_script": "function gen_common_attrs() return (get_photo_user_idTimeCostEnd - get_photo_user_idTimeCostStart) end", "type_name": "CommonRecoLuaAttrEnricher"}, "get_photo_user_id::calc_time_cost_e": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_photo_user_idTimeCostEnd"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_photo_user_idTimeCostEnd", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_photo_user_id::calc_time_cost_s": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["get_photo_user_idTimeCostStart"], "$output_item_attrs": []}, "save_elapsed_time_to_attr": "get_photo_user_idTimeCostStart", "type_name": "CommonRecoUserMetaInfoEnricher"}, "get_photo_user_id::copy_attr_4D5C55": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": ["photo_trigger_info"], "$modify_item_tables": [], "$output_common_attrs": ["photo_trigger_info_new"], "$output_item_attrs": []}, "attrs": [{"from_item": "photo_trigger_info", "to_common": "photo_trigger_info_new"}], "item_table": "photo_info_table", "type_name": "CommonRecoCopyAttrEnricher"}, "get_photo_user_id::copy_attr_7BF6F5": {"$metadata": {"$input_common_attrs": ["photo_trigger_info"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": ["photo_trigger_info"]}, "attrs": [{"from_common": "photo_trigger_info", "to_item": "photo_trigger_info"}], "item_table": "photo_info_table", "type_name": "CommonRecoCopyAttrEnricher"}, "get_photo_user_id::enrich_by_generic_grpc_9AC04C": {"$metadata": {"$input_common_attrs": ["photo_info_request"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_info_response"], "$output_item_attrs": []}, "downstream_processor": "get_photo_user_id::enrich_with_protobuf_973A08", "kess_service": "grpc_apiCorePhotoService", "method_name": "/kuaishou.negative.PhotoServiceRpc/GetByIdsContainsDeleted", "request_attr": "photo_info_request", "response_attr": "photo_info_response", "response_class": "kuaishou.negative.PhotoMapResponse", "timeout_ms": 1000, "type_name": "CommonRecoGenericGrpcEnricher"}, "get_photo_user_id::enrich_with_protobuf_973A08": {"$metadata": {"$input_common_attrs": ["photo_info_response"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_ids", "user_ids"], "$output_item_attrs": []}, "attrs": [{"name": "photo_ids", "path": "photos.key"}, {"name": "user_ids", "path": "photos.value.userId"}], "from_extra_var": "photo_info_response", "type_name": "CommonRecoProtobufAttrEnricher"}, "get_photo_user_id::enrich_with_protobuf_FA84A7": {"$metadata": {"$input_common_attrs": ["photo_trigger_info"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_id"], "$output_item_attrs": []}, "attrs": [{"name": "photo_id", "path": "photo_id"}], "from_extra_var": "photo_trigger_info", "type_name": "CommonRecoProtobufAttrEnricher"}, "get_photo_user_id::fill_protobuf_by_attr_enrich_5FC1A2": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "input_attr_column": "photo_trigger_info", "inputs": [{"item_attr": "user_id", "path": "user_id"}], "item_table": "photo_info_table", "type_name": "FillProtobufFieldEnricher"}, "get_photo_user_id::parse_protobuf_from_string_B6DAD0": {"$metadata": {"$input_common_attrs": ["trigger_msg"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_trigger_info"], "$output_item_attrs": []}, "class_name": "kuaishou.ad.algorithm.AdDupPhotoBatchTriggerInfo", "input_attr": "trigger_msg", "output_attr": "photo_trigger_info", "type_name": "CommonRecoProtobufParseAttrEnricher"}, "get_photo_user_id::perf_time_cost": {"$metadata": {"$input_common_attrs": ["get_photo_user_idTimeCost"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "check_point": "module:get_photo_user_id", "common_attrs": ["get_photo_user_idTimeCost"], "perf_base": 1, "type_name": "CommonRecoAttrValuePerflogObserver"}, "get_photo_user_id::send_to_kafka_7D2683": {"$metadata": {"$input_common_attrs": ["photo_trigger_info_new_str"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "is_common_attr": true, "message_column": "photo_trigger_info_new_str", "topic": "ad_creative_photo_data_collection_message", "type_name": "SendToKafkaObserver"}, "get_photo_user_id::serialize_protobuf_message_081D58": {"$metadata": {"$input_common_attrs": ["photo_trigger_info_new"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["photo_trigger_info_new_str"], "$output_item_attrs": []}, "from_common_attr": "photo_trigger_info_new", "serialize_to_common_attr": "photo_trigger_info_new_str", "type_name": "CommonRecoProtobufSerializeAttrEnricher"}, "log_debug_info_BB7CD8": {"$metadata": {"$input_common_attrs": ["rank_req", "rank_resp", "retrieval_req", "retrieval_resp"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "common_attrs": ["retrieval_req", "retrieval_resp", "rank_req", "rank_resp"], "for_debug_request_only": false, "item_attrs": [""], "type_name": "CommonRecoDebugInfoObserver"}, "log_debug_info_EC01A7": {"$metadata": {"$input_common_attrs": ["similar_list_req", "similar_list_resp"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "common_attrs": ["similar_list_req", "similar_list_resp"], "for_debug_request_only": false, "item_attrs": [""], "type_name": "CommonRecoDebugInfoObserver"}, "set_attr_value_99630B": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["data_type_name", "enable_use_cache", "enable_use_cache_name", "int_type_num", "photo_create_time_name", "photo_id", "photo_id_name", "rank_list_name", "request_num", "retrieval_list_name", "similar_list_name", "string_type_num", "valid_duration", "valid_duration_name"], "$output_item_attrs": []}, "common_attrs": [{"name": "data_type_name", "type": "string", "value": "data_type"}, {"name": "retrieval_list_name", "type": "string", "value": "RetrievalList"}, {"name": "rank_list_name", "type": "string", "value": "RankList"}, {"name": "similar_list_name", "type": "string", "value": "DupPhotoRealtimeSimilarList"}, {"name": "photo_create_time_name", "type": "string", "value": "photo_create_time"}, {"name": "photo_id_name", "type": "string", "value": "photo_id"}, {"name": "valid_duration_name", "type": "string", "value": "valid_duration"}, {"name": "enable_use_cache_name", "type": "string", "value": "enable_use_cache"}, {"name": "photo_id", "type": "int", "value": 116859400437}, {"name": "enable_use_cache", "type": "int", "value": 1}, {"name": "valid_duration", "type": "int", "value": 30}, {"name": "int_type_num", "type": "int", "value": 1}, {"name": "string_type_num", "type": "int", "value": 3}, {"name": "request_num", "type": "int", "value": 10}], "type_name": "CommonRecoItemAttrDefaultValueEnricher"}, "sleep_F6116A": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "sleep_ms": 5000, "type_name": "CommonRecoSleepObserver"}}, "type_name": "CommonRecoPipeline"}, "pipeline_map": {"ad_item_info_collection": {"__PARENT": "base_pipeline", "pipeline": ["ad_item_info_collection::calc_time_cost_s", "ad_item_info_collection::ad_material_fetch_message_from_kafka_enricher_849D40", "ad_item_info_collection::parse_protobuf_from_string_E658E6", "ad_item_info_collection::enrich_with_protobuf_64241D", "ad_item_info_collection::_branch_controller_1CB4C1AD", "ad_item_info_collection::perflog_BC187182", "ad_item_info_collection::set_attr_value_B0F4D0", "ad_item_info_collection::ad_instance_convert_1C0248", "ad_item_info_collection::enrich_with_protobuf_DD0B79", "ad_item_info_collection::_branch_controller_6C80B438", "ad_item_info_collection::perflog_BABA8506", "ad_item_info_collection::return__6A0259", "ad_item_info_collection::_branch_controller_A7C8C385", "ad_item_info_collection::perflog_A0BF7AA4", "ad_item_info_collection::return__16C414", "ad_item_info_collection::cache_check::calc_time_cost_s", "ad_item_info_collection::cache_check::set_attr_value_FEA432", "ad_item_info_collection::cache_check::enrich_attr_by_lua_B31FCE", "ad_item_info_collection::cache_check::get_common_attr_from_redis_287AA9", "ad_item_info_collection::cache_check::_branch_controller_1D0BEC3B", "ad_item_info_collection::cache_check::perflog_141FCF41", "ad_item_info_collection::cache_check::return__04FED1", "ad_item_info_collection::cache_check::write_to_redis_6F3F23", "ad_item_info_collection::cache_check::calc_time_cost_e", "ad_item_info_collection::cache_check::calc_time_cost", "ad_item_info_collection::cache_check::perf_time_cost", "ad_item_info_collection::perflog_05FDB71B", "ad_item_info_collection::build_protobuf_148C5B", "ad_item_info_collection::build_protobuf_DAB14F", "ad_item_info_collection::enrich_by_generic_grpc_E2F6DB", "ad_item_info_collection::enrich_with_protobuf_74A9DD", "ad_item_info_collection::enrich_attr_by_lua_831712", "ad_item_info_collection::_branch_controller_A72FEE86", "ad_item_info_collection::perflog_C00ED652", "ad_item_info_collection::return__D7A377", "ad_item_info_collection::_branch_controller_********", "ad_item_info_collection::perflog_2BC15FF3", "ad_item_info_collection::return__EDCBDA", "ad_item_info_collection::perflog_2B40DF8D", "ad_item_info_collection::build_protobuf_F6CD79", "ad_item_info_collection::build_protobuf_3C9242", "ad_item_info_collection::enrich_by_generic_grpc_B3A660", "ad_item_info_collection::enrich_with_protobuf_2D31FE", "ad_item_info_collection::enrich_attr_by_lua_D03336", "ad_item_info_collection::_branch_controller_370EF81D", "ad_item_info_collection::return__B920B9", "ad_item_info_collection::perflog_039E7665", "ad_item_info_collection::build_protobuf_C46FC3", "ad_item_info_collection::enrich_by_generic_grpc_9B2BC9", "ad_item_info_collection::enrich_with_protobuf_513708", "ad_item_info_collection::enrich_attr_by_lua_25699A", "ad_item_info_collection::_branch_controller_C2420FF6", "ad_item_info_collection::return__70B3DF", "ad_item_info_collection::perflog_1A440E82", "ad_item_info_collection::build_protobuf_0EC329", "ad_item_info_collection::send_to_kafka_2395FA", "ad_item_info_collection::_branch_controller_6C333340", "ad_item_info_collection::item_user_collection::calc_time_cost_s", "ad_item_info_collection::item_user_collection::perflog_F57E9461", "ad_item_info_collection::item_user_collection::set_attr_value_EE28E3", "ad_item_info_collection::item_user_collection::ad_instance_convert_6224EC", "ad_item_info_collection::item_user_collection::enrich_with_protobuf_83C10F", "ad_item_info_collection::item_user_collection::_branch_controller_9B26B75A", "ad_item_info_collection::item_user_collection::perflog_7EDF6DEC", "ad_item_info_collection::item_user_collection::return__9215A4", "ad_item_info_collection::item_user_collection::_branch_controller_274393C0", "ad_item_info_collection::item_user_collection::perflog_814A6078", "ad_item_info_collection::item_user_collection::return__56237E", "ad_item_info_collection::item_user_collection::cache_check::calc_time_cost_s", "ad_item_info_collection::item_user_collection::cache_check::set_attr_value_E6E07B", "ad_item_info_collection::item_user_collection::cache_check::enrich_attr_by_lua_697A77", "ad_item_info_collection::item_user_collection::cache_check::get_common_attr_from_redis_D05816", "ad_item_info_collection::item_user_collection::cache_check::_branch_controller_14EB408E", "ad_item_info_collection::item_user_collection::cache_check::perflog_6500F7F7", "ad_item_info_collection::item_user_collection::cache_check::return__E3598F", "ad_item_info_collection::item_user_collection::cache_check::write_to_redis_B5AFEC", "ad_item_info_collection::item_user_collection::cache_check::calc_time_cost_e", "ad_item_info_collection::item_user_collection::cache_check::calc_time_cost", "ad_item_info_collection::item_user_collection::cache_check::perf_time_cost", "ad_item_info_collection::item_user_collection::build_protobuf_891FED", "ad_item_info_collection::item_user_collection::build_protobuf_3C06C4", "ad_item_info_collection::item_user_collection::enrich_by_generic_grpc_EEE934", "ad_item_info_collection::item_user_collection::enrich_with_protobuf_E1BE7C", "ad_item_info_collection::item_user_collection::enrich_attr_by_lua_E54A01", "ad_item_info_collection::item_user_collection::enrich_attr_by_lua_D46A7D", "ad_item_info_collection::item_user_collection::perflog_39234151", "ad_item_info_collection::item_user_collection::build_protobuf_CFC896", "ad_item_info_collection::item_user_collection::build_protobuf_D1733C", "ad_item_info_collection::item_user_collection::perflog_90424AB3", "ad_item_info_collection::item_user_collection::send_to_kafka_918FE1", "ad_item_info_collection::item_user_collection::calc_time_cost_e", "ad_item_info_collection::item_user_collection::calc_time_cost", "ad_item_info_collection::item_user_collection::perf_time_cost", "ad_item_info_collection::_branch_controller_195AF713", "ad_item_info_collection::new_photo_collection::calc_time_cost_s", "ad_item_info_collection::new_photo_collection::perflog_38C6D571", "ad_item_info_collection::new_photo_collection::ad_instance_convert_713BC9", "ad_item_info_collection::new_photo_collection::enrich_with_protobuf_97442D", "ad_item_info_collection::new_photo_collection::enrich_attr_by_lua_95C190", "ad_item_info_collection::new_photo_collection::enrich_attr_by_lua_17697D", "ad_item_info_collection::new_photo_collection::_branch_controller_A362652F", "ad_item_info_collection::new_photo_collection::perflog_F6452FB4", "ad_item_info_collection::new_photo_collection::enrich_attr_by_lua_CD430F", "ad_item_info_collection::new_photo_collection::build_protobuf_1B1748", "ad_item_info_collection::new_photo_collection::build_protobuf_5AFE9D", "ad_item_info_collection::new_photo_collection::log_debug_info_90C44E", "ad_item_info_collection::new_photo_collection::send_to_kafka_5DB1B4", "ad_item_info_collection::new_photo_collection::calc_time_cost_e", "ad_item_info_collection::new_photo_collection::calc_time_cost", "ad_item_info_collection::new_photo_collection::perf_time_cost", "ad_item_info_collection::calc_time_cost_e", "ad_item_info_collection::calc_time_cost", "ad_item_info_collection::perf_time_cost"]}, "debug_req": {"__PARENT": "base_pipeline", "pipeline": ["set_attr_value_99630B", "get_kconf_params_47BE2A", "enrich_attr_by_lua_097CC9", "enrich_attr_by_lua_8708F3", "build_protobuf_75BDB6", "build_protobuf_D94392", "build_protobuf_B75AB6", "build_protobuf_DACA72", "build_protobuf_E904A1", "build_protobuf_DB58DD", "build_protobuf_AB201A", "build_protobuf_6834AA", "enrich_by_generic_grpc_2AD8C9", "build_protobuf_A3032B", "enrich_by_generic_grpc_6A38B4", "log_debug_info_BB7CD8", "build_protobuf_1EE1A7", "enrich_by_generic_grpc_961F78", "log_debug_info_EC01A7", "sleep_F6116A"]}, "get_photo_userid": {"__PARENT": "base_pipeline", "pipeline": ["get_photo_user_id::calc_time_cost_s", "get_photo_user_id::ad_material_fetch_message_from_kafka_enricher_8C95EA", "get_photo_user_id::parse_protobuf_from_string_B6DAD0", "get_photo_user_id::enrich_with_protobuf_FA84A7", "get_photo_user_id::build_protobuf_751164", "get_photo_user_id::enrich_by_generic_grpc_9AC04C", "get_photo_user_id::enrich_with_protobuf_973A08", "get_photo_user_id::build_table_from_common_list_attr_EF3AC9", "get_photo_user_id::copy_attr_7BF6F5", "get_photo_user_id::fill_protobuf_by_attr_enrich_5FC1A2", "get_photo_user_id::copy_attr_4D5C55", "get_photo_user_id::serialize_protobuf_message_081D58", "get_photo_user_id::send_to_kafka_7D2683", "get_photo_user_id::calc_time_cost_e", "get_photo_user_id::calc_time_cost", "get_photo_user_id::perf_time_cost"]}}}, "runner_pipeline_group": {"ad_item_info_collection": {"core_num_thread_ratio": 0.0, "pipeline": ["ad_item_info_collection"], "thread_num": 10}, "debug_req": {"core_num_thread_ratio": 0.0, "pipeline": ["debug_req"], "thread_num": 1}, "get_photo_userid": {"core_num_thread_ratio": 0.0, "pipeline": ["get_photo_userid"], "thread_num": 10}}, "service_identifier": "ad-creative-data-collection-server"}