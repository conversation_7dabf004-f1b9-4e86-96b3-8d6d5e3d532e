#!/usr/bin/env python3
# coding=utf-8
import os
import sys
dragon_path=os.path.abspath('../../../../../../dragon/')
ad_dragon_path=os.path.abspath('../../../../ad_dragon/')
sys.path.append(dragon_path)
sys.path.append(ad_dragon_path)

from dragonfly.common_leaf_dsl import LeafFlow, OfflineRunner
from dragon_server.creative.constants import *
from ks_leaf_functional.core.module import module
from dragon_server.creative.common import AdCreativeCommonApiMixin
from ks_leaf_functional.core.data_manager import data_manager, ab_param as ab, kconf_param as kconf

#===================================================== 常量定义 =====================================================
ns = "ad.ad_creative_data_collection_server"

class AdCreativeDataCollection(LeafFlow, AdCreativeCommonApiMixin):
#=====================================================  =====================================================
  def debug_req(self):
    self.set_attr_value(   # 设置 变量
      no_check = True,
			common_attrs=[
        {"name": "data_type_name", "type": "string", "value": "data_type"},
        {"name": "retrieval_list_name", "type": "string", "value": "RetrievalList"},
        {"name": "rank_list_name", "type": "string", "value": "RankList"},
        {"name": "similar_list_name", "type": "string", "value": "DupPhotoRealtimeSimilarList"},
        {"name": "photo_create_time_name", "type": "string", "value": "photo_create_time"},
        {"name": "photo_id_name", "type": "string", "value": "photo_id"},
        {"name": "valid_duration_name", "type": "string", "value": "valid_duration"},
        {"name": "enable_use_cache_name", "type": "string", "value": "enable_use_cache"},
        {"name": "photo_id", "type": "int", "value": 116859400437},
        {"name": "enable_use_cache", "type": "int", "value": 1},
        {"name": "valid_duration", "type": "int", "value": 30},
        {"name": "int_type_num", "type": "int", "value": 1},
        {"name": "string_type_num", "type": "int", "value": 3},
        {"name": "request_num", "type": "int", "value": 10},
      ]
		).get_kconf_params(   # 取kconf debug photo_id 配置
        kconf_configs=[
          dict(kconf_key="ad.adCreativeEngine.debugDupInfoQueryPhotoIdList",
            value_type="list_int64",
            export_common_attr="photo_id_list"
          )
        ]
    ).enrich_attr_by_lua(   # 从account_user_ids中取第一个值作为account_user_id
      import_common_attr = ["photo_id_list"],
      function_for_common = "getRandomPhotoId",
      export_common_attr = ["photo_id"],
      lua_script = """
        function getRandomPhotoId()
          local idx = math.floor(#photo_id_list * util.Random())
          if idx == 0 and #photo_id_list > 0 then 
            return photo_id_list[#photo_id_list]
          else 
            return photo_id_list[idx]
          end
        end
      """
    ).enrich_attr_by_lua(  # 获取当前时间戳
        function_for_common = "getTimeStamp",
        export_common_attr = ["photo_create_time"],
        lua_script = """
          function getTimeStamp()
            return util.GetTimestamp() // 1000
          end
        """
    ).build_protobuf( # 
      class_name="kuiba::SampleAttr",
      inputs=[
        { "common_attr": "int_type_num", "path": "type" },
        { "common_attr": "photo_id_name", "path": "name" },
        { "common_attr": "photo_id", "path": "int_value"},
      ],
      output_common_attr= "photo_attr",
    ).build_protobuf( # 
      class_name="kuiba::SampleAttr",
      inputs=[
        { "common_attr": "string_type_num", "path": "type" },
        { "common_attr": "data_type_name", "path": "name" },
        { "common_attr": "retrieval_list_name", "path": "string_value"},
      ],
      output_common_attr= "retrieval_data_type_attr",
    ).build_protobuf( # 
      class_name="kuiba::SampleAttr",
      inputs=[
        { "common_attr": "string_type_num", "path": "type" },
        { "common_attr": "data_type_name", "path": "name" },
        { "common_attr": "rank_list_name", "path": "string_value"},
      ],
      output_common_attr= "rank_data_type_attr",
    ).build_protobuf( # 
      class_name="kuiba::SampleAttr",
      inputs=[
        { "common_attr": "string_type_num", "path": "type" },
        { "common_attr": "data_type_name", "path": "name" },
        { "common_attr": "similar_list_name", "path": "string_value"},
      ],
      output_common_attr= "similar_list_type_attr",
    ).build_protobuf( # 
      class_name="kuiba::SampleAttr",
      inputs=[
        { "common_attr": "int_type_num", "path": "type" },
        { "common_attr": "photo_create_time_name", "path": "name" },
        { "common_attr": "photo_create_time", "path": "int_value"},
      ],
      output_common_attr= "photo_create_time_attr",
    ).build_protobuf( # 
      class_name="kuiba::SampleAttr",
      inputs=[
        { "common_attr": "int_type_num", "path": "type" },
        { "common_attr": "valid_duration_name", "path": "name" },
        { "common_attr": "valid_duration", "path": "int_value"},
      ],
      output_common_attr= "valid_duration_attr",
    ).build_protobuf( # 
      class_name="kuiba::SampleAttr",
      inputs=[
        { "common_attr": "int_type_num", "path": "type" },
        { "common_attr": "enable_use_cache_name", "path": "name" },
        { "common_attr": "enable_use_cache", "path": "int_value"},
      ],
      output_common_attr= "enable_use_cache_attr",
    ).build_protobuf( # 
      class_name="ks::platform::CommonRecoRequest",
      inputs=[
        { "common_attr": "request_num", "path": "request_num" },
        { "common_attr": "photo_attr", "path": "common_attr", "append": True},
        { "common_attr": "retrieval_data_type_attr", "path": "common_attr", "append": True},
        { "common_attr": "enable_use_cache_attr", "path": "common_attr", "append": True},
        { "common_attr": "valid_duration_attr", "path": "common_attr", "append": True},
      ],
      output_common_attr= "retrieval_req",
    ).enrich_by_generic_grpc(
      kess_service="ad-dup-info-query-server",
      timeout_ms= 10000,
      method_name="/ks.platform.CommonRecoLeafService/Recommend",
      request_attr= "retrieval_req",
      response_attr= "retrieval_resp",
      response_class="ks::platform::CommonRecoResponse"
    ).build_protobuf( # 
      class_name="ks::platform::CommonRecoRequest",
      inputs=[
        { "common_attr": "request_num", "path": "request_num" },
        { "common_attr": "photo_attr", "path": "common_attr", "append": True},
        { "common_attr": "rank_data_type_attr", "path": "common_attr", "append": True},
        { "common_attr": "enable_use_cache_attr", "path": "common_attr", "append": True},
        { "common_attr": "valid_duration_attr", "path": "common_attr", "append": True},
      ],
      output_common_attr= "rank_req",
    ).enrich_by_generic_grpc(
      kess_service="ad-dup-info-query-server",
      timeout_ms= 10000,
      method_name="/ks.platform.CommonRecoLeafService/Recommend",
      request_attr= "rank_req",
      response_attr= "rank_resp",
      response_class="ks::platform::CommonRecoResponse"
    ).debug_log(
      common_attrs = ["retrieval_req", "retrieval_resp", "rank_req", "rank_resp"]
    ).build_protobuf( # 
      class_name="ks::platform::CommonRecoRequest",
      inputs=[
        { "common_attr": "request_num", "path": "request_num" },
        { "common_attr": "photo_attr", "path": "common_attr", "append": True},
        { "common_attr": "similar_list_type_attr", "path": "common_attr", "append": True},
        { "common_attr": "photo_create_time_attr", "path": "common_attr", "append": True},
      ],
      output_common_attr= "similar_list_req",
    ).enrich_by_generic_grpc(
      kess_service="ad-dup-info-query-server",
      timeout_ms= 10000,
      method_name="/ks.platform.CommonRecoLeafService/Recommend",
      request_attr= "similar_list_req",
      response_attr= "similar_list_resp",
      response_class="ks::platform::CommonRecoResponse"
    ).debug_log(
      common_attrs = ["similar_list_req", "similar_list_resp"]
    ).sleep(5000)
    return self
  
  @module()
  def get_photo_user_id(self):
    """
      从kafka消费photo增量 补充userId并发送
    ------
    整体流程:
    ad_material_fetch_message_from_kafka: 获取photo增量消息
    """
    self.ad_material_fetch_message_from_kafka( # 从指定 topic 获取数据
      no_check = True,
      kafka_group = "ad_creative_data_collection",
      candidate_kafka_group = "ad_creative_data_collection_test",
      kafka_topic = "dragon_dup_photo_batch_trigger_mq",
      kafka_params = "kuaishou.consume.delay.ms=0;",
      out_string_column = "trigger_msg",
      msg_timestamp_column = "kafka_msg_fetch_timestamp"
    ).parse_protobuf_from_string(   # 解析pb
      input_attr="trigger_msg",
      output_attr="photo_trigger_info",
      class_name="kuaishou.ad.algorithm.AdDupPhotoBatchTriggerInfo",
    ).enrich_with_protobuf(   # 获取photoid
      from_extra_var = "photo_trigger_info",
      attrs = [
        dict(name="photo_id", path="photo_id"),
      ]
    ).build_protobuf(  # 构造 kuaishou::negative::GetByPhotoIdsRequest
      class_name="kuaishou::negative::GetByPhotoIdsRequest",
      inputs=[
        { "common_attr": "photo_id", "path": "photo_id", "append": True },
        { "common_attr": "timeout", "path": "timeout"}
      ],
      output_common_attr= "photo_info_request",
    ).enrich_by_generic_grpc(		# 请求 photoService 服务
      kess_service="grpc_apiCorePhotoService",
      timeout_ms= 1000,
      method_name="/kuaishou.negative.PhotoServiceRpc/GetByIdsContainsDeleted",
      request_attr= "photo_info_request",
      response_attr= "photo_info_response",
      response_class="kuaishou::negative::PhotoMapResponse"
    ).enrich_with_protobuf(
      from_extra_var = "photo_info_response",
      no_check = True,
      attrs = [
        dict(name="photo_ids", path="photos.key"),
        dict(name="user_ids", path="photos.value.userId")
      ]
    ).build_table_from_common_list_attr(  # 构造一张表
      no_check = True,
      new_table = "photo_info_table",
      build_config = [  # 第一行默认为主键
        dict(src = "photo_ids", dest = "photo_id"),
        dict(src = "user_ids", dest = "user_id"),
      ]
    ).copy_attr(    # 将 photo_trigger_info copy 到 item_attr 补数据
      item_table = "photo_info_table",
      no_check = True,
      attrs=[{
        "from_common": "photo_trigger_info",
        "to_item": "photo_trigger_info"
      }]
    ).fill_protobuf_field( 	# 为 photo_trigger_info 补充 user_id
      item_table = "photo_info_table",
      input_attr_column = "photo_trigger_info",
      inputs= [
        { "item_attr": "user_id", "path": "user_id" },
      ]
    ).copy_attr(    # 将 photo_trigger_info copy 回 common_attr
      item_table = "photo_info_table",
      attrs=[{
        "from_item": "photo_trigger_info",
        "to_common": "photo_trigger_info_new"
      }]
    ).serialize_protobuf_message(
      from_common_attr = "photo_trigger_info_new",
      serialize_to_common_attr = "photo_trigger_info_new_str"
    ).send_to_kafka(    # 写 embedding 消息
      is_common_attr = True,
      message_column = "photo_trigger_info_new_str",
      topic = "ad_creative_photo_data_collection_message"
    )
    return self

  @module()
  def ad_item_info_collection(self):
    """
      从kafka消费索引增量 收集商品id增量数据
    ------
    整体流程:
    ad_material_fetch_message_from_kafka: 获取索引增量消息
    parse_protobuf_from_string 解析消息
    enrich_with_protobuf 获取消息类型(insert/update、creative/unit...)
    photo_item_user_collection: 对于creative增量 通过正排获取到item_id和account_user_id
    item_user_collection: 对于unit增量 通过正排获取account_user_id
    """
    self.ad_material_fetch_message_from_kafka( # 从指定 topic 获取数据
      no_check = True,
      kafka_group = "ad_creative_data_collection",
      candidate_kafka_group = "ad_creative_data_collection_test",
      kafka_topic = "default_data_prod",
      kafka_params = "kuaishou.consume.delay.ms=120000;",
      out_string_column = "trigger_msg",
      msg_timestamp_column = "kafka_msg_fetch_timestamp"
    ).parse_protobuf_from_string(   # 解析pb
      input_attr = "trigger_msg",
      output_attr = "ad_instance",
      class_name = "kuaishou.ad.AdInstance"
    ).enrich_with_protobuf(   # 获取 msg_type 和 data_type
      from_extra_var = "ad_instance",
      no_check=True,
      attrs = [
        dict(name="data_type", path="type"),    # 数据类型 unit枚举值为3 创意为4
        dict(name="msg_type", path="msg_type"),    # 消息类型 insert为0
      ]
    ).if_("data_type == 4 and msg_type == 0") \
     .photo_item_user_collection() \
     .end_if_() \
     .if_("data_type == 3") \
     .item_user_collection() \
     .end_if_() \
     .if_("data_type == 17") \
     .new_photo_collection() \
     .end_if_()
    return self

  @module()
  def item_user_collection(self):
    """
      根据unit_id 获取账户userId 组成AdCreativeEngineItemTriggerInfo结构并下发
      ------
      整体流程:
      perflog: 单元增量打点
      set_attr_value: 默认值设置
      ad_instance_convert: AdInstance转换 转成Unit结构
      enrich_with_protobuf: 获取需要的字段
      商品id为0过滤 + merchant_type非0过滤
      cache_check: 缓存检查 以itemId为key 避免短时间重复发送
      get_account: 根据account_id获取account正排
      enrich_with_protobuf + enrich_attr_by_lua: 获取account_user_id
      build_protobuf: 构造AdCreativeEngineItemTriggerInfo
      send_to_kafka: 下发消息
    """
    self.perflog(
      mode = "count",
      namespace = ns,
      subtag = "item_admit",
      extra1 = "all_unit_inc_data"
    ).set_attr_value(   # 设置默认值
			  common_attrs=[
          {"name": "item_id", "type": "int", "value": 0}
			  ]
		).ad_instance_convert(    # 原始pb格式转为Unit结构
      ad_instance_column = 'ad_instance',
      type = 'Unit',
      output_column_ = 'unit_msg',
    ).enrich_with_protobuf(   # 获取 item_id unit_id account_id user_id和merchant_item_type
      from_extra_var = "unit_msg",
      attrs = [
        dict(name="item_id", path="unit_small_shop_merchant_support_info.item_id"),
        dict(name="unit_id", path="id"),
        dict(name="account_id", path="account_id"),
        dict(name="user_id", path="unit_support_info.live_user_id"),
        dict(name="merchant_item_type", path="unit_small_shop_merchant_support_info.merchant_item_type"),
      ]
    ).if_("item_id == 0") \
     .perflog(
        mode = "count",
        namespace = ns,
        subtag = "item_admit",
        extra1 = "item_id_zero"
    ).return_().end_if_() \
     .if_("merchant_item_type ~= 0") \
     .perflog(
        mode = "count",
        namespace = ns,
        subtag = "item_admit",
        extra1 = "merchant_item_type_invalid"
    ).return_().end_if_() \
    .cache_check("item_id", "acg_item_", "item_cache_key", "item_id_str", "cache_item_id", "adLiveMerchantInfo", 60, "item_admit") \
    .get_account("account_id", "account_id_info", "get_account_req", "get_account_resp") \
    .enrich_with_protobuf(
      from_extra_var = "get_account_resp",
      no_check = True,
      attrs = [
        dict(name="account_user_ids", path="items.account.user_id"),
      ]
    ).enrich_attr_by_lua(   # 从account_user_ids中取第一个值作为account_user_id
      import_common_attr = ["account_user_ids"],
      function_for_common = "getFirstItem",
      export_common_attr = ["account_user_id"],
      lua_script = """
        function getFirstItem()
          local len = #account_user_ids
          if (len > 0)
          then
            return account_user_ids[len]
          else
            return 0
          end
        end
      """
    ).enrich_attr_by_lua(   # 判断两个值是否相等
      import_common_attr = ["account_user_id", "user_id"],
      function_for_common = "isEqual",
      export_common_attr = ["is_equal"],
      lua_script = 
      """
        function isEqual()
          if (account_user_id == user_id)
          then
            return 1
          else
            return 0 
          end
        end
      """
    ).perflog(
        mode = "count",
        namespace = ns,
        subtag = "item_admit",
        extra1 = "user_id_equal",
        extra2 = "{{is_equal}}"
    ).build_protobuf(   # 构造AdCreativeEngineItemTriggerInfo结构
			class_name="kuaishou::ad::algorithm::AdCreativeEngineItemDataInfo",
			inputs=[
				{ "common_attr": "unit_id", "path": "unit_id" }
			],
			output_common_attr= "item_data_info",
		).build_protobuf(
			class_name="kuaishou::ad::algorithm::AdCreativeEngineItemTriggerInfo",
			inputs=[
				{ "common_attr": "item_id", "path": "item_id" },
        { "common_attr": "account_user_id", "path": "user_id" },
        { "common_attr": "item_data_info", "path": "item_data_info", "append": True},
			],
      as_string = True,
			output_common_attr= "item_data_str",
		).perflog(
        mode = "count",
        namespace = ns,
        subtag = "item_admit",
        extra1 = "output_num"
    ).send_to_kafka(
      is_common_attr = True,
      message_column = "item_data_str",
      topic = "ad_creative_item_collection_message"
    )
    return self

  def photo_item_user_collection(self):
    self.perflog(
      mode = "count",
      namespace = ns,
      subtag = "creative_admit",
      extra1 = "all_creative_inc_data"
    ).set_attr_value(   # 设置photo_id默认值
			  common_attrs=[
          {"name": "photo_id", "type": "int", "value": 0},
          {"name": "item_id", "type": "int", "value": 0},
          {"name": "merchant_item_type", "type": "int", "value": 0},
			  ]
		).ad_instance_convert(    # 原始pb格式转为Creative结构
      ad_instance_column = 'ad_instance',
      type = 'Creative',
      output_column_ = 'creative_msg',
    ).enrich_with_protobuf(   # 获取 photo_id unit_id
      from_extra_var = "creative_msg",
      attrs = [
        dict(name="photo_id", path="photo_id"),
        dict(name="unit_id", path="unit_id"),
        dict(name="account_id", path="account_id"),
      ]
    ).if_("photo_id == 0") \
     .perflog(
        mode = "count",
        namespace = ns,
        subtag = "creative_admit",
        extra1 = "photo_id_zero"
    ).return_().end_if_() \
     .if_("unit_id == 0") \
     .perflog(
        mode = "count",
        namespace = ns,
        subtag = "creative_admit",
        extra1 = "unit_id_zero"
    ).return_().end_if_() \
    .cache_check("photo_id", "acg_photo_", "photo_cache_key", "photo_id_str", "cache_photo_id", "adLiveMerchantInfo", 60, "creative_admit") \
    .perflog(
        mode = "count",
        namespace = ns,
        subtag = "creative_admit",
        extra1 = "photo_num_after_cache"
    ).get_unit("unit_id", "unit_id_info", "get_unit_req", "get_unit_resp") \
    .enrich_with_protobuf(
      from_extra_var = "get_unit_resp",
      no_check = True,
      attrs = [
        dict(name="item_ids", path="items.unit.unit_small_shop_merchant_support_info.item_id"),
        dict(name="merchant_item_types", path="items.unit.unit_small_shop_merchant_support_info.merchant_item_type"),
      ]
    ).enrich_attr_by_lua(   # 从item_ids、merchant_item_types中取第一个值作为item_id、merchant_item_type
      import_common_attr = ["item_ids", "merchant_item_types"],
      function_for_common = "getFirstItem",
      export_common_attr = ["item_id", "merchant_item_type"],
      lua_script = """
        function getFirstItem()
          local len1 = #item_ids
          local len2 = #merchant_item_types
          local item_id = 0
          local merchant_item_type = 0
          if (len1 > 0) then item_id = item_ids[len1] end
          if (len2 > 0) then merchant_item_type = merchant_item_types[len2] end
          return item_id, merchant_item_type
        end
      """
    ).if_("item_id == 0") \
     .perflog(
        mode = "count",
        namespace = ns,
        subtag = "creative_admit",
        extra1 = "item_id_zero"
    ).return_().end_if_() \
    .if_("merchant_item_type ~= 0") \
     .perflog(
        mode = "count",
        namespace = ns,
        subtag = "creative_admit",
        extra1 = "merchant_item_type_invalid"
    ).return_().end_if_() \
    .perflog(
        mode = "count",
        namespace = ns,
        subtag = "creative_admit",
        extra1 = "get_item_id_succ"
    ).get_account("account_id", "account_id_info", "get_account_req", "get_account_resp") \
    .enrich_with_protobuf(
      from_extra_var = "get_account_resp",
      no_check = True,
      attrs = [
        dict(name="account_user_ids", path="items.account.user_id"),
      ]
    ).get_first_item("account_user_ids", "account_user_id") \
    .return_if_value_zero("account_user_id") \
    .perflog(
      mode = "count",
      namespace = ns,
      subtag = "creative_admit",
      extra1 = "get_account_id_succ"
    ).photo_user_get("photo_id", "photo_user_id", 1000) \
    .return_if_value_zero("photo_user_id") \
    .perflog(
      mode = "count",
      namespace = ns,
      subtag = "creative_admit",
      extra1 = "get_photo_id_succ"
    ).build_protobuf(
			class_name="kuaishou::ad::algorithm::AdCreativeEngineCustomPhotoItemInfo",
			inputs=[
				{ "common_attr": "item_id", "path": "item_id" },
        { "common_attr": "photo_id", "path": "photo_id" },
        { "common_attr": "account_user_id", "path": "item_user_id" },
        { "common_attr": "photo_user_id", "path": "photo_user_id"},
			],
      as_string = True,
			output_common_attr= "custom_item_data_str",
		).send_to_kafka(
      is_common_attr = True,
      message_column = "custom_item_data_str",
      topic = "ad_creative_custom_photo_item_info"
    )
    return self
  
  @module()
  def new_photo_collection(self):
    """
      获取最近新关联创意的 photo 
      ------
      整体流程:
      perflog: 单元增量打点
      ad_instance_convert: AdInstance转换 转成Photo结构
      enrich_with_protobuf: 获取需要的字段
      enrich_attr_by_lua: 计算photo首次过审、首次进索引、上传时间
      enrich_attr_by_lua: 判断photo首次过审/首次进索引是否在24h以内
    """
    self.perflog(
      mode = "count",
      namespace = ns,
      subtag = "photo_admit",
      extra1 = "all_photo_inc_data"
    ).ad_instance_convert(    # 原始pb格式转为Unit结构
      ad_instance_column = 'ad_instance',
      type = 'Photo',
      output_column_ = 'photo_msg',
    ).enrich_with_protobuf(   # 获取 item_id unit_id account_id user_id和merchant_item_type
      from_extra_var = "photo_msg",
      attrs = [
        dict(name="photo_id", path="photo_id"),
        dict(name="first_audit_passtime", path="first_audit_passtime"),
        dict(name="first_index_time", path="first_index_time"),
        dict(name="photo_upload_time", path="photo_upload_time"),
        dict(name="recently_used_time", path="recently_used_time"),
      ]
    ).enrich_attr_by_lua(   # 计算间隔
      import_common_attr = ["first_audit_passtime", "first_index_time", "photo_upload_time", "recently_used_time"],
      function_for_common = "getDuration",
      export_common_attr = ["first_audit_duration", "first_index_duration", "photo_upload_duration", "recently_used_duration"],
      lua_script = """
        function getDuration()
          local now = util.GetTimestamp() // 1000
          return (now - first_audit_passtime) // 3600000, (now - first_index_time) // 3600000, (now - photo_upload_time) // 3600000, (now - recently_used_time) // 3600000
        end
      """
    ).enrich_attr_by_lua(   # valid_check 判断是否需要处理 photo
      import_common_attr = ["first_audit_duration", "first_index_duration"],
      function_for_common = "validCheck",
      export_common_attr = ["photo_valid"],
      lua_script = """
        function validCheck()
          if first_audit_duration < 24 then
            return 1
          elseif first_index_duration < 24 then
            return 1
          else 
            return 0
          end
        end
      """
    ).if_("photo_valid == 1") \
      .perflog(
      mode = "count",
      namespace = ns,
      subtag = "photo_admit",
      extra1 = "valid_photo_num"
    ).enrich_attr_by_lua(   # 获取兜底创建时间
      import_common_attr = ["first_audit_passtime", "photo_upload_time"],
      function_for_common = "getCreateTime",
      export_common_attr = ["create_time"],
      lua_script = """
        function getCreateTime()
          if first_audit_passtime > 0 then
            return first_audit_passtime
          elseif photo_upload_time > 0 then
            return photo_upload_time
          else
            return util.GetTimestamp() // 1000
          end
        end
      """
    ).build_protobuf(   # 构造AdDupDataInfo结构
			class_name="kuaishou::ad::algorithm::AdDupDataInfo",
			inputs=[
				{ "common_attr": "create_time", "path": "create_time" }
			],
			output_common_attr= "ad_dup_data_info",
		).build_protobuf(   # 构造AdCreativeEngineItemTriggerInfo结构
			class_name="kuaishou::ad::algorithm::AdDupPhotoBatchTriggerInfo",
			inputs=[
				{ "common_attr": "photo_id", "path": "photo_id" },
        { "common_attr": "ad_dup_data_info", "path": "ad_dup_data_info", "append": True}
			],
      as_string = True,
			output_common_attr= "photo_info_str",
		).debug_log(
      common_attrs = ["photo_id", "first_audit_passtime", "first_index_time", "photo_upload_time", "recently_used_time",
                      "first_audit_duration", "first_index_duration", "recently_used_duration", "photo_upload_duration",
                      "photo_valid", "photo_info_str"]
    ).send_to_kafka(    # 写 embedding 消息
      is_common_attr = True,
      message_column = "photo_info_str",
      topic = "ad_creative_audit_valid_photo_inc_message"
    ).end_if_()
    return self

  def photo_user_get(self, photo_id_name, user_id_name, timeout):
    self.build_protobuf(  # 构造 kuaishou::negative::GetByPhotoIdsRequest
      class_name="kuaishou::negative::GetByPhotoIdsRequest",
      inputs=[
        { "common_attr": photo_id_name, "path": "photo_id", "append": True },
      ],
      output_common_attr= "photo_info_request",
    ).enrich_by_generic_grpc(		# 请求 photoService 服务
      kess_service="grpc_apiCorePhotoService",
      timeout_ms= timeout,
      method_name="/kuaishou.negative.PhotoServiceRpc/GetByIdsContainsDeleted",
      request_attr= "photo_info_request",
      response_attr= "photo_info_response",
      response_class="kuaishou::negative::PhotoMapResponse"
    ).enrich_with_protobuf(
      from_extra_var = "photo_info_response",
      no_check = True,
      attrs = [
        dict(name="photo_user_ids", path="photos.value.userId")
      ]
    ).get_first_item("photo_user_ids", "photo_user_id")
    return self

  @module()
  def cache_check(self, id_name, key_prefix, key_name, value_name, default_value_name, cluster_name, ttl, subtag):
    """
      功能: 缓存检查，避免同一个id频繁触发 由于目前没有算子直接调用Exists 为方便复用 会将id也写成value 方便get操作
      输入:
        id_name[string]: 构造key使用的列名
        key_prefix[string]: 缓存前缀 key_prefix + tostring(id_name) 作为缓存key
        key_name[string]: 缓存key存储的列名
        value_name[string]: value存储的列名
        default_value_name[string]: 缓存获取结果列 默认值为0 如果从redis获取成功 会被改为对应值
        cluster_name[string]: 集群名
        ttl[int]: 缓存时间 单位为s
        subtag[string]: 打点标识
    """
    self.set_attr_value(  # 设置缓存结果默认值 设置
			common_attrs=[
        {"name": default_value_name, "type": "int", "value": 0},
			]
		).enrich_attr_by_lua(   # 构造缓存 key key_prefix + item_id
      import_common_attr = [key_prefix, id_name],
      function_for_common = "genKey",
      export_common_attr = [key_name, value_name],
      lua_script = f'''
        function genKey()
          return "{key_prefix}"..tostring({id_name}), tostring({id_name})
        end
      '''
    ).get_common_attr_from_redis(   # 查缓存
      cluster_name = "adLiveMerchantInfo",
      redis_params = [
        {
          "redis_key": "{{" + key_name + "}}",
          "redis_value_type": "string",
          "output_attr_name": default_value_name,
          "output_attr_type": "int"
        }
      ]
    ).if_(f"{default_value_name} ~= 0") \
     .perflog(
        mode = "count",
        namespace = ns,
        subtag = subtag,
        extra1 = "hit_cache"
    ).return_().end_if_() \
     .write_to_redis(
      kcc_cluster= cluster_name,
      timeout_ms=10,
      key= "{{" + key_name + "}}",
      value= "{{" + value_name + "}}",
      expire_second= ttl
    )
    return self

# flow 定义
photo_user_get_flow = AdCreativeDataCollection("get_photo_userid")
ad_item_info_collection_flow = AdCreativeDataCollection("ad_item_info_collection")
debug_req_flow = AdCreativeDataCollection("debug_req")

with photo_user_get_flow, data_manager:
  photo_user_get_flow.get_photo_user_id()

with ad_item_info_collection_flow, data_manager:
  ad_item_info_collection_flow.ad_item_info_collection()

with debug_req_flow, data_manager:
  debug_req_flow.debug_req()

# runner 定义
ad_creative_data_collection_runner = OfflineRunner("ad-creative-data-collection-server")
ad_creative_data_collection_runner.add_leaf_flows(leaf_flows=[photo_user_get_flow], name="get_photo_userid", thread_num=10)
ad_creative_data_collection_runner.add_leaf_flows(leaf_flows=[ad_item_info_collection_flow], name="ad_item_info_collection", thread_num=10)
ad_creative_data_collection_runner.add_leaf_flows(leaf_flows=[debug_req_flow], name="debug_req", thread_num=1)

ad_creative_data_collection_runner.draw()
current_dir = os.path.dirname(__file__)
ad_creative_data_collection_runner.build(output_file=os.path.join(current_dir,
                                        "pub/ad_creative_data_collection_server/config/dynamic_json_config.json"),
                                        extra_fields=config)

