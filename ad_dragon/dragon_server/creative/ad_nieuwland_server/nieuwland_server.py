#!/usr/bin/env python3
# coding=utf-8

import os
import sys
dragon_path=os.path.abspath('../../../../../../dragon/')
ad_dragon_path=os.path.abspath('../../../../ad_dragon/')
sys.path.append(dragon_path)
sys.path.append(ad_dragon_path)

from dragonfly.common_leaf_dsl import Leaf<PERSON>low, OfflineRunner, LeafService
from dragon_server.creative.constants import *
from dragon_server.creative.common import AdCreativeCommonApiMixin

class NieusLandServerSubFlow(LeafFlow, AdCreativeCommonApiMixin):
  def nieuwland_sub_flow(self, num):
    self.nieuwland_generate_ops_enricher(
      item_table = "clip_table",
      fission = num
    )
    return self

class NieusLandServerFlow(NieusLandServerSubFlow):
  def nieuwland_flow(self, subflows):
    self.ad_material_fetch_message_from_kafka( # 从指定 topic 获取数据
      no_check = True,
      kafka_group = "nieuwland_fine_edite_server",
      candidate_kafka_group = "nieuwland_fine_edite_server_test",
      kafka_topic = "nieuwland_fine_edite",
      kafka_params = "kuaishou.set.offset.ms.ago=0;",
      out_column = "trigger_msg_list",
      msg_timestamp_column = "kafka_msg_fetch_timestamp"
    ).data_frame_table_parse_retriever( # 反序列化 类型过滤 将pb字段解析到attr
      from_column = "trigger_msg_list"
    )
    # for subflow in subflows:
    for i in range(1, 20):
      subflow = NieusLandServerSubFlow(name="nieuwland_sub_flow" + str(i), item_table="clip_table").nieuwland_sub_flow(i)
      self.mix_by_sub_flow(
				sub_flow=subflow,
				item_table="clip_table",
        pass_common_attrs=["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name",
                            "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg",
                            "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"],
				input_tables=[
					{
						"table_name" : "clip_table",
						"attrs" : ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list",
												"tts_muted", "tag_params", "music_style"]
					},
          {
            "table_name" : "video_table",
            "attrs" : ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration",
                        "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts",
                        "candidates_caption_params_idx"]
          },
          {
            "table_name" : "video_clip_table",
            "attrs" : ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"]
          },
          {
            "table_name" : "candidate_video_table",
            "attrs" : ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"]
          },
          {
            "table_name" : "candidate_audio_table",
            "attrs" : ["candidate_audio_id", "render_filename", "start_time", "end_time"]
          },
          {
            "table_name" : "candidate_bgm_table",
            "attrs" : ["candidate_bgm_id", "render_filename"]
          },
          {
            "table_name" : "candidate_logo_table",
            "attrs" : ["candidate_logo_id", "render_filename"]
          }
				],
        retrieve_tables=[
          {
            "table_name" : "video_script_table",
            "attrs" : ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"]
          }
        ],
				deep_copy=True,
				no_check=True
			)
    self.nieuwland_sub_flow(
      0
    ).nieuwland_message_send_to_kafka(
      item_table = "video_script_table",
    )
    return self
  # def send_flow(self, source_type):
  #   self.send_to_kafka(source_type)
  #   return self

  # def make_strategy_flows(self, subflows):
  #   return self

subflows = [
  # NieusLandServerSubFlow(name="nieuwland_sub_flow1", item_table="clip_table").nieuwland_sub_flow(1),
  # NieusLandServerSubFlow(name="nieuwland_sub_flow2", item_table="clip_table").nieuwland_sub_flow(2),
]

nieuwland_flow = NieusLandServerFlow(name="nieuwland_flow", item_table="clip_table").nieuwland_flow(subflows)
# runner 定义
material_generation_server_runner = OfflineRunner("ad-nieuwland-server")
material_generation_server_runner.add_leaf_flows(leaf_flows=[nieuwland_flow], name="nieuwland_flow", thread_num=1)
material_generation_server_runner.draw()


current_dir = os.path.dirname(__file__)
material_generation_server_runner.build(output_file=os.path.join(current_dir,
                                        "pub/ad-nieuwland-server/config/dynamic_json_config.json"),
                                        extra_fields=config)

