{"_DRAGONFLY_CREATE_TIME": "2023-06-09 14:05:27", "_DRAGONFLY_VERSION": "0.7.13", "grpc": {"client_map": {"AdDpaProductServer": "grpc_adDpaProductServerImpl", "AdMaterialBaseInfoService": "grpc_adMaterialBaseInfoService", "CmdLineExecutorService": "grpc_editorSdkServiceForKaiYanCopy", "CreativeInformationGetterService": "grpc_adCreativeInformationGetterService", "CreativeProcessService": "ad-alliance-creative-process-uni-service", "DpOneServiceMmuHigh": "grpc_dpOneServiceMmuHigh", "DpaProductSearchDealer": "grpc_ad_product_search", "EcomItemInfoService": "grpc_adEcomProductItemInfoRpcService", "KwaiShopProductService": "kwaishop-product-detail-service", "KwaiShopProductServiceTest": "grpc_kwaishop-product-detail-service", "LiveCurrentLivingService": "grpc_liveExposeCurrentLivingRpcService", "LiveReservationReadServiceRpc": "grpc_liveReservationReadServiceRpc", "LiveStremInfoService": "grpc_adLiveInfoGetRpcService", "MediaProcessingJobServiceKey": "grpc_mediaProcessingJobService", "MmuPhotoMd5QueryServiceKey": "grpc_mmuPhotoMd5QueryService", "OcrDetectService": "grpc_ztOcrDetectService", "PhotoServiceKey": "grpc_apiCorePhotoService", "SellerTagInfoService": "grpc_kwaishopProductListAggregationService", "UniverseGenOptCtrInfService": "grpc_UniverseGenOptCTRInfRpcService", "UserTopPhotoService": "grpc_apiCoreUserTopPhotoService", "adTagRetrieveServerOfflineKey": "grpc_adTagRetrieveServer_offline", "forward_index_client": "ad-forward-index", "mmuAdEmbeddingServiceKey": "grpc_mmuAdEmbeddingService", "mmuDlFeatureCalculateServiceKey": "grpc_mmuDlFeatureCalculateService"}, "server": {"kcs_grpc_port_key": "AUTO_PORT1", "kess_name": "USE_KSN_AS_SERVICE", "port": 20012, "quit_wait_seconds": 120, "thread_num": 400}, "test": false}, "kess_config": {}, "pipeline_manager_config": {"base_pipeline": {"processor": {"ad_material_fetch_message_from_kafka_enricher_4053DE": {"$metadata": {"$input_common_attrs": ["kuaishou.set.offset.ms.ago=0;", "nieu<PERSON><PERSON>_fine_edite", "nieuwland_fine_edite_server_test"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": ["kafka_msg_fetch_timestamp", "trigger_msg_list"], "$output_item_attrs": []}, "candidate_kafka_group": "nieuwland_fine_edite_server_test", "kafka_group": "nieuwland_fine_edite_server", "kafka_params": "kuaishou.set.offset.ms.ago=0;", "kafka_topic": "nieu<PERSON><PERSON>_fine_edite", "msg_timestamp_column": "kafka_msg_fetch_timestamp", "out_column": "trigger_msg_list", "type_name": "AdMaterialFetchMessageFromKafkaEnricher"}, "ad_material_message_parse_retriever_B5A59A": {"$metadata": {"$input_common_attrs": ["trigger_msg_list"], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "from_column": "trigger_msg_list", "type_name": "DataFrameTableParseRetriever"}, "mix_by_sub_flow_001EB2": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow10", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_00C8C9": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow4", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_03EB9F": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow17", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_090D33": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow5", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_15EEEA": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow8", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_29035A": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow7", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_3AAADA": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow11", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_72C709": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow13", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_7A91A1": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow18", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_825865": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow19", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_9064F1": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow6", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_979586": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow9", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_9D1E2C": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow3", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_A0A623": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow14", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_AC6EB7": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow12", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_C72CF2": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow1", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_CAB41B": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow15", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_E9AD14": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow2", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "mix_by_sub_flow_EA78FF": {"$metadata": {"$input_common_attrs": ["blob_db", "blob_table", "candidate_bgm", "candidate_logo", "candidate_video_bg", "error_info", "error_msg", "first_industry_name", "font_candidate", "padding_bg", "product_name", "resolution", "scene", "script_id", "second_industry_name", "task_id", "user_id", "warning_text", "workflow_id"], "$input_item_attrs": ["candidate_audio_table::candidate_audio_id", "candidate_audio_table::end_time", "candidate_audio_table::render_filename", "candidate_audio_table::start_time", "candidate_bgm_table::candidate_bgm_id", "candidate_bgm_table::render_filename", "candidate_logo_table::candidate_logo_id", "candidate_logo_table::render_filename", "candidate_video_table::candidate_video_id", "candidate_video_table::cover_filename", "candidate_video_table::end_time", "candidate_video_table::height", "candidate_video_table::local_filename", "candidate_video_table::muted", "candidate_video_table::render_dubbing", "candidate_video_table::render_filename", "candidate_video_table::start_time", "candidate_video_table::width", "clip_table::clip_id", "clip_table::clip_type", "clip_table::duration", "clip_table::music_style", "clip_table::tag_params", "clip_table::transition_mode", "clip_table::transition_time_left", "clip_table::transition_time_right", "clip_table::tts_muted", "clip_table::unit_id", "clip_table::video_list", "video_clip_table::end_time", "video_clip_table::height", "video_clip_table::local_filename", "video_clip_table::muted", "video_clip_table::render_dubbing", "video_clip_table::render_filename", "video_clip_table::start_time", "video_clip_table::video_clip_id", "video_clip_table::width", "video_table::candidate_dubbing_tts", "video_table::candidate_video", "video_table::candidate_video_vs", "video_table::candidates_caption_params_idx", "video_table::caption_height_bias", "video_table::caption_params", "video_table::caption_params_list", "video_table::content_type", "video_table::duration", "video_table::erase_ng", "video_table::is_muted", "video_table::patch_mode", "video_table::tags_params", "video_table::video_clip", "video_table::video_id"], "$modify_item_tables": ["video_script_table"], "$output_common_attrs": [], "$output_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"]}, "deep_copy": true, "downstream_processor": "nieuwland_message_send_to_kafka_34FA12", "enrich_tables": [], "flow_name": "nieuwland_sub_flow16", "input_tables": [{"attrs": ["clip_id", "clip_type", "unit_id", "transition_time_left", "transition_time_right", "transition_mode", "duration", "video_list", "tts_muted", "tag_params", "music_style"], "table_name": "clip_table"}, {"attrs": ["video_id", "content_type", "video_clip", "candidate_video", "candidate_video_vs", "is_muted", "patch_mode", "erase_ng", "duration", "caption_params", "tags_params", "caption_params_list", "caption_height_bias", "candidate_dubbing_tts", "candidates_caption_params_idx"], "table_name": "video_table"}, {"attrs": ["video_clip_id", "render_filename", "render_dubbing", "local_filename", "width", "height", "muted", "start_time", "end_time"], "table_name": "video_clip_table"}, {"attrs": ["candidate_video_id", "local_filename", "render_filename", "render_dubbing", "start_time", "end_time", "cover_filename", "width", "height", "muted"], "table_name": "candidate_video_table"}, {"attrs": ["candidate_audio_id", "render_filename", "start_time", "end_time"], "table_name": "candidate_audio_table"}, {"attrs": ["candidate_bgm_id", "render_filename"], "table_name": "candidate_bgm_table"}, {"attrs": ["candidate_logo_id", "render_filename"], "table_name": "candidate_logo_table"}], "item_table": "clip_table", "pass_common_attrs": ["task_id", "user_id", "script_id", "product_name", "first_industry_name", "second_industry_name", "resolution", "scene", "blob_db", "blob_table", "padding_bg", "warning_text", "candidate_video_bg", "candidate_bgm", "candidate_logo", "font_candidate", "workflow_id", "error_msg", "error_info"], "retrieve_tables": [{"attrs": ["fission", "script_req", "photo_resource", "cover_resource", "width", "height", "duration", "replaced_video", "replaced_music"], "table_name": "video_script_table"}], "type_name": "CommonRecoPipelineMixer"}, "nieuwland_generate_ops_enricher_023DB9": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 3, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_0543A3": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 12, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_0A6C63": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 11, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_0D5DA1": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 17, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_140005": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 19, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_1C003A": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 18, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_272AEB": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 2, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_4BD4A9": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 8, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_51C94B": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 15, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_52C7F7": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 16, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_577728": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 4, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_6D8090": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 13, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_7C9531": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 0, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_9D1DBC": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 14, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_A1E09D": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 5, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_C5E31F": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 10, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_C8E91C": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 7, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_D31DAC": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 9, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_D341BE": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 1, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_generate_ops_enricher_FE0B5A": {"$metadata": {"$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "fission": 6, "item_table": "clip_table", "type_name": "NieuwLandGenerationOpsEnricher"}, "nieuwland_message_send_to_kafka_34FA12": {"$metadata": {"$downstream_item_attrs": ["video_script_table::cover_resource", "video_script_table::duration", "video_script_table::fission", "video_script_table::height", "video_script_table::photo_resource", "video_script_table::replaced_music", "video_script_table::replaced_video", "video_script_table::script_req", "video_script_table::width"], "$input_common_attrs": [], "$input_item_attrs": [], "$modify_item_tables": [], "$output_common_attrs": [], "$output_item_attrs": []}, "item_table": "video_script_table", "type_name": "NieuwlandMessageSendToKafkaObserver"}}, "type_name": "CommonRecoPipeline"}, "pipeline_map": {"nieuwland_flow": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["ad_material_fetch_message_from_kafka_enricher_4053DE", "ad_material_message_parse_retriever_B5A59A", "mix_by_sub_flow_C72CF2", "mix_by_sub_flow_E9AD14", "mix_by_sub_flow_9D1E2C", "mix_by_sub_flow_00C8C9", "mix_by_sub_flow_090D33", "mix_by_sub_flow_9064F1", "mix_by_sub_flow_29035A", "mix_by_sub_flow_15EEEA", "mix_by_sub_flow_979586", "mix_by_sub_flow_001EB2", "mix_by_sub_flow_3AAADA", "mix_by_sub_flow_AC6EB7", "mix_by_sub_flow_72C709", "mix_by_sub_flow_A0A623", "mix_by_sub_flow_CAB41B", "mix_by_sub_flow_EA78FF", "mix_by_sub_flow_03EB9F", "mix_by_sub_flow_7A91A1", "mix_by_sub_flow_825865", "nieuwland_generate_ops_enricher_7C9531", "nieuwland_message_send_to_kafka_34FA12"]}, "nieuwland_sub_flow1": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_D341BE"]}, "nieuwland_sub_flow10": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_C5E31F"]}, "nieuwland_sub_flow11": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_0A6C63"]}, "nieuwland_sub_flow12": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_0543A3"]}, "nieuwland_sub_flow13": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_6D8090"]}, "nieuwland_sub_flow14": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_9D1DBC"]}, "nieuwland_sub_flow15": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_51C94B"]}, "nieuwland_sub_flow16": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_52C7F7"]}, "nieuwland_sub_flow17": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_0D5DA1"]}, "nieuwland_sub_flow18": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_1C003A"]}, "nieuwland_sub_flow19": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_140005"]}, "nieuwland_sub_flow2": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_272AEB"]}, "nieuwland_sub_flow3": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_023DB9"]}, "nieuwland_sub_flow4": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_577728"]}, "nieuwland_sub_flow5": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_A1E09D"]}, "nieuwland_sub_flow6": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_FE0B5A"]}, "nieuwland_sub_flow7": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_C8E91C"]}, "nieuwland_sub_flow8": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_4BD4A9"]}, "nieuwland_sub_flow9": {"__PARENT": "base_pipeline", "item_table": "clip_table", "pipeline": ["nieuwland_generate_ops_enricher_D31DAC"]}}}, "runner_pipeline_group": {"nieuwland_flow": {"core_num_thread_ratio": 0.0, "pipeline": ["nieuwland_flow"], "thread_num": 1}}, "service_identifier": "ad-nieuwland-server"}