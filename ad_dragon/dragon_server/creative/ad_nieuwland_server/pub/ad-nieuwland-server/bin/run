#!/bin/sh

cd `dirname $0` || exit
absolute_path=`readlink -f .`
service_dir=`dirname $absolute_path`
service_name=`basename $service_dir`
exe_absolute_path="$absolute_path/$service_name"

function ChangeGlogDir() {
  config_dir=${service_dir}/config/server_static.flags
  sed -i "s#log_dir=../log/#log_dir=/data/logs/${MY_POD_NAME}/#" $config_dir
  if [ ! -e /data/logs/${MY_POD_NAME} ];then
    mkdir -p /data/logs/${MY_POD_NAME}
  fi
  if [ ! -e ${service_dir}/log ];then
    ln -sv /data/logs/${MY_POD_NAME} ${service_dir}/log
  elif [ ! -L ${service_dir}/log ];then
    rm -rf ${service_dir}/log
    ln -sv /data/logs/${MY_POD_NAME} ${service_dir}/log
  fi
}

function SymLinkSimpleIncDir() {
  if [ ! -e /data/web_server/ad_index_inc/${MY_POD_NAME}/simple.inc ];then
    mkdir -p /data/web_server/ad_index_inc/${MY_POD_NAME}/simple.inc
  fi
  if [ ! -e ${service_dir}/ad_index/simple.inc ];then
    ln -sv /data/web_server/ad_index_inc/${MY_POD_NAME}/simple.inc ${service_dir}/ad_index/simple.inc
  elif [ ! -L ${service_dir}/ad_index/simple.inc ];then
    rm -rf ${service_dir}/ad_index/simple.inc
    ln -sv /data/web_server/ad_index_inc/${MY_POD_NAME}/simple.inc ${service_dir}/ad_index/simple.inc
  fi
}

function ModifyCandidateFlags() {
  if [[ "${KWS_SERVICE_STAGE}/${KWS_LANE_ID}" = "CANDIDATE/ad_test" || "${KWS_SERVICE_STAGE}/${KWS_LANE_ID}" = "CANDIDATE/ad_prt" ]]; then
    is_exist=`cat ${service_dir}/config/server_static.flags | grep "open_chain_log"`
    if [ -z "$is_exist" ]; then
      echo "--open_trace_log=true" >> ${service_dir}/config/server_static.flags
      echo "--open_chain_log=true" >> ${service_dir}/config/server_static.flags
    fi
  fi
}

if [ -f $absolute_path/export_classpath.sh ]; then
  source $absolute_path/export_classpath.sh
fi

if [ -f $absolute_path/model_sync.sh ]; then
  source $absolute_path/model_sync.sh
fi

if [ -f $service_dir/ksp-export.sh ]; then
  source "$service_dir/ksp-export.sh"
  #物理机没有 MY_POD_IP 环境变量，埋一个
  which nslookup awk >/dev/null
  if [ $? -eq 0  -a "X${MY_POD_IP}" = "X" ]; then
    export MY_POD_IP="$(nslookup $(hostname)|grep -A2 "$(hostname)"|grep Address|awk -F ':' '{print $NF}'|sed 's/ //g')"
  fi
fi

if [ -f "/etc/profile.d/kws_env.sh" ]; then
  source /etc/profile.d/kws_env.sh
fi
if [ "X${KWS_LANE_ID}" = "X" ] && [ "X${KWS_SERVICE_GROUP}" = "X" ]; then   # 外部未设置的情况下进行设置
  if [ "${KWS_SERVICE_REGION}/${KWS_SERVICE_STAGE}" = "HB1/PREONLINE" ]; then
    # @蔡云飞 @张光磊 其他同学请勿修改
    if [ "X${KWS_SERVICE_AZ}" = "XYZ" ]; then
      # auto pub 只允许在 YZ/PREONLINE
      case "${KWS_SERVICE_NAME}" in
        "ad-front-server" |\
        "ad-front-server-knews"|\
        "ad-front-server-search" |\
        "ad-front-server-splash" |\
        "ad-front-server-follow" |\
        "ad-front-server-kwai-galaxy" |\
        "ad-front-server-universe")
          export KWS_LANE_ID="ad.front"
          ;;
        "ad-adserver" |\
        "ad-adserver-universe" |\
        "ad-server" |\
        "ad-server-universe" |\
        "ad-fanstop" |\
        "ad-nearby-fanstop" |\
        "ad-server-detail" |\
        "ad-server-galaxy" |\
        "ad-server-knews" |\
        "ad-server-merchant" |\
        "ad-server-search" |\
        "ad-server-thanos" |\
        "ad-brand")
          export KWS_LANE_ID="ad.adserver"
          ;;
        "ad-rank-server" |\
        "ad-rank-server-detail" |\
        "ad-rank-server-galaxy" |\
        "ad-rank-server-knews" |\
        "ad-rank-server-merchant" |\
        "ad-rank-server-search" |\
        "ad-rank-server-splash" |\
        "ad-rank-server-universe" |\
        "ad-rank-server-thanos")
          export KWS_LANE_ID="ad.rank"
          ;;
        "ad-target-server" |\
        "ad-target-server-knews" |\
        "ad-target-server-large" |\
        "ad-target-server-splash" |\
        "ad-target-server-thanos" |\
        "ad-target-search-default" |\
        "ad-target-server-search" |\
        "ad-target-server-galaxy" |\
        "ad-target-server-universe")
          export KWS_LANE_ID="ad.target"
          ;;
        "ad-pack-server" |\
        "ad-pack-server-universe")
          export KWS_LANE_ID="ad.pack"
          ;;
        #内循环auto-pub拆分
        "ad-server-fanstop")
          export KWS_LANE_ID="ad.adserver-fanstop"
          ;;
        "ad-rank-server-fanstop")
          export KWS_LANE_ID="ad.rank-fanstop"
          ;;
        "ad-target-server-fanstop" |\
        "ad-merchant-dsp" |\
        "ad-target-server-merchant" |\
        "ad-target-server-merchant-live" |\
        "ad-merchant-dsp-live")
          export KWS_LANE_ID="ad.target-fanstop"
          ;;
        #ps-router auto-pub
        "ad-twin-towers-router" |\
        "ad-twin-towers-router-thanos" |\
        "ad-twin-towers-router-universe" |\
        "ad-tdm-router" |\
        "ad-predict-router" |\
        "ad-predict-router-universe" |\
        "ad-predict-router-ic-retrieval" |\
        "ad-predict-router-dsp-ic" |\
        "ad-predict-router-dsp" |\
        "ad-predict-router-dsp-retrieval")
          export KWS_LANE_ID="ad.router"
          ;;
        # bid-service auto-pub
        "ad-bid-service" |\
        "ad-bid-service-fanstop" |\
        "ad-bid-service-universe")
          export KWS_LANE_ID="ad.bid"
          ;;
        *)
          ;;
      esac
    elif [ "X${KWS_SERVICE_AZ}" = "XZW" ]; then
      if [ "X${PREONLINE_ZW_LANE_ID}" = "Xad.preonline-zw" ]; then
        # ZW/PREONLINE 统一管理为相同的泳道
        case "${KWS_SERVICE_NAME}" in
          "ad-pack-server" |\
          "ad-pack-server-universe" |\
          "ad-front-server" |\
          "ad-front-server-knews"|\
          "ad-front-server-search" |\
          "ad-front-server-splash" |\
          "ad-front-server-follow" |\
          "ad-front-server-kwai-galaxy" |\
          "ad-front-server-universe" |\
          "ad-adserver" |\
          "ad-adserver-universe" |\
          "ad-server" |\
          "ad-server-universe" |\
          "ad-fanstop" |\
          "ad-nearby-fanstop" |\
          "ad-server-detail" |\
          "ad-server-fanstop" |\
          "ad-server-galaxy" |\
          "ad-server-knews" |\
          "ad-server-merchant" |\
          "ad-server-search" |\
          "ad-server-thanos" |\
          "ad-rank-server" |\
          "ad-rank-server-detail" |\
          "ad-rank-server-fanstop" |\
          "ad-rank-server-galaxy" |\
          "ad-rank-server-knews" |\
          "ad-rank-server-merchant" |\
          "ad-rank-server-search" |\
          "ad-rank-server-splash" |\
          "ad-rank-server-thanos" |\
          "ad-rank-server-universe" |\
          "ad-merchant-dsp" |\
          "ad-merchant-dsp-live" |\
          "ad-target-server-merchant" |\
          "ad-target-server-merchant-live" |\
          "ad-target-server" |\
          "ad-target-server-fanstop" |\
          "ad-target-server-knews" |\
          "ad-target-server-large" |\
          "ad-target-server-splash" |\
          "ad-target-server-thanos" |\
          "ad-target-search-default" |\
          "ad-target-server-search" |\
          "ad-target-server-galaxy" |\
          "ad-target-server-universe" |\
          "ad-brand" |\
          "ad-brand-pinpai" |\
          "ad-twin-towers-router" |\
          "ad-twin-towers-router-thanos" |\
          "ad-twin-towers-router-universe" |\
          "ad-tdm-router" |\
          "ad-predict-router" |\
          "ad-predict-router-universe" |\
          "ad-predict-router-ic-retrieval" |\
          "ad-predict-router-dsp-ic" |\
          "ad-predict-router-dsp" |\
          "ad-predict-router-dsp-retrieval" |\
          "ad-bid-service" |\
          "ad-style-server" |\
          "ad-style-server-universe" |\
          "ad-query-retrieval" |\
          "ad-bid-service-fanstop" |\
          "ad-bid-service-universe")
            export KWS_LANE_ID="ad.preonline-zw"
            ;;
          *)
            ;;
        esac
      fi
    fi
    echo "[$(date +%F\ %T)] export KWS_LANE_ID = ${KWS_LANE_ID}"
  fi
fi
if [ -f $service_dir/bin/before_start_hook.sh ];then
  source $service_dir/bin/before_start_hook.sh
fi

if [ "X${AUTO_PERF_HEAP_DIR}" != "X" ]; then
  mkdir -p ${AUTO_PERF_HEAP_DIR}
  if [ -d "${AUTO_PERF_HEAP_DIR}" ]; then
    ## 复制 bin 文件
    cp $exe_absolute_path ${AUTO_PERF_HEAP_DIR}/
    ## 使用 jemalloc 每申请 2GB 导出一个 Heap 文件到 ${AUTO_PERF_HEAP_DIR}
    export MALLOC_CONF="prof:true,prof_active:true,prof_prefix:${AUTO_PERF_HEAP_DIR}/jperf,lg_prof_interval:31"
  else
    echo "[$(date +%F\ %T)] ${AUTO_PERF_HEAP_DIR} create failed, check access"
  fi
fi
if [ "$(ulimit -c)" != "unlimited" ]; then
  ulimit -c unlimited
fi

if [ -f ${service_dir}/config/dynamic_json_config.py ]; then
  python ${service_dir}/config/dynamic_json_config.py > ${service_dir}/config/dynamic_json_config.json
fi

if [ -x "${HADOOP_HOME}/bin/hadoop" ]; then
  export JAVA_TOOL_OPTIONS="-Djdk.lang.processReaperUseDefaultStackSize=true "$JAVA_TOOL_OPTIONS
  export CLASSPATH=`${HADOOP_HOME}/bin/hadoop classpath --glob`:${CLASSPATH}
fi

export ALERT_LEVEL
export ALERT_MESSAGE

if [[ `$exe_absolute_path --version` ]]
then
  compiler_dev=`$exe_absolute_path --version | grep $service_name |awk '{print $5}'| sed 's/@.*//g'`
  if [ -n "$compiler_dev" ]
  then
    if [[ "$compiler_dev" != root ]]
    then
      compiler=$compiler_dev
    else
      compiler=`$exe_absolute_path --version | grep $service_name | awk '{print $5}' | awk -F '-' '{print $2}'`
    fi
  fi
  if [[ "$compiler" == jenkins && "${KWS_SERVICE_STAGE}" != CANDIDATE ]]
  then
    ALERT_LEVEL="P0"
    ALERT_MESSAGE="$compiler"
  else
    ALERT_LEVEL="P2"
    ALERT_MESSAGE="$compiler"
  fi
else
  echo "$exe_absolute_path  not support --version"
fi

if [[ $DEPLOYMENT_PLATFORM == "CLOUD" ]];then
  ChangeGlogDir
  SymLinkSimpleIncDir
  ModifyCandidateFlags
fi

if [ -f /opt/data/kess/gaea/agent/latest/gaea_agent_startup.sh ]; then
  exec /opt/data/kess/gaea/agent/latest/gaea_agent_startup.sh -s "LD_LIBRARY_PATH=$service_dir/lib/:/home/<USER>/mkl_lib/:/home/<USER>/mkl_lib:/usr/local/cuda-9.0/lib64/ exec $exe_absolute_path $opts --flagfile=../config/server_static.flags" -w $service_dir 2>&1
else
  LD_LIBRARY_PATH=$service_dir/lib/:/home/<USER>/mkl_lib/:/home/<USER>/mkl_lib:/usr/local/cuda-9.0/lib64/ exec $exe_absolute_path $opts --flagfile=../config/server_static.flags
fi

