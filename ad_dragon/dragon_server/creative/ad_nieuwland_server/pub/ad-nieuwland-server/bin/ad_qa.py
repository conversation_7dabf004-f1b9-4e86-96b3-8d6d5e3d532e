
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
try:
    import commands  # python2
except:
    import subprocess as commands  # python3
import json
import logging
import os
import socket
try:
    import urllib2  # python2
except:
    import urllib.request as urllib2  # python3
    import urllib
import traceback
import tornado.httpserver
import tornado.ioloop
import tornado.web
from tornado.options import define, options
import time
import sys
import threading
try:
    reload(sys)
except:
    from importlib import reload
    reload(sys)

#sys.setdefaultencoding('utf8')

define("port", default=-1, help="run on the given port", type=int)

class DeployHelper:
    def get_data(self, url, form_data=None):
      http_headers = {"Content-Type": "application/json"}
      data = {}
      i=0
      while(i<3):
          try:
              logging.info("get_data start")
              if form_data:
                  request = urllib2.Request(url, headers=http_headers, data=json.dumps(form_data))
              else:
                  request = urllib2.Request(url, headers=http_headers)
              contents = urllib2.urlopen(request, timeout=30).read()
              data = json.loads(contents)
              logging.info("get_data end")
              break
          except Exception as e:
              i+=1
              msg = traceback.format_exc()
              logging.warning("Exception: %s\n%s" % (str(e), msg))
              logging.critical("Network err at:%s" % url)
      return data

    def execute(self, cmd):
        logging.debug("exec [%s]" % cmd)
        code, output = commands.getstatusoutput(cmd)
        logging.debug("exec [%s] end" % cmd)
        return code == 0, output

    def get_project_info(self):
        logging.info("get_project_info start")
        url='https://diffenv.test.gifshow.com/autotest_getservice?local_host=%s&pod_name=%s' % (socket.gethostname(),getEnvironValue("MY_POD_NAME", default = ""))
        resp = self.get_data(url)
        logging.info("get_project_info get_data end")
        # resp = {
        #   'build_user' : 'zhangguanglei',
        #   'deploy_path' : '/data/project/ad_server',
        #   'commands' : [  # 所有的 command 都会切换到 deploy_path 执行
        #     'bash bin/load.sh stop',
        #     'rm -rf ad_index/simple.inc',
        #     'rm -rf /data/coredump/core*',
        #     'bash bin/load.sh start'
        #   ],
        #   'code': 0,
        #   'product_name' : 'ad-adserver',
        #   'product_url' : 'http://ksp.corp.kuaishou.com/api/product/products/467/versions/download/?name=1.0.3774-b186446_c186446_all'
        # }

        try :
            logging.info(resp)
            if 'code' in resp and resp['code'] == 0:
                self.deploy_path=resp['deploy_path']  # deploy path
                self.product_url=resp['product_url']  # ksp product url
                self.product_name=resp['product_name'] # product name (ad_server/target_server etc)
                self.build_user=resp['build_user']  # build user (zhangguanglei)
                self.commands=resp['commands']  # execute at deploy_path
                self.oper_type=resp.get('oper_type',0) # 2-cloud ;0/1-physical machine deploy
                return True
            else:
                logging.error("Bad response for url: %s --> resp: %s" % (url, json.dumps(resp)))
                return False
        except Exception as e:
            msg = traceback.format_exc()
            logging.warning("Exception: %s\n%s" % (str(e), msg))
            return False

    def report_status(self, isok, msg):
        if not isok:
            logging.error("FIXME %s" % msg)
            raise Exception("%s" % msg)

    def deploy(self):
        if self.get_project_info():
            tmp_file = '/tmp/tmp_%s_%s.tgz' % (self.build_user, self.product_name)
            commands = [
                "rm -rf %s" % tmp_file,
                "wget -c -T30 -t3  %s -O %s" % (self.product_url, tmp_file),
                "md5sum %s" %tmp_file,
                "tar -zxf %s -C %s" % (tmp_file, self.deploy_path),
                "rm -rf %s" % tmp_file
            ]

            if self.oper_type==2: commands=[]
            for command in self.commands:
                commands.append("cd %s && %s" % (self.deploy_path, command))

            n=1
            while(n<=10):
                stat=0
                for command in commands:
                    ret, msg = self.execute(command)
                    if not ret:
                        logging.error("command %s exec fail, error info: %s" % (command, msg))
                        if n==10: return False
                        n=n+1
                        stat=1
                        time.sleep(60)
                        break
                    else:
                        logging.info("command %s exec success, info :%s" % (command,msg))
                if stat==0: return True
        else:
            return False

    def coverage(self, deploypath, module, version, flow):
        gcovpath = "%s/bin/coverage/.kbuild-out/.build/" %deploypath
        logging.info("gcovpath: %s" % (gcovpath))
        if not os.path.isdir(gcovpath):return False
        #merchant default流量单独处理下
        if module == 'merchant':
            module = 'adtarget'
            flow = 'merchant'

        logging.info("gcovpath is dir")
        find_cmd = "find %s/bin/coverage/.kbuild-out/.build/ -name '*.gcda' -type f -print | grep -v 'teams/cdn/' | xargs ls -l | head -n 1" % deploypath
        find_cmd1 = "find %s/bin/coverage_ori/.kbuild-out/.build/ -name '*.gcda' -type f -print | grep -v 'teams/cdn/' | xargs ls -l | head -n 1" % deploypath
        output = os.popen("cd %s && %s && sh bin/load.sh stop && mv %s/bin/coverage %s/bin/coverage_ori && %s" % (deploypath, find_cmd, deploypath, deploypath, find_cmd1))
        logging.info("stop output: %s" % (output.read()))
        logging.info("cd %s && sh bin/load.sh stop && mv %s/bin/coverage %s/bin/coverage_ori" % (deploypath, deploypath, deploypath))
        self.execute("cd %s/bin/coverage_ori && rm -f gcda.list gcda_%s.tar.gz && \
                      find .kbuild-out/.build/ -name '*.gcda' -type f -print | grep -v 'teams/cdn/' > gcda.list && \
                      tar -T gcda.list -czvf gcda_%s.tar.gz && cd -" %(deploypath, flow, flow))
        logging.info("tar gcda success")
        csc_cmd = "wget -O csc_linux_amd64.tar.gz https://halo.corp.kuaishou.com/api/cloud-storage/v1/public-objects/devcloud-product/cloud-storage%2Fcsc_linux_amd64_1.0.tar.gz && tar -xzvf csc_linux_amd64.tar.gz && chmod +x csc && cd -"
        gcov_cmd = "cd %s/bin/coverage_ori && " % deploypath
        self.execute(gcov_cmd + csc_cmd)
        logging.info("gcov_cmd + csc_cmd: %s" % (gcov_cmd + csc_cmd))

        pub_url = "http://ad-checker.test.gifshow.com/api/coverage_pub_flow_info"
        items = '{"key": "%s", "type": "md5"}' % (version)
        status, output = self.execute("curl -X POST -H 'Content-Type: application/json' -d '%s' '%s'" %(items, pub_url))
        md5 = version
        if status:
            md5 = output.split('\n')[-1]

        cloudpath="engine_coverage/%s/%s/gray/" %(module, md5)
        self.execute("cd %s/bin/coverage_ori && ./csc upload gcda_%s.tar.gz %s && cd -" % (deploypath, flow, cloudpath))
        logging.info("deploypath, flow, cloudpath: %s, %s, %s" % (deploypath, flow, cloudpath))

        items='{"module_name":"%s", "keyid":"%s", "gcc_version": "10", "mode": "gray", "flow": "%s"}' %(module, md5, flow)
        cloudurl='http://ad-coverage.test.gifshow.com/api/utils/gen_cov'
        self.execute("curl -X POST -H 'Content-Type: application/json' -d '%s' '%s'" %(items, cloudurl))
        logging.info("curl -X POST -H 'Content-Type: application/json' -d '%s' '%s'" %(items, cloudurl))
        self.execute("rm -rf %s/bin/coverage %s/bin/coverage_ori" % (deploypath, deploypath))
        logging.info("rm -rf %s/bin/coverage %s/bin/coverage_ori" % (deploypath, deploypath))
        return True

class autotest_deployMainHandler(tornado.web.RequestHandler):
    def get(self):
        logging.info("autotest_deployMainHandler get")
        deployer = DeployHelper()
        stat=deployer.deploy()

        code=0
        if stat==False:code=1

        resp = {"code":code}
        self.write(resp)
        self.finish()
        logging.info("autotest_deployMainHandler end")

class autotest_coverageMainHandler(tornado.web.RequestHandler):
    def get(self):
        logging.info("autotest_coverageMainHandler get")
        module = self.get_argument('module','')
        flow = self.get_argument('flow','')
        version = self.get_argument('version','')
        deploypath = self.get_argument('deploy_path','')
        logging.info("module:[%s], flow:[%s], version:[%s], deploypath:[%s]" % (module, flow, version, deploypath))

        deployer = DeployHelper()
        stat=deployer.coverage(deploypath, module, version, flow)

        resp = {"code":0}
        self.write(resp)
        self.finish()
        logging.info("autotest_coverageMainHandler end")

class autotest_statusMainHandler(tornado.web.RequestHandler):
    def get(self):
        logging.info("autotest_statusMainHandler get")

        stat=1
        module = self.get_argument('module','')
        pod = self.get_argument('pod','')

        SERVICE_NAME =  getEnvironValue('KWS_SERVICE_NAME', default = "")
        POD_NAME=getEnvironValue('MY_POD_NAME', default = "")

        if module==SERVICE_NAME and pod==POD_NAME:
            stat=0

        resp = {"code":stat}
        self.write(resp)
        self.finish()
        logging.info("autotest_statusMainHandler end")

class autotest_portStatMainHandler(tornado.web.RequestHandler):
    def get(self):
        logging.info("autotest_portStatMainHandler get")
        stat=1

        port=self.get_argument('port','0')
        pod_name=self.get_argument('pod_name','')

        if port and pod_name:
            cmd="cat /home/<USER>/PORT_INFO.json|grep %s|wc -l" %(port)
            logging.debug(cmd)
            ret,out=commands.getstatusoutput(cmd)
            if ret==0 and int(out)>0:
                cmd="env|grep MY_POD_NAME|grep %s|wc -l" %(pod_name)
                ret,out=commands.getstatusoutput(cmd)
                if ret==0 and int(out)>0: stat=0

        resp = {"code":stat}
        self.write(resp)
        self.finish()
        logging.info("autotest_portStatMainHandler end")

class autotest_isCoreMainHandler(tornado.web.RequestHandler):
    def get(self):
        logging.info("autotest_isCoreMainHandler get")
        stat=0

        start=self.get_argument('starttime','')
        binpath=self.get_argument('binpath','')
        if start and binpath:
            cmd='find %s -name "*.backtrace" -newermt "%s"|wc -l' %(binpath, start)
            logging.debug(cmd)
            ret,out=commands.getstatusoutput(cmd)
            if ret==0:
                logging.info(out)
                stat=int(out)

        resp = {"code":stat}
        self.write(resp)
        self.finish()
        logging.info("autotest_isCoreMainHandler end")

class autotest_execCmdMainHandler(tornado.web.RequestHandler):
    def get(self):
        logging.info("autotest_execCmdMainHandler get")
        ret=0
        out=''

        cmd=self.get_argument('cmd','')
        if cmd:
            logging.debug(cmd)
            ret,out=commands.getstatusoutput(cmd)
            if ret==0:logging.info(out)

        resp = {"code":ret, "result":out}
        self.write(resp)
        self.finish()
        logging.info("autotest_execCmdMainHandler end")

class checkStatusMainHandler(tornado.web.RequestHandler):
    def get(self):
        try:
            logging.info("checkStatusMainHandler start")
            res = {"status":2, "message":"success"}

            self.write(res) #what meaning
            self.finish()
            logging.info("checkStatusMainHandler end")
        except Exception as e:
            logging.error("checkStatusMainHandler get Exception:", e)

def getFreePort1():
    sock = socket.socket()
    try:
        sock.bind(('', 0))
        ip, port = sock.getsockname()
        sock.close()
    except Exception as e:
        port = -1

    return port

def getFreePort():
    try:
        port=-1
        with open('/home/<USER>/PORT_INFO.json','r') as f:
            PORT_CONF = json.load(f)
            #max_port = len(PORT_CONF) - 1
            #port_key = 'AUTO_PORT' + str(max_port)
            #port = PORT_CONF.get(port_key, 0)
            port=PORT_CONF.get('AUTO_PORT3', 0)
    except Exception as e:
        port = 0

    return port

def getWebPort():
    try:
        port=0
        with open('/home/<USER>/PORT_INFO.json','r') as f:
            PORT_CONF=json.load(f)
            port=PORT_CONF.get('AUTO_PORT0', 0)
    except Exception as e:
        port = 0

    return port

def getEnvironValue(key_name, default):
    try:
       environ_dict = os.environ
       value_of_key = environ_dict.get(key_name)
    except Exception as e:
       value_of_key = ''

    return default if value_of_key == "" else value_of_key

def uploadAddress(port):
    header_dict = {"Content-Type":"application/json;charset:utf8"}
    hostname = socket.gethostname()
    local_ip = socket.gethostbyname(hostname)
    ip_port = '%s:%s' % (local_ip, port)

    module_name =  getEnvironValue('KWS_SERVICE_NAME', default = "")
    module_region = getEnvironValue('KWS_SERVICE_REGION', default = "")
    module_envtype = getEnvironValue('KWS_SERVICE_STAGE', default = "")
    if "CANDIDATE" == module_envtype:
        module_envtype = "CANDIDATE_gray"

    channel="CM_%s" %module_envtype

    module_az = getEnvironValue('KWS_SERVICE_AZ', default = "")
    pod_name=getEnvironValue('MY_POD_NAME', default = "")
    cluster_name=getEnvironValue('CLUSTER_NAME', default = "")
    namespace=getEnvironValue('MY_POD_NAMESPACE', default = "default")
    laneid=getEnvironValue('KWS_LANE_ID', default = "")

    land_cmd=""
    if pod_name and cluster_name and namespace:
        land_cmd="kcsctl -s %s exec %s -n %s" %(cluster_name,pod_name,namespace)

    webport=getWebPort()
    tmp={"land_cmd":land_cmd, "pod_name":pod_name, "lane_id":laneid, "web_port":webport}
    url_params = {"status":"online", "machinename":hostname, "host":ip_port, "logintype": "cloud", "modulename":module_name, "region":module_region, "envtype":module_envtype, "az":module_az, "extra": json.dumps(tmp), "channel":channel}

    url_params_encode = json.dumps(url_params)
    url = 'https://ad-env.test.gifshow.com/machine/add'

    try:
        data = bytes(url_params_encode,"utf-8")
    except:
        data=url_params_encode
    logging.debug(data)

    try:
        req = urllib2.Request(url = url,data = data, headers = header_dict)
        response = urllib2.urlopen(req, timeout=30)
        res_data = response.read()
        logging.debug(res_data)

        if(int(json.loads(res_data)['status']) != 0):
            logging.error("response status is:", json.loads(res_data)['status'])
        response.close()
    except Exception as e:
        logging.error(e)

    return

def uploadJobScheduler():
    logging.info("uploadJobScheduler thread start")
    time.sleep(60)
    while(1):
        logging.info("uploadAddress start")
        uploadAddress(freePort)
        logging.info("uploadAddress success")

        time.sleep(60*60)
    logging.error("uploadJobScheduler exit!!")

    upload_thread = threading.Thread(target = uploadJobScheduler)
    upload_thread.start()

if __name__ == '__main__':
    logging.basicConfig(
                        datefmt='%Y-%m-%d %H:%M:%S',
                        level=logging.DEBUG,
                        #level=logging.INFO,
                        format="%(levelname)s %(asctime)s %(filename)s:%(lineno)d,%(funcName)s]%(message)s")


    curr_dir = sys.path[0]
    artifact_file_path = os.path.join(curr_dir, "../")
    if not os.path.isfile("%s/artifact.tar.gz" % artifact_file_path):
        os.chdir(artifact_file_path)
        cmd = "touch .artifact_test && tar -zcvf artifact.tar.gz ./* --exclude=log --exclude=artifact.tar.gz ."
        ret, _ = commands.getstatusoutput(cmd)
        if ret:
            sys.exit(1)
        os.chdir(curr_dir)

    hostname = socket.gethostname()
    local_ip = socket.gethostbyname(hostname)

    tornado.options.options.logging = "debug"
    tornado.options.parse_command_line()
    app = tornado.web.Application(handlers=[(r"/autotest_deploy",autotest_deployMainHandler),
                                  (r"/autotest_status",autotest_statusMainHandler),
                                  (r"/autotest_portstat",autotest_portStatMainHandler),
                                  (r"/checkstatus", checkStatusMainHandler),
                                  (r"/autotest_iscore", autotest_isCoreMainHandler),
                                  (r"/autotest_execmd", autotest_execCmdMainHandler),
                                  (r"/autotest_coverage",autotest_coverageMainHandler)])
    http_server = tornado.httpserver.HTTPServer(app)

    if not options.port or int(options.port) == -1:
        retryTimes = 0
        while retryTimes < 3:
            freePort = getFreePort()
            logging.debug("get free port %s" % freePort)
            if freePort > 1024 and freePort < 65535:
                break
            retryTimes += 1

        if retryTimes >=3:
            logging.error("getfree port failed exceed 3 times")
            sys.exit(1)
    else:
        freePort = options.port

    logging.info("web server listen port is %d" %freePort)

    upload_thread = threading.Thread(target = uploadJobScheduler)
    upload_thread.start()

    http_server.bind(freePort)

    http_server.start(num_processes=2)
    tornado.ioloop.IOLoop.instance().start()
