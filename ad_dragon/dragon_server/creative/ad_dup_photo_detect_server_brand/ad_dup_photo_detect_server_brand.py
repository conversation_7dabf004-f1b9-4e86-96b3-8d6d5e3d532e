#!/usr/bin/env python3
# coding=utf-8
import os
import sys
dragon_path=os.path.abspath('../../../../../../dragon/')
ad_dragon_path=os.path.abspath('../../../../ad_dragon/')
sys.path.append(dragon_path)
sys.path.append(ad_dragon_path)

from dragonfly.common_leaf_dsl import LeafFlow, OfflineRunner
from dragon_server.creative.constants import *
from ks_leaf_functional.core.module import module
from dragon_server.creative.common import AdCreativeCommonApiMixin
from ks_leaf_functional.core.data_manager import data_manager, ab_param as ab, kconf_param as kconf

#===================================================== 常量定义 =====================================================
ns = "ad.ad_dup_photo_detect_server_brand"
target_photo_table = "TargetPhotoInfoTable"   # target photo 表名
query_photo_table = "QueryPhotoInfoTable"  # query photo 表名
recent_cache_key_prefix = "rrec_"
emb_cache_prefix = "ndpecp_"
frame_key_prefix = "npkf_"
cache_cluster = "adDupPhotoCacheBrand"
dup_id_cache_prefix = "dip_"
dup_score_cache_prefix = "dsp_"

class AdDupPhotoDetectServerBrand(LeafFlow, AdCreativeCommonApiMixin):
#===================================================== stage1:photo信息更新并请求mmu获取embeddng =======================================================
  @module()
  def batch_photo_message_collection(self):
    """
      从kafka消费photo存量数据
    ------
    整体流程:
    ad_material_fetch_message_from_kafka: 获取photo增量消息
    """
    self.ad_material_fetch_message_from_kafka( # 从指定 topic 获取数据
      no_check = True,
      kafka_group = "ad_dup_photo_detect_server_brand",
      candidate_kafka_group = "ad_dup_photo_detect_server_brand_test",
      kafka_topic = "ad_brand_batch_photo_info",
      kafka_params = "kuaishou.consume.delay.ms=0;",
      sleep_time = 200,
      out_string_column = "trigger_msg",
      msg_timestamp_column = "kafka_msg_fetch_timestamp"
    ).enrich_attr_by_lua(
        import_common_attr = ["trigger_msg"],
        function_for_common = "parseString",
        export_common_attr = ["photo_id", "create_time", "photo_id_str"],
        lua_script = """
          function parseString()
            reps = "-"
            local photo_id = 0
            local create_time = 0
            local i = 1
            string.gsub(trigger_msg,'[^'..reps..']+',function (w)
              if i == 1 then
                photo_id = tonumber(w)
                i = i + 1
              elseif i == 2 then
                create_time = tonumber(w)
                i = i + 1
              end
            end)
            return photo_id, create_time, tostring(photo_id)
          end
        """
      ).if_("photo_id == 0 or create_time == 0") \
      .perflog(
        mode = "count",
        namespace = ns,
        subtag = "admit",
        extra1 = "id_or_ctime_zero"
      ).return_() \
    .end_if_() \
    .enrich_attr_by_lua(   # 计算创建时间间隔 大于1年半丢弃
      import_common_attr = ["create_time"],
      function_for_common = "calcCreateDuration",
      export_common_attr = [ "duration"],
      lua_script = """
        function calcCreateDuration()
          local ts = util.GetTimestamp() // 1000
          return (ts - create_time) // 86400000
        end
      """
    ).if_("duration > 547") \
      .perflog(
        mode = "count",
        namespace = ns,
        subtag = "admit",
        extra1 = "photo_too_old"
      ).return_() \
    .end_if_() \
    .enrich_attr_by_lua(   # photo_id 转 photo_id_list
      no_check=True,
      import_common_attr = ["photo_id"],
      function_for_common = "buildPhotoIdList",
      export_common_attr = ["photo_id_list"],
      lua_script = """
        function buildPhotoIdList()
          local photo_id_list = {}
          photo_id_list[1] = photo_id
          return photo_id_list
        end
      """
    ).build_table_from_common_list_attr(  #构造photo表
      new_table = query_photo_table,
      no_check =True,
      build_config = [  # 第一行默认为主键
        dict(src = "photo_id_list", dest = "photo_id")
      ]
    ).set_attr_mod_enricher(  # photoId 取模映射 为避免消息不均匀 使用质数取模
      no_check=True,
      is_common_attr = True,
      key_column = "photo_id",
      mod_num = 7,
      output_column = "subflow_shard_id"
    ).ad_message_queue_dispatch_mixer(
      task_queue_id="{{subflow_shard_id}}",
      message_queue_type=0,
      packed_common_attrs = ["photo_id", "create_time", "photo_id_str"],
      packed_table_columns = [
        {
          "table_name" : query_photo_table,
          "columns" : ["photo_id"]
        }
      ]
    )
    return self

  def inc_photo_message_collection(self):
    """
      从kafka消费photo存量数据
    ------
    整体流程:
    ad_material_fetch_message_from_kafka: 获取photo增量消息
    """
    self.ad_material_fetch_message_from_kafka( # 从指定 topic 获取数据
      no_check = True,
      kafka_group = "ad_dup_photo_detect_server_brand",
      candidate_kafka_group = "ad_dup_photo_detect_server_brand_test",
      kafka_topic = "ad_brand_recall_photo_request",
      kafka_params = "",
      sleep_time = 200,
      out_string_column = "trigger_msg",
      msg_timestamp_column = "kafka_msg_fetch_timestamp"
    ).parse_protobuf_from_string(   # 解析pb
      input_attr="trigger_msg",
      output_attr="brand_dup_request",
      class_name="kuaishou.ad.brand.creative.CreativeBuildEmbedMessage",
    ).enrich_with_protobuf(   # 获取字段
      from_extra_var = "brand_dup_request",
      no_check =True,
      attrs = [
        dict(name="creative_id_list", path="creative_embed_info.creative_id"),
        dict(name="photo_id_list", path="creative_embed_info.photo_id"),
      ]
    ).build_table_from_common_list_attr(  # 构造photo表 补充 create_time photo_id_str
      new_table = query_photo_table,
      no_check =True,
      build_config = [  # 第一行默认为主键
        dict(src = "photo_id_list", dest = "photo_id"),
        dict(src = "creative_id_list", dest = "creative_id")
      ]
    ).enrich_attr_by_lua(   # 生产 create_time photo_id_str
      no_check=True,
      item_table = query_photo_table,
      import_item_attr = ["photo_id"],
      function_for_item = "buildPhotoInfo",
      export_item_attr = ["create_time", "photo_id_str"],
      lua_script = """
        function buildPhotoInfo()
          return util.GetTimestamp() // 1000, tostring(photo_id)
        end
      """
    ).arrange_by_sub_flow(
      item_table = query_photo_table,
      sub_flow = dispatch_flow,
      expected_partition_size=1,
      pass_common_attrs = ["photo_id_list"],
      pass_item_attrs = ["photo_id", "creative_id", "create_time", "photo_id_str"],
      merge_item_attrs = [],
      no_check = True
    )
    return self

  def dispatch(self):
    self.set_attr_mod_enricher(  # photoId 取模映射 为避免消息不均匀 使用质数取模
      no_check=True,
      key_column = "photo_id",
      mod_num = 7,
      output_column = "subflow_shard_id"
    ).ad_message_queue_dispatch_mixer(
      task_queue_id="{{subflow_shard_id}}",
      message_queue_type=0,
      packed_table_columns = [
        {
          "table_name" : query_photo_table,
          "columns" : ["photo_id", "creative_id", "create_time", "photo_id_str"]
        }
      ],
      packed_common_attrs = ["photo_id", "create_time", "photo_id_list"],
    )
    return self

  def index_inc_debug(self):
    """
    """
    self.ad_material_fetch_message_from_kafka( # 从指定 topic 获取数据
      no_check = True,
      kafka_group = "ad_dup_photo_detect_server_brand",
      candidate_kafka_group = "ad_dup_photo_detect_server_brand_test",
      kafka_topic = "target_universe_ktable_prod",
      kafka_params = "",
      out_string_column = "trigger_msg",
      msg_timestamp_column = "kafka_msg_fetch_timestamp"
    ).parse_protobuf_from_string(   # 解析pb
       input_attr = "trigger_msg",
      output_attr = "ad_instance",
      class_name = "kuaishou.ad.AdInstance"
    ).enrich_with_protobuf(   # 获取 msg_type 和 data_type
      from_extra_var = "ad_instance",
      no_check=True,
      attrs = [
        dict(name="data_type", path="type"),
      ]
    ).perflog(
      mode = "count",
      namespace = ns,
      subtag = "inc_debug",
      extra1 = "data_type"
    ).if_("data_type == 99") \
      .debug_log(
        respect_sample_logging = False,
        common_attrs = ["ad_instance"]
      ).end_if_()
    return self

  def dispatch(self):
    self.set_attr_mod_enricher(  # photoId 取模映射 为避免消息不均匀 使用质数取模
      no_check=True,
      key_column = "photo_id",
      mod_num = 7,
      output_column = "subflow_shard_id"
    ).ad_message_queue_dispatch_mixer(
      task_queue_id="{{subflow_shard_id}}",
      message_queue_type=0,
      packed_table_columns = [
        {
          "table_name" : query_photo_table,
          "columns" : ["photo_id", "creative_id", "create_time", "photo_id_str"]
        }
      ],
      packed_common_attrs = ["photo_id", "create_time", "photo_id_list"],
    )
    return self
  # TODO(wanglei10): 通用接口改造下移
  def get_embedding_from_rpc(self):
    """
      请求mmu rpc接口 得到 avg_emb_normal
    """
    self.build_protobuf( # 获取emb缓存失败时 需要请求mmu获取emb 构造生产md5请求
      class_name="mmu::video::DlFeatureRequest",
      inputs=[
        { "common_attr": "photo_id_str", "path": "id" },
        { "common_attr": "biz", "path": "source_from"},
      ],
      output_common_attr= "embedding_req",
    ).enrich_by_generic_grpc(		# 请求 md5生成服务
      kess_service="grpc_mmuDlFeatureCalculateService",
      timeout_ms= 10000,
      method_name="/mmu.video.DlFeatureCalculateService/CalculateFeature",
      request_attr= "embedding_req",
      response_attr= "embedding_resp",
      response_class="mmu::video::DlFeatureResponse"
    ).enrich_with_protobuf(   # 获取字段
      from_extra_var = "embedding_resp",
      attrs = [
        dict(name="embedding_list", path="feature.features.value.feature")
      ]
    ).enrich_attr_by_lua(   # emb 校验 1 size<=1280 2 size%128=0 3 求avg 
      import_common_attr = ["embedding_list"],
      no_check = True,
      function_for_common = "embCheckAndAvg",
      export_common_attr = [ "is_emb_valid", "avg_emb", "emb_num"],
      lua_script = """
        function embCheckAndAvg()
          local len = #embedding_list
          local emb_num = len / 128
          local avg_emb = {}
          for i = 1, 128 do
            avg_emb[i] = 0.0
          end
          if len == 0 or len % 128 ~= 0 or len > 1280 then
            return 0, avg_emb, math.floor(emb_num)
          else
            for i = 1, len do
              local idx  = i - 1
              avg_emb[idx % 128 + 1] = avg_emb[idx % 128 + 1] + embedding_list[i]
            end
            for i = 1, #avg_emb do
              avg_emb[i] = avg_emb[i] / emb_num
            end
            return 1, avg_emb, math.floor(emb_num)
          end
        end
      """
    ).embedding_normalize_enricher(   # 向量归一化
      is_common_attr = True,
      photo_embedding_column = "avg_emb",
      photo_normal_embedding_column = "avg_emb_normal"
    ).enrich_attr_by_lua(   # emb 正确性校验  TODO(wanglei10): 写缓存
      import_common_attr = ["avg_emb_normal"],
      no_check = True,
      function_for_common = "innerProduct",
      export_common_attr = [ "self_inner_product"],
      lua_script = """
        function innerProduct()
          local val = 0.0
          for i = 1, #avg_emb_normal do
            val = val + avg_emb_normal[i] * avg_emb_normal[i]
          end
          return val
        end
      """
    ).if_("is_emb_valid == 0 or math.abs(self_inner_product - 1) > epsilon") \
      .perflog(
        mode = "count",
        namespace = ns,
        subtag = "admit",
        extra1 = "emb_invalid"
      ).return_() \
    .end_if_()
    return self
  
  # TODO(wanglei10): 通用接口改造下移
  @module()
  def build_embedding_inc(self):
    """
      构造 embedding_inc 消息
    ------
    整体流程:
    """
    self.set_attr_value(
			common_attrs=[
        {"name": "long_value_type", "type": "int", "value": 2 },
        {"name": "int_value_type", "type": "int", "value": 1 },
        {"name": "string_value_type", "type": "int", "value": 5 },
        {"name": "photo_id_name", "type": "string", "value": "photo_id"},
        {"name": "create_time_name", "type": "string", "value": "create_time"},
        {"name": "emb_num_name", "type": "string", "value": "emb_num"},
        {"name": "avg_embedding_name", "type": "string", "value": "avg_embedding"},
        {"name": "is_inc_name", "type": "string", "value": "is_inc"},
        {"name": "table_name", "type": "string", "value": "ad_creative_engine:ad_photo_embedding_brand"},
        {"name": "emb_cache_prefix", "type": "string", "value": emb_cache_prefix},
      ]
		).enrich_attr_by_lua(   # 生成时间戳
      import_common_attr = ["avg_emb_normal", "photo_id", "emb_cache_prefix"],
      function_for_common = "genTsAndEmbStr",
      export_common_attr = [ "modify_time", "avg_emb_str"],
      lua_script = """
        function genTsAndEmbStr()
          return util.GetTimestamp() // 1000000, table.concat(avg_emb_normal, ";")
        end
      """
    ).build_protobuf(
      class_name="com::kuaishou::ad::dip::source::proto::SourceIncField",
      inputs=[
        { "common_attr": "photo_id_name", "path": "name" },
        { "common_attr": "long_value_type", "path": "field_type"},
        { "common_attr": "photo_id", "path": "long_value"},
      ],
      output_common_attr= "photo_field_info",
    ).build_protobuf(
      class_name="com::kuaishou::ad::dip::source::proto::SourceIncField",
      inputs=[
        { "common_attr": "create_time_name", "path": "name" },
        { "common_attr": "long_value_type", "path": "field_type"},
        { "common_attr": "create_time", "path": "long_value"},
      ],
      output_common_attr= "create_time_field_info",
    ).build_protobuf(
      class_name="com::kuaishou::ad::dip::source::proto::SourceIncField",
      inputs=[
        { "common_attr": "emb_num_name", "path": "name" },
        { "common_attr": "int_value_type", "path": "field_type"},
        { "common_attr": "emb_num", "path": "int_value"},
      ],
      output_common_attr= "emb_num_field_info",
    ).build_protobuf(
      class_name="com::kuaishou::ad::dip::source::proto::SourceIncField",
      inputs=[
        { "common_attr": "is_inc_name", "path": "name" },
        { "common_attr": "int_value_type", "path": "field_type"},
        { "common_attr": "is_inc_data", "path": "int_value"},
      ],
      output_common_attr= "is_inc_field_info",
    ).build_protobuf(
      class_name="com::kuaishou::ad::dip::source::proto::SourceIncField",
      inputs=[
        { "common_attr": "avg_embedding_name", "path": "name" },
        { "common_attr": "string_value_type", "path": "field_type"},
        { "common_attr": "avg_emb_str", "path": "string_value"},
      ],
      output_common_attr= "avg_embedding_field_info",
    ).build_protobuf(
      class_name="com::kuaishou::ad::dip::source::proto::SourceInc",
      inputs=[
          { "common_attr": "modify_time", "path": "modify_time" },
          { "common_attr": "table_name", "path": "table_name" },
          { "common_attr": "photo_field_info", "path": "fields", "append": True},
          { "common_attr": "create_time_field_info", "path": "fields", "append": True},
          { "common_attr": "emb_num_field_info", "path": "fields", "append": True},
          { "common_attr": "avg_embedding_field_info", "path": "fields", "append": True},
          { "common_attr": "is_inc_field_info", "path": "fields", "append": True}
      ],
      as_string = True,
      output_common_attr="embedding_inc_str",
    ).send_to_kafka(    # 写到 emb_inc_topic
      is_common_attr = True,
      message_column = "embedding_inc_str",
      topic = "ad_dup_photo_embedding_inc_brand"
    )
    return self

  @module()
  def cache_info_process(self):
    """
      处理 photo->md5 photo->createTime md5->photo md5->createTime 等缓存信息
    ------
    整体流程:
    """
    self.set_attr_value(   # 设置 redis_photo_create_time redis_photo_md5 默认值
			common_attrs=[
        {"name": "redis_photo_create_time", "type": "int", "value": 0},
        {"name": "redis_photo_md5", "type": "string", "value": ""},
        #{"name": "photo_blobstore_bucket", "type": "string", "value": "ad-dsp"},
        {"name": "redis_md5_seed_photo", "type": "int", "value": 0},
        {"name": "redis_md5_create_time", "type": "int", "value": 0},
        #{"name": "is_first_attach", "type": "int", "value": 1},
        {"name": "is_new_md5_generate", "type": "int", "value": 0},
        {"name": "biz", "type": "string", "value": "AD"},
        {"name": "is_emb_valid", "type": "int", "value": 0 }
      ]
        # {"name": "is_inc", "type": "int", "value": 1},
		).enrich_attr_by_lua(   # 构造获取createTime和md5的redis key
      import_common_attr = ["photo_id"],
      function_for_common = "genKey",
      export_common_attr = ["photo_create_time_key", "photo_md5_key"],
      lua_script = """
        function genKey()
          return "fpdpc_"..tostring(photo_id), "fpdpm_"..tostring(photo_id)
        end
      """
    ).get_common_attr_from_redis(   # 获取创建时间和md5
      cluster_name = cache_cluster,
      redis_params = [
        { "redis_key": "{{photo_create_time_key}}", "redis_value_type": "string", "output_attr_name": "redis_photo_create_time", "output_attr_type": "int"},
        { "redis_key": "{{photo_md5_key}}", "redis_value_type": "string", "output_attr_name": "redis_photo_md5", "output_attr_type": "string"}
      ]
    ).if_("#redis_photo_md5 ~= 0") \
      .perflog( # 如果从redis中取到 md5
        mode = "count",
        namespace = ns,
        subtag = "prepare",
        extra1 = "get_md5_succ_from_redis"
      ).if_("redis_photo_create_time ~= 0") \
        .perflog( # 如果从redis中取到createTime
          mode = "count",
          namespace = ns,
          subtag = "prepare",
          extra1 = "get_create_time_succ_from_redis"
        ).enrich_attr_by_lua(   # 取 redis createTime和请求带的createTime的较小值 如果
          import_common_attr = ["redis_photo_create_time", "create_time"],
          no_check = True,
          function_for_common = "createTimeCheck",
          export_common_attr = ["photo_create_time", "need_update_redis_create_time"],
          lua_script = """
            function createTimeCheck()
              if redis_photo_create_time > create_time then
                return create_time, 1
              else
                return redis_photo_create_time, 0
              end
            end
          """
        ).if_("need_update_redis_create_time == 1") \
          .write_to_redis(   # 如果 create_time < redis_photo_create_time 需要更新redis_photo_create_time
            kcc_cluster=cache_cluster,
            timeout_ms=10,
            key="{{photo_create_time_key}}",
            value="{{create_time}}"
          ) \
         .end_if_() \
      .else_() \
        .return_() \
      .end_if_() \
    .else_() \
      .perflog( # 从redis中取md5失败
        mode = "count",
        namespace = ns,
        subtag = "prepare",
        extra1 = "get_md5_fail_from_redis"
      ).get_photo_md5() \
      .if_("code == 0") \
        .perflog( # 获取 md5 失败 结束
          mode = "count",
          namespace = ns,
          subtag = "prepare",
          extra1 = "get_md5_fail_from_rpc"
        ).return_() \
      .end_if_() \
      .perflog( # 获取 md5 成功
        mode = "count",
        namespace = ns,
        subtag = "prepare",
        extra1 = "get_md5_succ_from_rpc"
      ).copy_attr(   # 将 create_time copy 写入 photo_create_time
        attrs=[
          {
            "from_common": "create_time",
            "to_common": "photo_create_time"
          }
        ]
      ).write_to_redis(   # photo->md5 成功 写入redis
        kcc_cluster=cache_cluster,
        timeout_ms=10,
        key="{{photo_md5_key}}",
        value="{{md5}}"
      ).write_to_redis(   # photo->ctime 写入redis
        kcc_cluster=cache_cluster,
        timeout_ms=10,
        key="{{photo_create_time_key}}",
        value="{{create_time}}"
      ) \
    .end_if_() \
    .enrich_attr_by_lua(   # 构造获取createTime和md5的redis key
      import_common_attr = ["redis_photo_md5"],
      function_for_common = "genKey",
      export_common_attr = ["md5_seed_photo_key", "md5_creative_time_key"],
      lua_script = """
        function genKey()
          return "new_pdmsp_"..redis_photo_md5, "new_pdmc_"..redis_photo_md5
        end
      """
    ).get_common_attr_from_redis(   # 获取md5指向photo和md5首次触发时间
      cluster_name = cache_cluster,
      redis_params = [
        { "redis_key": "{{md5_seed_photo_key}}", "redis_value_type": "string", "output_attr_name": "redis_md5_seed_photo", "output_attr_type": "int"},
        { "redis_key": "{{md5_creative_time_key}}", "redis_value_type": "string", "output_attr_name": "redis_md5_create_time", "output_attr_type": "int"}
      ]
    ).enrich_attr_by_lua(   # 根据获取redis数据的情况 判断要不要更新数据 如果redis中无md5信息或记录的md5触发时间大于当前请求photo createTime 0表示md5存在但无需改动 1表示
      import_common_attr = ["redis_md5_create_time", "redis_md5_seed_photo", "photo_create_time", "photo_id"],
      no_check = True,
      function_for_common = "recordCheck",
      export_common_attr = [ "md5_seed_photo", "need_update_redis_md5_info", "md5_create_time", "photo_id_str"],
      lua_script = """
        function recordCheck()
          if redis_md5_create_time == 0 or redis_md5_seed_photo == 0 then
            return photo_id, 1, photo_create_time, tostring(photo_id)
          elseif redis_md5_create_time > photo_create_time then
            return photo_id, 2, photo_create_time, tostring(photo_id)
          else
            return redis_md5_seed_photo, 0, redis_md5_create_time, tostring(photo_id)
          end
        end
      """
    ).if_("need_update_redis_md5_info ~= 0") \
      .perflog(   # 由于photo的创建时间比md5更早 需要更新md5->seedphoto映射 记录其对应photo和创建时间
        mode = "count",
        namespace = ns,
        subtag = "prepare",
        extra1 = "update_md5_info"
      ).write_to_redis(   # md5->photo 成功 写入redis
        kcc_cluster=cache_cluster,
        timeout_ms=10,
        key="{{md5_seed_photo_key}}",
        value="{{photo_id}}"
      ).write_to_redis(   # md5->ctime 写入redis
        kcc_cluster=cache_cluster,
        timeout_ms=10,
        key="{{md5_creative_time_key}}",
        value="{{photo_create_time}}"
      ) \
    .end_if_() \
    .enrich_attr_by_lua(   # 判断photo通过md5映射是否指向自己
      import_common_attr = ["md5_seed_photo", "photo_id"],
      no_check = True,
      function_for_common = "isSeedPhoto",
      export_common_attr = [ "is_seed_photo"],
      lua_script = """
        function isSeedPhoto()
          if md5_seed_photo == photo_id then
            return 1
          else
            return 0
          end
        end
      """
    )
    return self

  def get_photo_md5(self):
    """
      根据photo blobkey 获取 md5 被 cache_info_process() 方法调用
    ------
    整体流程:
    """
    self.build_protobuf( # 构造生产md5请求
      class_name="mmu::photo::GetPhotoMd5HexRequest",
      inputs=[
        { "common_attr": "photo_id", "path": "photo_id", "append": True},
      ],
      output_common_attr= "md5_req",
    ).enrich_by_generic_grpc(		# 获取 md5
      kess_service="grpc_mmuPhotoMd5QueryService",
      timeout_ms= 1000,
      method_name="/mmu.photo.MmuPhotoMd5QueryService/GetPhotoMd5",
      request_attr= "md5_req",
      response_attr= "md5_resp",
      response_class="mmu::photo::GetPhotoMd5HexResponse"
    ).enrich_with_protobuf(
      from_extra_var = "md5_resp",
      attrs = [
        #dict(name="resp_photo_ids", path="photo_md5.key"),
        dict(name="resp_md5s", path="photo_md5.value")
      ]
    ).enrich_attr_by_lua(   # 获取 md5
      import_common_attr = ["resp_md5s"],
      function_for_common = "getMd5",
      export_common_attr = [ "redis_photo_md5", "code"],
      lua_script = """
        function getMd5()
          local n = #resp_md5s
          if n > 0 then
            return resp_md5s[n], 1
          else
            return "", 0
          end
        end
      """
    ).set_attr_value(
      common_attrs=[
        {"name": "is_new_md5_generate", "type": "int", "value": 1}
      ]
    )
    return self

  @module()
  def recent_photo_cache(self):
    """
      分钟级别存储photo信息
    ------
    整体流程:
    """
    self.build_protobuf(  # 构造 ItemEmbeddingInfo 结构 写 redis 缓存
			class_name="ks::ad_dragon::kconf::ItemEmbeddingInfo",
			inputs=[
				{ "common_attr": "photo_id", "path": "item_id" },
        { "common_attr": "photo_create_time", "path": "create_time" },
				{ "common_attr": "avg_emb_normal", "path": "value", "append": True},
			],
      as_string = True,
			output_common_attr= "photo_embedding_str",
		).set_attr_value(   # 设置recent触发photo的
			common_attrs=[
        {"name": "recent_cache_key_prefix", "type": "string", "value": recent_cache_key_prefix},
      ]
		).enrich_attr_by_lua(   # 构造 redis embedding 缓存 key
      no_check=True,
      import_common_attr = ["recent_cache_key_prefix"],
      function_for_common = "genEmbeddingCacheRedisKey",
      export_common_attr = ["redis_emb_cache_key", "ts_min"],
      lua_script = """
        function genEmbeddingCacheRedisKey()
          local ts_min = util.GetTimestamp() // 60000000
          return recent_cache_key_prefix..tostring(ts_min), ts_min
        end
      """
    ).redis_simple_write(
      op = "SAdd",
      is_common_attr = True,
      cluster_name = cache_cluster,
      key_column = "redis_emb_cache_key",
      value_column = "photo_embedding_str",
      ttl = 3600,  # 1小时
    )
    return self

  @module()
  def recent_photo_retrieval(self):
    self.recent_item_retrieve_from_redis(    # 构造新表 获取 5 min photo 缓存
      no_check=True,
      item_table = target_photo_table,
      cluster_name = cache_cluster,
      key_prefix = recent_cache_key_prefix,
      cur_ts_min_column = "{{ts_min}}",
      new_key_column = "photo_id",
      new_embedding_column = "item_embedding",
      create_time_column = "create_time"
    ).count_reco_result( # 统计 item数
      item_table = target_photo_table,
      save_count_to = "recent_item_num_before"
    ).filter_by_attr(   # 创建时间过滤
      item_table = target_photo_table,
      attr_name="create_time",
      remove_if=">",
      compare_to= "{{photo_create_time}}",
      remove_if_attr_missing=True,
    ).count_reco_result(
      item_table = target_photo_table,
      save_count_to = "recent_item_num_after_ct_filter"
    ).copy_attr(
      item_table= target_photo_table,
      attrs=[{
        "from_common": "avg_emb_normal",
        "to_item": "avg_emb_normal"
      }]
    ).enrich_attr_by_lua(   # 计算内积 
      item_table = target_photo_table,
      import_item_attr = ["avg_emb_normal", "item_embedding"],
      no_check = True,
      function_for_item = "innerProductNew",
      export_item_attr = [ "score"],
      lua_script = """
        function innerProductNew()
          local len1 = #avg_emb_normal
          local len2 = #item_embedding
          local res = 0.0
          if len1 == 128 and len2 == 128 then
            for i = 1, 128 do
              res = res + avg_emb_normal[i] * item_embedding[i]
            end
            return res
          end
          return res
        end
      """
    ).count_reco_result( # 统计 item数
      item_table = target_photo_table,
      save_count_to = "redis_recall_num"
    ).perflog(
      mode = "interval",
      value = "{{redis_recall_num}}",
      namespace = ns,
      subtag = "redis_recall_num"
    ).perflog(
      mode = "interval",
      value = "{{recent_item_num_before}}",
      namespace = ns,
      subtag = "recent_item_num_before"
    ).perflog(
      mode = "interval",
      value = "{{recent_item_num_after_ct_filter}}",
      namespace = ns,
      subtag = "recent_item_num_after_ct_filter"
    )
    return self

  @module()
  def target_item_filter(self):
    self.get_kconf_params(   # 取召回截断数
      kconf_configs=[
        dict(kconf_key="ad.materialGenerationServer.dupPhotoBrandCommonConf",
           value_type="json",
           json_path="truncate_num",
           export_common_attr="truncate_num"
        )
      ]
    ).count_reco_result( # 统计 item数
      item_table = target_photo_table,
      save_count_to = "total_recall_num"
    ).perflog(
      mode = "interval",
      value = "{{total_recall_num}}",
      namespace = ns,
      subtag = "total_recall_num"
    ).batch_svalue_get_from_redis_enricher(   # 取 photo 创建时间
      item_table = target_photo_table,
      input_data_type = "int",
      output_data_type = "int",
      key_prefix = "fpdpc_",
      key_column = "photo_id",
      value_column = "create_time",
      redis_cluster_name = cache_cluster
    ).filter_by_attr(   # 将 创建时间晚于主 photo 的过滤掉
      item_table = target_photo_table,
      attr_name="create_time",
      remove_if=">",
      compare_to= "{{photo_create_time}}",
      remove_if_attr_missing=True,
    ).filter_by_attr(   # 将 id 等于 query photo id 的 item 过滤掉
      item_table = target_photo_table,
      attr_name="photo_id",
      remove_if="==",
      compare_to="{{photo_id}}",
      remove_if_attr_missing=True,
    ).copy_attr(  # 将photo_id_list copy到item_attr
      item_table = target_photo_table,
      attrs=[
        {"from_common": "photo_id_list",  "to_item": "photo_id_list"},
      ]
    ).enrich_attr_by_lua(   # 判断photo是否需要删除
      item_table = target_photo_table,
      import_item_attr = ["photo_id", "photo_id_list"],
      function_for_item = "checkIfDelete",
      export_item_attr = ["photo_invalid"],
      lua_script = """
        function checkIfDelete()
          for i, v in ipairs(photo_id_list) do
            if v == photo_id then
                return 1
            end
          end
          return 0
        end
      """
    ).filter_by_attr(   # 将 photo_invalid 的过滤掉
      item_table = target_photo_table,
      attr_name="photo_invalid",
      remove_if="==",
      compare_to= 1,
      remove_if_attr_missing=True,
    ).sort(
      item_table = target_photo_table,
      score_from_attr= "score"
    ).deduplicate(
      item_table = target_photo_table,
    ).pack_item_attr(   # photo_id 和 score 转 commonAttr
      item_table = target_photo_table,
      item_source = {
        "reco_results": True
      },
      mappings = [
        {
          "from_item_attr": "photo_id",
          "to_common_attr": "retrieval_photo_ids",
        },
        {
          "from_item_attr": "score",
          "to_common_attr": "retrieval_photo_scores",
        },
      ]
    ).truncate(
      item_table = target_photo_table,
      size_limit="{{truncate_num}}"
    )
    return self
  
  @module()
  def build_query_photo_table(self):
    self.copy_attr(  # 将其他字段拷贝到表中
      item_table = query_photo_table,
      attrs=[
        {"from_common": "photo_create_time",  "to_item": "photo_create_time"},
        {"from_common": "md5_seed_photo",  "to_item": "md5_seed_photo"},
        {"from_common": "md5_create_time",  "to_item": "md5_create_time"},
      ]
    )
    return self

  def get_photo_frame_key(self, table_name):
    self.photo_frame_key_copy_observer(   # 新redis中 无数据时 拷贝 photo 抽帧 key
      item_table = table_name,
      old_prefix = "pkf_",
      new_prefix = frame_key_prefix,
      key_column = "photo_id",
      old_cluster_name = "adAlgPhotoFrame",
      new_cluster_name = cache_cluster
    ).photo_frame_key_enricher(   # 取 photo 抽帧 key
      item_table = table_name,
      redis_cluster_name = cache_cluster,
      key_prefix = frame_key_prefix,
      photo_id_column = "photo_id",
      frame_keys_column = "frame_keys",
      frame_status_column = "frame_status"
    ).photo_frame_key_sample_enricher(  # blobkey 前密后疏采样
      item_table = table_name,
      frame_keys_column = "frame_keys",
      frame_status_column = "frame_status"
    )
    return self

  def query_photo_frame_get(self):
    self.get_photo_frame_key(query_photo_table) \
    .copy_attr(   # 把frameStatus copy到 commonAttr
      item_table = query_photo_table,
      attrs=[
        {
          "from_item": "frame_status",
          "to_common": "frame_status"
        }
      ]
    ).if_("frame_status == 0") \
      .photo_extract(query_photo_table) \
    .end_if_()
    return self

  def target_photo_frame_get(self):
    self.copy_attr(
      item_table = target_photo_table,
      attrs=[{
        "from_item": "frame_status", 
        "to_common": "frame_status"   
      }]
    ).if_("frame_status == 0") \
      .photo_extract(target_photo_table) \
    .end_if_() \
    .filter_invalid_target_photo()
    return self

  def filter_invalid_target_photo(self):
    self.enrich_attr_by_lua(   # 标记 photo 是否有抽帧 key
      item_table = target_photo_table,
      import_item_attr = ["frame_keys"],
      function_for_item = "checkTargetPhotoFrameValid",
      export_item_attr = ["frame_valid"],
      lua_script = """
        function checkTargetPhotoFrameValid()
          if #frame_keys == 0 then
            return 0
          else
            return 1
          end
        end
      """
    ).copy_attr(
      item_table = target_photo_table,
      no_check = True,
      attrs=[
        {
          "from_item": "frame_valid",
          "to_common": "frame_valid"
        }
      ]
    ).filter_by_attr(   # 无 frame_key 直接过滤
      item_table = target_photo_table,
      attr_name="frame_valid",
      remove_if="<=",
      compare_to= 0,
      remove_if_attr_missing=True,
    )
    return self

  def get_photo_rank_feats(self, table_name):
    self.blobstore_download_enricher(    # 请求 blobstore 获取抽帧信息
      item_table = table_name,
      bucket_name = "ad-material-storage",
      keys_column = "frame_keys",
      values_column = "frame_infos",
    ).image_resize_and_crop_enricher(   # 对每一帧按短边256 先压缩再 center crop
      item_table = table_name,
      images_column = "frame_infos",
      frame_height_column = "frame_height",
      frame_width_column = "frame_width",
      frame_size_column = "frame_size"
    ).photo_rank_feature_enricher(    # 获取 photo feature
      item_table = table_name,
      images_column = "frame_infos",
      frame_height_column = "frame_height",
      frame_width_column = "frame_width",
      feature_column = "feature"
    )
    return self

  def photo_sim_score_get(self):
    self.copy_attr(
      item_table = query_photo_table,
      attrs=[
        { "from_item": "feature", "to_common": "main_feature" },
        { "from_item": "frame_size", "to_common": "main_frame_size" },
      ]
    ).count_reco_result(
      item_table = target_photo_table,
      save_count_to = "target_item_after_rank"
    ).count_reco_result(
      item_table = query_photo_table,
      save_count_to = "query_item_after_rank"
    ).if_("target_item_after_rank == 0 or query_item_after_rank == 0") \
      .perflog(
        mode = "count",
        namespace = ns,
        subtag = "rank",
        extra1 = "target_or_query_item_empty"
      ).return_() \
     .end_if_() \
     .photo_rank_sim_info_enricher(
      item_table = target_photo_table,
      main_feature_column = "main_feature",
      main_frame_size_column = "main_frame_size",
      feature_column = "feature",
      frame_size_column = "frame_size",
      similar_info_column = "similar_info"
    ).photo_rank_sim_score_calc_enricher(
      item_table = target_photo_table,
      main_frame_size_column = "main_frame_size",
      frame_size_column = "frame_size",
      similar_info_column = "similar_info",
      similar_score_column = "similar_score"
    ).sort(
      item_table = target_photo_table,
      score_from_attr= "similar_score"
     )
    return self

  def photo_extract(self, table_name):
    self.photo_size_info_enricher(
      item_table = table_name,
      photo_id_column = "photo_id",
      frame_keys_column = "frame_keys",
      photo_width_column = "photo_width",
      photo_height_column = "photo_height",
      frame_status_column = "frame_status"  # 为false才执行
    ).get_kconf_params(   # 取抽帧超时时间
      no_check=True,
      kconf_configs=[
        dict(kconf_key="ad.materialGenerationServer.dupPhotoBrandCommonConf",
           value_type="json",
           json_path="frame_extract_timeout",
           export_common_attr="frame_extract_timeout"
        )
      ]
    ).photo_frame_extract_enricher(
      item_table = table_name,
      retry_time = 0, # 重试次数
      frame_extract_timeout_column = "frame_extract_timeout",
      photo_id_column = "photo_id",
      frame_keys_column = "frame_keys",
      photo_width_column = "photo_width",
      photo_height_column = "photo_height",
      frame_status_column = "frame_status",  # 为false才执行
      output_db_name = "ad",
      output_table_name = "material-storage",
      req_type="async_and_wait",
      redis_cluster_name="adDupPhotoInfoCache"
    ).photo_frame_key_record_observer(
      item_table = table_name,
      redis_cluster_name = cache_cluster,
      frame_keys_column = "frame_keys",
      key_prefix = frame_key_prefix,
      key_column = "photo_id",
      frame_status_column = "frame_status"
    ).photo_frame_key_sample_enricher(
      item_table = table_name,
      frame_keys_column = "frame_keys",
      frame_status_column = "frame_status"
    )
    return self

  @module()
  def result_build_and_send(self):
    self.brand_dup_result_build(
      no_check = True,
      item_table = target_photo_table,
      query_photo_table_name = query_photo_table,
      is_need_exit_column = "is_need_exit",
      photo_id_column = "photo_id",
      query_seed_photo_id_column = "md5_seed_photo",
      photo_create_time_column = "photo_create_time",
      query_frame_status_column = "frame_status",
      seed_photo_ctime_column = "md5_create_time",
      sim_photo_id_column = "sim_photo_id",
      sim_photo_score_column = "sim_photo_score",
      similar_info_message_column = "similar_info_message",
      dup_photo_id_column = "dup_photo_id",
      dup_photo_score_column = "dup_photo_id_score",
      rank_score_column = "similar_score",
      target_frame_status_column = "frame_status",
      create_time_column = "create_time",
    ).get_kconf_params(   # 取kconf配置数据
      kconf_configs=[
        dict(kconf_key="ad.materialGenerationServer.dupPhotoBrandCommonConf",
           value_type="json",
           json_path="time_out",
           export_common_attr="time_out"
        )
      ] 
    ).build_protobuf(  # 构造 kuaishou::negative::GetByPhotoIdsRequest
      class_name="kuaishou::negative::GetByPhotoIdsRequest",
      inputs=[
        { "common_attr": "photo_id", "path": "photo_id", "append": True },
        { "common_attr": "time_out", "path": "timeout"}
      ],
      output_common_attr= "photo_info_request",
    ).enrich_by_generic_grpc(		# 请求 photoService 服务
      kess_service="grpc_apiCorePhotoService",
      timeout_ms= 1000,
      method_name="/kuaishou.negative.PhotoServiceRpc/GetByIdsContainsDeleted",
      request_attr= "photo_info_request",
      response_attr= "photo_info_response",
      response_class="kuaishou::negative::PhotoMapResponse"
    ).enrich_with_protobuf(
      from_extra_var = "photo_info_response",
      no_check = True,
      attrs = [
        dict(name="photo_params", path="photos.value.pmm.ext_params")
      ]
    ).enrich_with_json(
        import_attr = "photo_params",
        attrs = [
          dict(name="photo_origin_height", path="h", output_type="int64"),
          dict(name="photo_origin_width", path="w", output_type="int64"),
        ],
        is_common_attr=True
    ).copy_attr(
      item_table = query_photo_table,
      attrs=[
        {
          "from_item": "dup_photo_id",
          "to_common": "dup_photo_id"
        },
        {
          "from_item": "sim_photo_id",
          "to_common": "sim_photo_id"
        },
        {
          "from_item": "sim_photo_score",
          "to_common": "sim_photo_score"
        },
        {
          "from_item": "dup_photo_id_score",
          "to_common": "dup_photo_id_score"
        }
      ]
    ).build_protobuf(
      item_table = target_photo_table,
      class_name="kuaishou::ad::algorithm::AdDupPhotoInfoOversea",
      inputs=[
        { "common_attr": "photo_id", "path": "photo_id" },
        { "common_attr": "creative_id", "path": "creative_id"},
        { "common_attr": "account_id", "path": "account_id"},
        { "common_attr": "create_time", "path": "create_time"},
        { "common_attr": "photo_create_time", "path": "photo_create_time"},
        { "common_attr": "md5_create_time", "path": "md5_create_time"},
        { "common_attr": "md5_seed_photo", "path": "md5_seed_photo"},
        { "common_attr": "is_seed_photo", "path": "is_seed_photo"},
        { "common_attr": "is_new_md5_generate", "path": "is_new_md5_generate"},
        { "common_attr": "is_first_attach", "path": "is_new_emb_generate"},
        { "item_attr": "photo_id", "path": "rankphoto_ids", "append": True},
        { "item_attr": "similar_score", "path": "rank_scores", "append": True},
        { "common_attr": "dup_photo_id", "path": "dup_photo_id"},
        { "common_attr": "sim_photo_score", "path": "max_similar_score"},
        { "common_attr": "sim_photo_id", "path": "similar_photo_id"},
        { "common_attr": "redis_photo_md5", "path": "photo_md5_str"},
        { "common_attr": "photo_origin_height", "path": "photo_origin_height"},
        { "common_attr": "photo_origin_width", "path": "photo_origin_width"},
      ],
      output_common_attr= "ad_dup_photo_result_brand",
    ).build_protobuf(
      item_table = target_photo_table,
      class_name="kuaishou::ad::algorithm::AdDupPhotoInfoOversea",
      inputs=[
        { "common_attr": "photo_id", "path": "photo_id" },
        { "common_attr": "creative_id", "path": "creative_id"},
        { "common_attr": "account_id", "path": "account_id"},
        { "common_attr": "create_time", "path": "create_time"},
        { "common_attr": "photo_create_time", "path": "photo_create_time"},
        { "common_attr": "md5_create_time", "path": "md5_create_time"},
        { "common_attr": "md5_seed_photo", "path": "md5_seed_photo"},
        { "common_attr": "is_seed_photo", "path": "is_seed_photo"},
        { "common_attr": "is_new_md5_generate", "path": "is_new_md5_generate"},
        { "common_attr": "is_first_attach", "path": "is_new_emb_generate"},
        { "common_attr": "retrieval_photo_ids", "path": "retrieval_photo_ids", "append": True},
        { "common_attr": "retrieval_photo_scores", "path": "retrieval_scores", "append": True},
        { "item_attr": "photo_id", "path": "rankphoto_ids", "append": True},
        { "item_attr": "similar_score", "path": "rank_scores", "append": True},
        { "common_attr": "dup_photo_id", "path": "dup_photo_id"},
        { "common_attr": "sim_photo_score", "path": "max_similar_score"},
        { "common_attr": "sim_photo_id", "path": "similar_photo_id"},
        { "common_attr": "redis_photo_md5", "path": "photo_md5_str"},
        { "common_attr": "photo_origin_height", "path": "photo_origin_height"},
        { "common_attr": "photo_origin_width", "path": "photo_origin_width"},
      ],
      as_string = True,
      output_common_attr= "ad_dup_photo_result_brand_str",
    ).send_to_kafka(
      is_common_attr = True,
      message_column = "ad_dup_photo_result_brand_str",
      topic = "ad_dup_photo_result_brand"
    ).build_protobuf(
      item_table = target_photo_table,
      class_name="kuaishou::ad::brand::creative::CreativeEmbedInfo",
      inputs=[
        { "common_attr": "creative_id", "path": "creative_id" },
        { "common_attr": "photo_id", "path": "photo_id" },
        { "common_attr": "dup_photo_id", "path": "sim_photo_id"},
      ],
      output_common_attr= "creative_embed_info",
    ).build_protobuf(
      item_table = target_photo_table,
      class_name="kuaishou::ad::brand::creative::CreativeRecallEmbedMessage",
      inputs=[
        { "common_attr": "creative_embed_info", "path": "creative_embed_info", "append": True},
      ],
      as_string = True,
      output_common_attr= "recall_message",
    ).send_to_kafka(
      is_common_attr = True,
      message_column = "recall_message",
      topic = "ad_brand_recall_photo_response"
    )
    return self

  @module()
  def ann_photo_retrieval(self):
    self.get_kconf_params(   # 取kconf配置数据
      kconf_configs=[
        dict(kconf_key="ad.materialGenerationServer.dupPhotoBrandCommonConf",
           value_type="json",
           json_path="recall_num",
           export_common_attr="recall_num"	# 召回数
        ),
        dict(kconf_key="ad.materialGenerationServer.dupPhotoBrandCommonConf",
           value_type="json",
           json_path="time_out",
           export_common_attr="time_out"
        ),
        dict(kconf_key="ad.materialGenerationServer.dupPhotoBrandCommonConf",
           value_type="json",
           json_path="truncate_num",
           export_common_attr="truncate_num"
        ),
      ] 
    ).set_attr_value(   # 设置 index_name
			common_attrs=[
        {"name": "index_name", "type": "string", "value": "ann_dup_photo_brand"}
      ]
		).build_protobuf(  # 构造 AnnRequest.EmbeddingRequest.QueryVec
      class_name="kuaishou::ad::RttrEmbedding",
      inputs=[
        { "common_attr": "avg_emb_normal", "path": "value", "append": True }
      ],
      output_common_attr= "embedding",
    ).build_protobuf(  # 构造 AnnRequest.EmbeddingRequest 注意 这里没有填 index_name 后续可能会并行请求多个Ann服务 并对结果进行merge
      item_table = "new_clip_info_table",
      class_name="kuaishou::ad::EmbeddingRequest",
      inputs=[
        { "common_attr": "recall_num", "path": "search_num" },
        { "common_attr": "embedding", "path": "query_vec", "append": True },
        { "common_attr": "index_name", "path": "index_name"},
      ],
      output_common_attr= "embedding_request",
    ).build_protobuf(   # 构造 AdUserInfo
      class_name="kuaishou::ad::AdUserInfo",
      inputs=[
				{ "common_attr": "photo_id", "path": "id" },
      ],
      output_common_attr="user_info",
    ).build_protobuf(   # 构造 AnnRequest
      class_name="kuaishou::ad::AnnRequest",
      inputs=[
				{ "common_attr": "user_info", "path": "user_info" },
				{ "common_attr": "embedding_request", "path": "embedding_request", "append": True }
      ],
      output_common_attr="ann_request",
    ).enrich_by_generic_grpc(		# 请求 ann 服务
      kess_service= "grpc_ann_dup_photo_brand_embedding_retr",
      timeout_ms= "{{time_out}}",
      method_name="/kuaishou.ad.AdEmbeddingRetrGrpcService/Search",
      request_attr= "ann_request",
      response_attr= "ann_response",
      response_class="kuaishou.ad.AnnResponse"
    ).enrich_with_protobuf(
      from_extra_var = "ann_response",
      attrs = [
        dict(name="distance_list", path="embedding_result.result_item.distance"),
        dict(name="photo_id_list", path="embedding_result.result_item.embedding_item_id"),
      ]
    ).build_table_from_common_list_attr(
      no_check = True,
      new_table = target_photo_table,
      build_config = [  # 第一行默认为主键
        dict(src = "photo_id_list", dest = "photo_id"),
        dict(src = "distance_list", dest = "score")
      ]
    ).enrich_attr_by_lua(   # 从欧式距离转为余弦相似度
      item_table = target_photo_table,
      import_item_attr = ["score"],
      function_for_item = "convertToCosineSimilarity",
      export_item_attr = ["score"],
      lua_script = """
        function convertToCosineSimilarity()
          return 1 - score / 2
        end
      """
    ).count_reco_result( # 统计item数
      item_table = target_photo_table,
      save_count_to = "ann_recall_num"
    ).perflog(
      mode = "interval",
      value = "{{ann_recall_num}}",
      namespace = ns,
      subtag = "ann_recall_num",
    )
    return self

  @module()
  def core_process(self):
    self.mix_by_sub_flow(
      no_check=True,
      sub_flow= recent_photo_retrieval_flow,
      pass_common_attrs =["photo_create_time", "photo_id", "avg_emb_normal", "ts_min"],
      retrieve_tables= [
        {
          "table_name" : target_photo_table,
          "attrs" : ["photo_id", "score"]
        }
      ],
      deep_copy=True
    ).mix_by_sub_flow(
      no_check=True,
      sub_flow= ann_photo_retrieval_flow,
      pass_common_attrs =["photo_create_time", "photo_id", "avg_emb_normal"],
      retrieve_tables= [
        {
          "table_name" : target_photo_table,
          "attrs" : ["photo_id", "score"]
        }
      ],
      deep_copy=True,
    ).target_item_filter() \
    .build_query_photo_table() \
    .enrich_by_sub_flow(
      no_check=True,
      sub_flow = query_photo_feature_get_flow,
      item_table = query_photo_table,
      pass_item_attrs= ["photo_id"],
      merge_item_attrs= ["feature", "frame_keys", "frame_status", "frame_size", "photo_width", "photo_height", "frame_height", "frame_width"],
      deep_copy=True
    ).get_photo_frame_key(target_photo_table) \
    .arrange_by_sub_flow(
      item_table = target_photo_table,
      sub_flow = target_photo_frame_get_flow,
      expected_partition_size=1,
      pass_common_attrs = [""],
      pass_item_attrs = ["photo_id", "photo_blobstore_key", "frame_status"],
      merge_item_attrs = ["feature", "frame_keys", "frame_status", "frame_size", "frame_height", "frame_width"],
      no_check = True
    ).photo_sim_score_get() \
    .result_build_and_send() \
    .write_dup_info_cache()
    return self

  @module()
  def photo_dup_cache_check(self):
    self.copy_attr(
      item_table = query_photo_table,
      attrs=[
        {
          "from_item": "create_time",
          "to_common": "create_time"
        },
        {
          "from_item": "photo_id",
          "to_common": "photo_id"
        },
        {
          "from_item": "photo_id_str",
          "to_common": "photo_id_str"
        },
        {
          "from_item": "creative_id",
          "to_common": "creative_id"
        },
      ]
    ).set_attr_value(
			common_attrs=[
        {"name": "dup_score_cache_prefix", "type": "string", "value": dup_score_cache_prefix},
        {"name": "dup_id_cache_prefix", "type": "string", "value": dup_id_cache_prefix},
        {"name": "dup_photo_id_from_cache", "type": "int", "value": 0},
        {"name": "dup_photo_score_from_cache", "type": "double", "value": 0.0},
      ]
    ).enrich_attr_by_lua(   # 构造获取 dup_id dup_score缓存的 key
      import_common_attr = ["photo_id", "dup_score_cache_prefix", "dup_id_cache_prefix"],
      function_for_common = "genDupCacheKey",
      export_common_attr = ["dup_photo_id_cache_key", "dup_photo_score_cache_key"],
      lua_script = """
        function genDupCacheKey()
          return dup_id_cache_prefix..tostring(photo_id), dup_score_cache_prefix..tostring(photo_id)
        end
      """
    ).get_common_attr_from_redis(   # 获取创建时间和md5
      cluster_name = cache_cluster,
      redis_params = [
        { "redis_key": "{{dup_photo_id_cache_key}}", "redis_value_type": "string", "output_attr_name": "dup_photo_id_from_cache", "output_attr_type": "int"},
        { "redis_key": "{{dup_photo_score_cache_key}}", "redis_value_type": "string", "output_attr_name": "dup_photo_score_from_cache", "output_attr_type": "double"}
      ]
    ).if_("dup_photo_id_from_cache > 0 and dup_photo_score_from_cache > 1e-9") \
      .perflog( # 获取到 dup 缓存
          mode = "count",
          namespace = ns,
          subtag = "cache_check",
          extra1 = "get_dup_info_by_photo_id"
      ).build_protobuf(
        class_name="kuaishou::ad::algorithm::AdDupPhotoInfoOversea",
        inputs=[
          { "common_attr": "photo_id", "path": "photo_id" },
          { "common_attr": "creative_id", "path": "creative_id"},
          { "common_attr": "account_id", "path": "account_id"},
          { "common_attr": "dup_photo_id_from_cache", "path": "dup_photo_id"},
        ],
        as_string = True,
        output_common_attr= "ad_dup_photo_cache_info_str",
      ).send_to_kafka(
        is_common_attr = True,
        message_column = "ad_dup_photo_cache_info_str",
        topic = "ad_dup_photo_result_brand"
      ).build_protobuf(
        class_name="kuaishou::ad::brand::creative::CreativeEmbedInfo",
        inputs=[
          { "common_attr": "creative_id", "path": "creative_id" },
          { "common_attr": "photo_id", "path": "photo_id" },
          { "common_attr": "dup_photo_id_from_cache", "path": "sim_photo_id"},
        ],
        output_common_attr= "creative_embed_info",
      ).build_protobuf(
        item_table = target_photo_table,
        class_name="kuaishou::ad::brand::creative::CreativeRecallEmbedMessage",
        inputs=[
          { "common_attr": "creative_embed_info", "path": "creative_embed_info", "append": True},
        ],
        as_string = True,
        output_common_attr= "recall_message",
      ).send_to_kafka(
        is_common_attr = True,
        message_column = "recall_message",
        topic = "ad_brand_recall_photo_response"
      ).debug_log(
        respect_sample_logging = False,
        common_attrs = ["photo_id", "dup_photo_id_cache_key", "dup_photo_score_cache_key", "dup_photo_id_from_cache", "dup_photo_score_from_cache", "creative_embed_info"]
      ).return_() \
    .end_()
    return self

  @module()
  def write_dup_info_cache(self):
    self.set_attr_value(
			common_attrs=[
        {"name": "dup_score_cache_prefix", "type": "string", "value": dup_score_cache_prefix},
        {"name": "dup_id_cache_prefix", "type": "string", "value": dup_id_cache_prefix},
      ]
    ).enrich_attr_by_lua(   # 构造 dup_id dup_score缓存 key
      import_common_attr = ["photo_id", "dup_score_cache_prefix", "dup_id_cache_prefix"],
      function_for_common = "genDupCacheKey",
      export_common_attr = ["dup_photo_id_cache_key", "dup_photo_score_cache_key"],
      lua_script = """
        function genDupCacheKey()
          return dup_id_cache_prefix..tostring(photo_id), dup_score_cache_prefix..tostring(photo_id)
        end
      """
    ).write_to_redis(   # photo->dupPhotoId 写入redis
      kcc_cluster=cache_cluster,
      timeout_ms=10,
      key="{{dup_photo_id_cache_key}}",
      value="{{dup_photo_id}}"
    ).write_to_redis(   # photo->dupPhotoScore 写入redis
      kcc_cluster=cache_cluster,
      timeout_ms=10,
      key="{{dup_photo_score_cache_key}}",
      value="{{dup_photo_id_score}}"
    ).debug_log(
      respect_sample_logging = False,
      common_attrs = ["photo_id", "dup_photo_id_cache_key", "dup_photo_score_cache_key", "dup_photo_id", "dup_photo_id_score"]
    )
    return self
  
  @module()
  def seed_photo_dup_cache_check(self):
    self.set_attr_value(
			common_attrs=[
        {"name": "dup_score_cache_prefix", "type": "string", "value": dup_score_cache_prefix},
        {"name": "dup_id_cache_prefix", "type": "string", "value": dup_id_cache_prefix},
        {"name": "dup_seed_photo_id_from_cache", "type": "int", "value": 0},
        {"name": "dup_seed_photo_score_from_cache", "type": "double", "value": 0.0},
      ]
    ).enrich_attr_by_lua(   # 构造获取 dup_id dup_score缓存的 key
      import_common_attr = ["md5_seed_photo", "dup_score_cache_prefix", "dup_id_cache_prefix"],
      function_for_common = "genDupCacheKey",
      export_common_attr = ["dup_seed_photo_id_cache_key", "dup_seed_photo_score_cache_key"],
      lua_script = """
        function genDupCacheKey()
          return dup_id_cache_prefix..tostring(md5_seed_photo), dup_score_cache_prefix..tostring(md5_seed_photo)
        end
      """
    ).get_common_attr_from_redis(   # 获取创建时间和md5
      cluster_name = cache_cluster,
      redis_params = [
        { "redis_key": "{{dup_seed_photo_id_cache_key}}", "redis_value_type": "string", "output_attr_name": "dup_seed_photo_id_from_cache", "output_attr_type": "int"},
        { "redis_key": "{{dup_seed_photo_score_cache_key}}", "redis_value_type": "string", "output_attr_name": "dup_seed_photo_score_from_cache", "output_attr_type": "double"}
      ]
    ).if_("dup_seed_photo_id_from_cache > 0 and dup_seed_photo_score_from_cache > 1e-9") \
      .perflog( # 获取到 dup 缓存
          mode = "count",
          namespace = ns,
          subtag = "cache_check",
          extra1 = "get_dup_info_by_seed_photo_id"
      ).build_protobuf(
      class_name="kuaishou::ad::algorithm::AdDupPhotoInfoOversea",
      inputs=[
        { "common_attr": "photo_id", "path": "photo_id" },
        { "common_attr": "creative_id", "path": "creative_id"},
        { "common_attr": "account_id", "path": "account_id"},
        { "common_attr": "ad_dup_info_from_seed_photo_cache", "path": "dup_photo_id"},
      ],
      as_string = True,
      output_common_attr= "ad_dup_seed_photo_cache_info_str",
    ).send_to_kafka(
      is_common_attr = True,
      message_column = "ad_dup_seed_photo_cache_info_str",
      topic = "ad_dup_photo_result_brand"
    ).build_protobuf(
        class_name="kuaishou::ad::brand::creative::CreativeEmbedInfo",
        inputs=[
          { "common_attr": "creative_id", "path": "creative_id" },
          { "common_attr": "photo_id", "path": "photo_id" },
          { "common_attr": "dup_seed_photo_id_from_cache", "path": "sim_photo_id"},
        ],
        output_common_attr= "creative_embed_info",
      ).build_protobuf(
        item_table = target_photo_table,
        class_name="kuaishou::ad::brand::creative::CreativeRecallEmbedMessage",
        inputs=[
          { "common_attr": "creative_embed_info", "path": "creative_embed_info", "append": True},
        ],
        as_string = True,
        output_common_attr= "recall_message",
      ).send_to_kafka(
        is_common_attr = True,
        message_column = "recall_message",
        topic = "ad_brand_recall_photo_response"
      ).debug_log(
        respect_sample_logging = False,
        common_attrs = ["photo_id", "dup_seed_photo_id_cache_key", "dup_seed_photo_score_cache_key", "dup_seed_photo_id_from_cache", "dup_photo_score_from_cache", "recall_message"]
      ).return_() \
    .end_()
    return self

#===================================================== FLOW定义 =======================================================
# flow 定义
batch_data_prepare_flow = AdDupPhotoDetectServerBrand("batch_data_prepare")
inc_data_prepare_flow = AdDupPhotoDetectServerBrand("inc_data_prepare")
recent_photo_retrieval_flow = AdDupPhotoDetectServerBrand("recent_photo_retrieval")
ann_photo_retrieval_flow = AdDupPhotoDetectServerBrand("ann_photo_retrieval")
query_photo_feature_get_flow = AdDupPhotoDetectServerBrand("query_photo_feature_get", item_table = query_photo_table)
target_photo_frame_get_flow = AdDupPhotoDetectServerBrand("target_photo_frame_get", item_table = target_photo_table)
core_process_flow = AdDupPhotoDetectServerBrand("core_process_flow")
dispatch_flow = AdDupPhotoDetectServerBrand("dispatch_flow", item_table = query_photo_table)

with recent_photo_retrieval_flow, data_manager:
  module(module_name="recent_photo_retrieval")(function=AdDupPhotoDetectServerBrand.recent_photo_retrieval)(recent_photo_retrieval_flow)
with query_photo_feature_get_flow, data_manager:
  query_photo_feature_get_flow.query_photo_frame_get().get_photo_rank_feats(query_photo_table)
with target_photo_frame_get_flow, data_manager:
  target_photo_frame_get_flow.target_photo_frame_get().get_photo_rank_feats(target_photo_table)
with batch_data_prepare_flow, data_manager:
  batch_data_prepare_flow.batch_photo_message_collection()
with inc_data_prepare_flow, data_manager:
  inc_data_prepare_flow.inc_photo_message_collection()
with dispatch_flow, data_manager:
  dispatch_flow.dispatch()
with ann_photo_retrieval_flow, data_manager:
  module(module_name="ann_photo_retrieval")(function=AdDupPhotoDetectServerBrand.ann_photo_retrieval)(ann_photo_retrieval_flow)
with core_process_flow, data_manager:
  core_process_flow.ad_message_queue_retrieve(   # 从异步队列取数据 应该包含 photo表 (photoAttr列) 
              max_queue_size= 1,
              queue_number = 7,
              message_queue_type=0
            ).photo_dup_cache_check() \
            .cache_info_process() \
            .seed_photo_dup_cache_check() \
            .get_embedding_from_rpc() \
            .build_embedding_inc() \
            .recent_photo_cache() \
            .core_process() \
            .debug_log(
              respect_sample_logging = False,
              common_attrs = ["frame_status", "self_inner_product", "duration", "is_emb_valid", "photo_id",
                              "create_time", "photo_id_str", "avg_emb_normal",
                              "is_new_md5_generate", "redis_photo_md5", "photo_create_time",
                              "md5_seed_photo", "md5_create_time", "redis_emb_cache_key",
                              "photo_embedding_str", "retrieval_photo_ids", "retrieval_photo_scores", "ad_dup_photo_result_brand", "recall_message", "photo_params", "photo_info_response"]
              ).debug_log(
                respect_sample_logging = False,
                item_table = query_photo_table,
                item_attrs = ["photo_id", "photo_create_time","md5_seed_photo", "md5_create_time", "frame_keys", "photo_height", "photo_width", "frame_height", "frame_width"]
              ).debug_log(
                respect_sample_logging = False,
                item_table = target_photo_table,
                common_attrs = ["photo_id"],
                item_attrs = ["photo_id", "create_time", "score", "frame_keys", "frame_height", "frame_width", "similar_score"]
              )

#===================================================== Runner定义 =======================================================
# runner 定义
ad_dup_photo_detect_server_breand_runner = OfflineRunner("ad-dup-photo-detect-server-brand")
# ad_dup_photo_detect_server_breand_runner.add_leaf_flows(leaf_flows=[batch_data_prepare_flow], name="batch_data_prepare_flow", thread_num=1)
ad_dup_photo_detect_server_breand_runner.add_leaf_flows(leaf_flows=[inc_data_prepare_flow], name="inc_data_prepare_flow", thread_num=1)
ad_dup_photo_detect_server_breand_runner.add_leaf_flows(leaf_flows=[core_process_flow], name="core_process_flow", thread_num=7)
ad_dup_photo_detect_server_breand_runner.draw()
current_dir = os.path.dirname(__file__)
ad_dup_photo_detect_server_breand_runner.build(output_file=os.path.join(current_dir,
                                        "pub/ad_dup_photo_detect_server_brand/config/dynamic_json_config.json"),
                                        extra_fields=config)