<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.30.1 (20180420.1509)
 -->
<!-- Title: DAG&#45;ad&#45;dup&#45;photo&#45;detect&#45;server&#45;brand&#45;batch_data_prepare_flow Pages: 1 -->
<svg width="574pt" height="1728pt"
 viewBox="0.00 0.00 574.00 1728.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1724)">
<title>DAG&#45;ad&#45;dup&#45;photo&#45;detect&#45;server&#45;brand&#45;batch_data_prepare_flow</title>
<polygon fill="white" stroke="white" points="-4,5 -4,-1724 571,-1724 571,5 -4,5"/>
<text text-anchor="middle" x="283" y="-100" font-family="Times,serif" font-size="20.00">DAG drawn by Dragonfly v0.8.0</text>
<text text-anchor="middle" x="283" y="-78" font-family="Times,serif" font-size="20.00">Service: ad&#45;dup&#45;photo&#45;detect&#45;server&#45;brand</text>
<text text-anchor="middle" x="283" y="-56" font-family="Times,serif" font-size="20.00">RequestType: batch_data_prepare_flow</text>
<text text-anchor="middle" x="283" y="-34" font-family="Times,serif" font-size="20.00">Date: 2024&#45;05&#45;23 19:34:38</text>
<g id="clust1" class="cluster"><title>cluster_0</title>
<polygon fill="none" stroke="grey" points="8,-216 8,-1654 558,-1654 558,-216 8,-216"/>
<text text-anchor="middle" x="94" y="-1634" font-family="Times,serif" font-size="20.00">batch_data_prepare</text>
</g>
<!-- START -->
<g id="node1" class="node"><title>START</title>
<polygon fill="lightgray" stroke="black" points="312,-1720 254,-1720 254,-1662 312,-1662 312,-1720"/>
<polyline fill="none" stroke="black" points="266,-1720 254,-1708 "/>
<polyline fill="none" stroke="black" points="254,-1674 266,-1662 "/>
<polyline fill="none" stroke="black" points="300,-1662 312,-1674 "/>
<polyline fill="none" stroke="black" points="312,-1708 300,-1720 "/>
<text text-anchor="middle" x="283" y="-1687.3" font-family="Times,serif" font-size="14.00">START</text>
</g>
<!-- flow_start&#45;batch_data_prepare_0 -->
<g id="node3" class="node"><title>flow_start&#45;batch_data_prepare_0</title>
<ellipse fill="grey" stroke="grey" cx="283" cy="-1610" rx="5.76" ry="5.76"/>
</g>
<!-- START&#45;&gt;flow_start&#45;batch_data_prepare_0 -->
<g id="edge1" class="edge"><title>START&#45;&gt;flow_start&#45;batch_data_prepare_0</title>
<path fill="none" stroke="black" d="M283,-1661.93C283,-1649.82 283,-1636.14 283,-1626.02"/>
<polygon fill="black" stroke="black" points="286.5,-1625.76 283,-1615.76 279.5,-1625.76 286.5,-1625.76"/>
</g>
<!-- END -->
<g id="node2" class="node"><title>END</title>
<polygon fill="lightgray" stroke="black" points="305,-188 261,-188 261,-144 305,-144 305,-188"/>
<polyline fill="none" stroke="black" points="273,-188 261,-176 "/>
<polyline fill="none" stroke="black" points="261,-156 273,-144 "/>
<polyline fill="none" stroke="black" points="293,-144 305,-156 "/>
<polyline fill="none" stroke="black" points="305,-176 293,-188 "/>
<text text-anchor="middle" x="283" y="-162.3" font-family="Times,serif" font-size="14.00">END</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;0 -->
<g id="node5" class="node"><title>proc&#45;batch_data_prepare_0&#45;0</title>
<polygon fill="white" stroke="black" points="431.25,-1568 134.75,-1568 134.75,-1532 431.25,-1532 431.25,-1568"/>
<text text-anchor="middle" x="283" y="-1546.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::calc_time_cost_s</text>
</g>
<!-- flow_start&#45;batch_data_prepare_0&#45;&gt;proc&#45;batch_data_prepare_0&#45;0 -->
<g id="edge2" class="edge"><title>flow_start&#45;batch_data_prepare_0&#45;&gt;proc&#45;batch_data_prepare_0&#45;0</title>
<path fill="none" stroke="black" d="M283,-1604.05C283,-1598.2 283,-1587.99 283,-1578.07"/>
<polygon fill="black" stroke="black" points="286.5,-1578.05 283,-1568.05 279.5,-1578.05 286.5,-1578.05"/>
</g>
<!-- flow_end&#45;batch_data_prepare_0 -->
<g id="node4" class="node"><title>flow_end&#45;batch_data_prepare_0</title>
<ellipse fill="grey" stroke="grey" cx="283" cy="-230" rx="5.76" ry="5.76"/>
</g>
<!-- flow_end&#45;batch_data_prepare_0&#45;&gt;END -->
<g id="edge21" class="edge"><title>flow_end&#45;batch_data_prepare_0&#45;&gt;END</title>
<path fill="none" stroke="black" d="M283,-224.135C283,-218.414 283,-208.42 283,-198.373"/>
<polygon fill="black" stroke="black" points="286.5,-198.061 283,-188.061 279.5,-198.061 286.5,-198.061"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;1 -->
<g id="node6" class="node"><title>proc&#45;batch_data_prepare_0&#45;1</title>
<polygon fill="white" stroke="black" points="547,-1496 19,-1496 19,-1460 547,-1460 547,-1496"/>
<text text-anchor="middle" x="283" y="-1474.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::ad_material_fetch_message_from_kafka_enricher_D1338A</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;0&#45;&gt;proc&#45;batch_data_prepare_0&#45;1 -->
<g id="edge3" class="edge"><title>proc&#45;batch_data_prepare_0&#45;0&#45;&gt;proc&#45;batch_data_prepare_0&#45;1</title>
<path fill="none" stroke="black" d="M283,-1531.7C283,-1523.98 283,-1514.71 283,-1506.11"/>
<polygon fill="black" stroke="black" points="286.5,-1506.1 283,-1496.1 279.5,-1506.1 286.5,-1506.1"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;2 -->
<g id="node7" class="node"><title>proc&#45;batch_data_prepare_0&#45;2</title>
<polygon fill="white" stroke="black" points="461,-1424 105,-1424 105,-1388 461,-1388 461,-1424"/>
<text text-anchor="middle" x="283" y="-1402.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::enrich_attr_by_lua_B9B984</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;1&#45;&gt;proc&#45;batch_data_prepare_0&#45;2 -->
<g id="edge4" class="edge"><title>proc&#45;batch_data_prepare_0&#45;1&#45;&gt;proc&#45;batch_data_prepare_0&#45;2</title>
<path fill="none" stroke="black" d="M283,-1459.7C283,-1451.98 283,-1442.71 283,-1434.11"/>
<polygon fill="black" stroke="black" points="286.5,-1434.1 283,-1424.1 279.5,-1434.1 286.5,-1434.1"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;3 -->
<g id="node8" class="node"><title>proc&#45;batch_data_prepare_0&#45;3</title>
<ellipse fill="lightgrey" stroke="black" cx="283" cy="-1325" rx="260.323" ry="26.7407"/>
<text text-anchor="middle" x="283" y="-1328.8" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::_branch_controller_70E21193</text>
<text text-anchor="middle" x="283" y="-1313.8" font-family="Times,serif" font-size="14.00">(photo_id == 0 or create_time == 0)</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;2&#45;&gt;proc&#45;batch_data_prepare_0&#45;3 -->
<g id="edge5" class="edge"><title>proc&#45;batch_data_prepare_0&#45;2&#45;&gt;proc&#45;batch_data_prepare_0&#45;3</title>
<path fill="none" stroke="black" d="M283,-1387.86C283,-1380.36 283,-1371.25 283,-1362.36"/>
<polygon fill="black" stroke="black" points="286.5,-1362.13 283,-1352.13 279.5,-1362.13 286.5,-1362.13"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;4 -->
<g id="node9" class="node"><title>proc&#45;batch_data_prepare_0&#45;4</title>
<polygon fill="white" stroke="black" points="439,-1262 127,-1262 127,-1226 439,-1226 439,-1262"/>
<text text-anchor="middle" x="283" y="-1240.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::perflog_8A60B2CB</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;3&#45;&gt;proc&#45;batch_data_prepare_0&#45;4 -->
<g id="edge6" class="edge"><title>proc&#45;batch_data_prepare_0&#45;3&#45;&gt;proc&#45;batch_data_prepare_0&#45;4</title>
<path fill="none" stroke="black" d="M283,-1297.69C283,-1289.58 283,-1280.63 283,-1272.44"/>
<polygon fill="black" stroke="black" points="286.5,-1272.25 283,-1262.25 279.5,-1272.25 286.5,-1272.25"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;5 -->
<g id="node10" class="node"><title>proc&#45;batch_data_prepare_0&#45;5</title>
<polygon fill="white" stroke="black" points="429,-1190 137,-1190 137,-1154 429,-1154 429,-1190"/>
<text text-anchor="middle" x="283" y="-1168.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::return__16C414</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;4&#45;&gt;proc&#45;batch_data_prepare_0&#45;5 -->
<g id="edge7" class="edge"><title>proc&#45;batch_data_prepare_0&#45;4&#45;&gt;proc&#45;batch_data_prepare_0&#45;5</title>
<path fill="none" stroke="black" d="M283,-1225.7C283,-1217.98 283,-1208.71 283,-1200.11"/>
<polygon fill="black" stroke="black" points="286.5,-1200.1 283,-1190.1 279.5,-1200.1 286.5,-1200.1"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;6 -->
<g id="node11" class="node"><title>proc&#45;batch_data_prepare_0&#45;6</title>
<polygon fill="white" stroke="black" points="460,-1118 106,-1118 106,-1082 460,-1082 460,-1118"/>
<text text-anchor="middle" x="283" y="-1096.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::enrich_attr_by_lua_B28966</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;5&#45;&gt;proc&#45;batch_data_prepare_0&#45;6 -->
<g id="edge8" class="edge"><title>proc&#45;batch_data_prepare_0&#45;5&#45;&gt;proc&#45;batch_data_prepare_0&#45;6</title>
<path fill="none" stroke="black" d="M283,-1153.7C283,-1145.98 283,-1136.71 283,-1128.11"/>
<polygon fill="black" stroke="black" points="286.5,-1128.1 283,-1118.1 279.5,-1128.1 286.5,-1128.1"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;7 -->
<g id="node12" class="node"><title>proc&#45;batch_data_prepare_0&#45;7</title>
<ellipse fill="lightgrey" stroke="black" cx="283" cy="-1019" rx="266.369" ry="26.7407"/>
<text text-anchor="middle" x="283" y="-1022.8" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::_branch_controller_F77CBCCF</text>
<text text-anchor="middle" x="283" y="-1007.8" font-family="Times,serif" font-size="14.00">(duration &gt; 547)</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;6&#45;&gt;proc&#45;batch_data_prepare_0&#45;7 -->
<g id="edge9" class="edge"><title>proc&#45;batch_data_prepare_0&#45;6&#45;&gt;proc&#45;batch_data_prepare_0&#45;7</title>
<path fill="none" stroke="black" d="M283,-1081.86C283,-1074.36 283,-1065.25 283,-1056.36"/>
<polygon fill="black" stroke="black" points="286.5,-1056.13 283,-1046.13 279.5,-1056.13 286.5,-1056.13"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;8 -->
<g id="node13" class="node"><title>proc&#45;batch_data_prepare_0&#45;8</title>
<polygon fill="white" stroke="black" points="438,-956 128,-956 128,-920 438,-920 438,-956"/>
<text text-anchor="middle" x="283" y="-934.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::perflog_B970C91A</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;7&#45;&gt;proc&#45;batch_data_prepare_0&#45;8 -->
<g id="edge10" class="edge"><title>proc&#45;batch_data_prepare_0&#45;7&#45;&gt;proc&#45;batch_data_prepare_0&#45;8</title>
<path fill="none" stroke="black" d="M283,-991.694C283,-983.58 283,-974.626 283,-966.438"/>
<polygon fill="black" stroke="black" points="286.5,-966.248 283,-956.248 279.5,-966.248 286.5,-966.248"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;9 -->
<g id="node14" class="node"><title>proc&#45;batch_data_prepare_0&#45;9</title>
<polygon fill="white" stroke="black" points="430.25,-884 135.75,-884 135.75,-848 430.25,-848 430.25,-884"/>
<text text-anchor="middle" x="283" y="-862.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::return__04FED1</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;8&#45;&gt;proc&#45;batch_data_prepare_0&#45;9 -->
<g id="edge11" class="edge"><title>proc&#45;batch_data_prepare_0&#45;8&#45;&gt;proc&#45;batch_data_prepare_0&#45;9</title>
<path fill="none" stroke="black" d="M283,-919.697C283,-911.983 283,-902.712 283,-894.112"/>
<polygon fill="black" stroke="black" points="286.5,-894.104 283,-884.104 279.5,-894.104 286.5,-894.104"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;10 -->
<g id="node15" class="node"><title>proc&#45;batch_data_prepare_0&#45;10</title>
<polygon fill="white" stroke="black" points="449,-812 117,-812 117,-776 449,-776 449,-812"/>
<text text-anchor="middle" x="283" y="-790.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::set_attr_value_BA04E3</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;9&#45;&gt;proc&#45;batch_data_prepare_0&#45;10 -->
<g id="edge12" class="edge"><title>proc&#45;batch_data_prepare_0&#45;9&#45;&gt;proc&#45;batch_data_prepare_0&#45;10</title>
<path fill="none" stroke="black" d="M283,-847.697C283,-839.983 283,-830.712 283,-822.112"/>
<polygon fill="black" stroke="black" points="286.5,-822.104 283,-812.104 279.5,-822.104 286.5,-822.104"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;11 -->
<g id="node16" class="node"><title>proc&#45;batch_data_prepare_0&#45;11</title>
<polygon fill="white" stroke="black" points="462.25,-740 103.75,-740 103.75,-704 462.25,-704 462.25,-740"/>
<text text-anchor="middle" x="283" y="-718.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::enrich_attr_by_lua_DB344B</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;10&#45;&gt;proc&#45;batch_data_prepare_0&#45;11 -->
<g id="edge13" class="edge"><title>proc&#45;batch_data_prepare_0&#45;10&#45;&gt;proc&#45;batch_data_prepare_0&#45;11</title>
<path fill="none" stroke="black" d="M283,-775.697C283,-767.983 283,-758.712 283,-750.112"/>
<polygon fill="black" stroke="black" points="286.5,-750.104 283,-740.104 279.5,-750.104 286.5,-750.104"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;12 -->
<g id="node17" class="node"><title>proc&#45;batch_data_prepare_0&#45;12</title>
<polygon fill="white" stroke="black" points="507.25,-668 58.75,-668 58.75,-632 507.25,-632 507.25,-668"/>
<text text-anchor="middle" x="283" y="-646.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::build_table_from_common_list_attr_956196</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;11&#45;&gt;proc&#45;batch_data_prepare_0&#45;12 -->
<g id="edge14" class="edge"><title>proc&#45;batch_data_prepare_0&#45;11&#45;&gt;proc&#45;batch_data_prepare_0&#45;12</title>
<path fill="none" stroke="black" d="M283,-703.697C283,-695.983 283,-686.712 283,-678.112"/>
<polygon fill="black" stroke="black" points="286.5,-678.104 283,-668.104 279.5,-678.104 286.5,-678.104"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;13 -->
<g id="node18" class="node"><title>proc&#45;batch_data_prepare_0&#45;13</title>
<polygon fill="white" stroke="black" points="472,-596 94,-596 94,-560 472,-560 472,-596"/>
<text text-anchor="middle" x="283" y="-574.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::set_attr_mod_enricher_C905D8</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;12&#45;&gt;proc&#45;batch_data_prepare_0&#45;13 -->
<g id="edge15" class="edge"><title>proc&#45;batch_data_prepare_0&#45;12&#45;&gt;proc&#45;batch_data_prepare_0&#45;13</title>
<path fill="none" stroke="black" d="M283,-631.697C283,-623.983 283,-614.712 283,-606.112"/>
<polygon fill="black" stroke="black" points="286.5,-606.104 283,-596.104 279.5,-606.104 286.5,-606.104"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;14 -->
<g id="node19" class="node"><title>proc&#45;batch_data_prepare_0&#45;14</title>
<polygon fill="white" stroke="black" points="510,-524 56,-524 56,-488 510,-488 510,-524"/>
<text text-anchor="middle" x="283" y="-502.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::ad_message_queue_dispatch_mixer_AE9CC4</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;13&#45;&gt;proc&#45;batch_data_prepare_0&#45;14 -->
<g id="edge16" class="edge"><title>proc&#45;batch_data_prepare_0&#45;13&#45;&gt;proc&#45;batch_data_prepare_0&#45;14</title>
<path fill="none" stroke="black" d="M283,-559.697C283,-551.983 283,-542.712 283,-534.112"/>
<polygon fill="black" stroke="black" points="286.5,-534.104 283,-524.104 279.5,-534.104 286.5,-534.104"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;15 -->
<g id="node20" class="node"><title>proc&#45;batch_data_prepare_0&#45;15</title>
<polygon fill="white" stroke="black" points="432,-452 134,-452 134,-416 432,-416 432,-452"/>
<text text-anchor="middle" x="283" y="-430.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::calc_time_cost_e</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;14&#45;&gt;proc&#45;batch_data_prepare_0&#45;15 -->
<g id="edge17" class="edge"><title>proc&#45;batch_data_prepare_0&#45;14&#45;&gt;proc&#45;batch_data_prepare_0&#45;15</title>
<path fill="none" stroke="black" d="M283,-487.697C283,-479.983 283,-470.712 283,-462.112"/>
<polygon fill="black" stroke="black" points="286.5,-462.104 283,-452.104 279.5,-462.104 286.5,-462.104"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;16 -->
<g id="node21" class="node"><title>proc&#45;batch_data_prepare_0&#45;16</title>
<polygon fill="white" stroke="black" points="425.25,-380 140.75,-380 140.75,-344 425.25,-344 425.25,-380"/>
<text text-anchor="middle" x="283" y="-358.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::calc_time_cost</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;15&#45;&gt;proc&#45;batch_data_prepare_0&#45;16 -->
<g id="edge18" class="edge"><title>proc&#45;batch_data_prepare_0&#45;15&#45;&gt;proc&#45;batch_data_prepare_0&#45;16</title>
<path fill="none" stroke="black" d="M283,-415.697C283,-407.983 283,-398.712 283,-390.112"/>
<polygon fill="black" stroke="black" points="286.5,-390.104 283,-380.104 279.5,-390.104 286.5,-390.104"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;17 -->
<g id="node22" class="node"><title>proc&#45;batch_data_prepare_0&#45;17</title>
<polygon fill="white" stroke="black" points="425.25,-308 140.75,-308 140.75,-272 425.25,-272 425.25,-308"/>
<text text-anchor="middle" x="283" y="-286.3" font-family="Times,serif" font-size="14.00">batch_photo_message_collection::perf_time_cost</text>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;16&#45;&gt;proc&#45;batch_data_prepare_0&#45;17 -->
<g id="edge19" class="edge"><title>proc&#45;batch_data_prepare_0&#45;16&#45;&gt;proc&#45;batch_data_prepare_0&#45;17</title>
<path fill="none" stroke="black" d="M283,-343.697C283,-335.983 283,-326.712 283,-318.112"/>
<polygon fill="black" stroke="black" points="286.5,-318.104 283,-308.104 279.5,-318.104 286.5,-318.104"/>
</g>
<!-- proc&#45;batch_data_prepare_0&#45;17&#45;&gt;flow_end&#45;batch_data_prepare_0 -->
<g id="edge20" class="edge"><title>proc&#45;batch_data_prepare_0&#45;17&#45;&gt;flow_end&#45;batch_data_prepare_0</title>
<path fill="none" stroke="black" d="M283,-271.912C283,-263.746 283,-254.055 283,-246.155"/>
<polygon fill="black" stroke="black" points="286.5,-245.97 283,-235.97 279.5,-245.97 286.5,-245.97"/>
</g>
</g>
</svg>
