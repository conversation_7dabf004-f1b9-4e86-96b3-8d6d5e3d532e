<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.30.1 (20180420.1509)
 -->
<!-- Title: DAG&#45;ad&#45;dup&#45;photo&#45;detect&#45;server&#45;brand&#45;inc_data_prepare_flow Pages: 1 -->
<svg width="430pt" height="1006pt"
 viewBox="0.00 0.00 430.00 1006.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1002)">
<title>DAG&#45;ad&#45;dup&#45;photo&#45;detect&#45;server&#45;brand&#45;inc_data_prepare_flow</title>
<polygon fill="white" stroke="white" points="-4,5 -4,-1002 427,-1002 427,5 -4,5"/>
<text text-anchor="middle" x="211" y="-100" font-family="Times,serif" font-size="20.00">DAG drawn by Dragonfly v0.8.3</text>
<text text-anchor="middle" x="211" y="-78" font-family="Times,serif" font-size="20.00">Service: ad&#45;dup&#45;photo&#45;detect&#45;server&#45;brand</text>
<text text-anchor="middle" x="211" y="-56" font-family="Times,serif" font-size="20.00">RequestType: inc_data_prepare_flow</text>
<text text-anchor="middle" x="211" y="-34" font-family="Times,serif" font-size="20.00">Date: 2024&#45;11&#45;21 20:48:18</text>
<g id="clust1" class="cluster"><title>cluster_0</title>
<polygon fill="none" stroke="grey" points="8,-216 8,-932 414,-932 414,-216 8,-216"/>
<text text-anchor="middle" x="84.5" y="-912" font-family="Times,serif" font-size="20.00">inc_data_prepare</text>
</g>
<g id="clust2" class="cluster"><title>cluster_inc_data_prepare_5</title>
<polygon fill="none" stroke="grey" points="16,-264 16,-514 406,-514 406,-264 16,-264"/>
<text text-anchor="middle" x="211" y="-494" font-family="Times,serif" font-size="20.00">dispatch_flow (arrange_by_sub_flow_C2D939)</text>
</g>
<!-- START -->
<g id="node1" class="node"><title>START</title>
<polygon fill="lightgray" stroke="black" points="240,-998 182,-998 182,-940 240,-940 240,-998"/>
<polyline fill="none" stroke="black" points="194,-998 182,-986 "/>
<polyline fill="none" stroke="black" points="182,-952 194,-940 "/>
<polyline fill="none" stroke="black" points="228,-940 240,-952 "/>
<polyline fill="none" stroke="black" points="240,-986 228,-998 "/>
<text text-anchor="middle" x="211" y="-965.3" font-family="Times,serif" font-size="14.00">START</text>
</g>
<!-- flow_start&#45;inc_data_prepare_0 -->
<g id="node3" class="node"><title>flow_start&#45;inc_data_prepare_0</title>
<ellipse fill="grey" stroke="grey" cx="211" cy="-888" rx="5.76" ry="5.76"/>
</g>
<!-- START&#45;&gt;flow_start&#45;inc_data_prepare_0 -->
<g id="edge1" class="edge"><title>START&#45;&gt;flow_start&#45;inc_data_prepare_0</title>
<path fill="none" stroke="black" d="M211,-939.925C211,-927.818 211,-914.143 211,-904.015"/>
<polygon fill="black" stroke="black" points="214.5,-903.764 211,-893.764 207.5,-903.764 214.5,-903.764"/>
</g>
<!-- END -->
<g id="node2" class="node"><title>END</title>
<polygon fill="lightgray" stroke="black" points="233,-188 189,-188 189,-144 233,-144 233,-188"/>
<polyline fill="none" stroke="black" points="201,-188 189,-176 "/>
<polyline fill="none" stroke="black" points="189,-156 201,-144 "/>
<polyline fill="none" stroke="black" points="221,-144 233,-156 "/>
<polyline fill="none" stroke="black" points="233,-176 221,-188 "/>
<text text-anchor="middle" x="211" y="-162.3" font-family="Times,serif" font-size="14.00">END</text>
</g>
<!-- proc&#45;inc_data_prepare_0&#45;0 -->
<g id="node5" class="node"><title>proc&#45;inc_data_prepare_0&#45;0</title>
<polygon fill="white" stroke="black" points="380,-846 42,-846 42,-810 380,-810 380,-846"/>
<text text-anchor="middle" x="211" y="-824.3" font-family="Times,serif" font-size="14.00">ad_material_fetch_message_from_kafka_enricher_E451F9</text>
</g>
<!-- flow_start&#45;inc_data_prepare_0&#45;&gt;proc&#45;inc_data_prepare_0&#45;0 -->
<g id="edge2" class="edge"><title>flow_start&#45;inc_data_prepare_0&#45;&gt;proc&#45;inc_data_prepare_0&#45;0</title>
<path fill="none" stroke="black" d="M211,-882.055C211,-876.199 211,-865.986 211,-856.074"/>
<polygon fill="black" stroke="black" points="214.5,-856.049 211,-846.049 207.5,-856.049 214.5,-856.049"/>
</g>
<!-- flow_end&#45;inc_data_prepare_0 -->
<g id="node4" class="node"><title>flow_end&#45;inc_data_prepare_0</title>
<ellipse fill="grey" stroke="grey" cx="211" cy="-230" rx="5.76" ry="5.76"/>
</g>
<!-- flow_end&#45;inc_data_prepare_0&#45;&gt;END -->
<g id="edge12" class="edge"><title>flow_end&#45;inc_data_prepare_0&#45;&gt;END</title>
<path fill="none" stroke="black" d="M211,-224.135C211,-218.414 211,-208.42 211,-198.373"/>
<polygon fill="black" stroke="black" points="214.5,-198.061 211,-188.061 207.5,-198.061 214.5,-198.061"/>
</g>
<!-- proc&#45;inc_data_prepare_0&#45;1 -->
<g id="node6" class="node"><title>proc&#45;inc_data_prepare_0&#45;1</title>
<polygon fill="white" stroke="black" points="321.25,-774 100.75,-774 100.75,-738 321.25,-738 321.25,-774"/>
<text text-anchor="middle" x="211" y="-752.3" font-family="Times,serif" font-size="14.00">parse_protobuf_from_string_D7635E</text>
</g>
<!-- proc&#45;inc_data_prepare_0&#45;0&#45;&gt;proc&#45;inc_data_prepare_0&#45;1 -->
<g id="edge3" class="edge"><title>proc&#45;inc_data_prepare_0&#45;0&#45;&gt;proc&#45;inc_data_prepare_0&#45;1</title>
<path fill="none" stroke="black" d="M211,-809.697C211,-801.983 211,-792.712 211,-784.112"/>
<polygon fill="black" stroke="black" points="214.5,-784.104 211,-774.104 207.5,-784.104 214.5,-784.104"/>
</g>
<!-- proc&#45;inc_data_prepare_0&#45;2 -->
<g id="node7" class="node"><title>proc&#45;inc_data_prepare_0&#45;2</title>
<polygon fill="white" stroke="black" points="303.25,-702 118.75,-702 118.75,-666 303.25,-666 303.25,-702"/>
<text text-anchor="middle" x="211" y="-680.3" font-family="Times,serif" font-size="14.00">enrich_with_protobuf_968E4C</text>
</g>
<!-- proc&#45;inc_data_prepare_0&#45;1&#45;&gt;proc&#45;inc_data_prepare_0&#45;2 -->
<g id="edge4" class="edge"><title>proc&#45;inc_data_prepare_0&#45;1&#45;&gt;proc&#45;inc_data_prepare_0&#45;2</title>
<path fill="none" stroke="black" d="M211,-737.697C211,-729.983 211,-720.712 211,-712.112"/>
<polygon fill="black" stroke="black" points="214.5,-712.104 211,-702.104 207.5,-712.104 214.5,-712.104"/>
</g>
<!-- proc&#45;inc_data_prepare_0&#45;3 -->
<g id="node8" class="node"><title>proc&#45;inc_data_prepare_0&#45;3</title>
<polygon fill="white" stroke="black" points="342.25,-630 79.75,-630 79.75,-594 342.25,-594 342.25,-630"/>
<text text-anchor="middle" x="211" y="-608.3" font-family="Times,serif" font-size="14.00">build_table_from_common_list_attr_13851F</text>
</g>
<!-- proc&#45;inc_data_prepare_0&#45;2&#45;&gt;proc&#45;inc_data_prepare_0&#45;3 -->
<g id="edge5" class="edge"><title>proc&#45;inc_data_prepare_0&#45;2&#45;&gt;proc&#45;inc_data_prepare_0&#45;3</title>
<path fill="none" stroke="black" d="M211,-665.697C211,-657.983 211,-648.712 211,-640.112"/>
<polygon fill="black" stroke="black" points="214.5,-640.104 211,-630.104 207.5,-640.104 214.5,-640.104"/>
</g>
<!-- proc&#45;inc_data_prepare_0&#45;4 -->
<g id="node9" class="node"><title>proc&#45;inc_data_prepare_0&#45;4</title>
<polygon fill="white" stroke="black" points="296.25,-558 125.75,-558 125.75,-522 296.25,-522 296.25,-558"/>
<text text-anchor="middle" x="211" y="-536.3" font-family="Times,serif" font-size="14.00">enrich_attr_by_lua_9AA610</text>
</g>
<!-- proc&#45;inc_data_prepare_0&#45;3&#45;&gt;proc&#45;inc_data_prepare_0&#45;4 -->
<g id="edge6" class="edge"><title>proc&#45;inc_data_prepare_0&#45;3&#45;&gt;proc&#45;inc_data_prepare_0&#45;4</title>
<path fill="none" stroke="black" d="M211,-593.697C211,-585.983 211,-576.712 211,-568.112"/>
<polygon fill="black" stroke="black" points="214.5,-568.104 211,-558.104 207.5,-568.104 214.5,-568.104"/>
</g>
<!-- flow_start&#45;dispatch_flow_5 -->
<g id="node10" class="node"><title>flow_start&#45;dispatch_flow_5</title>
<ellipse fill="grey" stroke="grey" cx="211" cy="-470" rx="5.76" ry="5.76"/>
</g>
<!-- proc&#45;inc_data_prepare_0&#45;4&#45;&gt;flow_start&#45;dispatch_flow_5 -->
<g id="edge7" class="edge"><title>proc&#45;inc_data_prepare_0&#45;4&#45;&gt;flow_start&#45;dispatch_flow_5</title>
<path fill="none" stroke="black" d="M211,-521.841C211,-510.749 211,-496.459 211,-485.855"/>
<polygon fill="black" stroke="black" points="214.5,-485.815 211,-475.816 207.5,-485.816 214.5,-485.815"/>
</g>
<!-- proc&#45;dispatch_flow_5&#45;0 -->
<g id="node12" class="node"><title>proc&#45;dispatch_flow_5&#45;0</title>
<polygon fill="white" stroke="black" points="305.25,-428 116.75,-428 116.75,-392 305.25,-392 305.25,-428"/>
<text text-anchor="middle" x="211" y="-406.3" font-family="Times,serif" font-size="14.00">set_attr_mod_enricher_73C3F7</text>
</g>
<!-- flow_start&#45;dispatch_flow_5&#45;&gt;proc&#45;dispatch_flow_5&#45;0 -->
<g id="edge8" class="edge"><title>flow_start&#45;dispatch_flow_5&#45;&gt;proc&#45;dispatch_flow_5&#45;0</title>
<path fill="none" stroke="black" d="M211,-464.055C211,-458.199 211,-447.986 211,-438.074"/>
<polygon fill="black" stroke="black" points="214.5,-438.049 211,-428.049 207.5,-438.049 214.5,-438.049"/>
</g>
<!-- flow_end&#45;dispatch_flow_5 -->
<g id="node11" class="node"><title>flow_end&#45;dispatch_flow_5</title>
<ellipse fill="grey" stroke="grey" cx="211" cy="-278" rx="5.76" ry="5.76"/>
</g>
<!-- flow_end&#45;dispatch_flow_5&#45;&gt;flow_end&#45;inc_data_prepare_0 -->
<g id="edge11" class="edge"><title>flow_end&#45;dispatch_flow_5&#45;&gt;flow_end&#45;inc_data_prepare_0</title>
<path fill="none" stroke="black" d="M211,-272.077C211,-265.953 211,-255.211 211,-246.247"/>
<polygon fill="black" stroke="black" points="214.5,-246.027 211,-236.027 207.5,-246.027 214.5,-246.027"/>
</g>
<!-- proc&#45;dispatch_flow_5&#45;1 -->
<g id="node13" class="node"><title>proc&#45;dispatch_flow_5&#45;1</title>
<polygon fill="white" stroke="black" points="343,-356 79,-356 79,-320 343,-320 343,-356"/>
<text text-anchor="middle" x="211" y="-334.3" font-family="Times,serif" font-size="14.00">ad_message_queue_dispatch_mixer_E5FEB8</text>
</g>
<!-- proc&#45;dispatch_flow_5&#45;0&#45;&gt;proc&#45;dispatch_flow_5&#45;1 -->
<g id="edge9" class="edge"><title>proc&#45;dispatch_flow_5&#45;0&#45;&gt;proc&#45;dispatch_flow_5&#45;1</title>
<path fill="none" stroke="black" d="M211,-391.697C211,-383.983 211,-374.712 211,-366.112"/>
<polygon fill="black" stroke="black" points="214.5,-366.104 211,-356.104 207.5,-366.104 214.5,-366.104"/>
</g>
<!-- proc&#45;dispatch_flow_5&#45;1&#45;&gt;flow_end&#45;dispatch_flow_5 -->
<g id="edge10" class="edge"><title>proc&#45;dispatch_flow_5&#45;1&#45;&gt;flow_end&#45;dispatch_flow_5</title>
<path fill="none" stroke="black" d="M211,-319.912C211,-311.746 211,-302.055 211,-294.155"/>
<polygon fill="black" stroke="black" points="214.5,-293.97 211,-283.97 207.5,-293.97 214.5,-293.97"/>
</g>
</g>
</svg>
