<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.30.1 (20180420.1509)
 -->
<!-- Title: DAG&#45;ad&#45;dup&#45;photo&#45;detect&#45;server&#45;brand&#45;index_inc_debug_flow Pages: 1 -->
<svg width="384pt" height="846pt"
 viewBox="0.00 0.00 384.00 846.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 842)">
<title>DAG&#45;ad&#45;dup&#45;photo&#45;detect&#45;server&#45;brand&#45;index_inc_debug_flow</title>
<polygon fill="white" stroke="white" points="-4,5 -4,-842 381,-842 381,5 -4,5"/>
<text text-anchor="middle" x="188" y="-100" font-family="Times,serif" font-size="20.00">DAG drawn by Dragonfly v0.8.0</text>
<text text-anchor="middle" x="188" y="-78" font-family="Times,serif" font-size="20.00">Service: ad&#45;dup&#45;photo&#45;detect&#45;server&#45;brand</text>
<text text-anchor="middle" x="188" y="-56" font-family="Times,serif" font-size="20.00">RequestType: index_inc_debug_flow</text>
<text text-anchor="middle" x="188" y="-34" font-family="Times,serif" font-size="20.00">Date: 2024&#45;06&#45;01 01:25:20</text>
<g id="clust1" class="cluster"><title>cluster_0</title>
<polygon fill="none" stroke="grey" points="8,-216 8,-772 368,-772 368,-216 8,-216"/>
<text text-anchor="middle" x="83" y="-752" font-family="Times,serif" font-size="20.00">index_inc_debug</text>
</g>
<!-- START -->
<g id="node1" class="node"><title>START</title>
<polygon fill="lightgray" stroke="black" points="217,-838 159,-838 159,-780 217,-780 217,-838"/>
<polyline fill="none" stroke="black" points="171,-838 159,-826 "/>
<polyline fill="none" stroke="black" points="159,-792 171,-780 "/>
<polyline fill="none" stroke="black" points="205,-780 217,-792 "/>
<polyline fill="none" stroke="black" points="217,-826 205,-838 "/>
<text text-anchor="middle" x="188" y="-805.3" font-family="Times,serif" font-size="14.00">START</text>
</g>
<!-- flow_start&#45;index_inc_debug_0 -->
<g id="node3" class="node"><title>flow_start&#45;index_inc_debug_0</title>
<ellipse fill="grey" stroke="grey" cx="188" cy="-728" rx="5.76" ry="5.76"/>
</g>
<!-- START&#45;&gt;flow_start&#45;index_inc_debug_0 -->
<g id="edge1" class="edge"><title>START&#45;&gt;flow_start&#45;index_inc_debug_0</title>
<path fill="none" stroke="black" d="M188,-779.925C188,-767.818 188,-754.143 188,-744.015"/>
<polygon fill="black" stroke="black" points="191.5,-743.764 188,-733.764 184.5,-743.764 191.5,-743.764"/>
</g>
<!-- END -->
<g id="node2" class="node"><title>END</title>
<polygon fill="lightgray" stroke="black" points="210,-188 166,-188 166,-144 210,-144 210,-188"/>
<polyline fill="none" stroke="black" points="178,-188 166,-176 "/>
<polyline fill="none" stroke="black" points="166,-156 178,-144 "/>
<polyline fill="none" stroke="black" points="198,-144 210,-156 "/>
<polyline fill="none" stroke="black" points="210,-176 198,-188 "/>
<text text-anchor="middle" x="188" y="-162.3" font-family="Times,serif" font-size="14.00">END</text>
</g>
<!-- proc&#45;index_inc_debug_0&#45;0 -->
<g id="node5" class="node"><title>proc&#45;index_inc_debug_0&#45;0</title>
<polygon fill="white" stroke="black" points="359.25,-686 16.75,-686 16.75,-650 359.25,-650 359.25,-686"/>
<text text-anchor="middle" x="188" y="-664.3" font-family="Times,serif" font-size="14.00">ad_material_fetch_message_from_kafka_enricher_F3A6FD</text>
</g>
<!-- flow_start&#45;index_inc_debug_0&#45;&gt;proc&#45;index_inc_debug_0&#45;0 -->
<g id="edge2" class="edge"><title>flow_start&#45;index_inc_debug_0&#45;&gt;proc&#45;index_inc_debug_0&#45;0</title>
<path fill="none" stroke="black" d="M188,-722.055C188,-716.199 188,-705.986 188,-696.074"/>
<polygon fill="black" stroke="black" points="191.5,-696.049 188,-686.049 184.5,-696.049 191.5,-696.049"/>
</g>
<!-- flow_end&#45;index_inc_debug_0 -->
<g id="node4" class="node"><title>flow_end&#45;index_inc_debug_0</title>
<ellipse fill="grey" stroke="grey" cx="188" cy="-230" rx="5.76" ry="5.76"/>
</g>
<!-- flow_end&#45;index_inc_debug_0&#45;&gt;END -->
<g id="edge9" class="edge"><title>flow_end&#45;index_inc_debug_0&#45;&gt;END</title>
<path fill="none" stroke="black" d="M188,-224.135C188,-218.414 188,-208.42 188,-198.373"/>
<polygon fill="black" stroke="black" points="191.5,-198.061 188,-188.061 184.5,-198.061 191.5,-198.061"/>
</g>
<!-- proc&#45;index_inc_debug_0&#45;1 -->
<g id="node6" class="node"><title>proc&#45;index_inc_debug_0&#45;1</title>
<polygon fill="white" stroke="black" points="298,-614 78,-614 78,-578 298,-578 298,-614"/>
<text text-anchor="middle" x="188" y="-592.3" font-family="Times,serif" font-size="14.00">parse_protobuf_from_string_E658E6</text>
</g>
<!-- proc&#45;index_inc_debug_0&#45;0&#45;&gt;proc&#45;index_inc_debug_0&#45;1 -->
<g id="edge3" class="edge"><title>proc&#45;index_inc_debug_0&#45;0&#45;&gt;proc&#45;index_inc_debug_0&#45;1</title>
<path fill="none" stroke="black" d="M188,-649.697C188,-641.983 188,-632.712 188,-624.112"/>
<polygon fill="black" stroke="black" points="191.5,-624.104 188,-614.104 184.5,-624.104 191.5,-624.104"/>
</g>
<!-- proc&#45;index_inc_debug_0&#45;2 -->
<g id="node7" class="node"><title>proc&#45;index_inc_debug_0&#45;2</title>
<polygon fill="white" stroke="black" points="281.25,-542 94.75,-542 94.75,-506 281.25,-506 281.25,-542"/>
<text text-anchor="middle" x="188" y="-520.3" font-family="Times,serif" font-size="14.00">enrich_with_protobuf_1B522A</text>
</g>
<!-- proc&#45;index_inc_debug_0&#45;1&#45;&gt;proc&#45;index_inc_debug_0&#45;2 -->
<g id="edge4" class="edge"><title>proc&#45;index_inc_debug_0&#45;1&#45;&gt;proc&#45;index_inc_debug_0&#45;2</title>
<path fill="none" stroke="black" d="M188,-577.697C188,-569.983 188,-560.712 188,-552.112"/>
<polygon fill="black" stroke="black" points="191.5,-552.104 188,-542.104 184.5,-552.104 191.5,-552.104"/>
</g>
<!-- proc&#45;index_inc_debug_0&#45;3 -->
<g id="node8" class="node"><title>proc&#45;index_inc_debug_0&#45;3</title>
<polygon fill="white" stroke="black" points="251.25,-470 124.75,-470 124.75,-434 251.25,-434 251.25,-470"/>
<text text-anchor="middle" x="188" y="-448.3" font-family="Times,serif" font-size="14.00">perflog_D502DCFB</text>
</g>
<!-- proc&#45;index_inc_debug_0&#45;2&#45;&gt;proc&#45;index_inc_debug_0&#45;3 -->
<g id="edge5" class="edge"><title>proc&#45;index_inc_debug_0&#45;2&#45;&gt;proc&#45;index_inc_debug_0&#45;3</title>
<path fill="none" stroke="black" d="M188,-505.697C188,-497.983 188,-488.712 188,-480.112"/>
<polygon fill="black" stroke="black" points="191.5,-480.104 188,-470.104 184.5,-480.104 191.5,-480.104"/>
</g>
<!-- proc&#45;index_inc_debug_0&#45;4 -->
<g id="node9" class="node"><title>proc&#45;index_inc_debug_0&#45;4</title>
<ellipse fill="lightgrey" stroke="black" cx="188" cy="-371" rx="132.404" ry="26.7407"/>
<text text-anchor="middle" x="188" y="-374.8" font-family="Times,serif" font-size="14.00">_branch_controller_6713A8DE</text>
<text text-anchor="middle" x="188" y="-359.8" font-family="Times,serif" font-size="14.00">(data_type == 99)</text>
</g>
<!-- proc&#45;index_inc_debug_0&#45;3&#45;&gt;proc&#45;index_inc_debug_0&#45;4 -->
<g id="edge6" class="edge"><title>proc&#45;index_inc_debug_0&#45;3&#45;&gt;proc&#45;index_inc_debug_0&#45;4</title>
<path fill="none" stroke="black" d="M188,-433.858C188,-426.356 188,-417.25 188,-408.358"/>
<polygon fill="black" stroke="black" points="191.5,-408.126 188,-398.126 184.5,-408.126 191.5,-408.126"/>
</g>
<!-- proc&#45;index_inc_debug_0&#45;5 -->
<g id="node10" class="node"><title>proc&#45;index_inc_debug_0&#45;5</title>
<polygon fill="white" stroke="black" points="266,-308 110,-308 110,-272 266,-272 266,-308"/>
<text text-anchor="middle" x="188" y="-286.3" font-family="Times,serif" font-size="14.00">log_debug_info_BC67A5</text>
</g>
<!-- proc&#45;index_inc_debug_0&#45;4&#45;&gt;proc&#45;index_inc_debug_0&#45;5 -->
<g id="edge7" class="edge"><title>proc&#45;index_inc_debug_0&#45;4&#45;&gt;proc&#45;index_inc_debug_0&#45;5</title>
<path fill="none" stroke="black" d="M188,-343.694C188,-335.58 188,-326.626 188,-318.438"/>
<polygon fill="black" stroke="black" points="191.5,-318.248 188,-308.248 184.5,-318.248 191.5,-318.248"/>
</g>
<!-- proc&#45;index_inc_debug_0&#45;5&#45;&gt;flow_end&#45;index_inc_debug_0 -->
<g id="edge8" class="edge"><title>proc&#45;index_inc_debug_0&#45;5&#45;&gt;flow_end&#45;index_inc_debug_0</title>
<path fill="none" stroke="black" d="M188,-271.912C188,-263.746 188,-254.055 188,-246.155"/>
<polygon fill="black" stroke="black" points="191.5,-245.97 188,-235.97 184.5,-245.97 191.5,-245.97"/>
</g>
</g>
</svg>
