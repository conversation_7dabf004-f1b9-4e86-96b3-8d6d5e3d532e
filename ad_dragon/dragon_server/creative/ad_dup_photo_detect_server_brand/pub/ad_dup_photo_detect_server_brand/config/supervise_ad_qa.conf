[program:ad_qa]
command=python3 {directory}/bin/ad_qa.py
directory={directory}
numprocs=1
umask=022
priority=999
autostart=true
autorestart=unexpected
startsecs=3
startretries=3
exitcodes=0
stopsignal=TERM
stopwaitsecs=3
user=web_server
environment=USER="web_server"
stopasgroup=true
killasgroup=true
redirect_stderr=false
stdout_logfile={directory}/log/qa.stdout
stdout_logfile_maxbytes=100MB
stdout_logfile_backups=1
stdout_capture_maxbytes=10MB
stdout_events_enabled=false
stderr_logfile={directory}/log/qa.stderr
stderr_logfile_maxbytes=100MB
stderr_logfile_backups=1
stderr_capture_maxbytes=10MB
stderr_events_enabled=false
