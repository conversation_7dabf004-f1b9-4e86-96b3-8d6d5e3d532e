syntax = "proto3";

package kuaishou.ad;

option java_package = "com.kuaishou.protobuf.ad.crypt";

option java_outer_classname = "AdCryptServiceProto";

option java_multiple_files = true;

message AdChargeCiphertext {
    string charge_info = 1;
}

message AdChargeText {
    string charge_info = 1;
}

service AdCryptService {
    rpc encrypt (AdChargeText) returns (AdChargeCiphertext);
    rpc decrypt (AdChargeCiphertext) returns (AdChargeText);
};
