#include <csignal>
#include <unistd.h>

#include <kess/common/logger.h>
#include <kess/common/to_string.h>
#include <kess/rpc/rpc_facade.h>

#include "teams/ad/ad_crypt_server/server/ad_crypt_service.h"
#include "base/common/logging.h"
#include "ks/serving_util/server_base.h"
#include "ks/serving_util/zk_server_register.h"
#include "teams/ad/ad_base/src/kess/macros.h"

DEFINE_uint64(grpc_thread_num, 100, "number of threads");
DEFINE_string(server_address, "0.0.0.0:20012", "server address");
DEFINE_uint64(grpc_server_port, 20012, "server grpc port");
DEFINE_int32(web_thread_num, 1, "server http thread_num");
DEFINE_int32(web_server_port, 20010, "server http port");

namespace common = ks::kess::common;
namespace scheduler = ks::kess::scheduler;
namespace rpc = ks::kess::rpc;

int main(int argc, char **argv) {
  ks::serving_util::ServerStatus::Singleton()->SetStatusCode(ks::serving_util::ServerStatusCode::STARTING);
  base::InitApp(&argc, &argv, "ad cs server");
  ks::ad_cs::AdCryptService service(FLAGS_grpc_thread_num, FLAGS_server_address);

  ks::ad_cs::AdCryptWebService web_service(FLAGS_web_thread_num);
  net::WebServer::Options web_server_option;
  web_server_option.port = FLAGS_web_server_port;
  web_server_option.backlog = 1024;
  net::WebServer web_server(web_server_option, &web_service);
  web_server.Start();
  LOG(INFO) << "web server started " << FLAGS_web_server_port;

  // 初始化 kess 环境
  bool kess_test = ks::DynamicJsonConfig::GetConfig()->GetBoolean("kess_test", false);
  std::string kess_service_name = ks::DynamicJsonConfig::GetConfig()->GetString("kess_service_name",
      "grpc_adCryptServer");
  KESS_SERVER_INIT(kess_service_name, FLAGS_grpc_server_port, kess_test);
  auto p_async_server = service.GetAsyncServer();
  auto& builder = p_async_server->Builder();
  rpc::RpcFacade::SetupGrpcServer(&builder, *scheduler);

  // 启动 grpc
  p_async_server->BuildAndStart();
  LOG(INFO)<<"Start CS Service Succeed: " << FLAGS_grpc_server_port;
  ::google::FlushLogFiles(::google::INFO);
  if (!service.Start()) {
    LOG(FATAL) << "start cs server Failed";
  }

  // 不再注册grpc到zk
  //ks::ServerBase::Singleton()->ZkAutoRegister();

  // 注册 grpc 到 Kess
  if (scheduler->Register() != 0) {
    common::Logger::Error("Register fails.");
    std::abort();
  }

  ks::serving_util::ServerStatus::Singleton()->SetStatusCode(ks::serving_util::ServerStatusCode::RUNNING);

  //等待停止的信号
  ks::ServerBase::Singleton()->WaitForSignal();
  ks::serving_util::ServerStatus::Singleton()->SetStatusCode(ks::serving_util::ServerStatusCode::STOPPING);

  // 停掉Kess调度器
  scheduler->Unregister();
  base::SleepForMilliseconds(30*1000);
  //停止服务
  service.Stop();
  web_server.Stop();

  LOG(INFO) << "CS SERVER quits safety";
  ::google::FlushLogFiles(::google::INFO);
  ks::serving_util::ServerStatus::Singleton()->SetStatusCode(ks::serving_util::ServerStatusCode::STOPPED);
  return 0;
}
