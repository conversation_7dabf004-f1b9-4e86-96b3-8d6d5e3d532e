#pragma once

#include "teams/ad/ad_base/src/common/common.h"
#include "teams/ad/ad_base/src/rpc/async_grpc_server/server.h"
#include "teams/ad/ad_base/src/rpc/async_grpc_server/server_async_extern.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_crypt_service.grpc.pb.h"
#include "teams/ad/ad_base/src/ksp/ad_web_request_handler.h"

namespace ks {
namespace ad_cs {
class AdCryptService {
public:
  AdCryptService(int thread_num, const std::string & server_address)
  : async_server_(server_address, thread_num) {}
  bool Start();
  void Stop();
  grpc::Status EncryptHandler(const kuaishou::ad::AdChargeText *request, kuaishou::ad::AdChargeCiphertext *response);
  grpc::Status DecryptHandler(const kuaishou::ad::AdChargeCiphertext *request, kuaishou::ad::AdChargeText *response);
  ks::ad_base::AsyncServer2<kuaishou::ad::AdCryptService::AsyncService>* GetAsyncServer() {
    return &async_server_;
  };
private:
  ks::ad_base::AsyncServer2<kuaishou::ad::AdCryptService::AsyncService> async_server_;
};

class AdCryptServiceWebServiceHandlerDict : public serving_base::WebRequestHandlerDict {
public:
  explicit AdCryptServiceWebServiceHandlerDict();
  ~AdCryptServiceWebServiceHandlerDict() {
    for (auto it = handler_dict_.begin(); it != handler_dict_.end(); ++it) {
      delete it->second;
    }
  }
private:
  DISALLOW_COPY_AND_ASSIGN(AdCryptServiceWebServiceHandlerDict);
};

class AdCryptWebService : public serving_base::WebService {
public:
  explicit AdCryptWebService(int threads_num)
      : WebService(threads_num) {
    for (int i = 0; i < threads_num_; ++i) {
      handler_dicts_.Put(new AdCryptServiceWebServiceHandlerDict());
    }
  }
  ~AdCryptWebService() {
    for (int i = 0; i < threads_num_; ++i) delete handler_dicts_.Take();
  }
private:
  DISALLOW_COPY_AND_ASSIGN(AdCryptWebService);
};

}
}
