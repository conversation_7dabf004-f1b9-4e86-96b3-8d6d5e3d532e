#include "teams/ad/ad_crypt_server/server/ad_crypt_service.h"
#include "base/common/logging.h"
#include "base/common/base.h"
#include "serving_base/crypt/aes_crypter.h"
#include "base/encoding/base64.h"
#include "ks/util/json.h"
#include "infra/falcon_counter/src/falcon/counter.h"

namespace ks {
namespace ad_cs {

static serving_base::AesCrypter crypter;

bool AdCryptService::Start() {
  bool ok = ks::ad_base::AddRpcCall2<kuaishou::ad::AdChargeText, kuaishou::ad::AdChargeCiphertext, kuaishou::ad::AdCryptService::AsyncService, AdCryptService>
          (async_server_,
          &kuaishou::ad::AdCryptService::AsyncService::Requestencrypt,
          this,
          &AdCryptService::EncryptHandler);
  if (!ok) {
    LOG(ERROR)<<"AddRpcCall failed";
    return false;
  }
  ok = ks::ad_base::AddRpcCall2<kuaishou::ad::AdChargeCiphertext, kuaishou::ad::AdChargeText, kuaishou::ad::AdCryptService::AsyncService, AdCryptService>
          (async_server_,
          &kuaishou::ad::AdCryptService::AsyncService::Requestdecrypt,
          this,
          &AdCryptService::DecryptHandler);
  if (!ok) {
    LOG(ERROR)<<"AddRpcCall failed";
    return false;
  }
  LOG(INFO)<<"AddRpcCall ok";
  async_server_.Start();
  return true;
}

grpc::Status AdCryptService::EncryptHandler(const kuaishou::ad::AdChargeText *request, kuaishou::ad::AdChargeCiphertext *response) {
  falcon::Inc("ad_cs.encrypt_count", 1);
  auto &charge_info = request->charge_info();
  std::string base64_decode_string;
  if (KS_LIKELY(base::Base64Decode(charge_info, &base64_decode_string))) {
    std::string base64_result;
    base::Base64Encode(crypter.Encrypt(base64_decode_string), &base64_result);
    response->set_charge_info(base64_result);
    LOG_EVERY_N(INFO, 10000) << "encrypt succeed. request == " << request->ShortDebugString()
                             <<". response == " << response->ShortDebugString();
  } else {
    falcon::Inc("ad_cs.encrypt_failed_count", 1);
    LOG(ERROR) << "encrypt failed. decode failed. request == " << request->ShortDebugString();
  }
  return Status::OK;
}

grpc::Status AdCryptService::DecryptHandler(const kuaishou::ad::AdChargeCiphertext *request, kuaishou::ad::AdChargeText *response) {
  falcon::Inc("ad_cs.decrypt_count", 1);
  auto &charge_info = request->charge_info();
  std::string base64_decode_string;
  if (KS_LIKELY(base::Base64Decode(charge_info, &base64_decode_string))) {
    std::string aes_decode_result;
    std::string base64_result;
    if (KS_LIKELY(crypter.Decrypt(base64_decode_string, &aes_decode_result))) {
      base::Base64Encode(aes_decode_result, &base64_result);
      response->set_charge_info(base64_result);
      LOG_EVERY_N(INFO, 10000) << "decrypt succeed. request == " << request->ShortDebugString()
                               <<". response == " << response->ShortDebugString();
    } else {
      falcon::Inc("ad_cs.decrypt_failed_count", 1);
      LOG(WARNING) << "decrypt failed. request == " << request->ShortDebugString();
    }
  } else {
    falcon::Inc("ad_cs.decrypt_decode_failed_count", 1);
    LOG(WARNING) << "decrypt failed. request == " << request->ShortDebugString();
  }
  return Status::OK;
}

void AdCryptService::Stop(){
  //扫尾工作在AsyncServer的析构函数中进行
}

AdCryptServiceWebServiceHandlerDict::AdCryptServiceWebServiceHandlerDict() : WebRequestHandlerDict(true) {
  handler_dict_["/update_config"] = new ks::ad_base::ConfigUpdateHandler();
  handler_dict_["/server_status_code"] = new ks::ad_base::ServerStatusHandler();
}

}
}
