#pragma once

#include <memory>
#include <mutex>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include "base/thread/thread_pool.h"
#include "serving_base/utility/system_util.h"
#include "teams/ad/ad_base/src/common/condition_time_wait.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_data_format_server/proto/ad_data_format_server.kess.grpc.pb.h"
#include "teams/ad/data_push/version_manager_base.h"

namespace ks {

namespace ad_base {
class Dot;
}

namespace ad_data_format_server {

class ControllerManager {
 public:
  struct WeightedProducer {
    std::string m_pod_name;
    int m_weight;
    WeightedProducer(const std::string& pod_name, int weight) {
      m_pod_name = pod_name;
      m_weight = weight;
    }
    bool operator<(const WeightedProducer& wp) const {
      return this->m_weight < wp.m_weight;
    }
    bool operator==(const WeightedProducer& wp) const {
      return this->m_pod_name == wp.m_pod_name;
    }
  };
  static ControllerManager& Instance() {
    static ControllerManager instance;
    return instance;
  }
  void Start();
  void Stop();
  // 更新 producer 上报信息
  void UpdateProducerInfo(const ReportTaskInfoReq& report_info);
  // 恢复任务
  bool RecoverTask(const std::string& pod_name, RecoverTaskResp* resp);

 private:
  ControllerManager();
  ~ControllerManager();
  ControllerManager(const ControllerManager&) = delete;
  ControllerManager& operator=(const ControllerManager&) = delete;
  void ReloadProducerInfo();
  void CleanExpireProducer();
  void RecordProducerInfo2Redis(::ks::infra::RedisClient* cli);
  void UpdateAndDispatch(::ks::infra::RedisClient* cli,
                         const std::set<std::string>& data_names);
  void DispatchTask2Producer(const std::unordered_map<std::string, std::string>& dispatch_tasks);
  bool GetPodName(std::string* pod_name);
  void GenerateDispatchRequest(const std::string& task_name,
                               const std::string& data_version,
                               DispatchTaskReq* req);
  std::string GenerateCombineKey(const std::string& task_name,
                                 const std::string& version);
  void DoTask();
  void ReportInfo(const std::set<std::string>& all_datas);
  // data_name 集合
  std::unordered_set<std::string> datas_;
  // <data_name, version>
  std::unordered_set<std::string> running_tasks_;
  // <data_name, version>
  std::unordered_map<std::string, std::string> wait_tasks_;
  // <data_name#version, status>
  std::unordered_map<std::string, ReportTaskInfoReq> producer_info_;
  std::set<WeightedProducer> weighted_producers_;
  thread::ThreadPool thread_pool_;
  ad_base::ConditionTimeWait cw_;
  std::atomic_bool stop_{false};
  std::mutex mutex_;
  std::shared_ptr<ad::data_push::TimeStrPathCurator> curator_;
  std::unique_ptr<ad_base::Dot> dot_;
};

}  // namespace ad_data_format_server
}  // namespace ks
