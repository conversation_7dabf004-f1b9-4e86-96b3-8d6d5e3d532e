#include "teams/ad/ad_data_format_server/controller/controller_service.h"
#include "teams/ad/ad_data_format_server/controller/controller_manager.h"
#include "teams/ad/ad_data_format_server/utils/zk/zk_leader_manager.h"

namespace ks {
namespace ad_data_format_server {
::grpc::Status ControllerService::ReportTaskInfo(::grpc::ServerContext* context,
                                                 const ReportTaskInfoReq* req,
                                                 ReportTaskInfoResp* resp) {
  if (!ZkLeaderManager::Instance().IsLeader()) {
    resp->set_status(false);
  } else {
    ControllerManager::Instance().UpdateProducerInfo(*req);
    resp->set_status(true);
  }
  return grpc::Status::OK;
}

::grpc::Status ControllerService::RecoverTask(::grpc::ServerContext* context,
                                              const RecoverTaskReq* req,
                                              RecoverTaskResp* resp) {
  if (!ZkLeaderManager::Instance().IsLeader()) {
    resp->set_status(false);
  } else {
    bool ret = ControllerManager::Instance().RecoverTask(req->pod_info().pod_name(), resp);
    resp->set_status(true);
  }
  return grpc::Status::OK;
}

}  // namespace ad_data_format_server
}  // namespace ks
