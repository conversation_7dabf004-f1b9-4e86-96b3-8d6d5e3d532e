#include "teams/ad/ad_data_format_server/controller/controller_manager.h"
#include <vector>
#include "absl/strings/str_cat.h"
#include "base/common/closure.h"
#include "base/file/file_util.h"
#include "base/time/timestamp.h"
#include "perfutil/perfutil.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/dot/dot.h"
#include "teams/ad/ad_data_format_server/proto/ad_data_format_server.kess.grpc.pb.h"
#include "teams/ad/ad_data_format_server/utils/kconf/kconf.h"
#include "teams/ad/ad_data_format_server/utils/zk/zk_leader_manager.h"
#include "teams/ad/ad_data_format_server/proto/ad_data_format_server.pb.h"

namespace ks {
namespace ad_data_format_server {
namespace {
static const char log_prefix[] = "[ad_data_format_controller]";
static const char kProducerClientKey[] = "ad_data_format_producer_client";
static const char kKfsPath[] = "/data/web_server/ad_kfs/";
static const char kCheckedSuffix[] = "checked";
static const char DATA_CHECK_REDIS_PREFIX[] = "data_check:";
}

ControllerManager::ControllerManager() : thread_pool_(1), dot_(std::make_unique<ad_base::Dot>(1)) {
  curator_ =
    std::make_shared<ad::data_push::TimeStrPathCurator>(ad::data_push::DataPushConfig::Create("default"));
}

ControllerManager::~ControllerManager() {
  thread_pool_.JoinAll();
}

void ControllerManager::Start() {
  thread_pool_.AddTask(NewCallback(this, &ControllerManager::DoTask));
}

void ControllerManager::Stop() {
  cw_.TryStop();
  while (!stop_) {
    sleep(1);
  }
}

void ControllerManager::ReloadProducerInfo() {
  // 还原 producer 信息和任务执行状态
  auto* cli = ::ks::ad_base::KconfRedis::Instance().GetAdRedisClient(
  "adWarmupData", ks::engine_base::DependDataLevel::STRONG_DEPEND);
  if (cli == nullptr) {
    LOG(ERROR) << "client is nullptr";
    return;
  }
  std::set<std::string> pod_name_set;
  int ret = cli->SGetMembers("all_pod_name", &pod_name_set);
  if (ret != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
    LOG(ERROR) << log_prefix << "get all pod set failde, ret=" << ret;
    return;
  }
  std::unique_lock<std::mutex> lock(mutex_);
  for (const auto& pod : pod_name_set) {
    std::string value;
    int ret = cli->Get(pod, &value);
    if (ret != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
      LOG(WARNING) << log_prefix << "get pod info failed, pod_name=" << pod << ", ret=" << ret;
      continue;
    }
    ReportTaskInfoReq pb;
    if (pb.ParseFromString(value)) {
      producer_info_[pod] = pb;
      LOG(INFO) << log_prefix << "producer_info=" << pb.DebugString();
    }
    // 还原任务状态
    for (int i = 0; i < pb.task_execution_infos_size(); i++) {
      auto task_info = pb.task_execution_infos(i);
      if (task_info.task_status() != TaskStatus::TS_FINISHED) {
        running_tasks_.emplace(GenerateCombineKey(task_info.task_name(), task_info.data_version()));
      }
    }
  }
}

bool ControllerManager::RecoverTask(const std::string& pod_name,
                                    RecoverTaskResp* resp) {
  if (!resp) return false;
  std::lock_guard<std::mutex> lock(mutex_);
  auto itr = producer_info_.find(pod_name);
  if (itr != producer_info_.end()) {
    LOG(INFO) << log_prefix << "RecoverTask, pod_name=" << pod_name;
    resp->mutable_task_execution_infos()->CopyFrom(itr->second.task_execution_infos());
    return true;
  }
  return false;
}

void ControllerManager::CleanExpireProducer() {
  std::unique_lock<std::mutex> lock(mutex_);
  for (const auto& producer : producer_info_) {
    if (producer.second.task_execution_infos_size() == 0) {
      continue;
    }
    int64_t last_time = producer.second.task_execution_infos(0).timestamp();
    int64_t now = base::GetTimestamp() / (1000 * 1000);
    if (now - last_time > AdKconfUtil::producerExpireSeconds()) {
      auto itr = weighted_producers_.find(WeightedProducer(producer.first, 0));
      if (itr != weighted_producers_.end()) {
        weighted_producers_.erase(itr);
      }
    }
  }
}

std::string ControllerManager::GenerateCombineKey(const std::string& task_name,
                                                  const std::string& version) {
  return absl::Substitute("$0#$1", task_name, version);
}

void ControllerManager::UpdateProducerInfo(const ReportTaskInfoReq& report_info) {
  auto* cli = ::ks::ad_base::KconfRedis::Instance().GetAdRedisClient(
  "adWarmupData", ks::engine_base::DependDataLevel::STRONG_DEPEND);
  if (cli == nullptr) {
    LOG(ERROR) << "client is nullptr";
    return;
  }
  int ret = cli->SAdd("all_pod_name", report_info.pod_info().pod_name());
  if (ret != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
    LOG(WARNING) << log_prefix << "update all pod name failed, ret=" << ret;
    return;
  }
  std::unique_lock<std::mutex> lock(mutex_);
  producer_info_[report_info.pod_info().pod_name()] = report_info;
  int total_task_size = report_info.task_execution_infos_size();
  int running_task_size = 0;
  for (int i = 0; i < total_task_size; i++) {
    auto task_info = report_info.task_execution_infos(i);
    std::string version_name;
    int64_t version = 0;;
    bool checked;
    bool version_checked = curator_->ExtractVersion(
      task_info.data_version(), &version_name, &version, &checked);
    if (!version_checked) {
      continue;
    }
    switch (task_info.task_status()) {
      case TaskStatus::TS_RUNING:
        running_task_size++;
        break;
      case TaskStatus::TS_FINISHED:
        // 记录到 redis 中
        {
          std::string finished_key = absl::Substitute("fts#$0", task_info.task_name());
          int ret = cli->ZAdd(finished_key, task_info.data_version(), version);
          if (ret != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
            LOG(WARNING) << log_prefix << "update finished task failed, task="
                         << GenerateCombineKey(task_info.task_name(), task_info.data_version());
          }
          int64_t cnt = 0;
          ret = cli->ZRemRangeByIndex(finished_key, 0, -20, &cnt);
          if (ret == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
            LOG(INFO) << "zadd version " << task_info.data_version()
                      << " ts " << version << " for "
                      << task_info.task_name() << " remove " << cnt << " versions";
          }
        }
        break;
      default:
        running_task_size++;
        break;
    }
  }
  int weight = running_task_size > AdKconfUtil::minTaksSizePerPod() ? 0 : running_task_size;
  WeightedProducer wp(report_info.pod_info().pod_name(), weight);
  auto itr = weighted_producers_.find(wp);
  if (itr != weighted_producers_.end()) {
    weighted_producers_.erase(itr);
  }
  LOG(INFO) << log_prefix << "update weighted_producers_, pod_name="
            << wp.m_pod_name << ", weight=" << wp.m_weight;
  weighted_producers_.insert(wp);
}

void ControllerManager::RecordProducerInfo2Redis(::ks::infra::RedisClient* cli) {
  std::lock_guard<std::mutex> lock(mutex_);
  for (const auto& it : producer_info_) {
    std::string value;
    if (!it.second.SerializeToString(&value)) {
      continue;
    }
    int ret = cli->Set(it.first, value);
    if (ret != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
      LOG(WARNING) << log_prefix << "set key failed, key=" << it.first;
      continue;
    }
  }
}

void ControllerManager::UpdateAndDispatch(::ks::infra::RedisClient* cli,
                                          const std::set<std::string>& data_names) {
  int64_t now = base::GetTimestamp() / (1000 * 1000);
  std::vector<std::string> exist_versions, finished_versions;
  std::unordered_map<std::string, std::string> new_versions;
  // 更新待分发任务
  for (const auto& name : data_names) {
    exist_versions.clear();
    if (cli->ZGetMembersByScore("kfs_p2p_data_version_" + name, "0", std::to_string(now),
                                &exist_versions) != 0
        || exist_versions.empty()) {
      continue;
    }
    // 待处理队列里始终维护最新版本？
    LOG(INFO) << log_prefix << "wait_tasks, data_name=" << name << ", version=" << exist_versions.back();
    wait_tasks_[name] = exist_versions.back();
    // 清理已完成任务
    finished_versions.clear();
    if (cli->ZGetMembersByScore("fts_" + name, "0", std::to_string(now), &finished_versions) != 0
        || finished_versions.empty()) {
      continue;
    }
    running_tasks_.erase(GenerateCombineKey(name, finished_versions.back()));
  }

  // 分发任务到 producer
  std::unordered_map<std::string, std::string> dispatch_tasks;
  for (const auto& task : wait_tasks_) {
    auto itr = running_tasks_.find(GenerateCombineKey(task.first, task.second));
    if (itr == running_tasks_.end()) {
      auto dir = absl::Substitute("$0/$1/$2", kKfsPath, task.first, task.second);
      // 过滤预处理后的版本
      if (absl::EndsWith(dir, kCheckedSuffix)) {
        continue;
      }
      if (!base::file_util::PathExists(dir)) {
        continue;
      }
      dispatch_tasks.emplace(task);
    }
  }
  DispatchTask2Producer(dispatch_tasks);
}

void ControllerManager::DispatchTask2Producer(
  const std::unordered_map<std::string, std::string>& dispatch_tasks) {
  auto client = ks::ad_base::AdKessClient::ClientOfKey<kess::AdDataFormatService>(kProducerClientKey);
  std::lock_guard<std::mutex> lock(mutex_);
  for (const auto& task : dispatch_tasks) {
    std::string pod_name;
    if (GetPodName(&pod_name)) {
      // 调用 rpc 接口分发任务
      auto itr = producer_info_.find(pod_name);
      if (itr == producer_info_.end()) {
        LOG(WARNING) << log_prefix << "Invalid pod_name=" << pod_name;
        continue;
      }
      // 过滤 producer 不支持 task
      std::set<std::string> support_list(
        itr->second.support_task_list().begin(),
        itr->second.support_task_list().end());
      if (support_list.find(task.first) == support_list.end()) {
        LOG_EVERY_N(INFO, 100) << log_prefix << "unsupport task, task=" << task.first;
        continue;
      }
      DispatchTaskReq request;
      DispatchTaskResp response;
      LOG(INFO) << log_prefix << "Begin dispatch task"
                << absl::Substitute("[name=$0,version=$1,pod_name=$2,peer=$3]",
                                    task.first, task.second, pod_name, itr->second.pod_info().peer());
      GenerateDispatchRequest(task.first, task.second, &request);
      const auto options = ks::ad_base::OptionsFromMilli(client.first->time_out);
      auto grpc_status = client.second->SelectByDest(itr->second.pod_info().peer(), false)
                         ->DispatchTask(options, request, &response);
      if (!grpc_status.ok()) {
        LOG(ERROR) << "Request DispatchTask failed error_code: "
                   << grpc_status.error_code()
                   << ", error_message: " << grpc_status.error_message()
                   << ", request: " << request.DebugString();
        continue;
      }
      // 发送成功后加入队列
      running_tasks_.insert(GenerateCombineKey(task.first, task.second));
    }
  }
}

bool ControllerManager::GetPodName(std::string* pod_name) {
  if (weighted_producers_.empty()) {
    LOG(INFO) << log_prefix << "no valid producer";
    return false;
  }
  *pod_name = weighted_producers_.begin()->m_pod_name;
  return true;
}

void ControllerManager::DoTask() {
  while (!cw_.Stoped()) {
    auto* cli = ::ks::ad_base::KconfRedis::Instance().GetAdRedisClient(
    "adWarmupData", ks::engine_base::DependDataLevel::STRONG_DEPEND);
    if (cli == nullptr) {
      LOG(ERROR) << "client is nullptr";
      cw_.WaitForMillisecond(100*1000);
      continue;
    }
    ReloadProducerInfo();
    CleanExpireProducer();
    if (!ZkLeaderManager::Instance().IsLeader()) {
      cw_.WaitForMillisecond(10*1000);
      continue;
    }
    // step 1 获取数据全集
    std::set<std::string> data_names;
    int ret = cli->SGetMembers("all_p2p_data", &data_names);
    if (ret != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
      LOG(WARNING) << log_prefix << "get all_p2p_data_set failed, ret=" << ret;
      cw_.WaitForMillisecond(500);
      continue;
    }
    // step 2 分发任务
    UpdateAndDispatch(cli, data_names);
    // step 3 更新 producer 状态
    RecordProducerInfo2Redis(cli);
    // step 4 记录校验数据生产 lag
    ReportInfo(data_names);
    cw_.WaitForMillisecond(60*1000);
  }
  stop_ = true;
}

void ControllerManager::ReportInfo(const std::set<std::string>& all_datas) {
    auto* cli = ::ks::ad_base::KconfRedis::Instance().GetAdRedisClient(
    "adDataFormatProducer", ks::engine_base::DependDataLevel::WEAK_DEPEND);
  if (!cli) return;
  int64_t current = base::GetTimestamp();
  // 记录数据校验 lag
  for (const std::string& data : all_datas) {
    std::string redis_key = absl::Substitute("$0$1", DATA_CHECK_REDIS_PREFIX, data);
    std::string value;
    int ret = cli->Get(redis_key, &value);
    if (ret != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
      LOG(WARNING) << log_prefix << absl::Substitute("get producer info failed, redis_key=$0, err_code=$1",
                                                     redis_key, ret);
      dot_->Count(1, "not_produce", data);
      continue;
    }
    DataCheckRedisInfo pb;
    if (!pb.ParseFromString(value)) {
      LOG(WARNING) << log_prefix << absl::Substitute("parse pb failed, data=$0", data);
      dot_->Count(1, "parse_pb_failed", data);
      continue;
    }
    if (pb.version_meta_infos().empty()) {
      continue;
    }
    int64_t last_check_time = pb.version_meta_infos(0).timestamp();
    dot_->Interval((current - last_check_time) / 1000 / 1000, "produce_lag", data);
  }
}

void ControllerManager::GenerateDispatchRequest(
        const std::string& task_name,
        const std::string& data_version, DispatchTaskReq* req) {
  if (req) {
    auto* task_info = req->add_task_execution_infos();
    task_info->set_task_name(task_name);
    task_info->set_data_version(data_version);
  }
}

}  // namespace ad_data_format_server
}  // namespace ks
