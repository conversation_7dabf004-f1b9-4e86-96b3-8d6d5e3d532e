#pragma once

#include "teams/ad/ad_base/src/dot/dot.h"
#include "teams/ad/ad_data_format_server/proto/ad_data_format_server.kess.grpc.pb.h"

namespace ks {
namespace ad_data_format_server {
class ControllerService final : public ks::ad_data_format_server::kess::AdDataFormatService::Service {
 public:
  ControllerService() : dot_request(
    Product::UNKNOWN_PRODUCT,
    kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_UNKNOWN,
    kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_UNKNOWN_FROM,
    0), dot_response(
    Product::UNKNOWN_PRODUCT,
    kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_UNKNOWN,
    kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_UNKNOWN_FROM, 0) {}
  ::grpc::Status ReportTaskInfo(::grpc::ServerContext* context,
                                const ReportTaskInfoReq* req,
                                ReportTaskInfoResp* resp) override;
  ::grpc::Status RecoverTask(::grpc::ServerContext* context,
                             const RecoverTaskReq* req,
                             RecoverTaskResp* resp) override;

 private:
  ks::ad_base::Dot dot_request;
  ks::ad_base::Dot dot_response;
};
}  // namespace ad_data_format_server
}  // namespace ks
