syntax = "proto3";

package ks.ad_data_format_server;

enum TaskStatus {
  TS_INIT = 0;
  TS_RUNING = 1;
  TS_FINISHED = 2;
}

enum TaskErrorCode {
  TCC_EMPTY = 0;
  TCC_PARSE_FILE = 1;
  TCC_UPLOAD_DATA = 2;
  TCC_CHECK_DATA = 3;
}

message ServicePodInfo {
  string pod_name = 1;
  string peer = 2;  // 格式为IP:PORT
};

message TaskExecutionInfo {
	string task_name = 1;
  string data_version = 2;
  TaskStatus task_status = 3;
  TaskErrorCode task_error_code = 4;
  int64 timestamp = 5;
  int64 version = 6;
}

message ProducerInfo {
  ServicePodInfo pod_info = 1;  
  repeated TaskExecutionInfo task_infos = 2;
}

message ReportTaskInfoReq {
  repeated string support_task_list = 1;
  repeated TaskExecutionInfo task_execution_infos = 2;
  ServicePodInfo pod_info = 3;
}

message ReportTaskInfoResp {
  bool status = 1;
}

message RecoverTaskReq {
  ServicePodInfo pod_info = 1;
}

message RecoverTaskResp {
  repeated TaskExecutionInfo task_execution_infos = 1;
  bool status = 2;
}

message DispatchTaskReq {
	repeated TaskExecutionInfo task_execution_infos = 1;
}

message DispatchTaskResp {
  int64 ret_code = 1;
}

message DataVersionMetaInfo {
  string data_version = 1;
  int64 data_size = 2;
  repeated int64 all_keys = 3;
  int64 timestamp = 4;
}

message DataCheckRedisInfo {
  repeated DataVersionMetaInfo version_meta_infos = 1;
}

service AdDataFormatService {
  rpc ReportTaskInfo(ReportTaskInfoReq) returns (ReportTaskInfoResp);

  rpc RecoverTask(RecoverTaskReq) returns (RecoverTaskResp);

  rpc DispatchTask(DispatchTaskReq) returns (DispatchTaskResp);
}
