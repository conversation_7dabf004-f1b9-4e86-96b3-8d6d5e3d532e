#pragma once

#include <memory>
#include <mutex>
#include <string>

namespace ks {

namespace infra {
namespace zk {
class ZkClient;
}  // namespace zk
}  // namespace infra

namespace ad_data_format_server {

class ZkSimpleClient {
 public:
  explicit ZkSimpleClient(const std::string& dynamic_json_node = "zk_leader");
  ~ZkSimpleClient();

  bool GetNodeContent(std::string* content);
  bool IsValid();

 private:
  void ContentWatchCallback(int rc, const std::string& node_path,
                            const std::string& content);

 private:
  std::string zk_path_;
  std::string zk_host_;
  std::unique_ptr<ks::infra::zk::ZkClient> zk_client_;
  std::mutex node_content_mtx_;
  std::string node_content_;
  bool init_succ_{false};
};

}  // namespace ad_data_format_server
}  // namespace ks
