#include "teams/ad/ad_data_format_server/utils/zk_simple_client/zk_simple_client.h"

#include "ks/serving_util/dynamic_config.h"
#include "zk_client/zk_client_impl.hh"

namespace ks {
namespace ad_data_format_server {

ZkSimpleClient::ZkSimpleClient(const std::string& dynamic_json_node)
    : zk_client_(new ks::infra::zk::ZkClientImpl()) {
  auto json = DynamicJsonConfig::GetConfig()->Get(dynamic_json_node);
  if (json == nullptr) {
    LOG(ERROR) << "zk '" << dynamic_json_node
               << "' not present in dynamic json config";
    return;
  }
  zk_host_ = json->GetString("zk_host");
  if (zk_host_.empty()) {
    LOG(ERROR) << "zk '" << dynamic_json_node
               << ".zk_host' empty or not provided";
    return;
  }
  zk_path_ = json->GetString("zk_path");
  if (zk_path_.empty()) {
    LOG(ERROR) << "zk '" << dynamic_json_node
               << ".zk_path' empty or not provided";
  }

  if (!zk_client_->ContentWatch(
          zk_host_, zk_path_,
          std::bind(&ZkSimpleClient::ContentWatchCallback, this,
                    std::placeholders::_1, std::placeholders::_2,
                    std::placeholders::_3),
          this)) {
    LOG(ERROR) << "zk ContentWatch failed zk_path_ =" << zk_path_
               << " zk_host_ = " << zk_host_;
    return;
  }

  init_succ_ = true;
  LOG(INFO) << "zk_client init zk_path = " << zk_path_
            << " zk_host = " << zk_host_;
}

ZkSimpleClient::~ZkSimpleClient() {
  zk_client_.reset();
}

bool ZkSimpleClient::IsValid() { return init_succ_; }

bool ZkSimpleClient::GetNodeContent(std::string* content) {
  if (!IsValid()) {
    return false;
  }

  std::lock_guard<std::mutex> guard(node_content_mtx_);
  *content = node_content_;

  return true;
}

void ZkSimpleClient::ContentWatchCallback(int rc, const std::string& node_path,
                                          const std::string& content) {
  std::lock_guard<std::mutex> guard(node_content_mtx_);
  LOG(INFO) << "ContentWatchCallback old_node_content = " << node_content_
            << " new_content = " << content;
  node_content_ = content;
}

}  // namespace ad_data_format_server
}  // namespace ks
