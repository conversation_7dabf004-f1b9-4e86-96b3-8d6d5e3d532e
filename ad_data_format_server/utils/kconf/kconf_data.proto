syntax = "proto3";
package ks.ad_data_format_server.kconf;
option cc_enable_arenas = true;

message TestMsg {
  int64 id = 1;
}

message DataCheckConfig {
  message ThresholdItem {
    bool allow_empty_data = 1;
    double inc_key_diff_ratio = 2;
    double dec_key_diff_ratio = 3;
    double inc_size_diff_ratio = 4;
    double dec_size_diff_ratio = 5;
    int64 data_max_size = 6;
  }
  map<string, ThresholdItem> data_threshold_config_map = 1;
  ThresholdItem default_threshold_config = 2;
}

message StandardTasksConfig {
  repeated string standard_map_tasks = 1;
  repeated string standard_set_tasks = 2;
}