#pragma once

#include <string>

#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/ad_data_format_server/utils/kconf/kconf_data.pb.h"

namespace ks {
namespace ad_data_format_server {

using ks::ad_data_format_server::kconf::DataCheckConfig;
using ks::ad_data_format_server::kconf::StandardTasksConfig;

class AdKconfUtil {
 public:
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.dataPush, testDataJobName,
                                        "hosting_split_test_unit")
  DEFINE_INT32_KCONF_NODE(ad.dataPush, hdfsStorgeHours, 24)
  DEFINE_INT32_KCONF_NODE(ad.dataPush, cleanVersionLimits, 100)
  DEFINE_PROTOBUF_NODE_KCONF(DataCheckConfig, ad.dataPush, dataCheckConfig)
  DEFINE_BOOL_KCONF_NODE(ad.dataPush, isFormatProducer, true)
  DEFINE_INT32_KCONF_NODE(ad.dataPush, formatProducerThreadNum, 20)
  // producer 节点 30 分钟未发心跳，任务过期
  DEFINE_INT32_KCONF_NODE(ad.dataPush, producerExpireSeconds, 1800)
  DEFINE_INT32_KCONF_NODE(ad.dataPush, minTaksSizePerPod, 30)
  DEFINE_PROTOBUF_NODE_KCONF(StandardTasksConfig, ad.dataPush,
                             standardTasksConfig)
  DEFINE_SET_NODE_KCONF(std::string, ad.dataPush, taskBlackList)
  DEFINE_BOOL_KCONF_NODE(ad.dataPush, allowProducerDataCheck, false)
  DEFINE_BOOL_KCONF_NODE(ad.dataPush, allowSaveDataAllKeys, false)
};

}  // namespace ad_data_format_server
}  // namespace ks
