#include "teams/ad/ad_data_format_server/utils/zk/zk_leader_selector.h"

#include "absl/strings/substitute.h"
#include "absl/time/clock.h"

#include "teams/ad/ad_base/src/common/os_version.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "base/common/logging.h"
#include "base/common/sleep.h"
#include "serving_base/utility/system_util.h"

namespace ks {
namespace ad_data_format_server {

std::string ZkLeaderSelector::GenerateMyId() {
  return ks::ad_base::AdKessClient::Instance().Peer();
}

void ZkLeaderSelector::OnLeadTaken() {
  LOG(INFO) << "change to master";
}

void ZkLeaderSelector::OnLeadLost() {
  LOG(INFO) << "change to slave";
}

}  // namespace ad_data_format_server
}  // namespace ks
