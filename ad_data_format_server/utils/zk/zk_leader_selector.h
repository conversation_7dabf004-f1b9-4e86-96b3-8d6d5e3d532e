#pragma once
#include <atomic>
#include <string>
#include <memory>
#include "infra/falcon_counter/src/falcon/counter.h"
#include "serving_base/utility/system_util.h"
#include "teams/ad/ad_base/src/common/zk_leader_selector.h"
#include "teams/ad/ad_base/src/pattern/singleton.h"

namespace ks {
namespace ad_data_format_server {

using ks::ad_base::pattern::StaticSingleton;
using ZkLeadChangeFunc = std::function<void(bool)>;

class ZkLeaderSelector final
  : public ks::ad_base::ZkLeaderSelector,
    public StaticSingleton<ZkLeaderSelector> {
 protected:
  std::string GenerateMyId() override;
  // 自定义 非主 -> 主 的行为
  void OnLeadTaken() override;
  // 自定义 主 -> 非主 的行为
  void OnLeadLost() override;
};
}  // namespace ad_data_format_server
}  // namespace ks
