#pragma once
#include "base/common/basic_types.h"
#include "teams/ad/ad_data_format_server/utils/zk/zk_leader_selector.h"

namespace ks {
namespace ad_data_format_server {

class ZkLeaderManager {
 public:
  static ZkLeaderManager& Instance() {
    static ZkLeaderManager instance;
    return instance;
  }
  void Start() {
    ZkLeaderSelector::Instance().Start();
  }

  void Stop() {
    ZkLeaderSelector::Instance().Stop();
  }

  bool IsLeader() {
    // leader 控制逻辑请在这里添加
    return ZkLeaderSelector::Instance().IsLeader();
  }

 private:
  ZkLeaderManager() {}
  ~ZkLeaderManager() {}
  DISALLOW_COPY_AND_ASSIGN(ZkLeaderManager);
};

}  // namespace ad_data_format_server
}  // namespace ks
