#pragma once

#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/data_check_plugin_base.h"

namespace ks {
namespace ad_data_format_server {

class SizeDiffRatioCheckPlugin : public DataCheckPluginBase {
 public:
  SizeDiffRatioCheckPlugin();
  ~SizeDiffRatioCheckPlugin();

 protected:
  bool IsN<PERSON>(const DataCheckConfig::ThresholdItem& threshold_item,
              const DataCheckRedisInfo& data_check_redis_info) override;
  bool DoCheckData(const PluginParams& params) override;
};

}  // namespace ad_data_format_server
}  // namespace ks
