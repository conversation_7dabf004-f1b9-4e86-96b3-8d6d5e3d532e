#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/data_check_plugin_manager.h"

#include <memory>
#include <string>
#include <utility>

#include "perfutil/perfutil.h"
#include "absl/strings/str_cat.h"
#include "teams/ad/ad_base/src/dot/dot.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_data_format_server/proto/ad_data_format_server.pb.h"
#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/empty_check_plugin.h"
#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/key_diff_ratio_check_plugin.h"
#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/size_diff_ratio_check_plugin.h"
#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/data_max_size_check_plugin.h"

namespace ks {
namespace ad_data_format_server {

namespace {
static const char DATA_FORMAT_PRODUCER_REDIS_NAME[] = "adDataFormatProducer";
static const char DATA_CHECK_REDIS_PREFIX[] = "data_check:";
static const char CHECKED_DATA_SET_REDIS_KEY[] = "checked_data_set";

std::string NameOfDataCheckPluginType(
    DataCheckPluginType data_check_plugin_type) {
  std::string result;
  switch (data_check_plugin_type) {
    case DataCheckPluginType::Empty:
      result = "Empty";
      break;

    case DataCheckPluginType::KeyDiffRatio:
      result = "KeyDiffRatio";
      break;

    case DataCheckPluginType::SizeDiffRatio:
      result = "SizeDiffRatio";
      break;

    case DataCheckPluginType::MaxSize:
      result = "MaxSize";
      break;

    default:
      result = "Unknown";
  }

  return result;
}
}  // namespace

DataCheckPluginManager::~DataCheckPluginManager() {
  check_plugins_.clear();
}

DataCheckPluginManager::DataCheckPluginManager()
    : dot_(std::make_unique<ad_base::Dot>(0)) {
  check_plugins_.emplace_back(std::make_unique<EmptyCheckPlugin>());
  check_plugins_.emplace_back(std::make_unique<KeyDiffRatioCheckPlugin>());
  check_plugins_.emplace_back(std::make_unique<SizeDiffRatioCheckPlugin>());
  check_plugins_.emplace_back(std::make_unique<DataMaxSizeCheckPlugin>());
}

void DataCheckPluginManager::PostProc(
    const std::string& data_name, const std::string& old_data_version,
    const std::string& new_data_version,
    const DataCheckPluginType& failed_check_plugin_type,
    const DataStatisticsInfo& data_statistics_info) {
  std::string failed_check_plugin_type_name =
      NameOfDataCheckPluginType(failed_check_plugin_type);
  ::ks::infra::PerfUtil::GaugeLogStash(
      1, "ad.ad_data_format_server", "failed_check_plugin_type", data_name,
      failed_check_plugin_type_name, old_data_version, new_data_version);
  LOG(INFO) << data_name << " value check info failed_check_plugin_type = "
            << failed_check_plugin_type_name
            << " old_data_version = " << old_data_version
            << " new_data_version = " << new_data_version;
  auto perf_func = [&](const auto& num_info, const std::string& sub_tag) {
    if (num_info.second) {
      ::ks::infra::PerfUtil::GaugeLogStash(
          num_info.first, "ad.ad_data_format_server", sub_tag, data_name,
          new_data_version, old_data_version);
      LOG(INFO) << "data_statistics_info " << data_name << " " << sub_tag << " "
                << num_info.first;
    }
  };

  perf_func(data_statistics_info.old_data_key_num_info,
            "old_data_key_num_info");
  perf_func(data_statistics_info.new_data_key_num_info,
            "new_data_key_num_info");
  perf_func(data_statistics_info.common_key_num_info, "common_key_num_info");
  perf_func(data_statistics_info.new_data_size_info, "new_data_size_info");
  perf_func(data_statistics_info.old_data_size_info, "old_data_size_info");

  if (data_statistics_info.old_data_size_info.second > 0) {
    int size_diff_ratio =
        ((data_statistics_info.new_data_size_info.second -
          data_statistics_info.old_data_size_info.second) /
         static_cast<double>(data_statistics_info.old_data_size_info.second)) *
        1000;
    ::ks::infra::PerfUtil::GaugeLogStash(
        size_diff_ratio, "ad.ad_data_format_server", "data_size_diff_ratio",
        data_name, old_data_version, new_data_version);
  }
}

bool DataCheckPluginManager::GetDataCheckConfig(
    const std::string& data_name,
    DataCheckConfig::ThresholdItem* threshold_item) {
  auto& data_threshold_config_map =
      AdKconfUtil::dataCheckConfig()->data().data_threshold_config_map();
  auto threshold_iter = data_threshold_config_map.find(data_name);
  if (threshold_iter != data_threshold_config_map.end()) {
    *threshold_item = threshold_iter->second;
  } else {
    *threshold_item =
        AdKconfUtil::dataCheckConfig()->data().default_threshold_config();
  }

  return true;
}

void DataCheckPluginManager::ReadDataCheckRedisInfo(
    const std::string& data_name, DataCheckRedisInfo* data_check_redis_info) {
  auto redis_client = ks::ad_base::KconfRedis::Instance().GetAdRedisClient(
      DATA_FORMAT_PRODUCER_REDIS_NAME,
      ks::engine_base::DependDataLevel::WEAK_DEPEND);
  if (redis_client == nullptr) {
    LOG(ERROR) << "ReadDataCheckRedisInfo redis client failed "
               << DATA_FORMAT_PRODUCER_REDIS_NAME;
    dot_->Count(1, "read_data_check_redis_info", "redis client nullptr", data_name);
    return;
  }

  std::string key = absl::StrCat(DATA_CHECK_REDIS_PREFIX, data_name);
  std::string value;
  auto ret_code = redis_client->Get(key, &value);
  if (ret_code != ::ks::infra::KS_INF_REDIS_NO_ERROR) {
    LOG(ERROR) << "ReadDataCheckRedisInfo redis value failed " << data_name
               << ":" << ret_code;
    dot_->Count(1, "read_data_check_redis_info", "get_value_error", data_name,
                std::to_string(ret_code));
    return;
  }

  if (value.empty()) {
    LOG(ERROR) << "ReadDataCheckRedisInfo redis value empty " << data_name;
    dot_->Count(1, "read_data_check_redis_info", "value_empty", data_name);
    return;
  }

  DataCheckRedisInfo temp_redis_info;
  if (!temp_redis_info.ParseFromString(value)) {
    LOG(ERROR) << "ReadDataCheckRedisInfo parse from string error " << data_name;
    dot_->Count(1, "read_data_check_redis_info", "parse_error", data_name);
    return;
  }

  auto& version_meta_infos = temp_redis_info.version_meta_infos();
  if (version_meta_infos.empty() ||
      version_meta_infos[0].data_version().empty() ||
      version_meta_infos[0].data_size() == 0) {
    dot_->Count(1, "read_data_check_redis_info", "version_meta_infos invalid",
                data_name);
    return;
  }

  dot_->Count(1, "read_data_check_redis_info", "succ", data_name);
  dot_->Interval(value.size(), "read_data_check_redis_info", "value_size",
                 data_name);
  data_check_redis_info->Swap(&temp_redis_info);
}

bool DataCheckPluginManager::WriteDataCheckRedisInfo(
    const std::string& data_name,
    const DataCheckRedisInfo& data_check_redis_info) {
  std::string error_msg;
  uint64_t value_size = 0;
  do {
    auto redis_client = ks::ad_base::KconfRedis::Instance().GetAdRedisClient(
        DATA_FORMAT_PRODUCER_REDIS_NAME,
        ks::engine_base::DependDataLevel::WEAK_DEPEND);

    if (redis_client == nullptr) {
      error_msg = "redis_client_null";
      break;
    }

    std::string value;
    if (!data_check_redis_info.SerializeToString(&value)) {
      error_msg = "protobuf serialze string error";
      break;
    }

    value_size = value.size();

    std::string key = absl::StrCat(DATA_CHECK_REDIS_PREFIX, data_name);
    if (auto ret_code = redis_client->Set(key, value);
        ret_code != ::ks::infra::KS_INF_REDIS_NO_ERROR) {
      error_msg = "write redis error " + std::to_string(ret_code);
      break;
    }

    if (auto ret_code =
            redis_client->SAdd(CHECKED_DATA_SET_REDIS_KEY, data_name);
        ret_code != ::ks::infra::KS_INF_REDIS_NO_ERROR) {
      error_msg = "write redis data_set error" + std::to_string(ret_code);
      break;
    }
  } while (false);

  if (!error_msg.empty()) {
    dot_->Count(1, "write_data_check_redis_info", error_msg, data_name);
    LOG(ERROR) << "write_data_check_redis_info error_msg: " << error_msg
               << " data_name:" << data_name << " value_size: " << value_size;
    return false;
  }

  dot_->Count(1, "write_data_check_redis_info", "succ", data_name);
  dot_->Set(value_size, "write_data_check_redis_info", "value_size", data_name);

  auto meta_info = DataCheckHelper::GetVersionMetaInfo(data_check_redis_info);
  if (meta_info != nullptr) {
    LOG(INFO) << "write_data_check_redis_info:  data_name=" << data_name
              << " data_size = " << meta_info->data_size()
              << "all_keys_size = " << meta_info->all_keys_size();
  }

  return true;
}

}  // namespace ad_data_format_server
}  // namespace ks
