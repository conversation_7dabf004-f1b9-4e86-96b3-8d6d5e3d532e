#pragma once

#include <algorithm>
#include <cstdint>
#include <functional>
#include <string>
#include <utility>

#include "teams/ad/ad_data_format_server/proto/ad_data_format_server.pb.h"
#include "teams/ad/ad_data_format_server/utils/kconf/kconf.h"

namespace ks {
namespace ad_data_format_server {

enum class DataCheckPluginType {
  Unknown = 0,
  Empty = 1,
  KeyDiffRatio = 2,
  SizeDiffRatio = 3,
  MaxSize = 4,
};

struct DataStatisticsInfo {
  std::pair<uint32_t, bool> old_data_key_num_info{0, false};
  std::pair<uint32_t, bool> new_data_key_num_info{0, false};
  std::pair<uint32_t, bool> common_key_num_info{0, false};
  std::pair<uint64_t, bool> new_data_size_info{0, false};
  std::pair<uint64_t, bool> old_data_size_info{0, false};
};

struct PluginParams {
  const std::string* data_name = nullptr;
  const DataCheckConfig::ThresholdItem* threshold_item = nullptr;
  const DataCheckRedisInfo* data_check_redis_info = nullptr;
  std::function<uint32_t()> common_key_num_func;
  std::function<uint32_t()> old_data_key_num_func;
  std::function<uint32_t()> new_data_key_num_func;
  std::function<uint64_t()> new_data_size_func;
  std::function<uint64_t()> old_data_size_func;
};

class DataCheckPluginBase {
 public:
  explicit DataCheckPluginBase(DataCheckPluginType plugin_type)
      : plugin_type_(plugin_type) {}
  virtual ~DataCheckPluginBase() {}

  bool CheckData(const PluginParams& plugin_params,
                 DataStatisticsInfo* data_statistics_info) {
    if (!IsNeed(*plugin_params.threshold_item,
                *plugin_params.data_check_redis_info)) {
      return true;
    }

    return DoCheckData(plugin_params);
  }

  DataCheckPluginType plugin_type() const { return plugin_type_; }

 protected:
  virtual bool IsNeed(const DataCheckConfig::ThresholdItem& threshold_item,
                      const DataCheckRedisInfo& data_check_redis_info) = 0;
  virtual bool DoCheckData(const PluginParams& params) = 0;

 protected:
  DataCheckPluginType plugin_type_;
};

}  // namespace ad_data_format_server
}  // namespace ks
