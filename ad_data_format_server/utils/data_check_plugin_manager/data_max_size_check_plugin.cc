#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/data_max_size_check_plugin.h"

namespace ks {
namespace ad_data_format_server {

DataMaxSizeCheckPlugin::DataMaxSizeCheckPlugin()
    : DataCheckPluginBase(DataCheckPluginType::MaxSize) {}

DataMaxSizeCheckPlugin::~DataMaxSizeCheckPlugin() {
}

bool DataMaxSizeCheckPlugin::IsNeed(
    const DataCheckConfig::ThresholdItem& threshold_item,
    const DataCheckRedisInfo& data_check_redis_info) {
  return threshold_item.data_max_size() > 0;
}

bool DataMaxSizeCheckPlugin::DoCheckData(const PluginParams& params) {
  return params.new_data_size_func() < params.threshold_item->data_max_size();
}

}  // namespace ad_data_format_server
}  // namespace ks
