#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/key_diff_ratio_check_plugin.h"

#include <cmath>

namespace ks {
namespace ad_data_format_server {

KeyDiffRatioCheckPlugin::KeyDiffRatioCheckPlugin()
    : DataCheckPluginBase(DataCheckPluginType::KeyDiffRatio) {}

KeyDiffRatioCheckPlugin::~KeyDiffRatioCheckPlugin() {
}

bool KeyDiffRatioCheckPlugin::IsNeed(
    const DataCheckConfig::ThresholdItem& threshold_item,
    const DataCheckRedisInfo& data_check_redis_info) {
  return (threshold_item.inc_key_diff_ratio() > 0 ||
          threshold_item.dec_key_diff_ratio() > 0) &&
         (!data_check_redis_info.version_meta_infos().empty() &&
          !data_check_redis_info.version_meta_infos(0).all_keys().empty());
}

bool KeyDiffRatioCheckPlugin::DoCheckData(const PluginParams& params) {
  uint32_t old_data_key_num = params.old_data_key_num_func();
  if (old_data_key_num == 0) {
    return true;
  }

  uint32_t new_data_key_num = params.new_data_key_num_func();
  uint32_t common_key_num = params.common_key_num_func();
  double key_diff_ratio = fabs((new_data_key_num - common_key_num) /
                               static_cast<double>(old_data_key_num));

  if (new_data_key_num > old_data_key_num &&
      params.threshold_item->inc_key_diff_ratio() > 0) {
    return key_diff_ratio < params.threshold_item->inc_key_diff_ratio();
  }

  if (new_data_key_num <= old_data_key_num &&
      params.threshold_item->dec_key_diff_ratio() < 0) {
    return key_diff_ratio < params.threshold_item->dec_key_diff_ratio();
  }

  return true;
}

}  // namespace ad_data_format_server
}  // namespace ks
