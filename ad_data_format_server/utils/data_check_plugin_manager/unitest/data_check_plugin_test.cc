#include <cstdint>
#include <unordered_map>
#include <utility>
#include "base/testing/gtest.h"

#include "teams/ad/ad_data_format_server/proto/ad_data_format_server.pb.h"
#include "teams/ad/ad_data_format_server/utils/kconf/kconf_data.pb.h"
#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/data_check_helper.h"
#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/empty_check_plugin.h"
#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/key_diff_ratio_check_plugin.h"
#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/size_diff_ratio_check_plugin.h"

#define private public
#define protected public
#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/data_check_plugin_manager.h"
#undef private
#undef protected

namespace ks {
namespace ad_data_format_server {

namespace {
template<typename T>
uint64_t CalDataSize(const T& data_map) {
  uint64_t data_size = 0;
  for (auto& iter : data_map) {
    data_size += iter.first.size();
    data_size += iter.second.size();
  }

  return data_size;
}
}  // namespace

TEST(DataCheckPlugin, common) {
  EmptyCheckPlugin empty_check_plugin;
  KeyDiffRatioCheckPlugin key_diff_ratio_check_plugin;
  SizeDiffRatioCheckPlugin size_diff_ratio_check_plugin;

  DataCheckRedisInfo data_check_redis_info;
  auto version_meta_info = data_check_redis_info.add_version_meta_infos();
  version_meta_info->set_data_version("2022071118");
  version_meta_info->set_data_size(200);
  for (int i = 1; i <= 10; ++i) {
    version_meta_info->add_all_keys(i);
  }

  DataCheckConfig::ThresholdItem threshold_item;
  threshold_item.set_inc_key_diff_ratio(2.0);
  threshold_item.set_dec_key_diff_ratio(0.5);
  threshold_item.set_inc_size_diff_ratio(2.0);
  threshold_item.set_dec_size_diff_ratio(0.5);

  std::unordered_map<std::string, std::string> empty_test_data;
  std::unordered_map<std::string, std::string> true_test_data;
  for (int i = 6; i < 15; ++i) {
    true_test_data.insert(
        std::make_pair(std::to_string(i), std::to_string(i) + "_data"));
  }

  std::unordered_map<std::string, std::string> failed_diff_test_data;
  for (int i = 6; i <= 15; ++i) {
    failed_diff_test_data.insert(
        std::make_pair(std::to_string(i), std::to_string(i) + "_data"));
  }

  auto check_func = [&](auto& plugin, auto& data) {
    DataStatisticsInfo data_statistics_info;
    data_statistics_info.new_data_size_info =
        std::make_pair(CalDataSize(data), true);
    std::unordered_set<int64_t> data_keys;
    DataCheckHelper::GetAllKeysWithFunc(data, [&](const std::string& key) {
      data_keys.insert(std::hash<std::string>()(key));
    });

    PluginParams plugin_params;
    DataCheckPluginManager::Instance().InitPluginParams(
        data_check_redis_info, data_keys, "test_data", threshold_item,
        &data_statistics_info, &plugin_params);
    return plugin.CheckData(plugin_params, &data_statistics_info);
  };

  std::unordered_map<std::string, std::string> value_diff_test_data;
  value_diff_test_data["0"] = std::string(200, '0');

  EXPECT_FALSE(check_func(empty_check_plugin, empty_test_data));
  EXPECT_TRUE(check_func(empty_check_plugin, true_test_data));
  EXPECT_TRUE(check_func(key_diff_ratio_check_plugin, true_test_data));
  EXPECT_FALSE(check_func(key_diff_ratio_check_plugin, failed_diff_test_data));
  EXPECT_FALSE(check_func(size_diff_ratio_check_plugin, empty_test_data));
  EXPECT_TRUE(check_func(size_diff_ratio_check_plugin, value_diff_test_data));
}

}  // namespace ad_data_format_server
}  // namespace ks

int main(int argc, char **argv) {
  ::testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
}
