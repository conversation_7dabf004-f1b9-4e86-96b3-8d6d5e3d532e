#pragma once

#include <cstddef>
#include <cstdint>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "base/common/sleep.h"
#include "base/time/timestamp.h"
#include "teams/ad/ad_data_format_server/proto/ad_data_format_server.pb.h"
#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/data_check_helper.h"
#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/data_check_plugin_base.h"
#include "teams/ad/ad_data_format_server/utils/kconf/kconf_data.pb.h"

namespace ks {
namespace ad_base {
class Dot;
}

namespace ad_data_format_server {

class DataCheckPluginManager {
 public:
  static DataCheckPluginManager& Instance() {
    static DataCheckPluginManager instance_;
    return instance_;
  }
  ~DataCheckPluginManager();

  template <typename VALUE_TYPE>
  bool CheckData(const VALUE_TYPE& new_data,
                 const std::string& new_data_version,
                 const std::string& data_name, uint64_t data_size,
                 bool* can_commit_reids_info);

  template <typename VALUE_TYPE>
  bool CommitDataCheckRedisInfo(const VALUE_TYPE& new_data,
                                const std::string& new_data_version,
                                const std::string& data_name,
                                uint64_t data_size);

 private:
  DataCheckPluginManager();
  template <typename VALUE_TYPE>
  void InitPluginParams(const DataCheckRedisInfo& data_check_redis_info,
                        const VALUE_TYPE& new_data,
                        const std::string& data_name,
                        const DataCheckConfig::ThresholdItem& threshold_item,
                        DataStatisticsInfo* data_statistics_info,
                        PluginParams* plugin_params);
  void PostProc(const std::string& data_name,
                const std::string& old_data_version,
                const std::string& new_data_version,
                const DataCheckPluginType& failed_check_plugin_type,
                const DataStatisticsInfo& data_statistics_info);
  bool GetDataCheckConfig(const std::string& data_name,
                          DataCheckConfig::ThresholdItem* threshold_item);
  void ReadDataCheckRedisInfo(const std::string& data_name,
                              DataCheckRedisInfo* data_check_redis_info);
  bool WriteDataCheckRedisInfo(const std::string& data_name,
                               const DataCheckRedisInfo& data_check_redis_info);
  std::vector<std::unique_ptr<DataCheckPluginBase>> check_plugins_;
  std::unique_ptr<ad_base::Dot> dot_;
};

template <typename VALUE_TYPE>
bool DataCheckPluginManager::CheckData(const VALUE_TYPE& new_data,
                                       const std::string& new_data_version,
                                       const std::string& data_name,
                                       const uint64_t data_size,
                                       bool *can_commit_reids_info) {
  DataStatisticsInfo data_statistics_info;
  data_statistics_info.new_data_size_info = std::make_pair(data_size, true);
  DataCheckPluginType faild_check_plugin_type{DataCheckPluginType::Unknown};
  DataCheckConfig::ThresholdItem threshold_item;
  if (!GetDataCheckConfig(data_name, &threshold_item)) {
    return true;
  }

  DataCheckRedisInfo data_check_redis_info;
  ReadDataCheckRedisInfo(data_name, &data_check_redis_info);

  PluginParams plugin_params;
  InitPluginParams(data_check_redis_info, new_data, data_name, threshold_item,
                   &data_statistics_info, &plugin_params);

  for (auto& iter : check_plugins_) {
    if (iter && !iter->CheckData(plugin_params, &data_statistics_info)) {
      faild_check_plugin_type = iter->plugin_type();
      break;
    }
  }

  std::string old_data_version;
  auto* version_meta_info =
      DataCheckHelper::GetVersionMetaInfo(data_check_redis_info);
  if (version_meta_info != nullptr) {
    old_data_version = version_meta_info->data_version();
  }

  PostProc(data_name, old_data_version, new_data_version,
           faild_check_plugin_type, data_statistics_info);

  if (!old_data_version.empty() && old_data_version > new_data_version) {
    *can_commit_reids_info = false;
  }

  return faild_check_plugin_type == DataCheckPluginType::Unknown;
}

template <typename VALUE_TYPE>
bool DataCheckPluginManager::CommitDataCheckRedisInfo(
    const VALUE_TYPE& new_data, const std::string& new_data_version,
    const std::string& data_name, uint64_t data_size) {
  DataCheckRedisInfo data_check_redis_info;
  auto* version_check_info = data_check_redis_info.add_version_meta_infos();
  version_check_info->set_data_version(new_data_version);
  version_check_info->set_data_size(data_size);
  version_check_info->set_timestamp(base::GetTimestamp());

  if (AdKconfUtil::allowSaveDataAllKeys()) {
    version_check_info->mutable_all_keys()->Add(new_data.begin(),
                                                new_data.end());
  }

  int retry = 3;
  bool rst = false;
  while (retry--) {
    rst = WriteDataCheckRedisInfo(data_name, data_check_redis_info);
    if (rst) {
      break;
    }
    base::SleepForMilliseconds(10000);
  }

  return rst;
}

template <typename VALUE_TYPE>
void DataCheckPluginManager::InitPluginParams(
    const DataCheckRedisInfo& data_check_redis_info, const VALUE_TYPE& new_data,
    const std::string& data_name,
    const DataCheckConfig::ThresholdItem& threshold_item,
    DataStatisticsInfo* data_statistics_info, PluginParams* plugin_params) {
  plugin_params->data_name = &data_name;
  plugin_params->threshold_item = &threshold_item;
  plugin_params->data_check_redis_info = &data_check_redis_info;
  auto* version_meta_info =
      DataCheckHelper::GetVersionMetaInfo(data_check_redis_info);
  if (version_meta_info != nullptr) {
    data_statistics_info->old_data_size_info =
        std::make_pair(version_meta_info->data_size(), true);
  }

  plugin_params->common_key_num_func = [&]() {
    auto& common_key_num_info = data_statistics_info->common_key_num_info;
    if (common_key_num_info.second == false) {
      uint32_t common_key_num = 0;
      if (version_meta_info != nullptr) {
        std::for_each(version_meta_info->all_keys().begin(),
                      version_meta_info->all_keys().end(),
                      [&](const auto& key) {
                        if (new_data.count(key) != 0) {
                          ++common_key_num;
                        }
                      });
      }

      common_key_num_info = std::make_pair(common_key_num, true);
    }
    return common_key_num_info.first;
  };

  plugin_params->old_data_key_num_func = [&]() {
    auto& old_data_key_num_info = data_statistics_info->old_data_key_num_info;
    if (old_data_key_num_info.second == false) {
      uint32_t old_data_key_size = 0;
      if (version_meta_info != nullptr) {
        old_data_key_size = version_meta_info->all_keys_size();
      }
      old_data_key_num_info = std::make_pair(old_data_key_size, true);
    }
    return old_data_key_num_info.first;
  };

  plugin_params->new_data_key_num_func = [&]() {
    auto& new_data_key_num_info = data_statistics_info->new_data_key_num_info;
    if (new_data_key_num_info.second == false) {
      new_data_key_num_info = std::make_pair(new_data.size(), true);
    }

    return new_data_key_num_info.first;
  };

  plugin_params->new_data_size_func = [&]() {
    return data_statistics_info->new_data_size_info.first;
  };

  plugin_params->old_data_size_func = [&]() {
    auto& old_data_size_info = data_statistics_info->old_data_size_info;
    if (old_data_size_info.second == false) {
      uint64_t old_data_size = 0;
      if (version_meta_info != nullptr) {
        old_data_size = version_meta_info->data_size();
      }

      old_data_size_info = std::make_pair(old_data_size, true);
    }

    return old_data_size_info.first;
  };
}

}  // namespace ad_data_format_server
}  // namespace ks
