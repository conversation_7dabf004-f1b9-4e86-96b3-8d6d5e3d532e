#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/size_diff_ratio_check_plugin.h"

namespace ks {
namespace ad_data_format_server {

SizeDiffRatioCheckPlugin::SizeDiffRatioCheckPlugin()
    : DataCheckPluginBase(DataCheckPluginType::SizeDiffRatio) {}

SizeDiffRatioCheckPlugin::~SizeDiffRatioCheckPlugin() {
}

bool SizeDiffRatioCheckPlugin::IsNeed(
    const DataCheckConfig::ThresholdItem& threshold_item,
    const DataCheckRedisInfo& data_check_redis_info) {
  return (threshold_item.inc_size_diff_ratio() > 0 ||
          threshold_item.dec_size_diff_ratio()) &&
         (!data_check_redis_info.version_meta_infos().empty() &&
          data_check_redis_info.version_meta_infos(0).data_size() > 0);
}

bool SizeDiffRatioCheckPlugin::DoCheckData(const PluginParams& params) {
  uint64_t old_data_size = 0;
  old_data_size = params.old_data_size_func();
  if (old_data_size == 0) {
    return true;
  }

  uint64_t new_data_size = params.new_data_size_func();
  double diff_ratio = fabs((new_data_size - old_data_size) /
                           static_cast<double>(old_data_size));
  if (new_data_size > old_data_size &&
      params.threshold_item->inc_size_diff_ratio() > 0) {
    return diff_ratio < params.threshold_item->inc_size_diff_ratio();
  }

  if (new_data_size <= old_data_size &&
      params.threshold_item->dec_size_diff_ratio() > 0) {
    return diff_ratio < params.threshold_item->dec_size_diff_ratio();
  }

  return true;
}

}  // namespace ad_data_format_server
}  // namespace ks
