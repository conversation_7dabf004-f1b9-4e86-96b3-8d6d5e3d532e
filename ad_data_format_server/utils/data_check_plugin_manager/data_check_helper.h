#pragma once

#include <type_traits>
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader_type_helper.h"
#include "teams/ad/ad_data_format_server/proto//ad_data_format_server.pb.h"

namespace ks {
namespace ad_data_format_server {

namespace DataCheckHelper {

template <typename T, typename U,
          std::enable_if_t<ks::ad_base::is_map_v<T>, int> = 0>
void GetAllKeysWithFunc(const T& data, U func) {
  for (auto& iter : data) {
    func(iter.first);
  }
}

template <typename T, typename U,
          std::enable_if_t<ks::ad_base::is_set<T>::value, int> = 0>
void GetAllKeysWithFunc(const T& data, U func) {
  for (auto& iter : data) {
    func(iter);
  }
}

inline const DataVersionMetaInfo* GetVersionMetaInfo(
    const DataCheckRedisInfo& data_check_redis_info) {
  if (data_check_redis_info.version_meta_infos().empty()) {
    return nullptr;
  }

  return &data_check_redis_info.version_meta_infos(0);
}

}  // namespace DataCheckHelper

}  // namespace ad_data_format_server
}  // namespace ks
