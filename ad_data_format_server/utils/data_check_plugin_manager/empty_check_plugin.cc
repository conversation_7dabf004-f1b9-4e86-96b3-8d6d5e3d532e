#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/empty_check_plugin.h"

namespace ks {
namespace ad_data_format_server {

EmptyCheckPlugin::EmptyCheckPlugin()
    : DataCheckPluginBase(DataCheckPluginType::Empty) {}

EmptyCheckPlugin::~EmptyCheckPlugin() {
}

bool EmptyCheckPlugin::IsNeed(
    const DataCheckConfig::ThresholdItem& threshold_item,
    const DataCheckRedisInfo& data_check_redis_info) {
  return !threshold_item.allow_empty_data();
}

bool EmptyCheckPlugin::DoCheckData(const PluginParams& params) {
  return params.new_data_key_num_func() > 0;
}

}  // namespace ad_data_format_server
}  // namespace ks
