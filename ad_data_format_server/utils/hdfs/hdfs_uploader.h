#pragma once

#include <memory>
#include <mutex>
#include <string>
#include <map>
#include <vector>
#include <utility>
#include <fstream>
#include <iostream>
#include "absl/strings/str_split.h"
#include "absl/strings/str_join.h"
#include "base/common/closure.h"
#include "base/file/file_util.h"
#include "base/thread/thread_pool.h"
#include "base/time/timestamp.h"
#include "perfutil/perfutil.h"
#include "serving_base/util/hdfs.h"
#include "serving_base/hdfs_read/hdfs_file_util.h"
#include "serving_base/hdfs_read/hdfs_handle.h"
namespace ks {
namespace ad_data_format_server {
using ks::infra::PerfUtil;

class HdfsUploader {
 public:
  void SetUploadThreadPool(thread::ThreadPool* pool) {
    upload_pool_ = pool;
  }

  void AddUploadTask(std::string local_file, std::string hdfs_path) {
    if (!upload_pool_) {
      return;
    }

    std::lock_guard<std::mutex> guard(lock_);
    upload_pool_->AddTask(::NewCallback(this, &HdfsUploader::PutFileToHdfs, local_file, hdfs_path));
  }

  void PutFileToHdfs(std::string local_file, std::string hdfs_path) {
    int start = time(nullptr);
    std::string hdfs_file;
    size_t pos = local_file.rfind('/');
    if (pos == local_file.npos) {
      hdfs_file = hdfs_path + "/" + local_file;
    } else {
      hdfs_file = hdfs_path + "/" + local_file.substr(pos + 1);
    }

    if (hadoop::HDFSExists(hdfs_file.c_str())) {
      LOG(INFO) << "dst hdfs_file: " << hdfs_file << " has been exists, first remove it";
      if (!hadoop::HDFSRmr(hdfs_file.c_str())) {
        LOG(ERROR) << "hadoop::HDFSRmr(" << hdfs_file << ") failed.";
      }
    }
    LOG(INFO) << "start HDFSPut| " << local_file << " -> " << hdfs_file;
    int ret = hadoop::HDFSPut(local_file.c_str(), hdfs_file.c_str());
    if (ret == 0) {
      LOG(INFO) << "finish HDFSPut| " << local_file << " -> " << hdfs_file
                << " success. cost: " << (time(nullptr) - start) << "s";
    } else {
      LOG(ERROR) << "finish HDFSPut| " << local_file << " -> " << hdfs_file
                 << " failed. ret: " << ret;
    }
  }

  void TouchSuccess(const std::string& hdfs_path) {
    std::string flag_file = hdfs_path + "/_SUCCESS";
    if (!hadoop::HDFSExists(flag_file.c_str())) {
      if (hdfs_.Touch(flag_file)) {
        LOG(INFO) << "hdfs_.Touch(" << flag_file << ") success.";
      } else {
        LOG(ERROR) << "hdfs_.Touch(" << flag_file << ") failed.";
      }
    }
  }

  bool Mkdir(const std::string& hdfs_path) {
    if (!hadoop::HDFSExists(hdfs_path.c_str())) {
      if (!hadoop::HDFSMkdir(hdfs_path.c_str())) {
        LOG(ERROR) << "hadoop::HDFSMkdir(" << hdfs_path << ") failed.";
        return false;
      }
    }
    return true;
  }

  HdfsUploader() : hdfs_(hadoop::FLAGS_hadoop_namenode_ip,
      hadoop::FLAGS_hadoop_namenode_port, "ad") {}
  ~HdfsUploader() = default;

 private:
  bool enable_upload_ {false};
  thread::ThreadPool* upload_pool_ {nullptr};

  std::mutex lock_;

  base::HDFSWrapper hdfs_;
};

}  // namespace ad_data_format_server
}  // namespace ks
