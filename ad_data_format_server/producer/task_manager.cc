#include "teams/ad/ad_data_format_server/producer/task_manager.h"

#include <memory>
#include <unordered_map>
#include <utility>

#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_data_format_server/producer/task_base/customized_map_task_base.h"
#include "teams/ad/ad_data_format_server/producer/task_base/standard_map_task.h"
#include "teams/ad/ad_data_format_server/producer/task_base/standard_set_task.h"
#include "teams/ad/ad_data_format_server/proto/ad_data_format_server.kess.grpc.pb.h"
#include "teams/ad/ad_data_format_server/proto/ad_data_format_server.pb.h"
#include "teams/ad/ad_data_format_server/utils/kconf/kconf.h"
#include "teams/ad/ad_data_format_server/utils/zk_simple_client/zk_simple_client.h"
#include "teams/ad/engine_base/utils/deploy_variable.h"
#include "zk_client/zk_client_impl.hh"

namespace ks {
namespace ad_data_format_server {

namespace {

static const char kControllerClientKey[] = "ad_data_format_controller_client";

template <typename TaskList>
void StartTaskList(const TaskList& task_list) {
  std::for_each(task_list.begin(), task_list.end(),
                [](auto& task) { task->Start(); });
}

template <typename TaskList>
void StopTaskList(const TaskList& task_list) {
  std::for_each(task_list.begin(), task_list.end(),
                [](auto& task) { task->Stop(); });
}

template <typename TaskMap>
bool FindTask(const TaskMap& task_map, const std::string& task_name,
    TaskExecutionUnit *exec) {
  auto iter = task_map.find(task_name);
  if (iter == task_map.end()) {
    return false;
  }
  auto &task = iter->second;
  task->Clear();
  task->SetDataVersion(exec->data_version);
  exec->execute_func = [&task]() { task->Run(); };
  exec->task_status_func = [&task]() {
    return task->GetTaskStatus();
  };
  exec->task_error_code_func = [&task]() {
    return task->GetTaskErrorCode();
  };
  exec->task_start_time_func = [&task]() {
    return task->GetTaskStartTime();
  };
  exec->task_clear_func = [&task]() {
    return task->Clear();
  };
  return true;
}

template <typename TaskMap, typename Condition>
bool RemoveTaskIf(const std::string& task_name, const Condition& condition,
                  TaskMap* task_map) {
  auto iter = task_map->find(task_name);
  if (iter == task_map->end()) {
    return false;
  }

  if (!condition(iter)) {
    return false;
  }

  task_map->erase(iter);
  return true;
}

void ConvertToTaskExecutionInfo(const TaskExecutionUnit& task_unit,
                                TaskExecutionInfo* task_info) {
  task_info->set_task_name(task_unit.task_name);
  task_info->set_data_version(task_unit.data_version);
  task_info->set_task_status(task_unit.task_status_func());
  task_info->set_task_error_code(task_unit.task_error_code_func());
}

}  // namespace

bool TaskManager::AddData(const std::string& task_name, TaskType type) {
  auto ret = register_task_type_map_.emplace(task_name, type);
  if (!ret.second) {
    LOG(FATAL) << "task_name " << task_name << " already exists, ignore type " << int(type);
  }
  return ret.second;
}

void TaskManager::RegisterTask(std::unique_ptr<StandardMapTask>&& task) {
  if (AddData(task->GetTaskName(), TaskType::STANDARD_MAP_TASK))
    standard_map_task_map_.emplace(task->GetTaskName(), std::move(task));
}

void TaskManager::RegisterTask(std::unique_ptr<StandardSetTask>&& task) {
  if (AddData(task->GetTaskName(), TaskType::STANDARD_SET_TASK))
    standard_set_task_map_.emplace(task->GetTaskName(), std::move(task));
}

void TaskManager::RegisterTask(std::unique_ptr<CustomizedMapTaskBase>&& task) {
  if (AddData(task->GetTaskName(), TaskType::CUSTOMIZED_MAP_TASK))
    customized_map_task_map_.emplace(task->GetTaskName(), std::move(task));
}

void TaskManager::DispatchTask(const DispatchTaskReq& request,
                               DispatchTaskResp* response) {
  int failed_count = 0, succ_count = 0;
  for (int i = 0; i != request.task_execution_infos_size(); ++i) {
    auto& task = request.task_execution_infos(i);
    if (task.task_name().empty() || task.data_version().empty()) {
      continue;
      ++failed_count;
    }

    bool rst = dispatch_task_queue_.put(task);
    if (!rst) {
      LOG(ERROR) << "Dispatch Task Error: task_name = " << task.task_name()
                 << " data_version = " << task.data_version();
      ++failed_count;
      continue;
    }

    dot_->Count(1, "dispatch_task", task.task_name(), task.data_version());
    ++succ_count;
  }

  dot_->Count(failed_count, "dispatch_task_count", "failed");
  dot_->Count(succ_count, "dispatch_task_count", "succ");
  LOG(INFO) << "DispatchTask succ_count: " << succ_count
            << " failed_count: " << failed_count
            << " request: " << request.DebugString();
}

void TaskManager::StopPrepare() {
  stop_flag.store(true);
  if (thread_pool_)
    thread_pool_->JoinAll();
}

void TaskManager::DoTaskImpl() {
  if (!RecoverTask() || stop_flag.load()) {
    LOG(INFO) << "Waiting RecoverTask!!!";
    return;
  }
  if (!thread_pool_) {
    LOG(ERROR) << "No ThreadPool for TaskManager!!!";
    return;
  }

  TaskExecutionInfo task_execution_info;
  while (dispatch_task_queue_.get(task_execution_info)) {
    AddTaskToExecuteList(task_execution_info);
  }

  ReportTaskInfo();
  RemoveFinishedTask();
  StatisticsTaskInfos();
  DynamicAdjustStandardTask();
}

bool TaskManager::RecoverTask() {
  if (!already_recover_) {
    RecoverTaskResp response;
    if (GetRecoverTaskResponse(&response)) {
      for (int i = 0; i != response.task_execution_infos_size(); ++i) {
        AddTaskToExecuteList(response.task_execution_infos(i));
      }
      LOG(INFO) << "RecoverTask succ response: " << response.DebugString();
      already_recover_ = true;
    }
  }

  return already_recover_;
}

void TaskManager::AddTaskToExecuteList(
    const TaskExecutionInfo& task_execution_info) {
  if (register_task_type_map_.count(task_execution_info.task_name()) == 0) {
    LOG(INFO) << "AddTaskToExecuteList not support task: "
              << task_execution_info.DebugString();
    dot_->Count(1, "add_task_to_execute_list", "not_found_register_task",
                task_execution_info.task_name(),
                task_execution_info.data_version());
    return;
  }

  if (AdKconfUtil::taskBlackList()->count(task_execution_info.task_name()) != 0) {
    LOG(INFO) << "AddTaskToExecuteList task in blacklist: "
              << task_execution_info.DebugString();
    dot_->Count(1, "add_task_to_execute_list", "task_in_black_list",
                task_execution_info.task_name(),
                task_execution_info.data_version());
    return;
  }

  if (execute_task_name_set_.count(task_execution_info.task_name()) != 0) {
    LOG(INFO) << "AddTaskToExecuteList already in execute task list: "
              << task_execution_info.DebugString();
    dot_->Count(1, "add_task_to_execute_list", "already_in_execute_task_list",
                task_execution_info.task_name(),
                task_execution_info.data_version());
    return;
  }

  auto task_run_unit = GenTaskExecutionUnit(task_execution_info.task_name(),
                                            task_execution_info.data_version());
  if (task_run_unit == nullptr) {
    LOG(INFO) << "AddTaskToExecuteList gen task run unit error: "
              << task_execution_info.DebugString();
    dot_->Count(1, "add_task_to_execute_list", "gen_task_execution_unit_error",
                task_execution_info.task_name(),
                task_execution_info.data_version());
    return;
  }

  execute_task_list_.push_back(task_run_unit);
  execute_task_name_set_.insert(task_run_unit->task_name);
  thread_pool_->AddTask(
      NewCallback(task_run_unit.get(), &TaskExecutionUnit::Execute));

  dot_->Count(1, "add_task_to_execute_list", "succ",
              task_execution_info.task_name(),
              task_execution_info.data_version());

  LOG(INFO) << "AddTaskToExecuteList succ task_name: "
            << task_run_unit->task_name
            << "data_version: " << task_run_unit->data_version;
}

void TaskManager::ReportTaskInfo() {
  auto peer = GetControllerLeaderPeer();
  if (peer.empty()) {
    LOG(ERROR) << "Faild to get controller leader peer";
    return;
  }

  ReportTaskInfoReq request;
  BuildReportTaskInfoReq(&request);

  auto client = ks::ad_base::AdKessClient::ClientOfKey<
      ks::ad_data_format_server::kess::AdDataFormatService>(
      kControllerClientKey);
  const auto options = ks::ad_base::OptionsFromMilli(client.first->time_out);
  ReportTaskInfoResp response;
  auto grpc_status = client.second->SelectByDest(peer, false)
                         ->ReportTaskInfo(options, request, &response);

  if (!grpc_status.ok()) {
    LOG(ERROR) << "ReportTaskInfo Status: " << grpc_status.error_code()
               << ", message: " << grpc_status.error_message()
               << ", request: " << request.DebugString();
    return;
  }

  LOG_EVERY_N(INFO, 10) << "ReportTaskInfoReq: "
                        << request.pod_info().DebugString();

  for (int i = 0; i != request.task_execution_infos_size(); ++i) {
    auto& task_info = request.task_execution_infos(i);
    dot_->Set(1, "task_execution_info", task_info.task_name(),
              task_info.data_version(),
              TaskStatus_Name(task_info.task_status()),
              TaskErrorCode_Name(task_info.task_error_code()));
  }

  dot_->Interval(request.task_execution_infos_size(),
                 "task_execution_infos_size");

  int64 timestamp = base::GetTimestamp();
  std::for_each(execute_task_list_.begin(), execute_task_list_.end(),
                [&](const std::shared_ptr<TaskExecutionUnit>& task) {
                  auto task_status = task->task_status_func();
                  if (task_status == TaskStatus::TS_RUNING) {
                    dot_->Interval(timestamp - task->task_start_time_func(),
                                   "task_start_time", task->task_name,
                                   task->data_version);
                  }
                });
}

void TaskManager::RemoveFinishedTask() {
  execute_task_list_.remove_if(
      [&](const std::shared_ptr<TaskExecutionUnit>& task) {
        if (task->task_status_func() == TaskStatus::TS_FINISHED) {
          task->task_clear_func();
          execute_task_name_set_.erase(task->task_name);
          return true;
        }
        return false;
      });
}

std::shared_ptr<TaskExecutionUnit> TaskManager::GenTaskExecutionUnit(
    const std::string& task_name, const std::string& data_version) {
  auto iter = register_task_type_map_.find(task_name);
  if (iter == register_task_type_map_.end()) {
    return nullptr;
  }

  std::shared_ptr<TaskExecutionUnit> exec(new TaskExecutionUnit(task_name, data_version));
  bool rst = false;
  switch (iter->second) {
    case TaskType::STANDARD_MAP_TASK:
      rst = FindTask(standard_map_task_map_, task_name, exec.get());
      break;
    case TaskType::STANDARD_SET_TASK:
      rst = FindTask(standard_set_task_map_, task_name, exec.get());
      break;
    case TaskType::CUSTOMIZED_MAP_TASK:
      rst = FindTask(customized_map_task_map_, task_name, exec.get());
      break;
  }

  if (!rst) {
    return nullptr;
  }

  return exec;
}

void TaskManager::BuildReportTaskInfoReq(ReportTaskInfoReq* request) {
  for (auto& riter : register_task_type_map_) {
    request->add_support_task_list(riter.first);
  }

  int64 timestamp = base::GetTimestamp();
  for (auto& eiter : execute_task_list_) {
    auto* task_execution_info = request->add_task_execution_infos();
    ConvertToTaskExecutionInfo(*eiter, task_execution_info);
    task_execution_info->set_timestamp(timestamp);
  }

  GetServicePodInfo(request->mutable_pod_info());
}

bool TaskManager::GetRecoverTaskResponse(RecoverTaskResp* response) {
  auto peer = GetControllerLeaderPeer();
  if (peer.empty()) {
    LOG(ERROR) << "Faild to get controller leader peer";
    return false;
  }

  RecoverTaskReq request;
  GetServicePodInfo(request.mutable_pod_info());

  auto client = ks::ad_base::AdKessClient::ClientOfKey<
      ks::ad_data_format_server::kess::AdDataFormatService>(
      kControllerClientKey);
  const auto options = ks::ad_base::OptionsFromMilli(client.first->time_out);
  auto grpc_status = client.second->SelectByDest(peer, false)
                         ->RecoverTask(options, request, response);
  if (!grpc_status.ok()) {
    LOG(ERROR) << "Request recoverTask failed error_code: "
               << grpc_status.error_code()
               << ", error_message: " << grpc_status.error_message()
               << ", request: " << request.DebugString();
    return false;
  }

  if (!response->status()) {
    return false;
  }

  return true;
}

void TaskManager::GetServicePodInfo(ServicePodInfo* pod_info) {
  pod_info->set_pod_name(engine_base::DeployVariable::GetPodName());
  pod_info->set_peer(ad_base::AdKessClient::Instance().Peer());
}

std::string TaskManager::GetControllerLeaderPeer() {
  std::string peer;
  if (zk_client_)
    zk_client_->GetNodeContent(&peer);
  return peer;
}

void TaskManager::StatisticsTaskInfos() {
  for (auto& riter : register_task_type_map_) {
    dot_->Count(1, "register_task_type_info", riter.first,
                std::to_string(static_cast<int>(riter.second)));
  }
}

bool TaskManager::IsSupportTask(const std::string& task_name) {
  return register_task_type_map_.count(task_name) != 0;
}

void TaskManager::DynamicAdjustStandardTask() {
  auto& config = AdKconfUtil::standardTasksConfig()->data();
  std::unordered_set<std::string> config_standard_map_tasks,
      config_standard_set_tasks;
  for (int i = 0; i != config.standard_map_tasks_size(); ++i) {
    config_standard_map_tasks.emplace(config.standard_map_tasks(i));
    if (IsSupportTask(config.standard_map_tasks(i))) {
      continue;
    }
    RegisterTask(
        std::make_unique<StandardMapTask>(config.standard_map_tasks(i)));
    LOG(INFO) << "DynamicAdjustStandardTask add standard_map_task "
              << config.standard_map_tasks(i);
  }

  for (int i = 0; i != config.standard_set_tasks_size(); ++i) {
    config_standard_set_tasks.emplace(config.standard_set_tasks(i));
    if (IsSupportTask(config.standard_set_tasks(i))) {
      continue;
    }
    RegisterTask(
        std::make_unique<StandardSetTask>(config.standard_set_tasks(i)));
    LOG(INFO) << "DynamicAdjustStandardTask add standard_set_tasks "
              << config.standard_set_tasks(i);
  }

  auto condition = [](auto& task_iter) {
    if (task_iter->second->GetTaskStatus() == TaskStatus::TS_INIT) {
      return true;
    }

    return false;
  };

  for (auto iter = register_task_type_map_.begin();
       iter != register_task_type_map_.end();) {
    bool rst = false;
    if (iter->second == TaskType::CUSTOMIZED_MAP_TASK &&
               config_standard_map_tasks.count(iter->first) == 0) {
      rst = RemoveTaskIf(iter->first, condition, &standard_map_task_map_);
    }

    if (rst) {
      LOG(INFO) << "DynamicAdjustStandardTask remove standard_task task_name = "
                << iter->first
                << " task_type =" << static_cast<int>(iter->second);
      iter = register_task_type_map_.erase(iter);
    } else {
      ++iter;
    }
  }
}

TaskManager::TaskManager()
    : AdBgTask(1000, "task_manager") {
  dot_ = std::make_unique<ad_base::Dot>(1);
}

TaskManager::~TaskManager() {
  standard_map_task_map_.clear();
  standard_set_task_map_.clear();
  customized_map_task_map_.clear();
  register_task_type_map_.clear();
  execute_task_list_.clear();
  execute_task_name_set_.clear();
  if (thread_pool_)
    thread_pool_->JoinAll();
}

void TaskManager::StartPrepare() {
  zk_client_ = std::make_unique<ZkSimpleClient>();
  thread_pool_ = std::make_unique<thread::ThreadPool>(
          AdKconfUtil::formatProducerThreadNum());
}

}  // namespace ad_data_format_server
}  // namespace ks
