#include "teams/ad/ad_data_format_server/producer/producer_service.h"

#include "teams/ad/ad_data_format_server/producer/task_manager.h"

namespace ks {
namespace ad_data_format_server {

::grpc::Status ProducerService::DispatchTask(
    ::grpc::ServerContext* context,
    const ::ks::ad_data_format_server::DispatchTaskReq* request,
    ::ks::ad_data_format_server::DispatchTaskResp* response) {
  TaskManager::GetInstance().DispatchTask(*request, response);
  return ::grpc::Status::OK;
}

}  // namespace ad_data_format_server
}  // namespace ks
