#pragma once

#include <algorithm>
#include <atomic>
#include <functional>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "absl/strings/str_cat.h"
#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "base/common/closure.h"
#include "base/encoding/base64.h"
#include "base/file/dir_reader_posix.h"
#include "base/thread/thread_pool.h"
#include "base/time/timestamp.h"
#include "perfutil/perfutil.h"
#include "teams/ad/ad_base/src/common/condition_time_wait.h"
#include "teams/ad/ad_base/src/dot/dot.h"
#include "teams/ad/ad_data_format_server/producer/task_base/file_loader.h"
#include "teams/ad/ad_data_format_server/proto/ad_data_format_server.pb.h"
#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/data_check_plugin_manager.h"
#include "teams/ad/ad_data_format_server/utils/hdfs/hdfs_uploader.h"
#include "teams/ad/ad_data_format_server/utils/kconf/kconf.h"

namespace ks {
namespace ad_data_format_server {

static const char kKfsPath[] = "/data/web_server/ad_kfs";

template <typename T>
class TaskBase {
 public:
  using ContainerValueType = typename T::value_type;
  explicit TaskBase(const std::string& name, bool use_inner_parse = false)
      : name_(name),
        dot_(0),
        use_inner_parse_(use_inner_parse) {
    push_path_ = "/home/<USER>/user/p2p_check_done/" + name_;
    dump_path_ = "../../checked_dump/" + name_;
    base::file_util::CreateDirectory(dump_path_);
    Clear();
  }
  ~TaskBase() {
  }

  void SetDataVersion(const std::string& version) {
    version_ = version;
  }

  const std::string& GetDataVersion() { return version_; }

  TaskStatus GetTaskStatus() { return task_status_.load(); }

  TaskErrorCode GetTaskErrorCode() { return task_error_code_.load(); }

  int64 GetTaskStartTime() { return task_start_time_.load(); }

  const std::string GetTaskName() { return name_; }

  void Run() {
    task_start_time_.store(base::GetTimestamp());
    task_status_.store(TaskStatus::TS_RUNING);
    auto ret_code = ParseAndUpLoadData();
    dot_.Set(base::GetTimestamp() - GetTaskStartTime(),
             "task_finish_cost_time", name_, version_);
    dot_.Count(1, "task_error_code_info", TaskErrorCode_Name(ret_code),
               name_, version_);
    dot_.Set(file_data_->size(), "task_file_data_size_", name_, version_);
    task_error_code_.store(ret_code);
    task_status_.store(TaskStatus::TS_FINISHED);
    return;
  }

  void Clear() {
    file_data_.reset();
    task_status_.store(TaskStatus::TS_INIT);
    task_error_code_.store(TaskErrorCode::TCC_EMPTY);
    task_start_time_.store(0);
    succ_versions_.clear();
    data_size_ = 0;
    can_commit_reids_info_ = true;
    std::unordered_set<int64_t>().swap(data_keys_);
  }

 protected:
  // 归一化输出
  virtual bool ValueToString(const typename T::value_type&, std::string* str) = 0;
  virtual bool ParseLine(const std::string& line) { return true; }
  T* GetData() {
    return file_data_.get();
  }
  virtual void ParseFileDataPrepare() {}
  virtual void ParseFileDataPostProc() {}
  virtual uint64_t CalDataSize() = 0;

 private:
  TaskErrorCode ParseAndUpLoadData() {
    bool succ = ParseFileData();
    if (!succ) {
      return TaskErrorCode::TCC_PARSE_FILE;
    }

    succ = CheckData();
    if (!succ) {
      return TaskErrorCode::TCC_CHECK_DATA;
    }

    succ = UploadData();
    if (!succ) {
      return TaskErrorCode::TCC_UPLOAD_DATA;
    }

    if (AdKconfUtil::allowProducerDataCheck() && can_commit_reids_info_) {
      DataCheckPluginManager::Instance().CommitDataCheckRedisInfo(
          data_keys_, version_, name_, data_size_);
    }

    return TaskErrorCode::TCC_EMPTY;
  }

  bool ParseFileData() {
    bool succ = false;
    file_data_ = std::make_unique<T>();
    ParseFileDataPrepare();
    ks::ad_base::LoadFileInfo load_info;
    std::string file_path = absl::StrCat(kKfsPath, "/", name_, "/", version_);
    if (use_inner_parse_) {
      succ = file_loader_.LoadFromDirWithInnerParse(file_path, &load_info,
                                                    file_data_.get());
    } else {
      succ = file_loader_.LoadFromDir(
          file_path, &load_info,
          [&](const std::string& line) { return ParseLine(line); });
    }
    ParseFileDataPostProc();
    dot_.Set(load_info.succ_count, "parse_file_succ_line_count", name_,
             version_);
    if (load_info.line_count != 0) {
      double succ_percent = load_info.succ_count * 100;
      succ_percent = succ_percent / load_info.line_count;
      dot_.Set(static_cast<int>(succ_percent), "parse_file_succ_line_percent",
               name_, version_);
    }

    return succ;
  }

  bool CheckData() {
    bool succ = true;
    if (AdKconfUtil::allowProducerDataCheck()) {
      data_size_ = CalDataSize();
      GenDataKeys();
      succ = DataCheckPluginManager::Instance().CheckData(
          data_keys_, version_, name_, data_size_, &can_commit_reids_info_);
    }

    return succ;
  }

  // 清理本地数据, 保留最近的 3 份
  void CleanLocalOld() {
    using base::file_util::FileEnumerator;
    FileEnumerator file_enumerator(dump_path_, false, FileEnumerator::DIRECTORIES);
    std::vector<std::string> directories;
    for (auto path = file_enumerator.Next(); !path.value().empty(); path = file_enumerator.Next()) {
      directories.push_back(path.value());
    }
    std::sort(directories.begin(), directories.end(),
              [](const std::string &a, const std::string &b) -> bool { return a < b; });
    while (directories.size() > 2) {
      auto delete_path = dump_path_.Append(directories.front());
      base::file_util::Delete(base::FilePath(delete_path), true);
      directories.erase(directories.begin());
    }
    return;
  }
  // 清理 hdfs 数据
  void CleanHdfsOld() {
    std::vector<hadoop::HDFSPathInfo> exists_path;
    if (hadoop::HDFSListDirectory(push_path_.value().c_str(), &exists_path)) {
      if (exists_path.size() < AdKconfUtil::cleanVersionLimits()) {
        // 版本不多时暂不清理：适用制作频率以月为单位的数据
        return;
      }
      for (const auto &path : exists_path) {
        size_t last_pos = path.name.find_last_of('/');
        if (last_pos != std::string::npos) {
          std::string version_dir = path.name.substr(last_pos + 1);
          std::vector<std::string> v = absl::StrSplit(version_dir, ".");
          if (v.size() != 2) {
            continue;
          }
          absl::Time t;
          std::string err;
          if (!absl::ParseTime("%Y-%m-%d_%H%M", v[0], absl::LocalTimeZone(), &t, &err)) {
            LOG(WARNING) << "Failed to extract version: " << version_dir << " Error " << err;
            continue;
          }
          auto delay_min = absl::ToInt64Minutes(absl::Now() - t);
          if (delay_min > AdKconfUtil::hdfsStorgeHours() * 60) {
            if (hadoop::HDFSRmr(path.name.c_str())) {
              LOG(INFO) << "clean directory ok " << path.name;
            } else {
              LOG(INFO) << "clean directory failed " << path.name;
            }
          }
        } else {
          LOG(WARNING) << "not recognize dir " << path.name;
        }
      }
    } else {
      LOG(WARNING) << "list dir failed " << push_path_.value();
    }
  }
  // 检查 hdfs 上文件是否已存在
  bool CheckHdfs(const std::string &version) {
    auto push_success_flag = VersionPushPath(version).Append(push_ok_flag_);
    auto ret = hadoop::HDFSExists(push_success_flag.value().c_str());
    LOG(INFO) << "check hdfs, path=" << push_success_flag.value() << ", exists=" << ret;
    return ret;
  }
  // 检查本地文件是否已生成
  bool CheckLocal(const std::string &version) {
    auto dump_success_flag = VersionDumpPath(version).Append(dump_ok_flag_);
    auto ret = base::file_util::PathExists(dump_success_flag);
    LOG(INFO) << "check local, path=" << dump_success_flag.value() << ", exists=" << ret;
    return ret;
  }
  base::FilePath VersionDumpPath(const std::string& version) {
    return dump_path_.Append(version);
  }
  base::FilePath VersionPushPath(const std::string& version) {
    return push_path_.Append(version + ".checked");
  }
  // 数据校验后导出归一化文件
  bool DumpToFile(const std::string& version) {
    auto version_dump_path = VersionDumpPath(version);
    base::file_util::CreateDirectory(version_dump_path);
    auto dump_file = version_dump_path.value() + "/data";
    auto scope_ptr = GetData();
    if (scope_ptr) {
      std::ofstream of(dump_file, std::ios::out|std::ios::trunc);
      if (!of.is_open()) {
        LOG(ERROR) << "write file " << dump_file << " failed: open failed";
        of.close();
        return false;
      }
      std::string str;
      for (const auto& it : *scope_ptr) {
        if (!ValueToString(it, &str)) {
          continue;
        }
        of << str;
      }
      of.close();
    }
    auto success_file = version_dump_path.Append(dump_ok_flag_);
    // touch local success flag
    std::fstream output;
    output.open(success_file.value(), std::ios::out);
    if (!output) {
      LOG(ERROR) << "Error in creating file!!! " << success_file.value();
      return false;
    }
    output.close();
    return true;
  }
  // 上传文件到 hdfs
  bool HdfsPush(const std::string& version) {
    auto version_push_path = VersionPushPath(version);
    auto version_dump_path = VersionDumpPath(version);
    if (CheckHdfs(version)) {
      return true;
    }
    thread::ThreadPool upload_pool(1);
    uploader_.SetUploadThreadPool(&upload_pool);
    uploader_.Mkdir(version_push_path.value());
    LOG(INFO) << "begin push " << version_dump_path.value() << "->" << version_push_path.value();
    base::DirReaderPosix dir_reader(version_dump_path.value().c_str());
    while (dir_reader.Next()) {
      auto curr_path = base::FilePath(version_dump_path)
                       .Append(dir_reader.name()).ToString();
      base::PlatformFileInfo tmp_info;
      if (base::file_util::GetFileInfo(curr_path, &tmp_info)) {
        if (tmp_info.is_directory) {
          continue;
        } else {
          LOG(INFO) << "Start upload " << curr_path << " -> " << version_push_path.value();
          uploader_.AddUploadTask(curr_path, version_push_path.value());
        }
      } else {
        LOG(WARNING) << "GetFileInfo [" << curr_path << "] failed.";
      }
    }
    upload_pool.JoinAll();
    uploader_.TouchSuccess(version_push_path.value());
    LOG(INFO) << "push success" << version_dump_path.value() << "->" << version_push_path.value();
    return true;
  }
  bool UploadData() {
    LOG(INFO) << "Start Task name:" << name_;
    bool push_ret = false;
    std::string error_msg;
    do {
      // step 1. 清理历史文件
      CleanLocalOld();
      CleanHdfsOld();
      const std::string& version = GetDataVersion();
      if (version.empty()) {
        error_msg = "version empty";
        continue;
      }
      if (succ_versions_.count(version) > 0) {
        error_msg = "version already finish";
        continue;
      }
      // step 2. 数据版本校验
      if (CheckHdfs(version)) {
        error_msg = "version already push";
        succ_versions_.insert(version);
        continue;
      }
      // step 3. dump
      LOG(INFO) << "begin dump version=" << version;
      auto start_ms = base::GetTimestamp() / 1000;
      int64_t dump_end_ms = 0;
      if (!CheckLocal(version)) {
        bool dump_ret = DumpToFile(version);
        dump_end_ms = base::GetTimestamp() / 1000;
        LOG(INFO) << "local dump version=" << version
                  << ", path=" << VersionDumpPath(version).value()
                  << ", cost=" << dump_end_ms - start_ms
                  << ", dump_ret=" << dump_ret;
        dot_.Count((dump_end_ms - start_ms), "ad_data_format_server",
                   "dump.latency", name_);
        if (!dump_ret) {
          error_msg = "dump to file error";
          continue;
        }
      } else {
        LOG(INFO) << "local dump version=" << version
                  << ", path=" << VersionDumpPath(version).value()
                  << ", already ok!!";
      }
      // step 4. upload
      push_ret = HdfsPush(version);
      if (push_ret) {
        succ_versions_.insert(version);
      } else {
        error_msg = "hdfs push error!!!";
        continue;
      }
      auto push_end_ms = base::GetTimestamp() / 1000;
      LOG(INFO) << "hdfs push version=" << version
                << ", path=" << VersionPushPath(version).value()
                << ", cost=" << push_end_ms - dump_end_ms
                << ", push_ret=" << push_ret;
      dot_.Count((push_end_ms - dump_end_ms), "ad_data_format_server", "push",
                 name_);
    } while (false);

    if (!push_ret) {
      LOG(INFO) << "UploadData failed data_name: " << name_
                << " version: " << version_ << " error_msg: " << error_msg;
    }

    return push_ret;
  }

  void GenDataKeys() {
    DataCheckHelper::GetAllKeysWithFunc(
        *file_data_, [&](const std::string& key) {
          data_keys_.insert(std::hash<std::string>()(key));
        });
  }

 protected:
  const std::string name_;
  std::string version_;
  ks::ad_base::Dot dot_;
  uint64_t data_size_{0};
  bool can_commit_reids_info_{true};

 private:
  base::FilePath dump_path_;
  base::FilePath push_path_;
  std::set<std::string> succ_versions_;
  std::string dump_ok_flag_{"_LOCAL_SUCCESS"};  // 本地 dump 成功标记
  std::string push_ok_flag_{"_SUCCESS"};  // hdfs push 成功标记
  bool use_inner_parse_{false};
  std::atomic<TaskStatus> task_status_;
  std::atomic<TaskErrorCode> task_error_code_;
  std::atomic<int64> task_start_time_;
  std::unordered_set<int64_t> data_keys_;

  HdfsUploader uploader_;
  FileLoader file_loader_;
  std::unique_ptr<T> file_data_;
};

}  // namespace ad_data_format_server
}  // namespace ks
