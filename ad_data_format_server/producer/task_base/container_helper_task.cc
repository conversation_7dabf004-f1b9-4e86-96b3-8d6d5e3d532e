#include "teams/ad/ad_data_format_server/producer/task_base/container_helper_task.h"

namespace ks {
namespace ad_data_format_server {

ContainerHelperTask::ContainerHelperTask(const std::string& task_name,
                                         LineParser line_parser)
    : CustomizedMapTaskBase(task_name), line_parser_(line_parser) {}

ContainerHelperTask::~ContainerHelperTask() {
}

bool ContainerHelperTask::ParseLine(const std::string& line) {
  std::pair<int64_t, std::unique_ptr<google::protobuf::Message>> pb_data;
  if (!line_parser_(line, &pb_data) || pb_data.second == nullptr) {
    LOG_EVERY_N(INFO, 1000) << GetTaskName() << "ParseLine Error" << line;
    return false;
  }

  InsertProtobufValue(std::to_string(pb_data.first), *pb_data.second);
  return true;
}

}  // namespace ad_data_format_server
}  // namespace ks
