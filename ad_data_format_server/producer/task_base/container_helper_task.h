#pragma once

#include <functional>
#include <memory>
#include <string>
#include <utility>

#include "teams/ad/ad_data_format_server/producer/task_base/customized_map_task_base.h"

namespace ks {
namespace ad_data_format_server {

class ContainerHelperTask : public CustomizedMapTaskBase {
 public:
  using LineParser = std::function<bool(
      const std::string&,
      std::pair<int64_t, std::unique_ptr<google::protobuf::Message>> *)>;
  ContainerHelperTask(const std::string& data_name, LineParser line_parser);
  ~ContainerHelperTask();

  bool ParseLine(const std::string& line) override;

 private:
  LineParser line_parser_;
};

}  // namespace ad_data_format_server
}  // namespace ks
