#include "teams/ad/ad_data_format_server/producer/task_base/standard_map_task.h"

#include <absl/strings/substitute.h>

namespace ks {
namespace ad_data_format_server {

StandardMapTask::StandardMapTask(const std::string& task_name)
    : TaskBase(task_name, true) {}

StandardMapTask::~StandardMapTask() {}

bool StandardMapTask::ValueToString(const ContainerValueType& raw,
                                    std::string* res_str) {
  *res_str = absl::StrCat(raw.first, "\t", raw.second, "\n");
  return true;
}

uint64_t StandardMapTask::CalDataSize() {
  uint64_t data_size = 0;
  auto data_ptr = GetData();
  if (data_ptr != nullptr) {
    for (auto& iter : *data_ptr) {
      data_size += iter.first.size();
      data_size += iter.second.size();
    }
  }

  return data_size;
}

}  // namespace ad_data_format_server
}  // namespace ks
