#pragma once

#include <utility>
#include <string>
#include <vector>

#include "absl/strings/str_split.h"
#include "teams/ad/ad_base/src/file/utility.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader_type_helper.h"

namespace ks {
namespace ad_data_format_server {

class FileLoader {
 public:
  FileLoader() = default;
  ~FileLoader() = default;

  // 自定义解析方式
  bool LoadFromDir(const std::string& dir,
                   ks::ad_base::LoadFileInfo* load_info,
                   std::function<bool(const std::string&)> parser) {
    return ad_base::LoadFromFile(dir, parser, *load_info);
  }

  // 使用内置解析器解析到 data 数据中
  template <typename T>
  bool LoadFromDirWithInnerParse(const std::string& dir,
                                 ks::ad_base::LoadFileInfo* load_info,
                                 T* data) {
    return LoadFromDir(dir, load_info, [&](const std::string& line) {
      return InnerParse(dir, line, data);
    });
  }

 private:
  template <typename T,
            std::enable_if_t<ks::ad_base::is_map<T>::value, int> = 0>
  bool InnerParse(const std::string& dir, const std::string& line, T* data) {
    using TKey = typename T::key_type;
    using TValue = typename T::mapped_type;
    std::vector<absl::string_view> tokens =
        absl::StrSplit(line, absl::MaxSplits("\t", 1), absl::SkipEmpty());
    if (tokens.size() != 2) {
      LOG_EVERY_N(WARNING, 1000)
          << " dir_path=" << dir
          << " invalid token.size, size=" << tokens.size();
      return false;
    }
    ignore_result(absl::StripAsciiWhitespace(tokens[0]));
    ignore_result(absl::StripAsciiWhitespace(tokens[1]));

    bool suc = false;
    TKey key;
    suc = ks::ad_base::ParseFromString<TKey>(tokens[0].data(), tokens[0].size(), &key);
    if (!suc) {
      LOG_EVERY_N(WARNING, 1000)
          << "dir_path=" << dir
          << " key parse failed. decode_value:" << tokens[1]
          << ". key:" << tokens[0];
      return false;
    }

    TValue value;
    suc = ks::ad_base::ParseFromString<TValue>(tokens[1].data(), tokens[1].size(), &value);
    if (!suc) {
      LOG_EVERY_N(WARNING, 1000)
          << "dir_path=" << dir
          << " value parse failed. decode_value:" << tokens[1]
          << ". key:" << tokens[0];
      return false;
    }

    LOG_EVERY_N(INFO, 100000)
        << "dir_path=" << dir << " ParseLine succ, key:" << key
        << " value:" << value;
    data->emplace(std::move(key), std::move(value));

    return true;
  }

  template <typename T,
            std::enable_if_t<ks::ad_base::is_set<T>::value, int> = 0>
  bool InnerParse(const std::string& dir, const std::string& line, T* data) {
    using TValue = typename T::value_type;
    std::vector<absl::string_view> tokens = absl::StrSplit(line, "\t", absl::SkipEmpty());
    if (tokens.size() != 1) {
      LOG_EVERY_N(WARNING, 1000) << "dir_path=" << dir
                                 << " invalid token.size, size=" << tokens.size();
      return false;
    }

    TValue value;
    auto suc = ks::ad_base::ParseFromString<TValue>(tokens[0].data(), tokens[0].size(), &value);
    if (!suc) {
      LOG_EVERY_N(WARNING, 1000) << "dir_path=" << dir
                                 << " value parse failed. decode_value:" << tokens[0];
      return false;
    }

    LOG_EVERY_N(INFO, 10000)
        << "dir_path=" << dir << " ParseLine Succ, value:" << value;
    data->emplace(std::move(value));

    return true;
  }
};

}  // namespace ad_data_format_server
}  // namespace ks
