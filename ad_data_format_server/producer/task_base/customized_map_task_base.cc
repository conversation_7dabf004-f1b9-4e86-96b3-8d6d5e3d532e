#include "teams/ad/ad_data_format_server/producer/task_base/customized_map_task_base.h"

#include <utility>

#include "base/encoding/base64.h"

namespace ks {
namespace ad_data_format_server {

CustomizedMapTaskBase::CustomizedMapTaskBase(const std::string& task_name)
    : TaskBase(task_name) {}

CustomizedMapTaskBase::~CustomizedMapTaskBase() {}

bool CustomizedMapTaskBase::ValueToString(const ContainerValueType& raw,
                                          std::string* res_str) {
  *res_str = absl::StrCat(raw.first, "\t", raw.second, "\n");
  return true;
}

bool CustomizedMapTaskBase::InsertProtobufValue(
    const std::string& key, const ::google::protobuf::Message& message) {
  std::string message_str;
  message.SerializeToString(&message_str);
  std::string base64_message_str;
  base::Base64Encode(message_str, &base64_message_str);
  return GetData()->emplace(key, std::move(base64_message_str)).second;
}

uint64_t CustomizedMapTaskBase::CalDataSize() {
  uint64_t data_size = 0;
  auto data_ptr = GetData();
  if (data_ptr != nullptr) {
    for (auto& iter : *data_ptr) {
      data_size += iter.first.size();
      data_size += iter.second.size();
    }
  }

  return data_size;
}

}  // namespace ad_data_format_server
}  // namespace ks
