#include "teams/ad/ad_data_format_server/producer/task_base/standard_set_task.h"

namespace ks {
namespace ad_data_format_server {

StandardSetTask::StandardSetTask(const std::string& task_name)
    : TaskBase(task_name, true) {
}

StandardSetTask::~StandardSetTask() {
}

bool StandardSetTask::ValueToString(const ContainerValueType& raw,
                                    std::string* res_str) {
  *res_str = absl::StrCat(raw, "\n");
  return true;
}

uint64_t StandardSetTask::CalDataSize() {
  uint64_t data_size = 0;
  auto data_ptr = GetData();
  if (data_ptr != nullptr) {
    for (auto& iter : *data_ptr) {
      data_size += iter.size();
    }
  }

  return data_size;
}

}  // namespace ad_data_format_server
}  // namespace ks
