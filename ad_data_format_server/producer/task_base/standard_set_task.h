#pragma once

#include <string>
#include <unordered_set>

#include "teams/ad/ad_data_format_server/producer/task_base/task_base.h"

namespace ks {
namespace ad_data_format_server {

class StandardSetTask
    : public TaskBase<std::unordered_set<std::string>> {
 public:
  explicit StandardSetTask(const std::string& task_name);
  ~StandardSetTask();

 protected:
  bool ValueToString(const ContainerValueType& raw,
                     std::string* res_str) override;
  uint64_t CalDataSize() override;
};

}  // namespace ad_data_format_server
}  // namespace ks
