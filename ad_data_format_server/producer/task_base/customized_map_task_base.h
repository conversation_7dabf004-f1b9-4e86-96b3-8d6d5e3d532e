#pragma once

#include <string>
#include <unordered_map>

#include "google/protobuf/message.h"

#include "teams/ad/ad_data_format_server/producer/task_base/task_base.h"

namespace ks {
namespace ad_data_format_server {

// 自定义 ParseLine 的托管任务，需要在 ParseLine 中将数据转化 P2PCacheLoader
// 的标准数据格式
class CustomizedMapTaskBase
    : public TaskBase<std::unordered_map<std::string, std::string>> {
 public:
  explicit CustomizedMapTaskBase(const std::string& task_name);
  ~CustomizedMapTaskBase();

 protected:
  bool ValueToString(const ContainerValueType& value,
                     std::string* res_str) override;
  bool InsertProtobufValue(const std::string& key,
                           const ::google::protobuf::Message& message);
  uint64_t CalDataSize() override;
};

}  // namespace ad_data_format_server
}  // namespace ks
