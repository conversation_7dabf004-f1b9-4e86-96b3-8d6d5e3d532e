#pragma once

#include <string>
#include <unordered_map>
#include <utility>

#include "teams/ad/ad_data_format_server/producer/task_base/task_base.h"

namespace ks {
namespace ad_data_format_server {

class StandardMapTask
    : public TaskBase<std::unordered_map<std::string, std::string>> {
 public:
  explicit StandardMapTask(const std::string& task_name);
  ~StandardMapTask();

 protected:
  bool ValueToString(const ContainerValueType& raw,
                     std::string* res_str) override;
  uint64_t CalDataSize() override;
};

}  // namespace ad_data_format_server
}  // namespace ks
