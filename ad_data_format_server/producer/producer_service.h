#pragma once

#include "teams/ad/ad_data_format_server/proto/ad_data_format_server.kess.grpc.pb.h"

namespace ks {
namespace ad_data_format_server {

class ProducerService
    : public ks::ad_data_format_server::kess::AdDataFormatService::Service {
 public:
  ProducerService() = default;
  ~ProducerService() = default;

  ::grpc::Status DispatchTask(
      ::grpc::ServerContext* context,
      const ::ks::ad_data_format_server::DispatchTaskReq* request,
      ::ks::ad_data_format_server::DispatchTaskResp* response) override;
};

}  // namespace ad_data_format_server
}  // namespace ks
