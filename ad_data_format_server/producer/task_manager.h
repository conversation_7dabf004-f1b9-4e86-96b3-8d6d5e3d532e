#pragma once

#include <atomic>
#include <functional>
#include <list>
#include <memory>
#include <mutex>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>

#include "base/common/basic_types.h"
#include "teams/ad/ad_base/src/container/concurrent_queue.h"
#include "teams/ad/ad_base/src/timer_task/bg_task.h"
#include "teams/ad/ad_data_format_server/proto/ad_data_format_server.pb.h"

namespace ks {

namespace ad_base {
class Dot;
}

namespace ad_data_format_server {
class StandardMapTask;
class StandardSetTask;
class CustomizedMapTaskBase;
class CustomizedSetTaskBase;
class ZkSimpleClient;

struct TaskExecutionUnit {
  std::string task_name;
  std::string data_version;
  std::function<void()> execute_func;
  std::function<TaskStatus()> task_status_func;
  std::function<TaskErrorCode()> task_error_code_func;
  std::function<int64()> task_start_time_func;
  std::function<void()> task_clear_func;

  TaskExecutionUnit(const std::string& r_task_name,
                    const std::string& r_data_version)
      : task_name(r_task_name), data_version(r_data_version) {}

  void Execute() { execute_func(); }
};

enum class TaskType : int {
  STANDARD_MAP_TASK = 0,
  STANDARD_SET_TASK = 1,
  CUSTOMIZED_MAP_TASK = 2
};

class TaskManager final : public ad_base::AdBgTask {
 public:
  static TaskManager& GetInstance() {
    static TaskManager instance_;
    return instance_;
  }

  void RegisterTask(std::unique_ptr<StandardMapTask>&& task);
  void RegisterTask(std::unique_ptr<StandardSetTask>&& task);
  void RegisterTask(std::unique_ptr<CustomizedMapTaskBase>&& task);
  void DispatchTask(const DispatchTaskReq& request, DispatchTaskResp* response);

 protected:
  void DoTaskImpl() override;
  void StartPrepare() override;
  void StopPrepare() override;

 private:
  bool AddData(const std::string& task_name, TaskType type);
  bool RecoverTask();
  void AddTaskToExecuteList(const TaskExecutionInfo& task_execution_info);
  void ReportTaskInfo();
  void RemoveFinishedTask();
  std::shared_ptr<TaskExecutionUnit> GenTaskExecutionUnit(
      const std::string& task_name, const std::string& data_verison);
  void BuildReportTaskInfoReq(ReportTaskInfoReq* request);
  bool GetRecoverTaskResponse(RecoverTaskResp* response);
  void GetServicePodInfo(ServicePodInfo* pod_info);
  std::string GetControllerLeaderPeer();
  void StatisticsTaskInfos();
  bool IsSupportTask(const std::string& task_name);
  void DynamicAdjustStandardTask();

  TaskManager();
  ~TaskManager();

 private:
  std::unordered_map<std::string, std::unique_ptr<StandardMapTask>>
      standard_map_task_map_;
  std::unordered_map<std::string, std::unique_ptr<StandardSetTask>>
      standard_set_task_map_;
  std::unordered_map<std::string, std::unique_ptr<CustomizedMapTaskBase>>
      customized_map_task_map_;
  std::unordered_map<std::string, TaskType> register_task_type_map_;
  ks::ad_base::AdConcurrentQueue<TaskExecutionInfo> dispatch_task_queue_;
  std::list<std::shared_ptr<TaskExecutionUnit>> execute_task_list_;
  std::unordered_set<std::string> execute_task_name_set_;
  std::unique_ptr<thread::ThreadPool> thread_pool_;
  std::unique_ptr<ad_base::Dot> dot_;
  std::unique_ptr<ZkSimpleClient> zk_client_;
  bool already_recover_ = false;
  std::atomic_bool stop_flag{false};

  DISALLOW_COPY_AND_ASSIGN(TaskManager);
};

}  // namespace ad_data_format_server
}  // namespace ks

