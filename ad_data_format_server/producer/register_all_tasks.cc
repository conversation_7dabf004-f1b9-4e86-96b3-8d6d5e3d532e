#include "teams/ad/ad_data_format_server/producer/register_all_tasks.h"

#include <memory>
#include <set>
#include <string>
#include <vector>

#include "teams/ad/ad_data_format_server/producer/container_helper_tasks_header.h"
#include "teams/ad/ad_data_format_server/producer/task_base/container_helper_task.h"
#include "teams/ad/ad_data_format_server/producer/task_base/customized_map_task_base.h"
#include "teams/ad/ad_data_format_server/producer/task_base/standard_map_task.h"
#include "teams/ad/ad_data_format_server/producer/task_base/standard_set_task.h"
#include "teams/ad/ad_data_format_server/producer/task_manager.h"
#include "teams/ad/ad_data_format_server/utils/kconf/kconf.h"

namespace ks {

namespace ad_data_format_server {

namespace {

#define REGISTER_TASK_WITH_NAME(TASK_NAME, TASK_CLASS) \
  TaskManager::GetInstance().RegisterTask(             \
      std::make_unique<TASK_CLASS>(TASK_NAME));

#define REGISTER_TASK(TASK_CLASS) \
  TaskManager::GetInstance().RegisterTask(std::make_unique<TASK_CLASS>());

#define REGISTER_CONTAINER_HELPER_TASK(TASK_NAME, CONTAINER_HELPER) \
  TaskManager::GetInstance().RegisterTask(                          \
      std::make_unique<ContainerHelperTask>(                        \
          TASK_NAME, CONTAINER_HELPER::line_to_proto));

void RegisterStandardTasks() {
  auto& config = AdKconfUtil::standardTasksConfig()->data();
  std::set<std::string> map_tasks, set_tasks;
  for (const auto& st : config.standard_map_tasks()) {
    map_tasks.insert(st);
  }
  for (const auto& st : config.standard_set_tasks()) {
    set_tasks.insert(st);
  }
  for (const std::string& st : map_tasks) {
    REGISTER_TASK_WITH_NAME(st, StandardMapTask)
  }
  for (const std::string& st : set_tasks) {
    REGISTER_TASK_WITH_NAME(st, StandardSetTask)
  }
}

}  // namespace

void RegisterContainerHelperTasks() {
  REGISTER_CONTAINER_HELPER_TASK("ad-counter-photo_status",
                                 ks::ad_counter::PhotoStatusContainer)
  REGISTER_CONTAINER_HELPER_TASK("nobid_roi_fixed",
                                 ks::bid_server::NobidVideoPvContainer)
  //  REGISTER_CONTAINER_HELPER_TASK("ecom_product_price_data_to_adrank",
  //                                 ks::bid_server::EcomProductPriceContainer)
  // 已下线，修复编译问题
  // REGISTER_CONTAINER_HELPER_TASK("gsp_price_data",
  //                               ks::bid_server::MerchantGspPriceContainer)
  REGISTER_CONTAINER_HELPER_TASK("mcb_perform_control",
                                 ks::bid_server::McbPerformControlContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_backflow_event_delta_time_data_p2p",
                                 ks::bid_server::BackflowCvTimeDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("ad_merchant_cold_start_spu",
                                 ks::bid_server::BayesHeritageUnitDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("bonus_tag_flow_day",
                                 ks::bid_server::BonusTagDistributionByMinuteContainer)
  REGISTER_CONTAINER_HELPER_TASK("internal_merchant_level_category",
                                 ks::bid_server::SmbAccountTagContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "main_cost_trend_by_minute_v2",
      ks::bid_server::ImpressionDistributionModelV2Container)
  //  REGISTER_CONTAINER_HELPER_TASK(
  //      "conv_ratio_author_post_data",
  //      ks::bid_server::ConvRatioAuthorPostDataContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "jinniu_costcap_unit_max_cost",
      ks::bid_server::JinniuCostCapUnitMaxCostContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "mcb_best_performance_control",
      ks::bid_server::McbBestPerformanceControlContainer)
  // 已下线，修复编译问题
  // REGISTER_CONTAINER_HELPER_TASK(
  //    "backflow_stat_d",
  //    ks::bid_server::BackflowStatDContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "seven_day_paytimes_ratio",
      ks::bid_server::SevenDayPaytimesRatioContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "industry_playlet_sdpa",
      ks::engine_base::IndustryPlayletSdpaContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "performance_delivery_calibration_p2p",
      ks::bid_server::McbPerformanceCalibrationContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "performance_predict_mcb_product_p2p",
      ks::bid_server::McbPerformancePredictBaseContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "performance_predict_mcb_campaign_p2p",
      ks::bid_server::McbPerformancePredictBaseContainer)
  /*REGISTER_CONTAINER_HELPER_TASK(
      "mcb_retention_target",
      ks::bid_server::ProductAvgRetentionTargetContainer)*/
  REGISTER_CONTAINER_HELPER_TASK(
      "main_cost_second_bucket_to_bid_server",
      ks::bid_server::MainCostSecondDistributionModelContainer)
  REGISTER_CONTAINER_HELPER_TASK("ecom_infra_data",
                                 ks::engine_base::EcomDupUnitContainer)
  REGISTER_CONTAINER_HELPER_TASK("ecom_photo_spu_to_amd_target",
                                 ks::engine_base::PhotoSpuMapContainer)
  REGISTER_CONTAINER_HELPER_TASK("ad-counter-creative-posterior",
                                 ks::engine_base::CreativeCostContainer)
  REGISTER_CONTAINER_HELPER_TASK("support_p2p_cache_hq",
                                 ks::engine_base::SupportPrjectContainer)
  REGISTER_CONTAINER_HELPER_TASK("performance_predict_hosting",
                                 ks::engine_base::McbPerformanceContainer)
  REGISTER_CONTAINER_HELPER_TASK("twinbid_pcoc",
                                 ks::bid_server::TwinBidTargetContainer)
  REGISTER_CONTAINER_HELPER_TASK("impression_curve_by_second",
                                 ks::bid_server::ImpressionDistributionBySecondContainer)
  REGISTER_CONTAINER_HELPER_TASK("add_consume_impression_curve",
                                 ks::bid_server::AddConsumeDistributionBySecondContainer)
  REGISTER_CONTAINER_HELPER_TASK(
        "retention_days_bid_coef", ks::bid_server::RetentionDaysBidCoefContainer)
  //  REGISTER_CONTAINER_HELPER_TASK("ad_creative_audit",
  //                                 ks::engine_base::AdCreativeAuditContainer)
  REGISTER_CONTAINER_HELPER_TASK("product_cost",
                                 ks::engine_base::ProductCostTimeContainer)
  REGISTER_CONTAINER_HELPER_TASK("ecom_ecology_v2_p2p",
                                 ks::engine_base::EcomMerchantDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("mcb_initial_data",
                                 ks::engine_base::ProductAvgResultContainer)
  //  REGISTER_CONTAINER_HELPER_TASK("ad_creative_audit",
  //                                 ks::engine_base::AdCreativeAuditV2Container)
  REGISTER_CONTAINER_HELPER_TASK("phy_pos_id_aggr",
                                 ks::engine_base::UniversePhyPosAggrContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_rtb_win_rate",
                                 ks::engine_base::UniverseRtbWinRateContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_ecom_whitelist_rank_ratio",
                                 ks::engine_base::UniverseUnitIdRatioContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_agg_pos_replace",
      ks::engine_base::UniverseAggPosRepalceContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "inner_loop_author_gmv_interval",
      ks::engine_base::AuthorGmvIntervalCacheContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "native_ad_support_p2p_cache",
      ks::engine_base::NativeAdSupportProjectContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "live_ads_cpm_correction_v2",
      ks::engine_base::UniverseCpmCorrectionV2Container)
  REGISTER_CONTAINER_HELPER_TASK(
      "hq_support_project_info_v2",
      ks::engine_base::SupportProjectRatioLimitContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "union_rule_id_imp_pct",
      ks::engine_base::UnionRuleIdImpPctP2pCacheContainer)
  //  REGISTER_CONTAINER_HELPER_TASK(
  //      "ad_creative_audit", ks::engine_base::UniverseAdCreativeAuditV3Container)
  REGISTER_CONTAINER_HELPER_TASK(
      "union_endcard_id_imp_pct",
      ks::engine_base::UnionEncardIdImpPctP2pCacheContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_rtb_win_rate_bid_v2_task",
      ks::engine_base::UniverseRtbWinRateBidV2TaskContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_optimization_deep_targert_p2p",
      ks::engine_base::UniverseOptimizationTargetP2PContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "wangye06_ad_creative_stat_dt_merge",
      ks::engine_base::UniverseDirectHighCvrCreatviesContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "union_playable_product_name_post_data",
      ks::engine_base::UnionPlayableProductPostDataP2pCacheContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "union_interstitial_style_code_imp_pct",
      ks::engine_base::UnionInterstitialStyleCodeImpPctP2pCacheContainer)
  REGISTER_CONTAINER_HELPER_TASK("land_page_component_data",
                                 ks::front_server::LandPageContainer)
  REGISTER_CONTAINER_HELPER_TASK("app_tags",
                                 ks::front_server::StyleDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("pos_resource",
                                 ks::front_server::UniversePosResourcePb)
  //  REGISTER_CONTAINER_HELPER_TASK(  // 和 rank-server 使用的数据解析方式一致，这里不再重复制作
  //      "universe_hourly_cxr_data",
  //      ks::front_server::UniverseHourlyCxrDataContainer)
  //  REGISTER_CONTAINER_HELPER_TASK(  // 同上
  //      "universe_product_cov_stat",
  //      ks::front_server::UniverseProductCovStatContainer)
  //  REGISTER_CONTAINER_HELPER_TASK(  // 同上
  //      "universe_new_creative_data",
  //      ks::front_server::UniverseNewCreativeDataContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_operation_discount",
      ks::front_server::UniverseOperationDiscountContainer)
  REGISTER_CONTAINER_HELPER_TASK("download_mid_page",
                                 ks::ad_style_server::DownloadMidPageContainer)
  REGISTER_CONTAINER_HELPER_TASK("action_bar_card_style",
                                 ks::ad_style_server::ActionBarCardStyleContainer)
  REGISTER_CONTAINER_HELPER_TASK("nld_derived_photo",
                                 ks::ad_style_server::NldDerivedPhotoContainer)
  REGISTER_CONTAINER_HELPER_TASK("derived_photo_material",
                                 ks::ad_style_server::DerivedPhotoContainer)
  //  REGISTER_CONTAINER_HELPER_TASK("app_tags",
  //                                 ks::ad_style_server::StyleDataAppTagContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "app_industry_download_info_v2_30d",
      ks::ad_style_server::AppDownloadInfoP2pContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "search_photo_merchant_category",
      ks::ad_target_search::PhotoMerchantCategoryContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "innerloop_keyword_dmpids_p2p",
      ks::ad_target_search::InnerLoopKeywordContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_audience_deep_rate_and_roi",
                                 ks::ad_rank::EcpcContainer)
  REGISTER_CONTAINER_HELPER_TASK("conv_ratio_author_post_data",
                                 ks::ad_rank::PostDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("ad_merchant_live_author_post_rate",
                                 ks::ad_rank::AuthorStatContainer)
  REGISTER_CONTAINER_HELPER_TASK("new_spu_list",
                                 ks::ad_rank::NewSpuListContainer)
  REGISTER_CONTAINER_HELPER_TASK("account_cold_start_ctr_cvr",
                                 ks::ad_rank::AccountDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("cold_start_prodcut_ocpx_action_type_ctr_cvr",
                                 ks::ad_rank::ProductDataContainer)
  // REGISTER_CONTAINER_HELPER_TASK("retention_calibration_p2p_data",
  //                                ks::ad_rank::RetentionIRContainer)
  REGISTER_CONTAINER_HELPER_TASK("cold_photo_sim",
                                 ks::ad_rank::ColdPhotoSimContainer)
  REGISTER_CONTAINER_HELPER_TASK("prometheus_pcvr_data",
                                 ks::ad_rank::EcomUnitDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("ecom_photo_bonus_explore_p2p",
                                 ks::ad_rank::EcomBonusExploreContainer)
  REGISTER_CONTAINER_HELPER_TASK("ad_dsp_cvr_weight_ocpc",
                                 ks::ad_rank::CvrWeightOcpcContainer)
  REGISTER_CONTAINER_HELPER_TASK("dpp_embedding_p2p_by_day",
                                 ks::front_server::DppEmbeddingP2pContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_hourly_cxr_data",
                                 ks::ad_rank::HourlyPosDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("game_opt_general_product_p2p",
                                 ks::ad_rank::GameOptGeneralContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_merchant_reco_promote_roi_cali",
                                 ks::ad_rank::MerRoiCaliDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("iaa_key_inapp_ecpc_product",
                                 ks::ad_rank::IaaKeyInappEcpcContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_merchant_live_jicheng_zhuzhan_p2p",
                                 ks::ad_rank::LiveJichangDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("pid_bonus_project",
                                 ks::ad_rank::PidBonusProjectContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_merchant_roas_jicheng_p2p",
                                 ks::ad_rank::RoasJichangDataContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "univ_new_medium_id_x_ad_style_top_gmv_author_list_30d",
      ks::ad_rank::TopGmvAuthor30DContainer)
  REGISTER_CONTAINER_HELPER_TASK("coupon_data",
                                 ks::ad_rank::AdCouponInfoDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_merchant_reco_promote_order_cali",
                                 ks::ad_rank::MerOrderCaliDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("calibration_product_p2p_file_unify",
                                 ks::ad_rank::SmartBiddingDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("live_ads_cpm_correction_v1",
                                 ks::ad_rank::CpmCorrectionDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_jicheng_ecom_fields_p2p",
                                 ks::ad_rank::JichengEcomFieldsContainer)
  REGISTER_CONTAINER_HELPER_TASK("pec_select_coin_data",
                                 ks::ad_rank::PecCoinSelectDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("rewarded_calibrate_data",
                                 ks::ad_rank::RewardedCalibrateContainer)
  REGISTER_CONTAINER_HELPER_TASK("brand_bucket_post_roi_data",
                                 ks::ad_rank::BrandBucketPostRoiContainer)
  REGISTER_CONTAINER_HELPER_TASK("iap_game_up_model_ecpc_product",
                                 ks::ad_rank::IapGameUpModelEcpcContainer)
  REGISTER_CONTAINER_HELPER_TASK("la_prior_data_product_level",
                                 ks::ad_rank::LaPriorDataProductContainer)
  REGISTER_CONTAINER_HELPER_TASK("plagiarism_relation",
                                 ks::ad_rank::PlagiarismRelationContainer)
  REGISTER_CONTAINER_HELPER_TASK("rewarded_conversion_data",
                                 ks::ad_rank::RewardedConversionContainer)
  REGISTER_CONTAINER_HELPER_TASK("fans_correction_data",
                                 ks::ad_rank::FansCalibrationDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("inspire_live_cost_calibration_p2p_file_cmd",
                                 ks::ad_rank::IndustryInspireLiveContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_merchant_reco_promote_order_cali_exp",
      ks::ad_rank::MerOrderCaliDataExpContainer)
  REGISTER_CONTAINER_HELPER_TASK("adrank_search_cost_ratio",
                                 ks::ad_rank::SearchCostRatioDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("ecom_product_price_data_to_adrank",
                                 ks::ad_rank::EcomProductPriceDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("live_stream",
                                 ks::ad_rank::IndustryLiveColdBonusContainer)
  REGISTER_CONTAINER_HELPER_TASK("la_exp_prior_data_product_level",
                                 ks::ad_rank::LaExpPriorDataProductContainer)
  REGISTER_CONTAINER_HELPER_TASK("la_prior_lps_data_product_level",
                                 ks::ad_rank::LaPriorLpsDataProductContainer)
  REGISTER_CONTAINER_HELPER_TASK("ecpc_mcb_general_product_p2p",
                                 ks::ad_rank::McbEcpcGeneralProductContainer)
  REGISTER_CONTAINER_HELPER_TASK("roi_up_model_ecpc_product",
                                 ks::ad_rank::RoiUpModelEcpcProductContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_shortvideo_antou_cost_data",
                                 ks::ad_rank::UniverseAntouUnitDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("internal_client_info",
                                 ks::ad_rank::NativeAdSupportProjectContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_short_video_model_correct",
                                 ks::ad_rank::ShortVideoModelCorrectContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_product_cov_stat",
                                 ks::ad_rank::UniverseProductCovStatContainer)
  REGISTER_CONTAINER_HELPER_TASK("xdt_roas_ads_cpm_correctionv1",
                                 ks::ad_rank::UniverseRoasCorrectionContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_xiaodian_roas_unit_price",
                                 ks::ad_rank::UniverseXdRoasPriceLtvContainer)
  REGISTER_CONTAINER_HELPER_TASK("seller_ecology_info_merge",
                                 ks::ad_rank::EcomFullPhaseSellerDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("calibration_product_p2p_file",
                                 ks::ad_rank::SmartBiddingCalibrationContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_medium_wangzhuan",
                                 ks::ad_rank::UniverseMediumWangzhuanContainer)
  // REGISTER_CONTAINER_HELPER_TASK("universe_new_creative_data",
  //                                ks::ad_rank::UniverseNewCreativeDataContainer)
  // REGISTER_CONTAINER_HELPER_TASK("retention_calibration_p2p_data",  // 避免重复制作
  //                                 ks::ad_rank::UniverseRetentionRateIRContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_up_model_ecpc_stat",
                                 ks::ad_rank::UniverseUpModelEcpcStatContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_audience_deep_rate",
                                 ks::ad_rank::UniverseAudienceDeepRateContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_inner_cluster_photo_p2p",
                                 ks::ad_rank::UniverseInnerClusterDataContainer)
  REGISTER_CONTAINER_HELPER_TASK("union_joint_bidding_distribution_to_hdfs",
                                 ks::ad_rank::UniverseJointBiddingItemContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_medium_context_info",
      ks::ad_rank::UniverseMediumContextInfoContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "calibration_product_p2p_file_cmd",
      ks::ad_rank::SmartBiddingCalibrationCmdContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_ecpc_united_mcda_stat",
      ks::ad_rank::UniverseEcpcUnitedMcdaDataContainer)
  //  REGISTER_CONTAINER_HELPER_TASK(  // 避免重复制作
  //      "universe_heritage_offline_data_p2p",
  //      ks::ad_rank::UniverseHeritageOfflineDataContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_isplayable_pscvr_stat",
      ks::ad_rank::UniverseIsplayablePscvrDataContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_operation_boost_price",
      ks::ad_rank::UniverseOperationBoostPriceContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_mcda_key_model_ecpc_stat",
      ks::ad_rank::UniverseMcdaKeyModelEcpcDataContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_bonus", ks::ad_rank::UniverseAllowanceStrategyItemContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_bonus_push_to_adserver",
      ks::ad_rank::UniverseAllowanceStrategyItemContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_xiaodian_order_cate_price",
      ks::ad_rank::UniverseXdtOrderUnitCatePriceContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "fanstop_recruit_live_audience_ecpc_adjust",
      ks::ad_rank::FanstopRecruitLiveAudienceEcpcContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "union_next_metric_media_app_id_product_list",
      ks::ad_rank::UnionNextMetricMediaAppIdProductContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_direct_to_xdt_jicheng_p2p",
      ks::ad_rank::UniverseDirectRestartJichengDataContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "inner_original_cid_pval",
      ks::ad_rank::InnerOriginalCidPvalContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "calibration_product_p2p_file_conversion",
      ks::ad_rank::SmartBiddingCalibrationConversionContainer)
  REGISTER_CONTAINER_HELPER_TASK("ad_product_ocpx_external",
                                 ks::ad_server::ProductTagKeyContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_operation_dark_block",
      ks::ad_server::UniverseOperationDarkBlockContainer)
  REGISTER_CONTAINER_HELPER_TASK("expansion_unit", ks::ad_target::EspContainer)
  REGISTER_CONTAINER_HELPER_TASK("adue_unit_limit_info",
                                 ks::ad_target::UeUnitLimitContainer)
  REGISTER_CONTAINER_HELPER_TASK("prometheus_ecom_author_cv_p2p",
                                 ks::ad_target::EcomAuthorCvContainer)
  REGISTER_CONTAINER_HELPER_TASK("reco_photo_tag_dict",
                                 ks::ad_target::RecoPhotoTagContainer)
  REGISTER_CONTAINER_HELPER_TASK("adue_photo_limit_info",
                                 ks::ad_target::UePhotoLimitContainer)
  REGISTER_CONTAINER_HELPER_TASK("user_bid_adjust_to_target",
                                 ks::ad_target::BidAdJustInfoContainer)
  REGISTER_CONTAINER_HELPER_TASK("universe_prerank_ecpm_delivery",
                                 ks::ad_target::CpmCorrectionContainer)
  REGISTER_CONTAINER_HELPER_TASK("wechat_game_p2p",
                                 ks::ad_target::WechatGameP2pContainer)
  REGISTER_CONTAINER_HELPER_TASK("ecom_spu_cluster_to_amd_target",
                                 ks::ad_target::EcomSpuClusterContainer)
  REGISTER_CONTAINER_HELPER_TASK("jinniu_live_dead_unit",
                                 ks::ad_target::JinniuLiveDeadUnitContainer)
  REGISTER_CONTAINER_HELPER_TASK("ecom_photo_corporations_p2p",
                                 ks::ad_target::EcomPhotoCorporationContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_media_score_for_mcb",
      ks::ad_target::UniverseMediaQualityPosContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_operation_dark_deviation",
      ks::ad_target::DarkCastControlRatioBypassContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "univ_inner_live_reserve_retr",
      ks::ad_target::UniverseLiveReserveRetrP2pContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_inner_explore_strategy_p2p",
      ks::ad_target::UniverseInnerExploreCreativesContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_operation_green_channel",
      ks::ad_target::UniverseOperationGreenChannelContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_operation_orient_delivery_adstyle",
      ks::ad_target::UniverseOperationOrientDeliveryAdstyleContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_operation_orient_delivery_mingtou",
      ks::ad_target::UniverseOperationOrientDeliveryMingtouContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "ad_universe_posid_favourate_advertiser_industry2_v1",
      ks::ad_target::UniversePosidFavourateAdvertiserIndustry2Container)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_operation_orient_delivery_block",
      ks::ad_target::UniverseOperationOrientDeliveryBlockContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "archimedes_rank_params_data",
      ks::ad_rank::ArchimedesRealtimeRankParamsDataContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_operation_inner_green_channel",
      ks::ad_target::UniverseOperationInnerGreenChannelContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      *ks::engine_base::AdKconfUtil::autoCpaBidP2pName(),
      ks::engine_base::AutoBidContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "industry_live_ctcvr_calibration_cmd",
      ks::ad_rank::IndustryLiveBiddingCalibrationContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "wechat_game_user_calibration",
      ks::ad_rank::WeChatGameUserCalibrationContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "seven_day_paytimes_long_ratio",
      ks::ad_rank::SevenDayPaytimesLongRatioContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_flow_profile_apps_features_p2p",
      ks::ad_rank::UniverseFlowProfileAppsFeaturesContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_heritage_all_relations_p2p",
      ks::ad_rank::UniverseHeritageAllRelationsContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "universe_heritage_offline_data_p2p",
      ks::ad_rank::UniverseHeritageOfflineDataContainer)
  REGISTER_CONTAINER_HELPER_TASK(
      "playlet_visitor_calibration_ratio_map",
      ks::ad_rank::PlayletVisitorCalibrationRatioContainer)
  // 编译错误
  // REGISTER_CONTAINER_HELPER_TASK("small_scence_calibrate_data",
  //                                ks::ad_rank::SmallScenceCalibrateContainer)
  REGISTER_CONTAINER_HELPER_TASK("search_user_admit_loader",
                                 ks::engine_base::SearchUserAdmitContainer)
  //  REGISTER_CONTAINER_HELPER_TASK(  // 避免重复制作
  //      *::ad::data_push::DataPushKconfUtil::universeNewCreativeDataP2pName(),
  //      ks::ad_rank::UniverseNewCreativeDataContainer)
}

void RegisterAllTasks() {
  RegisterContainerHelperTasks();
  RegisterStandardTasks();
}

}  // namespace ad_data_format_server
}  // namespace ks
