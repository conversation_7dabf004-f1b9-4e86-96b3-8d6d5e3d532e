#include "base/common/base.h"
#include "framework/application.h"
#include "gflags/gflags.h"
#include "glog/logging.h"
#include "ks/serving_util/server_base.h"
#include "ks/serving_util/server_status.h"
#include "serving_base/utility/web_request_handler.h"
#include "teams/ad/ad_base/src/common/gracefull_exit.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/ksp/ad_web_request_handler.h"
#include "teams/ad/ad_base/src/ksp/dynamic_port.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_data_format_server/producer/producer_service.h"
#include "teams/ad/ad_data_format_server/producer/register_all_tasks.h"
#include "teams/ad/ad_data_format_server/producer/task_manager.h"
#include "teams/ad/ad_data_format_server/utils/data_check_plugin_manager/data_check_plugin_manager.h"

DEFINE_int32(web_thread_num, 1, "the number of threads for web service");
DEFINE_int32(web_server_port, 20080, "the port for web service");


namespace ks {
namespace ad_data_format_server {

void BackgroudThreadStart() {
  AD_KESS_CLIENT_INIT_CHECK("init error");
  ks::ad_base::KconfRedis::Instance().Warmup();
  DataCheckPluginManager::Instance();
  RegisterAllTasks();
  TaskManager::GetInstance().Start();
}

void BackgroupThreadStop() {
  TaskManager::GetInstance().Stop();
}

}  // namespace ad_data_format_server
}  // namespace ks

int main(int argc, char **argv) {
  base::InitApp(&argc, &argv, "ad_data_format_server");
  ks::serving_util::ServerStatus::Singleton()->SetStatusCode(
      ks::serving_util::ServerStatusCode::STARTING);
  LOG(INFO) << "Start ad_data_format_server";
  ks::ad_data_format_server::BackgroudThreadStart();
  // 启动 web service
  ks::ad_base::DefaultAdWebService web_service(FLAGS_web_thread_num);
  net::WebServer::Options web_server_option;
  web_server_option.port =
      ks::ad_base::DynamicPort::Instance().Port("AUTO_PORT0");
  web_server_option.backlog = 1024;
  if (web_server_option.port <= 0) {
    web_server_option.port = FLAGS_web_server_port;
    LOG(WARNING) << "may be not running in kcs mode! can't get port AUTO_PORT0 "
                 << "use gflag port " << FLAGS_web_server_port;
  }

  net::WebServer web_server(web_server_option, &web_service);
  web_server.Start();

  LOG(INFO) << "web server started";
  ks::serving_util::ServerStatus::Singleton()->SetStatusCode(
      ks::serving_util::ServerStatusCode::RUNNING);

  ks::kess::rpc::grpc::Service *kess_service = nullptr;
  kess_service = new ks::ad_data_format_server::ProducerService();
  ks::ad_base::AdKessClient &kess_instance =
      ks::ad_base::AdKessClient::Instance();
  kess_instance.StartService(kess_service, web_server_option.port);
  ks::ad_data_format_server::BackgroupThreadStop();
  kess_instance.StopService();

  ks::serving_util::ServerStatus::Singleton()->SetStatusCode(
      ks::serving_util::ServerStatusCode::STOPPING);
  ::google::FlushLogFiles(::google::INFO);
  ::google::ShutdownGoogleLogging();
  ks::serving_util::ServerStatus::Singleton()->SetStatusCode(
      ks::serving_util::ServerStatusCode::STOPPED);
  ks::ad_base::GracefulShutdown();
  return 0;
}
