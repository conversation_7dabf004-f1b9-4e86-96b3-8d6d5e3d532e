proto_library(
  name = "kconf_data_proto",
  srcs = [
    "./utils/kconf/kconf_data.proto",
  ],
)

proto_library(
  name = 'ad_data_format_server_proto',
  srcs = [
    "./proto/ad_data_format_server.proto"
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

cc_library(
  name = "data_check_plugin_manager",
  srcs = [
    "./utils/data_check_plugin_manager/*.cc"
  ],
  deps = [
    ":kconf_data_proto",
    "//teams/ad/ad_base/src/kconf/BUILD:kconf_node",
    "//infra/perfutil/BUILD:perfutil",
    "//infra/redis_proxy_client/BUILD:redis_client",
    "//teams/ad/ad_base/src/redis/BUILD:ad_redis",
  ]
)

cc_library(
  name = "zk_simple_client",
  srcs = [
    "./utils/zk_simple_client/zk_simple_client.cc"
  ],
  deps = [
    "//ks/serving_util/BUILD:serving_util",
  ]
)

cc_library(
  name = "producer_task_base",
  srcs = [
    "./producer/task_base/*.cc",
  ],
  deps = [
    ":kconf_data_proto",
    ":data_check_plugin_manager",
    "//base/file/BUILD:file",
    "//base/thread/BUILD:thread",
    "//serving_base/hdfs_read/BUILD:hdfs_read",
    "//teams/ad/ad_base/src/dot/BUILD:ad_base_dot",
    "//teams/ad/ad_base/src/file/BUILD:utility"
  ],
  cppflags = [
    '-Ithird_party/abseil/',
  ]
)

cc_library(
  name = "producer_task_manager",
  srcs = [
    "./producer/task_manager.cc",
  ],
  deps = [
    ":ad_data_format_server_proto",
    ":producer_task_base",
    ":zk_simple_client",
    "//teams/ad/ad_base/src/kess/BUILD:kess_client",
    "//teams/ad/ad_base/src/timer_task/BUILD:bg_task"
  ]
)

cc_library(
  name = "producer_register_all_tasks",
  srcs = [
    "./producer/register_all_tasks.cc",
  ],
  deps = [
    ":producer_task_manager",
    "//third_party/croaring/BUILD:croaring",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_prometheus_prometheus_photo_unit_data__proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_ssp_ad_ssp__proto",
    "//teams/ad/engine_base/BUILD:universe_operation_config_proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_prometheus_prometheus_cost_data__proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_engine_trace_log_ad_simplify_trace_log__proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_joint_labeled_log__proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_cmd_proxy_proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_tables_all__proto",
    "//teams/ad/ad_post_server/post_maker/BUILD:dict_proto",
    "//teams/ad/ad_rank/BUILD:kconf_proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_hosting__proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_charge_tag_proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_rank_service__proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_search_ads_item_producer__proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_bid_server_bid_server_account__proto",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_universe_posterior_statistic__proto",
    "//teams/ad/ad_proto/maven/BUILD:ad_coupon_ad_coupon_cache__proto",
    "//teams/ad/ad_post_server/common/BUILD:record_proto",
    "//teams/ad/ad_target/BUILD:kconf_proto",
    "//teams/ad/engine_base/BUILD:kconf",
    "//teams/ad/engine_base/BUILD:universe_live_ads_cpm_correction_v2",
    "//teams/ad/ad_target/BUILD:kconf",
    "//teams/ad/ad_rank/BUILD:kconf",
    "//teams/ad/ad_base/src/dot/BUILD:ad_base_dot",
    "//teams/ad/ad_counter/BUILD:kconf"
  ],
  cppflags = [
    "-Ithird_party/croaring/"
  ]
)

cc_library(
  name = "producer_service",
  srcs = [
    "./producer/producer_service.cc"
  ],
  deps = [
    ":producer_task_manager"
  ]
)

cc_library(
  name = "task_controller",
  srcs = [
    "./controller/*.cc",
  ],
  deps = [
    ":ad_data_format_server_proto",
    ":zk",
    '//teams/ad/ad_base/src/kess/BUILD:kess_client',
    '//teams/ad/ad_base/src/redis/BUILD:ad_redis',
    '//teams/ad/ad_base/src/dot/BUILD:ad_base_dot',
    '//ks/serving_util/BUILD:serving_util',
    '//infra/kess_grpc-v1100/BUILD:kess-rpc',
    '//infra/kess_grpc-v1100/BUILD:kess-framework',
    '//third_party/abseil/BUILD:abseil',
    '//third_party/grpc-v1100/BUILD:grpc++_reflection',
  ]
)

cc_library(
  name = "zk",
  srcs = [
    "./utils/zk/*.cc",
  ],
  deps = [
    "//teams/ad/ad_base/src/common/BUILD:zk_leader_selector",
    "//teams/ad/ad_base/src/kess/BUILD:kess_client",
  ]
)

cc_binary(
  name = "ad_data_format_producer",
  srcs = [
    "./producer/ad_data_format_producer.cc"
  ],
  deps = [
    ":kconf_data_proto",
    ":producer_service",
    ":producer_register_all_tasks",
    "//ks/serving_util/BUILD:serving_util",
    "//serving_base/utility/BUILD:signal",
    "//teams/ad/ad_base/src/kconf/BUILD:kconf_node",
    "//teams/ad/ad_base/src/ksp/BUILD:ksp"
  ]
)

cc_binary(
  name = "ad_data_format_controller",
  srcs = [
    "./controller/ad_data_format_controller.cc"
  ],
  deps = [
    ":kconf_data_proto",
    ":task_controller",
    "//ks/serving_util/BUILD:serving_util",
    "//serving_base/utility/BUILD:signal",
    "//teams/ad/ad_base/src/kconf/BUILD:kconf_node",
    "//teams/ad/ad_base/src/ksp/BUILD:ksp",
    "//teams/ad/data_push/BUILD:p2p_data_push_client_lib",
  ]
)

cc_binary(
  name = "test",
  srcs = [
    "test/test.cc",
  ],
  deps = [
    ":producer_register_all_tasks",
  ]
)

cc_test(
  name = "data_check_plugin_test",
  srcs = [
    "./utils/data_check_plugin_manager/unitest/data_check_plugin_test.cc",
  ],
  deps = [
    ":ad_data_format_server_proto",
    ":kconf_data_proto",
    ":data_check_plugin_manager",
    "//third_party/gtest/BUILD:gtest",
    "//teams/ad/ad_base/src/dot/BUILD:ad_base_dot",
  ]
)