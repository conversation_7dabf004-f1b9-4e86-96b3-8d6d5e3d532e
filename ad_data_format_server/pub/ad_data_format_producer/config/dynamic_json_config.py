# -*- coding: UTF8 -*-
import json

config = {
  # 基于 grpc 的 tcpcopy, 可以设置 dryrun 环境
  "grpc_tcp_copy" : {
    "default" : "online",
    #"ad-rs171.idcgz1.hn1.kwaidc.com" : "dryrun_tcpcopy_receiver",   # dryrun
  },
  "grpc" : {
    "server" : {
      "kess_name" : "grpc_adDataFormatProducerService",  # kess_name 可以自动扩展为 kess_name + "_" + ksp 分组, default 不扩展
      "port" : 20083,
      "kcs_grpc_port_key" : "AUTO_PORT1",  # 支持服务部署到容器上， 此时 port 参数失效
      "thread_num": 250,  ## default cpu num
      "quit_wait_seconds" : 85,
      "start_warmup_seconds" : 40, #默认 10s
    },
    "client_map" : {
      "ad_data_format_controller_client" : "grpc_adDataFormatControllerService"
    }
  },
  "zk_leader": {
    "zk_host": "ad.zk.cluster.zw:2181",
    "zk_path": "/ks2/ad/adCounter/ad_data_format_server"
  }
}

if __name__ == "__main__":
  print json.dumps(config, indent=2, sort_keys=True);
