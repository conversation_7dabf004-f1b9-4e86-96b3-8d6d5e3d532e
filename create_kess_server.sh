#!/bin/bash

if [ $# -lt 4 ]; then
  echo "USAGE:: $0 SERVER_NAME[服务名] SERVICE_PROTO_NAME[PROTO文件名] SERVICE_NAME[PROTO定义的ServiceName] SERVER_KESS_NAME[kess系统注册名]"
  exit 1
fi
SOURCE_DIR="kess_server_base"
WORK_DIR="$1"


if [ -d $WORK_DIR ]; then
  echo "ERROR: $1 has exit!!!"
  exit 1
fi

# 复制模板，创建工程目录
cp -R $SOURCE_DIR $WORK_DIR
cd $WORK_DIR
sed -i "s/{{SERVER_NAME}}/"$1"/g" `grep {{SERVER_NAME}} -rl *`
sed -i "s/{{SERVICE_PROTO_NAME}}/"$2"/g" `grep {{SERVICE_PROTO_NAME}} -rl *`
sed -i "s/{{SERVICE_NAME}}/"$3"/g" `grep {{SERVICE_NAME}} -rl *`
sed -i "s/{{SERVER_KESS_NAME}}/"$4"/g" `grep {{SERVER_KESS_NAME}} -rl *`

server_name=$1
mv kess_server.cc $server_name.cc
mv pub/kess_server pub/$server_name
cd pub/$server_name/bin
ln -s ../../../../../../.build/opt/targets/teams/ad/$server_name/$server_name $server_name

echo "Create $server_name Success!!!"
