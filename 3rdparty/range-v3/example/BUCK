cxx_binary(
  name = 'comprehensions',
  srcs = [
    'comprehensions.cpp',
  ],
  deps = [
    '//:range-v3',
  ],
  compiler_flags = [
    '-std=c++14',
  ],
)

cxx_binary(
  name = 'count_if',
  srcs = [
    'count_if.cpp',
  ],
  deps = [
    '//:range-v3',
  ],
  compiler_flags = [
    '-std=c++14',
  ],
)

cxx_binary(
  name = 'count',
  srcs = [
    'count.cpp',
  ],
  deps = [
    '//:range-v3',
  ],
  compiler_flags = [
    '-std=c++14',
  ],
)

cxx_binary(
  name = 'find',
  srcs = [
    'find.cpp',
  ],
  deps = [
    '//:range-v3',
  ],
  compiler_flags = [
    '-std=c++14',
  ],
)

cxx_binary(
  name = 'for_each_assoc',
  srcs = [
    'for_each_assoc.cpp',
  ],
  deps = [
    '//:range-v3',
  ],
  compiler_flags = [
    '-std=c++14',
  ],
)

cxx_binary(
  name = 'for_each_sequence',
  srcs = [
    'for_each_sequence.cpp',
  ],
  deps = [
    '//:range-v3',
  ],
  compiler_flags = [
    '-std=c++14',
  ],
)

cxx_binary(
  name = 'hello',
  srcs = [
    'hello.cpp',
  ],
  deps = [
    '//:range-v3',
  ],
  compiler_flags = [
    '-std=c++14',
  ],
)

cxx_binary(
  name = 'is_sorted',
  srcs = [
    'is_sorted.cpp',
  ],
  deps = [
    '//:range-v3',
  ],
  compiler_flags = [
    '-std=c++14',
  ],
)

cxx_binary(
  name = 'filter_transform',
  srcs = [
    'filter_transform.cpp',
  ],
  deps = [
    '//:range-v3',
  ],
  compiler_flags = [
    '-std=c++14',
  ],
)

cxx_binary(
  name = 'accumulate_ints',
  srcs = [
    'accumulate_ints.cpp',
  ],
  deps = [
    '//:range-v3',
  ],
  compiler_flags = [
    '-std=c++14',
  ],
)

cxx_binary(
  name = 'comprehension_conversion',
  srcs = [
    'comprehension_conversion.cpp',
  ],
  deps = [
    '//:range-v3',
  ],
  compiler_flags = [
    '-std=c++14',
  ],
)

cxx_binary(
  name = 'sort_unique',
  srcs = [
    'sort_unique.cpp',
  ],
  deps = [
    '//:range-v3',
  ],
  compiler_flags = [
    '-std=c++14',
  ],
)
