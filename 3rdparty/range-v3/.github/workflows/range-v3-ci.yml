name: range-v3 CI

# Trigger on pushes to all branches and for all pull-requests
on: [push, pull_request]

env:
  CMAKE_VERSION: 3.16.2
  NINJA_VERSION: 1.9.0

jobs:
  build:
    name: ${{ matrix.config.name }}
    runs-on: ${{ matrix.config.os }}
    strategy:
      fail-fast: false
      matrix:
        config:
        # GCC-6
        - {
            name: "Linux GCC 6 Debug (C++14)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "gcc-6", cxx: "g++-6",
            cxx_standard: 14,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 6 Release (C++14)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-6", cxx: "g++-6",
            cxx_standard: 14,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 6 Debug (C++17)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "gcc-6", cxx: "g++-6",
            cxx_standard: 17,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 6 Release (C++17)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-6", cxx: "g++-6",
            cxx_standard: 17,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 6 Release (C++17, Concepts)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-6", cxx: "g++-6",
            cxx_standard: 17,
          }

        # GCC-7
        - {
            name: "Linux GCC 7 Debug (C++14)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "gcc-7", cxx: "g++-7",
            cxx_standard: 14,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 7 Release (C++14)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-7", cxx: "g++-7",
            cxx_standard: 14,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 7 Debug (C++17)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "gcc-7", cxx: "g++-7",
            cxx_standard: 17,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 7 Release (C++17)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-7", cxx: "g++-7",
            cxx_standard: 17,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 7 Release (C++17, Concepts)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-7", cxx: "g++-7",
            cxx_standard: 17,
          }

        # GCC-8
        - {
            name: "Linux GCC 8 Debug (C++14)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "gcc-8", cxx: "g++-8",
            cxx_standard: 14,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 8 Release (C++14)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-8", cxx: "g++-8",
            cxx_standard: 14,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 8 Debug (C++17)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "gcc-8", cxx: "g++-8",
            cxx_standard: 17,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 8 Release (C++17)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-8", cxx: "g++-8",
            cxx_standard: 17,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 8 Release (C++17, Concepts)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-8", cxx: "g++-8",
            cxx_standard: 17,
          }

        # GCC-9
        - {
            name: "Linux GCC 9 Debug (C++17)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "gcc-9", cxx: "g++-9",
            cxx_standard: 17,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 9 Release (C++17)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-9", cxx: "g++-9",
            cxx_standard: 17,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 9 Debug (C++20)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "gcc-9", cxx: "g++-9",
            cxx_standard: 20,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 9 Release (C++20)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-9", cxx: "g++-9",
            cxx_standard: 20,
            cxx_concepts: false
          }
        - {
            name: "Linux GCC 9 Release (C++20, Concepts)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "gcc-9", cxx: "g++-9",
            cxx_standard: 20,
          }

        # Clang-5.0
        - {
            name: "Linux Clang 5.0 Debug (C++14 / libc++ / ASAN)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "clang-5.0", cxx: "clang++-5.0",
            cxx_standard: 14,
            cxx_asan: true,
            libcxx: true
          }
        - {
            name: "Linux Clang 5.0 Debug (C++17 / ASAN)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "clang-5.0", cxx: "clang++-5.0",
            cxx_standard: 17,
            cxx_asan: true,
          }
        - {
            name: "Linux Clang 5.0 Debug (C++20 / ASAN)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "clang-5.0", cxx: "clang++-5.0",
            cxx_standard: 20,
            cxx_asan: true,
          }

        # Clang-6.0
        - {
            name: "Linux Clang 6.0 Debug (C++14 / libc++ / ASAN)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "clang-6.0", cxx: "clang++-6.0",
            cxx_standard: 14,
            cxx_asan: true,
            libcxx: true
          }
        - {
            name: "Linux Clang 6.0 Debug (C++17 / ASAN)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "clang-6.0", cxx: "clang++-6.0",
            cxx_standard: 17,
            cxx_asan: true,
          }
        - {
            name: "Linux Clang 6.0 Debug (C++20 / ASAN)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "clang-6.0", cxx: "clang++-6.0",
            cxx_standard: 20,
            cxx_asan: true,
          }

        # Clang-8
        - {
            name: "Linux Clang 8 Debug (C++14 / libc++ / ASAN)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "clang-8", cxx: "clang++-8",
            cxx_standard: 14,
            cxx_asan: true,
            libcxx: true
          }
        - {
            name: "Linux Clang 8 Debug (C++17 / ASAN)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "clang-8", cxx: "clang++-8",
            cxx_standard: 17,
            cxx_asan: true,
          }
        - {
            name: "Linux Clang 8 Debug (C++20 / ASAN)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "clang-8", cxx: "clang++-8",
            cxx_standard: 20,
            cxx_asan: true,
          }

        # Clang-9
        - {
            name: "Linux Clang 9 Debug (C++17 / ASAN)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "clang-9", cxx: "clang++-9",
            cxx_standard: 17,
            cxx_asan: true,
          }
        - {
            name: "Linux Clang 9 Release (C++17)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "clang-9", cxx: "clang++-9",
            cxx_standard: 17,
          }
        - {
            name: "Linux Clang 9 Debug (C++20 / ASAN)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "clang-9", cxx: "clang++-9",
            cxx_standard: 20,
            cxx_asan: true,
          }
        - {
            name: "Linux Clang 9 Release (C++20)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "clang-9", cxx: "clang++-9",
            cxx_standard: 20,
          }

        # Clang-10
        - {
            name: "Linux Clang 10 Debug (C++20 / ASAN)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: Debug,
            cc: "clang-10", cxx: "clang++-10",
            cxx_standard: 20,
            cxx_asan: true,
            cxx_concepts: false
          }
        - {
            name: "Linux Clang 10 Release (C++20 / Concepts)", artifact: "Linux.tar.xz",
            os: ubuntu-latest,
            build_type: RelWithDebInfo,
            cc: "clang-10", cxx: "clang++-10",
            cxx_standard: 20,
          }

        # AppleClang
        - {
            name: "macOS Clang Debug (C++17)", artifact: "macOS.tar.xz",
            os: macos-latest,
            build_type: Debug,
            cc: "clang", cxx: "clang++",
            cxx_standard: 17,
            cxx_asan: true,
          }
        - {
            name: "macOS Clang Release (C++17)", artifact: "macOS.tar.xz",
            os: macos-latest,
            build_type: RelWithDebInfo,
            cc: "clang", cxx: "clang++",
            cxx_standard: 17,
          }
        - {
            name: "macOS Clang Debug (C++20 / ASAN)", artifact: "macOS.tar.xz",
            os: macos-latest,
            build_type: Debug,
            cc: "clang", cxx: "clang++",
            cxx_standard: 20,
            cxx_asan: true,
          }
        - {
            name: "macOS Clang Release (C++20)", artifact: "macOS.tar.xz",
            os: macos-latest,
            build_type: RelWithDebInfo,
            cc: "clang", cxx: "clang++",
            cxx_standard: 20,
          }

        # MSVC 2019
        - {
            name: "Windows MSVC 2019 Debug (C++17)", artifact: "Windows-MSVC.tar.xz",
            os: windows-latest,
            build_type: Debug,
            cc: "cl", cxx: "cl",
            environment_script: "C:/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/VC/Auxiliary/Build/vcvars64.bat",
            cxx_standard: 17,
          }
        - {
            name: "Windows MSVC 2019 Release (C++17)", artifact: "Windows-MSVC.tar.xz",
            os: windows-latest,
            build_type: RelWithDebInfo,
            cc: "cl", cxx: "cl",
            environment_script: "C:/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/VC/Auxiliary/Build/vcvars64.bat",
            cxx_standard: 17,
          }
        - {
            name: "Windows MSVC 2019 Debug (C++20)", artifact: "Windows-MSVC.tar.xz",
            os: windows-latest,
            build_type: Debug,
            cc: "cl", cxx: "cl",
            environment_script: "C:/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/VC/Auxiliary/Build/vcvars64.bat",
            cxx_standard: 20,
          }
        - {
            name: "Windows MSVC 2019 Release (C++20)", artifact: "Windows-MSVC.tar.xz",
            os: windows-latest,
            build_type: RelWithDebInfo,
            cc: "cl", cxx: "cl",
            environment_script: "C:/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/VC/Auxiliary/Build/vcvars64.bat",
            cxx_standard: 20,
          }

    steps:
    - uses: actions/checkout@v1

    - name: Download Ninja and CMake
      id: cmake_and_ninja
      shell: cmake -P {0}
      run: |
        set(cmake_version $ENV{CMAKE_VERSION})
        set(ninja_version $ENV{NINJA_VERSION})

        message(STATUS "Using host CMake version: ${CMAKE_VERSION}")

        if ("${{ runner.os }}" STREQUAL "Windows")
          set(ninja_suffix "win.zip")
          set(cmake_suffix "win64-x64.zip")
          set(cmake_dir "cmake-${cmake_version}-win64-x64/bin")
        elseif ("${{ runner.os }}" STREQUAL "Linux")
          set(ninja_suffix "linux.zip")
          set(cmake_suffix "Linux-x86_64.tar.gz")
          set(cmake_dir "cmake-${cmake_version}-Linux-x86_64/bin")
        elseif ("${{ runner.os }}" STREQUAL "macOS")
          set(ninja_suffix "mac.zip")
          set(cmake_suffix "Darwin-x86_64.tar.gz")
          set(cmake_dir "cmake-${cmake_version}-Darwin-x86_64/CMake.app/Contents/bin")
        endif()

        set(ninja_url "https://github.com/ninja-build/ninja/releases/download/v${ninja_version}/ninja-${ninja_suffix}")
        file(DOWNLOAD "${ninja_url}" ./ninja.zip SHOW_PROGRESS)
        execute_process(COMMAND ${CMAKE_COMMAND} -E tar xvf ./ninja.zip)

        set(cmake_url "https://github.com/Kitware/CMake/releases/download/v${cmake_version}/cmake-${cmake_version}-${cmake_suffix}")
        file(DOWNLOAD "${cmake_url}" ./cmake.zip SHOW_PROGRESS)
        execute_process(COMMAND ${CMAKE_COMMAND} -E tar xvf ./cmake.zip)

        # Save the path for other steps
        file(TO_CMAKE_PATH "$ENV{GITHUB_WORKSPACE}/${cmake_dir}" cmake_dir)
        message("::set-output name=cmake_dir::${cmake_dir}")

        if (NOT "${{ runner.os }}" STREQUAL "Windows")
          execute_process(
            COMMAND chmod +x ninja
            COMMAND chmod +x ${cmake_dir}/cmake
          )
        endif()

    - name: Install GCC 6
      id: install_gcc_6
      if: startsWith(matrix.config.os, 'ubuntu') && ( matrix.config.cxx == 'g++-6' )
      shell: bash
      working-directory: ${{ env.HOME }}
      run: |
        sudo apt-get install -y gcc-6 g++-6

    - name: Install Clang 5
      id: install_clang_5
      if: startsWith(matrix.config.os, 'ubuntu') && ( matrix.config.cxx == 'clang++-5.0' )
      shell: bash
      working-directory: ${{ env.HOME }}
      run: |
        wget -O - https://apt.llvm.org/llvm-snapshot.gpg.key 2>/dev/null | sudo apt-key add -
        sudo add-apt-repository 'deb http://apt.llvm.org/bionic/ llvm-toolchain-bionic-5.0 main' -y
        sudo apt-get update -q
        sudo apt-get install -y clang-5.0

    - name: Install Clang 10
      id: install_clang_10
      if: startsWith(matrix.config.os, 'ubuntu') && ( matrix.config.cxx == 'clang++-10' )
      shell: bash
      working-directory: ${{ env.HOME }}
      run: |
        wget https://apt.llvm.org/llvm.sh
        chmod +x llvm.sh
        sudo ./llvm.sh 10

    - name: Install libc++
      id: install_libcxx
      if: matrix.config.libcxx
      shell: bash
      working-directory: ${{ env.HOME }}
      env:
        CC: ${{ matrix.config.cc }}
        CXX: ${{ matrix.config.cxx }}
      run: |
        $GITHUB_WORKSPACE/install_libcxx.sh

    - name: Configure
      shell: cmake -P {0}
      run: |
        set(ENV{CC} ${{ matrix.config.cc }})
        set(ENV{CXX} ${{ matrix.config.cxx }})

        if ("${{ runner.os }}" STREQUAL "Windows" AND NOT "x${{ matrix.config.environment_script }}" STREQUAL "x")
          execute_process(
            COMMAND "${{ matrix.config.environment_script }}" && set
            OUTPUT_FILE environment_script_output.txt
          )
          set(cxx_flags "/permissive- /EHsc")
          file(STRINGS environment_script_output.txt output_lines)
          foreach(line IN LISTS output_lines)
            if (line MATCHES "^([a-zA-Z0-9_-]+)=(.*)$")
              set(ENV{${CMAKE_MATCH_1}} "${CMAKE_MATCH_2}")
            endif()
          endforeach()
        endif()

        set(path_separator ":")
        if ("${{ runner.os }}" STREQUAL "Windows")
          set(path_separator ";")
        endif()
        set(ENV{PATH} "$ENV{GITHUB_WORKSPACE}${path_separator}$ENV{PATH}")

        if ("x${{ matrix.config.libcxx }}" STREQUAL "xtrue")
          set(cxx_flags "${cxx_flags} -stdlib=libc++ -nostdinc++ -cxx-isystem $ENV{GITHUB_WORKSPACE}/llvm/include/c++/v1/ -Wno-unused-command-line-argument")
          set(link_flags "${link_flags} -L $ENV{GITHUB_WORKSPACE}/llvm/lib -Wl,-rpath,$ENV{GITHUB_WORKSPACE}/llvm/lib -lc++abi")
        endif()

        if ("x${{ matrix.config.cxx_asan }}" STREQUAL "xtrue")
          set(cxx_flags "${cxx_flags} -fsanitize=address -fno-omit-frame-pointer")
        endif()

        set(cxx_concepts ON)
        if ("x${{ matrix.config.cxx_concepts }}" STREQUAL "xfalse")
          set(cxx_concepts OFF)
        endif()

        execute_process(
          COMMAND ${{ steps.cmake_and_ninja.outputs.cmake_dir }}/cmake
            -S .
            -B build
            -G Ninja
            -D CMAKE_BUILD_TYPE=${{ matrix.config.build_type }}
            -D CMAKE_MAKE_PROGRAM:STRING=ninja
            -D CMAKE_CXX_STANDARD:STRING=${{ matrix.config.cxx_standard }}
            -D "CMAKE_CXX_FLAGS:STRING=${cxx_flags}"
            -D "CMAKE_EXE_LINKER_FLAGS:STRING=${link_flags}"
            -D CMAKE_VERBOSE_MAKEFILE:BOOL=ON
            -D RANGE_V3_HEADER_CHECKS:BOOL=ON
            -D RANGES_PREFER_REAL_CONCEPTS:BOOL=${cxx_concepts}
            ${{ matrix.config.cmake_args }}
            ${extra_cmake_args}
          RESULT_VARIABLE result
        )
        if (NOT result EQUAL 0)
          message(FATAL_ERROR "Bad exit status")
        endif()

    - name: Build
      shell: cmake -P {0}
      continue-on-error: ${{ matrix.config.experimental || false }}
      run: |
        set(ENV{NINJA_STATUS} "[%f/%t %o/sec] ")

        if ("${{ runner.os }}" STREQUAL "Windows" AND NOT "x${{ matrix.config.environment_script }}" STREQUAL "x")
          file(STRINGS environment_script_output.txt output_lines)
          foreach(line IN LISTS output_lines)
            if (line MATCHES "^([a-zA-Z0-9_-]+)=(.*)$")
              set(ENV{${CMAKE_MATCH_1}} "${CMAKE_MATCH_2}")
            endif()
          endforeach()
        endif()

        set(path_separator ":")
        if ("${{ runner.os }}" STREQUAL "Windows")
          set(path_separator ";")
        endif()
        set(ENV{PATH} "$ENV{GITHUB_WORKSPACE}${path_separator}$ENV{PATH}")

        execute_process(
          COMMAND ${{ steps.cmake_and_ninja.outputs.cmake_dir }}/cmake --build build
          RESULT_VARIABLE result
        )
        if (NOT result EQUAL 0)
          message(FATAL_ERROR "Bad exit status")
        endif()

    - name: Run tests
      shell: cmake -P {0}
      continue-on-error: ${{ matrix.config.experimental || false }}
      run: |
        include(ProcessorCount)
        ProcessorCount(N)

        set(ENV{CTEST_OUTPUT_ON_FAILURE} "ON")

        execute_process(
          COMMAND ${{ steps.cmake_and_ninja.outputs.cmake_dir }}/ctest --verbose -j ${N}
          WORKING_DIRECTORY build
          RESULT_VARIABLE result
        )
        if (NOT result EQUAL 0)
          message(FATAL_ERROR "Running tests failed!")
        endif()
