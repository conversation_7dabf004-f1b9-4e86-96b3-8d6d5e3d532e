Examples
========

\section example-algorithms Examples: Algorithms

\subsection example-hello Hello, Ranges!

\snippet hello.cpp hello

\subsection example-any-all-none any_of, all_of, none_of

\snippet any_all_none_of.cpp any_all_none_of

\subsection example-count count

\snippet count.cpp count

\subsection example-count_if count_if

\snippet count_if.cpp count_if

\subsection example-find find, find_if, find_if_not on sequence containers

\snippet find.cpp find

\subsection example-for_each-seq for_each on sequence containers

\snippet for_each_sequence.cpp for_each_sequence

\subsection example-for_each-assoc for_each on associative containers

\snippet for_each_assoc.cpp for_each_assoc

\subsection example-is_sorted is_sorted

\snippet is_sorted.cpp is_sorted

\section example-views Examples: Views

\subsection example-filter-transform Filter and transform

\snippet filter_transform.cpp filter_transform

\subsection example-accumulate-ints Generate ints and accumulate

\snippet accumulate_ints.cpp accumulate_ints

\subsection example-comprehension-conversion Convert a range comprehension to a vector

\snippet comprehension_conversion.cpp comprehension_conversion

\section example-actions Examples: Actions

\subsection example-sort-unique Remove non-unique elements from a container

\snippet sort_unique.cpp sort_unique

\section example-gestalt Examples: Putting it all together

\subsection example-calendar Calendar

\snippet calendar.cpp calendar
