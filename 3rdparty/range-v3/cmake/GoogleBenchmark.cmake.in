# Copyright <PERSON> 2019
# Distributed under the Boost Software License, Version 1.0.
# (See accompanying file LICENSE.md or copy at http://boost.org/LICENSE_1_0.txt)

cmake_minimum_required(VERSION 2.8.2)

project(google-benchmark-download NONE)

include(ExternalProject)
ExternalProject_Add(google-benchmark
  GIT_REPOSITORY    https://github.com/google/benchmark.git
  GIT_TAG           master
  SOURCE_DIR        "${CMAKE_CURRENT_BINARY_DIR}/google-benchmark-src"
  BINARY_DIR        "${CMAKE_CURRENT_BINARY_DIR}/google-benchmark-build"
  CONFIGURE_COMMAND ""
  BUILD_COMMAND     ""
  INSTALL_COMMAND   ""
  TEST_COMMAND      ""
)
