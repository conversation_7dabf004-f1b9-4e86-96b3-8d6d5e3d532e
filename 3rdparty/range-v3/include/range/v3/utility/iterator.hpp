// Range v3 library
//
//  Copyright <PERSON> 2013-present
//
//  Use, modification and distribution is subject to the
//  Boost Software License, Version 1.0. (See accompanying
//  file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt)
//
// Project home: https://github.com/ericniebler/range-v3
//

#ifndef RANGES_V3_UTILITY_ITERATOR_HPP
#define RANGES_V3_UTILITY_ITERATOR_HPP

#include <range/v3/detail/config.hpp>
RANGES_DEPRECATED_HEADER(
    R"(This header is deprecated. Please include one of the following depending on your need:
    <range/v3/iterator/operations.hpp>,
    <range/v3/iterator/insert_iterators.hpp>,
    <range/v3/iterator/move_iterators.hpp>,
    <range/v3/iterator/reverse_iterator.hpp>,
    <range/v3/iterator/stream_iterators.hpp>)")

#include <range/v3/iterator/insert_iterators.hpp>
#include <range/v3/iterator/move_iterators.hpp>
#include <range/v3/iterator/operations.hpp>
#include <range/v3/iterator/reverse_iterator.hpp>
#include <range/v3/iterator/stream_iterators.hpp>

#endif
