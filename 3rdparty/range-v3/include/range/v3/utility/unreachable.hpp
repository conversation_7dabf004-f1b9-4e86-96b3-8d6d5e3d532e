// Range v3 library
//
//  Copyright <PERSON> 2014-present
//
//  Use, modification and distribution is subject to the
//  Boost Software License, Version 1.0. (See accompanying
//  file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt)
//
// Project home: https://github.com/ericniebler/range-v3
//
#ifndef RANGES_V3_UTILITY_UNREACHABLE_HPP
#define RANGES_V3_UTILITY_UNREACHABLE_HPP

#include <range/v3/detail/config.hpp>
RANGES_DEPRECATED_HEADER(
    "This header is deprecated. Please #include "
    "<range/v3/iterator/unreachable_sentinel.hpp> instead.")

#include <range/v3/iterator/unreachable_sentinel.hpp>

#endif
