// Copyright (c) 2016-2021 <PERSON>
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef PFR_HPP
#define PFR_HPP

/// \file pfr.hpp
/// Includes all the Boost.PFR headers

#include <pfr/core.hpp>
#include <pfr/functions_for.hpp>
#include <pfr/functors.hpp>
#include <pfr/io.hpp>
#include <pfr/io_fields.hpp>
#include <pfr/ops.hpp>
#include <pfr/ops_fields.hpp>
#include <pfr/tuple_size.hpp>

#endif // PFR_HPP
