// Copyright (c) 2016-2021 <PERSON>
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef PFR_DETAIL_CONFIG_HPP
#define PFR_DETAIL_CONFIG_HPP
#pragma once

#include <type_traits> // to get non standard platform macro definitions (__GLIBCXX__ for example)

// Reminder:
//  * MSVC++ 14.2 _MSC_VER == 1927 <- Loophole is known to work (Visual Studio ????)
//  * MSVC++ 14.1 _MSC_VER == 1916 <- Loophole is known to NOT work (Visual Studio 2017)
//  * MSVC++ 14.0 _MSC_VER == 1900 (Visual Studio 2015)
//  * MSVC++ 12.0 _MSC_VER == 1800 (Visual Studio 2013)

#if defined(_MSC_VER)
#   if !defined(_MSVC_LANG) || _MSC_VER <= 1900
#       error Boost.PFR library requires more modern MSVC compiler.
#   endif
#elif __cplusplus < 201402L
#   error Boost.PFR library requires at least C++14.
#endif

#ifndef PFR_USE_LOOPHOLE
#   if defined(_MSC_VER)
#       if _MSC_VER >= 1927
#           define PFR_USE_LOOPHOLE 1
#       else
#           define PFR_USE_LOOPHOLE 0
#       endif
#   elif defined(__clang_major__) && __clang_major__ >= 8
#       define PFR_USE_LOOPHOLE 0
#   else
#       define PFR_USE_LOOPHOLE 1
#   endif
#endif

#ifndef PFR_USE_CPP17
#   ifdef __cpp_structured_bindings
#       define PFR_USE_CPP17 1
#   elif defined(_MSVC_LANG)
#       if _MSVC_LANG >= 201703L
#           define PFR_USE_CPP17 1
#       else
#           define PFR_USE_CPP17 0
#       endif
#   else
#       define PFR_USE_CPP17 0
#   endif
#endif

#if (!PFR_USE_CPP17 && !PFR_USE_LOOPHOLE)
#   if (defined(_MSC_VER) && _MSC_VER < 1916) ///< in Visual Studio 2017 v15.9 PFR library with classic engine normally works
#      error Boost.PFR requires /std:c++latest or /std:c++17 flags on your compiler.
#   endif
#endif

#ifndef PFR_USE_STD_MAKE_INTEGRAL_SEQUENCE
// Assume that libstdc++ since GCC-7.3 does not have linear instantiation depth in std::make_integral_sequence
#   if defined( __GLIBCXX__) && __GLIBCXX__ >= 20180101
#       define PFR_USE_STD_MAKE_INTEGRAL_SEQUENCE 1
#   elif defined(_MSC_VER)
#       define PFR_USE_STD_MAKE_INTEGRAL_SEQUENCE 1
//# elif other known working lib
#   else
#       define PFR_USE_STD_MAKE_INTEGRAL_SEQUENCE 0
#   endif
#endif

#ifndef PFR_HAS_GUARANTEED_COPY_ELISION
#   if  defined(__cpp_guaranteed_copy_elision) && (!defined(_MSC_VER) || _MSC_VER > 1928)
#       define PFR_HAS_GUARANTEED_COPY_ELISION 1
#   else
#       define PFR_HAS_GUARANTEED_COPY_ELISION 0
#   endif
#endif

#if defined(__has_cpp_attribute)
#   if __has_cpp_attribute(maybe_unused)
#       define PFR_MAYBE_UNUSED [[maybe_unused]]
#   endif
#endif

#ifndef PFR_MAYBE_UNUSED
#   define PFR_MAYBE_UNUSED
#endif


#endif // PFR_DETAIL_CONFIG_HPP
