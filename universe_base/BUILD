import os

cc_library(
  name = "kconf",
  srcs = [
    "kconf/*.cc"
  ],
  excludes = [
    "kconf/kconf.cc"
  ],
  deps = [
    ":kconf_proto",
    "//teams/ad/ad_base/src/kconf/BUILD:kconf_node",
    "//teams/ad/ad_base/src/geohash/BUILD:location"
  ],
  cppflags = [
    '-Iinfra/location/src/'
  ],
)

proto_library(
  name = "kconf_proto",
  srcs = [
    "kconf/kconf_data.proto"
  ]
)

cc_library(
    name = 'universe_aggre_task',
    srcs = [
      'universe_aggre_task/universe_aggre_task_request_ps.cc',
      'universe_aggre_task/aggre_task_manager.cc',
      'universe_aggre_task/aggre_task_uilt.cc',
      "kconf/*.cc",
      "utility/*.cc",
      "utility/degrade_manager/*.cc",
      "union_mid_page/union_mid_page_util.cc"
    ],
    deps = [
      ':kconf_proto',
      "//base/time/BUILD:time",
      "//base/strings/BUILD:strings",
      "//base/hash_function/BUILD:hash_function",
      "//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_service_ad_predict_service__proto",
      '//teams/ad/ad_base/src/kess/BUILD:kess_client',
      "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_task_log__proto",
      '//teams/ad/ad_base/src/redis/BUILD:ad_redis',
      '//teams/ad/ad_base/src/kess/BUILD:kess_client',
      '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_forward_index__proto',
      "//infra/redis_proxy_client/BUILD:redis_client",
   ],
   cppflags = [
     '-Iinfra/location/src/',
   ],
)

