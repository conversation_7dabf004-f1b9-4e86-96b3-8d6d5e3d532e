{"_by_ab_test_group_config": {"_default": {}}, "_strategy": {"strategies": []}, "ack_rank_info_sample_rate": 0.2, "ad_dsp_common_zk_config": {"max_connections_per_proxy": 5, "redis_timeout_mills": 10, "zk_path": "config.zk.cluster.zw:2181:/ks2/redis/adDspCommon/_ZW"}, "ad_trace_always_log_producer_config": {"topic": "ad_trace_always_log", "zk_path": "/kuaishou/kafkaLogs/producers/ZL_COMMON1"}, "ad_trace_log_producer_ad_server_config": {"topic": "ad_trace_log_ad_server", "zk_path": "/kuaishou/kafkaLogs/producers/LF_LOG1"}, "ad_trace_log_producer_ad_target_config": {"topic": "ad_trace_log_target", "zk_path": "/kuaishou/kafkaLogs/producers/LF_LOG1"}, "ad_trace_log_producer_config": {"topic": "ad_trace_log", "zk_path": "/kuaishou/kafkaLogs/producers/LF_LOG1"}, "adx_photo_update_interval_seconds": 120, "algo_embedding_redis_config": {"creative_key_prefix": "adrec2_", "redis_timeout_mills": 10, "user_key_prefix": "adreu2_", "zk_path": "config.zk.cluster.zw:2181:/ks2/redis/adBrowsedList/_ZW"}, "data_push_config": {"switch_key": "adUeqServerSwitch"}, "_user_experiment_allocation": {"world_ad_server_llsid_exp": {}, "world_ads_legacy_hash": {"expansion_exp": {"expansion_base": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89], "more_expansion": [90, 91, 92, 93, 94, 95]}, "no_delivery": {"no_delivery_1": [99], "no_delivery_2": [98], "no_delivery_3": [97], "no_delivery_4": [96]}}}, "ad_trace_log_producer_invalid_config": {"topic": "ad_trace_log_invalid", "zk_path": "/kuaishou/kafkaLogs/producers/PG_LOG4"}, "enable_use_secure_cdn_url": true, "for_test": 0, "grpc_tcp_copy": {"ad-rs-jswg65.idcyz.hb1.kwaidc.com": "online_tcpcopy_sender", "bjzyx-rs3055.zqy": "online_tcpcopy_sender", "ad-rs982.idczw.hb1.kwaidc.com": "online_tcpcopy_sender", "default": "online"}, "inc_message_back_seconds": 120, "ip_region_map_data_location": "../../ipdata", "ip_region_map_data_version_location": "../../ipdataversion", "message_queue_config": {"msg_obsolete_bound_second": 1800, "topic": "ad_dsp_updated", "zk_path1": "/kuaishou/kafkaTopics/consumersNew/yz-topic-cluster1", "zk_path2": "/kuaishou/kafkaTopics/consumersNew/pg-topic-cluster1", "zk_path3": "/kuaishou/kafkaTopics/consumersNew/yz-topic-cluster2", "zk_path4": "/kuaishou/kafkaTopics/consumersNew/pg-topic-cluster2"}, "grpc": {"client_map": {}, "server": {"kess_name": "grpc_adDiagApiService", "port": 21012, "quit_wait_seconds": 85, "rpc_name": "AdDiagApiService", "thread_num": 300, "use_application_service": true, "kcs_grpc_port_key": "AUTO_PORT1"}, "test": 0}, "max_ack_rank_info": 10, "server_config": {"zk_path_grpc": "/ks2/grpc:20082"}, "use_secure_cdn_url": true, "zk_hosts_config_key": {"_YZ": "config.zk.cluster.yz:2181", "_ZW": "config.zk.cluster.zw:2181"}}