# -*- coding: UTF8 -*-
import json

config = {
  "max_ack_rank_info": 10,
  "ack_rank_info_sample_rate": 0.2,
  "for_test" : 0,
  "enable_use_secure_cdn_url": True,
  "use_secure_cdn_url": True,
  "inc_message_back_seconds": 120,
  "kess_call_name": "grpc_adRankService",
  "adx_photo_update_interval_seconds":120,
  "ip_region_map_data_location" : "../../ipdata",
  "ip_region_map_data_version_location" : "../../ipdataversion",
  # 基于 grpc 的 tcpcopy, 可以设置 dryrun 环境
  "grpc_tcp_copy" : {
    "default" : "online",
    # tcp copy 环境
    "ad-rs-jswg65.idcyz.hb1.kwaidc.com": "online_tcpcopy_sender",
    "bjzyx-rs3055.zqy": "online_tcpcopy_sender",
    "ad-rs982.idczw.hb1.kwaidc.com": "online_tcpcopy_sender",
  },
  "message_queue_config": {
    "msg_obsolete_bound_second": 1800,
    "zk_path1": "/kuaishou/kafkaTopics/consumersNew/yz-topic-cluster1",
    "zk_path2": "/kuaishou/kafkaTopics/consumersNew/pg-topic-cluster1",
    "zk_path3": "/kuaishou/kafkaTopics/consumersNew/yz-topic-cluster2",
    "zk_path4": "/kuaishou/kafkaTopics/consumersNew/pg-topic-cluster2",
    "topic": "ad_dsp_updated",
  },
  "grpc": {
   "client_map": {
     "grpc_adFrontServer": "grpc_adFrontServer",
     "grpc_adFrontServer_follow": "grpc_adFrontServer_follow",
     "grpc_adFrontServer_search": "grpc_adFrontServer_search",
     "grpc_adFrontServer_splash": "grpc_adFrontServer_splash",
     "grpc_adFrontServer_detail": "grpc_adFrontServer_detail",
     "grpc_adFrontServer_kwai-galaxy": "grpc_adFrontServer_kwai-galaxy",
     "grpc_adFrontServer_knews": "grpc_adFrontServer_knews",
     "grpc_adFrontServer_universe": "grpc_adFrontServer_universe",
     "grpc_AdCounterService": "grpc_AdCounterService",
     "grpc_adForwardIndexService": "grpc_adForwardIndexService",
   },
   "server": {
      "kess_name": "grpc_adDiagApi",
      "kcs_grpc_port_key": "AUTO_PORT1",
      "port": 21012,
      "quit_wait_seconds": 85,
      "thread_num": 300,
    },
    "test": 0
  },
  "ad_trace_always_log_producer_config": {
    "zk_path": "/kuaishou/kafkaLogs/producers/PG_LOG3",
    "topic" : "ad_trace_always_log",
  },
  "ad_trace_log_producer_config": {
    "zk_path": "/kuaishou/kafkaLogs/producers/LF_LOG1",
    "topic" : "ad_trace_log",
  },
  "ad_trace_log_producer_ad_server_config": {
    "zk_path": "/kuaishou/kafkaLogs/producers/LF_LOG1",
    "topic" : "ad_trace_log_ad_server",
  },
  "ad_trace_log_producer_ad_target_config": {
    "zk_path": "/kuaishou/kafkaLogs/producers/LF_LOG1",
    "topic" : "ad_trace_log_target",
  },
  "ack_redis_config": {
    "zk_path":"config.zk.cluster.zw:2181:/ks2/redis/adUsedItem/_ZW",
    "max_connections_per_proxy": 5,
    "redis_timeout_mills": 10,
    "ttl_in_seconds": 30,
    "dsp_key_prefix": "ad_dsp_nn",
    "detail_key_prefix": "ad_detail_nn",
    "dsp_bid_prefix_key": "ad_dsp_bid",
  },
  "ack_kafka_config": {
    "kafka_topic" : "server_show_ack_predict_server",
    # Partition count of the topic.
    "partition_cnt" : 60,
    "dsp_key_prefix": "ad_dsp_nn",
    "detail_key_prefix": "ad_detail_nn",
    "dsp_bid_prefix_key": "ad_dsp_bid",
  },
  "retrieval_ack_kafka_config": {
    "kafka_topic" : "ad_server_target_response",
    "dsp_key_prefix" : "ad_dsp_retrieval",
    "detail_key_prefix": "ad_detail_retrieval",
  },
  "detail_cpm_ack_kafka_config": {
    "kafka_topic" : "detail_cpm_server_show_ack_predict_server",
    "partition_cnt" : 120,
    "detail_key_prefix": "ad_detail_cpm_nn",
  },
  "universe_cpm_ack_kafka_config": {
    "kafka_topic" : "ad_pv_cpm_ack_ps_universe",
    "partition_cnt" : 40,
    "detail_key_prefix": "ad_universe_cpm_nn",
  },
  "delivery_candidate_kafka_config": {
    "kafka_topic" : "delivery_candidate",
    "candidate_key_prefix": "ad_candidate",
  },
  "detail_delivery_candidate_kafka_config": {
    "kafka_topic" : "universe_delivery_candidate",
    "candidate_key_prefix": "ad_detail_candidate",
  },
  "zk_hosts_config_key": {
    "_YZ" : "config.zk.cluster.yz:2181",
    "_ZW" : "config.zk.cluster.zw:2181",
  },
  "server_config" : {
    "zk_path_grpc" : "/ks2/grpc:20082",
  },
  "ad_dsp_common_zk_config": {
      "zk_path":"config.zk.cluster.zw:2181:/ks2/redis/adDspCommon/_ZW",
      "max_connections_per_proxy":5,
      "redis_timeout_mills":10
  },
  "_by_ab_test_group_config": {
    "_default": {
    },
  },
  # 策略对应值为数组，每个数组元素中，name表示策略名称，为必填字段
  # 相同step对应策略的执行顺序与以下策略的声明顺序正相关
  "_strategy" : {
    "strategies" : [
    ],
  },
  "ad_trace_always_log_producer_config": {
    "zk_path": "/kuaishou/kafkaLogs/producers/ZL_COMMON1",
    "topic" : "ad_trace_always_log",
  },
  "ad_trace_log_producer_invalid_config": {
    "zk_path": "/kuaishou/kafkaLogs/producers/PG_LOG4",
    "topic" : "ad_trace_log_invalid",
  },
  "server_show_log_producer_config": {
    "topic" : "ad_server_show_log"
  },
  "server_show_log_producer_config_test": {
    "topic" : "ad_server_show_log_test1"
  },
  "server_show_log_ads_producer_config": {
    "topic" : "ad_server_show_log_ads"
  },
  "server_show_log_ads_producer_config_test": {
    "topic" : "ad_server_show_log_ads_test"
  },
}

if __name__ == "__main__":
  json.encoder.FLOAT_REPR = str
  print json.dumps(config, indent=2, sort_keys=True)
