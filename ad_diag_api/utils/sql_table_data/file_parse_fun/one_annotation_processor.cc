#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/one_annotation_processor.h"

#include "glog/logging.h"
#include "perfutil/perfutil.h"
#include "absl/strings/substitute.h"
#include "teams/ad/ad_diag_api/utils/utils.h"

namespace ks {
namespace ad_diag_api {

void OneAnnotationProcessor::UniqueParse() {
  LOG(INFO) << "begin_one_parse_annotation, table_name: " << table_name;
  for (int i = 0; i < common_table_struct_vec.size(); ++i) {
    OneAnnotationTableName one_info;
    one_info.str_id = common_table_struct_vec[i].str_id;
    one_info.num_id = common_table_struct_vec[i].num_id;
    one_info.annotation = common_table_struct_vec[i].annotation;
    one_info.update_time = common_table_struct_vec[i].update_time;
    one_annotation_infos.emplace_back(one_info);
  }
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "annotation_parse", absl::Substitute("$0_$1_$2",
                                table_name, common_table_struct_vec.size(), one_annotation_infos.size()));

  LOG(INFO) << "finish_parse, table_name: " << table_name
            << " common_table_struct_vec: " << common_table_struct_vec.size()
            << " unique_table_struct_vec: " << one_annotation_infos.size();
}

void OneAnnotationProcessor::Update() {
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "compare_size",
      absl::Substitute("$0_$1_$2", table_name, exist_id_set.size(), one_annotation_infos.size()));

  for (int i = 0; i < one_annotation_infos.size(); ++i) {
    std::string replace_sql = "replace into " + table_name + " ("
                              + col_name_map["num_id"] + ", "
                              + col_name_map["name"] + ",  show_order, update_time, "
                              + col_name_map["str_id"] + ")"
                              + " VALUES(" + one_annotation_infos[i].num_id + ", '"
                              + one_annotation_infos[i].annotation + "', 100, '"
                              + one_annotation_infos[i].update_time + "', '"
                              + one_annotation_infos[i].str_id + "')";
    bool dataset = manager->ExecuteSQLWithRetry(replace_sql, 2);
    if (dataset == false) {
      LOG(ERROR) << "auto_sql_replace falied! table_name: " << table_name
                 << " error_sql: " << replace_sql;
      ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "update_sql_status",
                                          "update_sql_error", replace_sql);
    } else {
      LOG_EVERY_N(INFO, 50) << "auto_sql_replace succ! table_name: " << table_name
                            << " sql: " << replace_sql;
    }
  }
}

}  // namespace ad_diag_api
}  // namespace ks
