#pragma once
#include <string>
#include <vector>
#include <unordered_map>
#include "teams/ad/ad_diag_api/utils/bg_task/update_sql_table/update_sql_table.h"
#include "serving_base/mysql_util/db_conn_manager.h"
#include "mysql-connector/cppconn/resultset.h"

#undef PACKAGE_NAME
#include "teams/ad/ad_diag_api/utils/bg_task/ad_dsp_position_data/ad_dsp_position_data.h"

#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/annotation_processor_base.h"

namespace ks {
namespace ad_diag_api {

class TwoAnnotationProcessor : public AnnotationProcessorBase {
 public:
  TwoAnnotationProcessor(const std::string& file_name_, const std::string& file_context_,
                         const std::string& table_name_, const std::string& search_key_,
                         serving_base::mysql_util::DbConnManager* manager_,
                         std::unordered_map<std::string, std::string> col_name_map_) :
                         AnnotationProcessorBase(file_name_, file_context_,
                         table_name_, search_key_, manager_, col_name_map_) {}
  std::vector<TwoAnnotationTableName> two_annotation_infos;
  void UniqueParse() override;
  void Update() override;

 private:
  bool GetTwoAnnotations(const std::string annotation, TwoAnnotationTableName* two_info);
};
}  // namespace ad_diag_api
}  // namespace ks
