#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/annotation_processor_base.h"

namespace ks {
namespace ad_diag_api {

void AnnotationProcessorBase::Process() {
  // MutiRetrievalTag 表无注释解析的要求, 针对该表不能进行公共解析
  if (table_name != "MutiRetrievalTag") {
    CommonParse();
  }
  // 以 num_id 查询数据库中数据量
  QueryExistSqlInfo(table_name, col_name_map["str_id"], manager, &exist_id_set);
  UniqueParse();
  Update();
}

void AnnotationProcessorBase::CommonParse() {
  LOG(INFO) << "begin_common_parse, table_name: " << table_name;
  CommonFileParse common_file_parse(file_name, file_context, table_name,
                                  search_key, &common_table_struct_vec);
  common_file_parse.CommonParse();
}

}  // namespace ad_diag_api
}  // namespace ks
