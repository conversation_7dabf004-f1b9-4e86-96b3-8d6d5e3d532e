#pragma once
#include <string>
#include <unordered_map>
#include "teams/ad/ad_diag_api/utils/utils.h"
#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/annotation_processor_base.h"
#include "teams/ad/ad_diag_api/utils/kconf/kconf.h"
#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/one_annotation_processor.h"
#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/two_annotation_processor.h"
#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/six_annotation_processor.h"
#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/eight_annotation_processor.h"
#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/no_annotation_processor.h"
namespace ks {
namespace ad_diag_api {

AnnotationProcessorBase* GetAnnotationProcessor(const std::string& file_name,
                              const std::string& file_context,
                              const std::string& table_name,
                              const std::string& search_key,
                              serving_base::mysql_util::DbConnManager* manager) {
  auto annotation_handler = AdKconfUtil::annotationHandler()->GetConfig();
  if (!annotation_handler || !annotation_handler->IsObject()) {
    LOG(FATAL) << "can not parse file! no corr annotation_handler";
    return nullptr;
  }
  std::unordered_map<std::string, std::string> col_name_map;
  if (IsOneAnnotation(table_name, annotation_handler, &col_name_map)) {
    LOG(INFO) << "use OneAnnotation, file_name:" << file_name << " table_name: " << table_name;
    return new OneAnnotationProcessor(file_name, file_context, table_name, search_key, manager, col_name_map);
  } else if (IsTwoAnnotation(table_name, annotation_handler, &col_name_map)) {
    LOG(INFO) << "use TwoAnnotation, file_name:" << file_name << " table_name: " << table_name;
    return new TwoAnnotationProcessor(file_name, file_context, table_name, search_key, manager, col_name_map);
  } else if (IsSixAnnotation(table_name, annotation_handler, &col_name_map)) {
    LOG(INFO) << "use SixAnnotation, file_name:" << file_name << " table_name: " << table_name;
    return new SixAnnotationProcessor(file_name, file_context, table_name, search_key, manager, col_name_map);
  } else if (IsEightAnnotation(table_name, annotation_handler, &col_name_map)) {
    LOG(INFO) << "use EightAnnotation, file_name:" << file_name << " table_name: " << table_name;
    return new EightAnnotationProcessor(file_name, file_context, table_name,
                                        search_key, manager, col_name_map);
  } else if (IsNoAnnotationProcessor(table_name, annotation_handler, &col_name_map)) {
    LOG(INFO) << "use NoAnnotationProcessor, file_name:" << file_name << "table_name: " << table_name;
    return new NoAnnotationProcessor(file_name, file_context, table_name, search_key, manager, col_name_map);
  } else {
    LOG(ERROR) << "unknown table_name! table_name: " << table_name;
    return nullptr;
  }
}

}  // namespace ad_diag_api
}  // namespace ks
