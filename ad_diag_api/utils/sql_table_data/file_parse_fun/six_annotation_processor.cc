#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/six_annotation_processor.h"

#include "glog/logging.h"
#include "perfutil/perfutil.h"
#include "absl/strings/substitute.h"

namespace ks {
namespace ad_diag_api {

void SixAnnotationProcessor::UniqueParse() {
  LOG(INFO) << "begin_six_parse_annotation, table_name: " << table_name;
  for (int i = 0; i < common_table_struct_vec.size(); ++i) {
    SixAnnotationTableName six_info;
    six_info.num_id = common_table_struct_vec[i].num_id;
    six_info.str_id = common_table_struct_vec[i].str_id;
    six_info.update_time = common_table_struct_vec[i].update_time;
    if (!GetSixAnnotations(common_table_struct_vec[i].annotation, &six_info)) {
      LOG(WARNING) << "parse_six_annotation fail, table_name: " << table_name
                << " str_id: " << six_info.str_id
                << "num_id: " << six_info.str_id
                << "annotation: " << common_table_struct_vec[i].annotation;
      ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "parse_error", "six_annotation_error",
                  absl::Substitute("$0_$1", table_name, common_table_struct_vec[i].annotation));
      continue;
    }
    LOG(INFO) << "parse_six_annotation, table_name: " << table_name << " str_id: " << six_info.str_id
              << " show_order: " << six_info.show_order << " name: " << six_info.name1;
    six_annotation_infos.emplace_back(six_info);
  }
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "annotation_parse", absl::Substitute("$0_$1_$2",
                                table_name, common_table_struct_vec.size(), six_annotation_infos.size()));

  LOG_EVERY_N(INFO, 1000) << "finish_parse, table_name: " << table_name
            << " common_table_struct_vec: " << common_table_struct_vec.size()
            << " unique_table_struct_vec: " << six_annotation_infos.size();
}

bool SixAnnotationProcessor::GetSixAnnotations(const std::string annotation,
                                               SixAnnotationTableName* six_info) {
  std::vector<std::string> annotation_message_vec =
                                absl::StrSplit(annotation, ";", absl::SkipEmpty());
  if (annotation_message_vec.size() != 6) {
    return false;
  }
  six_info->name1 = StrStrip(annotation_message_vec[0]);
  six_info->name2 = StrStrip(annotation_message_vec[1]);
  six_info->name3 = StrStrip(annotation_message_vec[2]);
  six_info->name4 = StrStrip(annotation_message_vec[3]);
  six_info->name5 = StrStrip(annotation_message_vec[4]);
  six_info->name6 = StrStrip(annotation_message_vec[5]);
  return true;
}

void SixAnnotationProcessor::Update() {
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "compare_size",
      absl::Substitute("$0_$1_$2", table_name, exist_id_set.size(), six_annotation_infos.size()));

  for (int i = 0; i < six_annotation_infos.size(); ++i) {
    std::string replace_sql = "replace into " + table_name + "("
                                + col_name_map["num_id"] + ", " + col_name_map["str_id"] + ", "
                                + col_name_map["name1"] + ", " + col_name_map["name2"] + ", "
                                + col_name_map["name3"] + ", " + col_name_map["name4"] + ", "
                                + col_name_map["name5"] + ", " + col_name_map["name6"] + ", "
                                + "show_order, update_time)"
                                + " VALUES(" + six_annotation_infos[i].num_id + ", '"
                                + six_annotation_infos[i].str_id + "', '"
                                + six_annotation_infos[i].name1 + "', '"
                                + six_annotation_infos[i].name2 + "', '"
                                + six_annotation_infos[i].name3 + "', '"
                                + six_annotation_infos[i].name4 + "', '"
                                + six_annotation_infos[i].name5 + "', '"
                                + six_annotation_infos[i].name6 + "', "
                                + six_annotation_infos[i].show_order + ", '"
                                + six_annotation_infos[i].update_time + "')";
    bool dataset = manager->ExecuteSQLWithRetry(replace_sql, 2);
    if (dataset == false) {
      LOG(ERROR) << "auto_sql_replace falied! table_name: " << table_name << " error_sql: " << replace_sql;
      ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "update_sql_status", "update_sql_error",
                                         replace_sql);
    } else {
      LOG(INFO) << "auto_sql_replace succ! table_name: " << table_name
                            << " sql: " << replace_sql;
    }
  }
}

}  // namespace ad_diag_api
}  // namespace ks
