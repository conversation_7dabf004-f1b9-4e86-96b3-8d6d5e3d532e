#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/no_annotation_processor.h"

#include "base/time/time.h"
#include "glog/logging.h"
#include "perfutil/perfutil.h"
#include "absl/strings/substitute.h"
#include "absl/strings/str_split.h"
#include "teams/ad/ad_diag_api/utils/utils.h"

namespace ks {
namespace ad_diag_api {

void NoAnnotationProcessor::UniqueParse() {
  LOG(INFO) << "begin NoAnnotationProcessor, file_name: " << file_name
            << " table_name: " << table_name << " search_key: " << search_key;
  std::vector<std::string> vec_file_context = absl::StrSplit(file_context, "\n", absl::SkipEmpty());
  LOG(INFO) << "split_file, file_name: " << file_name << " vec_file_context: " << vec_file_context.size();

  // 获取时间信息
  base::Time time;
  std::string update_time;
  time.Now().ToStringInFormat("%Y-%m-%d %H:%M:%S", &update_time);
  bool is_corr_to_table = false;
  for (int i = 0; i < vec_file_context.size(); ++i) {
    auto& item = vec_file_context[i];
    if (item.find(search_key) != std::string::npos) {
      is_corr_to_table = true;
      LOG(INFO) << "file_find_search_key, file_name: "<< file_name
                << " table_name: " << table_name << " search_key: " << search_key
                << " item: " << item;
      continue;
    }
    if (item.find("MAX_TAG") != std::string::npos && item.find("KMAXTAGID") != std::string::npos) {
      LOG(INFO) << "find_end_tag: " << item;
      is_corr_to_table = false;
      continue;
    }
    if (is_corr_to_table) {
      std::vector<std::string> vec_line_context = absl::StrSplit(item, "=", absl::SkipEmpty());
      if (vec_line_context.size() !=  2) {
        LOG(INFO) << "line_context invalid: " << item << " file_name: " << file_name
                  << " table_name: " << table_name;
        continue;
      }
      // 主要用于过滤掉文件中的示例部分
      if (!CheckStrID(vec_line_context[0])) {
        LOG(INFO) << "str_id invalid: " << vec_line_context[0] << "table_name: " << table_name;
        continue;
      }
      // 此处 vec_line_context[1] 包含等号后的所有内容(含注释)
      if (!CheckNumID(vec_line_context[1])) {
        LOG(INFO) << "num_id invalid: " << vec_line_context[1] << "table_name: " << table_name;
        continue;
      }
      common_table.str_id = common_table.num_id + "(" + common_table.str_id + ")";
      common_table.update_time = update_time;
      no_annotation_infos.emplace_back(common_table);
    }
  }
  LOG(INFO) << "finish_no_annotation_parse_file, file_name:" << file_name
            << " table_name: " << table_name << " search_key: " << search_key
            << " no_annotation_infos.size: " << no_annotation_infos.size();
}

bool NoAnnotationProcessor::CheckStrID(const std::string& str_id) {
  // str_id 不包含冒号
  if (str_id.find(":") != std::string::npos) {
    return false;
  }
  common_table.str_id = StrStrip(str_id);
  return true;
}

bool NoAnnotationProcessor::CheckNumID(const std::string& num_id_context) {
  std::vector<std::string> num_id_vec = absl::StrSplit(num_id_context, ",", absl::SkipEmpty());
  if (num_id_vec.size() <= 0) {
    LOG(ERROR) << "num_id_vec_size_invalid: " << num_id_context
               << " table_name: " << table_name;
    return false;
  }
  std::string num_id = StrStrip(num_id_vec[0]);
  if (!IsNumber(num_id)) {
    return false;
  }
  common_table.num_id = num_id;
  return true;
}

void NoAnnotationProcessor::Update() {
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "compare_size",
      absl::Substitute("$0_$1_$2", table_name, exist_id_set.size(), no_annotation_infos.size()));
  for (int i = 0; i < no_annotation_infos.size(); ++i) {
    std::string replace_sql = "replace into " + table_name + " ("
                              + col_name_map["num_id"] + ", show_order, update_time, "
                              + col_name_map["str_id"] + ")"
                              + " VALUES(" + no_annotation_infos[i].num_id + ", 100, '"
                              + no_annotation_infos[i].update_time + "', '"
                              + no_annotation_infos[i].str_id + "')";
    bool dataset = manager->ExecuteSQLWithRetry(replace_sql, 2);
    if (dataset == false) {
      LOG(ERROR) << "auto_sql_replace falied! table_name: " << table_name
                 << " error_sql: " << replace_sql;
      ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "update_sql_status",
                                          "update_sql_error", replace_sql);
    } else {
      LOG(INFO) << "auto_sql_replace succ! table_name: " << table_name
                            << " sql: " << replace_sql;
    }
  }
}

}  // namespace ad_diag_api
}  // namespace ks
