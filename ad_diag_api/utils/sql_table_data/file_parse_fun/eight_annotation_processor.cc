#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/eight_annotation_processor.h"

#include "glog/logging.h"
#include "perfutil/perfutil.h"
#include "absl/strings/substitute.h"

namespace ks {
namespace ad_diag_api {

void EightAnnotationProcessor::UniqueParse() {
  LOG(INFO) << "begin_eight_parse_annotation, table_name: " << table_name;
  for (int i = 0; i < common_table_struct_vec.size(); ++i) {
    EightAnnotationTableName eight_info;
    eight_info.num_id = common_table_struct_vec[i].num_id;
    eight_info.str_id = common_table_struct_vec[i].str_id;
    eight_info.update_time = common_table_struct_vec[i].update_time;
    if (!GetEightAnnotations(common_table_struct_vec[i].annotation, &eight_info)) {
      ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "parse_error", "eight_annotation_error",
                  absl::Substitute("$0_$1", table_name, common_table_struct_vec[i].annotation));
      continue;
    }
    LOG(INFO) << "parse_eight_annotation, table_name: " << table_name << " str_id: " << eight_info.str_id
              << " show_order: " << eight_info.show_order << " name: " << eight_info.name1;
    eight_annotation_infos.emplace_back(eight_info);
  }
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "annotation_parse", absl::Substitute("$0_$1_$2",
                                table_name, common_table_struct_vec.size(), eight_annotation_infos.size()));

  LOG_EVERY_N(INFO, 1000) << "finish_parse, table_name: " << table_name
            << " common_table_struct_vec: " << common_table_struct_vec.size()
            << " unique_table_struct_vec: " << eight_annotation_infos.size();
}

bool EightAnnotationProcessor::GetEightAnnotations(const std::string annotation,
                                               EightAnnotationTableName* eight_info) {
  std::vector<std::string> annotation_message_vec =
                                absl::StrSplit(annotation, ";", absl::SkipEmpty());
  if (annotation_message_vec.size() != 8) {
    return false;
  }
  eight_info->name1 = StrStrip(annotation_message_vec[0]);
  eight_info->name2 = StrStrip(annotation_message_vec[1]);
  eight_info->name3 = StrStrip(annotation_message_vec[2]);
  eight_info->name4 = StrStrip(annotation_message_vec[3]);
  eight_info->name5 = StrStrip(annotation_message_vec[4]);
  eight_info->name6 = StrStrip(annotation_message_vec[5]);
  eight_info->name7 = StrStrip(annotation_message_vec[6]);
  eight_info->name8 = StrStrip(annotation_message_vec[7]);
  return true;
}

void EightAnnotationProcessor::Update() {
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "compare_size",
      absl::Substitute("$0_$1_$2", table_name, exist_id_set.size(), eight_annotation_infos.size()));

  for (int i = 0; i < eight_annotation_infos.size(); ++i) {
    std::string replace_sql = "update " + table_name + " SET "
            + col_name_map["num_id"] + " = " + eight_annotation_infos[i].num_id
            + ", " + col_name_map["name1"] + " = '" + eight_annotation_infos[i].name1
            + "', " + col_name_map["name2"] + " = '" + eight_annotation_infos[i].name2
            + "', " + col_name_map["name3"] + " = '" + eight_annotation_infos[i].name3
            + "', " + col_name_map["name4"] + " = '" + eight_annotation_infos[i].name4
            + "', " + col_name_map["name5"] + " = '" + eight_annotation_infos[i].name5
            + "', " + col_name_map["name6"] + " = '" + eight_annotation_infos[i].name6
            + "', " + col_name_map["name7"] + " = '" + eight_annotation_infos[i].name7
            + "', " + col_name_map["name8"] + " = '" + eight_annotation_infos[i].name8
            + "', show_order = " + eight_annotation_infos[i].show_order
            + ", update_time = '" + eight_annotation_infos[i].update_time + "'"
            + " where " + col_name_map["str_id"] + " = '" + eight_annotation_infos[i].str_id +
            + "'";
    bool dataset = manager->ExecuteSQLWithRetry(replace_sql, 2);
    if (dataset == false) {
      LOG(ERROR) << "auto_sql_replace falied! table_name: " << table_name << " error_sql: " << replace_sql;
      ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "update_sql_status", "update_sql_error",
                                         replace_sql);
    } else {
      LOG_EVERY_N(INFO, 50) << "auto_sql_replace succ! table_name: " << table_name
                            << " sql: " << replace_sql;
    }
  }
}

}  // namespace ad_diag_api
}  // namespace ks
