#pragma once
#include <string>
#include <vector>
#include "glog/logging.h"
#include "absl/strings/str_split.h"
#include "absl/strings/substitute.h"

#include "teams/ad/ad_diag_api/utils/sql_table_data/sql_table_struct/sql_table_struct.h"
namespace ks {
namespace ad_diag_api {

class CommonFileParse {
 public:
  CommonFileParse(const std::string& file_name_, const std::string& file_context_,
                  const std::string& table_name_, const std::string& search_key_,
                  std::vector<CommonTable>* common_table_struct_vec_) {
    file_name = file_name_;
    file_context = file_context_;
    table_name = table_name_;
    search_key = search_key_;
    common_table_struct_vec = common_table_struct_vec_;
  }
  void CommonParse();
  bool GetTableID(const std::string& id_name_str);
 private:
  std::string file_name;
  std::string file_context;
  std::string table_name;
  std::string search_key;
  std::vector<CommonTable>* common_table_struct_vec{nullptr};
  CommonTable common_table;
};

}  // namespace ad_diag_api
}  // namespace ks
