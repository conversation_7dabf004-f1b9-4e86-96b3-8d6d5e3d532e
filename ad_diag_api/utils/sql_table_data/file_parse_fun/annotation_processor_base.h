#pragma once
#include <string>
#include <vector>
#include <unordered_set>
#include <unordered_map>
#include "teams/ad/ad_diag_api/utils/bg_task/update_sql_table/update_sql_table.h"
#include "serving_base/mysql_util/db_conn_manager.h"
#include "mysql-connector/cppconn/resultset.h"

#undef PACKAGE_NAME
#include "teams/ad/ad_diag_api/utils/bg_task/ad_dsp_position_data/ad_dsp_position_data.h"

#include "glog/logging.h"
#include "perfutil/perfutil.h"
#include "absl/strings/substitute.h"

#include "teams/ad/ad_diag_api/utils/sql_table_data/sql_table_struct/sql_table_struct.h"
#include "teams/ad/ad_diag_api/utils/utils.h"
#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/common_file_parse.h"

namespace ks {
namespace ad_diag_api {

class AnnotationProcessorBase {
 public:
  AnnotationProcessorBase(const std::string& file_name_,
               const std::string& file_context_,
               const std::string& table_name_,
               const std::string& search_key_,
               serving_base::mysql_util::DbConnManager* manager_,
               std::unordered_map<std::string, std::string> col_name_map_) {
    file_name = file_name_;
    file_context = file_context_;
    search_key = search_key_;
    table_name = table_name_;
    manager = manager_;
    col_name_map = col_name_map_;
  }
  virtual ~AnnotationProcessorBase() {}
  void Process();
  std::string file_name;
  std::string file_context;
  std::string search_key;
  std::string table_name;
  serving_base::mysql_util::DbConnManager* manager{nullptr};
  std::unordered_map<std::string, std::string> col_name_map;
  std::unordered_set<std::string> exist_id_set;

  std::vector<CommonTable> common_table_struct_vec;
  virtual void UniqueParse() = 0;
  virtual void Update() = 0;

 private:
  void CommonParse();
};

}  // namespace ad_diag_api
}  // namespace ks
