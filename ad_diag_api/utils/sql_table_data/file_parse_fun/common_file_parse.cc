#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/common_file_parse.h"
#include "perfutil/perfutil.h"
#include "base/time/time.h"
#include "teams/ad/ad_diag_api/utils/utils.h"


namespace ks {
namespace ad_diag_api {
bool CommonFileParse::GetTableID(const std::string& id_name_str) {
    std::vector<std::string> id_name_vec =
                        absl::StrSplit(id_name_str, "=", absl::SkipEmpty());
    // 依据等号的分隔, 过滤不符合规范的 id
    if (id_name_vec.size() != 2) {
      return false;
    }
    std::string num_id = StrStrip(id_name_vec[1]);
    if (num_id.size() >= 1 && (num_id[(num_id.size() - 1)] == ';'
                          || num_id[(num_id.size() - 1)] == ',')) {
      num_id = num_id.substr(0, num_id.size() - 1);
    }
    // 校验 id 是否为数字
    if (!IsNumber(num_id)) {
      return false;
    }
    common_table.str_id = StrStrip(id_name_vec[0]);
    common_table.num_id = num_id;
    return true;
}

void CommonFileParse::CommonParse() {
  LOG(INFO) << "begin_common_parse_file file_context, file_name:" << file_name
            << " table_name: " << table_name << " search_key: " << search_key;
  std::vector<std::string> vec_file_context = absl::StrSplit(file_context, "\n", absl::SkipEmpty());
  LOG(INFO) << "split_file, file_name: " << file_name << " vec_file_context: " << vec_file_context.size();

  // 获取时间信息
  base::Time time;
  std::string update_time;
  time.Now().ToStringInFormat("%Y-%m-%d %H:%M:%S", &update_time);
  bool is_corr_to_table = false;
  for (int i = 0; i < vec_file_context.size(); ++i) {
    auto& item = vec_file_context[i];
    //  中括号是为兼容 BETTER_ENUM  枚举类型
    if (item.find(search_key) != std::string::npos && (item.find("{") != std::string::npos
                                                  || item.find("(") != std::string::npos)) {
      LOG(INFO) << "begin add info to sql table, file_name: "<< file_name
                << " table_name: " << table_name << " search_key: " << search_key
                << " item: " << item;
      is_corr_to_table = true;
      continue;
    }
    if (item.find("}") != std::string::npos || StrStrip(item) == ")") {
      is_corr_to_table = false;
      LOG(INFO) << "finish add info to sql table. table_name: " << table_name
                << " search_key: " << search_key;
      continue;
    }
    if (is_corr_to_table) {
      if (item.find("=") == std::string::npos) {
        LOG(INFO) << "invalid  line_context: " << item << " file_name: " << file_name
                  << " table_name: " << table_name;
        continue;
      }
      std::vector<std::string> vec_line_context = absl::StrSplit(item, "//", absl::SkipWhitespace());
      // 依据注释符号的分隔
      if (vec_line_context.size() != 2) {
        LOG(INFO) << "auto vec_line irregular, line_str: " << item;
        ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "common_parse_error", "line_context_error",
                                                       absl::Substitute("$0_$1", table_name, item));
        continue;
      }
      // 分隔 vec_line_context[0] 以获取 id & name
      common_table.clear();
      if (!GetTableID(vec_line_context[0])) {
        ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "parse_error", "id_name_error",
                                                                          vec_line_context[0]);
        LOG_EVERY_N(WARNING, 1000) << "error_id_name: " << vec_line_context[0] << " file_name: " << file_name
                   << " table_name: " << table_name << " search_key: " << search_key;
        continue;
      }
      common_table.annotation = StrStrip(vec_line_context[1]);
      common_table.update_time = update_time;
      // 填充 common_table_struct_vec
      common_table_struct_vec->emplace_back(common_table);
    }
  }
  LOG_EVERY_N(INFO, 1000) << "finish_common_parse_file, file_name:" << file_name
               << " table_name: " << table_name << " search_key: " << search_key
               << " common_table_struct_vec: " << common_table_struct_vec->size();
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "common_parse",
                            absl::Substitute("$0_$1_$2_$3", file_name, table_name,
                            search_key, common_table_struct_vec->size()));
}
}  // namespace ad_diag_api
}  // namespace ks
