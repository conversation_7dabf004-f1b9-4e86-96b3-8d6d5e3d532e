#pragma once
#include <string>

namespace ks {
namespace ad_diag_api {

struct CommonTable {
  std::string str_id;
  std::string num_id;
  std::string annotation;  // 包含注释符后的所有内容
  std::string update_time;
  std::string show_order{"100"};
  void clear() {
    str_id.clear();
    num_id.clear();
    annotation.clear();
  }
};

struct OneAnnotationTableName : public CommonTable {};

struct TwoAnnotationTableName : public CommonTable {
  std::string name;
  void clear() {
    str_id.clear();
    num_id.clear();
    annotation.clear();
    show_order.clear();
    name.clear();
  }
};

struct SixAnnotationTableName : public CommonTable {
  std::string name1;
  std::string name2;
  std::string name3;
  std::string name4;
  std::string name5;
  std::string name6;
};

struct EightAnnotationTableName : public CommonTable {
  std::string name1;
  std::string name2;
  std::string name3;
  std::string name4;
  std::string name5;
  std::string name6;
  std::string name7;
  std::string name8;
};
}  // namespace ad_diag_api
}  // namespace ks
