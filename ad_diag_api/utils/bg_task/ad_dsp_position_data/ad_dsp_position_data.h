#pragma once

#include <string>
#include <vector>
#include <tuple>
#include <unordered_map>
#include <memory>
#include <utility>
#include <map>

#include "glog/logging.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/ad_proto/kuaishou/ad/dsp/ad_dsp_base_rpc_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace ks {
namespace ad_diag_api {
typedef kuaishou::ad::tables::AdPosition AdPostionType;

struct AdDspPosInfoAll {
  std::unordered_map<std::string, std::string> ad_pos_map;  // key --> pos_info
  std::unordered_map<std::string, std::string> ad_pos_id_map;  // pos_id --> pos_info
  std::unordered_map<int64, AdPostionType> all_positions_info;
  int32 size() const {
    return ad_pos_id_map.size();
  }
  bool empty() const {
    return ad_pos_map.empty();
  }
};

class AdDspPositionData : public ad_base::P2pCacheLoader<AdDspPosInfoAll> {
 public:
  static AdDspPositionData* GetInstance() {
    static AdDspPositionData instance;
    return &instance;
  }

 public:
  bool GetAdPositionData(std::unordered_map<int64, AdPostionType>* all_positions);
  // 检验 p2p 是否加载到数据
  bool IsParseReady();

 private:
  AdDspPositionData(): ad_base::P2pCacheLoader<AdDspPosInfoAll>(
                    1 * 60 * 60 * 1000, "ad_dsp_positions",
                    engine_base::DependDataLevel::STRONG_DEPEND) { }
  bool ParseLine(const std::string &line) override;
  // 后处理过程，主要根据解析后的数据更新 kconf, 用于 front 广告位兜底
  void DoTaskPostProc() override;
  // 对于相同的的广告位信息, 按照 pos_id 由大到小排序
  std::string ComparePosInfo(const std::string& old_pos_info, const std::string& new_pos_info);
  void GetTimeNow(int32* cur_day, int32* last_day, int32* cur_hour);

 private:
  int32 cur_day = 0;
  int32 last_day = 0;
  int32 cur_hour = 0;
  bool update_label = false;
};

}  // namespace ad_diag_api
}  // namespace ks
