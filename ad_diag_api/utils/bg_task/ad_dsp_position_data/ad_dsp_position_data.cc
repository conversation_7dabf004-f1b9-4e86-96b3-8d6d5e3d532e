#include "teams/ad/ad_diag_api/utils/bg_task/update_sql_table/update_sql_table.h"
#include "serving_base/mysql_util/db_conn_manager.h"
#include "mysql-connector/cppconn/resultset.h"

#undef PACKAGE_NAME
#include "teams/ad/ad_diag_api/utils/bg_task/ad_dsp_position_data/ad_dsp_position_data.h"

#include "base/encoding/base64.h"
#include "glog/logging.h"
#include "perfutil/perfutil.h"
#include "third_party/abseil/absl/strings/substitute.h"
#include "teams/ad/ad_diag_api/utils/utils.h"
#include "teams/ad/ad_diag_api/utils/kconf/kconf.h"

namespace ks {
namespace ad_diag_api {

bool AdDspPositionData::ParseLine(const std::string& line) {
  kuaishou::ad::tables::AdPosition info;
  std::string decode;
  if (!(base::Base64Decode(line, &decode))) {
    LOG(ERROR) << "PrometheusCostInfo: decode64 failed!";
    ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "dsp_position_parse_faild");
    return false;
  }
  if (!info.ParseFromString(decode)) {
    LOG(ERROR) << "PrometheusCostInfo: parse failed!";
    ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "dsp_position_parse_faild");
    return false;
  }
  LOG_EVERY_N(INFO, 10000) << "info_short_debug: " << info.ShortDebugString();
  // 获取 key 与 pos_id
  std::string key = absl::Substitute("$0_$1_$2_$3", info.app_id(),
                    info.page_id(), info.sub_page_id(), info.action());
  std::string pos_id = absl::Substitute("$0", info.id());

  // 构造 creative_material_type 格式: "[1,2,3] -> [1,2,3]
  std::string creative_material_types_str = info.creative_material_types();
  std::string creative_material_type;
  std::string s;
  // creative_material_type 字段存在 json_string 和 string 两种情况
  // 对 json_string 格式去掉双引号
  if (creative_material_types_str[0] == '"') {
    creative_material_type = creative_material_types_str.substr(1, creative_material_types_str.size() - 2);
  } else {
    creative_material_type = creative_material_types_str;
  }
  std::string pos_info = absl::Substitute("$0_$1_$2", pos_id, creative_material_type, info.report_scene());
  // all_positions_info 用于更新 sql 表使用
  (*process_data_).all_positions_info[info.id()] = info;
  // 删除无效广告位
  if (info.status() != kuaishou::ad::AdEnum::Status::AdEnum_Status_NORMAL) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "p2p_parse_size", "filter_size", key);
    LOG(INFO) << "p2p_filter pos_info, pos_id = " << info.id()
              << " key = " << key << " status = " << info.status();
    return false;
  }

  // 用于写 kconf 使用
  (*process_data_).ad_pos_id_map[pos_id] = pos_info;
  if ((*process_data_).ad_pos_map.count(key)) {
    LOG(INFO) << "key has exit: " << key << " id: " << pos_id
              << "two_pos_info: " << (*process_data_).ad_pos_map[key] << " " << pos_info;
    ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "repeated_key", key);
    pos_info = ComparePosInfo((*process_data_).ad_pos_map[key], pos_info);
    LOG(INFO) << "final_pos_info: " << pos_info;
  }
  (*process_data_).ad_pos_map[key] = pos_info;
  return true;
}

bool AdDspPositionData::GetAdPositionData(std::unordered_map<int64, AdPostionType>* all_positions) {
  auto scope_ptr = GetData();
  if (!scope_ptr) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "get_ad_position_data", "scope_ptr_null");
    LOG(INFO) << "scope_ptr_null";
    return false;
  }
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "get_ad_position_data", "scope_ptr_not_null");
  for (auto iter = (*scope_ptr).all_positions_info.begin(); iter != (*scope_ptr).all_positions_info.end();) {
    (*all_positions)[iter->first] = iter->second;
    iter++;
  }
  return true;
}

std::string AdDspPositionData::ComparePosInfo(const std::string& old_pos_info,
                                              const std::string& new_pos_info) {
  std::vector<std::string> old_pos_info_vec = absl::StrSplit(old_pos_info, "_", absl::SkipEmpty());
  std::vector<std::string> new_pos_info_vec = absl::StrSplit(new_pos_info, "_", absl::SkipEmpty());
  if (old_pos_info_vec.size() <= 0) {
    return new_pos_info;
  }
  if (new_pos_info_vec.size() <= 0) {
    return old_pos_info;
  }
  return old_pos_info_vec[0] > new_pos_info_vec[0] ? old_pos_info : new_pos_info;
}

void AdDspPositionData::DoTaskPostProc() {
  if (!AdKconfUtil::enableUpdataPosInfoToKconf()) {
    return;
  }
  if (!GetData()) {
    return;
  }
  GetTimeNow(&cur_day, &last_day, &cur_hour);
  if (cur_day != last_day) {
    last_day = cur_day;
    update_label = false;
    LOG(INFO) << "begin a new day,  day = " << cur_day;
  }
  if (cur_hour < AdKconfUtil::updataPosInfoToKconfTime() || update_label == true) {
    LOG(INFO) << "can not update kconf, time less 22 or has updated."
              << " cur_hour = " << cur_hour
              << " update_label = " << update_label;
    return;
  }
  update_label = true;
  std::string ad_pos_map_str = MapDoubleStringToString((*GetData()).ad_pos_map);
  std::string ad_pos_id_map_str = MapDoubleStringToString((*GetData()).ad_pos_id_map);
  LOG(INFO) << "ad_pos_map_str: " << ad_pos_map_str;
  LOG(INFO) << "ad_pos_id_map_str: " << ad_pos_id_map_str;
  std::string key_kconf_path = "ad.adDiagApi.keyToPosInfo";
  std::string pos_id_kconf_path = "ad.adDiagApi.posIdToPosInfo";
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "update_kconf_posinfo");
  UpdateProductionKconf(key_kconf_path, ad_pos_map_str);
  UpdateProductionKconf(pos_id_kconf_path, ad_pos_id_map_str);
}

void AdDspPositionData::GetTimeNow(int32* cur_day, int32* last_day, int32* cur_hour) {
  if (!cur_day || !last_day || !cur_hour) {
    return;
  }
  double now_ms = base::GetTimestamp() / 1000;
  base::Time now_time = base::Time::FromTimeT(now_ms / 1000);
  base::Time::Exploded struct_time;
  now_time.LocalExplode(&struct_time);
  *cur_day = struct_time.day_of_month;
  if (*last_day == 0) {
    *last_day = struct_time.day_of_month;
  }
  *cur_hour = struct_time.hour;
  LOG(INFO) << "get cur timeinfo. cur_day = " << *cur_day
            << " last_day = " << *last_day
            << " cur_hour = " << *cur_hour;
}

}  // namespace ad_diag_api
}  // namespace ks
