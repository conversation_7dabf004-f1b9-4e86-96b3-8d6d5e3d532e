#include "teams/ad/ad_diag_api/utils/bg_task/ad_pos_manager/ad_pos_manager.h"

#include <string>
#include <vector>
#include <algorithm>
#include <unordered_set>
#include <unordered_map>

#include "glog/logging.h"
#include "perfutil/perfutil.h"
#include "absl/strings/str_split.h"
#include "absl/strings/numbers.h"
#include "absl/strings/str_cat.h"
#include "mysql-connector/cppconn/resultset.h"
#include "teams/ad/ad_base/src/dot/dot.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_diag_api/utils/sql_util.h"
#include "teams/ad/ad_diag_api/utils/kconf/kconf.h"
#include "teams/ad/ad_diag_api/utils/bg_task/update_sql_table/update_sql_table.h"
#include "serving_base/mysql_util/db_conn_manager.h"

#undef PACKAGE_NAME
#include "teams/ad/ad_diag_api/utils/bg_task/ad_dsp_position_data/ad_dsp_position_data.h"


namespace ks {
namespace ad_diag_api {

#define SHOW_BASE_INFO(item)  \
  auto id = item.id();  \
  auto creative_material_types = item.creative_material_types();  \
  auto app_id = item.app_id();  \
  auto page_id = item.page_id();  \
  auto sub_page_id = item.sub_page_id();  \
  auto ad_place_position = item.ad_place_position(); \
  auto action = item.action();  \
  auto name = item.name();  \
  auto desc = item.desc();  \
  auto status = item.status();  \
  auto support_flags = item.support_flags();  \
  auto create_time = item.create_time();  \
  auto report_scene = item.report_scene();

bool AdPosManager::MySqlDbInitialize() {
  option.host = FLAGS_sql_host;
  option.schema = FLAGS_sql_schema;
  option.user = FLAGS_sql_user;
  option.passwd = FLAGS_sql_passwd;
  manager = new serving_base::mysql_util::DbConnManager(option);
  if (!manager->Connect()) {
    LOG(WARNING) << "Connect DB failed";
    return false;
  }
  return true;
}

void AdPosManager::UpDateAdPostion() {
  if (!MySqlDbInitialize()) {
    return;
  }
  // p2p 获取的数据
  std::unordered_map<int64, kuaishou::ad::tables::AdPosition> all_positions;
  if (!AdDspPositionData::GetInstance()->GetAdPositionData(&all_positions)) {
    LOG(ERROR) << "get posinfo from p2p failed";
    return;
  }
  base::Time time;
  std::string update_time;
  time.Now().ToStringInFormat("%Y-%m-%d %H:%M:%S", &update_time);
  std::string sql_check = "select id from ad_dsp_position";
  std::unordered_set<int64> id_set;
  getExistedId(sql_check, manager, &id_set);
  // pos_id 不更新到 sql 集合
  auto pos_id_list = AdKconfUtil::UpdatePosIdsBlackList();
  for (auto iter = all_positions.begin(); iter != all_positions.end(); ++iter) {
    auto& item = (iter->second);
    SHOW_BASE_INFO(item);
    if (pos_id_list->count(id) > 0) {
      continue;
    }
    // creative_material_type 字段存在 json_string 和 string 两种情况
    // 对 json_string 格式去掉双引号
    std::string creative_material_types_val;
    if (creative_material_types[0] == '"') {
      creative_material_types_val = creative_material_types.substr(1, creative_material_types.size() - 2);
    } else {
      creative_material_types_val = creative_material_types;
    }
    std::string sql_update;
    if (id_set.count(iter->second.id()) > 0) {
      sql_update = "UPDATE ad_dsp_position SET `creative_material_types` = '" + creative_material_types_val +
                   "', `app_id` = '" + app_id + "', `page_id` = '" + absl::StrCat(page_id) +
                   "', `sub_page_id` = '" + absl::StrCat(sub_page_id) +
                   "', `ad_place_position` = '" + absl::StrCat(ad_place_position) +
                   "', `action` = '" + absl::StrCat(action) + "', `name` = '" + absl::StrCat(name) +
                   "', `desc` = '" + desc + "', `status` = '" + absl::StrCat(status) +
                   "', `support_flags` = '" + absl::StrCat(support_flags) +
                   "', `create_time` = '" + absl::StrCat(create_time) +
                   "', `report_scene` = '" + absl::StrCat(report_scene) +
                   "', `update_time` = '" + update_time +
                   "' WHERE `id` = " + absl::StrCat(id);
    } else {
      sql_update = "INSERT INTO ad_dsp_position(`id`, `creative_material_types`, `app_id`, "
                   "`page_id`, `sub_page_id`, `ad_place_position`, `action`, `name`, "
                   "`desc`, `status`, `support_flags`, `create_time`, `report_scene`, `update_time`) "
                   "VALUES('" + absl::StrCat(id) + "', '" + creative_material_types_val + "', '" + app_id +
                   "', '" + absl::StrCat(page_id) + "', '" + absl::StrCat(sub_page_id) + "', '"
                   + absl::StrCat(ad_place_position) + "', '" + absl::StrCat(action) + "', '" + name +
                    "', '" + desc + "', '" + absl::StrCat(status) +
                   "', '" + absl::StrCat(support_flags) + "', '" + absl::StrCat(create_time) + "', '" +
                   absl::StrCat(report_scene) + "', '" + update_time + "')";
      LOG(INFO) << "find new pos_id: " << id;
      ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api",
                                         "new_pos_id", absl::StrCat(id));
    }
    bool dataset = manager->ExecuteSQLWithRetry(sql_update, 1);
    if (dataset == false) {
      // 记录 sql 执行失败的情况
      LOG(ERROR) << "ad_dsp_position. mysql exec update failed. error sql: " << sql_update;
      ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api",
                                         "update_error", absl::StrCat(id));
    }
  }
}

void AdPosManager::Start() {
  work_thread_ = new std::thread {
    [this] () {
      while (!stop_flag_.load()){
        UpDateAdPostion();
        LOG(INFO) << "finish update ad_dsp_position";
        std::this_thread::sleep_for(std::chrono::seconds(12 * 60 * 60));
      }
    }
  };
}

void AdPosManager::Stop() {
  stop_flag_.store(true);
  if (work_thread_) {
    work_thread_->join();
    delete work_thread_;
  }
}

}  //  namespace ad_diag_api
}  //  namespace ks
