#pragma once

#include <atomic>
#include <thread>

#include "gflags/gflags.h"
#include "teams/ad/ad_diag_api/utils/bg_task/update_sql_table/update_sql_table.h"
#include "serving_base/mysql_util/db_conn_manager.h"
#include "mysql-connector/cppconn/resultset.h"

#undef PACKAGE_NAME
#include "teams/ad/ad_diag_api/utils/bg_task/ad_dsp_position_data/ad_dsp_position_data.h"


namespace ks {
namespace ad_diag_api {

DECLARE_string(sql_host);
DECLARE_string(sql_schema);
DECLARE_string(sql_user);
DECLARE_string(sql_passwd);

class AdPosManager{
 public:
  static AdPosManager* GetInstance() {
    static AdPosManager ad_pos_manager;
    return &ad_pos_manager;
  }
  void Start();
  void Stop();

 private:
  void UpDateAdPostion();
  bool MySqlDbInitialize();

 private:
  serving_base::mysql_util::DbConnManager::Option option;
  serving_base::mysql_util::DbConnManager* manager{nullptr};
  std::atomic_bool stop_flag_{false};
  std::thread* work_thread_{nullptr};
};

}  // namespace ad_diag_api
}  // namespace ks
