#include "teams/ad/ad_diag_api/utils/bg_task/update_sql_table/update_sql_table.h"

#include "teams/ad/ad_diag_api/utils/kconf/kconf.h"
#include "teams/ad/ad_diag_api/utils/http_client.h"
#include "teams/ad/ad_diag_api/utils/utils.h"
#include "teams/ad/ad_diag_api/utils/sql_util.h"
#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/annotation_processor_base.h"
#include "teams/ad/ad_diag_api/utils/sql_table_data/sql_table_struct/sql_table_struct.h"
#include "teams/ad/ad_diag_api/utils/sql_table_data/file_parse_fun/annotation_processor_factory.h"


namespace ks {
namespace ad_diag_api {

bool UpdateSqlTableManager::MySqlDbInitialize() {
  if (AdKconfUtil::enableUseKDBTest()) {
    option.host = FLAGS_sql_host_test;
    option.schema = FLAGS_sql_schema_test;
    option.user = FLAGS_sql_user_test;
    option.passwd = FLAGS_sql_passwd_test;
  } else {
    option.host = FLAGS_sql_host;
    option.schema = FLAGS_sql_schema;
    option.user = FLAGS_sql_user;
    option.passwd = FLAGS_sql_passwd;
  }
  manager = new serving_base::mysql_util::DbConnManager(option);
  if (!manager->Connect()) {
    LOG(WARNING) << "Connect DB failed";
    return false;
  }
  return true;
}

void UpdateSqlTableManager::UpdateSqlTable() {
  if (!MySqlDbInitialize()) {
    return;
  }
  // 请求 git api, 按 token 初始化
  HttpClient client("ad_diag_api");
  client.SetToken(*(AdKconfUtil::gitCloneCookie()));

  std::string target_url;
  std::string file_context;

  auto file_info_json = AdKconfUtil::fileUrlSearchKeyTableInfo()->data;
  if (!file_info_json || !file_info_json->IsArray()) {
    LOG(FATAL) << "format error! fileUrlSearchKeyTableInfo is empty or is not an array";
    return;
  }
  auto file_info_vec = file_info_json->array();
  for (auto* const obj : file_info_vec) {
    if (!obj) {
      LOG(ERROR) << "content error! obj is null";
      continue;
    }
    file_name = obj->GetString("file_name");
    target_url = obj->GetString("target_url");
    auto code = client.Get(target_url.c_str());
    if (code != CURLE_OK) {
      // 文件下载失败，报警
      LOG(ERROR) << "http请求失败, error_code: " << code
                  << "download_file: " << file_name
                  << "target_url: " << target_url;
      ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "download_error",
                            absl::Substitute("$0_$1", file_name, target_url));
      continue;
    }
    file_context = client.get_result_data();
    LOG(INFO) << "download succ, file_name: " << file_name
              << " target_url: " << target_url;
    auto search_key_table_json = obj->Get("search_key_table_map");
    if (!search_key_table_json) {
      LOG(ERROR) << "content error! search_key_table_map is null!";
      continue;
    }
    std::map<std::string, std::string> search_key_map;
    GetSearchKeyTableMapFromJson(search_key_table_json, &search_key_map);
    CommonHandleFile(file_context, search_key_map);
  }
  return;
}

void UpdateSqlTableManager::GetSearchKeyTableMapFromJson(const Json* search_key_table_json,
                                        std::map<std::string, std::string>* search_key_map) {
  LOG(INFO) << "begin to get search_key_map";
  for (auto iter = search_key_table_json->object_begin(); iter !=
                                              search_key_table_json->object_end(); ++iter) {
    LOG(INFO) << "search_key_map table_name: " << iter->first
              << " search_key: " << iter->second->StringValue();
    (*search_key_map)[iter->first] = iter->second->StringValue();
  }
}

void UpdateSqlTableManager::CommonHandleFile(const std::string& file_context,
                            const std::map<std::string, std::string>& search_key_map) {
  for (auto iter = search_key_map.begin(); iter != search_key_map.end(); ++iter) {
    std::string table_name = iter->second;
    std::string search_key = iter->first;
    AnnotationProcessorBase* annotation_processor = GetAnnotationProcessor(file_name,
                                      file_context, table_name, search_key, manager);
    if (!annotation_processor) {
      // 表没有找到对应的解析方式
      LOG(ERROR) << "table_name has not define Annotation! table_name: " << table_name
                 << "please add table_name to kconf ad.adDiagApi.annotationHandler";
      ks::infra::PerfUtil::CountLogStash(1, "ad.ad_diag_api", "undefined_table", table_name);
      return;
    }
    annotation_processor->Process();
    delete annotation_processor;
    annotation_processor = nullptr;
  }
}

void UpdateSqlTableManager::Start() {
  work_thread_ = new std::thread {
    [this] () {
      while (!stop_flag_.load()) {
        LOG(INFO) << "begin update_sql";
        UpdateSqlTable();
        LOG(INFO) << "finish update_sql";
        std::this_thread::sleep_for(std::chrono::seconds(24 * 60 * 60));
        // std::this_thread::sleep_for(std::chrono::seconds(60));
      }
    }
  };
}

void UpdateSqlTableManager::Stop() {
  stop_flag_.store(true);
  if (work_thread_) {
    work_thread_->join();
    delete work_thread_;
  }
}

}  // namespace ad_diag_api
}  // namespace ks
