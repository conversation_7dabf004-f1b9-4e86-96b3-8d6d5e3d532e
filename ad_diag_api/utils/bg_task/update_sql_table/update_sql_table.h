#pragma once

#include <atomic>
#include <thread>
#include <string>
#include <map>
#include <vector>
#include <unordered_map>
#include <unordered_set>

#include "gflags/gflags.h"
#include "teams/ad/ad_diag_api/utils/bg_task/update_sql_table/update_sql_table.h"
#include "serving_base/mysql_util/db_conn_manager.h"
#include "mysql-connector/cppconn/resultset.h"

#undef PACKAGE_NAME
#include "teams/ad/ad_diag_api/utils/bg_task/ad_dsp_position_data/ad_dsp_position_data.h"

#include "ks/util/json.h"

#include "teams/ad/ad_diag_api/utils/kconf/kconf.h"

namespace ks {
namespace ad_diag_api {

DECLARE_string(sql_host_test);
DECLARE_string(sql_schema_test);
DECLARE_string(sql_user_test);
DECLARE_string(sql_passwd_test);

DECLARE_string(sql_host);
DECLARE_string(sql_schema);
DECLARE_string(sql_user);
DECLARE_string(sql_passwd);

class UpdateSqlTableManager{
 public:
  static UpdateSqlTableManager* GetInstance() {
    static UpdateSqlTableManager instance;
    return &instance;
  }

  void Start();
  void Stop();

 private:
  UpdateSqlTableManager() {
  }
  ~UpdateSqlTableManager() {
    delete manager;
  }

  void UpdateSqlTable();

  // 处理文件的函数, 解析文件内容，更新到 sql 表中
  void CommonHandleFile(const std::string& file_context,
          const std::map<std::string, std::string>& search_key_map);
  void GetSearchKeyTableMapFromJson(const Json* search_key_table_json,
                                    std::map<std::string, std::string>* search_key_map);
  bool MySqlDbInitialize();
  serving_base::mysql_util::DbConnManager::Option option;
  serving_base::mysql_util::DbConnManager* manager{nullptr};

  std::string file_name;
  std::atomic_bool stop_flag_{false};
  std::thread* work_thread_{nullptr};
};

}  // namespace ad_diag_api
}  // namespace ks
