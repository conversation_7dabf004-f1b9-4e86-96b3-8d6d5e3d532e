#include "teams/ad/ad_diag_api/utils/utils.h"

#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#include "rapidjson/stringbuffer.h"
#include "absl/strings/str_split.h"
#include "absl/strings/substitute.h"
#include "ks/util/json.h"
#include "base/strings/string_split.h"
#include "base/time/timestamp.h"
#include "base/common/sleep.h"
#include "base/time/time.h"
#include "teams/ad/ad_diag_api/utils/bg_task/update_sql_table/update_sql_table.h"
#include "serving_base/mysql_util/db_conn_manager.h"
#include "mysql-connector/cppconn/resultset.h"

#undef PACKAGE_NAME
#include "teams/ad/ad_diag_api/utils/bg_task/ad_dsp_position_data/ad_dsp_position_data.h"

#include "pub/src/base/encoding/url_encode.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/engine_base/data_stability/depend_data_level.h"
#include "teams/ad/engine_base/creative_debug_util/creative_debug_util.h"
#include "redis_proxy_client/redis_response.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_proto/kuaishou/ad/session_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/fanstop/fans_top_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_diag_api/utils/kconf/ad_core_meta_origin.pb.h"
#include "teams/ad/ad_diag_api/utils/kconf/ad_core_meta_origin.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_forward_index.kess.grpc.pb.h"
#include "teams/ad/ad_diag_api/utils/kconf/kconf.h"
#include "teams/ad/ad_diag_api/utils/sql_util.h"
#include "teams/ad/ad_diag_api/utils/http_client.h"


namespace ks {
namespace ad_diag_api {

DECLARE_string(sql_host);
DECLARE_string(sql_schema);
DECLARE_string(sql_user);
DECLARE_string(sql_passwd);

#define FORWARD_REQ(id_name, id, func_name) \
  kuaishou::ad::forward_index::Get##id_name##Req req; \
  kuaishou::ad::forward_index::Get##id_name##Resp res; \
  auto* id_info = req.add_id_info(); \
  id_info->set_##func_name##_id(id); \
  ::grpc::Status status = client.second\
      ->SelectOne()->Get##id_name(options, req, &res); \
  if (res.items_size() <= 0) { \
    LOG(ERROR) << "QuerySubLevelIds failed: forward rep failed." \
               << " data_level = " << data_level \
               << ", id = " << data_level_id \
               << ", status = " << status.error_code(); \
    result->set("status", "-1"); \
    result->set("error_message", "forward rep failed"); \
    return false; \
  } \

std::string StrStrip(const std::string& str) {
  if (str.size() == 0) {
    LOG(INFO) << "str_strip_empty";
    return "";
  }
  std::string ret = str;
  std::size_t found_first = ret.find_first_not_of(" ");
  if (found_first != std::string::npos) {
    ret.erase(0, found_first);
  }
  std::size_t found_last = ret.find_last_not_of(" ");
  if (found_last != std::string::npos) {
    ret.erase(found_last + 1);
  }
  return ret;
}

bool IsNumber(const std::string& str) {
  return str.find_first_not_of("0123456789") == std::string::npos;
}

std::string ComposeSQL(const std::string& table_name,
                       const std::string& str_id,
                       const std::string& show_order,
                       const std::string& name) {
  std::string update_sql = "replace into " + table_name + " SET name = \"" + name +
                            "\", show_order = '" + show_order + "', id = '" + str_id + "'";
  LOG(INFO) << "composesql update_sql:" << update_sql;
  return update_sql;
}

void QueryExistSqlInfo(const std::string& table_name,
                       const std::string& str_id,
                       serving_base::mysql_util::DbConnManager* manager,
                       std::unordered_set<std::string>* exist_id_set) {
  std::string query_sql = "select " + str_id + " from " + table_name;
  LOG(INFO) << "QueryExistSql: " << query_sql;
  sql::ResultSet* dataset = manager->ExecuteQuery(query_sql);
  try {
    dataset->beforeFirst();
    while (dataset->next()) {
      auto value = dataset->getString(str_id);
      (*exist_id_set).emplace(value);
    }
  } catch (sql::SQLException& e) {
    LOG(ERROR) << "Failed because: " << e.what();
  }
  LOG(INFO) << absl::Substitute("$0_id_set: ", table_name) << exist_id_set->size();
}

bool IsOneAnnotation(const std::string& table_name, const base::Json* annotation_handler,
                     std::unordered_map<std::string, std::string>* col_name_map) {
  auto one_annotaion_handler = annotation_handler->Get("OneAnnotation");
  if (!one_annotaion_handler || !one_annotaion_handler->IsObject()) {
    LOG(ERROR) << "can not parse one_annotation file! because no corr OneAnnotation";
    return false;
  }
  auto one_annotation_table = one_annotaion_handler->Get(table_name);
  if (!one_annotation_table || !one_annotation_table->IsObject()) {
    return false;
  }
  LOG(INFO) << "table_name: " << table_name << " is one annotation table";
  (*col_name_map)["num_id"] = one_annotation_table->GetString("num_id", "num_id");
  (*col_name_map)["str_id"] = one_annotation_table->GetString("str_id", "id");
  (*col_name_map)["name"] = one_annotation_table->GetString("name", "name");
  return true;
}

bool IsTwoAnnotation(const std::string& table_name, const base::Json* annotation_handler,
                     std::unordered_map<std::string, std::string>* col_name_map) {
  auto two_annotaion_handler = annotation_handler->Get("TwoAnnotation");
  if (!two_annotaion_handler || !two_annotaion_handler->IsObject()) {
    LOG(ERROR) << "can not parse two_annotation file! because no corr TwoAnnotation";
    return false;
  }
  // 判断 table_name 是否属于该解析方式
  auto two_annotation_table = two_annotaion_handler->Get(table_name);
  if (!two_annotation_table || !two_annotation_table->IsObject()) {
    return false;
  }
  LOG(INFO) << "table_name: " << table_name << " is two annotation table";
  (*col_name_map)["num_id"] = two_annotation_table->GetString("num_id", "num_id");
  (*col_name_map)["str_id"] = two_annotation_table->GetString("str_id", "id");
  (*col_name_map)["name"] = two_annotation_table->GetString("name", "name");
  return true;
}

bool IsSixAnnotation(const std::string& table_name, const base::Json* annotation_handler,
                     std::unordered_map<std::string, std::string>* col_name_map) {
  auto six_annotation_handler = annotation_handler->Get("SixAnnotation");
  if (!six_annotation_handler || !six_annotation_handler->IsObject()) {
    LOG(ERROR) << "can not parse six_annotation file! because no corr SixAnnotation";
    return false;
  }
  auto six_annotation_table = six_annotation_handler->Get(table_name);
  if (!six_annotation_table || !six_annotation_table->IsObject()) {
    return false;
  }
  LOG(INFO) << "table_name: " << table_name << " is six annotation table";
  (*col_name_map)["num_id"] = six_annotation_table->GetString("num_id", "Identifier_id");
  (*col_name_map)["str_id"] = six_annotation_table->GetString("str_id", "id");
  (*col_name_map)["name1"] = six_annotation_table->GetString("name1", "name");
  (*col_name_map)["name2"] = six_annotation_table->GetString("name2", "filter_stage");
  (*col_name_map)["name3"] = six_annotation_table->GetString("name3", "optimizable_type");
  (*col_name_map)["name4"] = six_annotation_table->GetString("name4", "suggestions");
  (*col_name_map)["name5"] = six_annotation_table->GetString("name5", "reasons_disclosure");
  (*col_name_map)["name6"] = six_annotation_table->GetString("name6", "owner");
  return true;
}

bool IsEightAnnotation(const std::string& table_name, const base::Json* annotation_handler,
                     std::unordered_map<std::string, std::string>* col_name_map) {
  if (annotation_handler == nullptr) {
    LOG(ERROR) << "annotation_handler nullptr";
    return false;
  }

  auto eight_annotation_handler = annotation_handler->Get("EightAnnotation");
  if (!eight_annotation_handler || !eight_annotation_handler->IsObject()) {
    LOG(ERROR) << "can not parse eight_annotation file! because no corr EightAnnotation";
    return false;
  }
  auto eight_annotation_table = eight_annotation_handler->Get(table_name);
  if (!eight_annotation_table || !eight_annotation_table->IsObject()) {
    return false;
  }
  LOG(INFO) << "table_name: " << table_name << " is eight annotation table";
  (*col_name_map)["num_id"] = eight_annotation_table->GetString("num_id", "Identifier_id");
  (*col_name_map)["str_id"] = eight_annotation_table->GetString("str_id", "id");
  (*col_name_map)["name1"] = eight_annotation_table->GetString("name1", "name");
  (*col_name_map)["name2"] = eight_annotation_table->GetString("name2", "filter_stage");
  (*col_name_map)["name3"] = eight_annotation_table->GetString("name3", "optimizable_type");
  (*col_name_map)["name4"] = eight_annotation_table->GetString("name4", "suggestions");
  (*col_name_map)["name5"] = eight_annotation_table->GetString("name5", "reasons_disclosure");
  (*col_name_map)["name6"] = eight_annotation_table->GetString("name6", "owner");
  (*col_name_map)["name7"] = eight_annotation_table->GetString("name7", "advisement_cn");
  (*col_name_map)["name8"] = eight_annotation_table->GetString("name8", "advisement_en");
  return true;
}
bool IsNoAnnotationProcessor(const std::string& table_name, const base::Json* annotation_handler,
                                 std::unordered_map<std::string, std::string>* col_name_map) {
  auto no_annotation_handler = annotation_handler->Get("NoAnnotation");
  if (!no_annotation_handler || !no_annotation_handler->IsObject()) {
    LOG(ERROR) << "can not parse no_annotation file! because no corr NoAnnotation";
    return false;
  }
  auto no_annotation_table = no_annotation_handler->Get(table_name);
  if (!no_annotation_table || !no_annotation_table->IsObject()) {
    return false;
  }
  LOG(INFO) << "table_name: " << table_name << "is no name annotation table";
  (*col_name_map)["num_id"] = no_annotation_table->GetString("num_id", "id");
  (*col_name_map)["str_id"] = no_annotation_table->GetString("str_id", "name");
  return true;
}

std::string MapDoubleStringToString(const std::unordered_map<std::string, std::string>& map_double_string) {
  rapidjson::Document doc;
  rapidjson::Document::AllocatorType& allocator = doc.GetAllocator();
  rapidjson::Value root(rapidjson::kObjectType);
  rapidjson::Value key(rapidjson::kStringType);
  rapidjson::Value value(rapidjson::kStringType);
  for (auto iter = map_double_string.begin(); iter != map_double_string.end(); ++iter) {
    key.SetString(iter->first.c_str(), allocator);
    value.SetString(iter->second.c_str(), allocator);
    root.AddMember(key, value, allocator);
  }
  rapidjson::StringBuffer buffer;
  rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
  root.Accept(writer);
  return buffer.GetString();
}

void UpdateProductionKconf(const std::string& kconf_path, const std::string& kconf_data) {
  std::string request_url = "https://kconf.corp.kuaishou.com/api/config/subconfig/update";
  std::string kconf_data_encoded = base::EncodeUrlComponent(kconf_data.c_str());

  std::string request_data = "key=" + kconf_path + "&comment=baizongyao&stage=production&snapshotId=-1" +
                             "&content=" + kconf_data_encoded;
  LOG(INFO) << "production kconf_path: " << kconf_path << " request_data: " << request_data;

  std::unordered_map<std::string, std::string> headers;
  headers["Authorization"] = "Token 8304cc7fb8108a33ebc03b62aa20774f";
  headers["Content-Type"] = "application/x-www-form-urlencoded";

  HttpClient client;
  auto code = client.Post(request_url.c_str(), request_data, headers);
  auto resp = client.get_result_data();
  if (code != CURLE_OK) {
    LOG(ERROR) << "production http请求失败, error code: " << code;
    return;
  }
  LOG(INFO) << "production kconf_path: " << kconf_path << " resp: " << resp;
}


void UpdateCandidateKconf(const std::string& kconf_path, const std::string& kconf_data) {
  CreateCandidateKconf(kconf_path);
  std::string request_url = "https://kconf-test.corp.kuaishou.com/api/config/subconfig/update";
  std::string kconf_data_encoded = base::EncodeUrlComponent(kconf_data.c_str());

  std::string request_data = "key=" + kconf_path + "&comment=auto&stage=testing&snapshotId=-1" +
                             "&content=" + kconf_data_encoded;

  LOG(INFO) << "candidate kconf_path: " << kconf_path << " request_data: " << request_data;

  std::unordered_map<std::string, std::string> headers;
  headers["Authorization"] = "Token bbmyPkfG7Ci12JwILLqYqIv9xD98XApq";
  headers["Content-Type"] = "application/x-www-form-urlencoded";

  HttpClient client;
  auto code = client.Post(request_url.c_str(), request_data, headers);
  auto resp = client.get_result_data();
  if (code != CURLE_OK) {
    LOG(ERROR) << "candidate http请求失败, error code: " << code;
    return;
  }
  LOG(INFO) << "candidate kconf_path: " << kconf_path << " resp: " << resp;
}

void CreateCandidateKconf(const std::string& kconf_path) {
  std::string request_url = "https://kconf-test.corp.kuaishou.com/api/config/create";
  std::string request_data = "key=" + kconf_path + "&type=json" +
                             "&desc=baizongyao";
  LOG(INFO) << "candidate kconf_path=" << kconf_path << ", request_data = " << request_data;
  std::unordered_map<std::string, std::string> headers;
  headers["Authorization"] = "Token bbmyPkfG7Ci12JwILLqYqIv9xD98XApq";
  headers["Content-Type"] = "application/x-www-form-urlencoded";
  HttpClient client;
  client.Post(request_url.c_str(), request_data, headers);
  std::string resp = client.get_result_data();
  LOG(INFO) << "candidate kconf_path=" << kconf_path << ", resp = " << resp;
}

void GetCandidateKconfDate(const std::string& kconf_path) {
  std::string request_url = "https://kconf-test.corp.kuaishou.com/api/config/get";
  std::unordered_map<std::string, std::string> headers;
  headers["Authorization"] = "Token bbmyPkfG7Ci12JwILLqYqIv9xD98XApq";
  std::unordered_map<std::string, std::string> params;
  params["key"] = kconf_path;
  HttpClient client;
  auto code = client.Get(request_url.c_str(), headers, params);
  std::string resp = client.get_result_data();
  if (code != CURLE_OK) {
    LOG(ERROR) << "candidate get kconf data failed! error code: " << code;
  }
  LOG(INFO) << "candidata kconf_path: " << kconf_path << " resp: " << resp;

  rapidjson::Document doc;
  rapidjson::ParseResult parse_code = doc.Parse(resp.c_str());
  if (!parse_code) {
    LOG(INFO) << "candidate parse_code failed! resp:" <<  resp;
  }
  if (doc.HasMember("data")) {
    if (doc["data"].HasMember("subConfigs") &&
        doc["data"]["subConfigs"].IsArray()) {
      // for (const auto& sub_config : doc["data"]["subConfigs"].GetArray()) {
        const auto& sub_config = doc["data"]["subConfigs"][0];
        if (sub_config.IsObject()) {
          auto result_data = sub_config["data"].GetString();
          LOG(INFO) << "candidate result_data: " << sub_config["data"].GetString();
          base::Json result_data_json(base::StringToJson(result_data));
          if (result_data_json.IsObject()) {
            auto out = result_data_json.GetString("kuaishou_nebula_100012264_100012550_0", "not_find");
            LOG(INFO) << "candidate out_string: " << out;
          } else {
            LOG(INFO) << "candidate resp_obj is not a obj";
          }
        }
    }
  }
}

void FanstopUeFreq(const std::string user_id,
                   const std::string app_id,
                   base::JsonObject* ue_fanstop_browsed_info) {
  std::string key = absl::StrCat("nbs_", app_id, "_uid_", user_id);
  base::JsonArray ue_frep_result;
  ks::infra::RedisClient* client = ks::ad_base::KconfRedis::Instance().
                                   GetAdRedisClient("adUeFansTopFreq",
                                   engine_base::DependDataLevel::WEAK_DEPEND);
  if (client == nullptr) {
    LOG(ERROR) << "adUeFansTopFreq redis client get failed!, key: " << key;
    return;
  }
  int32 timeout = 30;
  std::vector<std::string> fanstop_browsed_info_list;
  ks::infra::RedisErrorCode error_code = client->ListRange(key, &fanstop_browsed_info_list, 0, -1, timeout);
  if (error_code != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
    LOG(WARNING) << "get uefrep fanstop_browse_set info failed! key: " << key
                 << " error_code = " << error_code;
    return;
  }
  if (fanstop_browsed_info_list.size() <= 0) {
    LOG(INFO) << "uefrep fanstop_browse_set info is empty!, key: " << key;
    return;
  }
  kuaishou::fanstop::FanstopBrowsedInfo browsed_info;
  for (const std::string& value : fanstop_browsed_info_list) {
    if (!browsed_info.ParseFromString(value)) {
      LOG(INFO) << "uefrep fanstop_browsed_info parse error! key: " << key;
    }
    // 微妙转换成毫秒，保证前端逻辑统一
    browsed_info.set_timestamp(browsed_info.timestamp()/1000);
    std::string browsed_info_str;
    ::google::protobuf::util::MessageToJsonString(browsed_info, &browsed_info_str);
    base::Json browsed_info_js(base::StringToJson(browsed_info_str));
    ue_frep_result.append(browsed_info_js);
  }
  ue_fanstop_browsed_info->set("fanstop_browsed_type", "ue_frep");
  ue_fanstop_browsed_info->set("fanstop_browsed_info", ue_frep_result);
  LOG(INFO) << "UeFanstopBrowsedSet: " << ue_fanstop_browsed_info->ToString();
}

void FanstopServerShowBrowsedSet(const std::string user_id,
                                 const std::string app_id,
                                 base::JsonArray* fanstop_browsed_info) {
  std::string photo_key = absl::StrCat("nbs_", app_id, "_uid_", user_id);
  std::string live_key = absl::StrCat("nbs_", app_id, "_uid_l_", user_id);
  std::string photo_key_follow = absl::StrCat("nbs_", app_id, "_uid_", user_id, "_follow");
  std::string live_key_follow = absl::StrCat("nbs_", app_id, "_uid_l_", user_id, "_follow");

  base::JsonArray photo_result;
  base::JsonArray live_result;
  base::JsonArray photo_follow_result;
  base::JsonArray live_follow_result;
  GetRedisResult(photo_key, &photo_result);
  GetRedisResult(live_key, &live_result);
  GetRedisResult(photo_key_follow, &photo_follow_result);
  GetRedisResult(live_key_follow, &live_follow_result);

  base::JsonObject photo_result_js;
  base::JsonObject live_result_js;
  base::JsonObject photo_follow_result_js;
  base::JsonObject live_follow_result_js;
  photo_result_js.set("fanstop_browsed_type", "photo");
  photo_result_js.set("fanstop_browsed_info", photo_result);
  live_result_js.set("fanstop_browsed_type", "live");
  live_result_js.set("fanstop_browsed_info", live_result);
  photo_follow_result_js.set("fanstop_browsed_type", "photo_follow");
  photo_follow_result_js.set("fanstop_browsed_info", photo_follow_result);
  live_follow_result_js.set("fanstop_browsed_type", "live_follow");
  live_follow_result_js.set("fanstop_browsed_info", live_follow_result);
  fanstop_browsed_info->append(photo_result_js);
  fanstop_browsed_info->append(live_result_js);
  fanstop_browsed_info->append(photo_follow_result_js);
  fanstop_browsed_info->append(live_follow_result_js);

  LOG(INFO) << "fanstop ServerShowBrowsedSet: " << fanstop_browsed_info->ToString();
}

void GetRedisResult(const std::string& key, base::JsonArray* result) {
  ks::infra::RedisClient* client = ks::ad_base::KconfRedis::Instance().
                                   GetAdRedisClient("fanstopBrowsedSet",
                                   engine_base::DependDataLevel::WEAK_DEPEND);
  if (client == nullptr) {
    LOG(ERROR) << "fanstopBrowsedSet redis client get failed!, key: " << key;
    return;
  }
  int32 timeout = 30;
  std::vector<std::string> fanstop_browsed_info_list;
  ks::infra::RedisErrorCode error_code = client->ListRange(key, &fanstop_browsed_info_list, 0, -1, timeout);
  if (error_code != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
    LOG(WARNING) << "get servershow fanstop_browse_set info failed! key: " << key
                 << " error_cod = " << error_code;
    return;
  }
  if (fanstop_browsed_info_list.size() <= 0) {
    LOG(WARNING) << "servershow fanstop_browse_set info is empty!, key: " << key;
    return;
  }
  kuaishou::fanstop::FanstopBrowsedInfo browsed_info;
  for (const std::string& value : fanstop_browsed_info_list) {
    if (!browsed_info.ParseFromString(value)) {
      LOG(INFO) << "fanstop_browsed_info parse error! key: " << key;
    }
    // 微妙转换成毫秒，保证前端逻辑统一
    browsed_info.set_timestamp(browsed_info.timestamp()/1000);
    std::string browsed_info_str;
    ::google::protobuf::util::MessageToJsonString(browsed_info, &browsed_info_str);
    base::Json browsed_info_js(base::StringToJson(browsed_info_str));
    result->append(browsed_info_js);
  }
}


void FanstopClientShowBrowsedSet(const std::string user_id,
                                 const std::string app_id,
                                 base::JsonArray* ad_action_info) {
  std::string photo_imp_key = absl::StrCat(app_id, "_", 10, "_", user_id);
  std::string item_imp_key = absl::StrCat(app_id, "_", 1, "_", user_id);
  std::string live_imp_key = absl::StrCat(app_id, "_", 60, "_", user_id);
  std::string live_played_key = absl::StrCat(app_id, "_", 68, "_", user_id);
  std::string live_imp_inspire_key = absl::StrCat(app_id, "_", 60, "_", user_id, "_inspire");
  std::string live_played_inspire_key = absl::StrCat(app_id, "_", 68, "_", user_id, "_inspire");

  base::JsonArray photo_imp_result;
  base::JsonArray item_imp_result;
  base::JsonArray live_imp_result;
  base::JsonArray live_played_result;
  GetPipelineRedisResult(photo_imp_key, &photo_imp_result);
  GetPipelineRedisResult(item_imp_key, &item_imp_result);
  GetPipelineRedisResult(live_imp_key, &live_imp_result);
  GetPipelineRedisResult(live_played_key, &live_played_result);
  GetPipelineRedisResult(live_imp_inspire_key, &live_imp_result);
  GetPipelineRedisResult(live_played_inspire_key, &live_played_result);

  base::JsonObject photo_imp_js;
  base::JsonObject item_imp_js;
  base::JsonObject live_imp_js;
  base::JsonObject live_played_js;
  photo_imp_js.set("action_type", "AD_PHOTO_IMPRESSION");
  photo_imp_js.set("ad_detail_info", photo_imp_result);
  item_imp_js.set("action_type", "AD_ITEM_IMPRESSION");
  item_imp_js.set("ad_detail_info", item_imp_result);
  live_imp_js.set("action_type", "AD_LIVE_IMPRESSION");
  live_imp_js.set("ad_detail_info", live_imp_result);
  live_played_js.set("action_type", "AD_LIVE_PLAYED_STARTED");
  live_played_js.set("ad_detail_info", live_played_result);
  ad_action_info->append(photo_imp_js);
  ad_action_info->append(item_imp_js);
  ad_action_info->append(live_imp_js);
  ad_action_info->append(live_played_js);

  LOG(INFO) << "ClientShowBrowsedSet: " << ad_action_info->ToString();
}

void GetPipelineRedisResult(const std::string& key, base::JsonArray* result) {
  ::ks::infra::RedisPipelineClient* piple_client = ks::ad_base::KconfRedis::Instance().
                                      GetAdRedisPipelineClient("adSessionCacheFanstop");
  if (piple_client == nullptr) {
    LOG(ERROR) << "adSessionCacheFanstop redis piple_client get failed!";
    return;
  }
  int64 now_msec = base::GetTimestamp() / 1000;
  int64 range = 7 * 24 * 60 * 60 * 1000;
  std::string max_time = absl::StrCat(now_msec);
  std::string min_time = absl::StrCat(now_msec - range);
  ks::infra::RedisResponse<std::vector<std::string>> response =
                    piple_client->ZGetMembersByScore(key, min_time, max_time);
  int32 timeout = 10;
  std::vector<std::string> ad_detail_info_list;
  auto error_code = response.Get(&ad_detail_info_list, timeout);
  if (error_code != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
    LOG(WARNING) << "get client fanstop_browse_set info failed, key: " << key
                 << " error code: " << error_code;
    return;
  }
  if (ad_detail_info_list.size() <= 0) {
    LOG(INFO) << "client fanstop_browse_set info is empty!, key: " << key;
    return;
  }
  kuaishou::ad::AdDetailInfo ad_detail_info;
  for (const std::string& value : ad_detail_info_list) {
    if (!ad_detail_info.ParseFromString(value)) {
      LOG(INFO) << "ad_detail_info parse error! key: " << key;
    }
    std::string detail_info_str;
    ::google::protobuf::util::MessageToJsonString(ad_detail_info, &detail_info_str);
    base::Json detail_info_js(base::StringToJson(detail_info_str));
    result->append(detail_info_js);
  }
}

void AdServerShowBrowsedSet(const std::string user_id,
                            const std::string app_id,
                            base::JsonArray* ad_browsed_info) {
  std::string redis_name = app_id == "kuaishou" ? "adBrowseSetKuaishou" : "adBrowseSetNebula";
  std::string key = absl::StrCat("abs_", app_id, "_uid_", user_id);
  std::string key_follow = absl::StrCat("follow", app_id, "_", user_id);
  std::vector<std::string> ad_browsed_info_list;
  std::vector<std::string> ad_browsed_info_follow_list;
  GetRedisResult(key, redis_name, &ad_browsed_info_list);
  GetRedisResult(key, redis_name, &ad_browsed_info_follow_list);
  if (ad_browsed_info_list.size() <= 0) {
    LOG(INFO) << "ad_browsed_info_list less 0. redis_name = " << redis_name
              << " redis_key = " << key;
  }
  if (ad_browsed_info_follow_list.size() <= 0) {
    LOG(INFO) << "ad_browsed_info_follow_list less 0. redis_name = " << redis_name
              << " redis_key = " << key;
  }

  base::JsonArray photo_result;
  base::JsonArray live_result;
  base::JsonArray photo_follow_result;
  base::JsonArray live_follow_result;
  ParseAdDspBrowsedInfo(ad_browsed_info_list, &photo_result, &live_result);
  ParseAdDspBrowsedInfo(ad_browsed_info_follow_list, &photo_follow_result,
                                                     &live_follow_result);

  base::JsonObject photo_result_js;
  base::JsonObject live_result_js;
  base::JsonObject photo_follow_result_js;
  base::JsonObject live_follow_result_js;
  photo_result_js.set("ad_dsp_browsed_type", "photo");
  photo_result_js.set("ad_dsp_browsed_info", photo_result);
  live_result_js.set("ad_dsp_browsed_type", "live");
  live_result_js.set("ad_dsp_browsed_info", live_result);
  photo_follow_result_js.set("ad_dsp_browsed_type", "photo_follow");
  photo_follow_result_js.set("ad_dsp_browsed_info", photo_follow_result);
  live_follow_result_js.set("ad_dsp_browsed_type", "live_follow");
  live_follow_result_js.set("ad_dsp_browsed_info", live_follow_result);

  ad_browsed_info->append(photo_result_js);
  ad_browsed_info->append(live_result_js);
  ad_browsed_info->append(photo_follow_result_js);
  ad_browsed_info->append(live_follow_result_js);

  LOG(INFO) << "ad_dsp ServerShowBrowsedSet: " << ad_browsed_info->ToString();
}

void GetRedisResult(const std::string& redis_key, const std::string& redis_name,
                    std::vector<std::string>* browsed_info_list) {
  ::ks::infra::RedisPipelineClient* redis_client = ks::ad_base::KconfRedis::Instance().
                                                  GetAdRedisPipelineClient(redis_name);
  if (redis_client == nullptr) {
    LOG(ERROR) << "redis piple_client get failed!, redis_name = " << redis_name;
    return;
  }
  int32 timeout = 10;
  ks::infra::RedisResponse<std::vector<std::string>> response = redis_client->ListRange(redis_key);
  ks::infra::RedisErrorCode error_code = response.Get(browsed_info_list, timeout);
  if (error_code != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
    LOG(INFO) << "redis_key has missed. redis_name: " << redis_name
              << " redis_key: " << redis_key;
  }
}

void ParseAdDspBrowsedInfo(const std::vector<std::string>& ad_browsed_info_list,
                           base::JsonArray* browsed_info_photo,
                           base::JsonArray* browsed_info_live) {
  kuaishou::ad::AdBrowsedDetailInfo ad_browsed_info;
  for (const std::string& value : ad_browsed_info_list) {
    if (!ad_browsed_info.ParseFromString(value)) {
      LOG(INFO) << "ad_dsp browsed_info parse error!";
    }
    std::string ad_browsed_info_str;
    ::google::protobuf::util::MessageToJsonString(ad_browsed_info, &ad_browsed_info_str);
    base::Json ad_browsed_info_js(base::StringToJson(ad_browsed_info_str));
    if (ad_browsed_info.live_stream_id() > 0) {
      browsed_info_live->append(ad_browsed_info_str);
    } else {
      browsed_info_photo->append(ad_browsed_info_str);
    }
  }
}

void AdDebugKeyProcess(const std::string& flow_type,
                       const std::string& id,
                       const std::string& query,
                       base::JsonObject* result) {
  auto key_map = AdKconfUtil::adDebugKeyMap();
  base::JsonObject reason;
  int32 timeout = 100;
  auto iter = key_map->find(flow_type);
  if (iter == key_map->end()) {
    LOG(ERROR) << "not in adDebugKeyMap. 不支持的流量类型";
    result->set("error_message", "不支持的流量类型");
    result->set("status", -1);
    return;
  }
  std::map<std::string, std::string> field_values;

  if (!AdKconfUtil::enableMoveCreativeDebugOperations()) {
    std::string value = id;
    auto client = ks::ad_base::KconfRedis::Instance().GetAdRedisClient("adFrontDebug");
    std::string key_set;
    if (!engine_base::AdKconfUtil::enableUseNewDebugKey()) {
      key_set = absl::Substitute("engine_debug_$0", iter->second);
    } else {
      double now_ms = base::GetTimestamp() / 1000;
      base::Time now_time = base::Time::FromTimeT(now_ms / 1000);
      base::Time::Exploded struct_time;
      now_time.LocalExplode(&struct_time);
      key_set = absl::Substitute("engine_debug_$0_$1", iter->second, struct_time.hour % 2);
    }

    // 搜索单独设置搜索词
    if (iter->second == "10014" || iter->second == "11014") {
      value = absl::Substitute("$0_$1", id, query);
    }

    //  检查 id 是否已经存在
    bool has_exist = false;
    std::vector<std::string> values;
    client->ListRange(key_set, &values, 0, -1, 3000);
    for (int i = 0; i < values.size(); ++i) {
      if (iter->second == "10014" || iter->second == "11014") {
        // 搜索场景同一搜索词只保留一个 creative
        std::vector<std::string> tokens;
        std::string spilt_str = "_";
        base::SplitString(values[i], spilt_str, &tokens);
        if (tokens.size() == 2 && tokens[1] == query && tokens[0] != id) {
          int64_t del_cnt = 0;
          client->ListRem(key_set, values[i], 0, &del_cnt);
        }
      }
      if (values[i] == value) {
        has_exist = true;
        LOG_EVERY_N(INFO, 50) << "find same value value. key_set = " << key_set << ", value = " << values[i];
        break;
      }
    }
    //  id 不存在, 插入
    if (!has_exist) {
      int64_t length = 0;
      client->ListPushRight(key_set, value, &length, 3000);
      client->Expire(key_set, 3600);
      base::SleepForSeconds(1);
    }

    if (ks::ad_base::AdRandom::GetInt(1, 500) == 1) {
      std::vector<std::string> values_check;
      client->ListRange(key_set, &values_check, 0, -1, 3000);
      LOG(INFO) << "values_check. cur_key = " << key_set << ", value.size" << values_check.size();
    }

    //  获取 debug 结果
    std::string key_get = "engine_debug_" + iter->second + "_" + value;
    if (engine_base::AdKconfUtil::enableFrontNewDebugReply()) {
      double now_ms = base::GetTimestamp() / 1000;
      base::Time now_time = base::Time::FromTimeT(now_ms / 1000);
      base::Time::Exploded struct_time;
      now_time.LocalExplode(&struct_time);
      key_get = absl::Substitute("engine_debug_$0_$1_$2", iter->second, value, struct_time.hour % 2);
    }
    // 内循环特殊前缀
    auto pre_key = AdKconfUtil::preDebugKey();
    if (pre_key->count(flow_type) > 0) {
      key_get = absl::StrCat(pre_key->find(flow_type)->second, key_get);
      LOG(INFO) << "inner_key_get = " << key_get;
    }
    ks::infra::RedisErrorCode ret_get = client->HashGetAllFields(key_get, &field_values);
    if (ret_get != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
      LOG(ERROR) << "get debug_message fail. debug_key = " << key_get << ", error_code = " << ret_get;
      result->set("error_message", "获取调试数据失败");
      result->set("status", -1);
      return;
    }
  } else {
    bool ret = engine_base::SetCreativeDebugInfo(iter->second, id, query);
    if (ret) {
      base::SleepForSeconds(1);
    }
    // 内循环特殊前缀
    auto pre_key = AdKconfUtil::preDebugKey();
    std::string special_prefix;
    if (pre_key->count(flow_type) > 0) {
      special_prefix = pre_key->find(flow_type)->second;
    }
    // 获取 debug 结果
    bool res = engine_base::GetCreativeDebugResult(&field_values, iter->second, id, query, special_prefix);
    if (!res) {
      result->set("error_message", "获取调试数据失败");
      result->set("status", -1);
      return;
    }
  }

  serving_base::mysql_util::DbConnManager::Option option;
  option.host = FLAGS_sql_host;
  option.schema = FLAGS_sql_schema;
  option.user = FLAGS_sql_user;
  option.passwd = FLAGS_sql_passwd;
  serving_base::mysql_util::DbConnManager* manager = new serving_base::mysql_util::DbConnManager(option);
  if (!manager->Connect()) {
    LOG(ERROR) << "connect db failed";
    result->set("error_message", "connect db failed");
    result->set("status", -1);
    return;
  }
  //  过滤原因黑名单
  CleanFilterCondition(iter->second, id, query, &field_values);
  // 过滤原因中如果既有正常广告过滤原因，也有特殊的广告过滤原因，去除这些特殊的过滤原因
  auto special_reasons = AdKconfUtil::whiteBoxDebugSpecialFilterReasons()->data();
  int special_reason_cnt = 0;
  for (const auto& reason : special_reasons.GetReasons()) {
    if (field_values.count(reason) > 0) {
      ++special_reason_cnt;
    }
  }
  bool is_exclude_special_reason = false;
  if (field_values.size() > special_reason_cnt) {
    is_exclude_special_reason = true;
  }

  base::JsonArray filter_info_array;
  for (const auto& iter : field_values) {
    if (is_exclude_special_reason && special_reasons.IsSpecialReason(iter.first)) {
      continue;
    }
    base::JsonObject filter_info;
    std::string sql_query = "select * from AdTraceFilterCondition where id = '" + iter.first + "'";
    sqlQuery(sql_query, manager, &filter_info);
    filter_info.set("reason_times", iter.second);
    filter_info_array.append(filter_info);
  }
  result->set("reason", filter_info_array);
  result->set("status", "200");
}

void CleanFilterCondition(const std::string& flow_type,
                          const std::string& id,
                          const std::string& query,
                          std::map<std::string, std::string>* field_values) {
  auto black_set = AdKconfUtil::filterConditionBlackList();
  for (auto iter = field_values->begin(); iter != field_values->end();) {
    if (black_set->count(iter->first) > 0) {
      LOG(INFO) << "balck_list filter. flow_type = " << flow_type
                << ", creative_id = " << id
                << ", query = " << query
                << ", filter_reason = " << iter->first;
      iter = field_values->erase(iter);
    } else {
      iter++;
    }
  }
}

bool GetAccountId(const std::string& data_level,
                  const int64& data_level_id,
                  int64* account_id,
                  base::JsonObject* result) {
  // 请求正排
  std::string kess_name = "grpc_adForwardIndexService_algorithm";
  auto client = ks::ad_base::AdKessClient::ClientOfKey
                <kuaishou::ad::forward_index::kess::AdForwardIndexService>(kess_name);
  ks::kess::rpc::grpc::Options options;
  options.SetTimeout(std::chrono::milliseconds(500));
  ::grpc::Status status;
  if (data_level == "unit") {
    FORWARD_REQ(Unit, data_level_id, unit);
    *account_id = res.items(0).unit().account_id();
  } else if (data_level == "campaign") {
    FORWARD_REQ(Campaign, data_level_id, campaign);
    *account_id = res.items(0).campaign().account_id();
  }

  LOG(INFO) << "QuerySubLevelIds data_level = " << data_level
            << ", id = " << data_level_id
            << ", account_id = " << *account_id;
  return true;
}

void QuerySubLevelIdsProcess(const std::string& data_level,
                             const int64& data_level_id,
                             const int64& account_id,
                             base::JsonObject* result) {
  if (data_level == "account") {
    GetCampaignIds(data_level, data_level_id, account_id, result);
  } else if (data_level == "campaign") {
    GetUnitIds(data_level, data_level_id, account_id, result);
  } else if (data_level == "unit") {
    GetAllCreativeIds(data_level, data_level_id, account_id, result);
  } else {
    LOG(ERROR) << "QuerySubLevelIds failed: unknown data_level."
               << " data_level = " << data_level
               << ", id = " << data_level_id;
    result->set("status", "-1");
    result->set("error_message", "unknown data_level");
  }
}

void GetCampaignIds(const std::string& data_level,
                    const int64& data_level_id,
                    const int64& account_id,
                    base::JsonObject* result) {
  kuaishou::adcore::meta::CampaignIdsGetRequest request;
  kuaishou::adcore::meta::IdsGetResponse response;
  std::string kess_name = "grpc_adCoreMetaOriginRpcService";
  auto client = ks::ad_base::AdKessClient::ClientOfKey
                <kuaishou::adcore::meta::kess::AdCoreMetaOriginService>(kess_name);
  ks::kess::rpc::grpc::Options options;
  options.SetTimeout(std::chrono::milliseconds(5000));

  request.set_account_id(account_id);
  request.add_put_status_in(1);
  auto* meta_cur_scope = request.mutable_meta_current_scope();
  meta_cur_scope->set_caller_type(3);
  meta_cur_scope->mutable_account()->set_account_id(account_id);

  auto status = client.second->SelectOne()->GetCampaignIds(options, request, &response);

  if (status.error_code() != grpc::StatusCode::OK) {
    LOG(ERROR) << "QuerySubLevelIds failed: grpc GetCampaignIds error."
              << " grpc_status: " << status.error_code()
              << ", data_level = " << data_level
              << ", id = " << data_level_id;
    result->set("status", "-1");
    result->set("error_message", "grpc GetCampaignIds error");
    return;
  }

  base::JsonObject info;
  base::JsonArray sub_level_ids;
  for (int i = 0; i < response.id_size(); ++i) {
    sub_level_ids.append(static_cast<int64>(response.id(i)));
  }
  info.set("sub_level_type", "campaign");
  info.set("sub_level_ids", sub_level_ids);
  result->set("info", info);
}

void GetUnitIds(const std::string& data_level,
                    const int64& data_level_id,
                    const int64& account_id,
                    base::JsonObject* result) {
  kuaishou::adcore::meta::UnitIdsGetRequest request;
  kuaishou::adcore::meta::IdsGetResponse response;
  std::string kess_name = "grpc_adCoreMetaOriginRpcService";
  auto client = ks::ad_base::AdKessClient::ClientOfKey
                <kuaishou::adcore::meta::kess::AdCoreMetaOriginService>(kess_name);
  ks::kess::rpc::grpc::Options options;
  options.SetTimeout(std::chrono::milliseconds(5000));

  request.set_account_id(account_id);
  request.add_campaign_id(data_level_id);
  request.add_put_status_in(1);
  auto* meta_cur_scope = request.mutable_meta_current_scope();
  meta_cur_scope->set_caller_type(3);
  meta_cur_scope->mutable_account()->set_account_id(account_id);

  auto status = client.second->SelectOne()->GetUnitIds(options, request, &response);

  if (status.error_code() != grpc::StatusCode::OK) {
    LOG(ERROR) << "QuerySubLevelIds failed: grpc GetUnitIds error."
              << " grpc_status: " << status.error_code()
              << ", data_level = " << data_level
              << ", id = " << data_level_id;
    result->set("status", "-1");
    result->set("error_message", "grpc GetUnitIds error");
    return;
  }

  base::JsonObject info;
  base::JsonArray sub_level_ids;
  for (int i = 0; i < response.id_size(); ++i) {
    sub_level_ids.append(static_cast<int64>(response.id(i)));
  }
  info.set("sub_level_type", "unit");
  info.set("sub_level_ids", sub_level_ids);
  result->set("info", info);
}

void GetAllCreativeIds(const std::string& data_level,
                    const int64& data_level_id,
                    const int64& account_id,
                    base::JsonObject* result) {
  kuaishou::adcore::meta::CreativeIdsGetRequest request;
  kuaishou::adcore::meta::IdsGetResponse response;
  kuaishou::adcore::meta::IdsGetResponse program_response;
  std::string kess_name = "grpc_adCoreMetaOriginRpcService";
  auto client = ks::ad_base::AdKessClient::ClientOfKey
                <kuaishou::adcore::meta::kess::AdCoreMetaOriginService>(kess_name);
  ks::kess::rpc::grpc::Options options;
  options.SetTimeout(std::chrono::milliseconds(5000));

  request.set_account_id(account_id);
  request.add_unit_id(data_level_id);
  request.add_put_status_in(1);
  auto* meta_cur_scope = request.mutable_meta_current_scope();
  meta_cur_scope->set_caller_type(3);
  meta_cur_scope->mutable_account()->set_account_id(account_id);

  auto status = client.second->SelectOne()->GetCreativeIds(options, request, &response);
  auto program_status = client.second->SelectOne()
                        ->GetAdvProgramCreativeIds(options, request, &program_response);

  if (status.error_code() != grpc::StatusCode::OK && program_status.error_code() != grpc::StatusCode::OK) {
    LOG(ERROR) << "QuerySubLevelIds failed: grpc GetAllCreativeIds error."
              << " grpc_status: " << status.error_code()
              << ", program_status: " << program_status.error_code()
              << ", data_level = " << data_level
              << ", id = " << data_level_id;
    result->set("status", "-1");
    result->set("error_message", "grpc GetCreativeIds error");
    return;
  }
  if (status.error_code() != grpc::StatusCode::OK) {
    LOG(ERROR) << "QuerySubLevelIds failed: grpc GetCreativeIds error."
              << " grpc_status: " << status.error_code()
              << ", data_level = " << data_level
              << ", id = " << data_level_id;
  }
  if (program_status.error_code() != grpc::StatusCode::OK) {
    LOG(ERROR) << "QuerySubLevelIds failed: grpc GetAdvProgramCreativeIds error."
              << ", program_status: " << program_status.error_code()
              << ", data_level = " << data_level
              << ", id = " << data_level_id;
  }

  base::JsonObject info;
  base::JsonArray sub_level_ids;
  base::JsonArray sub_level_program_ids;

  for (int i = 0; i < response.id_size(); ++i) {
    sub_level_ids.append(static_cast<int64>(response.id(i)));
  }
  for (int i = 0; i < program_response.id_size(); ++i) {
    sub_level_program_ids.append(static_cast<int64>(program_response.id(i)));
  }
  info.set("sub_level_type", "creative");
  info.set("sub_level_ids", sub_level_ids);
  info.set("sub_level_program_ids", sub_level_program_ids);
  result->set("info", info);
}

}  // namespace ad_diag_api
}  // namespace ks
