#pragma once

#include <stddef.h>
#include <string>
#include <vector>
#include <unordered_set>
#include <unordered_map>
#include <map>
#include "glog/logging.h"
#include "ks/util/json.h"
#include "teams/ad/ad_diag_api/utils/bg_task/update_sql_table/update_sql_table.h"
#include "serving_base/mysql_util/db_conn_manager.h"
#include "mysql-connector/cppconn/resultset.h"

#undef PACKAGE_NAME
#include "teams/ad/ad_diag_api/utils/bg_task/ad_dsp_position_data/ad_dsp_position_data.h"


namespace ks {
namespace ad_diag_api {

// 删除字符首尾空格
std::string StrStrip(const std::string& str);
// 检查一个字符串是否是数字
bool IsNumber(const std::string& str);
// 组装 sql 语句的函数
std::string ComposeSQL(const std::string& table_name,
                        const std::string& str_id,
                        const std::string& show_order,
                        const std::string& name);
// 查询现有 sql 表中所含的 str_id
void QueryExistSqlInfo(const std::string& table_name,
                       const std::string& str_id,
                       serving_base::mysql_util::DbConnManager* manager,
                       std::unordered_set<std::string>* exist_id_set);
// 被更新的 sql 表, 必须有 update_time 列, 才能更新成功
// 针对没有写 show_order 的文件, 使用该解析方法, show_order 在拼装 sql 时写死, 为 100.0
bool IsOneAnnotation(const std::string& table_name, const base::Json* annotation_handler,
                     std::unordered_map<std::string, std::string>* col_name_map);
// 针对写 show_order 的文件, 使用该解析方法
bool IsTwoAnnotation(const std::string& table_name, const base::Json* annotation_handler,
                     std::unordered_map<std::string, std::string>* col_name_map);
// 该方法主要针对 AdTraceFilterCondition 这个表进行解析
bool IsSixAnnotation(const std::string& table_name, const base::Json* annotation_handler,
                     std::unordered_map<std::string, std::string>* col_name_map);
// 该方法主要针对 AdTraceFilterCondition 这个表进行解析
bool IsEightAnnotation(const std::string& table_name, const base::Json* annotation_handler,
                     std::unordered_map<std::string, std::string>* col_name_map);
// 解析无需注释写入 sql 表的情况, 主要针对 MutiRetrievalTag 表
bool IsNoAnnotationProcessor(const std::string& table_name, const base::Json* annotation_handler,
                     std::unordered_map<std::string, std::string>* col_name_map);

// 将 unordered_map<string, string> 形式结构转换为 string 表示
std::string MapDoubleStringToString(const std::unordered_map<std::string, std::string>& map_double_string);

// 更新正式环境 kconf 内容
void UpdateProductionKconf(const std::string& kconf_path, const std::string& kconf_value);

// 更新测试环境 kconf 内容
void UpdateCandidateKconf(const std::string& kconf_path, const std::string& kconf_data);
void CreateCandidateKconf(const std::string& kconf_path);
void GetCandidateKconfDate(const std::string& kconf_path);

// 粉条浏览记录获取
void FanstopServerShowBrowsedSet(const std::string user_id,
                                 const std::string app_id,
                                 base::JsonArray* fanstop_browsed_info);
void FanstopClientShowBrowsedSet(const std::string user_id,
                                 const std::string app_id,
                                 base::JsonArray* ad_action_info);

void FanstopUeFreq(const std::string user_id,
                   const std::string app_id,
                   base::JsonObject* ue_fanstop_browsed_info);
void GetRedisResult(const std::string& key, base::JsonArray* result);
void GetPipelineRedisResult(const std::string& key, base::JsonArray* result);

void AdServerShowBrowsedSet(const std::string user_id,
                            const std::string app_id,
                            base::JsonArray* ad_browsed_info);
void GetRedisResult(const std::string& redis_key, const std::string& redis_name,
                    std::vector<std::string>* browsed_info_list);
void ParseAdDspBrowsedInfo(const std::vector<std::string>& ad_browsed_info_list,
                           base::JsonArray* browsed_info_photo,
                           base::JsonArray* browsed_info_live);
//  过滤原因 debug 处理函数
void AdDebugKeyProcess(const std::string& flow_type,
                       const std::string& id,
                       const std::string& query,
                       base::JsonObject* result);
void CleanFilterCondition(const std::string& flow_type,
                          const std::string& id,
                          const std::string& query,
                          std::map<std::string, std::string>* field_values);

void QuerySubLevelIdsProcess(const std::string& data_level,
                             const int64& data_level_id,
                             const int64& account_id,
                             base::JsonObject* result);
//  查询正排，获取 AccountId
bool GetAccountId(const std::string& data_level,
                  const int64& data_level_id,
                  int64* account_id,
                  base::JsonObject* result);
// 查询平台接口，获取子层级 ids
void GetCampaignIds(const std::string& data_level,
                    const int64& data_level_id,
                    const int64& account_id,
                    base::JsonObject* result);
void GetUnitIds(const std::string& data_level,
                    const int64& data_level_id,
                    const int64& account_id,
                    base::JsonObject* result);
void GetAllCreativeIds(const std::string& data_level,
                    const int64& data_level_id,
                    const int64& account_id,
                    base::JsonObject* result);

}  // namespace ad_diag_api
}  // namespace ks

