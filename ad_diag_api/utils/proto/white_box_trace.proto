syntax = "proto3";

package kuaishou.ad.whitebox.trace;

option java_package = "com.kuaishou.ad.whitebox.trace";
option java_outer_classname = "WhiteBoxTrace";

// 这个 pb 文件白盒 c++ 服务与 java 服务都在使用, 但是 proto 都是在各自服务中定义, 不要修改已有字段

enum ServiceType {
  UNKNOWN = 0;
  AD_DIAG_API = 1;
  AD_WHITE_BOX_API = 2;
}

message WhiteBoxApiTraceLog {
  int64 timestamp = 1;
  int64 cost_time_ms = 2;
  string user_name = 3;
  string uri = 4;
  string tab_type = 5;
  string chart_name = 6;
  string index_name = 7;
  string query_type = 8;
  string ad_queue_type = 9;
  ServiceType service_type = 10;
  string service_stage = 11;
}
