#pragma once

#include <algorithm>
#include <set>
#include <string>
#include <memory>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "ks/util/json.h"
#include "base/common/logging.h"
#include "absl/strings/numbers.h"
#include "absl/strings/str_cat.h"
#include "absl/strings/str_split.h"
#include "teams/ad/ad_base/src/hash/hash.h"
#include "teams/ad/ad_diag_api/utils/kconf/kconf.h"
#include "teams/ad/ad_diag_api/utils/http_client.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"

namespace ks {
namespace ad_diag_api {

struct StatisticsData {
  int64 id{0};
  int total_cnt{0};
  int index_cnt{0};
  double avg_rank_score{0};
  double min_rank_score{0};
  double max_rank_score{0};
  static StatisticsData FromString(const std::string& value) {
    StatisticsData data;
    std::vector<std::string> tokens = absl::StrSplit(value, "_");
    if (tokens.size() != 5) return data;
    ignore_result(absl::SimpleAtoi(tokens[0], &data.total_cnt));
    ignore_result(absl::SimpleAtoi(tokens[1], &data.index_cnt));
    ignore_result(absl::SimpleAtod(tokens[2], &data.avg_rank_score));
    ignore_result(absl::SimpleAtod(tokens[3], &data.min_rank_score));
    ignore_result(absl::SimpleAtod(tokens[4], &data.max_rank_score));
    return data;
  }
  double GetPassRatio() const {
    return total_cnt == 0 ? 0 : index_cnt * 1.0 / total_cnt;
  }
};

class CreativeOptStatsGetter {
 public:
  std::string GetData(const std::string& type, const std::string& products, const std::string& accounts) {
    // 0. 构造 redis key
    std::string product_aids_key = "p2a_";
    std::string account_stats_key = "a2c_";
    if (type == "universe") {
      product_aids_key = absl::StrCat("universe_", product_aids_key);
      account_stats_key = absl::StrCat("universe_", account_stats_key);
    } else if (type == "external") {
      product_aids_key = absl::StrCat("external_", product_aids_key);
      account_stats_key = absl::StrCat("external_", account_stats_key);
    } else {
      return "[]";
    }
    // 1. 解析产品和账户
    std::vector<std::string> product_list = absl::StrSplit(products, ',');
    std::vector<std::string> account_list = absl::StrSplit(accounts, ',');
    if (product_list.empty() && account_list.empty()) return "[]";
    // 2. 查询结果
    // 2.1 构造 client
    auto* client = ks::ad_base::KconfRedis::Instance().GetAdRedisClient("adCreativeServerResultView");
    std::vector<std::string> p2aids;
    // 2.2 查询 product 下 account
    if (!product_list.empty()) {
      std::vector<std::string> keys;
      for (const auto& product : product_list) {
        if (product.empty()) continue;
        for (int shard = 0; shard < 10; shard++) {
          keys.emplace_back(absl::StrCat(product_aids_key, shard, "_", ks::ad_base::FNVHash()(product)));
        }
      }
      if (!keys.empty()) client->MGet(keys, &p2aids, 1000);
    }
    // 2.3 查询 account 维度的胜出统计数据
    if (!account_list.empty() || !p2aids.empty()) {
      std::vector<int64> aids;
      std::vector<std::string> keys;
      std::vector<std::string> values;
      // 2.3.1 原始输入的 account
      for (const auto& account : account_list) {
        if (account.empty()) continue;
        keys.emplace_back(absl::StrCat(account_stats_key, account.substr(account.size() - 1), "_", account));
        aids.emplace_back();
        ignore_result(absl::SimpleAtoi(account, &aids.back()));
      }
      // 2.3.2 从 product 中获取的 account
      for (const auto& p2aid : p2aids) {
        if (p2aid.empty()) continue;
        for (const auto& aid : absl::StrSplit(p2aid, '|')) {
          if (aid.empty()) continue;
          keys.emplace_back(absl::StrCat(account_stats_key, aid.substr(aid.size() - 1), "_", aid));
          aids.emplace_back();
          ignore_result(absl::SimpleAtoi(aid, &aids.back()));
        }
      }
      // 2.3.3 查询 redis
      client->MGet(keys, &values, 1000);
      // 2.4 解析结果
      if (values.empty()) return "[]";
      if (keys.size() != values.size()) return "[]";
      std::vector<StatisticsData> data_vec;
      for (int i = 0; i < keys.size(); i++) {
        StatisticsData data = StatisticsData::FromString(values[i]);
        if (data.total_cnt == 0) continue;
        data.id = aids[i];
        data_vec.emplace_back(data);
      }
      // 2.4.1 排序
      std::sort(data_vec.begin(), data_vec.end(), [&](const auto& a, const auto& b) {
        if (a.total_cnt == 0) return false;
        if (a.total_cnt == 0) return true;
        double pass_a = a.total_cnt == 0 ? 0 : a.index_cnt * 1.0 / a.total_cnt;
        double pass_b = b.total_cnt == 0 ? 0 : b.index_cnt * 1.0 / b.total_cnt;
        if (pass_a < pass_b) return true;
        if (pass_a > pass_b) return false;
        return a.total_cnt > b.total_cnt;
      });
      // 2.4.2 取前 N 个结果返回
      int cnt = AdKconfUtil::accountCntPerProduct();
      base::Json result(json_array());
      for (int i = 0; i < data_vec.size() && i < cnt; i++) {
        base::Json item(json_object());
        item.set("id", data_vec[i].id);
        item.set("total_cnt", data_vec[i].total_cnt);
        item.set("index_cnt", data_vec[i].index_cnt);
        item.set("pass_ratio", data_vec[i].GetPassRatio());
        item.set("avg_rank_score", data_vec[i].avg_rank_score);
        item.set("min_rank_score", data_vec[i].min_rank_score);
        item.set("max_rank_score", data_vec[i].max_rank_score);
        result.append(item);
      }
      return result.ToString();
    }
    return "[]";
  }

 private:
  static constexpr const char* redisCluster = "adCreativeServerResultView";   // NOLINT
  std::string product_aids_key = "p2a_";
  std::string account_stats_key = "a2c_";
};

}  // namespace ad_diag_api
}  // namespace ks
