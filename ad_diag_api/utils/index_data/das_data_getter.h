#pragma once

#include <algorithm>
#include <string>
#include <memory>
#include <unordered_map>
#include <vector>

#include "ks/util/json.h"
#include "base/common/logging.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_diag_api/utils/http_client.h"
#include "teams/ad/ad_diag_api/utils/index_data/types.h"
#include "teams/ad/ad_diag_api/utils/index_data/instance_convert.h"

namespace ks {
namespace ad_diag_api {

class DasIndexDataGetter {
 public:
  static std::shared_ptr<std::vector<AdInstance>> GetIncDataFromHttpApi(int64_t id,
                                                                        const std::string table,
                                                                        int64_t start, int64_t end) {
    ks::ad_diag_api::HttpClient client;
    std::string target_url = "https://ad-data-integration.test.gifshow.com/api/edata/clickhouse/query/das/inc";   // NOLINT
    std::unordered_map<std::string, std::string> params;
    params.emplace("tableName", table);
    params.emplace("id", std::to_string(id));
    params.emplace("startTime", std::to_string(start));
    params.emplace("endTime", std::to_string(end));
    auto code = client.Get(target_url.c_str(), params);
    auto& resp = client.get_result_data();
    std::shared_ptr<std::vector<AdInstance>> result = std::make_shared<std::vector<AdInstance>>();
    if (code != CURLE_OK) return result;
    base::Json resp_json(base::StringToJson(resp));
    base::Json* data_json = resp_json.Get("data");
    if (data_json == nullptr) return result;
    if (!data_json->IsArray())  return result;
    for (auto* item : data_json->array()) {
      auto fields = item->GetString("fields");
      base::Json fields_json(base::StringToJson(fields));
      auto& instance = result->emplace_back();
      auto type = FindWithDefault(kTableName2EnumTypeMap, table, AdEnum::UNKNOWN_AD_INSTANCE_TYPE);
      instance.set_type(type);
      instance.set_process_time(item->GetInt("process_time", 0));
      instance.set_binlog_time(item->GetInt("binlog_time", 0));
      for (auto* f : fields_json.array()) {
        auto* field = instance.add_fields();
        field->set_name(f->GetString("name"));
        if (auto s = f->GetString("string_value"); !s.empty()) {
          field->set_string_value(s);
        }
        if (auto i = f->GetInt("int_value", 0); i != 0) {
          field->set_int_value(i);
        }
        if (auto d = f->GetFloat("double_value", 0.0); d != 0) {
          field->set_double_value(d);
        }
      }
      // 解析 modified list
      auto modified_list = item->GetString("modify_field_list");
      // 临时使用 business_trace_info 存储 modified list
      instance.set_business_trace_info(modified_list);
      ConvertFieldsToExtensions(&instance);
    }
    // 按照时间距离现在从近到远排序
    std::sort(result->begin(), result->end(), [](const AdInstance& a, const AdInstance& b) {
      return a.process_time() <= b.process_time();
    });
    return result;
  }

  static std::shared_ptr<std::vector<AdInstance>> GetBaseDataFromHttpApi(int64_t id,
                                                                         const std::string table,
                                                                         int64_t start, int64_t end) {
    ks::ad_diag_api::HttpClient client;
    std::string target_url = "https://ad-data-integration.test.gifshow.com/api/edata/clickhouse/query/das/base";   // NOLINT
    std::unordered_map<std::string, std::string> params;
    params.emplace("tableName", table);
    params.emplace("id", std::to_string(id));
    params.emplace("startTime", std::to_string(start));
    params.emplace("endTime", std::to_string(end));
    auto code = client.Get(target_url.c_str(), params);
    auto& resp = client.get_result_data();
    std::shared_ptr<std::vector<AdInstance>> result = std::make_shared<std::vector<AdInstance>>();
    if (code != CURLE_OK) return result;
    base::Json resp_json(base::StringToJson(resp));
    base::Json* data_json = resp_json.Get("data");
    if (data_json == nullptr) return result;
    if (!data_json->IsArray())  return result;
    for (auto* item : data_json->array()) {
      auto& instance = result->emplace_back();
      std::string pb_json_string = item->GetString("data_json");
      int64_t base_version = item->GetInt("benchmark_time", 0);
      instance.set_process_time(base_version);
      instance.set_type(FindWithDefault(kTableName2EnumTypeMap, table, AdEnum::UNKNOWN_AD_INSTANCE_TYPE));
      SetExtension(&instance, pb_json_string);
    }
    // 按照时间距离现在从近到远排序
    std::sort(result->begin(), result->end(), [](const AdInstance& a, const AdInstance& b) {
      return a.process_time() <= b.process_time();
    });
    return result;
  }
};

}  // namespace ad_diag_api
}  // namespace ks
