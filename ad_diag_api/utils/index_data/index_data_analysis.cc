#include "teams/ad/ad_diag_api/utils/index_data/index_data_analysis.h"

#include <algorithm>
#include <string>
#include <memory>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "ks/util/json.h"
#include "perfutil/perfutil.h"
#include "base/common/closure.h"
#include "base/common/logging.h"
#include "base/common/map_util-inl.h"
#include "base/thread/thread_pool.h"
#include "absl/strings/str_split.h"
#include "absl/strings/str_join.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_diag_api/utils/kconf/kconf.h"
#include "teams/ad/ad_diag_api/utils/http_client.h"
#include "teams/ad/ad_diag_api/utils/index_data/types.h"
#include "teams/ad/ad_diag_api/utils/index_data/tools.h"
#include "teams/ad/ad_diag_api/utils/index_data/index_status.h"
#include "teams/ad/ad_diag_api/utils/index_data/table_valid.h"
#include "teams/ad/ad_diag_api/utils/index_data/instance_convert.h"
#include "teams/ad/ad_diag_api/utils/index_data/das_data_getter.h"
#include "teams/ad/ad_diag_api/utils/index_data/creative_score_data_getter.h"
#include "teams/ad/ad_diag_api/utils/index_data/index_build_trace_log_getter.h"

namespace ks {
namespace ad_diag_api {

void IndexDataAnalyzer::Analyze() {
  thread::ThreadPool pool(10);
  // 1. 查询分析 das 物料结果
  pool.AddTask(::NewCallback(this, &IndexDataAnalyzer::QueryDasData, AdEnum::CREATIVE));
  pool.AddTask(::NewCallback(this, &IndexDataAnalyzer::QueryDasData, AdEnum::UNIT));
  pool.AddTask(::NewCallback(this, &IndexDataAnalyzer::QueryDasData, AdEnum::CAMPAIGN));
  pool.AddTask(::NewCallback(this, &IndexDataAnalyzer::QueryDasData, AdEnum::ACCOUNT));
  // 2. 查询分析 打分 结果
  pool.AddTask(::NewCallback(this, &IndexDataAnalyzer::QueryScoreData, 1));
  pool.AddTask(::NewCallback(this, &IndexDataAnalyzer::QueryScoreData, 4));
  pool.AddTask(::NewCallback(this, &IndexDataAnalyzer::QueryScoreData, 2));
  pool.AddTask(::NewCallback(this, &IndexDataAnalyzer::QueryScoreData, 5));
  // 3. 查询分析 索引 结果
  pool.AddTask(::NewCallback(this, &IndexDataAnalyzer::QueryIndexTraceData, AdEnum::CREATIVE));
  pool.AddTask(::NewCallback(this, &IndexDataAnalyzer::QueryIndexTraceData, AdEnum::UNIT));
  pool.AddTask(::NewCallback(this, &IndexDataAnalyzer::QueryIndexTraceData, AdEnum::CAMPAIGN));
  pool.AddTask(::NewCallback(this, &IndexDataAnalyzer::QueryIndexTraceData, AdEnum::ACCOUNT));
  // 4. 查询 ps 模型物料流下发结果
  pool.AddTask(::NewCallback(this, &IndexDataAnalyzer::QueryPsItemUpdateTraceData));
  // 等待完成
  pool.JoinAll();
}

void IndexDataAnalyzer::FillIndexStatusIntoJson(const std::string& key, base::Json *json) {
  base::Json index_status(json_object());
  auto fill_status = [&](const std::string& key, std::vector<IndexStatus>* score_status) {
    if (score_status == nullptr) return;
    base::Json status(json_array());
    // 合并 index status
    // 1. 合并 das 结果
    std::vector<IndexStatus> final_status(das_indexes_status_.begin(), das_indexes_status_.end());
    // 2. 合并 打分 结果
    if (index_id_data_.table == "ad_dsp_creative") {
      final_status.insert(final_status.end(), score_status->begin(), score_status->end());
    }
    // 3. 合并 索引 trace 结果
    auto trace_iter = index_trace_status_.find(key);
    if (trace_iter != index_trace_status_.end() &&
        trace_iter->second != nullptr &&
        trace_iter->second->size() > 0) {
      final_status.insert(final_status.end(), trace_iter->second->begin(), trace_iter->second->end());
    }
    // 4.合并召回构建结果
    auto target_iter = target_trace_status_.find(key);
    if (target_iter != target_trace_status_.end() &&
        target_iter->second != nullptr &&
        target_iter->second->size() > 0) {
      final_status.insert(final_status.end(), target_iter->second->begin(), target_iter->second->end());
    }
    // 按照时间排序
    std::sort(final_status.begin(), final_status.end(), [](const IndexStatus& a, const IndexStatus& b) {
      return a.timestamp >= b.timestamp;
    });
    // 写入 json 结果
    for (auto& s : final_status) {
      base::Json status_json(json_object());
      base::Time ts = base::Time::FromDoubleT((double)s.timestamp / 1e3);
      std::string ts_str;
      ts.ToStringInSeconds(&ts_str);
      status_json.set("id", s.id);
      status_json.set("timestamp", ts_str);
      status_json.set("side", s.side);
      status_json.set("status", s.status);
      status_json.set("filter", s.filter);
      status_json.set("filter_detail", s.filter_detail);
      status_json.set("suggestion", s.suggestion);
      status_json.set("table", s.table);
      status.append(status_json);
    }
    index_status.set(key, status);
  };
  // 1. 填充 external
  fill_status("external", &external_score_indexes_status_);
  // 4. 填充 live
  fill_status("live", &internal_score_indexes_status_);
  // 4. 填充 inner photo
  fill_status("inner", &internal_score_indexes_status_);
  // 2. 填充 universe
  fill_status("universe", &universe_score_indexes_status_);
  // 3. 填充 search
  fill_status("search", &search_score_indexes_status_);
  // 3. 填充 bidword
  fill_status("bidword", &search_score_indexes_status_);
  json->set(key, index_status);
}

void IndexDataAnalyzer::FillDasDataIntoJson(const std::string& key, base::Json *json) {}

void IndexDataAnalyzer::FillScoreDataIntoJson(const std::string& key, base::Json *json) {
  base::Json score_data(json_object());
  auto fill_data = [&](const std::string& cur_key, std::vector<IndexStatus>* score_status) {
    if (score_status == nullptr) return;
    base::Json status(json_array());
    for (auto& s : *score_status) {
      base::Json info(json_object());
      base::Time ts = base::Time::FromDoubleT((double)s.timestamp / 1e3);
      std::string ts_str;
      ts.ToStringInSeconds(&ts_str);
      std::string first_index_ts_str;
      if (s.creative_first_index_time <= 0) {
        first_index_ts_str = "之前未进索引";
      } else {
        ts = base::Time::FromDoubleT((double)s.creative_first_index_time / 1e3);
        ts.ToStringInSeconds(&first_index_ts_str);
      }
      info.set("timestamp", ts_str);
      info.set("first_index_time", first_index_ts_str);
      info.set("creative_id", s.creative_id);
      info.set("unit_id", s.unit_id);
      info.set("campaign_id", s.campaign_id);
      info.set("account_id", s.account_id);
      info.set("photo_id", s.photo_id);
      info.set("status", s.status);
      info.set("msg", s.filter_detail);
      status.append(info);
    }
    score_data.set(cur_key, status);
  };
  fill_data("external", &external_score_indexes_status_);
  fill_data("internal", &internal_score_indexes_status_);
  fill_data("inner", &internal_score_indexes_status_);
  fill_data("universe", &universe_score_indexes_status_);
  fill_data("search", &search_score_indexes_status_);
  fill_data("bidword", &search_score_indexes_status_);
  json->set(key, score_data);
}

void IndexDataAnalyzer::FillBuildDataIntoJson(const std::string& key, base::Json *json) {}

void IndexDataAnalyzer::FillTargetDataIntoJson(const std::string& key, base::Json *json) {}

void IndexDataAnalyzer::FillPsItemUpdateInfoIntoJson(const std::string& key, base::Json *json) {
  base::Json ps_data_internal(json_array());
  base::Json ps_data_external(json_array());
  if (ps_item_update_data_ != nullptr && ps_item_update_data_->size() > 0) {
    for (const auto& d : *ps_item_update_data_) {
      base::Json info(json_object());
      info.set("id", d.id);
      info.set("timestamp", d.timestamp);
      info.set("biz", d.biz);
      info.set("status", d.status);
      info.set("reason", d.reason);
      if (d.biz_code == 1) {
        ps_data_external.append(info);
      } else if (d.biz_code == 2) {
        ps_data_internal.append(info);
      }
    }
  }
  base::Json ps_data(json_object());
  ps_data.set("internal", ps_data_internal);
  ps_data.set("external", ps_data_external);
  json->set(key, ps_data);
}

// 查询 das 产出数据并做分析
void IndexDataAnalyzer::QueryDasData(AdEnum::AdInstanceType type) {
  int64_t cur_ts = base::GetTimestamp() / 1000;
  // 对增量来说，所有层级都需要查询，因为上层级状态变更会影响下层级
  // 全量只用查询本层级，便于确定其实在线状态
  int64_t id = 0;
  std::string table = "";
  // das 数据查询时间单位为 ms
  int64_t start = start_time_ * 1000L;
  int64_t end = end_time_ * 1000L;
  // 根据 type 设置要查询的条件
  switch (type) {
    case AdEnum::ACCOUNT:
      id = index_id_data_.account_id;
      table = "ad_dsp_account";
      break;
    case AdEnum::CAMPAIGN:
      id = index_id_data_.campaign_id;
      table = "ad_dsp_campaign";
      break;
    case AdEnum::UNIT:
      id = index_id_data_.unit_id;
      table = "ad_dsp_unit";
      break;
    case AdEnum::CREATIVE:
      id = index_id_data_.creative_id;
      table = "ad_dsp_creative";
      break;
    default:
      break;
  }
  if (id == 0 || table.empty()) return;
  // 查询增量数据
  das_inc_data_[type] = DasIndexDataGetter::GetIncDataFromHttpApi(id, table, start, end);
  // 如果是本层级，需查询全量数据
  if (id == index_id_data_.id) {
    das_base_data_ = DasIndexDataGetter::GetBaseDataFromHttpApi(id, table, start, end);
  }
  ks::infra::PerfUtil::IntervalLogStash(base::GetTimestamp() / 1000 - cur_ts,
                                        "ad.ad_diag_api",
                                        "query_index_data",
                                        "time_cost",
                                        "das_data_http", AdEnum::AdInstanceType_Name(type));
  // 压缩结果数据
  CompactDasData(type);
  // 解析增量结果
  std::lock_guard<std::mutex> lock(mtx_);
  for (auto& inc : *(das_inc_data_[type])) {
    IndexStatus& status = das_indexes_status_.emplace_back();
    status.timestamp = inc.process_time() / 1000000;
    status.side = "平台";
    status.status = inc.status_flag() ? "上线" : "下线";
    status.filter = "平台操作";
    status.filter_detail = inc.business_trace_info();
    status.suggestion = inc.status_flag() ? "无" : "排查广告主操作";
    status.table = AdEnum::AdInstanceType_Name(type);
    status.id = inc.hash_key();
  }
  ks::infra::PerfUtil::IntervalLogStash(base::GetTimestamp() / 1000 - cur_ts,
                                        "ad.ad_diag_api",
                                        "query_index_data",
                                        "time_cost",
                                        "das_data_all", AdEnum::AdInstanceType_Name(type));
}

// 对 das 增量产出数据做压缩
// 1. 连续相连两条数据状态相同则删除后面的一条
// 2. 不需要处理的增量 -> 不改变物料状态的增量
void IndexDataAnalyzer::CompactDasData(AdEnum::AdInstanceType type) {
  int64_t cur_ts = base::GetTimestamp() / 1000;
  auto& inc = das_inc_data_[type];
  if (inc->empty()) return;
  bool online = false;
  std::shared_ptr<std::vector<AdInstance>> after_compact = std::make_shared<std::vector<AdInstance>>();
  // 遍历数据，检查有效性
  bool pre_valid = false;
  bool first = true;
  for (auto i = 0; i < inc->size(); i++) {
    auto& instance = inc->at(i);
    bool valid = true;
    std::string reason = "未知原因";
    switch (type) {
      case AdEnum::CREATIVE: {
          const Creative& creative = instance.GetExtension(Creative::creative_old);
          valid = IsTableValid(creative, &reason);
          instance.set_hash_key(creative.id());
        }
        break;
      case AdEnum::UNIT: {
          const Unit& unit = instance.GetExtension(Unit::unit_old);
          valid = IsTableValid(unit, &reason);
          instance.set_hash_key(unit.id());
        }
        break;
      case AdEnum::CAMPAIGN: {
          const Campaign& campaign = instance.GetExtension(Campaign::campaign_old);
          valid = IsTableValid(campaign, &reason);
          instance.set_hash_key(campaign.id());
        }
        break;
      case AdEnum::ACCOUNT: {
          const Account& account = instance.GetExtension(Account::account_old);
          valid = IsTableValid(account, &reason);
          instance.set_hash_key(account.id());
        }
        break;
      default:
        break;
    }
    // 有效状态时，不需要展示原因
    if (valid) reason = "无";
    // 检查是否是影响状态变化的增量
    if (!CheckModifiedFieldList(type, instance.business_trace_info())) continue;
    // 其他情况下，第一条消息保留, 非第一条消息连续状态相同的情况下，仅保留最老的一条
    if (!first && valid == pre_valid) continue;
    pre_valid = valid;
    first = false;
    // 临时使用该字段存储上下线状态
    instance.set_status_flag(valid);
    // 临时使用该字段存储过滤原因
    instance.set_business_trace_info(reason);
    after_compact->emplace_back(instance);
  }
  das_inc_data_[type] = after_compact;
  ks::infra::PerfUtil::IntervalLogStash(base::GetTimestamp() / 1000 - cur_ts,
                                        "ad.ad_diag_api",
                                        "query_index_data",
                                        "time_cost",
                                        "compact_data", AdEnum::AdInstanceType_Name(type));
}

// 对增量消息的 modified list 检查，如果不是关注的字段，则不需要处理该增量
bool IndexDataAnalyzer::CheckModifiedFieldList(AdEnum::AdInstanceType type,
                                               const std::string& modified_list) {
    if (modified_list.empty()) return false;
    size_t s = modified_list.find("[");
    size_t e = modified_list.find("]");
    if (s == std::string::npos) return false;
    if (e == std::string::npos) return false;
    if (e <= s) return false;
    std::string real_modified_list = modified_list.substr(s + 1, e - s - 1);
    if (real_modified_list.empty()) return false;
    std::vector<std::string> split_fields = absl::StrSplit(real_modified_list, ',');
    if (split_fields.empty()) return false;
    static const std::unordered_set<std::string> kCreativeCheckFields = {
      "put_status",
      "review_status",
      "community_review_status"
    };
    static const std::unordered_set<std::string> kUnitCheckFields = {
      "put_status",
      "review_status",
      "community_review_status",
      "external_put_status",
      "zombie_flag"
    };
    static const std::unordered_set<std::string> kCampaignCheckFields = {
      "put_status"
    };
    static const std::unordered_set<std::string> kAccountCheckFields = {
      "put_status",
      "review_status",
      "frozen_status",
      "user_id"
    };
    const std::unordered_set<std::string>* check_fields = nullptr;
    switch (type) {
      case AdEnum::CREATIVE:
        check_fields = &kCreativeCheckFields;
        break;
      case AdEnum::UNIT:
        check_fields = &kUnitCheckFields;
        break;
      case AdEnum::CAMPAIGN:
        check_fields = &kCampaignCheckFields;
        break;
      case AdEnum::ACCOUNT:
        check_fields = &kAccountCheckFields;
        break;
      default:
        return false;
    }
    if (check_fields == nullptr) return false;
    for (auto& field : split_fields) {
      auto f = trim(field);
      if (f.empty()) continue;
      if (check_fields->count(f)) return true;
    }
    return false;
}

// 查询创意打分数据
void IndexDataAnalyzer::QueryScoreData(int biz) {
  int64_t cur_ts = base::GetTimestamp() / 1000;
  if (biz == 1) {
    internal_score_ = CreativeScoreDataGetter::GetInternalFromHttpApi(index_id_data_.id, index_id_data_.table, start_time_,  end_time_);// NOLINT
    ks::infra::PerfUtil::IntervalLogStash(base::GetTimestamp() / 1000 - cur_ts,
                                          "ad.ad_diag_api",
                                          "query_index_data",
                                          "time_cost",
                                          "score_data_http", std::to_string(biz));
    AnalyzeScoreData(internal_score_, &internal_score_indexes_status_);
  } else if (biz == 4) {
    external_score_ = CreativeScoreDataGetter::GetExternalFromHttpApi(index_id_data_.id, index_id_data_.table, start_time_,  end_time_);    // NOLINT
    ks::infra::PerfUtil::IntervalLogStash(base::GetTimestamp() / 1000 - cur_ts,
                                          "ad.ad_diag_api",
                                          "query_index_data",
                                          "time_cost",
                                          "score_data_http", std::to_string(biz));
    AnalyzeScoreData(external_score_, &external_score_indexes_status_);
  } else if (biz == 5) {
    search_score_ = CreativeScoreDataGetter::GetSearchFromHttpApi(index_id_data_.id, index_id_data_.table, start_time_,  end_time_);    // NOLINT
    ks::infra::PerfUtil::IntervalLogStash(base::GetTimestamp() / 1000 - cur_ts,
                                          "ad.ad_diag_api",
                                          "query_index_data",
                                          "time_cost",
                                          "score_data_http", std::to_string(biz));
    AnalyzeScoreData(search_score_, &search_score_indexes_status_);
  } else if (biz == 2) {
    universe_score_ = CreativeScoreDataGetter::GetUniverseFromHttpApi(index_id_data_.id, index_id_data_.table, start_time_,  end_time_);    // NOLINT
    ks::infra::PerfUtil::IntervalLogStash(base::GetTimestamp() / 1000 - cur_ts,
                                          "ad.ad_diag_api",
                                          "query_index_data",
                                          "time_cost",
                                          "score_data_http", std::to_string(biz));
    AnalyzeScoreData(universe_score_, &universe_score_indexes_status_);
  }
  ks::infra::PerfUtil::IntervalLogStash(base::GetTimestamp() / 1000 - cur_ts,
                                        "ad.ad_diag_api",
                                        "query_index_data",
                                        "time_cost",
                                        "score_data_all", std::to_string(biz));
}

// 分析创意打分数据
void IndexDataAnalyzer::AnalyzeScoreData(const CreativeScoreVecPtr& score,
                                         std::vector<IndexStatus>* index_status) {
  const auto& reason_map = AdKconfUtil::indexFilterReason();
  const auto& suggestion_map = AdKconfUtil::indexFilterSuggestion();
  index_status->clear();
  for (auto& score : *score) {
    IndexStatus& status = index_status->emplace_back();
    status.timestamp = score.timestamp();
    status.side = "创意优选";
    status.status = score.new_score() >= 60 ? "上线" : "下线";
    // 增加命中实验标记
    if (score.new_score() >= 60) {
      std::vector<int> exp_tags;
      for (int i = 1; i <= 7; i++) {
        int tmp = 1 << (i + 23);
        if ((score.new_score() & tmp) == tmp) exp_tags.emplace_back(i);
      }
      std::string exp_info = "(exp:" + absl::StrJoin(exp_tags, "|") + ")";
      status.status += exp_info;
    }
    status.creative_id = score.creative_id();
    status.unit_id = score.unit_id();
    status.campaign_id = score.campaign_id();
    status.account_id = score.account_id();
    status.photo_id = score.photo_id();
    status.creative_first_index_time = index_id_data_.creative_index_time > 0 ?
                                       index_id_data_.creative_index_time * 1000 : score.first_index_time();
    status.table = "CREATIVE";
    status.id = score.creative_id();
    // 优选成功的
    if (score.new_score() >= 60) {
      status.filter = "未过滤";
      status.filter_detail = "无";
      status.suggestion = "无";
      continue;
    }
    // 优选失败的
    // 1. 排查是否进入过优选池
    if (score.opt_info_size() > 0) {
      bool miss_base = true;
      for (auto& info : score.opt_info()) {
        if (info.exp_tag() != 1) continue;
        miss_base = false;
        if (info.is_block()) {
          // 被截断
          status.filter = "被截断";
          status.filter_detail = FindWithDefault(*reason_map, 999, "创意优选物料量超 quota 上限");
          status.suggestion = FindWithDefault(*suggestion_map, 999, "联系服务owner[@fengzezhao]");
        } else {
          // 被裁剪
          status.filter = "被裁剪";
          status.filter_detail = FindWithDefault(*reason_map, info.crop_type(), "创意优选命中裁剪策略") + absl::StrCat("[code:", info.crop_type(), "]");    // NOLINT
          status.suggestion = FindWithDefault(*suggestion_map, info.crop_type(), "联系服务owner[@fengzezhao]"); // NOLINT
        }
        break;
      }
      if (miss_base) {
        status.filter = "优选未胜出";
        status.filter_detail = "创意前期投放效果差,基于统计后验打分排序靠后";
        status.suggestion = "建议通过天枢工具查看创意新建保护期的掉量数据，并做相应优化；如提高出价、更换优质素材等";   // NOLINT
      }
      continue;
    }
    // 2. 排查未被选中的
    if (score.msg_type() == 1) {
      // 全量的检查级联和僵尸状态
      if (score.heper_info().is_cascade_failed()) {
        status.filter = "级联失败";
        status.filter_detail = "creative、unit、campaign、account层级存在审核未通过或未打开情况";
        status.suggestion = "检查物料审核状态或者平台操作打开物料";
      } else if (score.heper_info().is_zombie()) {
        status.filter = "僵尸创意";
        status.filter_detail = "创意不活跃,最近30天内无消耗";
        status.suggestion = "创建新的物料进行投放";
      } else {
        status.filter = "优选未胜出";
        status.filter_detail = "创意前期投放效果差,基于统计后验打分排序靠后";
        status.suggestion = "建议通过天枢工具查看创意新建保护期的掉量数据，并做相应优化；如提高出价、更换优质素材等";   // NOLINT
      }
    } else if (score.msg_type() == 2) {
        int inc_drop = score.inc_drop_code() & 0x0000ffff;
        int inc_code = score.inc_drop_code() >> 16;
        switch (inc_drop) {
          case 1:
            status.filter = "非本业务部署物料";
            status.filter_detail = "非本业务部署物料";
            status.suggestion = "无";
            break;
          case 2:
            status.filter = "僵尸创意";
            status.filter_detail = "创意不活跃,最近30天内无消耗";
            status.suggestion = "创建新的物料进行投放";
            break;
          case 3:
            status.filter = "被裁剪";
            status.filter_detail = FindWithDefault(*reason_map, inc_code, "创意优选命中裁剪策略");
            status.suggestion = FindWithDefault(*suggestion_map, inc_code, "联系服务owner[@fengzezhao]");
            break;
          default:
            status.filter = "增量控量";
            status.filter_detail = "为避免突发打增量影响在线服务稳定性,控制增量下发数量";
            status.suggestion = "1.等待全量优选进行;2.紧急情况联系服务owner[@fengzezhao]";
            break;
        }
    } else {
      status.filter = "异常未知原因";
      status.filter_detail = "异常未知原因";
      status.suggestion = "异常未知原因,请联系服务owner[@fengzezhao]";
    }
  }
}

void IndexDataAnalyzer::QueryIndexTraceData(AdEnum::AdInstanceType type) {
  int64_t cur_ts = base::GetTimestamp() / 1000;
  int64_t id = 0;
  std::string table = "";
  // das 数据查询时间单位为 ms
  int64_t start = start_time_ * 1000L;
  int64_t end = end_time_ * 1000L;
  // 根据 type 设置要查询的条件
  switch (type) {
    case AdEnum::ACCOUNT:
      id = index_id_data_.account_id;
      table = "ad_dsp_account";
      break;
    case AdEnum::CAMPAIGN:
      id = index_id_data_.campaign_id;
      table = "ad_dsp_campaign";
      break;
    case AdEnum::UNIT:
      id = index_id_data_.unit_id;
      table = "ad_dsp_unit";
      break;
    case AdEnum::CREATIVE:
      id = index_id_data_.creative_id;
      table = "ad_dsp_creative";
      break;
    default:
      break;
  }
  if (id == 0 || table.empty()) return;
  auto trace_log = IndexBuildTraceLogGetter::GetIndexBuildTraceFromHttpApi(id, table, start, end);
  ks::infra::PerfUtil::IntervalLogStash(base::GetTimestamp() / 1000 - cur_ts,
                                        "ad.ad_diag_api",
                                        "query_index_data",
                                        "time_cost",
                                        "trace_data_http");
  // 解析构建结果
  std::lock_guard<std::mutex> lock(mtx_);
  for (const auto& log : *trace_log) {
    const auto& build_key = log.build_key();
    const auto& trace_type = log.trace_type();
    int status_code = log.status_code();
    // 忽视跳过某些原因
    if (AdKconfUtil::indexFilterReasonSkipCode()->count(status_code)) continue;
    IndexStatus status;
    status.timestamp = log.timestamp();
    status.account_id = log.account_id();
    status.campaign_id = log.campaign_id();
    status.unit_id = log.unit_id();
    status.id = log.id();
    std::string default_reason = absl::StrCat("未知原因 code:", status_code , "，请联系[@fengzezhao]");
    status.filter_detail = FindWithDefault(*AdKconfUtil::indexFilterReason(), status_code, default_reason) + absl::StrCat("[code:", status_code, "]");    // NOLINT
    status.suggestion = FindWithDefault(*AdKconfUtil::indexFilterSuggestion(), status_code, default_reason);
    status.table = AdEnum::AdInstanceType_Name(log.table());
    //根据 trace type 填充 filter
    switch (trace_type) {
      case IndexTraceType::BUILD_DROP_MESSAGE:
        status.filter = "构建无效，不下发";
        break;
      case IndexTraceType::BUILD_OFFLINE_MESSAGE:
        status.filter = "构建下线";
        break;
      case IndexTraceType::TARGET_INDEX_FILTER_MESSAGE:
        status.filter = "召回构建过滤";
        break;
      default:
        status.filter = "无";
        break;
    }
    if (status_code == 0) {
      status.status = "上线";
      status.filter = "无";
      status.filter_detail = "无";
      status.suggestion = "无";
    } else {
      status.status = "下线";
    }
    // 根据 build key 填充 side
#define PUSH_STATUS(trace_vector, key)                                    \
  if (trace_vector[#key] == nullptr) {                                    \
    trace_vector[#key] = std::make_shared<std::vector<IndexStatus>>();    \
  }                                                                       \
  trace_vector[#key]->emplace_back(status);

    switch (build_key) {
      // 外循环短视频召回
      case IndexTraceKey::INDEX_PRODUCER_OUTER_PHOTO:
        status.side = "召回构建";
        PUSH_STATUS(target_trace_status_, external);
        break;
      // 直播召回
      case IndexTraceKey::INDEX_PRODUCER_LIVE:
        status.side = "召回构建";
        PUSH_STATUS(target_trace_status_, live);
        break;
      // inner photo 召回
      case IndexTraceKey::INDEX_PRODUCER_INNER_PHOTO:
        status.side = "召回构建";
        PUSH_STATUS(target_trace_status_, inner);
        break;
      // 索引构建，row 最终链路，给 ad-target-server 产出全量、增量
      case IndexTraceKey::TARGET_EXTERNAL_SCHEMAFREE_PROD:
      case IndexTraceKey::TARGET_EXTERNAL_SCHEMAFREE_NEW_PROD:
        status.side = "索引构建";
        PUSH_STATUS(index_trace_status_, external);
        break;
      // 索引构建，row 最终链路，给 ad-target-server-merchant-live 产出全量、增量
      case IndexTraceKey::TARGET_AMD_LIVE_SCHEMAFREE_PROD:
      case IndexTraceKey::TARGET_AMD_LIVE_SCHEMAFREE_NEW_PROD:
        status.side = "索引构建";
        PUSH_STATUS(index_trace_status_, live);
        break;
      // 索引构建，row 最终链路，给 ad-target-server-inner-photo 产出全量、增量
      case IndexTraceKey::TARGET_INNER_PHOTO_SCHEMAFREE_PROD:
      case IndexTraceKey::TARGET_INNER_PHOTO_SCHEMAFREE_NEW_PROD:
        status.side = "索引构建";
        PUSH_STATUS(index_trace_status_, inner);
        break;
      // 索引构建，row 最终链路，给 ad-target-search 产出全量、增量
      case IndexTraceKey::TARGET_SEARCH_SCHEMAFREE_PROD:
        status.side = "索引构建";
        PUSH_STATUS(index_trace_status_, search);
        break;
      // 索引构建，row 最终链路，给 ad-target-server-bidword 产出全量、增量
      case IndexTraceKey::TARGET_BIDWORD_KTABLE_NEW_PROD:
      case IndexTraceKey::TARGET_BIDWORD_KTABLE_PROD:
      case IndexTraceKey::TARGET_BIDWORD_INC_KTABLE_PROD:
      case IndexTraceKey::TARGET_BIDWORD_SCHEMAFREE_PROD:
        status.side = "索引构建";
        PUSH_STATUS(index_trace_status_, bidword);
        break;
      // 索引构建，row 最终链路，给 ad-target-universe 产出全量、增量
      case IndexTraceKey::TARGET_UNIVERSE_SCHEMAFREE_PROD:
        status.side = "索引构建";
        PUSH_STATUS(index_trace_status_, universe);
        break;
      default:
        break;
    }
#undef PUSH_STATUS
  }
  ks::infra::PerfUtil::IntervalLogStash(base::GetTimestamp() / 1000 - cur_ts,
                                        "ad.ad_diag_api",
                                        "query_index_data",
                                        "time_cost",
                                        "trace_data_all");
}

void IndexDataAnalyzer::QueryPsItemUpdateTraceData() {
  if (index_id_data_.creative_id <= 0) return;
  int64_t cur_ts = base::GetTimestamp() / 1000;
  ps_item_update_data_ = PsItemInfoGetter::GetItemUpdateTraceInfo(index_id_data_.creative_id, index_id_data_.table, start_time_ * 1000, end_time_ * 1000);  // NOLINT
  ks::infra::PerfUtil::IntervalLogStash(base::GetTimestamp() / 1000 - cur_ts,
                                        "ad.ad_diag_api",
                                        "query_index_data",
                                        "time_cost",
                                        "ps_item_data");
}

}  // namespace ad_diag_api
}  // namespace ks
