
#pragma once

#include <string>

namespace ks {
namespace ad_diag_api {

static std::string trim(const std::string &s) {
    auto pos_l = s.find_first_not_of(' ');
    if (pos_l == std::string::npos) return s;
    auto pos_r = s.find_last_not_of(' ');
    if (pos_r == std::string::npos) return s.substr(pos_l);
    return s.substr(pos_l, pos_r - pos_l + 1);
}

}  // namespace ad_diag_api
}  // namespace ks
