#pragma once

#include <string>
#include <memory>
#include <mutex>
#include <unordered_map>
#include <vector>

#include "ks/util/json.h"
#include "base/common/closure.h"
#include "base/common/logging.h"
#include "base/common/map_util-inl.h"
#include "base/thread/thread_pool.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_diag_api/utils/http_client.h"
#include "teams/ad/ad_diag_api/utils/index_data/types.h"
#include "teams/ad/ad_diag_api/utils/index_data/index_status.h"
#include "teams/ad/ad_diag_api/utils/index_data/table_valid.h"
#include "teams/ad/ad_diag_api/utils/index_data/instance_convert.h"
#include "teams/ad/ad_diag_api/utils/index_data/first_index_time_getter.h"
#include "teams/ad/ad_diag_api/utils/index_data/das_data_getter.h"
#include "teams/ad/ad_diag_api/utils/index_data/ps_item_info_getter.h"
#include "teams/ad/ad_diag_api/utils/index_data/creative_score_data_getter.h"
#include "teams/ad/ad_diag_api/utils/index_data/index_build_trace_log_getter.h"

namespace ks {
namespace ad_diag_api {


class IndexDataAnalyzer {
 public:
  IndexDataAnalyzer(const IndexIdData& id_data, int64_t start, int64_t end)
        : index_id_data_(id_data), start_time_(start), end_time_(end) {}

  void Analyze();
  void FillIndexStatusIntoJson(const std::string& key, base::Json *json);
  void FillDasDataIntoJson(const std::string& key, base::Json *json);
  void FillScoreDataIntoJson(const std::string& key, base::Json *json);
  void FillBuildDataIntoJson(const std::string& key, base::Json *json);
  void FillTargetDataIntoJson(const std::string& key, base::Json *json);
  void FillPsItemUpdateInfoIntoJson(const std::string& key, base::Json *json);


 private:
  void QueryDasData(AdEnum::AdInstanceType type);
  // 对 das 增量产出数据做压缩
  // 1. 连续相连两条数据状态相同则删除后面的一条
  void CompactDasData(AdEnum::AdInstanceType type);
  bool CheckModifiedFieldList(AdEnum::AdInstanceType type, const std::string& modified_list);
  void AnalyzeScoreData(const CreativeScoreVecPtr& score, std::vector<IndexStatus>* status);
  void QueryScoreData(int biz);
  void QueryIndexTraceData(AdEnum::AdInstanceType type);
  void QueryPsItemUpdateTraceData();

 private:
  IndexIdData index_id_data_;
  int64_t start_time_;
  int64_t end_time_;
  mutable std::mutex mtx_;
  std::vector<IndexStatus> das_indexes_status_;
  std::vector<IndexStatus> internal_score_indexes_status_;
  std::vector<IndexStatus> external_score_indexes_status_;
  std::vector<IndexStatus> search_score_indexes_status_;
  std::vector<IndexStatus> universe_score_indexes_status_;
  // 四个层级 das 数据
  std::unordered_map<AdEnum::AdInstanceType, std::shared_ptr<std::vector<AdInstance>>> das_inc_data_;
  std::shared_ptr<std::vector<AdInstance>> das_base_data_;
  // 内外循环 打分 数据
  CreativeScoreVecPtr internal_score_;
  CreativeScoreVecPtr external_score_;
  CreativeScoreVecPtr search_score_;
  CreativeScoreVecPtr universe_score_;
  // 索引构建 trace 日志数据
  std::unordered_map<std::string, std::shared_ptr<std::vector<IndexStatus>>> index_trace_status_;
  // 召回构建 trace 日志数据
  std::unordered_map<std::string, std::shared_ptr<std::vector<IndexStatus>>> target_trace_status_;
  // 模型物料流日志
  ItemUpdateInfoPtr ps_item_update_data_;
};

}  // namespace ad_diag_api
}  // namespace ks
