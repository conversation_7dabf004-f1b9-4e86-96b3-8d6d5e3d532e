#pragma once

#include <algorithm>
#include <set>
#include <string>
#include <memory>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "ks/util/json.h"
#include "base/common/logging.h"
#include "absl/strings/numbers.h"
#include "absl/strings/str_cat.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_diag_api/utils/kconf/kconf.h"
#include "teams/ad/ad_diag_api/utils/http_client.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_diag_api/utils/index_data/types.h"
#include "teams/ad/ad_diag_api/utils/index_data/instance_convert.h"
#include "teams/ad/ad_diag_api/utils/index_data/index_status.h"
#include "teams/ad/ad_diag_api/utils/index_data/creative_score_data_getter.h"

namespace ks {
namespace ad_diag_api {

struct ExternalIndexTraceCommonData {
  int64_t creative_id{0};
  int64_t unit_id{0};
  int64_t campaign_id{0};
  int64_t account_id{0};
  int64_t photo_id{0};
  int64_t create_time{0};
  int64_t first_audit_passtime{0};
  int64_t first_index_time{0};
  int64_t cpa_bid{0};
  int native_strict_status{0};
  int native_weak_status{0};
  int photo_dup_status{0};
  int creative_score{0};
  int zombie_status{0};
  std::string cascade_status{""};
  std::string live_creative_type{""};
};

struct ExternalOptInfoTraceData {
  int64_t timestamp{0};
  int32_t status{0};
  std::string detail{""};
  std::string reason{""};
  std::string suggestion{""};
};

class ExternalIndexDataGetter {
 public:
  const std::unordered_set<int> kReviewStatus = { 2, 5, 6 };
  const std::vector<std::string> kValidReason = {
    "已进入通用优选池，顺利通过索引",
    "您已受到直播保护，顺利通过索引",
    "您已受到新创意保护，顺利通过索引",
    "您已受到新创意、直播保护，顺利通过索引",
    "您已受到原创保护，顺利通过索引",
    "您已受到原创、直播保护，顺利通过索引",
    "您已受到原创、新创意保护，顺利通过索引",
    "您已受到原创、新创意、直播保护，顺利通过索引",
    "您已受到优质保护，顺利通过索引",
    "您已受到优质、直播保护，顺利通过索引",
    "您已受到优质、新创意保护，顺利通过索引",
    "您已受到优质、新创意、直播保护，顺利通过索引",
    "您已受到优质、原创保护，顺利通过索引",
    "您已受到优质、原创、直播保护，顺利通过索引",
    "您已受到优质、原创、新创意、保护，顺利通过索引",
    "您已受到优质、原创、新创意、直播保护，顺利通过索引"
  };
  const std::vector<std::string> kValidSuggestion = {
    "无",
    "直播素材索引阶段会受到保护，可以继续创作高质量直播素材哦",
    "新的原创创意会获得时效性保护，建议继续使用原创素材",
    "新的原创创意会获得时效性保护，建议继续使用原创素材",
    "建议继续使用原创素材，增加跑量机会",
    "建议继续使用原创素材，增加跑量机会",
    "建议继续使用原创素材，增加跑量机会",
    "建议继续使用原创素材，增加跑量机会",
    "建议继续使用优质素材，增加跑量机会",
    "建议继续使用优质素材，增加跑量机会",
    "建议继续使用优质素材，增加跑量机会",
    "建议继续使用优质素材，增加跑量机会",
    "建议继续使用优质、原创素材，增加跑量机会",
    "建议使用优质、原创素材，增加跑量机会",
    "建议继续使用优质、原创素材，增加跑量机会",
    "建议继续使用优质、原创素材，增加跑量机会"
  };

 public:
  std::string GetData(int64_t id, int64_t start, int64_t end) {
    common_data.creative_id = id;
    win_count = 0;
    valid = true;
    status.clear();
    GetCommonData();
    GetOptInfo(id, start, end);
    return GetResult();
  }

 private:
  std::string GetFromKtable(const std::string& table, int64_t id) {
    if (id <= 0) return "";
    std::unordered_map<std::string, std::string> params;
    params.emplace("table", "wt_" + table);
    params.emplace("id", std::to_string(id));
    params.emplace("stage", "prod");
    auto code = client.Get(kApiUrl, params);
    return code != CURLE_OK ? "" : client.get_result_data();
  }

  void GetCommonData() {
    // 查询 ktable
    // 1. query creative
    parseCreative();
    parseWtCreative();
    // 2. query unit
    parseUnit();
    // 3. query campaign
    parseCampaign();
    // 4 query account;
    parseAccount();
    // 5. query photo
    parsePhoto();
    parseWTPhoto();
    // 更新级联状态
    if (common_data.cascade_status.empty()) {
      common_data.cascade_status = "在投";
    }
  }

  void GetOptInfo(int64_t id, int64_t start, int64_t end) {
    const auto& reason_map = AdKconfUtil::indexFilterReason();
    const auto& suggestion_map = AdKconfUtil::indexFilterSuggestion();
    auto res = CreativeScoreDataGetter::GetExternalFromHttpApi(id, "ad_dsp_creative", start, end);
    if (res->empty()) return;
    for (auto& info : *res) {
      // 统计全量胜出次数
      if (info.new_score() >= 60 && info.msg_type() == 1) {
        win_count++;
      }
      status.emplace_back();
      auto& st = status.back();
      st.timestamp = info.timestamp();
      st.status = info.new_score() >= 60 ? 1 : 0;

      // 优选成功的
      if (info.new_score() >= 60) {
        int symbol = 0;
        symbol = symbol | ((common_data.native_strict_status == 4) ? 8 : 0);
        symbol = symbol | ((common_data.photo_dup_status != 1) ? 4 : 0);
        symbol = symbol | (info.heper_info().is_fresh() ? 2 : 0);
        symbol = symbol | ((common_data.live_creative_type == "LIVE_STREAM_CREATIVE_TYPE" ||
                          common_data.live_creative_type == "DIRECT_LIVE_STREAM_FOR_NON_MERCHANT") ? 1 : 0);
        st.reason = kValidReason.at(symbol);
        st.suggestion = kValidSuggestion.at(symbol);
        if (info.msg_type() == 1) {
          st.detail = "全量流通过";
        } else if (info.msg_type() == 5) {
          st.detail = "提价流通过";
        } else {
          st.detail = "增量流通过";
        }
        continue;
      }
      // 优选失败的
      // 1. 排查是否进入过优选池
      if (info.opt_info_size() > 0) {
        if (info.msg_type() == 1) {
          st.detail = "全量流被裁";
        } else {
          st.detail = "增量流被裁";
        }
        for (auto& info : info.opt_info()) {
          if (info.is_block()) {
            // 被截断
            st.reason = "创意优选物料量超 quota 上限";
            st.suggestion = "联系服务owner[@fengzezhao]";
          } else {
            // 被裁剪
            st.reason = FindWithDefault(*reason_map, info.crop_type(), "创意优选命中裁剪策略") + absl::StrCat("[code:", info.crop_type(), "]");    // NOLINT
            st.suggestion = FindWithDefault(*suggestion_map, info.crop_type(), "联系服务owner[@fengzezhao]");
          }
          // 找到 base 的结果，直接结束
          if (info.exp_tag() == 1) break;
        }
        continue;
      }
      st.detail = "未被优选";
      // 2. 排查未被选中的
      // 从未优选成功过
      if (common_data.first_index_time <= 0) {
        st.reason = "截止目前,创意未被优选命中";
        st.suggestion = "索引对新原创创意、优质原生有保护；如为重复素材，请优化素材质量或提高出价";
      } else {
        st.reason = "创意前期投放效果差,基于统计后验打分排序靠后";
        st.suggestion = "优化素材内容或提高出价";
      }
    }
  }

  void parseCreative() {
    int64_t id = common_data.creative_id;
    if (id == 0) {
      valid = false;
      return;
    }
    auto res = GetFromKtable("ad_dsp_creative", id);
    if (res.empty()) {
      valid = false;
      return;
    }
    base::Json res_json(base::StringToJson(res));
    base::Json* json_data = res_json.Get("data");
    if (json_data == nullptr) {
      valid = false;
      return;
    }
    if (!json_data->IsObject()) {
      valid = false;
      return;
    }
    int64_t id_value = 0;
    common_data.creative_id = id;
    common_data.unit_id = json_data->GetInt("unit_id", 0);
    common_data.campaign_id = json_data->GetInt("campaign_id", 0);
    common_data.account_id = json_data->GetInt("account_id", 0);
    common_data.photo_id = json_data->GetInt("photo_id", 0);
    common_data.creative_score = json_data->GetInt("creative_score_1", 0);
    common_data.zombie_status = json_data->GetInt("zombie_status", 0);
    common_data.live_creative_type = json_data->GetString("live_creative_type", "");
    // 时间相关
    int64_t create_time = 0;
    ignore_result(json_data->GetInt("create_time", &create_time));
    // first_audit_passtime 比较特殊，是 string，要二次转换成 int
    std::string first_audit_passtime_string = "";
    int64_t first_audit_passtime = 0;
    ignore_result(json_data->GetString("first_audit_passtime", &first_audit_passtime_string));
    ignore_result(absl::SimpleAtoi(first_audit_passtime_string, &first_audit_passtime));
    int64_t item_material2 = 0;
    ignore_result(json_data->GetInt("item_material2", &item_material2));
    int64_t create_source_type = 0;
    ignore_result(json_data->GetInt("create_source_type", &create_source_type));
    if (create_source_type == 4) {
      first_audit_passtime = first_audit_passtime == 0 ? create_time : first_audit_passtime;
    } else {
      first_audit_passtime = item_material2 == 0 ? create_time : item_material2;
    }
    common_data.create_time = create_time;
    common_data.first_audit_passtime = first_audit_passtime;
    // 级联状态
    do {
      // 如果已经级联失败，直接返回
      if (!common_data.cascade_status.empty()) break;
      // 投放审核状态
      int put_status = json_data->GetInt("put_status", 0);
      int review_status = json_data->GetInt("review_status", 0);
      int community_review_status = json_data->GetInt("community_review_status", 0);
      if (put_status != 1) {
        common_data.cascade_status = "创意未打开投放";
      } else if (!kReviewStatus.count(review_status) && !kReviewStatus.count(community_review_status)) {
        common_data.cascade_status = "创意审核未通过";
      }
    } while (0);
  }

  void parseWtCreative() {
    if (!valid) return;
    int64_t id = common_data.creative_id;
    if (id <= 0) return;
    auto res = GetFromKtable("creative", id);
    if (res.empty()) return;
    base::Json res_json(base::StringToJson(res));
    base::Json* json_data = res_json.Get("data");
    if (json_data == nullptr) return;
    if (!json_data->IsObject()) return;
    common_data.first_index_time = json_data->GetInt("first_index_time", 0) * 1000;
  }

  void parseUnit() {
    if (!valid) return;
    int64_t unit_id = common_data.unit_id;
    if (unit_id <= 0) return;
    auto res = GetFromKtable("ad_dsp_unit", unit_id);
    if (res.empty()) return;
    base::Json res_json(base::StringToJson(res));
    base::Json* json_data = res_json.Get("data");
    if (json_data == nullptr) return;
    if (!json_data->IsObject()) return;
    common_data.cpa_bid = json_data->GetInt("cpa_bid", 0);
    // 级联状态
    do {
      // 如果已经级联失败，直接返回
      if (!common_data.cascade_status.empty()) break;
      // 投放审核状态
      int put_status = json_data->GetInt("put_status", 0);
      int review_status = json_data->GetInt("review_status", 0);
      int community_review_status = json_data->GetInt("community_review_status", 0);
      int external_put_status = json_data->GetInt("external_put_status", 0);
      int shield_type = json_data->GetInt("shield_type", 0);
      if (put_status != 1) {
        common_data.cascade_status = "单元未打开投放";
      } else if (!kReviewStatus.count(review_status) && !kReviewStatus.count(community_review_status)) {
        common_data.cascade_status = "单元审核未通过";
      } else if (external_put_status == 2) {
        common_data.cascade_status = "单元外部关联状态无效";
      } else if (shield_type == 1) {
        common_data.cascade_status = "单元被屏蔽";
      }
    } while (0);
  }

  void parseCampaign() {
    if (!valid) return;
    int64_t id = common_data.campaign_id;
    if (id <= 0) return;
    auto res = GetFromKtable("ad_dsp_campaign", id);
    if (res.empty()) return;
    base::Json res_json(base::StringToJson(res));
    base::Json* json_data = res_json.Get("data");
    if (json_data == nullptr) return;
    if (!json_data->IsObject()) return;
    // 级联状态
    do {
      // 如果已经级联失败，直接返回
      if (!common_data.cascade_status.empty()) break;
      // 投放审核状态
      int put_status = json_data->GetInt("put_status", 0);
      if (put_status != 1) {
        common_data.cascade_status = "计划未打开投放";
      }
    } while (0);
  }

  void parseAccount() {
    if (!valid) return;
    int64_t account_id = common_data.account_id;
    if (account_id <= 0) return;
    auto res = GetFromKtable("ad_dsp_account", account_id);
    if (res.empty()) return;
    base::Json res_json(base::StringToJson(res));
    base::Json* json_data = res_json.Get("data");
    if (json_data == nullptr) return;
    if (!json_data->IsObject()) return;
    // 级联状态
    do {
      // 如果已经级联失败，直接返回
      if (!common_data.cascade_status.empty()) break;
      // 投放审核状态
      int put_status = json_data->GetInt("put_status", 0);
      int review_status = json_data->GetInt("review_status", 0);
      int frozen_status = json_data->GetInt("frozen_status", 0);
      int64_t user_id = json_data->GetInt("user_id", 0);
      if (put_status != 1) {
        common_data.cascade_status = "账户未打开投放";
      } else if (review_status != 2) {
        common_data.cascade_status = "账户未通过审核";
      } else if (frozen_status != 1) {
        common_data.cascade_status = "账户被冻结";
      } else if (user_id <= 0) {
        common_data.cascade_status = "账户未绑定快手id";
      }
    } while (0);
  }

  void parsePhoto() {
    if (!valid) return;
    int64_t photo_id = common_data.photo_id;
    if (photo_id <= 0) return;
    auto res = GetFromKtable("ad_dsp_photo", photo_id);
    if (res.empty()) return;
    base::Json res_json(base::StringToJson(res));
    base::Json* json_data = res_json.Get("data");
    if (json_data == nullptr) return;
    if (!json_data->IsObject()) return;
    common_data.native_strict_status = json_data->GetInt("native_strict_status", 0);
    common_data.native_weak_status = json_data->GetInt("native_weak_status", 0);
  }

  void parseWTPhoto() {
    if (!valid) return;
    int64_t photo_id = common_data.photo_id;
    if (photo_id <= 0) return;
    auto res = GetFromKtable("photo", photo_id);
    if (res.empty()) return;
    base::Json res_json(base::StringToJson(res));
    base::Json* json_data = res_json.Get("data");
    if (json_data == nullptr) return;
    if (!json_data->IsObject()) return;
    common_data.photo_dup_status = json_data->GetInt("photo_dup_status", 0);
  }

  void GetFirstIndexTime(const std::string& redis_key, const std::string json_key, int64_t* time) {
    if (!valid) return;
    auto* redis = ks::ad_base::KconfRedis::Instance().GetAdRedisClient("adLevelFirstEntrance");
    if (redis == nullptr) {
      LOG(ERROR) << "get redis client err, cluster adLevelFirstEntrance";
      return;
    }
    std::string value;
    auto ret = redis->Get(json_key, &value, 50);
    if (ret == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
      ignore_result(absl::SimpleAtoi(value, time));
    }
  }

  std::string GetResult() {
    if (!valid) return {};
    base::Json result(json_object());
    // 填充 common
    base::Json common(json_object());
    common.set("creative_id", common_data.creative_id);
    common.set("unit_id", common_data.unit_id);
    common.set("campaign_id", common_data.campaign_id);
    common.set("account_id", common_data.account_id);
    common.set("photo_id", common_data.photo_id);
    common.set("cpa_bid", common_data.cpa_bid * 1.0 / 1000);
    common.set("create_time", GetTimeString(common_data.create_time));
    common.set("first_audit_passtime", GetTimeString(common_data.first_audit_passtime));
    common.set("first_index_time", GetTimeString(common_data.first_index_time));
    common.set("cascade_status", common_data.cascade_status);
    common.set("score_status", common_data.creative_score >= 60 ? "优选通过" : "优选未通过");
    common.set("native_strict_status", common_data.native_strict_status == 4 ? "优质" : "非优质");
    common.set("native_weak_status", common_data.native_weak_status ? "原生" : "非原生");
    common.set("photo_dup_status", common_data.photo_dup_status == 1 ? "重复" : "非重复");
    common.set("zombie_status", common_data.zombie_status == 1 ? "僵尸" : "非僵尸");
    result.set("common_data", common);
    // 填充优选结果
    base::Json opt_info(json_object());
    opt_info.set("win_count", win_count);
    base::Json infos(json_array());
    for (auto& item : status) {
      base::Json info(json_object());
      base::Time ts = base::Time::FromDoubleT((double)item.timestamp / 1e3);
      std::string ts_str;
      ts.ToStringInSeconds(&ts_str);
      info.set("timestamp", ts_str);
      info.set("status", item.status == 1 ? "上线" : "下线");
      info.set("detail", item.detail);
      info.set("reason", item.reason);
      info.set("suggestion", item.suggestion);
      infos.append(info);
    }
    opt_info.set("infos", infos);
    result.set("opt_info", opt_info);
    return result.ToString();
  }

  std::string GetTimeString(int64_t timestamp) {
    if (timestamp <= 0) return "";
    base::Time ts = base::Time::FromDoubleT((double)timestamp / 1e3);
    std::string ts_str;
    ts.ToStringInSeconds(&ts_str);
    return ts_str;
  }

 private:
  ks::ad_diag_api::HttpClient client;
  ExternalIndexTraceCommonData common_data;
  std::vector<ExternalOptInfoTraceData> status;
  int win_count = 0;
  bool valid = true;
  static constexpr const char* kApiUrl = "https://ad-data-integration.test.gifshow.com/api/edata/debug/ktable/query";   // NOLINT
};

}  // namespace ad_diag_api
}  // namespace ks
