#pragma once

#include <string>
#include <memory>
#include <unordered_map>
#include <vector>

#include "ks/util/json.h"
#include "base/common/basic_types.h"
#include "base/common/logging.h"
#include "absl/strings/numbers.h"
#include "absl/strings/str_cat.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_diag_api/utils/index_data/index_status.h"

namespace ks {
namespace ad_diag_api {

class FirstIndexTimeGetter {
 public:
  static void GetFirstIndexTimeFromRedis(IndexIdData* id_data) {
    if (id_data == nullptr) return;
    auto* redis = ks::ad_base::KconfRedis::Instance().GetAdRedisClient("adLevelFirstEntrance");
    if (redis == nullptr) {
      LOG(ERROR) << "get redis client err, cluster adLevelFirstEntrance";
      return;
    }
    std::string value;
    if (id_data->creative_id > 0) {
      value.clear();
      std::string key = absl::StrCat("creative_", id_data->creative_id);
      auto ret = redis->Get(key, &value, 50);
      int64_t timestamp = 0;
      if (ret == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
        ignore_result(absl::SimpleAtoi(value, &id_data->creative_index_time));
      }
    }
    if (id_data->unit_id > 0) {
      value.clear();
      std::string key = absl::StrCat("unit_", id_data->unit_id);
      auto ret = redis->Get(key, &value, 50);
      int64_t timestamp = 0;
      if (ret == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
        ignore_result(absl::SimpleAtoi(value, &id_data->unit_index_time));
      }
    }
    if (id_data->campaign_id > 0) {
      value.clear();
      std::string key = absl::StrCat("campaign_", id_data->campaign_id);
      auto ret = redis->Get(key, &value, 50);
      int64_t timestamp = 0;
      if (ret == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
        ignore_result(absl::SimpleAtoi(value, &id_data->campaign_index_time));
      }
    }
    if (id_data->photo_id > 0) {
      value.clear();
      std::string key = absl::StrCat("photo_", id_data->photo_id);
      auto ret = redis->Get(key, &value, 50);
      int64_t timestamp = 0;
      if (ret == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
        ignore_result(absl::SimpleAtoi(value, &id_data->photo_index_time));
      }
    }
  }

  static int64_t GetFirstIndexTimeFromRedis(const std::string& key) {
    if (key.empty()) return 0;
    auto* redis = ks::ad_base::KconfRedis::Instance().GetAdRedisClient("adLevelFirstEntrance");
    std::string value;
    auto ret = redis->Get(key, &value, 50);
    int64_t timestamp = 0;
    if (ret == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
      ignore_result(absl::SimpleAtoi(value, &timestamp));
    }
    return timestamp;
  }
};

}  // namespace ad_diag_api
}  // namespace ks
