#pragma once

#include <string>
#include <memory>
#include <unordered_map>
#include <vector>

#include "ks/util/json.h"
#include "base/common/basic_types.h"
#include "base/common/logging.h"
#include "absl/strings/numbers.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_diag_api/utils/http_client.h"
#include "teams/ad/ad_diag_api/utils/index_data/types.h"
#include "teams/ad/ad_diag_api/utils/index_data/index_status.h"

namespace ks {
namespace ad_diag_api {

class TableIdGetter {
 public:
  static IndexIdData GetLevelIdsFromHttpApi(const std::string& table, int64_t id) {
    IndexIdData result(table, id);
    ks::ad_diag_api::HttpClient client;
    std::string target_url = "https://ad-data-integration.test.gifshow.com/api/edata/debug/ktable/query";
    std::unordered_map<std::string, std::string> params;
    params.emplace("table", "wt_" + table);
    params.emplace("id", std::to_string(id));
    params.emplace("stage", "prod");
    auto code = client.Get(target_url.c_str(), params);
    auto& resp = client.get_result_data();
    if (code != CURLE_OK) return result;
    base::Json resp_json(base::StringToJson(resp));
    base::Json* data_json = resp_json.Get("data");
    if (data_json == nullptr) return result;
    if (!data_json->IsObject()) return result;
    LOG(INFO) << "query data:" << resp;
    ignore_result(data_json->GetInt("creative_id", &result.creative_id));
    ignore_result(data_json->GetInt("unit_id", &result.unit_id));
    ignore_result(data_json->GetInt("campaign_id", &result.campaign_id));
    ignore_result(data_json->GetInt("account_id", &result.account_id));
    if (table == "ad_dsp_creative") {
      result.creative_id = id;
    } else if (table == "ad_dsp_unit") {
      result.unit_id = id;
    } else if (table == "ad_dsp_campaign") {
      result.campaign_id = id;
    } else if (table == "ad_dsp_account") {
      result.account_id = id;
    }
    result.valid = true;
    // 继续补充查询 photo id & live user id & package id
    if (result.unit_id == id) {    // 如果直接查询的就是 unit， 直接从上面取
      ignore_result(absl::SimpleAtoi(data_json->GetString("package_id", "0"), &result.package_id));
      ignore_result(absl::SimpleAtoi(data_json->GetString("live_user_id", "0"), &result.live_user_id));
    } else if (result.creative_id == id) {
      // 如果本身查询的就是 creative，直接从上面取
      ignore_result(data_json->GetInt("photo_id", &result.photo_id));
      if (result.photo_id == 0) {
        ignore_result(absl::SimpleAtoi(data_json->GetString("photo_id", "0"), &result.photo_id));
      }
      // live user id & package id 需要再查询一次 unit
      params["table"] = "wt_ad_dsp_unit";
      params["id"] = std::to_string(result.unit_id);
      code = client.Get(target_url.c_str(), params);
      if (code == CURLE_OK) {
        base::Json unit_resp_json(base::StringToJson(client.get_result_data()));
        auto* unit_data_json = unit_resp_json.Get("data");
        if (unit_data_json != nullptr) {
          ignore_result(absl::SimpleAtoi(data_json->GetString("package_id", "0"), &result.package_id));
          ignore_result(absl::SimpleAtoi(data_json->GetString("live_user_id", "0"), &result.live_user_id));
        }
      }
    }
    return result;
  }
};

}  // namespace ad_diag_api
}  // namespace ks
