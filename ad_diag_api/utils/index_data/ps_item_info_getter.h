#pragma once

#include <algorithm>
#include <string>
#include <memory>
#include <unordered_map>
#include <vector>

#include "ks/util/json.h"
#include "base/common/logging.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/ad_diag_api/utils/http_client.h"
#include "teams/ad/ad_diag_api/utils/index_data/types.h"
#include "teams/ad/ad_diag_api/utils/index_data/index_status.h"


namespace ks {
namespace ad_diag_api {

using ItemUpdateInfoPtr = std::shared_ptr<std::vector<PsItemUpdateInfo>>;

class PsItemInfoGetter {
 public:
  static ItemUpdateInfoPtr GetItemUpdateTraceInfo(int64_t id, const std::string table, int64_t start, int64_t end) {    // NOLINT
    ItemUpdateInfoPtr result = std::make_shared<std::vector<PsItemUpdateInfo>>();
    ks::ad_diag_api::HttpClient client;
    std::string target_url = "https://ad-data-integration.test.gifshow.com/api/edata/es/query/item_update_trace_info";    // NOLINT
    std::unordered_map<std::string, std::string> params;
    params.emplace("tableName", table);
    params.emplace("id", std::to_string(id));
    params.emplace("startTime", std::to_string(start));
    params.emplace("endTime", std::to_string(end));
    auto code = client.Get(target_url.c_str(), params);
    auto& resp = client.get_result_data();
    if (code != CURLE_OK) {
      LOG(INFO) << "fdebug: code not ok, code:" << code;
      return result;
    }
    base::Json resp_json(base::StringToJson(resp));
    base::Json* data_json = resp_json.Get("data");
    if (data_json == nullptr)  {
      LOG(INFO) << "fdebug: data_json not ok";
      return result;
    }
    base::Json* hits = data_json->Get("hits");
    if (hits == nullptr || !hits->IsArray()) {
      LOG(INFO) << "fdebug: hits not ok";
      return result;
    }
    for (auto* hit : hits->array()) {
      base::Json* src = hit->Get("_source");
      if (src == nullptr) continue;
      PsItemUpdateInfo info;
      info.id = src->GetInt("itemId", 0);
      std::string action = src->GetString("action", "");
      std::string reason = src->GetString("actionReason", "无");
      if (action == "ACTION_ADD") {
        info.status = "上线";
        reason = "无";
      } else if (action == "ACTION_DEL") {
        info.status = "下线";
      } else {
        info.status = "其他";
      }
      info.reason = reason;
      int64_t timestamp = src->GetInt("consumerTime", 0);
      base::Time ts = base::Time::FromDoubleT((double)timestamp / 1e3);
      ts.ToStringInSeconds(&info.timestamp);
      auto biz = src->GetString("serviceName", "");
      if (biz == "ExtrinsicCycleCreativeUpdateConsumer") {
        info.biz = "外循环";
        info.biz_code = 1;
      } else if (biz == "DspItemInfoUpdate2ConsumerV2") {
        info.biz = "内循环";
        info.biz_code = 2;
      } else {
        info.biz = "其他";
        info.biz_code = 0;
      }
      if (info.id > 0) {
        result->emplace_back(info);
      }
    }
    return result;
  }
};

}  // namespace ad_diag_api
}  // namespace ks
