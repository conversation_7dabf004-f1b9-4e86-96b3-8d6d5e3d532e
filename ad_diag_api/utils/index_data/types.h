
#pragma once

#include <string>
#include <unordered_map>
#include <vector>

#include "base/common/logging.h"
#include "base/common/map_util-inl.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"

namespace ks {
namespace ad_diag_api {

using AdEnum = kuaishou::ad::AdEnum;
using AdInstance = kuaishou::ad::AdInstance;
using Creative = kuaishou::ad::tables::Creative;
using Unit = kuaishou::ad::tables::Unit;
using Campaign = kuaishou::ad::tables::Campaign;
using Account = kuaishou::ad::tables::Account;

static const std::unordered_map<std::string, AdEnum::AdInstanceType> kTableName2EnumTypeMap = {
  {"ad_dsp_creative", AdEnum::CREATIVE},
  {"ad_dsp_unit", AdEnum::UNIT},
  {"ad_dsp_campaign", AdEnum::CAMPAIGN},
  {"ad_dsp_account", AdEnum::ACCOUNT},
};

}  // namespace ad_diag_api
}  // namespace ks
