#pragma once

#include <algorithm>
#include <string>
#include <memory>
#include <unordered_map>
#include <vector>

#include "ks/util/json.h"
#include "base/common/logging.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_diag_api/utils/http_client.h"
#include "teams/ad/ad_diag_api/utils/index_data/types.h"
#include "teams/ad/ad_diag_api/utils/index_data/index_status.h"
#include "teams/ad/ad_proto/kuaishou/ad/build_service_common.pb.h"


namespace ks {
namespace ad_diag_api {

using IndexTraceLog = com::kuaishou::ad::build::common::proto::IndexFilterReasonLog;
using IndexTraceLogVecPtr = std::shared_ptr<std::vector<IndexTraceLog>>;
using IndexTraceKey = com::kuaishou::ad::build::common::proto::IndexTraceKey;
using IndexTraceType = com::kuaishou::ad::build::common::proto::IndexTraceType;

class IndexBuildTraceLogGetter {
 public:
  static IndexTraceLogVecPtr GetIndexBuildTraceFromHttpApi(int64_t id, const std::string table,
                                                           int64_t start, int64_t end) {
    ks::ad_diag_api::HttpClient client;
    client.set_timeout(300);    // 修改超时时间
    std::string target_url = "https://ad-data-integration.test.gifshow.com/api/edata/clickhouse/query/index_trace";   // NOLINT
    std::unordered_map<std::string, std::string> params;
    params.emplace("tableName", table);
    params.emplace("id", std::to_string(id));
    params.emplace("startTime", std::to_string(start));
    params.emplace("endTime", std::to_string(end));
    auto code = client.Get(target_url.c_str(), params);
    auto& resp = client.get_result_data();
    std::shared_ptr<std::vector<IndexTraceLog>> result = std::make_shared<std::vector<IndexTraceLog>>();
    if (code != CURLE_OK) return result;
    base::Json resp_json(base::StringToJson(resp));
    base::Json* data_json = resp_json.Get("data");
    if (data_json == nullptr) return result;
    if (!data_json->IsArray())  return result;
    for (auto* item : data_json->array()) {
      IndexTraceLog log;
      auto status = google::protobuf::util::JsonStringToMessage(item->ToString(), &log);
      if (status.ok()) {
        result->emplace_back(log);
      }
    }
    // 按照时间距离现在从近到远排序
    std::sort(result->begin(), result->end(), [](const IndexTraceLog& a, const IndexTraceLog& b) {
      return a.timestamp() >= b.timestamp();
    });
    return result;
  }
};

}  // namespace ad_diag_api
}  // namespace ks
