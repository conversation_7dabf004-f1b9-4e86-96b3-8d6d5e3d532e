#pragma once

#include <string>
#include <memory>
#include <unordered_map>
#include <vector>

#include "ks/util/json.h"
#include "base/common/logging.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_diag_api/utils/http_client.h"
#include "teams/ad/ad_diag_api/utils/index_data/types.h"

namespace ks {
namespace ad_diag_api {

class TargetIndexDataGetter {};

}  // namespace ad_diag_api
}  // namespace ks
