
#pragma once

#include <string>
#include <unordered_map>
#include <vector>

#include "base/common/logging.h"
#include "base/common/map_util-inl.h"
#include "teams/ad/ad_diag_api/utils/index_data/types.h"

namespace ks {
namespace ad_diag_api {

static bool IsTableValid(const Creative& creative, std::string* reason) {
  if (creative.put_status() != AdEnum::PUT_STATUS_OPEN) {
    *reason = "投放状态未打开";
    return false;
  }
  if (creative.review_status() == AdEnum::REVIEW_THROUGH ||
      creative.review_status() == AdEnum::REVIEW_BASIC_THROUGH ||
      creative.review_status() == AdEnum::REVIEW_NEBULA_THROUGH ||
      creative.community_review_status() == AdEnum::REVIEW_THROUGH) {
    return true;
  }
  *reason = "审核未通过";
  return false;
}

static bool IsTableValid(const Unit& unit, std::string* reason) {
  if (unit.put_status() != AdEnum::PUT_STATUS_OPEN) {
    *reason = "投放状态未打开";
    return false;
  }
  if (unit.review_status() != AdEnum::REVIEW_THROUGH &&
      unit.community_review_status() != AdEnum::REVIEW_THROUGH) {
    *reason = "审核未通过";
    return false;
  }
  if (unit.external_put_status() == 2) {
    *reason = "关联短剧或商品不可投";
    return false;
  }
  if (unit.unit_algo_info().zombie_flag() == 1) {
    *reason = "僵尸单元";
    return false;
  }
  return true;
}

static bool IsTableValid(const Campaign& campaign, std::string* reason) {
  if (campaign.put_status() != AdEnum::PUT_STATUS_OPEN) {
    *reason = "投放状态未打开";
    return false;
  }
  return true;
}

static bool IsTableValid(const Account& account, std::string* reason) {
  if (account.put_status() != AdEnum::PUT_STATUS_OPEN) {
    *reason = "投放状态未打开";
    return false;
  }
  if (account.review_status() != AdEnum::REVIEW_THROUGH) {
    *reason = "审核未通过";
    return false;
  }
  if (account.frozen_status() != AdEnum::FROZEN_STATUS_CLOSED) {
    *reason = "账户被冻结";
    return false;
  }
  if (account.user_id() <= 0) {
    *reason = "绑定用户id为空";
    return false;
  }
  return false;
}

}  // namespace ad_diag_api
}  // namespace ks
