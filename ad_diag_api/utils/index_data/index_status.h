
#pragma once

#include <string>
#include <unordered_map>
#include <vector>

#include "ks/util/json.h"
#include "base/time/time.h"
#include "base/time/timestamp.h"
#include "base/common/logging.h"
#include "teams/ad/ad_diag_api/utils/index_data/types.h"

namespace ks {
namespace ad_diag_api {

struct IndexIdData {
  std::string table;
  int64_t id;
  int64_t creative_id{0};
  int64_t unit_id{0};
  int64_t campaign_id{0};
  int64_t account_id{0};
  int64_t package_id{0};
  int64_t live_user_id{0};
  int64_t photo_id{0};
  int64_t creative_index_time{0};
  int64_t unit_index_time{0};
  int64_t campaign_index_time{0};
  int64_t photo_index_time{0};
  bool valid{false};
  IndexIdData() = default;
  IndexIdData(const std::string& table, int64_t id): table(table), id(id) {}
  IndexIdData(const IndexIdData& other) :
      table(other.table),
      id(other.id),
      creative_id(other.creative_id),
      unit_id(other.unit_id),
      campaign_id(other.campaign_id),
      account_id(other.account_id),
      package_id(other.package_id),
      live_user_id(other.live_user_id),
      photo_id(other.photo_id),
      creative_index_time(other.creative_index_time),
      unit_index_time(other.unit_index_time),
      campaign_index_time(other.campaign_index_time),
      photo_index_time(other.photo_index_time) {}
};

struct IndexStatus {
  int64_t id{0};
  // 时间
  int64_t timestamp{0};
  // 产出数据的业务方     1. das   2. creative server   3. index builder    4. target index
  std::string side{""};
  // 物料状态           1. online  2. offline
  std::string status{""};
  // 过滤原因
  std::string filter{""};
  // 过滤原因说明
  std::string filter_detail {""};
  // 优化建议
  std::string suggestion{""};
  // 数据表
  std::string table{""};

  // ids
  int64_t creative_id{0};
  int64_t unit_id{0};
  int64_t campaign_id{0};
  int64_t account_id{0};
  int64_t photo_id{0};
  int64_t creative_first_index_time{0};

  std::string ToJsonString() {
    base::Json json(json_object());
    base::Time ts = base::Time::FromDoubleT((double)timestamp / 1e3);
    std::string ts_str;
    ts.ToStringInSeconds(&ts_str);
    json.set("timestamp", ts_str);
    json.set("side", side);
    json.set("status", status);
    json.set("filter", filter);
    json.set("filter_detail", filter_detail);
    json.set("suggestion", suggestion);
    return json.ToString();
  }
};

struct PsItemUpdateInfo {
  int64_t id{0};
  std::string timestamp{""};
  std::string biz{""};
  std::string status{""};
  std::string reason{""};
  int32 biz_code{0};
};

}  // namespace ad_diag_api
}  // namespace ks
