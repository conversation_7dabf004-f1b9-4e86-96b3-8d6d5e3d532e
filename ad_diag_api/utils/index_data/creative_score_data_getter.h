#pragma once

#include <algorithm>
#include <string>
#include <memory>
#include <unordered_map>
#include <vector>

#include "ks/util/json.h"
#include "base/common/logging.h"
#include "absl/strings/str_cat.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_base.pb.h"
#include "teams/ad/ad_diag_api/utils/http_client.h"
#include "teams/ad/ad_diag_api/utils/index_data/types.h"

namespace ks {
namespace ad_diag_api {

using CreativeScoreData = kuaishou::ad::algorithm::AdCreativeServerCreativeOptInfo;
using CreativeScoreVecPtr = std::shared_ptr<std::vector<CreativeScoreData>>;

class CreativeScoreDataGetter {
 public:
  static CreativeScoreVecPtr GetExternalFromHttpApi(int64_t id,
                                                    const std::string table,
                                                    int64_t start, int64_t end) {
    return GetFromHttpApi(id, table, start, end, 4);
  }

  static CreativeScoreVecPtr GetInternalFromHttpApi(int64_t id,
                                                    const std::string table,
                                                    int64_t start, int64_t end) {
    return GetFromHttpApi(id, table, start, end, 1);
  }

  static CreativeScoreVecPtr GetUniverseFromHttpApi(int64_t id,
                                                    const std::string table,
                                                    int64_t start, int64_t end) {
    return GetFromHttpApi(id, table, start, end, 2);
  }

  static CreativeScoreVecPtr GetSearchFromHttpApi(int64_t id,
                                                  const std::string table,
                                                  int64_t start, int64_t end) {
    return GetFromHttpApi(id, table, start, end, 5);
  }

 private:
  static CreativeScoreVecPtr GetFromHttpApi(int64_t id,
                                            const std::string table,
                                            int64_t start,
                                            int64_t end,
                                            int biz) {
    CreativeScoreVecPtr result = std::make_shared<std::vector<CreativeScoreData>>();
    // 1. 请求 klog 接口, 按 Token Name 初始化 http 客户端
    std::string index = "ad_creative_server_opt_info_for_biz";
    HttpClient client("AD-VWVvita9CO");
    std::string target_url = "http://api-klog.internal/gateway/query/es/ad";
    std::unordered_map<std::string, std::string> params;
    std::string query = absl::StrCat("creative_biz:", biz);
    if (table == "ad_dsp_creative") {
      query = absl::StrCat(query, " AND creative_id:", id);
    } else if (table == "ad_dsp_unit") {
      query = absl::StrCat(query, " AND unit_id:", id);
    } else if (table == "ad_dsp_campaign") {
      query = absl::StrCat(query, " AND campaign_id:", id);
    } else if (table == "ad_dsp_account") {
      query = absl::StrCat(query, " AND account_id:", id);
    } else {
      return result;
    }
    base::Json req(json_object());
    {
      req.set("index", index);
      req.set("from", "0");
      req.set("startTs", start);
      req.set("endTs", end);
      req.set("query", query);
      req.set("indexFormat", "yyyy.MM.dd");
      req.set("size", "500");
    }
    LOG(INFO) << "Creative_opt_info req: " << req.ToString();
    auto code = client.Post(true, target_url.c_str(), req.ToString(), params);
    auto& resp = client.get_result_data();
    if (code != CURLE_OK) return result;
    // 2. 解析请求的内容
    base::Json resp_json(base::StringToJson(resp));
    std::string resp_data_str;
    if (!resp_json.GetString("data", &resp_data_str)) return result;
    base::Json resp_data(base::StringToJson(resp_data_str));
    // LOG(INFO) << "resp.data: " << resp_data;
    auto* responses = resp_data.Get("responses");
    if (responses == nullptr || responses->size() == 0) return result;
    auto* hits = responses->Get(0)->Get("hits");
    if (hits == nullptr) return result;
    auto* data = hits->Get("hits");
    if (data == nullptr) return result;
    // 3. 设置返回的结果
    for (int index = 0; index < data->size(); ++index) {
      auto* item = data->Get(index)->Get("_source");
      if (item == nullptr) continue;
      google::protobuf::util::JsonParseOptions options;
      options.ignore_unknown_fields = true;
      CreativeScoreData info;
      google::protobuf::util::JsonStringToMessage(item->ToString(), &info, options);
      // 过滤掉不用结果
      // 1. 解析失败过滤
      if (info.creative_id() <= 0) continue;
      // 2. 非全量优选且分数不变日志过滤
      if (info.msg_type() != 1 && info.old_score() == info.new_score()) continue;
      result->emplace_back(info);
    }
    // 按照时间距离现在从近到远排序
    std::sort(result->begin(), result->end(), [](const auto& data1, const auto& data2) {
      return data1.timestamp() >= data2.timestamp();
    });
    return result;
  }
};

}  // namespace ad_diag_api
}  // namespace ks
