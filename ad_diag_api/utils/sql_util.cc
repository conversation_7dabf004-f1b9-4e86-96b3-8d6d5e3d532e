#include "teams/ad/ad_diag_api/utils/sql_util.h"

#include <ios>
#include <sstream>

namespace ks {
namespace ad_diag_api {
void sqlQuery(const std::string& sql, const std::string& key,
              serving_base::mysql_util::DbConnManager* manager,
              base::JsonObject* results) {
  sql::ResultSet* dataset = manager->ExecuteQuery(sql);
  if (dataset == nullptr) {
    LOG(ERROR) << "error_sql: " << sql;
    return;
  }
  try {
    dataset->beforeFirst();
    while (dataset->next()) {
      auto value = dataset->getInt(key);
      results->set(key, value);
    }
  } catch (sql::SQLException& e) {
    LOG(ERROR) << "Failed because: " << e.what();
  }
  return;
}

void sqlQuery(const std::string& sql,
              serving_base::mysql_util::DbConnManager* manager,
              base::JsonObject* object) {
  sql::ResultSet* dataset = manager->ExecuteQuery(sql);
  if (dataset == nullptr) {
    LOG(ERROR) << "error_sql: " << sql;
    return;
  }
  try {
    dataset->beforeFirst();
    while (dataset->next()) {
      sql::ResultSetMetaData* meta = dataset->getMetaData();
      for (size_t i = 1; i <= meta->getColumnCount(); ++i) {
        auto name = meta->getColumnName(i);
        int type = meta->getColumnType(i);
        if (type == sql::DataType::VARCHAR
            || type == sql::DataType::TIMESTAMP
            || type == sql::DataType::DATE
            || type == sql::DataType::TIME) {
          object->set(name, dataset->getString(name));
        } else if (type == sql::DataType::TINYINT
                  || type == sql::DataType::SMALLINT
                  || type == sql::DataType::INTEGER
                  || type == sql::DataType::BIGINT) {
          object->set(name, dataset->getInt64(name));
        } else {  // 适配 float
          std::stringstream ss;
          ss.setf(std::ios::fixed);
          ss.precision(1);
          ss << dataset->getDouble(name);
          object->set(name, ss.str());
        }
      }
    }
  } catch (sql::SQLException& e) {
    LOG(ERROR) << "Failed because: " << e.what();
  }
  return;
}

void sqlQueryPriority(const std::string& sql, serving_base::mysql_util::DbConnManager* manager,
                      base::JsonArray* results) {
  sql::ResultSet* dataset = manager->ExecuteQuery(sql);
  if (dataset == nullptr) {
    LOG(ERROR) << "error_sql: " << sql;
    return;
  }
  try {
    dataset->beforeFirst();
    while (dataset->next()) {
      base::JsonObject object;
      sql::ResultSetMetaData* meta = dataset->getMetaData();
      for (size_t i = 1; i <= meta->getColumnCount(); ++i) {
        auto name = meta->getColumnName(i);
        int type = meta->getColumnType(i);
        if (type == sql::DataType::VARCHAR
            || type == sql::DataType::TIMESTAMP
            || type == sql::DataType::DATE
            || type == sql::DataType::TIME) {
          object.set(name, dataset->getString(name));
        } else if (type == sql::DataType::TINYINT
                  || type == sql::DataType::SMALLINT
                  || type == sql::DataType::INTEGER
                  || type == sql::DataType::BIGINT) {
          object.set(name, dataset->getInt64(name));
        } else {  // 适配 float
          std::stringstream ss;
          ss.setf(std::ios::fixed);
          ss.precision(1);
          ss << dataset->getDouble(name);
          object.set(name, ss.str());
        }
      }
      results->append(object);
    }
  } catch (sql::SQLException& e) {
    LOG(ERROR) << "Failed because: " << e.what();
  }
  return;
}

void getExistedId(const std::string& sql,
              serving_base::mysql_util::DbConnManager* manager,
              std::unordered_set<int64>* id_set) {
  sql::ResultSet* dataset = manager->ExecuteQuery(sql);
  if (dataset == nullptr) {
    LOG(ERROR) << "error_sql: " << sql;
    return;
  }
  try {
    dataset->beforeFirst();
    while (dataset->next()) {
      auto value = dataset->getInt("id");
      (*id_set).emplace(value);
    }
  } catch (sql::SQLException& e) {
    LOG(ERROR) << "Failed because: " << e.what();
  }
  return;
}

void getExistedId(const std::string& sql,
              serving_base::mysql_util::DbConnManager* manager,
              std::unordered_set<std::string>* id_set) {
  sql::ResultSet* dataset = manager->ExecuteQuery(sql);
  if (dataset == nullptr) {
    LOG(ERROR) << "error_sql: " << sql;
    return;
  }
  try {
    dataset->beforeFirst();
    while (dataset->next()) {
      auto value = dataset->getString("id");
      (*id_set).emplace(value);
    }
  } catch (sql::SQLException& e) {
    LOG(ERROR) << "Failed because: " << e.what();
  }
  return;
}

}  // namespace ad_diag_api
}  // namespace ks
