syntax  = "proto3";
option cc_enable_arenas = true;
package ks.ad_diag_api.kconf;

message KconfProtoSample {
}

message EngineFlowMeta {
  string flow_name = 1;  // 极速版发现页 & 同城页 之类的中文名字
  string flow_value = 2; // 跟 flow_name 对应的英文标识
  string flow_engine_env = 3; // 流量走哪个集群, default & thanos & detail & galaxy & knews 等
}

message EngineFlowData {
  repeated EngineFlowMeta engine_flow_meta = 1;
}

message DebugServerMeta {
  string server_name = 1;  // 服务的名字
  string grpc_kess_name = 2; // 服务注册 kess
}

message DebugServerData {
  repeated DebugServerMeta debug_server_meta = 1;
}

message SessionTimeRange {
  int64 hate_range = 1; // 负反馈
  int64 imp_range = 2; // 展现
  int64 click_range = 3; // 点击
  int64 click2_range = 4; // 二跳点击
}

message WhiteBoxDebugSpecialFilterReasonsMsg {
  repeated string reasons = 1;
}

message MySqlTableQueryColumnMappingName {
  message TableColumnMapping {
    map<string, string> column_config = 1;
  }
  map<string, TableColumnMapping> table_config = 1;
}

message WhiteBodApiTabConf {
  // c++ 服务一个tab对应一个接口, 根据 uri 判断就行
  message OneConf {
    repeated string matchItem = 1;
    repeated string regPattern = 2;
  }
  message OneTabConf {
    OneConf uri = 1;
    string perfSubTag = 3;
  }
  repeated OneTabConf items = 1;
}