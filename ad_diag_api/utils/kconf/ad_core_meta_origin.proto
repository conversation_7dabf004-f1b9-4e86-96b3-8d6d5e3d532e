syntax = "proto3";

package kuaishou.adcore.meta;

import "teams/ad/ad_diag_api/utils/kconf/ad_core_meta_base.proto";
// import "google/protobuf/wrappers.proto";

message CampaignIdsGetRequest{
 MetaCurrentScope meta_current_scope = 1;
 uint64 account_id = 2;
 repeated uint32 put_status_in = 3;
}

message UnitIdsGetRequest{
 MetaCurrentScope meta_current_scope = 1;
 uint64 account_id = 2;
 repeated uint64 campaign_id = 3;
 repeated uint32 put_status_in = 4;
}

message CreativeIdsGetRequest{
 MetaCurrentScope meta_current_scope = 1;
 uint64 account_id = 2;
 repeated uint64 campaign_id = 3;
 repeated uint64 unit_id = 4;
 repeated uint32 put_status_in = 5;
}

message IdsGetResponse {
 MetaRespStatus status = 1;
 repeated uint64 id = 2;
}

service AdCoreMetaOriginService {

 rpc GetCampaignIds (CampaignIdsGetRequest) returns (IdsGetResponse);
 rpc GetUnitIds (UnitIdsGetRequest) returns (IdsGetResponse);
 rpc GetCreativeIds (CreativeIdsGetRequest) returns (IdsGetResponse);
 rpc GetAdvProgramCreativeIds (CreativeIdsGetRequest) returns (IdsGetResponse);

}

