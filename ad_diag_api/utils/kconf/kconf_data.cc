#include "teams/ad/ad_diag_api/utils/kconf/kconf_data.h"

#include <map>
#include <regex>

#include "absl/strings/numbers.h"
#include "base/strings/string_number_conversions.h"
#include "glog/stl_logging.h"
#include "rapidjson/document.h"
#include "rapidjson/error/error.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_index/framework/target/target_key.h"

namespace ks {
namespace ad_diag_api {

bool DebugSpecialFilterReasons::Init() {
  for (const std::string& reason : pb().reasons()) {
    reasons.insert(reason);
  }
  return true;
}

int64_t DebugSpecialFilterReasons::IsSpecialReason(const std::string& reason) const {
  if (reasons.count(reason) > 0) {
    return true;
  }
  return false;
}

const std::unordered_set<std::string>& DebugSpecialFilterReasons::GetReasons() const {
  return reasons;
}

bool MySqlTableQueryColumnMappingNameStruct::Load(const std::string& json_str) {
  if (json_str.empty()) {
    return true;
  }
  base::Json json_obj(base::StringToJson(json_str));
  if (!json_obj.IsArray()) {
    return true;
  }
  for (auto table_iter = json_obj.array_begin(); table_iter != json_obj.array_end(); ++table_iter) {
    const base::Json& table_conf = **table_iter;
    if (!table_conf.IsObject()) {
      LOG(INFO) << "table_conf is not object. table_name";
      continue;
    }
    std::string table_mapping_name = table_conf.GetString("table_mapping_name", "");
    std::string table_name = table_conf.GetString("table_name", "");
    if (table_name.empty()) {
      LOG(INFO) << "table_name is empty. table_mapping_name: " << table_mapping_name;
      continue;
    }
    if (!table_mapping_name.empty()) {
      mapping_name_2_table_name[table_mapping_name] = table_name;
      table_name_2_mapping_name[table_name] = table_mapping_name;
    }
    base::Json* column_conf_array = table_conf.Get("column_conf");
    if (column_conf_array == nullptr || !(column_conf_array->IsArray())) {
      LOG(INFO) << "column_conf is nullptr or is not array. table_mapping_name: " << table_mapping_name
                << ", table_name: " << table_name
                << ", is nullptr: " << (column_conf_array == nullptr ? "true" : "false") << ", is array: "
                << (column_conf_array == nullptr ? "nullptr"
                                                 : (column_conf_array->IsArray() ? "true" : "false"));
      continue;
    }
    std::vector<ColumnConfig> table_config_list;
    std::unordered_map<std::string, ColumnConfig> table_config_map;
    for (auto column_iter = column_conf_array->array_begin(); column_iter != column_conf_array->array_end();
         ++column_iter) {
      const base::Json& column_conf = **column_iter;

      const std::string& column_name = column_conf.GetString("column_name", "");
      const std::string& mapping_name = column_conf.GetString("mapping_name", "");
      const std::string& type = column_conf.GetString("type", "");
      const bool can_be_modified = column_conf.GetBoolean("can_be_modified", false);
      if (column_name.empty() || mapping_name.empty() || type.empty()) {
        LOG(INFO) << "column_conf is empty. table_name: " << table_name << ", column_name: " << column_name
                  << ", mapping_name: " << mapping_name << ", type: " << type;
        continue;
      }
      ColumnConfig config;
      config.column_name = column_name;
      config.mapping_name = mapping_name;
      config.can_be_modified = can_be_modified;
      config.type = (type == "int" ? 1 : (type == "string" ? 2 : 0));
      table_config_list.emplace_back(config);
      table_config_map[column_name] = config;
    }
    column_config_map[table_name] = table_config_map;
    column_config_list[table_name] = table_config_list;
  }
  return true;
}

const std::vector<ColumnConfig>& MySqlTableQueryColumnMappingNameStruct::GetTableMappingConfig(
    const std::string& table_name) const {
  const auto& iter = column_config_list.find(table_name);
  if (iter == column_config_list.end()) {
    return default_value;
  }
  return iter->second;
}

bool MySqlTableQueryColumnMappingNameStruct::IsIntType(const std::string& table_name,
                                                       const std::string& column_name) const {
  const auto& iter = column_config_map.find(table_name);
  if (iter == column_config_map.end()) {
    return false;
  }
  const auto& type_iter = iter->second.find(column_name);
  if (type_iter == iter->second.end()) {
    return false;
  }
  return type_iter->second.IsIntType();
}

bool MySqlTableQueryColumnMappingNameStruct::IsStringType(const std::string& table_name,
                                                          const std::string& column_name) const {
  const auto& iter = column_config_map.find(table_name);
  if (iter == column_config_map.end()) {
    return false;
  }
  const auto& type_iter = iter->second.find(column_name);
  if (type_iter == iter->second.end()) {
    return false;
  }
  return type_iter->second.IsStringType();
}

std::string MySqlTableQueryColumnMappingNameStruct::GetTableMappingName(const std::string& table_name) const {
  const auto& iter = table_name_2_mapping_name.find(table_name);
  if (iter != table_name_2_mapping_name.end()) {
    return iter->second;
  }
  return table_name;
}

std::string MySqlTableQueryColumnMappingNameStruct::GetTableName(const std::string& mapping_name) const {
  const auto& iter = mapping_name_2_table_name.find(mapping_name);
  if (iter != mapping_name_2_table_name.end()) {
    return iter->second;
  }
  return "";
}

bool WhiteBodApiTabConfStruct::Init() {
  LOG(INFO) << "ZT_TEST_DEBUG: kconfContent: " << pb().ShortDebugString();
  return true;
}

std::string WhiteBodApiTabConfStruct::getTabType(const std::string& uri) const {
  for (const auto& one_tab_conf : pb().items()) {
    // uri 完全匹配
    for (const auto& match_item : one_tab_conf.uri().matchitem()) {
      if (uri == match_item) {
        return one_tab_conf.perfsubtag();
      }
    }
    // uri 正则表达式匹配
    std::smatch pieces_match;
    for (const auto& reg_pattern : one_tab_conf.uri().regpattern()) {
      if (std::regex_match(uri, pieces_match, std::regex(reg_pattern))) {
        return one_tab_conf.perfsubtag();
      }
    }
  }
  return "";
}

}  // namespace ad_diag_api
}  // namespace ks

