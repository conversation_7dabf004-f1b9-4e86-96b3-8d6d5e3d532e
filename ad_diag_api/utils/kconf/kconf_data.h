#pragma once

#include <iostream>
#include <set>
#include <string>
#include <map>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>
#include <algorithm>

#include "rapidjson/document.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/ad_diag_api/utils/kconf/kconf_data.pb.h"

namespace base {
class Json;
}  // namespace base

namespace Json {
class Value;
}  // namespace Json
namespace ks {
namespace ad_diag_api {

class DebugSpecialFilterReasons
    : public ks::ad_base::kconf::KconfInitProto<kconf::WhiteBoxDebugSpecialFilterReasonsMsg> {
 private:
  std::unordered_set<std::string> reasons;

 public:
  bool Init() override;
  int64_t IsSpecialReason(const std::string& reason) const;
  const std::unordered_set<std::string>& GetReasons() const;
};

struct ColumnConfig {
  std::string column_name;
  std::string mapping_name;
  int type;   // 1 int  2 string
  bool can_be_modified;
  bool IsIntType() const {
    return type == 1;
  }
  bool IsStringType() const {
    return type == 2;
  }
};

class MySqlTableQueryColumnMappingNameStruct {
 public:
  MySqlTableQueryColumnMappingNameStruct() = default;
  virtual ~MySqlTableQueryColumnMappingNameStruct() = default;

  bool Load(const std::string& json_str);
  const std::vector<ColumnConfig>& GetTableMappingConfig(const std::string& table_name) const;
  bool IsIntType(const std::string& table_name, const std::string& column_name) const;
  bool IsStringType(const std::string& table_name, const std::string& column_name) const;
  std::string GetTableMappingName(const std::string& table_name) const;
  std::string GetTableName(const std::string& mapping_name) const;

 private:
  std::vector<ColumnConfig> default_value;
  // <table_name, <column_name, column_config>>  通过 tableName + columnName 查配置
  std::unordered_map<std::string, std::unordered_map<std::string, ColumnConfig>> column_config_map;
  // 需要保序
  std::unordered_map<std::string, std::vector<ColumnConfig>> column_config_list;
  // 数据库中文名 -> 英文名映射
  std::unordered_map<std::string, std::string> mapping_name_2_table_name;
  // 数据库英文名 -> 中文名映射
  std::unordered_map<std::string, std::string> table_name_2_mapping_name;
};

class WhiteBodApiTabConfStruct : public ks::ad_base::kconf::KconfInitProto<kconf::WhiteBodApiTabConf> {
 public:
  bool Init() override;
  std::string getTabType(const std::string& uri) const;
};

}  // namespace ad_diag_api
}  // namespace ks

