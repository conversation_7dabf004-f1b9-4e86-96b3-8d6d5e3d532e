#pragma once

#include <string>
#include "base/common/basic_types.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/ad_diag_api/utils/kconf/kconf_data.h"
#include "teams/ad/ad_diag_api/utils/kconf/kconf_data.pb.h"

#ifndef KCONF_CC_WITH_IMPL
  #undef  DEFINE_KCONF_NODE
  #define DEFINE_KCONF_NODE(type, config_path, config_key, default_value)                    \
    API_KCONF_NODE(type, config_path, config_key, default_value)
  #undef DEFINE_KCONF_NODE_LOAD
  #define DEFINE_KCONF_NODE_LOAD(type, config_path, config_key)                              \
    API_KCONF_NODE_LOAD(type, config_path, config_key)
  #undef DEFINE_SET_NODE_KCONF
  #define DEFINE_SET_NODE_KCONF(type, config_path, config_key)                                  \
    API_SET_NODE_KCONF(type, config_path, config_key)
  #undef DEFINE_KCONF_MAP_KCONF
  #define DEFINE_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key) \
    API_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key)
#else
  #undef DEFINE_KCONF_NODE_ATTR
  #define DEFINE_KCONF_NODE_ATTR __attribute__ ((used))
#endif

namespace ks {
namespace ad_diag_api {
using namespace ks::ad_base::kconf;  // NOLINT
using namespace ks::ad_diag_api::kconf;   //  NOLINT
class AdKconfUtil {
 public:
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, nebulaExploreAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, innerExploreAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, kuaishouExploreAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, kuaishouFeaturedAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, nebulaInspireAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, kuaishouInspireAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, microAppInspireAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, kuaishouSearchAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, nebulaSearchAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, detailAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, kuaishouSplashAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, knewsAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, universeAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, inspireLiveAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, backupAdRequest, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, cloudCookie, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adDiagApi, gitCloneCookie, "7JVuev1wD_qBXKsLxk2G")

  DEFINE_PROTOBUF_NODE_KCONF(EngineFlowData, ad.adDiagApi, engineFlowData)
  DEFINE_PROTOBUF_NODE_KCONF(DebugServerData, ad.adDiagApi, debugServerData)
  DEFINE_PROTOBUF_NODE_KCONF(SessionTimeRange, ad.engine, sessionTimeRange)
  DEFINE_PROTOBUF_NODE_KCONF(DebugSpecialFilterReasons, ad.adserver2, whiteBoxDebugSpecialFilterReasons)
  DEFINE_PROTOBUF_NODE_KCONF(WhiteBodApiTabConfStruct, adData.whiteBox, whiteBoxApiPerfConf)
  DEFINE_KCONF_NODE_LOAD(MySqlTableQueryColumnMappingNameStruct, ad.adDiagApi, mySqlTableQueryColumnName)

  DEFINE_STRING_STRING_MAP_KCONF(ad.adDiagApi, adDebugKeyMap)
  DEFINE_STRING_STRING_MAP_KCONF(ad.adDiagApi, preDebugKey)
  DEFINE_STRING_STRING_MAP_KCONF(ad.adDiagApi, keyWordMapping)
  DEFINE_STRING_STRING_MAP_KCONF(ad.adDiagApi, tableNameMapping)
  DEFINE_STRING_STRING_MAP_KCONF(ad.adDiagApi, fileUrlMapping)
  DEFINE_STRING_STRING_MAP_KCONF(ad.adDiagApi, adTraceCommonSearchKey)
  DEFINE_STRING_STRING_MAP_KCONF(ad.adDiagApi, IndexUrlMapping);
  DEFINE_STRING_STRING_MAP_KCONF(ad.adDiagApi, targetIndexStatusQueryUrl);

  DEFINE_INT64_STRING_MAP_KCONF(ad.adDiagApi, creativeServerScoreReason);
  DEFINE_INT64_STRING_MAP_KCONF(ad.adDiagApi, indexFilterReason);
  DEFINE_INT64_STRING_MAP_KCONF(ad.adDiagApi, indexFilterSuggestion);
  DEFINE_SET_NODE_KCONF(int64, ad.adDiagApi, indexFilterReasonSkipCode)

  DEFINE_JSON_NODE_KCONF(ad.adDiagApi, fileUrlSearchKeyTableInfo)
  DEFINE_JSON_NODE_KCONF(ad.adDiagApi, annotationHandler)
  DEFINE_JSON_NODE_KCONF(ad.adDiagApi, uniqueCompose)

  DEFINE_SET_NODE_KCONF(std::string, ad.adDiagApi, modifyPermissionWhiteList)
  DEFINE_SET_NODE_KCONF(std::string, ad.adDiagApi, permissionWhiteList)
  DEFINE_SET_NODE_KCONF(std::string, ad.adDiagApi, filterConditionBlackList)
  DEFINE_SET_NODE_KCONF(std::string, ad.adDiagApi, mySqlTableEditPermissionUserList)
  DEFINE_SET_NODE_KCONF(int32, ad.adDiagApi, UpdatePosIdsBlackList)

  DEFINE_LIST_NODE_KCONF(std::string, ad.adDiagApi, mysqlTableName)

  DEFINE_BOOL_KCONF_NODE(ad.adDiagApi, enableUpdataPos, true)
  DEFINE_BOOL_KCONF_NODE(ad.adDiagApi, disableFanstopClientShowData, false)
  DEFINE_BOOL_KCONF_NODE(ad.adDiagApi, enableUpdataPosInfoToKconf, false)
  DEFINE_BOOL_KCONF_NODE(ad.adDiagApi, enableP2pPosInfo, false)
  DEFINE_BOOL_KCONF_NODE(ad.adDiagApi, enableUseKDBTest, true)
  DEFINE_BOOL_KCONF_NODE(ad.adDiagApi, enableMoveCreativeDebugOperations, false)
  DEFINE_INT32_KCONF_NODE(ad.adDiagApi, updataPosInfoToKconfTime, 22);
  DEFINE_INT32_KCONF_NODE(ad.adCreativeServer, accountCntPerProduct, 100);
};

}  // namespace ad_diag_api
}  // namespace ks

#ifndef KCONF_CC_WITH_IMPL
  #undef  DEFINE_KCONF_NODE
  #define DEFINE_KCONF_NODE(type, config_path, config_key, default_value)                    \
    DEFINE_KCONF_NODE_BODY(type, config_path, config_key, default_value)
  #undef DEFINE_KCONF_NODE_LOAD
  #define DEFINE_KCONF_NODE_LOAD(type, config_path, config_key)                              \
    DEFINE_KCONF_NODE_LOAD_BODY(type, config_path, config_key)
  #undef DEFINE_SET_NODE_KCONF
  #define DEFINE_SET_NODE_KCONF(type, config_path, config_key)                                  \
    DEFINE_SET_NODE_KCONF_BODY(type, config_path, config_key)
  #undef DEFINE_KCONF_MAP_KCONF
  #define DEFINE_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key) \
    DEFINE_KCONF_MAP_KCONF_BODY(key_type, value_type, config_path, config_key)
#else
  #undef DEFINE_KCONF_NODE_ATTR
  #define DEFINE_KCONF_NODE_ATTR
#endif
