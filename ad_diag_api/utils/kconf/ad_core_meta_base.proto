syntax = "proto3";

package kuaishou.adcore.meta;


// 基础架构通过ScopeInterceptor注入了请求相关的参数
message MetaRequestInfo {
  string opt_hostname = 1;
  string opt_ip = 2;
  string request_kslog = 3;
  string request_id = 4;
  string device_id = 5;
  string client_id = 6;
  string cookie = 7;
  string http_accept = 8;
  string user_agent = 9;
  string referer = 10;
}

// visitor 基础架构会负责grpc的上下文
message MetaCurrentScope {
  int32 caller_type = 1;
  MetaRequestInfo meta_request_info = 2;
  MetaAccountInfo account = 3;
  MetaAgentLoginInfo agent = 4;
  MetaVisitorInfo visitor = 5;
}

message MetaAccountInfo {
  uint64 account_id = 1;
  uint64 owner_user_id = 2;
  uint64 name = 3;
  uint64 put_status = 4;
}

message MetaAgentLoginInfo {
  uint64 account_uc_id = 1;
  uint64 account_user_id = 2;
  uint64 agent_user_id = 3;
  uint64 admin_user_id = 4;
}

message MetaVisitorInfo {
  uint64 ks_user_id = 1;
  string name = 2;
  string visitor_head_url = 3;
}

message ResponseHolder {
  bytes value = 1;
}

message PageInfoPb {
  uint32 page_num = 1;
  uint32 page_size = 2;
  uint64 total_count = 3 ;
}

message MetaRespStatus {
  uint32 code = 1; // 是否操作成功 result=1为成功
  string error_msg = 2; // 错误描述
}

