#pragma once

#include <string>
#include "serving_base/utility/web_request_handler.h"
#include "teams/ad/ad_diag_api/service/web_service.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_ueq_service.kess.grpc.pb.h"

DECLARE_string(ab_test_group_allocation_filename);
namespace ks {
namespace ad_diag_api {

class DiagHandler : public serving_base::WebRequestHandler {
 public:
  DiagHandler() {}
  virtual void ProcessRequest(net::HTTPSession* session) {};
  
 private:

  DISALLOW_COPY_AND_ASSIGN(DiagHandler);
};

class AdDiagGrpcServiceImpl final : public kuaishou::ad::kess::AdUeqService::Service {
 public:
  explicit AdDiagGrpcServiceImpl(int thread_num) : thread_num_(thread_num) {
    for (int i = 0; i < thread_num_; ++i) {
      handler_list_.Put(new DiagHandler());
    }
  }

  ~AdDiagGrpcServiceImpl() {
    for (int i = 0; i < thread_num_; ++i) {
      delete handler_list_.Take();
    }
  }

 private:
  const int thread_num_;
  thread::BlockingQueue<DiagHandler*> handler_list_;

  DISALLOW_COPY_AND_ASSIGN(AdDiagGrpcServiceImpl);
  
};

class AdWebServiceHandlerDict : public serving_base::WebRequestHandlerDict {
 public:
  AdWebServiceHandlerDict();
  ~AdWebServiceHandlerDict() {
    for (auto it = handler_dict_.begin(); it != handler_dict_.end(); ++it) {
      delete it->second;
    }
  }
  static serving_base::WebRequestHandlerDict* CreateHandler();
 private:
  DISALLOW_COPY_AND_ASSIGN(AdWebServiceHandlerDict);

};

}  // namespace ad_diag_api
}  // namespace ks



