#pragma once

#include <string>

#include "serving_base/utility/web_request_handler.h"
#include "teams/ad/ad_diag_api/utils/kconf/kconf.h"

namespace ks {
namespace ad_diag_api {

// NOTE: 这里维护所有和 grpc 无关的 http 接口逻辑

DECLARE_string(ab_test_group_allocation_filename);

void SendTraceLog(const std::string& uri, const std::string& user_name, const int64_t& time_cost_ms);

#define INTERFACE(interface_name)                                                                         \
  class interface_name##Handler : public serving_base::WebRequestHandler {                                \
   public:                                                                                                \
    interface_name##Handler() {}                                                                          \
    virtual void ProcessRequest(net::HTTPSession* session) {                                              \
      int64_t start_ts_ms = base::GetTimestamp() / 1000;                                                  \
      std::string uri = session->request.path().as_string();                                              \
      auto iter = cookies_.find("username");                                                              \
      std::string user_name = "";                                                                         \
      if (iter != cookies_.end()) {                                                                       \
        user_name = iter->second;                                                                         \
      }                                                                                                   \
      ProcessRequestInner(session);                                                                       \
      int64_t time_cost_ms = base::GetTimestamp() / 1000 - start_ts_ms;                                   \
      SendTraceLog(uri, user_name, time_cost_ms);                                                         \
      ks::infra::PerfUtil::IntervalLogStash(time_cost_ms, "ad.white_box_api", "time_interval_total", uri, \
                                            "", "", "", user_name);                                       \
    }                                                                                                     \
    void ProcessRequestInner(net::HTTPSession* session);                                                  \
                                                                                                          \
   private:                                                                                               \
    DISALLOW_COPY_AND_ASSIGN(interface_name##Handler);                                                    \
  };


INTERFACE(GetFlowTypeList)  // 获取流量类型列表接口

INTERFACE(GetRequestData)   // 获取原始请求串接口

INTERFACE(GetDebugServer)   // 获取可调试 Server 列表接口

INTERFACE(TiggerRequest)    // 触发调试请求接口

INTERFACE(GetOnlineData)    // 在线数据查询

INTERFACE(GetForwardData)   // 在线数据查询

INTERFACE(GetPostData)      // 后验数据查询

INTERFACE(GetInvertData)    // 倒排数据查询

INTERFACE(GetUserSessionData)   // 获取用户历史数据

INTERFACE(GetDebugServerHost)   // podName -> ip:port

INTERFACE(GetCreativeOptInfo)   // 创意优选 creative_score 变化

INTERFACE(GetIndexData)       // 索引一键查询

INTERFACE(GetExternalIndexData)   // 外循环索引一键查询

INTERFACE(GetCreativeOptStats)   // 创意优选统计信息

INTERFACE(PermissionCheck)  // 更新数据库表的权限检查

INTERFACE(GetMysqlTableName)  // mysql 数据库表名

INTERFACE(QueryIdName)  // 根据 id 进行数据库的查询

INTERFACE(InsertIdName)  // 向表中插入 id->name

INTERFACE(UpdateMySqlTableRow)  // 更新 mysql 数据工具包数据表

INTERFACE(QueryAdDspPosition)  // 查询广告位信息

INTERFACE(UpdateAdDspPosition)  // 更新广告位信息

INTERFACE(DeleteAdDspPosition)  // 删除广告位信息

INTERFACE(PermissionUser)  // 用户权限

INTERFACE(TriggerAdPreview)  // 触发广告体验

INTERFACE(GetPermission)  // 触发广告体验

INTERFACE(AdDebugKey)  //  广告位过滤原因 debug

INTERFACE(DebugKeySupportScene)  //  支持 debug 的场景

INTERFACE(QuerySubLevelIds)  //  子层级数据查询

INTERFACE(GetUserName)  //  获取登录的用户名

INTERFACE(HandleLogin)  //  用户登录认证

INTERFACE(GetUserAdxAds)  //  用户登录认证

INTERFACE(QueryFilterReason)  // 查询过滤原因

INTERFACE(UpdateFilterReason)  // 更新过滤原因
}  // namespace ad_diag_api
}  // namespace ks
