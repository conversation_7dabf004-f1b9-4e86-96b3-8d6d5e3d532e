#include "teams/ad/ad_diag_api/service/kess_server_service.h"
#include "teams/ad/ad_base/src/ksp/ad_web_request_handler.h"

DEFINE_string(ab_test_group_allocation_filename,
              "../../ab_test_group_allocation_config/config/ab_test_group_allocation.json",
              "config file storing ab test grouping info.");

namespace ks {
namespace ad_diag_api {

AdWebServiceHandlerDict::AdWebServiceHandlerDict() : WebRequestHandlerDict(true) {
    /* ======== 服务接口 ========= */
}

serving_base::WebRequestHandlerDict* AdWebServiceHandlerDict::CreateHandler() {
  ks::ad_base::DefaultAdWebServiceDict::CallDict handler;
  handler["/proxy/get_flow_type_list"] = new GetFlowTypeListHandler();     // 获取支持的流量类型列表
  handler["/proxy/get_ad_request"] = new GetRequestDataHandler();          // 获取原始请求串
  handler["/proxy/get_debug_server_list"] = new GetDebugServerHandler();   // 获取支持路由的服务列表
  handler["/proxy/trigger_ad_request"] = new TiggerRequestHandler();       // 通过前端触发请求
  handler["/proxy/get_forward_data"] = new GetForwardDataHandler();        // 获取正排数据
  handler["/proxy/get_post_data"] = new GetPostDataHandler();              // 获取后验数据
  handler["/proxy/get_invert_data"] = new GetInvertDataHandler();          // 获取倒排数据
  handler["/api/utils/GetUserSession"] = new GetUserSessionDataHandler();        // 获取用户历史广告信息
  handler["/api/utils/GetUserAdxAds"] = new GetUserAdxAdsHandler();        // 获取用户 adx 广告
  handler["/proxy/get_debug_server_host"] = new GetDebugServerHostHandler();     // 设置监听的 creative_id
  handler["/proxy/get_creative_opt_info"] = new GetCreativeOptInfoHandler();  // 获取创意优选 creative_score
  handler["/proxy/query_index_data"] = new GetIndexDataHandler();             // 获取索引数据信息
  handler["/proxy/query_external_index_data"] = new GetExternalIndexDataHandler();   // 获取外循环索引数据信息
  handler["/proxy/query_creative_opt_stats"] = new GetCreativeOptStatsHandler();   // 获取外循环索引数据信息
  handler["/proxy/permission_check"] = new PermissionCheckHandler();         //  更新数据库表的权限检查
  handler["/proxy/get_mysql_table_name"] = new GetMysqlTableNameHandler();    //  获取 mysql 数据库表名
  handler["/proxy/query_id_name"] = new QueryIdNameHandler();                 //  根据 id 进行数据库的查询
  handler["/proxy/insert_id_name"] = new InsertIdNameHandler();               //  向表中插入 id->name
  handler["/proxy/query_ad_dsp_position"] = new QueryAdDspPositionHandler();  //  查询广告位信息
  handler["/proxy/update_ad_dsp_position"] = new UpdateAdDspPositionHandler();  //  更新广告位字段数据
  handler["/proxy/delete_ad_dsp_position"] = new DeleteAdDspPositionHandler();  //  删除广告位信息
  handler["/proxy/get_all_developers"] = new PermissionUserHandler();  //  删除广告位信息
  handler["/proxy/trigger_ad_preview"] = new TriggerAdPreviewHandler();  //  触发广告预览
  handler["/proxy/get_user_permission"] = new GetPermissionHandler();   //  获取用户权限
  handler["/proxy/ad_debug_handler"] = new AdDebugKeyHandler();         //  广告位过滤原因 debug
  handler["/proxy/debug_key_support_scene"] = new DebugKeySupportSceneHandler();  //  支持 debug 的场景
  handler["/proxy/query_sub_level_ids"] = new QuerySubLevelIdsHandler();  //  子层级数据查询
  handler["/api/utils/isLogin"] = new GetUserNameHandler();  //  获取登录的用户名
  handler["/api/utils/handleLogin"] = new HandleLoginHandler();  //  登录认证
  handler["/proxy/update_table"] = new UpdateMySqlTableRowHandler();  // 数据工具包 mysql 数据表更新
  handler["/proxy/query_filter_reason"] = new QueryFilterReasonHandler();  // 查询过滤原因
  handler["/proxy/update_filter_reason"] = new UpdateFilterReasonHandler();  // 更新过滤原因
  return new ks::ad_base::DefaultAdWebServiceDict(handler);
}

}  // namespace ad_diag_api
}  // namespace ks

