{"status": 200, "message": "success", "data": {"id": 1604497, "uuid": "89c03e15515d4a23813bc545ca032d52", "diffMd5": "51e3d74c6a66e30f6eb3f3a6a3c6e1c5", "scmType": "git", "gitProjectId": 34312, "svnPath": "*************************:ad/ad.git,master", "version": "0f813f24feab45d5dc94337ce72f3ed111387c7b", "message": "", "teamIds": "", "viewers": "", "gitCommitRange": "HEAD^:HEAD", "status": 1, "deleted": 0, "creator": "<PERSON><PERSON><PERSON><PERSON>", "updater": "<PERSON><PERSON><PERSON><PERSON>", "createTime": 1703753428342, "updateTime": 1703753428342, "localTime": 1703753093000, "mrId": 1604497, "mergeUser": "", "mergeTimeSubmit": 0, "mergeTimeBegin": 0, "mergeTimeEnd": 0, "mergeMessage": "", "mergeComment": "", "pipelineArgs": "", "repoList": [{"id": 15736, "projectId": 34312, "description": null, "name": null, "path": null, "webUrl": null, "lastActivityAt": null, "groupId": 17172, "branchId": 670930}], "statusDesc": "已创建", "repoMrSetting": null}, "traceId": "0a8f0c70658d37545434b8c826d77986", "timestamp": 1703753556713}