#!/bin/bash

path=`pwd`
regex='^(feat|fix|docs|style|refactor|perf|test|build|chore|revert|removed)\(\s*(location|module|all|.+)\):\s*[^\s]+'

result=$(grep -E $regex $1)

# current branch name
current_branch=$(git branch | grep \* | cut -d ' ' -f2)

hook_path=$(git status --porcelain|cut -d '/' -f1|awk -F' ' '{print $NF}'|uniq|tr '\n' ',')

if [ -z "$hook_path" ]; then
    exit 0
fi

RESULT_FOO=`python ${path}/.git/hooks/commitHook.py -m ${hook_path}`

RED=$(tput setaf 1)
YELLOW=$(tput setaf 3)
RESET=$(tput sgr0)

# if [[ $result ]]
if [[ $result || $current_branch != "master" || $RESULT_FOO == "False" ]]
then
  echo $result
else
  echo "${RED}"
  echo "请按规范填写commit-msg 详情见https://docs.corp.kuaishou.com/d/home/<USER>"
  echo "${RESET}"
  echo "${YELLOW}"
  cat $path/.git/hooks/template_git
  exit 1
fi
