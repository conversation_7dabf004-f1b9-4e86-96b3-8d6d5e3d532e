Commit Message格式：请按规范提交commit信息
<type>(<scope>): <subject>
<!-- 空行 -->
<body>
<!-- 空行 -->
  
###########################################################################################################################
举例：
refactor(adFront/adRank):添加指标打点用于验证实验功能
针对front的流量出口、rank的出口队列大小进行打点监控，用于验证PV融合不平的问题
  
###########################################################################################################################
字段	必须	说明
type	是	提交类别（下面列表详细说明）
scope	是	commit影响范围
subject	是	commit 目的的简短描述，不超过50个字符
body	否(建议填写)	描述当前修改的行为详细信息或修改的目的，以下变更必填：1. 影响主逻辑的重构;2. 影响排序计费公式的逻辑

###########################################################################################################################
Type值	说明
feat	添加新特性
fix	修复bug
docs	仅仅修改了文档
style	仅仅修改了空格、格式缩进、逗号等，不改变代码逻辑
refactor	代码重构，没有添加新功能或修复bug
test	添加测试用例
chore	改变构建建流程、或者增加依赖库、工具等
revert	回滚到上一个版本
removed	删除功能或无用代码
perf	性能优化

