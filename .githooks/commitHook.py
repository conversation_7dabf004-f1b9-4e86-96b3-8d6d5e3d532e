#!/usr/bin/env python
# -*- coding: utf-8 -*-
#scriptname:commitHook.py
import argparse
import json
import logging
import sys
import os
try:
  import commands  # python2
except:
  import subprocess as commands  # python3

def getUser():
    """"""

    ret, user = execute("svn auth | grep Username | awk '{print $2}' | head -n1")
    if 0 != ret:
        user = "nobody"
    else:
        if "Permission denied" in user:
            user = os.environ.get("BUILD_USER_NAME","")
        user = user.strip()
        if user == "root" or len(user) == 0:
            hostname = os.environ.get("HOSTNAME","")
            if hostname:
                tlist = hostname.split("-")
                if tlist:
                    if hostname.find("horus-s-")!=-1:
                        if len(tlist)>3:user = tlist[3]
                    else:
                        if len(tlist)>1:user = tlist[1]
    return user


def execute(cmd):
    """"""
    try:
      logging.debug("执行 [%s]" % cmd)
      code, output = commands.getstatusoutput(cmd)
      return code, output
    except:
      logging.debug("execute except")
      return 1, ""

def getkconfMessage(url):
    """getkconfMessage"""

    return_msg = []
    get_msg = {}

    ret, msg = execute("curl -s  " + url ) #https://ad-env.corp.kuaishou.com/diffreport/kconfSwitchGet?key=adqa.adForYQ.CommitHook&stage=testing&snapshotId=0")

    result = json.loads(msg)
   
    if "status" in result.keys() and result["status"] == "0":
        if "message" in result.keys():
            if "subConfigs" in result["message"]:
                if "data" in result["message"]["subConfigs"][0]:
                    data_dict = json.loads(result["message"]["subConfigs"][0]["data"])
                    for module in data_dict:
                        return_msg.append(str(module))        
                else:
                    logging.error("message  subconfigs data fail")
        else:
            logging.error("result no message")
            return 1, []
    else:
        logging.error("result is %s", json.dumps(result))
        return 1, []
    
    return 0, return_msg

def needHook(module):
    """needHook"""

    url = "https://ad-env.corp.kuaishou.com/diffreport/kconfSwitchGet?key=adqa.adForYQ.CommitHook&stage=testing&snapshotId=0"
    
    user_url = "https://ad-env.corp.kuaishou.com/diffreport/kconfSwitchGet?key=adqa.adForYQ.CommitHookUsers&stage=testing&snapshotId=0"

    status, modules = getkconfMessage(url)

    if status != 0:
        return "False"

    if  modules:
        for one in modules: 
            if one in module:
                u_status, users = getkconfMessage(user_url)
                user = getUser()
                if u_status != 0:
                    return "False"
                if users:
                    for one_user in users: 
                        if one_user in user:
                            return "True"
                    return "False"
                else:
                    return "True"
        return "False"

    return "True"


if __name__ == '__main__':

    parser = argparse.ArgumentParser(usage="Command: -h/--help for help")
    parser.add_argument("-m", "--module", help="module", default="front")
    args = parser.parse_args()

    res = needHook(args.module)
    print res
