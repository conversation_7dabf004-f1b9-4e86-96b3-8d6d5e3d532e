#include <csignal>
#include <iostream>
#include <atomic>
#include <string>

#include "base/common/logging.h"
#include "teams/ad/ad_base/src/ksp/ad_web_request_handler.h"
#include "teams/ad/ad_base/src/ksp/dynamic_port.h"
#include "ks/serving_util/server_base.h"
#include "ks/serving_util/server_status.h"
#include "gflags/gflags.h"
#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "perfutil/perfutil.h"
#include "third_party/brpc-0.9.6/src/brpc/server.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_creative_server/warm_up.h"
#include "teams/ad/ad_creative_server/bg_task.h"
#include "teams/ad/ad_creative_server/task/task_manager.h"
#include "teams/ad/ad_creative_server/service/ad_creative_service.h"

DEFINE_int32(web_thread_num, 4, "server http thread_num");
DEFINE_int32(web_server_port, 10080, "server http port");

int main(int argc, char *argv[]) {
  base::InitApp(&argc, &argv, "creative server");
  // http
  ks::ad_base::DefaultAdWebService web_service(FLAGS_web_thread_num);
  net::WebServer::Options web_server_option;
  web_server_option.port = ks::ad_base::DynamicPort::Instance().Port("AUTO_PORT0");
  web_server_option.backlog = 1024;
  if (web_server_option.port <= 0) {
    web_server_option.port = ks::DynamicJsonConfig::GetConfig()->GetInt(
      "web_server_port", FLAGS_web_server_port);
    LOG(INFO) << "may be not running in kcs mode! can't get port AUTO_PORT0 "
      << "use dynamic_json port or gflag " << web_server_option.port;
  }
  net::WebServer web_server(web_server_option, &web_service);
  web_server.Start();
  LOG(INFO) << "web server started port " << web_server_option.port;

  base::ServerStatus::Singleton()->SetStatusCode(base::ServerStatusCode::STARTING);

  const auto& stage = ks::infra::KEnv::GetKWSInfo()->GetServiceStage();
  if (stage == "PREONLINE") {
    ks::infra::kenv::ServiceMeta::SetGroup("pre-gray20");
  }

  ks::creative_server::warm_up();
  // brpc::StartDummyServerAt(8888/*port*/);  // heap profile
  ks::creative_server::StartBgTask();
  LOG(INFO) << "BgTask started!";
  ::google::FlushLogFiles(::google::INFO);
  ks::creative_server::TaskManager::GetInstance()->Start();
  LOG(INFO) << "TaskManager started!";
  ::google::FlushLogFiles(::google::INFO);

  auto service = new ks::creative_server::AdCreativeServiceImpl;
  ks::ad_base::AdKessClient &kess_instance = ks::ad_base::AdKessClient::Instance();
  kess_instance.StartService(service, web_server_option.port);

  ks::serving_util::ServerStatus::Singleton()->SetStatusCode(ks::serving_util::ServerStatusCode::RUNNING);

  //等待停止的信号
  kess_instance.StopService();
  // ks::ServerBase::Singleton()->WaitForSignal();
  base::ServerStatus::Singleton()->SetStatusCode(base::ServerStatusCode::STOPPING);

  // 停止 WEB 服务
  web_server.Stop();
  LOG(INFO) << "web server stop!";
  ks::creative_server::TaskManager::GetInstance()->Stop();
  LOG(INFO) << "task stop!";
  std::this_thread::sleep_for(std::chrono::seconds(5));     // 等待 5s 钟再调用 kill -9
  std::raise(9);

  LOG(INFO) << "Stopping BgTask...";
  ::google::FlushLogFiles(::google::INFO);
  ks::creative_server::StopBgTask();
  LOG(INFO) << "BgTask stopped!";
  ::google::FlushLogFiles(::google::INFO);

  //  更新服务器状态
  ks::serving_util::ServerStatus::Singleton()->SetStatusCode(ks::serving_util::ServerStatusCode::STOPPED);
  LOG(INFO) << "creative stop!";
  ::google::FlushLogFiles(::google::INFO);
  return 0;
}
