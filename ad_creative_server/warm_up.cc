#include "teams/ad/ad_creative_server/warm_up.h"

#include "base/common/logging.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/container/singleton.h"
#include "teams/ad/ad_creative_server/util/deploy_util/deploy_state.h"
#include "teams/ad/ad_creative_server/biz/biz.h"

using ks::ad_base::Singleton;

namespace ks {
namespace creative_server {

void warm_up() {
  LOG(INFO) << "start to warm up";
  falcon::SetMaxCounterNumber(40960);

  AD_KESS_CLIENT_INIT_CHECK("init error");

  // 启动
  LOG(INFO) << "warm up finished";
  ::google::FlushLogFiles(::google::INFO);
}

}  // namespace creative_server
}  // namespace ks
