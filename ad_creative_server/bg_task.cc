#include "teams/ad/ad_creative_server/bg_task.h"

#include <thread>
#include <string>

#include "base/common/sleep.h"
#include "base/time/timestamp.h"
#include "teams/ad/ad_creative_server/common/glags.h"
#include "teams/ad/ad_creative_server/util/deploy_util/deploy_state.h"
#include "teams/ad/ad_creative_server/util/kconf/kconf.h"
#include "teams/ad/ad_creative_server/util/creative_status_view/creative_status_view.h"
#include "teams/ad/ad_creative_server/util/bg_task/all_bg_task.h"
#include "teams/ad/ad_creative_server/biz/biz.h"
#include "teams/ad/ad_creative_server/util/unit_bid_lift_log.h"
#include "teams/ad/ad_creative_server/util/creative_score_snapshot/creative_score_snapshot.h"
#include "teams/ad/ad_creative_server/util/perf_log_util/perf_log_util.h"
#include "teams/ad/ad_creative_server/util/bg_task/innerloop_photo_creative_data/innerloop_photo_creative_data.h"
#include "teams/ad/ad_creative_server/util/bg_task/innerloop_dup_photo_data/innerloop_dup_photo_data.h"
#include "teams/ad/ad_creative_server/util/bg_task/universe_creative_cost/universe_creative_cost.h"
#include "teams/ad/ad_creative_server/util/bg_task/innerloop_creative_cost_data_realtime/innerloop_creative_cost_data_realtime.h"
#include "teams/ad/ad_creative_server/util/bg_task/innerloop_creative_cost/innerloop_creative_cost.h"
#include "teams/ad/ad_creative_server/util/bg_task/innerloop_dead_unit_data/innerloop_dead_unit_data.h"
#include "teams/ad/ad_creative_server/util/bg_task/unit_budget_data/unit_no_budget_hours.h"
#include "teams/ad/ad_creative_server/util/bg_task/universe_unit_budget_data/universe_unit_no_budget_hours.h"
#include "teams/ad/ad_creative_server/util/bg_task/ecom_plagiarism_cid_set/ecom_plagiarism_cid_set.h"
#include "teams/ad/ad_creative_server/util/bg_task/new_spu/new_spu_release_time.h"
#include "teams/ad/ad_creative_server/util/bg_task/new_spu/new_spu_corp.h"
#include "teams/ad/ad_creative_server/util/bg_task/photo_item_spu/photo_item_spu.h"
#include "teams/ad/ad_creative_server/util/bg_task/dpp_photo_cost_cpm/dpp_photo_cost_cpm.h"
#include "teams/ad/engine_base/cache_loader/new_cost_account.h"
#include "teams/ad/ad_creative_server/util/bg_task/universe_unit_skip_balcklist/universe_unit_skip_balcklist.h"
#include "teams/ad/ad_creative_server/util/bg_task/full_multi_ad_counter_data/full_multi_ad_counter_data.h"
#include "teams/ad/ad_creative_server/util/bg_task/creative_posterior_universe/creative_posterior_universe.h"
#include "teams/ad/ad_creative_server/util/bg_task/creative_funnel_data_universe/creative_funnel_universe.h"
#include "teams/ad/ad_creative_server/util/stats_collector/select_stats_collector.h"
#include "teams/ad/engine_base/search/cache_loader/search_multi_counter.h"

DEFINE_bool(enable_new_tag_from_ms, false, "new creative tag switch");

namespace ks {
namespace creative_server {

void StartBgTask() {
  FLAGS_enable_new_tag_from_ms = PCKconfUtil::enableGetNewCreativeTagFromMarkServer();
  int64 ts = base::GetTimestamp();
  // 不需要 wait for data ready
  START_TASK_NEW(SelectStatsCollector)
  START_TASK(UnitBidLiftLog);
  // 正常启动且需要 wait for data ready
  START_TASK(CreativeScoreSnapshot);
  START_TASK(ForwardIndex);
  START_TASK(AccountValidBudget);
  START_TASK(PhotoSimplePost);
  START_TASK(CreativePostData);
  START_TASK(CreativeFunnelData);
  START_TASK(RtbGrayWhiteList);
  START_TASK(PhotoQualityScore);
  START_TASK(PhotoItemSpu);
  START_TASK(ks::ad_target_search::SearchMultiCounter)
  // 区分部署的任务
  if (BizUtil::GetInstance()->IsDefaultBiz()) {
    START_TASK(InnerloopPhoto2liveCreativeData);
    START_TASK(InnerloopPhoto2liveCreativeDataV2);
    START_TASK(InnerloopDupPhotoData);
    START_TASK(JinniuDeadUnitData);
    START_TASK(InnerloopCreativeRealTimeCost);
    START_TASK(NewSpuReleaseTime);
    START_TASK(NewSpuCorp);
    if (PCKconfUtil::EnableInnerloopCreativeCost()) {
      START_TASK(InnerloopCreativeCost);
    }
    if (PCKconfUtil::enableEcomPlagiarismCidSet()) {
      START_TASK(EcomPlagiarismCidSet);
    }
  } else if (BizUtil::GetInstance()->IsExternalCirculationUnitBiz()) {
    START_TASK(UnitNoBudgetHour);
  } else if (BizUtil::GetInstance()->IsUniverseDefaultBiz()) {
    if (PCKconfUtil::EnableInnerloopCreativeCost()) {
      START_TASK(InnerloopCreativeCost);
    }
    if (PCKconfUtil::enableUniverseCreativeCost()) {
      START_TASK(UniverseCreativeCost);
    }
    ks::creative_server::GetUniverseOperationDarkBlockContainer()->Start();
    START_TASK(ks::engine_base::NewCostAccount);
    START_TASK(CreativePostDataUniverse);
    START_TASK(CreativeFunnelDataUniverse);
    START_TASK(UniverseUnitNoBudgetHour);
  } else if (BizUtil::GetInstance()->IsSearchBiz()) {
    START_TASK(FullMultiAdCounterData);
  }

  if (PCKconfUtil::enablePhotoHistoryDataLoader()) {
    START_TASK(PhotoHistoryDataLoader);
  }


  WAIT_TASK(PhotoQualityScore);
  WAIT_TASK(CreativePostData);
  WAIT_TASK(CreativeFunnelData);
  WAIT_TASK(PhotoSimplePost);
  WAIT_TASK(AccountValidBudget);
  WAIT_TASK(RtbGrayWhiteList);
  WAIT_TASK(ForwardIndex);
  WAIT_TASK(CreativeScoreSnapshot);

  if (PCKconfUtil::enablePhotoHistoryDataLoader()) {
    WAIT_TASK(PhotoHistoryDataLoader);
  }


  WAIT_TASK(PhotoItemSpu);
  WAIT_TASK(ks::ad_target_search::SearchMultiCounter)

  // 区分部署的任务
  if (BizUtil::GetInstance()->IsDefaultBiz()) {
    WAIT_TASK(InnerloopPhoto2liveCreativeData);
    WAIT_TASK(InnerloopPhoto2liveCreativeDataV2);
    WAIT_TASK(InnerloopDupPhotoData);
    WAIT_TASK(JinniuDeadUnitData);
    WAIT_TASK(InnerloopCreativeRealTimeCost);
    WAIT_TASK(NewSpuReleaseTime);
    WAIT_TASK(NewSpuCorp);
  } else if (BizUtil::GetInstance()->IsExternalCirculationUnitBiz()) {
    WAIT_TASK(UnitNoBudgetHour);
  } else if (BizUtil::GetInstance()->IsUniverseDefaultBiz()) {
    if (PCKconfUtil::EnableInnerloopCreativeCost()) {
      WAIT_TASK(InnerloopCreativeCost);
    }
    if (PCKconfUtil::enableUniverseCreativeCost()) {
      WAIT_TASK(UniverseCreativeCost);
    }
    while (!ks::creative_server::GetUniverseOperationDarkBlockContainer()->IsReady()) {
     base::SleepForMilliseconds(100);
    }
    WAIT_TASK(CreativePostDataUniverse);
    WAIT_TASK(CreativeFunnelDataUniverse);
    WAIT_TASK(ks::engine_base::NewCostAccount);
    WAIT_TASK(UniverseUnitNoBudgetHour);
  } else if (BizUtil::GetInstance()->IsSearchBiz()) {
    WAIT_TASK(FullMultiAdCounterData);
  }

  int64 ts_diff = base::GetTimestamp() - ts;
  LOG(INFO) << "Start bg task Complete, time cost: " << ts_diff / (1000 * 1000) << "s.";
  Dotter::GetInstance()->Interval(ts_diff, "bg_task_start_time");
  ::google::FlushLogFiles(::google::INFO);
}

void StopBgTask() {
  // 运行中不依赖其他 bg task
  SelectStatsCollector::Instance().Stop();
  UnitBidLiftLog::GetInstance()->Stop();
  CreativePostData::GetInstance()->Stop();
  CreativeFunnelData::GetInstance()->Stop();
  PhotoSimplePost::GetInstance()->Stop();
  AccountValidBudget::GetInstance()->Stop();
  RtbGrayWhiteList::GetInstance()->Stop();
  PhotoQualityScore::GetInstance()->Stop();
  if (PCKconfUtil::enablePhotoHistoryDataLoader()) {
    PhotoHistoryDataLoader::GetInstance()->Stop();
  }

  PhotoItemSpu::GetInstance()->Stop();
  ks::ad_target_search::SearchMultiCounter::GetInstance()->Stop();
  // 区分部署的任务
  if (BizUtil::GetInstance()->IsDefaultBiz()) {
    InnerloopPhoto2liveCreativeData::GetInstance()->Stop();
    InnerloopPhoto2liveCreativeDataV2::GetInstance()->Stop();
    InnerloopDupPhotoData::GetInstance()->Stop();
    JinniuDeadUnitData::GetInstance()->Stop();
    InnerloopCreativeRealTimeCost::GetInstance()->Stop();
    NewSpuReleaseTime::GetInstance()->Stop();
    NewSpuCorp::GetInstance()->Stop();
    if (PCKconfUtil::EnableInnerloopCreativeCost()) {
      STOP_TASK(InnerloopCreativeCost);
    }
    if (PCKconfUtil::enableEcomPlagiarismCidSet()) {
      STOP_TASK(EcomPlagiarismCidSet);
    }
  } else if (BizUtil::GetInstance()->IsExternalCirculationUnitBiz()) {
    if (UnitNoBudgetHour::GetInstance() != nullptr) {
      UnitNoBudgetHour::GetInstance()->Stop();
    }
  } else if (BizUtil::GetInstance()->IsUniverseDefaultBiz()) {
    if (PCKconfUtil::EnableInnerloopCreativeCost()) {
      STOP_TASK(InnerloopCreativeCost);
    }
    if (PCKconfUtil::enableUniverseCreativeCost()) {
      STOP_TASK(UniverseCreativeCost);
    }
    CreativePostDataUniverse::GetInstance()->Stop();
    CreativeFunnelDataUniverse::GetInstance()->Stop();
    ks::creative_server::GetUniverseOperationDarkBlockContainer()->Stop();
    STOP_TASK(ks::engine_base::NewCostAccount);
    STOP_TASK(UniverseUnitNoBudgetHour);
  } else if (BizUtil::GetInstance()->IsSearchBiz()) {
    STOP_TASK(FullMultiAdCounterData);
  }
  ForwardIndex::GetInstance()->Stop();
  CreativeScoreSnapshot::GetInstance()->Stop();
}

}  // namespace creative_server
}  // namespace ks
