proto_library(
    name = "ad_creative_service_proto",
    srcs = [
      "proto/*.proto"
    ],
    deps = [
      "//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_base",
      "//teams/ad/ad_proto/kuaishou/BUILD:all_table_proto",
    ],
    enable_kess = True,
    enable_grpc = True,
    use_grpc_ver = "v1100",
)

proto_library(
  name = "creative_score_proto",
  srcs = [
    "./proto/creative_score.proto",
  ],
  deps = [
    "//teams/ad/ad_proto/kuaishou/BUILD:all_table_proto",
  ],
)

proto_library(
  name = "audit_post_back_photo_data_proto",
  srcs = [
    "./proto/audit_post_back_photo_data.proto",
  ],
  deps = [
  ],
)

proto_library(
    name = "kconf_data_proto",
    srcs = [
      "./util/kconf/*.proto"
    ],
)

cc_library(
    name = "biz",
    srcs = [
        "biz/*.cc",
    ],
    deps = [
      ':creative_score_proto',
      "//base/common/BUILD:base",
      "//third_party/abseil/BUILD:abseil",
      "//infra/redis_proxy_client/BUILD:redis_client",
    ],
)

cc_library(
    name = "creative_posterior_universe_scene",
    srcs = ["./util/bg_task/creative_posterior_universe/*.cc"],
    deps = [
        "//base/common/BUILD:base",
        "//base/time/BUILD:time",
        "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    ]
)

cc_library(
    name = "creative_funnel_universe",
    srcs = ["./util/bg_task/creative_funnel_data_universe/*.cc"],
    deps = [
        "//base/common/BUILD:base",
        "//base/time/BUILD:time",
        "//third_party/abseil/BUILD:abseil",
        "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    ]
)

cc_library(
    name = 'kconf',
    srcs = [
        "./util/kconf/*.cc",
    ],
    deps = [
        ':kconf_data_proto',
        "//teams/ad/ad_base/src/kconf/BUILD:kconf_node",
        "//third_party/rapidjson/BUILD:rapidjson"
    ],
)

cc_library(
    name = "white_list_manager",
    srcs = ["./util/white_list_manager/*.cc"],
    deps = [
      ":kconf",
      "//base/common/BUILD:base",
      "//base/time/BUILD:time",
      "//base/thread/BUILD:thread",
      "//third_party/abseil/BUILD:abseil",
    ]
)

cc_library(
    name = 'kafka',
    srcs = ["./util/kafka_util/*.cc"],
    deps = [
        ':redis_util',
        "//base/time/BUILD:time",
        "//teams/ad/ad_base/src/kafka/BUILD:kafka",
        "//third_party/abseil/BUILD:abseil",
    ]
)

cc_library(
    name = 'deploy',
    srcs = ["./util/deploy_util/*.cc"],
    deps = [

    ]
)

cc_library(
    name = "file_util",
    srcs = [
        "./util/file_util/*.cc",
    ],
    deps = [
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_base",
        "//serving_base/hdfs_read/BUILD:hdfs_read",
        "//base/time/BUILD:time",
        "//base/strings/BUILD:strings",
        "//base/common/BUILD:base",
        "//base/file/BUILD:file",
        "//infra/redis_proxy_client/BUILD:redis_client",
        "//infra/perfutil/BUILD:perfutil",
    ]
)

cc_library(
    name = "redis_util",
    srcs = [
        "./util/redis_util/*.cc",
    ],
    deps = [
        ':kconf',
        "//infra/redis_proxy_client/BUILD:redis_client",
        "//base/strings/BUILD:strings",
    ]
)

cc_library(
    name = "bid_lift_log",
    srcs = [
        "util/unit_bid_lift_log.cc",
    ],
    deps = [
        "//base/common/BUILD:base",
        "//base/file/BUILD:file",
        "//third_party/libcuckoo/BUILD:cuckoo",
    ]
)

cc_library(
  name = "global_thread_pool",
  srcs = [
    "./util/global_thread_pool/*.cc",
  ],
  deps = [
    ':kconf',
    "//base/thread/BUILD:thread",
  ],
)

cc_library(
  name = "json_util",
  srcs = [
    "./util/json_util/*.cc",
  ],
  deps = [
    ':kconf',
    "//serving_base/jansson/BUILD:json",
  ]
)

cc_library(
  name = "creative_score_snapshot",
  srcs = [
    "./util/creative_score_snapshot/*.cc",
  ],
  deps = [
    ':kconf',
    ':global_thread_pool',
    ':biz',
    ':deploy',
    "//base/common/BUILD:base",
    "//base/encoding/BUILD:encoding",
    "//base/file/BUILD:file",
    "//third_party/abseil/BUILD:abseil",
    "//infra/perfutil/BUILD:perfutil",
    "//serving_base/hdfs_read/BUILD:hdfs_read",
    "//teams/ad/engine_base/BUILD:material_feature_type",
  ]
)

cc_library(
  name = "account_valid_budget",
  srcs = ["./util/bg_task/account_valid_budget/*.cc"],
  deps = [
    "//base/common/BUILD:base",
    "//third_party/abseil/BUILD:abseil",
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
  ]
)

cc_library(
  name = "splash_rtb_gray",
  srcs = ["./util/bg_task/splash_rtb_gray/*.cc"],
  deps = [
    "//base/common/BUILD:base",
    "//third_party/abseil/BUILD:abseil",
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    "//third_party/nlohmann_json/BUILD:nlohmann_json"
  ]
)

cc_library(
    name = "creative_posterior_all_scene",
    srcs = [
      "./util/bg_task/creative_posterior/*.cc",
      "./util/bg_task/innerloop_creative_cost/*.cc",
      "./util/bg_task/innerloop_photo_creative_data/*.cc",
      "./util/bg_task/innerloop_dup_photo_data/*.cc",
      "./util/bg_task/innerloop_creative_cost_data_realtime/*.cc",
      "./util/bg_task/innerloop_dead_unit_data/*.cc",
      "./util/bg_task/ecom_plagiarism_cid_set/*.cc",
      ],
    deps = [
        "//base/common/BUILD:base",
        "//base/time/BUILD:time",
        "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    ]
)

cc_library(
  name = 'universe_creative_cost',
  srcs = [
    "./util/bg_task/universe_creative_cost/*.cc"
  ],
  deps = [
    "//base/common/BUILD:base",
    "//third_party/abseil/BUILD:abseil",
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
  ],
)

cc_library(
  name = 'universe_unit_skip_balcklist',
  srcs = [
    "./util/bg_task/universe_unit_skip_balcklist/*.cc"
  ],
  deps = [
    "//base/common/BUILD:base",
    "//third_party/abseil/BUILD:abseil",
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
  ],
)

cc_library(
    name = "photo_statistics",
    srcs = ["./util/bg_task/photo_statistic/*.cc"],
    deps = [
        ':kconf',
        ':file_util',
        "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
        "//base/common/BUILD:base",
        "//third_party/abseil/BUILD:abseil",
    ]
)

cc_library(
    name = "forward_index",
    srcs = ["./util/bg_task/forward_index/*.cc"],
    deps = [
        ':kconf',
        ':deploy',
        ':global_thread_pool',
        ':white_list_manager',
        "//base/common/BUILD:base",
        "//base/time/BUILD:time",
        "//infra/perfutil/BUILD:perfutil",
        "//third_party/abseil/BUILD:abseil",
        "//teams/ad/ad_table_lite/BUILD:ad_table_lite",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_forward_index_proto",
    ]
)

cc_library(
    name = "creative_status_view",
    srcs = ["./util/creative_status_view/*.cc"],
    deps = [
        "//base/file/BUILD:file",
        "//infra/perfutil/BUILD:perfutil",
        "//third_party/abseil/BUILD:abseil",
        "//third_party/libcuckoo/BUILD:cuckoo",
    ]
)

cc_library(
  name = "split_test_data",
  srcs = ["./util/bg_task/split_test_data/*.cc"],
  deps = [
    "//base/common/BUILD:base",
    "//third_party/abseil/BUILD:abseil",
    "//teams/ad/data_push/BUILD:kconf",
    "//infra/perfutil/BUILD:perfutil",
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_hosting_campaign_info_proto"
  ]
)

cc_library(
    name = "creative_funnel",
    srcs = ["./util/bg_task/creative_funnel_data/*.cc"],
    deps = [
        "//base/common/BUILD:base",
        "//base/time/BUILD:time",
        "//third_party/abseil/BUILD:abseil",
        "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    ]
)

cc_library(
  name = "photo_quality_score",
  srcs = ["./util/bg_task/photo_quality_score/*.cc"],
  deps = [
    "//base/common/BUILD:base",
    "//base/time/BUILD:time",
    "//third_party/abseil/BUILD:abseil",
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
  ]
)

cc_library(
  name = "unit_budget_data",
  srcs = ["./util/bg_task/unit_budget_data/*.cc"],
  deps = [
    "//base/common/BUILD:base",
    "//base/time/BUILD:time",
    "//third_party/abseil/BUILD:abseil",
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
  ]
)

cc_library(
  name = "photo_history_data",
  srcs = ["./util/bg_task/photo_history_data/*.cc"],
  deps = [
    "//base/common/BUILD:base",
    "//base/time/BUILD:time",
    "//third_party/abseil/BUILD:abseil",
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
  ]
)

cc_library(
  name = "new_spu",
  srcs = ["./util/bg_task/new_spu/*.cc"],
  deps = [
    "//base/common/BUILD:base",
    "//base/time/BUILD:time",
    "//third_party/abseil/BUILD:abseil",
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
  ]
)

cc_library(
  name = "account_cost",
  srcs = ["./util/bg_task/account_cost/*.cc"],
  deps = [
    "//base/common/BUILD:base",
    "//base/time/BUILD:time",
    "//third_party/abseil/BUILD:abseil",
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
  ]
)

cc_library(
  name = "product_agent_cost",
  srcs = ["./util/bg_task/product_agent_cost/*.cc"],
  deps = [
    "//base/common/BUILD:base",
    "//base/time/BUILD:time",
    "//third_party/abseil/BUILD:abseil",
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
  ]
)

cc_library(
  name = 'dpp_photo_cost_cpm',
  srcs = [
    "./util/bg_task/dpp_photo_cost_cpm/*.cc"
  ],
  deps = [
    "//third_party/abseil/BUILD:abseil",
    "//third_party/libcuckoo/BUILD:cuckoo",
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
  ],
)

cc_library(
  name = 'photo_item_spu',
  srcs = [
    "./util/bg_task/photo_item_spu/*.cc"
  ],
  deps = [
    "//third_party/abseil/BUILD:abseil",
    "//third_party/libcuckoo/BUILD:cuckoo",
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
  ],
)

cc_library(
  name = "full_ad_multi_counter",
  srcs = ["./util/bg_task/full_multi_ad_counter_data/*.cc"],
  deps = [
    "//base/common/BUILD:base",
    "//base/time/BUILD:time",
    "//third_party/abseil/BUILD:abseil",
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_search_ads_search_ad_counter__proto",
  ]
)

cc_library(
    name = "helper",
    srcs = [
        "./util/helper/*.cc",
    ],
    deps = [
        ':kconf',
        ':creative_status_view',
        ':context_data',
        ':kafka',
        ':ad_creative_service_proto',
        ':creative_score_snapshot',
        "//base/common/BUILD:base",
        "//base/time/BUILD:time",
        "//base/file/BUILD:file",
        "//teams/ad/ad_proto/kuaishou/BUILD:all_table_proto",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_base",
        "//teams/ad/ad_proto/kuaishou/BUILD:enums_proto",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_inc_proto",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_cache__proto",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_creative_server__proto",
        "//infra/redis_proxy_client/BUILD:redis_client",
        "//third_party/nlohmann_json/BUILD:nlohmann_json",
        "//third_party/folly/BUILD:folly",
    ]
)

cc_library(
    name = 'context_data',
    srcs = ["./context_data/*.cc"],
    deps = [
        ':kconf',
        ':biz',
        ':splash_rtb_gray',
        "//serving_base/utility/BUILD:time_helper",
        "//base/common/BUILD:base",
        "//base/strings/BUILD:strings",
        "//third_party/abseil/BUILD:abseil",
        "//infra/perfutil/BUILD:perfutil",
        "//teams/ad/ad_proto/kuaishou/BUILD:all_table_proto",
    ],
)

cc_library(
    name = 'strategy',
    srcs = [
        "./strategy/*.cc",
    ],
    deps = [
        ':kconf',
        ':context_data',
        ':kafka',
        ':forward_index',
        ':cropper',
        ':opt_pool',
        "//base/common/BUILD:base",
        "//third_party/abseil/BUILD:abseil",
        "//teams/ad/ad_proto/kuaishou/BUILD:all_table_proto",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_base",
    ]
)

cc_library(
  name = "cropper",
  srcs = [
    "./src/crop/*.cc",
    "./src/crop/filter/*.cc",
    "./src/crop/cropper/*.cc",
    "./src/crop/cropper/search/*.cc",
    "./src/crop/cropper/universe/*.cc",
    "./src/crop/cropper/innerloop/*.cc"
  ],
  deps = [
    ':kconf',
    ':context_data',
    ':forward_index',
    "//base/common/BUILD:base",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_base",
  ]
)

cc_library(
  name = "new_scorer",
  srcs = [
    "./src/scorer/*.cc",
    "./src/scorer/scorer/*.cc",
    "./src/scorer/filter/*.cc",
  ],
  deps = [
    ':kconf',
    ':context_data',
    ':forward_index',
    "//teams/ad/ad_base/src/kafka/BUILD:kafka",
    "//teams/ad/engine_base/BUILD:search_cache_loader",
    "//serving_base/jansson/BUILD:json",
    "//third_party/nlohmann_json/BUILD:nlohmann_json",
    "//third_party/abseil/BUILD:abseil",
  ],
)

cc_library(
    name = "universe_unit_no_budget_hours",
    srcs = ["./util/bg_task/universe_unit_budget_data/*.cc"],
    deps = [
        "//base/common/BUILD:base",
        "//base/time/BUILD:time",
        "//third_party/abseil/BUILD:abseil",
        "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    ]
)

cc_library(
  name = "opt_pool",
  srcs = [
    "./src/pool/*.cc",
    "./src/pool/opt_pool/*.cc",
    "./src/pool/universe_opt_pool/*.cc",
    "./src/pool/external_opt_pool/*.cc",
    "./src/pool/internal_opt_pool/*.cc",
    "./src/pool/search_opt_pool/*.cc",
  ],
  deps = [
    ':kconf',
    ':context_data',
    ':new_scorer',
    ':forward_index',
    ':split_test_data',
    "//base/common/BUILD:base",
    "//third_party/abseil/BUILD:abseil",
  ],
)

cc_library(
    name = 'task',
    srcs = [
      "./task/*.cc"
    ],
    deps = [
        ':kconf',
        ':context_data',
        ':kafka',
        ':redis_util',
        ':file_util',
        ':strategy',
        ':creative_status_view',
        ':creative_funnel',
        ':creative_funnel_universe',
        ':ad_creative_service_proto',
        ':biz',
        ':helper',
        ':universe_unit_no_budget_hours',
        ':global_thread_pool',
        ':creative_score_snapshot',
        ':account_valid_budget',
        ':universe_creative_cost',
        ':universe_unit_skip_balcklist',
        ':stats_collector',
        ':white_list_manager',
        "//base/common/BUILD:base",
        "//base/thread/BUILD:thread",
        "//base/time/BUILD:time",
        "//base/file/BUILD:file",
        "//third_party/abseil/BUILD:abseil",
        "//teams/ad/ad_proto/kuaishou/BUILD:all_table_proto",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_base",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_ems_hosting_label_info__proto",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_dsp_batch_refresh",
        "//teams/ad/ad_proto/kuaishou/BUILD:enums_proto",
        "//teams/ad/ad_base/src/redis/BUILD:ad_kconf_redis",
        "//teams/ad/grid/BUILD:grid_sdk",
        "//infra/redis_proxy_client/BUILD:redis_client",
        "//infra/perfutil/BUILD:perfutil",
        "//serving_base/jansson/BUILD:json",
        "//third_party/nlohmann_json/BUILD:nlohmann_json",
        "//teams/ad/engine_base/BUILD:material_feature_type",
    ]
)

cc_library(
  name = "stats_collector",
  srcs = [
    "./util/stats_collector/*.cc"
  ],
  deps = [
    ':kconf',
    ':biz',
    "//base/common/BUILD:base",
    "//base/thread/BUILD:thread",
    "//third_party/abseil/BUILD:abseil",
    "//infra/perfutil/BUILD:perfutil",
    "//infra/redis_proxy_client/BUILD:redis_client",
    "//teams/ad/ad_base/src/redis/BUILD:ad_kconf_redis",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_creative_server_stats__proto",
  ]
)

cc_library(
    name = 'creative_service',
    srcs = [
        "warm_up.cc",
        "service/*.cc",
    ],
    deps = [
        ":deploy",
        ':kconf',
        ":ad_creative_service_proto",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_base",
        "//teams/ad/ad_proto/kuaishou/BUILD:algo_rank",
        "//base/common/BUILD:base",
        "//teams/ad/ad_base/src/kess/BUILD:kess_client",
        "//infra/redis_proxy_client/BUILD:redis_client",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_base",
        "//teams/ad/ad_table_lite/BUILD:ad_table_lite",
    ]
)

cc_binary(
    name = 'creative_server',
    srcs = ["*.cc"],
    deps = [
        ':task',
        ':creative_service',
        ':photo_statistics',
        ':forward_index',
        ':creative_posterior_all_scene',
        ':creative_posterior_universe_scene',
        ':creative_funnel',
        ':bid_lift_log',
        ':creative_score_snapshot',
        ':account_valid_budget',
        ':split_test_data',
        ':splash_rtb_gray',
        ':photo_quality_score',
        ':unit_budget_data',
        ':photo_history_data',
        ':dpp_photo_cost_cpm',
        ':photo_item_spu',
        ':full_ad_multi_counter',
        ':white_list_manager',
        '//teams/ad/ad_base/src/ksp/BUILD:ksp',
        "//infra/perfutil/BUILD:perfutil",
        "//teams/ad/engine_base/BUILD:cache_loader",
        "//third_party/brpc-0.9.6/BUILD:brpc",
        "//teams/ad/ad_counter/BUILD:api",
    ],
    cppflags = [
        '-fopenmp',
        '-Ithird_party/abseil/',
        '-Ithird_party/glog/',
        '-Iinfra/redis_proxy_client/src',
        '-I.build/pb/c++/teams/ad/ad_proto/kuaishou/ad',
        "-Ithird_party/libev/",
    ],
    ldflags = [
        '-fopenmp',
    ]
)

