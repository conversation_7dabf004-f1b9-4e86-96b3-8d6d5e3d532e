#pragma once

namespace ks {
namespace creative_server {

#define START_TASK(name) \
  name::GetInstance()->Start();

#define WAIT_TASK(name)                                     \
  while (!name::GetInstance()->IsDataReady()) {             \
    std::this_thread::sleep_for(std::chrono::seconds(1));   \
  }                                                         \
  LOG(INFO) << #name << " isReady";                        \

#define STOP_TASK(name) \
  name::GetInstance()->Stop();

#define START_TASK_NEW(name) \
  name::Instance().Start();

#define WAIT_TASK_NEW(name)                                     \
  while (!name::Instance().IsDataReady()) {             \
    std::this_thread::sleep_for(std::chrono::seconds(1));   \
  }                                                         \
  LOG(INFO) << #name << " isReady";                        \

#define STOP_TASK_NEW(name) \
  name::Instance().Stop();


void StartBgTask();

void StopBgTask();

}  // namespace creative_server
}  // namespace ks
