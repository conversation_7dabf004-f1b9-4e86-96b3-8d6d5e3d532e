#include "teams/ad/ad_creative_server/util/bg_task/product_agent_cost/product_agent_cost.h"

namespace ks {
namespace creative_server {

int64 ProductAgentCost::GetCost(const std::string& key) {
  auto ptr = GetData();
  if (!ptr || !ptr.get()) { return 0; }
  auto iter = ptr->find(key);
  if (iter != ptr->end()) {
    return iter->second;
  }
  return 0;
}

}  //  namespace creative_server
}  //  namespace ks
