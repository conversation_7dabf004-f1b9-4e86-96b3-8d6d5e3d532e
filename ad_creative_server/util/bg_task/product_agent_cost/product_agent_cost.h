#pragma once

#include <unordered_map>
#include <string>
#include <memory>
#include <vector>

#include "base/common/basic_types.h"
#include "absl/container/flat_hash_map.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"

namespace ks {
namespace creative_server {

using ProductAgentCostTp = absl::flat_hash_map<std::string, int64_t>;

class ProductAgentCost : public ad_base::P2pCacheLoader<ProductAgentCostTp,
                                                   ad_base::DeserializeFileEnum::Basic> {
 public:
  static ProductAgentCost* GetInstance() {
    static ProductAgentCost instance;
    return &instance;
  }

  int64 GetCost(const std::string& key);

 private:
  ProductAgentCost() : ad_base::P2pCacheLoader<ProductAgentCostTp, ad_base::DeserializeFileEnum::Basic>(
            10 * 60 * 1000, "creative_server_product_agent_cost") {}
  DISALLOW_COPY_AND_ASSIGN(ProductAgentCost);
};

}  // namespace creative_server
}  // namespace ks
