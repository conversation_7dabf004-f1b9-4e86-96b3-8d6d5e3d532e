#pragma once
#include <functional>
#include <memory>
#include <sstream>
#include <string>
#include <vector>
#include <utility>


#include "libcuckoo/cuckoohash_map.hh"
#include "absl/container/flat_hash_map.h"
#include "absl/container/flat_hash_set.h"
#include "base/thread/thread_pool.h"
#include "teams/ad/ad_base/src/hash/hash.h"
#include "teams/ad/ad_base/src/common/condition_time_wait.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader_data.pb.h"

namespace ks {
namespace creative_server {

class PhotoItemSpuData {
 public:
  using StructData = absl::flat_hash_set<int64_t>;
  using ProtoData = ad_base::CommonTupleStruct;
  using Container = absl::flat_hash_map<int64_t, StructData>;
  static bool line_to_proto(const std::string& line,
                            std::pair<int64_t, std::unique_ptr<google::protobuf::Message>>* pb) {
    if (!pb) {
      return false;
    }
    std::vector<absl::string_view> tokens = absl::StrSplit(line, "\t", absl::SkipEmpty());
    if (tokens.size() != 2) {
      return false;
    }
    int64_t photo_id = 0;
    if (!absl::SimpleAtoi(tokens[0], &photo_id)) {
      return false;
    }
    std::string item_list = std::string(tokens[1]);
    if (item_list.empty()) {
      return false;
    }
    pb->first = photo_id;
    std::unique_ptr<ProtoData> proto = std::make_unique<ProtoData>();
    pb->second = std::make_unique<ProtoData>();
    proto->set_id(photo_id);
    proto->set_str_value(item_list);
    pb->second.reset(proto.release());
    LOG_EVERY_N(INFO, 10000) << "photo spu data info"
                             << ", id:" << photo_id
                             << ", value:" << pb->second->ShortDebugString();
    return true;
  }

  static Container::value_type pb_to_record(int64_t key, std::unique_ptr<google::protobuf::Message> pb) {
    if (!pb || !pb.get()) { return {}; }
    auto* proto = static_cast<ProtoData*>(pb.get());
    if (proto == nullptr) {
      LOG(ERROR) << "pb:" << pb->ShortDebugString();
      return {0, {}};
    }
    std::vector<absl::string_view> spu_v = absl::StrSplit(proto->str_value(), "\002", absl::SkipEmpty());
    if (spu_v.empty()) {
      return {0, {}};
    }
    absl::flat_hash_set<int64> spu_ids;
    for (const auto& spu : spu_v) {
      int64_t spu_id = 0;
      if (absl::SimpleAtoi(spu, &spu_id) && spu_id != 0) {
        spu_ids.emplace(spu_id);
      }
    }
    auto set_2_str = [](const absl::flat_hash_set<int64_t>& set) {
      std::stringstream ss;
      ss << "[";
      for (auto& s : set) {
        ss << ",";
        ss <<  s;
      }
      ss << "]";
      return ss.str();
    };
    LOG_EVERY_N(INFO, 10000) << set_2_str(spu_ids);
    return std::make_pair(key, spu_ids);
  }
};

class PhotoItemSpu : public ad_base::P2pCacheLoaderHelper<PhotoItemSpuData> {
 public:
  static PhotoItemSpu* GetInstance() {
    static PhotoItemSpu instance;
    return &instance;
  }

 public:
  absl::flat_hash_set<int64_t> GetPhotoItemSpu(int64_t pid);

 private:
  PhotoItemSpu() : ad_base::P2pCacheLoaderHelper<PhotoItemSpuData>(
      5 * 60 * 1000, "creative_server_photo_item_spu") {}
};

}  // namespace creative_server
}  // namespace ks
