#include "teams/ad/ad_creative_server/util/bg_task/photo_item_spu/photo_item_spu.h"

namespace ks {
namespace creative_server {

absl::flat_hash_set<int64_t> PhotoItemSpu::GetPhotoItemSpu(int64_t pid) {
  const auto& data = GetData();
  if (data == nullptr || data.get() == nullptr || pid == 0) {
    return {};
  }
  auto iter = data->find(pid);
  if (iter != data->end()) {
    return iter->second;
  }
  return {};
}

}  // namespace creative_server
}  // namespace ks
