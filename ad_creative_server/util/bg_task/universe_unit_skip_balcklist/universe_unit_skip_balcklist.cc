#include "teams/ad/ad_creative_server/util/bg_task/universe_unit_skip_balcklist/universe_unit_skip_balcklist.h"

namespace ks {
namespace creative_server {

ks::ad_base::P2pCacheLoaderHelper<UniverseOperationDarkBlockContainer>*
                                                GetUniverseOperationDarkBlockContainer() {
  static ks::ad_base::P2pCacheLoaderHelper<UniverseOperationDarkBlockContainer> ins(
                                          3 * 60 * 1000, "universe_operation_dark_block",
                                          ks::engine_base::DependDataLevel::STRONG_DEPEND);
  return &ins;
}

}  // namespace creative_server
}  // namespace ks
