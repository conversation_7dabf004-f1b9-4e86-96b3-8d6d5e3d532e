#pragma once

#include <vector>
#include <string>
#include <memory>
#include <set>
#include <utility>

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/engine_base/universe/operation_config/operation_config_group.h"
#include "teams/ad/engine_base/universe/operation_config/operation_config_dark_control.h"

namespace ks {
namespace creative_server {

class DarkBlockConfigGroup :
  public engine_base::OperationConfigGroup<engine_base::OperationConfigDarkControl> {
 public:
  using TType = engine_base::OperationConfigDarkControl;
  DarkBlockConfigGroup() {}
  ~DarkBlockConfigGroup() {}

  // 获取暗投屏蔽的 product_name
  void GetBlackProductName(std::set<int64_t>* black_products) const {
    for (const auto& t : config_group) {
      black_products->insert(t.ad_product_ids.begin(),
                             t.ad_product_ids.end());
    }
  }

  // 获取暗投屏蔽的 account_id
  void GetBlackAccountId(std::set<int64_t>* black_accounts) const {
    for (const auto& t : config_group) {
      black_accounts->insert(t.ad_account_ids.begin(),
                             t.ad_account_ids.end());
    }
  }

  // 获取暗投屏蔽的 unit_id
  void GetBlackUnitId(std::set<int64_t>* black_unit) const {
    for (const auto& t : config_group) {
      black_unit->insert(t.ad_unit_ids.begin(),
                         t.ad_unit_ids.end());
    }
  }
  void BuildHashMap() {
    for (const auto& t : config_group) {
      for (int64_t unit_id : t.ad_unit_ids) {
        auto& key_set = unit_id2_multikeys_[unit_id];
        key_set.insert(t.ad_bid_camp_conv_deep_types.begin(), t.ad_bid_camp_conv_deep_types.end());
      }
      for (int64_t account_id : t.ad_account_ids) {
        auto& key_set = account_id2_multikeys_[account_id];
        key_set.insert(t.ad_bid_camp_conv_deep_types.begin(), t.ad_bid_camp_conv_deep_types.end());
      }
      for (int64_t product_id : t.ad_product_ids) {
        auto& key_set = product_id2_multikeys_[product_id];
        key_set.insert(t.ad_bid_camp_conv_deep_types.begin(), t.ad_bid_camp_conv_deep_types.end());
      }
      for (int64_t second_industry_id : t.ad_second_industry_ids) {
        auto& key_set = second_industry_id2_multikeys_[second_industry_id];
        key_set.insert(t.ad_bid_camp_conv_deep_types.begin(), t.ad_bid_camp_conv_deep_types.end());
      }
      for (int64_t author_id : t.ad_author_ids) {
        auto& key_set = author_id2_multikeys_[author_id];
        key_set.insert(t.ad_bid_camp_conv_deep_types.begin(), t.ad_bid_camp_conv_deep_types.end());
      }
    }
  }

  void describe(std::ostream& os) const {
    os << "unit_id2_multikeys_: ";
    for (auto& [id, keys] : unit_id2_multikeys_) {
      os << id << ":[" << absl::StrJoin(keys, ",") << "], ";
    }
    os << "account_id2_multikeys_: ";
    for (auto& [id, keys] : account_id2_multikeys_) {
      os << id << ":[" << absl::StrJoin(keys, ",") << "], ";
    }
    os << "product_id2_multikeys_: ";
    for (auto& [id, keys] : product_id2_multikeys_) {
      os << id << ":[" << absl::StrJoin(keys, ",") << "], ";
    }
    os << "second_industry_id2_multikeys_: ";
    for (auto& [id, keys] : second_industry_id2_multikeys_) {
      os << id << ":[" << absl::StrJoin(keys, ",") << "], ";
    }
    os << "author_id2_multikeys_: ";
    for (auto& [id, keys] : author_id2_multikeys_) {
      os << id << ":[" << absl::StrJoin(keys, ",") << "], ";
    }
  }

  // ad_bid_camp_conv_deep_types
  bool CheckIfBlock(int64_t account_id,
                    int64_t unit_id,
                    int64_t product_id,
                    int64_t second_industry_id,
                    int64_t author_id,
                    int64_t bid_type,
                    int64_t campaign_type,
                    int64_t ocpx_action_type,
                    int64_t deep_conversion_type) {
    // 四位二进制数 从最高位到最低位 分别表示 是否使用 bid_type campaign_type ocpx_action_type deep_conversion_type 进行查询 1 表示用具体值 0 表示用通配符  //NOLINT
    static const std::vector<int32_t> idxs = {0, 1, 2, 4, 8, 16, 3, 5, 9, 6, 10, 12, 7, 11, 13, 14, 15};
    for (int32_t idx : idxs) {
      std::string key = absl::Substitute("$0-$1-$2-$3",
                                          (idx & (1 << 3)) ? bid_type : 0,
                                          (idx & (1 << 2)) ? campaign_type : 0,
                                          (idx & (1 << 1)) ? ocpx_action_type : 0,
                                          (idx & (1 << 0)) ? deep_conversion_type : 0);
      if ((unit_id2_multikeys_.count(unit_id) &&
           unit_id2_multikeys_[unit_id].count(key)) ||
          (account_id2_multikeys_.count(account_id) &&
           account_id2_multikeys_[account_id].count(key)) ||
          (product_id2_multikeys_.count(product_id) &&
           product_id2_multikeys_[product_id].count(key)) ||
          (second_industry_id2_multikeys_.count(second_industry_id) &&
           second_industry_id2_multikeys_[second_industry_id].count(key)) ||
          (author_id2_multikeys_.count(author_id) &&
           author_id2_multikeys_[author_id].count(key))) {
        return true;
      }
    }
    return false;
  }
 public:
  using HashSetHashMap = absl::flat_hash_map<int64_t, absl::flat_hash_set<std::string>>;
  HashSetHashMap unit_id2_multikeys_;
  HashSetHashMap account_id2_multikeys_;
  HashSetHashMap product_id2_multikeys_;
  HashSetHashMap second_industry_id2_multikeys_;
  HashSetHashMap author_id2_multikeys_;
};

inline std::ostream &operator<<(std::ostream &os, const DarkBlockConfigGroup &sr) {
  sr.describe(os);
  return os;
}

// 线上使用 struct
class UniverseOperationDarkBlockContainer {
 public:
  using StructData = DarkBlockConfigGroup;
  using ProtoData = engine_base::operation_config::ConfigInfoResponse;
  // note: 在这里进行在线使用的数据结构的切换
  using Container = absl::flat_hash_map<int64_t, StructData>;
  // using Container = std::unordered_map<int64_t, ProtoData>; // 2.线上使用 pb 结构
  // 文件中一行转换为 pb
  static bool line_to_proto(const std::string& line,
                            std::pair<int64_t, std::unique_ptr<google::protobuf::Message>>* pb) {
    if (!pb) {
      return false;
    }
    std::unique_ptr<ProtoData> msg(new ProtoData());
    if (!ks::ad_base::pb_utils::ParseBase64PB(*msg, line)) {
      ks::infra::PerfUtil::CountLogStash(1, "ad.engine", "UniverseOperationConfig.fail",
                                        "parse_failed", "universe_operation_dark_block");
      return false;
    }
    LOG(INFO) << "UniverseOperationConfig:" << msg->ShortDebugString();

    if (msg->status() != 1) {
      ks::infra::PerfUtil::CountLogStash(1, "ad.engine", "UniverseOperationConfig.fail",
                                        "status_error", "universe_operation_dark_block");
      return false;
    }
    pb->first = 0;
    pb->second = std::move(msg);
    return true;
  }
  // pb 转换为内存中的 kv 结构: value 为 StrutData
  static Container::value_type pb_to_record(int64_t key, std::unique_ptr<google::protobuf::Message> pb) {
    StructData struct_data;
    if (pb) {
      auto* msg = static_cast<ProtoData*>(pb.get());
      for (const auto& one_data : msg->dark_control()) {
        if (one_data.status() != 0 || one_data.type() != 3) {
          continue;
        }
        int64_t cur_timestamp = base::GetTimestamp() / 1000;
        if ((one_data.begin_time() == 0 || cur_timestamp > one_data.begin_time()) &&
            (one_data.end_time() == 0 || cur_timestamp < one_data.end_time())) {
          struct_data.AddData(one_data);
        }
      }
      struct_data.BuildHashMap();
      struct_data.Debug();
    }
    return std::make_pair(key, struct_data);
  }
};
ks::ad_base::P2pCacheLoaderHelper<UniverseOperationDarkBlockContainer>*
                                                GetUniverseOperationDarkBlockContainer();

}  // namespace creative_server
}  // namespace ks
