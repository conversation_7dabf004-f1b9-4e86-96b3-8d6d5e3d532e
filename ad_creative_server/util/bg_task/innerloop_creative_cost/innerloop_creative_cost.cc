#include "teams/ad/ad_creative_server/util/bg_task/innerloop_creative_cost/innerloop_creative_cost.h"

#include <fstream>
#include "absl/strings/substitute.h"
#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "absl/base/macros.h"
#include "base/strings/string_split.h"
#include "base/file/file_util.h"
#include "teams/ad/ad_creative_server/context_data/common_type.h"

namespace ks {
namespace creative_server {

InnerloopCreativeCost::InnerloopCreativeCost()
    : ad_base::P2pCacheLoader<absl::flat_hash_map<int64, std::vector<int64>>>(
            3 * 60 * 1000,
            "innerloop_creative_server_creative_cost") {
}

bool InnerloopCreativeCost::ParseLine(const std::string& line) {
  std::vector<std::string> tokens;
  base::SplitString(line, "\t", &tokens);
  if (tokens.size() != 4) {
    return false;
  }
  LOG_EVERY_N(INFO, 10000) << "moqi debug InnerloopCreativeCost:" << line;
  int64 creative_id = 0;
  if (!absl::SimpleAtoi(tokens[0], &creative_id)) {
    LOG_EVERY_N(WARNING, 10000) << "creative_id:" << tokens[0] << ", is invalid, not integer!";
    return false;
  }
  int64 cost_3day = 0;
  if (!absl::SimpleAtoi(tokens[1], &cost_3day)) {
    LOG_EVERY_N(WARNING, 10000) << "cost_3day:" << tokens[1] << ", is invalid, not integer!";
    return false;
  }
  int64 cost_5day = 0;
  if (!absl::SimpleAtoi(tokens[2], &cost_5day)) {
    LOG_EVERY_N(WARNING, 10000) << "cost_5day:" << tokens[2] << ", is invalid, not integer!";
    return false;
  }
  int64 cost_7day = 0;
  if (!absl::SimpleAtoi(tokens[3], &cost_7day)) {
    LOG_EVERY_N(WARNING, 10000) << "cost_7day:" << tokens[3] << ", is invalid, not integer!";
    return false;
  }
  std::vector<int64> vec = {cost_3day, cost_5day, cost_7day};
  (*process_data_)[creative_id] = vec;
  tokens.clear();
  LOG_EVERY_N(INFO, 10000) << "InnerloopCreativeCost creative id:" << creative_id
      << ", cost_3day:" << cost_3day
      << ", cost_5day:" << cost_5day
      << ", cost_7day:" << cost_7day;
  return true;
}

int InnerloopCreativeCost::GetCreativeCostByIndex(int64 creative_id, int index) {
  const auto& cost_data = GetData();
  if (cost_data == nullptr || cost_data.get() == nullptr) {
    LOG_EVERY_N(INFO, 10000) << "cost_data nullptr.";
    return 0;
  }
  auto data_iter = cost_data->find(creative_id);
  if (data_iter != cost_data->end()) {
    auto& cost_vec = data_iter->second;
    if (index >= 0 && index < cost_vec.size()) {
      return cost_vec[index];
    }
  }
  return 0;
}

}  // namespace creative_server
}  // namespace ks

