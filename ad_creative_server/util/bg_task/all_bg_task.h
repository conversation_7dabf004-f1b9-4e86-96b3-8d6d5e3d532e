#pragma once

#include "teams/ad/ad_counter/api/ad_counter.h"
#include "teams/ad/ad_creative_server/util/bg_task/photo_statistic/photo_simple_post.h"
#include "teams/ad/ad_creative_server/util/bg_task/forward_index/forward_index.h"
#include "teams/ad/ad_creative_server/util/bg_task/creative_posterior/creative_posterior_all_scene.h"
#include "teams/ad/ad_creative_server/util/bg_task/creative_funnel_data/creative_funnel.h"
#include "teams/ad/ad_creative_server/util/bg_task/external_select_creative/external_select_creative.h"
#include "teams/ad/ad_creative_server/util/bg_task/account_valid_budget/account_valid_budget.h"
#include "teams/ad/ad_creative_server/util/bg_task/unit_budget_data/unit_no_budget_hours.h"
#include "teams/ad/ad_creative_server/util/bg_task/universe_unit_budget_data/universe_unit_no_budget_hours.h"
#include "teams/ad/ad_creative_server/util/bg_task/split_test_data/split_test_data.h"
#include "teams/ad/ad_creative_server/util/bg_task/splash_rtb_gray/rtb_white_list.h"
#include "teams/ad/ad_creative_server/util/bg_task/photo_quality_score/photo_quality_score.h"
#include "teams/ad/ad_creative_server/util/bg_task/photo_history_data/photo_history_data_loader.h"
#include "teams/ad/ad_creative_server/util/bg_task/account_cost/account_cost.h"
#include "teams/ad/ad_creative_server/util/bg_task/product_agent_cost/product_agent_cost.h"
#include "teams/ad/ad_creative_server/util/bg_task/creative_posterior_universe/creative_posterior_universe.h"
#include "teams/ad/ad_creative_server/util/bg_task/creative_funnel_data_universe/creative_funnel_universe.h"
#include "teams/ad/ad_creative_server/util/bg_task/universe_unit_skip_balcklist/universe_unit_skip_balcklist.h"
