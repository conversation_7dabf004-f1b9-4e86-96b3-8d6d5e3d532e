#include "teams/ad/ad_creative_server/util/bg_task/photo_quality_score/photo_quality_score.h"

namespace ks {
namespace creative_server {

double PhotoQualityScore::GetPhotoQualityScore(int64_t pid) {
  auto ptr = GetData();
  if (!ptr || !ptr.get()) { return 0; }
  auto iter = ptr->find(pid);
  if (iter != ptr->end()) {
    return iter->second;
  }
  return 0;
}

}  //  namespace creative_server
}  //  namespace ks
