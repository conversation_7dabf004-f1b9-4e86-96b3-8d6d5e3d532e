#pragma once

#include <unordered_map>
#include <string>
#include <memory>
#include <vector>

#include "base/common/basic_types.h"
#include "absl/container/flat_hash_map.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"

namespace ks {
namespace creative_server {

// compare double with integer
// return 0 when equal within epsilon
// return 1 when greater
// otherwise return -1
static inline int CmpDoubleAndInt(double d, int i, double epsilon = 1e-6) {
  auto diff = std::abs(d - i);
  if (diff <= epsilon) return 0;
  return d > i ? 1 : -1;
}

static int TranslateDoubleToInt(double score, int min = -3, int max = 3) {
  if (score == 0) { return 0; }
  for (int i = min; i <= max; i++) {
    if (CmpDoubleAndInt(score, i) == 0) { return i; }
  }
  return 0;
}

class PhotoQualityScore : public ad_base::P2pCacheLoader<absl::flat_hash_map<int64_t, double>,
                                                         ad_base::DeserializeFileEnum::Basic> {
 public:
  static PhotoQualityScore* GetInstance() {
    static PhotoQualityScore instance;
    return &instance;
  }

  double GetPhotoQualityScore(int64 pid);

 private:
  PhotoQualityScore() : ad_base::P2pCacheLoader<absl::flat_hash_map<int64_t, double>,
                                                ad_base::DeserializeFileEnum::Basic>(
            3 * 60 * 1000, "adphoto_quality_score_dt") {}
  DISALLOW_COPY_AND_ASSIGN(PhotoQualityScore);
};

}  // namespace creative_server
}  // namespace ks
