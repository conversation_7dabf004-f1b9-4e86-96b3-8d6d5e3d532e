#pragma once
#include <memory>
#include <string>
#include <vector>
#include <utility>

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader_data.pb.h"

namespace ks {
namespace creative_server {

class InnerloopCreativeRealTimeCost :
    public ad_base::P2pCacheLoader<absl::flat_hash_map<int64, double>> {
 public:
  static InnerloopCreativeRealTimeCost* GetInstance() {
    static InnerloopCreativeRealTimeCost creative_cost;
    return &creative_cost;
  }

  double GetCreativeRealTimeCost(int64 creative_id);
 private:
  InnerloopCreativeRealTimeCost();
  bool ParseLine(const std::string& line) override;
  DISALLOW_COPY_AND_ASSIGN(InnerloopCreativeRealTimeCost);
};

}  // namespace creative_server
}  // namespace ks
