#pragma once

#include "base/common/basic_types.h"
#include "teams/ad/ad_creative_server/util/bg_task/photo_feature/photo_release_time.h"
#include "teams/ad/ad_creative_server/util/bg_task/photo_feature/photo_first_audit_time.h"

namespace ks {
namespace creative_server {

class PhotoTimeManager {
 public:
  static PhotoTimeManager* GetInstance() {
    static PhotoTimeManager instance;
    return &instance;
  }

  int64_t GetPhotoReleaseTime(int64_t photo_id) {
    return PhotoReleaseTime::GetInstance()->GetPhotoReleaseTime(photo_id);
  }
  int64_t GetPhotoFirstAuditTime(int64_t photo_id) {
    return PhotoFirstAuditTime::GetInstance()->GetPhotoFirstAuditTime(photo_id);
  }
  int64_t GetPhotoValidTime(int64_t photo_id, int64_t default_ts = 0) {
    auto photo_release_time = PhotoReleaseTime::GetInstance()->GetPhotoReleaseTime(photo_id);
    auto photo_first_audit_time = PhotoFirstAuditTime::GetInstance()->GetPhotoFirstAuditTime(photo_id);
    if (photo_release_time <= 0 && photo_first_audit_time <= 0) { return default_ts; }
    return photo_release_time > 0 ? photo_release_time : photo_first_audit_time;
  }

 private:
  PhotoTimeManager() {}
  DISALLOW_COPY_AND_ASSIGN(PhotoTimeManager);
};

}  // namespace creative_server
}  // namespace ks
