#pragma once

#include <unordered_map>
#include <string>
#include <memory>
#include <vector>

#include "base/common/basic_types.h"
#include "absl/container/flat_hash_map.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"

namespace ks {
namespace creative_server {

using PhotoAudioEmbValueTp = absl::flat_hash_map<int64_t, std::vector<float>>;

class PhotoAudioEmb: public ad_base::P2pCacheLoader<PhotoAudioEmbValueTp> {
 public:
  static PhotoAudioEmb* GetInstance() {
    static PhotoAudioEmb instance;
    return &instance;
  }

  void GetPhotoAudioEmb(int64_t photo_id, std::vector<float>* photo_data);

 private:
  PhotoAudioEmb();
  bool ParseLine(const std::string& line);
  DISALLOW_COPY_AND_ASSIGN(PhotoAudioEmb);
};

}  // namespace creative_server
}  // namespace ks
