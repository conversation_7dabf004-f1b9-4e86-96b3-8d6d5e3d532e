#include "teams/ad/ad_creative_server/util/bg_task/photo_feature/photo_audio_emb.h"

#include "base/time/timestamp.h"
#include "absl/strings/numbers.h"
#include "absl/strings/str_split.h"

namespace ks {
namespace creative_server {

constexpr int32_t kEmbSize = 128;  // emb size
constexpr int64_t kLogFrequencyPhoto = 10000000;

PhotoAudioEmb::PhotoAudioEmb()
    : ad_base::P2pCacheLoader<PhotoAudioEmbValueTp>(3 * 60 * 1000, "ad_audemb") {}

bool PhotoAudioEmb::ParseLine(const std::string& line) {
  std::vector<absl::string_view> tokens = absl::StrSplit(line, "\t", absl::SkipEmpty());
  if (tokens.size() != 2) {
    return false;
  }

  // check key
  int64_t photo_id = 0;
  if (!absl::SimpleAtoi(tokens[0], &photo_id)) {
    LOG_EVERY_N(WARNING, 10000) << "photo_audio_emb key:" << tokens[0] << ", is invalid, not integer!";
    return false;
  }
  std::vector<absl::string_view> value_tokens = absl::StrSplit(tokens[1], ",", absl::SkipEmpty());
  // check value
  if (value_tokens.size() != kEmbSize) {
    LOG(WARNING) << "photo_audio_emb failed. decode_value:" << tokens[1] << ". key:" << tokens[0];
    return false;
  }
  (*process_data_)[photo_id].reserve(value_tokens.size());
  for (auto& token : value_tokens) {
    float value = 0;
    if (!absl::SimpleAtof(token, &value)) {
      LOG(WARNING) << "photo_audio_emb failed. decode_value:" << tokens[2] << ". key:" << tokens[0];
      return false;
    }
    (*process_data_)[photo_id].emplace_back(value);
  }
  LOG_EVERY_N(INFO, kLogFrequencyPhoto) << "photo_audio_emb key:" << tokens[0]
    << " emb:" << tokens[1];
  return true;
}

void PhotoAudioEmb::GetPhotoAudioEmb(int64_t photo_id, std::vector<float>* photo_data) {
  if (photo_id <= 0) { return; }
  const auto& post_feature_data = GetData();
  if (!post_feature_data || !post_feature_data.get()) {
    return;
  }
  auto post_feature_data_iter = post_feature_data->find(photo_id);
  if (post_feature_data_iter != post_feature_data->end()) {
    photo_data->clear();
    photo_data->insert(photo_data->end(),
                       post_feature_data_iter->second.begin(),
                       post_feature_data_iter->second.end());
  }
}

}  // namespace creative_server
}  // namespace ks
