#pragma once

#include <unordered_map>
#include <string>
#include <memory>
#include <vector>

#include "base/common/basic_types.h"
#include "absl/container/flat_hash_set.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"

namespace ks {
namespace creative_server {

class RtbGrayWhiteList : public ad_base::P2pCacheLoader<absl::flat_hash_set<int64_t>,
                                                           ad_base::DeserializeFileEnum::Basic> {
 public:
  static RtbGrayWhiteList* GetInstance() {
    static RtbGrayWhiteList instance;
    return &instance;
  }

  bool IsRtbGray(int64 id);

 private:
  RtbGrayWhiteList() : ad_base::P2pCacheLoader<absl::flat_hash_set<int64_t>,
                                                  ad_base::DeserializeFileEnum::Basic>(
            10 * 60 * 1000, "rtb_white_list") {}
  DISALLOW_COPY_AND_ASSIGN(RtbGrayWhiteList);
};

}  // namespace creative_server
}  // namespace ks
