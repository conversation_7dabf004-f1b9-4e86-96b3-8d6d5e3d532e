#pragma once

#include <unordered_map>
#include <string>
#include <memory>
#include <vector>

#include "base/common/basic_types.h"
#include "absl/container/flat_hash_map.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"

namespace ks {
namespace creative_server {

using UnitBudgetTp = absl::flat_hash_map<int64_t, int64_t>;

class UnitNoBudgetHour : public ad_base::P2pCacheLoader<UnitBudgetTp,
                                                          ad_base::DeserializeFileEnum::Basic> {
 public:
  static UnitNoBudgetHour* GetInstance() {
    static UnitNoBudgetHour instance;
    return &instance;
  }

 public:
  int64 GetUnitNoBudgetHour(int64 unit_id);

 private:
  UnitNoBudgetHour() : ad_base::P2pCacheLoader<UnitBudgetTp, ad_base::DeserializeFileEnum::Basic>(
            10 * 60 * 1000, "unit_no_budget_hours") {}
  DISALLOW_COPY_AND_ASSIGN(UnitNoBudgetHour);
};

}  // namespace creative_server
}  // namespace ks
