#include "teams/ad/ad_creative_server/util/bg_task/unit_budget_data/unit_no_budget_hours.h"

namespace ks {
namespace creative_server {

int64 UnitNoBudgetHour::GetUnitNoBudgetHour(int64 unit_id) {
  auto ptr = GetData();
  if (!ptr || !ptr.get()) { return 0; }
  auto iter = ptr->find(unit_id);
  if (iter != ptr->end()) {
    return iter->second;
  }
  return 0;
}

}  //  namespace creative_server
}  //  namespace ks
