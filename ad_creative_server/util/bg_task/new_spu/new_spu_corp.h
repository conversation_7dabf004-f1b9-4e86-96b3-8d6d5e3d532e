#pragma once

#include <unordered_map>
#include <string>
#include <memory>
#include <vector>

#include "base/common/basic_types.h"
#include "absl/container/flat_hash_map.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"

namespace ks {
namespace creative_server {

using NewSpuCorpTp = absl::flat_hash_map<int64_t, std::string>;

class NewSpuCorp : public ad_base::P2pCacheLoader<NewSpuCorpTp,
                                                   ad_base::DeserializeFileEnum::Basic> {
 public:
  static NewSpuCorp* GetInstance() {
    static NewSpuCorp instance;
    return &instance;
  }

  std::string GetCorp(int64_t spu_id);

 private:
  NewSpuCorp() :
            ad_base::P2pCacheLoader<NewSpuCorpTp, ad_base::DeserializeFileEnum::Basic>(
                10 * 60 * 1000, "new_spu_corp_p2p") {}
  DISALLOW_COPY_AND_ASSIGN(NewSpuCorp);
};

}  // namespace creative_server
}  // namespace ks
