#pragma once

#include <array>
#include <unordered_map>
#include <string>
#include <memory>
#include <vector>

#include "base/common/basic_types.h"
#include "absl/container/flat_hash_map.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"

namespace ks {
namespace creative_server {

class UniverseUnitNoBudgetHour : public ad_base::P2pCacheLoader<absl::flat_hash_map<int64_t,
                                 std::vector<int32>>> {
 public:
  static UniverseUnitNoBudgetHour* GetInstance() {
    static UniverseUnitNoBudgetHour instance;
    return &instance;
  }

  int32 GetUniverseUnitNoBudgetHour(int64_t unit_id, int index);

 private:
  UniverseUnitNoBudgetHour();
  bool ParseLine(const std::string& line) override;
  DISALLOW_COPY_AND_ASSIGN(UniverseUnitNoBudgetHour);
};

}  // namespace creative_server
}  // namespace ks
