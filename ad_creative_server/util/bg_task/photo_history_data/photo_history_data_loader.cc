#include "teams/ad/ad_creative_server/util/bg_task/photo_history_data/photo_history_data_loader.h"

#include <vector>
#include "absl/strings/numbers.h"
#include "absl/strings/str_split.h"
#include "teams/ad/ad_creative_server/context_data/common_type.h"

namespace ks {
namespace creative_server {

bool PhotoHistoryDataLoader::ParseLine(const std::string& line) {
  std::vector<absl::string_view> tokens = absl::StrSplit(line, kHistoryFieldSeparator);
  if (tokens.size() < kHistoryColumns) {
    LOG(ERROR) << "invalid line: " << line;
    return false;
  }
  PhotoHistoryData history_data;
  int64_t photo_id;
  if (!absl::SimpleAtoi(tokens[0], &photo_id)) {
    LOG(ERROR) << "invalid line: " << line;
    return false;
  }
  if (!absl::SimpleAtoi(tokens[1], &history_data.impr)) {
    history_data.impr = 0;
  }
  if (!absl::SimpleAtoi(tokens[2], &history_data.cost)) {
    history_data.cost = 0;
  }
  if (!absl::SimpleAtoi(tokens[3], &history_data.impr_3days)) {
    history_data.impr_3days = 0;
  }
  if (!absl::SimpleAtoi(tokens[4], &history_data.cost_3days)) {
    history_data.cost_3days = 0;
  }
  if (!absl::SimpleAtoi(tokens[5], &history_data.pcvr_rank)) {
    history_data.pcvr_rank = 0;
  }
  if (!absl::SimpleAtoi(tokens[6], &history_data.content_rating)) {
    history_data.content_rating = 0;
  }
  (*process_data_)[photo_id] = history_data;
  return true;
}
PhotoHistoryData PhotoHistoryDataLoader::GetPhotoHistoryData(int64 photo_id) {
  const auto& post_data = GetData();
  PhotoHistoryData result_data;
  if (post_data == nullptr || post_data.get() == nullptr) {
    return result_data;
  }
  auto data_iter = post_data->find(photo_id);
  if (data_iter != post_data->end()) {
    result_data.cost = data_iter->second.cost;
    result_data.impr = data_iter->second.impr;
    result_data.cost_3days = data_iter->second.cost_3days;
    result_data.impr_3days = data_iter->second.impr_3days;
    result_data.content_rating = data_iter->second.content_rating;
    result_data.pcvr_rank = data_iter->second.pcvr_rank;
  }
  return result_data;
}

}  // namespace creative_server
}  // namespace ks
