#pragma once

#include <array>
#include <vector>
#include <unordered_map>
#include <string>
#include <atomic>
#include <thread>
#include <mutex>

#include "base/common/basic_types.h"
#include "absl/container/flat_hash_map.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"

namespace ks {
namespace creative_server {

class UniverseCreativeCost :
    public ad_base::P2pCacheLoader<absl::flat_hash_map<int64, std::vector<int64>>> {
 public:
  static UniverseCreativeCost* GetInstance() {
    static UniverseCreativeCost creative_cost;
    return &creative_cost;
  }

  int GetCreativeCostByIndex(int64 creative_id, int index);
 private:
  UniverseCreativeCost();
  bool ParseLine(const std::string& line) override;
  DISALLOW_COPY_AND_ASSIGN(UniverseCreativeCost);
};

}  // namespace creative_server
}  // namespace ks
