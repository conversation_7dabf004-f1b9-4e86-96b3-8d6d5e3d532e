#pragma once

#include <string>
#include <unordered_map>

#include "absl/container/flat_hash_map.h"
#include "teams/ad/data_push/utils/kconf/kconf.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_hosting.pb.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"

namespace ks {
namespace creative_server {

using kuaishou::ad::SplitTestGroup;
using SplitTestGroupTp = absl::flat_hash_map<int64_t, SplitTestGroup>;

class SplitTestGroupData : public ad_base::P2pCacheLoader<SplitTestGroupTp,
                                                            ad_base::DeserializeFileEnum::PBBinary> {
 public:
  static SplitTestGroupData* GetInstance() {
    static SplitTestGroupData instance;
    return &instance;
  }

 public:
  int64 GetGroupTag(int64_t group_id);  // return -1 if not found

 private:
  SplitTestGroupData() : ad_base::P2pCacheLoader<SplitTestGroupTp, ad_base::DeserializeFileEnum::PBBinary>(
            3 * 60 * 1000, *::ad::data_push::DataPushKconfUtil::HostingSplitTestGroupP2pName()) {}
};

}   // namespace creative_server
}   // namespace ks
