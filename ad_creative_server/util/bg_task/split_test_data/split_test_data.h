#pragma once

#include "teams/ad/ad_creative_server/util/bg_task/split_test_data/split_test_group.h"
#include "teams/ad/ad_creative_server/util/bg_task/split_test_data/split_test_unit.h"

namespace ks {
namespace creative_server {

class SplitTestData {
 public:
  static SplitTestData* GetInstance() {
    static SplitTestData instance;
    return &instance;
  }

 public:
  void Start();
  void Stop();
  bool IsReady();
  bool IsDataReady() { return IsReady(); }
  int64 GetUnitGroupTag(int64_t unit_id);  // return -1 if not found or not open

 private:
  SplitTestData();
  bool start_{false};
  SplitTestUnitData* unit_data_{nullptr};
  SplitTestGroupData* group_data_{nullptr};
};

}   // namespace creative_server
}   // namespace ks
