#pragma once

#include <string>
#include <unordered_map>

#include "absl/container/flat_hash_map.h"
#include "teams/ad/data_push/utils/kconf/kconf.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_hosting.pb.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"

namespace ks {
namespace creative_server {

using kuaishou::ad::SplitTestUnit;
using SplitTestUnitTp = absl::flat_hash_map<int64_t, SplitTestUnit>;

class SplitTestUnitData : public ad_base::P2pCacheLoader<SplitTestUnitTp,
                                                            ad_base::DeserializeFileEnum::PBBinary> {
 public:
  static SplitTestUnitData* GetInstance() {
    static SplitTestUnitData instance;
    return &instance;
  }

 public:
  int64 GetGroupId(int64_t unit_id);  // return -1 if not found or not open

 private:
  SplitTestUnitData() : ad_base::P2pCacheLoader<SplitTestUnitTp, ad_base::DeserializeFileEnum::PBBinary>(
            3 * 60 * 1000, *::ad::data_push::DataPushKconfUtil::HostingSplitTestUnitP2pName()) {}
};

}   // namespace creative_server
}   // namespace ks
