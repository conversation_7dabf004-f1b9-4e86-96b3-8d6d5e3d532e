#include "teams/ad/ad_creative_server/util/bg_task/split_test_data/split_test_unit.h"

#include "teams/ad/ad_creative_server/util/perf_log_util/perf_log_util.h"

namespace ks {
namespace creative_server {

int64 SplitTestUnitData::GetGroupId(int64_t unit_id) {
  auto ptr = GetData();
  if (!ptr || !ptr.get()) { return -1; }
  auto iter = ptr->find(unit_id);
  if (iter != ptr->end()) {
    Dotter::GetInstance()->Count(1, "get_unit_group_id", "hit");
    return static_cast<int64_t>(iter->second.group_id());
  }
  Dotter::GetInstance()->Count(1, "get_unit_group_id", "miss");
  return -1;
}

}   // namespace creative_server
}   // namespace ks
