#pragma once
#include <utility>
#include <memory>
#include <string>
#include <vector>

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader_data.pb.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
namespace ks {
namespace creative_server {

struct InnerloopDupPhotoInfo {
 public:
  int64_t photo_id = 0;
  int64_t crop_tag = 0;
};

class InnerloopDupPhotoDataContainer {
 public:
  using StructData = InnerloopDupPhotoInfo;
  using ProtoData = ad_base::InnerloopDupPhotoDataPb;
  using Container = absl::flat_hash_map<int64_t, StructData>;
  static bool line_to_proto(const std::string& line,
                            std::pair<int64_t, std::unique_ptr<google::protobuf::Message>>* pb) {
    if (!pb) {
      return false;
    }
    std::vector<absl::string_view> tokens = absl::StrSplit(line, ":", absl::SkipEmpty());
    if (tokens.size() != 2) {
      return false;
    }
    ignore_result(absl::StripAsciiWhitespace(tokens[0]));
    ignore_result(absl::StripAsciiWhitespace(tokens[1]));

    int64_t photo_id = 0;
    if (!absl::SimpleAtoi(tokens[0], &photo_id)) {
      return false;
    }
    int64_t crop_tag = 0;
    if (!absl::SimpleAtoi(tokens[1], &crop_tag)) {
      return false;
    }

    LOG_EVERY_N(INFO, 10000) << "ecom dup_photo data" << photo_id << ";" << crop_tag;

    std::unique_ptr<ProtoData> msg(new ProtoData());
    msg->set_photo_id(photo_id);
    msg->set_crop_tag(crop_tag);
    pb->first = photo_id;
    pb->second = std::move(msg);
    return true;
  }

  static Container::value_type pb_to_record(int64_t key, std::unique_ptr<google::protobuf::Message> pb) {
    InnerloopDupPhotoInfo data;
    if (pb) {
      auto* msg = static_cast<ProtoData*>(pb.get());
      data.photo_id = msg->photo_id();
      data.crop_tag = msg->crop_tag();
    }
    return std::make_pair(key, data);
  }
};

class InnerloopDupPhotoData : public ad_base::P2pCacheLoaderHelper<
    InnerloopDupPhotoDataContainer> {
 public:
  static InnerloopDupPhotoData* GetInstance() {
    static InnerloopDupPhotoData instance;
    return &instance;
  }

 public:
  void GetPhotoData(int64_t photo_id, InnerloopDupPhotoInfo* photo_data);

 private:
  InnerloopDupPhotoData()
      :ad_base::P2pCacheLoaderHelper<InnerloopDupPhotoDataContainer>(
      5 * 60 * 1000, "ecom_dup_photo_data_v1") {}
};



}  // namespace creative_server
}  // namespace ks
