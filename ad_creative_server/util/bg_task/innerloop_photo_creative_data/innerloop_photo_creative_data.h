#pragma once
#include <utility>
#include <memory>
#include <string>
#include <vector>

#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader_data.pb.h"
#include "teams/ad/ad_base/src/timer_task/p2p_cache_loader.h"
namespace ks {
namespace creative_server {

struct InnerloopPhotoCreativeInfo {
 public:
  int64_t rank = 0;
  double cost = 0.0;
  int64_t swap_reason = 0;
};

class InnerloopPhotoCreativeDataContainer {
 public:
  using StructData = InnerloopPhotoCreativeInfo;
  using ProtoData = ad_base::InnerloopPhotoCreativeDataPb;
  using Container = absl::flat_hash_map<int64_t, StructData>;
  static bool line_to_proto(const std::string& line,
                            std::pair<int64_t, std::unique_ptr<google::protobuf::Message>>* pb) {
    if (!pb) {
      return false;
    }
    std::vector<absl::string_view> tokens = absl::StrSplit(line, ":", absl::SkipEmpty());
    if (tokens.size() != 4) {
      return false;
    }
    ignore_result(absl::StripAsciiWhitespace(tokens[0]));
    ignore_result(absl::StripAsciiWhitespace(tokens[1]));
    ignore_result(absl::StripAsciiWhitespace(tokens[2]));
    ignore_result(absl::StripAsciiWhitespace(tokens[3]));

    int64_t creative_id = 0;
    if (!absl::SimpleAtoi(tokens[0], &creative_id)) {
      return false;
    }
    int64_t rank = 0;
    if (!absl::SimpleAtoi(tokens[1], &rank)) {
      return false;
    }
    double cost = 0.0;
    if (!absl::SimpleAtod(tokens[2], &cost)) {
      return false;
    }
    int64_t swap_reason = 0;
    if (!absl::SimpleAtoi(tokens[3], &swap_reason)) {
      return false;
    }
    LOG_EVERY_N(INFO, 1000000) << " ecom creative data " << creative_id
                               << " rank " << rank
                               << " cost " << cost
                               << " swap reason " << swap_reason;

    std::unique_ptr<ProtoData> msg(new ProtoData());
    msg->set_rank(rank);
    msg->set_cost(cost);
    msg->set_swap_reason(swap_reason);
    pb->first = creative_id;
    pb->second = std::move(msg);
    return true;
  }

  static Container::value_type pb_to_record(int64_t key, std::unique_ptr<google::protobuf::Message> pb) {
    InnerloopPhotoCreativeInfo data;
    if (pb) {
      auto* msg = static_cast<ProtoData*>(pb.get());
      data.rank = msg->rank();
      data.cost = msg->cost();
      data.swap_reason = msg->swap_reason();
    }
    return std::make_pair(key, data);
  }
};

class InnerloopPhotoCreativeData : public ad_base::P2pCacheLoaderHelper<
    InnerloopPhotoCreativeDataContainer> {
 public:
  static InnerloopPhotoCreativeData* GetInstance() {
    static InnerloopPhotoCreativeData instance;
    return &instance;
  }

 public:
  void GetCreativeRank(int64_t creative_id, InnerloopPhotoCreativeInfo* creative_data);

 private:
  InnerloopPhotoCreativeData()
      :ad_base::P2pCacheLoaderHelper<InnerloopPhotoCreativeDataContainer>(
      5 * 60 * 1000, "ecom_photo_creative_data") {}
};

class InnerloopPhoto2liveCreativeData : public ad_base::P2pCacheLoaderHelper<
    InnerloopPhotoCreativeDataContainer> {
 public:
  static InnerloopPhoto2liveCreativeData* GetInstance() {
    static InnerloopPhoto2liveCreativeData instance;
    return &instance;
  }

 public:
  void GetCreativeRank(int64_t creative_id, InnerloopPhotoCreativeInfo* creative_data);

 private:
  InnerloopPhoto2liveCreativeData()
      :ad_base::P2pCacheLoaderHelper<InnerloopPhotoCreativeDataContainer>(
      5 * 60 * 1000, "ecom_photo_creative_data_p2l") {}
};

class InnerloopPhoto2liveCreativeDataV2 : public ad_base::P2pCacheLoaderHelper<
    InnerloopPhotoCreativeDataContainer> {
 public:
  static InnerloopPhoto2liveCreativeDataV2* GetInstance() {
    static InnerloopPhoto2liveCreativeDataV2 instance;
    return &instance;
  }

 public:
  void GetCreativeRank(int64_t creative_id, InnerloopPhotoCreativeInfo* creative_data);

 private:
  InnerloopPhoto2liveCreativeDataV2()
      :ad_base::P2pCacheLoaderHelper<InnerloopPhotoCreativeDataContainer>(
      5 * 60 * 1000, "ecom_photo_creative_data_p2l_v2") {}
};


}  // namespace creative_server
}  // namespace ks
