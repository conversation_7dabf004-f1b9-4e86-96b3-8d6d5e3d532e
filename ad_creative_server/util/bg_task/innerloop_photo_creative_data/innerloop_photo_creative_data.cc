#include "teams/ad/ad_creative_server/util/bg_task/innerloop_photo_creative_data/innerloop_photo_creative_data.h"

namespace ks {
namespace creative_server {

void InnerloopPhotoCreativeData::GetCreativeRank(int64_t creative_id,
                                                 InnerloopPhotoCreativeInfo* creative_data) {
  const auto& data = GetData();
  if (data == nullptr || data.get() == nullptr) {
    LOG_EVERY_N(INFO, 10000) << "data nullptr.";
    return;
  }
  auto iter = data->find(creative_id);
  if (iter != data->end()) {
    *creative_data = iter->second;
    return;
  }
  return;
}

void InnerloopPhoto2liveCreativeData::GetCreativeRank(int64_t creative_id,
                                                 InnerloopPhotoCreativeInfo* creative_data) {
  const auto& data = GetData();
  if (data == nullptr || data.get() == nullptr) {
    LOG_EVERY_N(INFO, 10000) << "data nullptr.";
    return;
  }
  auto iter = data->find(creative_id);
  if (iter != data->end()) {
    *creative_data = iter->second;
    return;
  }
  return;
}

void InnerloopPhoto2liveCreativeDataV2::GetCreativeRank(int64_t creative_id,
                                                 InnerloopPhotoCreativeInfo* creative_data) {
  const auto& data = GetData();
  if (data == nullptr || data.get() == nullptr) {
    LOG_EVERY_N(INFO, 10000) << "data nullptr.";
    return;
  }
  auto iter = data->find(creative_id);
  if (iter != data->end()) {
    *creative_data = iter->second;
    return;
  }
  return;
}



}  // namespace creative_server
}  // namespace ks
