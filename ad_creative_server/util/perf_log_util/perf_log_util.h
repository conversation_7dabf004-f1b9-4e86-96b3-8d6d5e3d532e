#pragma once

#include <string>

#include "base/common/basic_types.h"
#include "perfutil/perfutil.h"
#include "base/common/logging.h"
#include "teams/ad/ad_creative_server/util/deploy_util/deploy_state.h"

namespace ks {
namespace creative_server {

class Dot {
 public:
  Dot() {
    ns_ = "ad.ad_creative_server";
    stage_ = DeployState::Instance().GetStageName();
    service_ = DeployState::Instance().GetServiceName();
    instance_ = DeployState::Instance().GetPodName();
    LOG(INFO) << "init Dot:"
              << "ns=" << ns_
              << "|stage=" << stage_
              << "|service=" << service_
              << "|instance=" << instance_;
  }

  Dot(const std::string &ns,
      const std::string &stage,
      const std::string &service,
      const std::string &instance) :
      ns_(ns), stage_(stage), service_(service), instance_(instance) {}

  void Reset(const std::string &ns,
             const std::string &stage,
             const std::string &service,
             const std::string &instance) {
    ns_ = ns;
    stage_ = stage;
    service_ = service;
    instance_ = instance;
  }

  void Count(int64 value,
             const std::string& sub_tag,
             const std::string& extra1 = "",
             const std::string& extra2 = "",
             const std::string& extra3 = "") {
    if (sub_tag.empty()) {
      LOG(WARNING) << " perf log sub tag empty!";
      return;
    }
    ks::infra::PerfUtil::CountLogStash(value, ns_, sub_tag,
                                       extra1, extra2, extra3, stage_, service_, instance_);
  }

  void Count(const std::string& sub_tag,
             const std::string& extra1 = "",
             const std::string& extra2 = "",
             const std::string& extra3 = "") {
    if (sub_tag.empty()) {
      LOG(WARNING) << " perf log sub tag empty!";
      return;
    }
    ks::infra::PerfUtil::CountLogStash(1, ns_, sub_tag,
                                       extra1, extra2, extra3, stage_, service_, instance_);
  }

  void Interval(int64 value,
                const std::string& sub_tag,
                const std::string& extra1 = "",
                const std::string& extra2 = "",
                const std::string& extra3 = "") {
    if (sub_tag.empty()) {
      LOG(WARNING) << " perf log sub tag empty!";
      return;
    }
    ks::infra::PerfUtil::IntervalLogStash(value, ns_, sub_tag,
                                          extra1, extra2, extra3, stage_, service_, instance_);
  }

  void Set(int64 value,
           const std::string& sub_tag,
           const std::string& extra1 = "",
           const std::string& extra2 = "",
           const std::string& extra3 = "") {
    if (sub_tag.empty()) {
      LOG(WARNING) << " perf log sub tag empty!";
      return;
    }
    ks::infra::PerfUtil::SetLogStash(value, ns_, sub_tag,
                                     extra1, extra2, extra3, stage_, service_, instance_);
  }

 private:
  std::string ns_;
  std::string stage_;
  std::string service_;
  std::string instance_;
};

class Dotter {
 public:
  static Dotter* GetInstance() {
    static Dotter dotter;
    return &dotter;
  }

  void Reset(const std::string &ns,
             const std::string &stage,
             const std::string &service,
             const std::string &instance) {
    dot_.Reset(ns, stage, service, instance);
  }

  void Count(int64 value,
             const std::string& sub_tag,
             const std::string& extra1 = "",
             const std::string& extra2 = "",
             const std::string& extra3 = "") {
    dot_.Count(value, sub_tag, extra1, extra2, extra3);
  }

  void Count(const std::string& sub_tag,
             const std::string& extra1 = "",
             const std::string& extra2 = "",
             const std::string& extra3 = "") {
    dot_.Count(sub_tag, extra1, extra2, extra3);
  }

  void Interval(int64 value,
                const std::string& sub_tag,
                const std::string& extra1 = "",
                const std::string& extra2 = "",
                const std::string& extra3 = "") {
    dot_.Interval(value, sub_tag, extra1, extra2, extra3);
  }

  void Set(int64 value,
           const std::string& sub_tag,
           const std::string& extra1 = "",
           const std::string& extra2 = "",
           const std::string& extra3 = "") {
    dot_.Set(value, sub_tag, extra1, extra2, extra3);
  }

 private:
  Dotter() {}

  Dot dot_;
  DISALLOW_COPY_AND_ASSIGN(Dotter);
};

class PerfLog {
 public:
  static void Count(int64 value,
                    const std::string& sub_tag,
                    const std::string& extra1 = "",
                    const std::string& extra2 = "",
                    const std::string& extra3 = "",
                    const std::string& extra4 = "",
                    const std::string& extra5 = "",
                    const std::string& extra6 = "") {
    if (sub_tag.empty()) {
      LOG(WARNING) << "per sub tag is empty!";
      return;
    }
    ks::infra::PerfUtil::CountLogStash(value, "ad.ad_creative_server", sub_tag,
                                       extra1, extra2, extra3, extra4, extra5, extra6);
  }

  static void Interval(int64 value,
                       const std::string& sub_tag,
                       const std::string& extra1 = "",
                       const std::string& extra2 = "",
                       const std::string& extra3 = "",
                       const std::string& extra4 = "",
                       const std::string& extra5 = "",
                       const std::string& extra6 = "") {
    if (sub_tag.empty()) {
      LOG(WARNING) << "per sub tag is empty!";
      return;
    }
    ks::infra::PerfUtil::IntervalLogStash(value, "ad.ad_creative_server", sub_tag,
                                          extra1, extra2, extra3, extra4, extra5, extra6);
  }

  static void Set(int64 value,
                  const std::string& sub_tag,
                  const std::string& extra1 = "",
                  const std::string& extra2 = "",
                  const std::string& extra3 = "",
                  const std::string& extra4 = "",
                  const std::string& extra5 = "",
                  const std::string& extra6 = "") {
    if (sub_tag.empty()) {
      LOG(WARNING) << "per sub tag is empty!";
      return;
    }
    ks::infra::PerfUtil::SetLogStash(value, "ad.ad_creative_server", sub_tag,
                                          extra1, extra2, extra3, extra4, extra5, extra6);
  }
};

}  // namespace creative_server
}  // namespace ks
