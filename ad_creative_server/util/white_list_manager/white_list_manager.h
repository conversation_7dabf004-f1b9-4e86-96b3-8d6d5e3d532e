#pragma once

#include <atomic>
#include <memory>
#include <mutex>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>

#include "base/common/basic_types.h"
#include "base/thread/thread_pool.h"
#include "libcuckoo/cuckoohash_map.hh"
#include "teams/ad/ad_base/src/hash/hash.h"
#include "teams/ad/ad_base/src/common/condition_time_wait.h"

namespace ks {
namespace creative_server {

struct WhiteListData {
  std::unordered_map<int64_t, int> creative_ids;
  std::unordered_map<int64_t, int> unit_ids;
  std::unordered_map<int64_t, int> campaign_ids;
  std::unordered_map<int64_t, int> account_ids;
  std::vector<std::string> users;
};

// 特别队列配置，只支持 account 维度
struct SpecialWhiteListData {
  std::unordered_set<int64_t> all_ids;
  std::unordered_map<std::string, std::unordered_set<int64_t>> tag_2_ids;
  std::unordered_map<std::string, int32> tag_2_quota;
};

class WhiteListManager {
 public:
  static WhiteListManager* GetInstance() {
    static WhiteListManager instance;
    return &instance;
  }

  bool IsCreativeHit(int64_t id) const;
  bool IsUnitHit(int64_t id) const;
  bool IsCampaignHit(int64_t id) const;
  bool IsAccountHit(int64_t id) const;
  bool IsSpecialAccountHit(int64_t id) const;
  void GetSpecialAccountList(const std::string& tag, std::unordered_set<int64_t>* list);
  std::string GetUserNameByCreativeId(int64_t id) const;
  std::string GetUserNameByUnitId(int64_t id) const;
  std::string GetUserNameByCampaignId(int64_t id) const;
  std::string GetUserNameByAccountId(int64_t id) const;
  std::shared_ptr<WhiteListData> GetData() const {
    const auto& data = data_[data_index_.load()];
    return data.raw_data;
  }
  std::shared_ptr<SpecialWhiteListData> GetSpecialData() const {
    const auto& data = special_data_[special_data_index_.load()];
    return data.raw_data;
  }

 private:
  WhiteListManager();
  void Update();
  void ProcessSpecialConfig();
  struct DataHolder {
    std::shared_ptr<WhiteListData> raw_data{nullptr};
  };
  DataHolder data_[2];
  std::atomic_int data_index_{0};
  struct SpecialDataHolder {
    std::shared_ptr<SpecialWhiteListData> raw_data{nullptr};
  };
  SpecialDataHolder special_data_[2];
  std::atomic_int special_data_index_{0};
  ad_base::ConditionTimeWait cw_;
  thread::ThreadPool pool_;
};

}  // namespace creative_server
}  // namespace ks
