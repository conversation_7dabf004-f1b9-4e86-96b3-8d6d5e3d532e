#include "teams/ad/ad_creative_server/util/white_list_manager/white_list_manager.h"

#include <algorithm>

#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "base/common/closure.h"
#include "base/common/logging.h"
#include "base/hash_function/city.h"
#include "teams/ad/ad_creative_server/util/kconf/kconf.h"
#include "teams/ad/ad_creative_server/util/perf_log_util/perf_log_util.h"

namespace ks {
namespace creative_server {

WhiteListManager::WhiteListManager(): pool_(1) {
  pool_.AddTask(::NewCallback(this, &WhiteListManager::Update));
}

bool WhiteListManager::IsCreativeHit(int64_t id) const {
  auto data = GetData();
  if (data == nullptr) return false;
  return data->creative_ids.find(id) != data->creative_ids.end();
}

bool WhiteListManager::IsUnitHit(int64_t id) const {
  auto data = GetData();
  if (data == nullptr) return false;
  return data->unit_ids.find(id) != data->unit_ids.end();
}

bool WhiteListManager::IsCampaignHit(int64_t id) const {
  auto data = GetData();
  if (data == nullptr) return false;
  return data->campaign_ids.find(id) != data->campaign_ids.end();
}

bool WhiteListManager::IsAccountHit(int64_t id) const {
  auto data = GetData();
  if (data == nullptr) return false;
  return data->account_ids.find(id) != data->account_ids.end();
}

bool WhiteListManager::IsSpecialAccountHit(int64_t id) const {
  auto data = GetSpecialData();
  if (data == nullptr) return false;
  return data->all_ids.find(id) != data->all_ids.end();
}

void WhiteListManager::GetSpecialAccountList(const std::string& tag, std::unordered_set<int64_t>* list) {
  list->clear();
  auto data = GetSpecialData();
  if (data == nullptr) return;
  auto iter = data->tag_2_ids.find(tag);
  if (iter == data->tag_2_ids.end()) return;
  list->insert(iter->second.begin(), iter->second.end());
}

std::string WhiteListManager::GetUserNameByCreativeId(int64_t id) const {
  auto data = GetData();
  if (data == nullptr) return "";
  auto iter = data->creative_ids.find(id);
  if (iter == data->creative_ids.end()) return "";
  if (iter->second >= data->users.size()) return "";
  return data->users.at(iter->second);
}

std::string WhiteListManager::GetUserNameByUnitId(int64_t id) const {
  auto data = GetData();
  if (data == nullptr) return "";
  auto iter = data->unit_ids.find(id);
  if (iter == data->unit_ids.end()) return "";
  if (iter->second >= data->users.size()) return "";
  return data->users.at(iter->second);
}

std::string WhiteListManager::GetUserNameByCampaignId(int64_t id) const {
  auto data = GetData();
  if (data == nullptr) return "";
  auto iter = data->campaign_ids.find(id);
  if (iter == data->campaign_ids.end()) return "";
  if (iter->second >= data->users.size()) return "";
  return data->users.at(iter->second);
}

std::string WhiteListManager::GetUserNameByAccountId(int64_t id) const {
  auto data = GetData();
  if (data == nullptr) return "";
  auto iter = data->account_ids.find(id);
  if (iter == data->account_ids.end()) return "";
  if (iter->second >= data->users.size()) return "";
  return data->users.at(iter->second);
}

void WhiteListManager::Update() {
  while (!cw_.Stoped()) {
    auto new_data = std::make_unique<WhiteListData>();
    auto cur_time = absl::Now();
    std::string time_string = absl::FormatTime("%Y%m%d", cur_time, absl::LocalTimeZone());
    LOG(INFO) << "cur time date:" << time_string;
    const auto& white_list_conf = PCKconfUtil::creativeWhiteListConfig()->data().pb();
    // 遍历解析 pb
    for (auto& c : white_list_conf.configs()) {
      if (time_string > c.end()) {
        // 已经过期
        PerfLog::Set(1, "white_list_config", c.user(), "already_expire");
        LOG(INFO) << "fdebug conf expire:" << c.ShortDebugString();
        continue;
      } else if (time_string < c.begin()) {
        // 还未开始
        PerfLog::Set(1, "white_list_config", c.user(), "not_start");
        LOG(INFO) << "fdebug conf expire:" << c.ShortDebugString();
        continue;
      } else {
        PerfLog::Set(1, "white_list_config", c.user(), "in_use");
      }
      // 特别队列单独处理
      if (!c.special_tag().empty()) { continue; }
      int idx = new_data->users.size();
      new_data->users.emplace_back(c.user());
      std::unordered_map<int64_t, int>* container = nullptr;
      if (c.level() == "creative") {
        container = &(new_data->creative_ids);
      } else if (c.level() == "unit") {
        container = &(new_data->unit_ids);
      } else if (c.level() == "campaign") {
        container = &(new_data->campaign_ids);
      } else if (c.level() == "account") {
        container = &(new_data->account_ids);
      }
      if (container == nullptr) continue;
      for (auto id : c.ids()) container->emplace(id, idx);
    }

    DataHolder& bg = data_[!(data_index_.load())];
    bg.raw_data.reset(new_data.release());
    int fg = data_index_.load();
    data_index_.store(!fg);
    // 处理特别队列配置
    ProcessSpecialConfig();
    // 每分钟更新一次
    cw_.WaitForMillisecond(60000);
  }
}

void WhiteListManager::ProcessSpecialConfig() {
  auto new_data = std::make_unique<SpecialWhiteListData>();
  auto cur_time = absl::Now();
  std::string time_string = absl::FormatTime("%Y%m%d", cur_time, absl::LocalTimeZone());
  LOG(INFO) << "cur time date:" << time_string;
  const auto& white_list_conf = PCKconfUtil::creativeWhiteListConfig()->data().pb();
  // 遍历解析 pb
  int total_quota = 0;
  for (auto& c : white_list_conf.configs()) {
    if (time_string > c.end() || time_string < c.begin()) continue;
    int quota = c.special_quota();
    const std::string& tag = c.special_tag();
    // 非特别队列不处理
    if (tag.empty()) { continue; }
    quota = (quota <= 0) ? PCKconfUtil::specialWhiteListQuota() : quota;
    total_quota += quota;
    if (new_data->tag_2_ids.find(tag) == new_data->tag_2_ids.end()) {
      new_data->tag_2_ids.emplace(tag, std::unordered_set<int64_t>());
    }
    if (new_data->tag_2_quota.find(tag) == new_data->tag_2_quota.end()) {
      new_data->tag_2_quota.emplace(tag, quota);
    }
    for (auto id : c.ids()) {
      new_data->all_ids.emplace(id);
      new_data->tag_2_ids[tag].emplace(id);
    }
    // 同 tag 如果有多个配置，取 quota 最大的配置
    new_data->tag_2_quota[tag] = std::max(new_data->tag_2_quota[tag], quota);
  }
  // 如果总 quota 超限，按照比例降低
  if (total_quota > PCKconfUtil::specialWhiteListQuota()) {
    double ratio = PCKconfUtil::specialWhiteListQuota() * 1.0 / total_quota;
    for (auto& item : new_data->tag_2_quota) item.second *= ratio;
  }
  SpecialDataHolder& bg = special_data_[!(special_data_index_.load())];
  bg.raw_data.reset(new_data.release());
  int fg = special_data_index_.load();
  special_data_index_.store(!fg);
}

}  // namespace creative_server
}  // namespace ks
