#pragma once

#include <atomic>
#include <mutex>

#include "base/thread/thread_pool.h"

namespace ks {
namespace creative_server {

// 全局线程池，统一进行多线程处理
// 若某些地方需要进行线程同步完成后才能继续后续处理的可以使用 absl::BlockingCounter 来配合使用
class GlobalThreadPool {
 public:
  static GlobalThreadPool* GetInstance() {
    static GlobalThreadPool instance;
    return &instance;
  }
  ~GlobalThreadPool();

  void AddTask(Closure* task);
  void Stop();

 private:
  GlobalThreadPool();
  void DotPendingTaskNum();
  std::atomic_bool stop_{false};
  thread::ThreadPool pool_;
  mutable std::mutex mtx_;
};

}  // namespace creative_server
}  // namespace ks
