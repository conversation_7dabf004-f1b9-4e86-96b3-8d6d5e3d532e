#pragma once

#include <string>
#include <unordered_map>

namespace ks {
namespace creative_server {

class DeployState {
 public:
  static DeployState& Instance();

  bool IsTestEnv() const { return test_; }

  const std::string& GetServiceName() const {
    return service_name_;
  }

  const std::string& GetStageName() const {
    return stage_name_;
  }

  const std::string& GetKessName() const {
    return kess_name_;
  }

  const std::string& GetPodName() const {
    return pod_name_;
  }

  const int32_t GetShardId() const {
    return shard_id_;
  }

  void Initialize();

 private:
  DeployState() = default;
  ~DeployState() = default;
  DeployState(const DeployState& other) = delete;
  DeployState(const DeployState&& other) = delete;
  DeployState operator=(const DeployState& other) = delete;
  DeployState operator=(const DeployState&& other) = delete;

 private:
  bool test_ {false};
  bool initialized_ {false};
  std::string kess_name_;     // ad-creative-server
  std::string service_name_;  // universe 等
  std::string stage_name_;    // PROD/CANDIDATE
  std::string pod_name_;      // 实例名
  int32_t shard_id_ {-1};
};

}  // namespace creative_server
}  // namespace ks
