#pragma once

#include <vector>
#include <algorithm>
#include <string>

#include "base/hdfs/hdfs.h"
#include "base/file/file_util.h"
#include "serving_base/hdfs_read/hdfs_file_util.h"
#include "serving_base/hdfs_read/hdfs_file_stream.h"

namespace ks {
namespace creative_server {
namespace file_helper {

// 删除 hdfs 指定路径下的数据至保留最新 72 份数据
static inline bool ClearHistoryFile(const std::string& hdfs_path, int reserved_num = 72) {
  std::vector<hadoop::HDFSPathInfo> hdfs_files;
  if (!hadoop::HDFSListDirectory(hdfs_path.c_str(), &hdfs_files)) {
    LOG(ERROR) << "hadoop::HDFSListDirectory(" << hdfs_path << ") failed.";
    return false;
  }
  std::sort(hdfs_files.begin(), hdfs_files.end(),
            [](const hadoop::HDFSPathInfo& a, const hadoop::HDFSPathInfo& b) {
              return a.type > b.type || (a.type == b.type && a.name > b.name);
            });
  int64_t success_num = 0;
  int64_t delete_num = 0;
  for (auto hdfs_file : hdfs_files) {
    std::string path = hdfs_file.name;
    if (hdfs_file.type != hadoop::kDirectory) {
      LOG(ERROR) << "Invalid latest path: " << hdfs_file.name
                 << ", it's not a directory. Delete It!";
      LOG(INFO) << "Path: " << path << " delete!";

      bool ret = hadoop::HDFSRmr(path.c_str());
      delete_num++;
      if (delete_num > 20) {
        return true;
      }
      continue;
    }

    if (path.back() == '/') {
      path.pop_back();
    }
    std::string flag_file = path + "/_SUCCESS";
    if (!hadoop::HDFSExists(flag_file.c_str())) {
      LOG(ERROR) << "flag_file: " << flag_file
                 << " does not exist, maybe not prepared.";
      LOG(INFO) << "Path: " << path << " delete!";
      bool ret = hadoop::HDFSRmr(path.c_str());
      delete_num++;
      if (delete_num > 20) {
        return true;
      }
      continue;
    }
    success_num++;
    if (success_num > reserved_num) {
      bool ret = hadoop::HDFSRmr(path.c_str());
      delete_num++;
      if (delete_num > 20) {
        return true;
      }
      continue;
    }
  }
  return true;
}

// 获取 hadoop 指定路径下的最新版本文件路径
static inline std::string GetLatestHdfsFiles(const std::string& path) {
  std::vector<hadoop::HDFSPathInfo> hdfs_files;
  if (!hadoop::HDFSListDirectory(path.c_str(), &hdfs_files)) {
    LOG(ERROR) << "hadoop::HDFSListDirectory(" << path << ") failed.";
    return "";
  }
  std::sort(hdfs_files.begin(), hdfs_files.end(),
            [](const hadoop::HDFSPathInfo& a, const hadoop::HDFSPathInfo& b) {
              return a.type > b.type || (a.type == b.type && a.name > b.name);
            });
  for (auto hdfs_file : hdfs_files) {
    std::string path = hdfs_file.name;
    if (hdfs_file.type != hadoop::kDirectory) { continue; }
    if (path.back() == '/') { path.pop_back(); }
    std::string flag_file = path + "/_SUCCESS";
    if (!hadoop::HDFSExists(flag_file.c_str())) { continue; }
    return path;
  }
  return "";
}

// 存储 cuckoohash_map 至文件
template<typename LockTableType>
static inline bool SaveCuckoohashMap(const std::string& file, const LockTableType& table) {
  std::ofstream ofstream(file, std::ios::out | std::ios::trunc);
  if (ofstream.is_open()) {
    ofstream << table;
    ofstream.close();
    LOG(INFO) << "Save |" << file << "| succeed, size: " << table.size();
    return true;
  }
  ofstream.close();
  LOG(ERROR) << "Save failure |" << file << "|!";
  return false;
}

// 加载文件至 cuckoohash_map
template<typename LockTableType>
static inline bool LoadCuckoohashMap(const std::string& file, LockTableType* table) {
  std::ifstream ifstream(file, std::ios::in);
  if (ifstream.is_open()) {
    ifstream >> *table;
    LOG(INFO) << "Load |" << file << "| succeed, size: " << table->size();
    ifstream.close();
    return true;
  }
  ifstream.close();
  LOG(ERROR) << "Load failure |" << file << "|!";
  return false;
}

}  // namespace file_helper
}  // namespace creative_server
}  // namespace ks
