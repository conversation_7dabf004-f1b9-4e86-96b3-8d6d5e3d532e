#pragma once

#include <cstdio>
#include <string>
#include <vector>
#include <unordered_set>

#include "serving_base/hdfs_read/hdfs_file_util.h"
#include "serving_base/hdfs_read/hdfs_file_stream.h"

#include "base/strings/string_printf.h"

namespace ks {
namespace creative_server {

enum class CreativeFileLocation {
  UNKNOWN = 0,
  HDFS = 1,
  LOCAL = 2
};

static constexpr char kDownloadDone[] = "creative_server_hdfs_done";

class FileUtil {
 public:
  static std::vector<std::string> GetFiles(const std::string& path);
  static std::vector<std::string> GetLatestFiles(const std::string& path);
  static void DumpLatestHdfsFile(const std::string& src,
                                 const std::string& success_file,
                                 const std::string& dst);
  static void DeleteFile(const std::string& path, int remain_nums);
  static std::string GetLatestDir(const std::string& path_dir, const std::string& done = "");
  static int64_t GetLatestCreativeFileByTables(const std::string& path,
                                               const std::string& info_file,
                                               const std::unordered_set<std::string>& tables,
                                               std::vector<std::string>* files);
  static std::string GetLatestHdfsVersion(const std::string& path, const std::string& success_file);
 private:
  static std::string GetLatestHdfsDir(const std::string& path, const std::string& success_file);
  static std::string GetHdfsPathFromHdfsName(const std::string& path, const std::string& name);
  static bool CheckHasSuccessFile(const std::string& path);
};

}  // namespace creative_server
}  // namespace ks
