#include "teams/ad/ad_creative_server/util/file_util/pb_loader.h"

#include <sys/stat.h>
#include <string>

namespace ks {
namespace creative_server {

bool PbReader::Eof() const {
  if (file_) {
    return feof(file_) != 0 && error_code_ == ReadErrorCode::NO_ERROR;
  }
  return false;
}

size_t PbReader::FileSize() const {
  if (path_ == nullptr) {
    return 0;
  }
  struct stat statbuff;
  if (stat(path_, &statbuff) < 0) {
    return 0;
  }
  return statbuff.st_size;
}

int PbReader::Open(const char* path) {
  Close();
  path_ = path;
  file_ = fopen(path, "rb");
  if (file_ == nullptr) {
    LOG(ERROR) << "open pb read file failed, path: " << path;
    return -1;
  }
  return 0;
}

std::string PbReader::Read() {
  // 读取长度
  uint32_t length = 0;
  size_t read_len = fread(&length, 1, sizeof(uint32_t), file_);
  if (read_len == 0 && Eof()) {
    return "";
  }
  if (read_len != sizeof(uint32_t) || length == 0 || length > kMaxInstanceLength) {
    LOG(ERROR) << "read pb length failed, expect bytes: " << sizeof(uint32_t)
                << ", actual bytes: " << read_len << ", length: " << length;
    return "";
  }
  if (length > kMaxInstanceLength) {
    error_code_ = ReadErrorCode::LEN_TOO_LARGE_ERROR;
    LOG(ERROR) << "pb instance is too large, byte size: " << length
                << ", error_code: " << error_code_;
    return "";
  }
  // 读取实例字节数组
  read_len = fread(buffer_, 1, length, file_);
  if (read_len != length) {
    LOG(ERROR) << "read instance bytes failed, expect bytes: " << length
                << ", actual bytes: " << read_len;
    return "";
  }

  bytes_ += sizeof(uint32_t) + length;
  return std::string(buffer_, length);
}

}  // namespace creative_server
}  // namespace ks
