#include "teams/ad/ad_creative_server/util/file_util/hdfs_reader.h"

#include <sys/stat.h>
#include <string>

namespace ks {
namespace creative_server {

#include <cstring>

bool HdfsPbReader::ReadToBuffer() {
  if (complete_) {
    return complete_;
  }
  char* cur_ptr = buffer_ + cur_index_;
  size_t left_count = valuable_buffer_size_ - cur_index_;
  if (left_count > kMaxPbInstanceLength) {
    return complete_;
  }
  if (left_count > 0) {
    std::memcpy(buffer_, cur_ptr, left_count);
  } else {
    left_count = 0;
  }
  auto writer_ptr = buffer_ + left_count;
  auto read_length = hdfs_.Read(writer_ptr, kHdfsReadLength);
  valuable_buffer_size_ = read_length + left_count;
  cur_index_ = 0;
  if (read_length <= 0) {
    complete_ = true;
  }
  return complete_;
}

}  // namespace creative_server
}  // namespace ks
