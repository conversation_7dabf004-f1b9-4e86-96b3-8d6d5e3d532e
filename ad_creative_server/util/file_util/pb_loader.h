#pragma once

#include <cstdio>
#include <string>

#include "base/common/logging.h"
#include "teams/ad/ad_creative_server/util/file_util/file_code.h"

namespace ks {
namespace creative_server {

// 单个 pb 实例最大长度
const size_t kMaxInstanceLength = 1024 * 1024;

class PbReader {
 public:
  PbReader() : file_(nullptr), bytes_(0) {}
  ~PbReader() {
    Close();
  }

  void Close() {
    if (file_ != nullptr) {
      fclose(file_);
      file_ = nullptr;
    }
    path_ = nullptr;
    bytes_ = 0;
  }

  size_t Bytes() const {
    return bytes_;
  }

  size_t FileSize() const;

  bool Eof() const;

  bool IsReadComplete() const {
    return bytes_ == FileSize();
  }

  int Open(const char* path);

  // 读取解析 pb 对象
  template<typename Pb>
  bool Read(Pb* pb) {
  // 读取长度
  uint32_t length = 0;
  size_t read_len = fread(&length, 1, sizeof(uint32_t), file_);
  if (read_len == 0 && Eof()) {
    return false;
  }
  if (read_len != sizeof(uint32_t) || length == 0 || length > kMaxInstanceLength) {
    LOG(ERROR) << "read pb length failed, expect bytes: " << sizeof(uint32_t)
                << ", actual bytes: " << read_len << ", length: " << length;
    return false;
  }
  if (length > kMaxInstanceLength) {
    error_code_ = ReadErrorCode::LEN_TOO_LARGE_ERROR;
    LOG(ERROR) << "pb instance is too large, byte size: " << length
                << ", error_code: " << error_code_;
    return false;
  }
  // 读取实例字节数组
  read_len = fread(buffer_, 1, length, file_);
  if (read_len != length) {
    LOG(ERROR) << "read instance bytes failed, expect bytes: " << length
                << ", actual bytes: " << read_len;
    return false;
  }
  // 解析 pb
  if (!pb->ParseFromArray(buffer_, length)) {
    error_code_ = ReadErrorCode::PRASE_ERROR;
    LOG(ERROR) << "parse pb from file failed, path:"
                << (path_ == nullptr ? std::string("nullptr") : std::string(path_))
                << ", error_code: " << error_code_;
    return false;
  }
  bytes_ += sizeof(uint32_t) + length;
  return true;
  }

  std::string Read();


 private:
  ReadErrorCode error_code_ { ReadErrorCode::NO_ERROR };
  const char* path_;
  FILE* file_;
  size_t bytes_;
  char buffer_[kMaxInstanceLength];
};

}  // namespace creative_server
}  // namespace ks
