#include "teams/ad/ad_creative_server/util/file_util/file_util.h"

#include <dirent.h>
#include <algorithm>
#include <fstream>
#include <sstream>

#include "base/common/logging.h"
#include "base/hdfs/hdfs.h"
#include "serving_base/util/hdfs.h"
#include "base/file/file_util.h"
#include "serving_base/jansson/json.h"

namespace ks {
namespace creative_server {

std::vector<std::string> FileUtil::GetLatestFiles(const std::string& path) {
  if (path.empty()) {
    return {};
  }
  auto latest_dir = GetLatestDir(path);
  LOG(INFO) << "find latest file path: " << latest_dir;
  return GetFiles(latest_dir);
}

std::vector<std::string> FileUtil::GetFiles(const std::string& path) {
  if (path.empty()) {
    return {};
  }
  struct dirent *dirp;
  DIR* dir = opendir(path.data());
  if (dir == nullptr) {
    LOG(ERROR) << "Path is not exist:" << path;
    return {};
  }
  std::vector<std::string> result;
  while ((dirp = readdir(dir)) != nullptr) {
    std::string name = dirp->d_name;
    if (name != "." && name != "..") {
      auto total_name = path + "/" + name;
      result.emplace_back(total_name);
    }
  }
  closedir(dir);
  if (result.empty()) {
    LOG(INFO) << "Path find no files.";
  }
  return result;
}

std::string FileUtil::GetLatestDir(const std::string& path, const std::string& done) {
  if (path.empty()) {
    return "";
  }
  struct dirent *dirp;
  DIR* dir = opendir(path.c_str());
  if (dir == nullptr) {
    LOG(ERROR) << "Path is not exist:" << path;
    return "";
  }
  std::vector<std::string> result;
  while ((dirp = readdir(dir)) != nullptr) {
    std::string name = dirp->d_name;
    if (name != "." && name != "..") {
      auto total_name = path + "/" + name;
      result.emplace_back(total_name);
    }
  }
  closedir(dir);
  if (result.empty()) {
    LOG(INFO) << "Path find no files : " << path;
    return "";
  }
  std::sort(result.begin(), result.end(), [](const std::string &a, const std::string &b){
    return a > b;
  });
  for (auto &res : result) {
    auto success = res + "/" + kDownloadDone;
    if (!done.empty()) {
      success = res + "/" + done;
    }
    FILE* file = fopen(success.c_str(), "r");
    if (file != nullptr) {
      // LOG(INFO) << "get latest dir:" << res;
      fclose(file);
      return res;
    }
  }
  return "";
}

std::string FileUtil::GetLatestHdfsVersion(const std::string& path, const std::string& success_file) {
  if (path.empty() || success_file.empty()) {
    return "";
  }
  auto dir = GetLatestHdfsDir(path, success_file);
  if (dir.empty()) { return ""; }
  auto version_pos = dir.rfind('/');
  if (version_pos == std::string::npos) {
    return "";
  }
  auto version = dir.substr(version_pos + 1);
  return version;
}

std::string FileUtil::GetLatestHdfsDir(const std::string& path, const std::string& success_file) {
  if (path.empty() || success_file.empty()) {
    return "";
  }
  hdfsFS hdfs = hdfsConnect("default", 0);
  if (hdfs == nullptr) {
    LOG(INFO) << "hdfs connect failed";
    return "";
  }
  int num = 0;
  hdfsFileInfo* dir_info = hdfsListDirectory(hdfs, path.c_str(), &num);
  std::vector<std::string> directoies;
  for (int i = 0; i < num; ++i) {
    char* name = dir_info[i].mName;
    if (hdfsExists(hdfs, name) != -1) {
      directoies.emplace_back(name);
    }
  }
  hdfsFreeFileInfo(dir_info, num);
  num = 0;
  std::sort(directoies.begin(), directoies.end(), [](const std::string& a, const std::string& b){
    return a > b;
  });
  std::string latest_dir = "";
  for (const auto& dir : directoies) {
    auto success = dir + "/" + success_file;
    if (hdfsExists(hdfs, success.c_str()) != -1) {
      latest_dir = GetHdfsPathFromHdfsName(path, dir);
      break;
    }
  }
  LOG(INFO) << "get latest hdfs dir:" << latest_dir;
  hdfsDisconnect(hdfs);
  return latest_dir;
}

std::string FileUtil::GetHdfsPathFromHdfsName(const std::string& path, const std::string& name) {
  if (path.empty() || name.empty()) {
    return "";
  }
  auto pos = name.rfind('/');
  if (pos == std::string::npos) {
    return "";
  }
  return path + name.substr(pos);
}

void FileUtil::DumpLatestHdfsFile(const std::string& src,
                                  const std::string& success_file,
                                  const std::string& dst) {
  if (src.empty() || dst.empty()) {
    return;
  }
  auto latest_dir = GetLatestHdfsDir(src, success_file);
  if (latest_dir.empty()) {
    return;
  }
  auto version_pos = latest_dir.rfind('/');
  if (version_pos == std::string::npos) {
    return;
  }
  auto latest_version = latest_dir.substr(version_pos + 1);
  if (!hadoop::HDFSExists(latest_dir.c_str())) {
    LOG(ERROR) << "path not exist:" << latest_version;
    return;
  }
  LOG(INFO) << "begin download hdfs files:" << latest_dir;
  std::string local_path = dst + "/" + latest_version;
  std::string local_path_tmp = dst + "/0";
  int ret = hadoop::HDFSGet(latest_dir.c_str(), local_path_tmp.c_str());
  if (ret != 0) {
    if (ret == -3) {
      LOG_EVERY_N(INFO, 100) << "note: file already download:" << latest_version;
    } else {
      LOG(WARNING) << "failed: " << ret << ", cannot copy from:" << latest_version << " to:" << local_path;
    }
    return;
  }
  std::string done_path = local_path_tmp + "/" + kDownloadDone;
  std::ofstream done(done_path.c_str(), std::ios::out);
  done.close();
  if (!base::file_util::ReplaceFile(local_path_tmp, local_path)) {
    LOG(WARNING) << "rename tmp file to target file failed:" << local_path_tmp;
  } else {
    LOG(INFO) << "finished download hdfs: " << latest_dir << " to " << local_path;
  }
  base::file_util::Delete(local_path_tmp, true);
  ::google::FlushLogFiles(::google::INFO);
}

// 从给定目录删除文件至剩余 remain_nums 数量
void FileUtil::DeleteFile(const std::string& path, int remain_nums = 6) {
  if (path.empty()) {
    return;
  }
  struct dirent *dirp;
  DIR* dir = opendir(path.c_str());
  if (dir == nullptr) {
    LOG(ERROR) << "Path is not exist:" << path;
    return;
  }
  std::vector<std::string> result;
  while ((dirp = readdir(dir)) != nullptr) {
    std::string name = dirp->d_name;
    if (name != "." && name != "..") {
      auto total_name = path + "/" + name;
      LOG(INFO) << "find local path: " << total_name;
      result.emplace_back(total_name);
    }
  }
  closedir(dir);
  if (result.empty()) {
    LOG(INFO) << "Path find no files.";
    return;
  }
  std::sort(result.begin(), result.end(), [](const std::string &a, const std::string &b){
    return a > b;
  });
  // 首先删除没有下载完整的文件
  result.erase(std::remove_if(result.begin(), result.end(), [](const std::string &dir) {
    if (!CheckHasSuccessFile(dir)) {
      base::file_util::Delete(dir, true);
      LOG(INFO) << "delete uncompleted file dir:" << dir;
      return true;
    }
    return false;
  }), result.end());
  // 然后删除超过数量的过期文件
  if (result.size() <= remain_nums) {
    return;
  }
  for (auto i = result.size() - 1; i >= remain_nums; --i) {
    base::file_util::Delete(result[i], true);
    LOG(INFO) << "delete timeout file dir:" << result[i];
  }
}

bool FileUtil::CheckHasSuccessFile(const std::string& path) {
  if (path.empty()) {
    return true;
  }
  struct dirent *dirp;
  DIR* dir = opendir(path.c_str());
  if (dir == nullptr) {
    LOG(ERROR) << "Path is not exist:" << path;
    return true;
  }
  std::vector<std::string> result;
  while ((dirp = readdir(dir)) != nullptr) {
    std::string name = dirp->d_name;
    if (name == kDownloadDone) {
      closedir(dir);
      return true;
    }
  }
  closedir(dir);
  return false;
}

int64_t FileUtil::GetLatestCreativeFileByTables(const std::string& path,
                                                const std::string& info_file,
                                                const std::unordered_set<std::string>& tables,
                                                std::vector<std::string>* files) {
  files->clear();
  if (path.empty() || tables.empty()) {
    return 0;
  }
  auto latext_files = GetLatestFiles(path);
  if (latext_files.empty()) {
    return 0;
  }
  std::string info_file_path;
  for (auto& file : latext_files) {
    if (file.find(info_file) != std::string::npos) {
      info_file_path = file;
      break;
    }
  }
  if (info_file_path.empty()) {
    return 0;
  }
  auto root_path_pos = info_file_path.rfind('/');
  auto root_path = info_file_path.substr(0, root_path_pos);
  std::string info_str;
  std::ifstream in(info_file_path.c_str(), std::ios::in);
  std::stringstream ss;
  ss << in.rdbuf();
  base::Json dump_info_json(base::StringToJson(ss.str()));
  base::Json* info_json = dump_info_json.Get("info");
  int64_t res = 0;
  if (info_json->IsArray()) {
    auto& arr = info_json->array();
    for (auto& item : arr) {
      LOG(INFO) << base::JsonToString(item->get());
      std::string table_name = item->GetString("tableName", "");
      std::string file_name = item->GetString("fileName", "");
      if (tables.find(table_name) != tables.end()) {
        res += item->GetInt("recordNum", 0);
        files->emplace_back(root_path + "/" + file_name);
      }
    }
  }
  in.close();
  return res;
}

}  // namespace creative_server
}  // namespace ks
