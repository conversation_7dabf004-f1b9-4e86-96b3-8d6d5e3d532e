#pragma once

#include <cstdio>
#include <string>
#include <cstdint>

#include "base/common/logging.h"
#include "serving_base/hdfs_read/hdfs_file_stream.h"
#include "base/common/basic_types.h"

namespace ks {
namespace creative_server {

// 单个 pb 实例最大长度
const size_t kMaxPbInstanceLength = 1024 * 1024;  // 1 MB
const size_t kHdfsReadLength = 1024 * 1024 * 2;  // 2 MB
const size_t kBufferSize = kMaxPbInstanceLength + kHdfsReadLength;

class HdfsPbReader {
 public:
  HdfsPbReader(const char *host, int port) : hdfs_(host, port), cur_index_(0) {
    hdfs_.set_retry_times(3);
    hdfs_.set_retry_interval(1);
  }
  ~HdfsPbReader() {
    Close();
  }

  void Close() {
    hdfs_.Close();
    path_ = nullptr;
    cur_index_ = 0;
    valuable_buffer_size_ = 0;
    complete_ = false;
  }

  bool IsReadComplete() {
    return complete_;
  }

  bool Open(const char* path) {
    path_ = path;
    return hdfs_.Open(path, O_RDONLY);
  }

  // 读取解析 pb 对象
  template<typename Pb>
  bool Read(Pb* pb) {
    if (complete_) {
      return false;
    }
    if (cur_index_ + sizeof(int32) > valuable_buffer_size_) {
      if (ReadToBuffer()) {
        return false;
      }
    }

    char* cur_ptr = buffer_ + cur_index_;
    int32 length = *(reinterpret_cast<int32*>(cur_ptr));
    cur_index_ += 4;
    if (length == 0 || length > kMaxPbInstanceLength) {
      LOG(ERROR) << "read pb length failed: "  << length;
      return false;
    }

    if (cur_index_ + length > valuable_buffer_size_) {
      if (ReadToBuffer()) {
        return false;
      }
    }

    if (!pb->ParseFromArray(buffer_ + cur_index_, length)) {
      LOG(ERROR) << "parse pb from file failed";
      return false;
    }
    cur_index_ += length;
    return true;
  }

 private:
  bool ReadToBuffer();

 private:
  const char* path_;
  hadoop::HDFSFileStream hdfs_;
  size_t cur_index_ {0};
  char buffer_[kBufferSize];
  bool complete_ {false};
  int32 valuable_buffer_size_ {0};
};

}  // namespace creative_server
}  // namespace ks
