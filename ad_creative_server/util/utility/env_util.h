#pragma once

#include <cstdlib>
#include <vector>
#include <string>
#include <stdexcept>

#include "base/common/basic_types.h"
#include "base/strings/string_split.h"
#include "base/common/logging.h"

namespace ks {
namespace creative_server {

static const char kInstanceNameEnvPath[] = "MY_POD_NAME";

// 读取环境变量
static std::string GetEnv(const std::string path) {
  std::string env(getenv(path.c_str()));
  return env;
}


// 读取环境变量
static std::string GetEnv(const char* path) {
  std::string env(getenv(path));
  return env;
}

// 获取实例尾号
static int64 GetInstanceDigit() {
  auto env = GetEnv(kInstanceNameEnvPath);
  std::vector<std::string> tokens;
  base::SplitString(env, "-", &tokens);
  int64 digit = -1;
  try {
    digit = std::stoll(tokens.back());
  } catch (const std::exception) {
    digit = -1;
    LOG(ERROR) << "instance name digit error! name=" << env;
  }
  // LOG(INFO) << "get instance name:" << env
  //           << ", digit=" << digit;
  return digit;
}

}  // namespace creative_server
}  // namespace ks
