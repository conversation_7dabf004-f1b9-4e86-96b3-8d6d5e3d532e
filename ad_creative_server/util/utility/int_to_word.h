#pragma once

#include <vector>
#include <string>

namespace ks {
namespace creative_server {

static std::string IntegerToWordString(int i) {
  static const std::vector<std::string> kIntegerToWordMap = {
    "zero", "one", "two", "three", "four",
    "five", "six", "seven", "eight", "nine",
    "ten", "eleven", "twelve", "thirteen", "fourteen",
    "fifteen", "sixteen", "seventeen", "eighteen", "nineteen"
  };
  if (i < 0 || i >= kIntegerToWordMap.size()) {
    return "";
  }
  return kIntegerToWordMap[i];
}

}  // namespace creative_server
}  // namespace ks
