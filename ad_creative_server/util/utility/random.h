#pragma once

#include <random>
#include "teams/ad/ad_base/src/hash/hash.h"

namespace ks {
namespace creative_server {

// 生成 [a, b) 之间的随机数
static double Random(double a = 0, double b = 1) {
  thread_local std::random_device rd;
  thread_local std::mt19937 gen(rd());
  thread_local std::uniform_real_distribution<> dis(a, b);
  return dis(gen);
}

static double RandomWithSeed(int64_t seed) {
  std::uniform_real_distribution<> dis(0, 1);
  std::mt19937 gen(seed);
  return dis(gen);
}

static double RandomWithSeed(uint64_t seed) {
  std::uniform_real_distribution<> dis(0, 1);
  std::mt19937 gen(seed);
  return dis(gen);
}

}  // namespace creative_server
}  // namespace ks

