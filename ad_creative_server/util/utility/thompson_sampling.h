#pragma once

#include <random>
#include <algorithm>

#include "base/time/timestamp.h"
#include "base/common/basic_types.h"
#include "teams/ad/ad_creative_server/util/utility/beta_distribution.h"

namespace ks {
namespace creative_server {

// 采样
template <class Generator>
static double ThompsonSampling(Generator& g, int64 alpha, int64 beta) {  // NOLINT
  beta = std::max(alpha, beta);
  thread_local BetaDistribution<> beta_distribution(alpha + 1, beta - alpha + 1);
  return beta_distribution(g);
}

static double ThompsonSampling(int64 alpha, int64 beta) {
  thread_local std::random_device rd;
  thread_local std::mt19937 random(rd());
  return ThompsonSampling(random, alpha, beta);
}

static double ThompsonSamplingWithSeed(int64 alpha, int64 beta, int64 seed = 0) {
  beta = std::max(alpha, beta);
  std::mt19937 random(seed == 0 ? base::GetTimestamp() : seed);
  BetaDistribution<> beta_distribution(alpha + 1, beta - alpha + 1);
  return beta_distribution(random);
}

}  // namespace creative_server
}  // namespace ks
