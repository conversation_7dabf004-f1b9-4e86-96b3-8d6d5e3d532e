
#pragma once

#include <string>

namespace ks {
namespace creative_server {

// 注册 redis 集群获取函数
#define REGISTER_REDIS_PIPELINE_CLUSTER_BY_NAME(name, t_ms_out) \
 public:\
  static ks::infra::RedisPipelineClient* name() { \
    return ks::infra::RedisProxyClient::GetRedisPipelineClientByKcc(#name, t_ms_out); \
  }

// 注册 redis 集群获取函数
#define REGISTER_REDIS_CLUSTER_BY_NAME(name, t_ms_out) \
 public:\
  static ks::infra::RedisClient* name() { \
    return ks::infra::RedisProxyClient::GetRedisClientByKcc(#name, t_ms_out); \
  }

}  // namespace creative_server
}  // namespace ks
