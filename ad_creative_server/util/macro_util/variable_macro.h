#pragma once

namespace ks {
namespace creative_server {

// 添加变量的 set 和 get 函数, access_permission 用来控制权限，public，private，protect
#define APPLY_SET_AND_GET(type, name) \
 public:\
  inline void set##name(type v) {\
      m_##name = v;\
  }\
  inline type get##name() {\
      return m_##name;\
  }

// 声明变量，并赋予 get 和 set 函数
#define AD_DYNAMIC_VARIABLE(type, name, access_permission) \
 access_permission:\
  type m_##name;\
 public:\
  inline void Set##name(type v) {\
      m_##name = v;\
  }\
  inline type Get##name() {\
      return m_##name;\
  }

// 声明变量，并赋予 get 和 set 函数
#define AD_DYNAMIC_VARIABLE_DEFAULT(type, name) \
  AD_DYNAMIC_VARIABLE(type, name, private)

}  // namespace creative_server
}  // namespace ks
