#pragma once
#include "libcuckoo/cuckoohash_map.hh"
#include "teams/ad/ad_base/src/hash/hash.h"

namespace ks {
namespace creative_server {

class UnitBidLiftLog {
 public:
  using BidLiftTmType = cuckoohash_map<int64_t, int64_t, ad_base::FNVHash>;
  static UnitBidLiftLog* GetInstance() {
    static UnitBidLiftLog unit_bid_change_log;
    return &unit_bid_change_log;
  }

  // 线程安全
  bool UpdateBidLiftTimestamp(int64_t unit_id, int64_t tm_ms);
  // 线程安全
  bool GetBidLiftTimestamp(int64_t unit_id, int64_t* tm_ms);

  // 非线程安全，使用前调用一次
  bool Recover();
  // 非线程安全，退出前调用一次
  bool Save();

  void Start() {}
  void Stop() {}

  ~UnitBidLiftLog() = default;

 private:
  UnitBidLiftLog() = default;
  BidLiftTmType data_;
};
}  // namespace creative_server
}  // namespace ks
