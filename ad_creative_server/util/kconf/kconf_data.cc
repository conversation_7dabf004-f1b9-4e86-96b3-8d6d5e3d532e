#include "teams/ad/ad_creative_server/util/kconf/kconf_data.h"

#include <string>
#include <sstream>

#include "absl/strings/numbers.h"
#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "base/strings/string_number_conversions.h"
#include "base/common/logging.h"
#include "base/common/map_util-inl.h"
#include "rapidjson/document.h"
#include "rapidjson/error/error.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_creative_server/util/perf_log_util/perf_log_util.h"

namespace ks {
namespace creative_server {

bool CreativeIncPhotoCrop::Init() {
  enable_exp.emplace(1);
  for (auto exp : pb().enable_exp()) {
    if (exp <= 0 || exp >= 8) { continue; }
    enable_exp.emplace(exp);
  }
  for (const auto& c : pb().config()) {
    configured_exp.emplace(c.first);
    dup_crop_ratio[c.first] = c.second.dup_photo_crop_ratio();
    for (const auto& r : c.second.score_crop_ratio()) {
      score_crop_ratio[c.first][r.first] = r.second;
    }
    for (auto s : c.second.white_score()) {
      white_score[c.first].emplace(s);
    }
  }
  return true;
}

bool CreativeIncPhotoCrop::IsWhite(int exp, int score) const {
  auto iter = white_score.find(exp);
  if (iter != white_score.end()) {
    return iter->second.count(score);
  }
  return false;
}

bool CreativeIncPhotoCrop::IsConfigured(int exp) const {
  return configured_exp.count(exp);
}

double CreativeIncPhotoCrop::GetDupCropRatio(int exp) const {
  return FindWithDefault(dup_crop_ratio, exp, 0.0f);
}

double CreativeIncPhotoCrop::GetScoreCropRatio(int exp, int score) const {
  auto iter = score_crop_ratio.find(exp);
  if (iter != score_crop_ratio.end()) {
    return FindWithDefault(iter->second, score, 0.0f);
  }
  return 0.0f;
}

bool AllStrategyConfig::Init() {
  const auto &config = pb().config();
  for (const auto& c : config) {
    active_exp.emplace(c.first);
  }
  return true;
}

// DupPhotoCropConfig -----------------------------------------------
int32 DupPhotoCropConfig::GetBaseQuota(int exp) const {
  auto iter = pb().config().find(exp);
  // 没有找到默认配置 max, 即不限制 quota
  if (iter == pb().config().end()) { return kInt32Max; }
  return iter->second.base_quota();
}

double DupPhotoCropConfig::GetMaxDupRatio(int exp) const {
  auto iter = pb().config().find(exp);
  // 没有找到默认配置 1.0 即不额外重复素材
  if (iter == pb().config().end()) { return 1.0; }
  return iter->second.max_dup_ratio();
}

int64 DupPhotoCropConfig::GetCostThreshold(int exp) const {
  auto iter = pb().config().find(exp);
  // 没有找到默认配置 -max, 即不通过消耗限制 quota
  if (iter == pb().config().end()) { return -1 * kInt32Max; }
  return iter->second.cost_threshold();
}

int32 DupPhotoCropConfig::CalculateQuota(int exp, int64 cost) const {
  auto base_quota = GetBaseQuota(exp);
  if (base_quota == kInt32Max) return kInt32Max;
  auto threshold = GetCostThreshold(exp);
  if (threshold == kInt32Max * -1) return kInt32Max;
  double ec = GetExpansionCoefficient(exp);
  int64 cost_diff = (cost - threshold) / 1000;  // 换算成元
  cost_diff = (cost_diff <= 0) ? 0 : cost_diff;
  return static_cast<int64>(ec * (base_quota + 40 * cost_diff / 1000));
}

double DupPhotoCropConfig::GetMaxCropRatio(int exp) const {
  auto iter = pb().config().find(exp);
  // 没有找到默认配置 0.0 即不裁剪
  if (iter == pb().config().end()) { return 0.0; }
  return iter->second.max_crop_ratio();
}

double DupPhotoCropConfig::GetExpansionCoefficient(int exp) const {
  auto iter = pb().config().find(exp);
  // 没有找到默认配置 1.0 即不膨胀
  if (iter == pb().config().end()) { return 1.0; }
  return iter->second.expansion_coefficient();
}

double DupPhotoCropConfig::GetIncTimeCoefficient(int exp, int hour) const {
  auto iter = pb().config().find(exp);
  // 没有找到默认配置 1.0
  if (iter == pb().config().end()) { return 1.0; }
  if (iter->second.inc_time_expansion_size() <= hour) { return 1.0; }
  return iter->second.inc_time_expansion(hour);
}

double DupPhotoCropConfig::GetFullSelectQuotaPercent(int exp) const {
  auto iter = pb().config().find(exp);
  // 没有找到默认配置 1.0 即不膨胀
  if (iter == pb().config().end()) { return 1.0; }
  // 字段没有配置, 则默认 1.0
  if (iter->second.select_quota_percent() == 0) { return 1.0; }
  return iter->second.select_quota_percent();
}

double DupPhotoCropConfig::GetBidUpQuotaPercent(int exp) const {
  auto iter = pb().config().find(exp);
  // 没有找到默认配置 1.0 即不膨胀
  if (iter == pb().config().end()) { return -1.0; }
  // 字段没有配置, 则默认 1.0
  if (iter->second.bid_up_inc_quota_percent() == 0) { return -1.0; }
  return iter->second.bid_up_inc_quota_percent();
}

// DppPhotoCropConfig -----------------------------------------------
int32 DppPhotoCropConfig::GetBaseQuota(int exp) const {
  auto iter = pb().config().find(exp);
  // 没有找到, 默认配置 100
  if (iter == pb().config().end()) { return 100; }
  return iter->second.base_quota();
}

double DppPhotoCropConfig::GetMaxDupRatio(int exp) const {
  auto iter = pb().config().find(exp);
  // 没有找到默认配置 1.0 即不额外重复素材
  if (iter == pb().config().end()) { return 1.0; }
  return iter->second.max_dup_ratio();
}

int32 DppPhotoCropConfig::GetBaseQuotaCostThresh(int exp) const {
  auto iter = pb().config().find(exp);
  // 没有找到默认配置 -max, 即不通过消耗限制 quota
  if (iter == pb().config().end()) { return -1 * kInt32Max; }
  return iter->second.base_quota_cost_thresh();
}

int32 DppPhotoCropConfig::CalculateQuota(int exp, int64 cost) const {
  auto base_quota = GetBaseQuota(exp);
  auto threshold = GetBaseQuotaCostThresh(exp);
  int64 cost_diff = cost / 1000 - threshold;  // 元
  cost_diff = (cost_diff <= 0) ? 0 : cost_diff;
  return static_cast<int64>(base_quota + GetQuotaPer1kCost(exp) * cost_diff / 1000);
}

int32 DppPhotoCropConfig::GetQuotaPer1kCost(int exp) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return 20; }
  return iter->second.quota_per_1k_cost();
}

double DppPhotoCropConfig::GetMaxCropRatio(int exp) const {
  auto iter = pb().config().find(exp);
  // 没有找到默认配置 0.0 即不裁剪
  if (iter == pb().config().end()) { return 0.0; }
  return iter->second.max_crop_ratio();
}

double DppPhotoCropConfig::GetIncQuotaRatio(int exp) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return 1.0; }
  return iter->second.inc_quota_ratio();
}

double DppPhotoCropConfig::GetIncTimeCoefficient(int exp, int hour) const {
  auto iter = pb().config().find(exp);
  // 没有找到默认配置 1.0
  if (iter == pb().config().end()) { return 1.0; }
  if (iter->second.inc_time_expansion_size() <= hour) { return 1.0; }
  return iter->second.inc_time_expansion(hour);
}

double DppPhotoCropConfig::GetFullQuotaRatio(int exp) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return 1.0; }
  // 字段没有配置, 则默认 1.0
  if (iter->second.full_quota_ratio() == 0) { return 1.0; }
  return iter->second.full_quota_ratio();
}

int32 DppPhotoCropConfig::GetOverrideByExpId(int exp) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return -1; }
  return iter->second.override_by_exp_id();
}

void DppPhotoCropConfig::GetPhotoEmbVersion(int exp, std::string *photo_emb_version) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return; }
  *photo_emb_version = iter->second.photo_emb_version();
}

int32 DppPhotoCropConfig::GetPreSelectNum(int exp) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return -1; }
  return iter->second.pre_select_num();
}

bool DppPhotoCropConfig::GetIsSkipWhitelist(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? false
                                     : iter->second.is_skip_whitelist();
}

double DppPhotoCropConfig::GetDppTheta(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? 0.5
                                     : iter->second.dpp_theta();
}

double DppPhotoCropConfig::GetMinQuotaRatio(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? 0.2
                                     : iter->second.min_quota_ratio();
}

double DppPhotoCropConfig::GetMaxQuotaRatio(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? 0.9
                                     : iter->second.max_quota_ratio();
}

int DppPhotoCropConfig::GetCostYuanDivideFactor(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? 1
                                     : iter->second.cost_yuan_divide_factor();
}

double DppPhotoCropConfig::GetDi2ExitThresh(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? 1e-10
                                     : iter->second.di2_exit_thresh();
}

double DppPhotoCropConfig::GetMinReward(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? 1e-4
                                     : iter->second.min_reward();
}

bool DppPhotoCropConfig::GetDebugByCase(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? false
                                     : iter->second.debug_by_case();
}

bool DppPhotoCropConfig::GetEnableDpp(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? false
                                     : iter->second.enable_dpp();
}

bool DppPhotoCropConfig::GetEnableDppWithoutTheta(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? false
                                     : iter->second.enable_dpp_without_theta();
}

int32 DppPhotoCropConfig::GetBidUpUnitQuota(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? 0
                                     : iter->second.bid_up_unit_quota();
}

bool DppPhotoCropConfig::GetEnableRandCluster(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? false
                                     : iter->second.enable_rand_cluster();
}

bool DppPhotoCropConfig::GetEnableStoreEmb(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? false
                                     : iter->second.enable_store_emb();
}

double DppPhotoCropConfig::GetKmeansConvergeDiffRatio(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? 0.01 :
    (iter->second.kmeans_converge_diff_ratio() < 1e-8 ? 0.01 : iter->second.kmeans_converge_diff_ratio());
}

int32 DppPhotoCropConfig::GetMaxClusterSize(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? 600 :
         (iter->second.max_cluster_size() == 0 ? 600 : iter->second.max_cluster_size());
}

bool DppPhotoCropConfig::GetCropByProduct() const {
  return pb().crop_by_product();
}

bool DppPhotoCropConfig::GetUseWeightedProductQuota(int exp) const {
  auto iter = pb().config().find(exp);
  return iter == pb().config().end() ? false
                                     : iter->second.use_weighted_product_quota();
}

int32 DppPhotoCropConfig::GetEmbMissRecallNum(int exp) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return 0; }
  return iter->second.emb_miss_recall_num();
}

int32 DppPhotoCropConfig::GetEmbMissRecallCostThresh(int exp) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return 1000; }
  return iter->second.emb_miss_recall_cost_thresh();
}

int32 DppPhotoCropConfig::GetKmeansMaxIterations(int exp) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return 100; }
  return iter->second.kmeans_max_iterations();
}

int32 DppPhotoCropConfig::GetDupCostThresh(int exp) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return 10; }
  return iter->second.dup_cost_thresh();
}

int32 DppPhotoCropConfig::GetNewPhotoFreeHour(int exp) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return 6; }
  return iter->second.new_photo_free_hour();
}

double DppPhotoCropConfig::GetCpmQuotaRatioOfCost(int exp) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return 1.0; }
  return iter->second.cpm_quota_ratio_of_cost();
}

double DppPhotoCropConfig::GetMinPhotoRemainRatio(int exp) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return 1.0; }
  return iter->second.min_photo_remain_ratio();
}

int32 DppPhotoCropConfig::GetNewPhotoFreeMaxNum(int exp) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return 10; }
  return iter->second.new_photo_free_max_num();
}

int32 DppPhotoCropConfig::GetCostScoreStatHour() const {
  return pb().cost_score_stat_hour() == 0 ? 4 : pb().cost_score_stat_hour();
}

int32 DppPhotoCropConfig::GetHighCostChannelThresh(int exp) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return 50; }
  return iter->second.high_cost_channel_thresh();
}

void DppPhotoCropConfig::GetDebugProducts(int exp, std::unordered_set<std::string> *products) const {
  auto iter = pb().config().find(exp);
  if (iter == pb().config().end()) { return; }
  for (const auto &product : iter->second.debug_products()) {
    products->emplace(product);
  }
}

bool CreativeWhiteListConfig::Init() {
  auto cur_time = absl::Now();
  std::string time_string = absl::FormatTime("%Y%m%d", cur_time, absl::LocalTimeZone());
  LOG(INFO) << "cur time date:" << time_string;
  for (auto& c : pb().configs()) {
    if (time_string < c.begin() || time_string > c.end()) {
      PerfLog::Set(1, "white_list_expire", c.user());
      continue;
    }
    int idx = users.size();
    users.emplace_back(c.user());
    std::unordered_map<int64_t, int>* container = nullptr;
    if (c.level() == "creative") {
      container = &creative_ids;
    } else if (c.level() == "unit") {
      container = &unit_ids;
    } else if (c.level() == "campaign") {
      container = &campaign_ids;
    } else if (c.level() == "account") {
      container = &account_ids;
    }
    if (container != nullptr) {
      for (auto id : c.ids()) {
        container->emplace(id, idx);
      }
    }
  }
  return true;
}

bool CreativeWhiteListConfig::IsCreativeHit(int64_t id) const {
  return creative_ids.find(id) != creative_ids.end();
}

bool CreativeWhiteListConfig::IsUnitHit(int64_t id) const {
  return unit_ids.find(id) != unit_ids.end();
}

bool CreativeWhiteListConfig::IsCampaignHit(int64_t id) const {
  return campaign_ids.find(id) != campaign_ids.end();
}

bool CreativeWhiteListConfig::IsAccountHit(int64_t id) const {
  return account_ids.find(id) != account_ids.end();
}

std::string CreativeWhiteListConfig::GetUserNameByCreativeId(int64_t id) const {
  auto iter = creative_ids.find(id);
  if (iter == creative_ids.end()) return "";
  if (iter->second >= users.size()) return "";
  return users.at(iter->second);
}

std::string CreativeWhiteListConfig::GetUserNameByUnitId(int64_t id) const {
  auto iter = unit_ids.find(id);
  if (iter == unit_ids.end()) return "";
  if (iter->second >= users.size()) return "";
  return users.at(iter->second);
}

std::string CreativeWhiteListConfig::GetUserNameByCampaignId(int64_t id) const {
  auto iter = campaign_ids.find(id);
  if (iter == campaign_ids.end()) return "";
  if (iter->second >= users.size()) return "";
  return users.at(iter->second);
}

std::string CreativeWhiteListConfig::GetUserNameByAccountId(int64_t id) const {
  auto iter = account_ids.find(id);
  if (iter == account_ids.end()) return "";
  if (iter->second >= users.size()) return "";
  return users.at(iter->second);
}

bool RiskLabelTagConfig::Init() {
  for (auto& t : pb().tags()) {
    tag_labels[t.tag()] = std::unordered_set<int64_t>();
    for (auto l : t.risk_labels()) {
      tag_labels[t.tag()].emplace(l);
      label_tags.emplace(l, t.tag());
      LOG(INFO) << "risk tag, label:" << l << ", tag:" << t.tag();
    }
  }
  return true;
}

int RiskLabelTagConfig::GetTagByLabel(int64_t l) const {
  return FindWithDefault(label_tags, l, 0);
}

}  // namespace creative_server
}  // namespace ks
