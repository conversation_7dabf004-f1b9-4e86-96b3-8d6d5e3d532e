#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>

#include "base/common/basic_types.h"
#include "teams/ad/ad_creative_server/util/kconf/kconf_data.pb.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"

namespace base {
class Json;
}  // namespace base

namespace Json {
class Value;
}  // namespace Json

namespace ks {
namespace creative_server {

struct HdfsDownloadConfig : public ks::ad_base::kconf::KconfInitProto<kconf::HdfsDownloadConfig> {
  HdfsDownloadConfig() = default;
  virtual ~HdfsDownloadConfig() {}
  bool Init() override { return true; }
};

struct UniverseFilteredRoaringType : public
         ks::ad_base::kconf::KconfInitProto<kconf::UniverseFilteredRoaringType> {
  UniverseFilteredRoaringType() = default;
  virtual ~UniverseFilteredRoaringType() {}
  bool Init() override { return true; }
};

struct AllStrategyConfig : public ks::ad_base::kconf::KconfInitProto<kconf::AllStrategyConfig> {
  AllStrategyConfig() = default;
  virtual ~AllStrategyConfig() {}
 public:
  bool Init() override;
  const bool IsExpActive(int exp) const { return active_exp.count(exp); }
  const std::unordered_set<int>& GetActiveExp() const { return active_exp; }
 private:
  std::unordered_set<int> active_exp;
};

struct PostCPMThreshConfig : public ks::ad_base::kconf::KconfInitProto<kconf::PostCPMThreshConfig> {
  PostCPMThreshConfig() = default;
  virtual ~PostCPMThreshConfig() {}
  bool Init() override { return true; }
};

struct ExpZombieCreativeConfig : public ks::ad_base::kconf::KconfInitProto<kconf::ExpZombieCreativeConfig> {
  ExpZombieCreativeConfig() = default;
  virtual ~ExpZombieCreativeConfig() {}
  bool Init() override { return true; }
};

struct CreativeIncWindow : public ks::ad_base::kconf::KconfInitProto<kconf::CreativeIncWindow> {
  CreativeIncWindow() = default;
  virtual ~CreativeIncWindow() {}
  bool Init() override { return true; }
  int GetTimeWindow() const { return pb().time_seconds(); }
  int GetLimitPerUnit() const { return pb().inc_num_per_unit(); }
};

struct CreativeIncPhotoCrop : public ks::ad_base::kconf::KconfInitProto<kconf::CreativeIncPhotoCrop> {
  CreativeIncPhotoCrop() = default;
  virtual ~CreativeIncPhotoCrop() {}
  bool Init() override;
  double GetDupCropRatio(int exp) const;
  double GetScoreCropRatio(int exp, int score) const;
  bool IsWhite(int exp, int score) const;
  bool IsConfigured(int exp) const;
  const std::unordered_set<int>& GetEnableExpTag() const { return enable_exp; }

 private:
  std::unordered_map<int, double> dup_crop_ratio;
  std::unordered_map<int, std::unordered_map<int, double>> score_crop_ratio;
  std::unordered_map<int, std::unordered_set<int>> white_score;
  std::unordered_set<int> enable_exp;
  std::unordered_set<int> configured_exp;
};

struct DupPhotoCropConfig : public ks::ad_base::kconf::KconfInitProto<kconf::DupPhotoCropConfig> {
  DupPhotoCropConfig() = default;
  virtual ~DupPhotoCropConfig() {}
  bool Init() override { return true; }
  int32 GetBaseQuota(int exp) const;
  double GetMaxDupRatio(int exp) const;
  double GetMaxCropRatio(int exp) const;
  double GetExpansionCoefficient(int exp) const;
  double GetFullSelectQuotaPercent(int exp) const;
  double GetBidUpQuotaPercent(int exp) const;
  int64 GetCostThreshold(int exp) const;
  int32 CalculateQuota(int exp, int64 cost) const;
  double GetIncTimeCoefficient(int exp, int hour) const;
};

struct DppPhotoCropConfig : public ks::ad_base::kconf::KconfInitProto<kconf::DppPhotoCropConfig> {
  DppPhotoCropConfig() = default;
  virtual ~DppPhotoCropConfig() {}
  bool Init() override { return true; }
  bool GetIsSkipWhitelist(int exp) const;
  bool GetDebugByCase(int exp) const;
  bool GetEnableDpp(int exp) const;
  bool GetEnableDppWithoutTheta(int exp) const;
  bool GetCropByProduct() const;
  bool GetEnableRandCluster(int exp) const;
  bool GetEnableStoreEmb(int exp) const;
  bool GetUseWeightedProductQuota(int exp) const;
  int32 GetCostScoreStatHour() const;
  double GetMaxDupRatio(int exp) const;
  double GetMaxCropRatio(int exp) const;
  double GetFullQuotaRatio(int exp) const;
  double GetIncQuotaRatio(int exp) const;
  int32 GetBaseQuota(int exp) const;
  int32 GetBaseQuotaCostThresh(int exp) const;
  int32 CalculateQuota(int exp, int64 cost) const;
  double GetIncTimeCoefficient(int exp, int hour) const;
  int32 GetOverrideByExpId(int exp) const;
  void GetPhotoEmbVersion(int exp, std::string *photo_emb_version) const;
  int32 GetPreSelectNum(int exp) const;
  double GetDppTheta(int exp) const;
  double GetMaxDailyCostTimeRatio(int exp) const;
  double GetMinQuotaRatio(int exp) const;
  double GetMaxQuotaRatio(int exp) const;
  int GetCostYuanDivideFactor(int exp) const;
  int GetMaxCandidateNum(int exp) const;
  double GetDi2ExitThresh(int exp) const;
  double GetMinReward(int exp) const;
  double GetKmeansConvergeDiffRatio(int exp) const;
  int32 GetMaxClusterSize(int exp) const;
  int32 GetEmbMissRecallNum(int exp) const;
  int32 GetEmbMissRecallCostThresh(int exp) const;
  int32 GetKmeansMaxIterations(int exp) const;
  int32 GetQuotaPer1kCost(int exp) const;
  int32 GetDupCostThresh(int exp) const;
  int32 GetNewPhotoFreeHour(int exp) const;
  double GetCpmQuotaRatioOfCost(int exp) const;
  double GetMinPhotoRemainRatio(int exp) const;
  int32 GetNewPhotoFreeMaxNum(int exp) const;
  int32 GetHighCostChannelThresh(int exp) const;
  int32 GetBidUpUnitQuota(int exp) const;
  void GetDebugProducts(int exp, std::unordered_set<std::string> *products) const;
};

struct DppAppendPhotos : public ks::ad_base::kconf::KconfInitProto<kconf::DppAppendPhotos> {
  DppAppendPhotos() = default;
  virtual ~DppAppendPhotos() {}
  bool Init() override { return true; }
};

struct DppAppendAccounts : public ks::ad_base::kconf::KconfInitProto<kconf::DppAppendAccounts> {
  DppAppendAccounts() = default;
  virtual ~DppAppendAccounts() {}
  bool Init() override { return true; }
};

struct DppAppendAccountsWithSampling : public ks::ad_base::kconf::KconfInitProto<kconf::DppAppendAccountsWithSampling> {  // NOLINT
  DppAppendAccountsWithSampling() = default;
  virtual ~DppAppendAccountsWithSampling() {}
  bool Init() override { return true; }
};

struct DppAppendProducts : public ks::ad_base::kconf::KconfInitProto<kconf::DppAppendProducts> {
  DppAppendProducts() = default;
  virtual ~DppAppendProducts() {}
  bool Init() override { return true; }
};

struct AccountQuotaConfig : public ks::ad_base::kconf::KconfInitProto<kconf::AccountQuotaConfig> {
  AccountQuotaConfig() = default;
  virtual ~AccountQuotaConfig() {}
  bool Init() override { return true; }
};

struct InternalNewCreativeThreshold : public ks::ad_base::kconf::KconfInitProto<kconf::InternalNewCreativeThreshold> {    // NOLINT
  InternalNewCreativeThreshold() = default;
  virtual ~InternalNewCreativeThreshold() {}
  bool Init() override { return true; }
};

struct ExternalNewPhotoThres : public ks::ad_base::kconf::KconfInitProto<kconf::ExternalNewPhotoThres> {    // NOLINT
    ExternalNewPhotoThres() = default;
  virtual ~ExternalNewPhotoThres() {}
  bool Init() override { return true; }
};

struct SearchIncCropConfig : public ks::ad_base::kconf::KconfInitProto<kconf::SearchIncCropConfig> {
  SearchIncCropConfig() = default;
  virtual ~SearchIncCropConfig() {}
  bool Init() override { return true; }
};

struct FanstopNewCreativeConfig : public ks::ad_base::kconf::KconfInitProto<kconf::FanstopNewCreativeConfig> {
  FanstopNewCreativeConfig() = default;
  virtual ~FanstopNewCreativeConfig() {}
  bool Init() override { return true; }
};

struct InEfficientCropConfig : public ks::ad_base::kconf::KconfInitProto<kconf::InEfficientCropConfig> {
  InEfficientCropConfig() = default;
  virtual ~InEfficientCropConfig() {}
  bool Init() override { return true; }
};

struct DuplicateCropConfig : public ks::ad_base::kconf::KconfInitProto<kconf::DuplicateCropConfig> {
  DuplicateCropConfig() = default;
  virtual ~DuplicateCropConfig() {}
  bool Init() override { return true; }
};

struct CreativeWhiteListConfig : public ks::ad_base::kconf::KconfInitProto<kconf::CreativeWhiteListConfig> {
  CreativeWhiteListConfig() = default;
  virtual ~CreativeWhiteListConfig() {}
  bool Init() override;
  bool IsCreativeHit(int64_t id) const;
  bool IsUnitHit(int64_t id) const;
  bool IsCampaignHit(int64_t id) const;
  bool IsAccountHit(int64_t id) const;
  std::string GetUserNameByCreativeId(int64_t id) const;
  std::string GetUserNameByUnitId(int64_t id) const;
  std::string GetUserNameByCampaignId(int64_t id) const;
  std::string GetUserNameByAccountId(int64_t id) const;
 private:
  std::unordered_map<int64_t, int> creative_ids;
  std::unordered_map<int64_t, int> unit_ids;
  std::unordered_map<int64_t, int> campaign_ids;
  std::unordered_map<int64_t, int> account_ids;
  std::vector<std::string> users;
};

struct RiskLabelTagConfig : public ks::ad_base::kconf::KconfInitProto<kconf::RiskLabelTagConfig> {
  RiskLabelTagConfig() = default;
  virtual ~RiskLabelTagConfig() {}
  bool Init() override;
  int GetTagByLabel(int64_t l) const;
  bool HasLabel(int64_t l) const { return label_tags.find(l) != label_tags.end(); }
 private:
  std::unordered_map<int, std::unordered_set<int64_t>> tag_labels;
  std::unordered_map<int64_t, int> label_tags;
};

}  // namespace creative_server
}  // namespace ks
