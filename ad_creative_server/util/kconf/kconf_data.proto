syntax = "proto3";
option cc_enable_arenas = true;
package ks.creative_server.kconf;

message Hdfs2LocalFilePath {
  message Hdfs2LocalItem {
    string hdfs = 1;                               // hdfs 路径
    string local = 2;                              // 本地存储路径
    string type_name = 3;                          // 文件类型    [Deserted]
    string done = 4;                               // 成功标志
    string flag_name = 5;                           // 加载完成标记名称 [Deserted]
    bool is_test = 6;                              // 是否是测试
    bool sharded = 7;                              // 是否分片
    string data_name = 8;                          // 数据名称
  }

  message Hdfs2Local {
    repeated Hdfs2LocalItem items = 1;
  }

  map<string, Hdfs2Local> paths = 1;
}

message HdfsDownloadConfig {
  message Config {
    string hdfs_src = 1;                              // hdfs 路径
    string local_dir = 2;                             // 本地存储路径
    string success = 4;                               // 成功标志
    bool is_test = 6;                                 // 是否是测试
    bool sharded = 7;                                 // 是否分片
    int32 alarm_threshold = 8;                        // 报警阈值，单位分钟
  }

  repeated Config configs = 1;
}

// 新全策略配置
message AllStrategyConfig {
  // 打分配置
  message ScorerConfig {
    repeated int32 scorer = 1;
    repeated int32 filter = 2;

    string ext_info = 100;
  }

  // 优选池配置
  message ProcessorConfig {
    string name = 1;
    int32 type_id = 2;
    int32 capacity = 3;
    int32 creative_label = 4;
    ScorerConfig scorer = 5;
    repeated int32 creative_type = 6;     // 加载的创意类型
    repeated int32 derivative_type = 7;   // 加载的衍生创意类型
    double quota_scale = 8;               // 细分 quota 配置缩减比例, 优先级比外面的高

    string ext_info = 100;
  }
  // 裁剪配置
  message CropConfig {
    int32 type_id = 1;
    repeated int32 filter = 2;

    string ext_info = 100;
  }
  // 策略配置
  message SrategyConfig {
    string name = 1;
    int32 priority = 2;
    int32 freq = 3;
    repeated ProcessorConfig processor = 4;
    repeated CropConfig cropper = 5;
    double quota_scale = 6;                   // quota 配置缩减比例
    string hosting_live_select = 7;
    string hosting_photo_select = 8;
    string live_hosting_photo_select = 9;

    string ext_info = 100;
  }

  // key : exp_tag, value : strategy config
  map<int32, SrategyConfig> config = 1;
}

message PostCPMThreshConfig {
  message ThreshConfig {
    int64 impr_thresh = 1;
    double cpm_thresh = 2;
  }
  map<int32, ThreshConfig> advanced_config = 1;
  map<int32, ThreshConfig> customize_config = 2;
}

message ExpZombieCreativeConfig {
  message Config {
    // 需要判断的僵尸标记位，取值 [1-4]
    repeated int32 zombie_flag = 1;
    // 不同 flag 间的关系, 1 : and, 2 : or
    int32 op = 2;
  }

  // key : exp tag, value : Config
  map<int32, Config> config = 1;
}

message CreativeIncWindow {
  int32 time_seconds = 1;
  int32 inc_num_per_unit = 2;
}

message CreativeIncPhotoCrop {
  message CropConfig {
    double dup_photo_crop_ratio = 1;
    repeated int32 white_score = 2;
    map<int32, double> score_crop_ratio = 3;
  }
  map<int32, CropConfig> config = 1;
  repeated int32 enable_exp = 2;
}

message DupPhotoCropConfig {
  message CropConfig {
    int32 base_quota = 1;                         // 基础 quota
    int64 cost_threshold = 2;                     // 消耗阈值, 高于阈值计算 quota, 低于阈值使用基础 quota
    double max_dup_ratio = 3;                     // 重复素材允许的最高占比, 高于此比例需要裁剪重复素材
    double max_crop_ratio = 4;                    // 裁剪数量比例上限
    double expansion_coefficient = 5;             // 膨胀系数
    double select_quota_percent = 6;              // 全量优选 quota 占比
    repeated double inc_time_expansion = 7;       // 增量优选按时间 quota 系数
    double bid_up_inc_quota_percent = 8;          // 提价流的 quota 比例
  }
  map<int32, CropConfig> config = 1;    // 分实验级别配置, 便于 ab 实验分析
}

message DppPhotoCropConfig {
  message CropConfig {
    int32 base_quota = 1;                         // 基础 quota
    int32 base_quota_cost_thresh = 2;             // 消耗阈值, 元, 高于阈值计算 quota, 低于阈值使用基础 quota
    double full_quota_ratio = 3;                  // 全量优选 quota 系数
    double inc_quota_ratio = 4;                   // 增量 quota 系数
    double min_quota_ratio = 5;                   // min_quota_ratio based on quota by cost
    double max_quota_ratio = 6;                   // max_quota_ratio based on quota by cost
    int32 emb_miss_recall_num = 7;                // embedding 缺失的单独保留的最大 quota
    int32 emb_miss_recall_cost_thresh = 8;        // embedding 缺失的 photo 超过一定消耗阈值才有机会保留 (yuan)
    int32 pre_select_num = 9;                     // dpp 初始化选取 cost 排序 topN
    int32 override_by_exp_id = 10;                // 跳过计算，直接复用 exp_n 的裁剪结果，0 表示不跳过
    string photo_emb_version = 11;                // photo embedding version (as redis prefix)
    bool is_skip_whitelist = 12;                  // if skip kconf whitelist
    bool debug_by_case = 13;                      // output mid variable for debug
    bool enable_dpp = 14;                         // dpp replace dup_photo_cropper
    double dpp_theta = 15;                        // theta * cost + (1 - theta) * diversity
    int32 cost_yuan_divide_factor = 16;           // reward = cost_yuan / cost_yuan_divide_factor
    double di2_exit_thresh = 17;                  // min di2 for dpp to exit
    double min_reward = 18;                       // min reward for numerical stability
    int32 kmeans_max_iterations = 19;             // max iterations of kmeans
    double kmeans_converge_diff_ratio = 20;       // point change percent thresh for kmeans
    int32 max_cluster_size = 21;                  // max size of kmeans cluster result
    double max_dup_ratio = 22;                    // 重复素材允许的最高占比, 高于此比例需要裁剪重复素材
    double max_crop_ratio = 23;                   // 裁剪数量比例上限
    repeated double inc_time_expansion = 24;      // 增量优选按时间 quota 系数
    int32 quota_per_1k_cost = 25;                 // for quota by cost
    double dup_cost_thresh = 26;                  // dup photo 小于该 cost(yuan) 则删除
    int32 new_photo_free_hour = 27;               // new photo 豁免时间
    double cpm_quota_ratio_of_cost = 28;          // cpm channel quota ratio of cost channel
    int32 new_photo_free_max_num = 29;            // new photo 最多进多少个
    int32 high_cost_channel_thresh = 30;          // 高消耗单独 quota
    repeated string debug_products = 31;          // 输出 product 裁剪详细过程
    bool enable_dpp_without_theta = 32;           // [tmp] 老版不带 theta 的 dpp
    bool enable_rand_cluster = 33;                // random cluster instead of kmeans
    bool enable_store_emb = 34;                   // store emb for debug/stat
    int32 bid_up_unit_quota = 35;                 // 对于在全量计算中被裁的, 可以保留的出价最高的 unit 的数量
    bool use_p2p_post_data = 36;                  // [废弃] 是否使用 hive p2p 的后验数据
    double min_photo_remain_ratio = 37;           // photo数 最低保留比例
    bool use_weighted_product_quota = 38;         // rt, use weight between cost quota and ratio quota
  }
  map<int32, CropConfig> config = 1;    // 分实验级别配置, 便于 ab 实验分析
  bool crop_by_product = 2;             // [废弃] 目前都按 product 裁剪
  int32 cost_score_stat_hour = 3;       // photo cost 的统计粒度(目前 4 小时)，需要折算到 24h
}

// for dpp append
message DppAppendPhotos {
  message PhotoInfo {
    repeated int64 photo_ids = 1;  // 爬坡 photo_id 列表
    string expire_date = 2;       // 失效日期
    string reason = 3;            // 加爬坡原因
  }
  repeated PhotoInfo append_list = 1;
}

message DppAppendAccounts {
  message AccountInfo {
    int64 account_id = 1;
    string expire_date = 2;       // 失效日期
    string reason = 3;            // 加爬坡原因
    repeated int64 account_ids = 4;
  }
  repeated AccountInfo append_list = 1;
}

message DppAppendAccountsWithSampling {
  message AccountInfo {
    int64 account_id = 1;
    string expire_date = 2;       // 失效日期
    string reason = 3;            // 加爬坡原因
    int64 sample_cnt = 4;         // 采样数
    repeated int64 account_ids = 5;
  }
  repeated AccountInfo append_list = 1;
}

message DppAppendProducts {
  message ProductInfo {
    repeated string products = 1;
    string expire_date = 2;       // 失效日期
    string reason = 3;            // 加爬坡原因
  }
  repeated ProductInfo append_list = 1;
}

message PhotoCostCpm {
  double cost = 1;
  double cpm = 2;
}

message UniverseFilteredRoaringType {
  message UniverseFilteredRoaringTypeItem {
    message DeepOcpxMixType {
      int32 mix_deep_type = 1;
      int32 mix_ocpx_type = 2;
    }
    repeated int64 bid_type = 1;
    repeated int64 ocpx_type = 2;
    repeated int64 deep_type = 3;
    repeated DeepOcpxMixType ocpx_deepconv_list = 4;
  }

  map<string, UniverseFilteredRoaringTypeItem> items = 1;
}

message AccountQuotaConfig {
  // 新账户 quota
  int32 new_queue_quota = 1;
  // 基础 quota
  int32 base_quota = 2;
  // 消耗参数，用于计算quota
  int64 cost_coefficient = 3;
  // 两次计算的时间间隔，单位 ms
  int32 time_interval = 4;
  // quota 限制生效的实验位置
  repeated int32 apply_exp = 5;
}

message InternalNewCreativeThreshold {
  message ThresholdItem {
    int64 cnt = 1;      // 数量, 为0表示不生效
    double ratio = 2;   // 比率, 为0表示不生效
  }
  message TagThresholdConfig {
    repeated ThresholdItem threshold = 1;
  }
  map<int32, TagThresholdConfig> config = 1;
}

message ExternalNewPhotoThres {
  message NewPhotoExp {
    int64 type = 1;
    int64 exp_id = 2;
    int64 start_day_gap = 3;
    int64 new_photo_day_gap = 4;
    double sim_score_thr = 5;
  }
  map<string, NewPhotoExp> exp_conf = 1;
}

message SearchIncCropConfig {
  message CropConfig {
    int32 inc_quota = 1;
    int32 msg_photo_quota = 2;
    double min_bid_ratio = 3;
  }
  map<int32, CropConfig> config = 1;
}

// 创意白名单配置
message CreativeWhiteListConfig {
  message Config {
    string user = 1;          // 申请加白人员
    string begin = 2;         // 加白生效开始时间
    string end = 3;           // 加白生效截止时间
    string level = 4;         // 加白层级 creative,unit,campaign,account
    repeated int64 ids = 5;   // 加白 id 列表
    string special_tag = 6;   // 特别队列标记
    int64 special_quota = 7;  // 特别队列的 quota
  }
  repeated Config configs = 1;
}

// 粉条新创意配置
message FanstopNewCreativeExpConfig {
  message Threshold {
      int64 time_window_minutes = 1;    // 冷启窗口大小，单位分钟
      int64 budget_thre = 2;            // 冷启订单最小预算，单位元
      int64 unit_conversion_thre = 3;   // 冷启 Unit 最小转化数
    }

  int32 exp_id = 1;
  map<int32, Threshold> ocpxs = 2;
}

message FanstopNewCreativeConfig {
  repeated FanstopNewCreativeExpConfig exps = 1;
}

message InEfficientCropTagsConfig {
  repeated int32 crop_tags = 1;
  int32 crop_only_jn_pc = 2;
  int32 skip_unit_listener = 3;
  int32 skip_charge_mode = 4;
  repeated int32 rand_crop_tags = 5;
  repeated int32 dup_crop_tags = 6;
  int32 dup_crop_type = 7;
}

message InEfficientCropConfig {
  map<int32, InEfficientCropTagsConfig> exp_to_tags = 1;
}

message DuplicateCropTagsConfig {
  repeated int32 crop_tags = 1;
}

message DuplicateCropConfig {
  map<int32, DuplicateCropTagsConfig> exp_to_tags = 1;
}

message RiskLabelTag {
  int32 tag = 1;
  repeated int64 risk_labels = 2;
}

message RiskLabelTagConfig {
  repeated RiskLabelTag tags = 1;
}
