#pragma once

#include <vector>
#include <unordered_map>
#include <string>
#include <utility>


#include "redis_proxy_client/redis_client.h"
#include "redis_proxy_client/redis_pipeline_client.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "base/strings/string_printf.h"
#include "redis_proxy_client/redis_response.h"
#include "teams/ad/ad_creative_server/util/redis_util/redis_cluster.h"

namespace ks {
namespace creative_server {

class RedisUtil {
 public:
  using RedisPipelineClient = ks::infra::RedisPipelineClient;
  using RedisClient = ks::infra::RedisClient;
  template <typename T>
  static void GetFromRedisByBatch(const std::vector<int64> &ids,
                           const std::string &key_format,
                           RedisPipelineClient *redis_pipeline_client,
                           int batch_size,
                           std::unordered_map<int64, T>* result) {
    if (ids.empty()) {
      return;
    }
    // 使用 pipeline 的方式从 redis 读取数据
    std::vector<std::string> keys;
    std::vector<std::string> redis_result;
    int parse_index = 0;
    for (int i = 0; i < ids.size(); ++i) {
      auto id = ids[i];
      keys.emplace_back(base::StringPrintf(key_format.data(), id));
      // 一次从 redis 中读取 batch size 的结果
      if ((keys.size() >= batch_size) || (i == ids.size() - 1)) {
        auto response = redis_pipeline_client->MGet(keys);
        // 读取成功
        if (response.Get(&redis_result) == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
          // 解析读取结果
          for (int j = 0; parse_index <= i; ++parse_index, ++j) {
            const auto &str = redis_result[j];
            if (str.empty()) {
              continue;
            }
            T pb_data;
            if (pb_data.ParseFromArray(str.data(), str.size())) {
              (*result)[ids[parse_index]] = pb_data;
            }
          }
        } else {
          // 失败
          parse_index = i + 1;
        }
        keys.clear();
        redis_result.clear();
      }
    }
  }

  template <typename T>
  static void GetFromRedisByBatch(const std::vector<std::string> &ids,
                                  const std::string &key_format,
                                  RedisPipelineClient *redis_pipeline_client,
                                  int batch_size,
                                  std::unordered_map<std::string, T>* result) {
    if (ids.empty()) {
      return;
    }
    // 使用 pipeline 的方式从 redis 读取数据
    std::vector<std::string> keys;
    std::vector<std::string> redis_result;
    int parse_index = 0;
    for (int i = 0; i < ids.size(); ++i) {
      auto id = ids[i];
      keys.emplace_back(base::StringPrintf(key_format.data(), id.c_str()));
      // 一次从 redis 中读取 batch size 的结果
      if ((keys.size() >= batch_size) || (i == ids.size() - 1)) {
        auto response = redis_pipeline_client->MGet(keys);
        // 读取成功
        if (response.Get(&redis_result) == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
          // 解析读取结果
          for (int j = 0; parse_index <= i; ++parse_index, ++j) {
            const auto &str = redis_result[j];
            if (str.empty()) {
              continue;
            }
            T pb_data;
            if (pb_data.ParseFromArray(str.data(), str.size())) {
              (*result)[ids[parse_index]] = pb_data;
            }
          }
        } else {
          // 失败
          parse_index = i + 1;
        }
        keys.clear();
        redis_result.clear();
      }
    }
  }

  static void GetFromRedisByBatch(const std::vector<int64> &ids,
                                  const std::string &key_format,
                                  RedisPipelineClient *redis_pipeline_client,
                                  int batch_size,
                                  std::vector<std::string>* result) {
    if (ids.empty()) {
      return;
    }
    // 使用 pipeline 的方式从 redis 读取数据
    std::vector<std::string> keys;
    std::vector<std::string> redis_result;
    int parse_index = 0;
    for (int i = 0; i < ids.size(); ++i) {
      auto id = ids[i];
      keys.emplace_back(base::StringPrintf(key_format.data(), id));
      // 一次从 redis 中读取 batch size 的结果
      if ((keys.size() >= batch_size) || (i == ids.size() - 1)) {
        auto response = redis_pipeline_client->MGet(keys);
        if (response.Get(&redis_result) == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
          // 存储结果
          result->insert(result->end(), redis_result.begin(), redis_result.end());
        }
        keys.clear();
        redis_result.clear();
      }
    }
  }

  static void GetFromRedisByBatch(const std::vector<int64> &ids,
                                  const std::string &key_format,
                                  RedisPipelineClient *redis_pipeline_client,
                                  int batch_size,
                                  std::unordered_map<int64, std::string>* result) {
    if (ids.empty()) {
      return;
    }
    // 使用 pipeline 的方式从 redis 读取数据
    std::vector<std::string> keys;
    std::vector<std::string> redis_result;
    int parse_index = 0;
    for (int i = 0; i < ids.size(); ++i) {
      auto id = ids[i];
      keys.emplace_back(base::StringPrintf(key_format.data(), id));
      // 一次从 redis 中读取 batch size 的结果
      if ((keys.size() >= batch_size) || (i == ids.size() - 1)) {
        auto response = redis_pipeline_client->MGet(keys);
        if (response.Get(&redis_result) == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
          // 存储结果
          for (const auto& res : redis_result) {
            if (!res.empty()) {
              result->emplace(ids[parse_index], res);
            }
            parse_index++;
          }
        } else {
          parse_index = i + 1;
        }
        keys.clear();
        redis_result.clear();
      }
    }
  }

  // 按 batch size 读取 list， 使用 vector 存储返回结果
  static void GetListRangeFromRedisByBatch(
                  const std::vector<int64>& ids,
                  const std::string &key_format,
                  RedisPipelineClient *redis_pipeline_client,
                  int batch_size,
                  std::vector<std::vector<std::string>>* result) {
    if (ids.empty()) {
      return;
    }
    std::vector<ks::infra::RedisResponse<std::vector<std::string>>> response;
    for (size_t i = 0; i < ids.size(); ++i) {
      auto id = ids[i];
      auto key = base::StringPrintf(key_format.data(), id);
      response.emplace_back(redis_pipeline_client->ListRange(key, 0, -1));
      if (response.size() >= batch_size || i == ids.size() - 1) {
        for (auto &r : response) {
          result->emplace_back();
          if (r.Get(&result->back()) != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
            result->back().clear();
          }
        }
        response.clear();
      }
    }
  }

  // 按 batch size 读取 list， 使用 map 存储返回结果
  static void GetListRangeFromRedisByBatch(
                  const std::vector<int64>& ids,
                  const std::string &key_format,
                  RedisPipelineClient *redis_pipeline_client,
                  int batch_size,
                  std::unordered_map<int64, std::vector<std::string>>* result) {
    if (ids.empty()) {
      return;
    }
    std::unordered_map<int64, ks::infra::RedisResponse<std::vector<std::string>>> response;
    for (size_t i = 0; i < ids.size(); ++i) {
      auto id = ids[i];
      auto key = base::StringPrintf(key_format.data(), id);
      if (result->find(id) == result->end()) {
        response.emplace(id, redis_pipeline_client->ListRange(key, 0, -1));
      }
      if (response.size() >= batch_size || i == ids.size() - 1) {
        for (auto &r : response) {
          result->emplace(r.first, std::vector<std::string>());
          if (r.second.Get(&(result->at(r.first))) != ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
            result->at(r.first).clear();
          }
        }
        response.clear();
      }
    }
  }

  template <typename T>
  static void GetFromRedis(const std::string &key,
                           RedisClient *redis_client,
                           T* result) {
    if (key.empty()) {
      return;
    }
    std::string str;
    auto response = redis_client->Get(key, &str);
    if (response == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
      if (str.empty()) {
        return;
      }
      result->ParseFromArray(str.data(), str.size());
    }
  }

 public:
  static const int kRedisBatchSize = 200;
};

}  // namespace creative_server
}  // namespace ks
