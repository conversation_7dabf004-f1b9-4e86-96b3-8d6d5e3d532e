#pragma once

#include <string>

#include "base/common/basic_types.h"
#include "redis_proxy_client/redis_client.h"
#include "redis_proxy_client/redis_pipeline_client.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "teams/ad/ad_creative_server/util/macro_util/redis_cluster.h"

namespace ks {
namespace creative_server {

static const int64 time_out_millis = 50;

class AdRedisCluster {
  // RedisPipelineClient
  REGISTER_REDIS_PIPELINE_CLUSTER_BY_NAME(adVisionFeature, time_out_millis);
  REGISTER_REDIS_PIPELINE_CLUSTER_BY_NAME(adMaterialTextBert, time_out_millis);
  REGISTER_REDIS_PIPELINE_CLUSTER_BY_NAME(adMaterialVideoMoco, time_out_millis);
  REGISTER_REDIS_PIPELINE_CLUSTER_BY_NAME(adCreativeExpressRetrieval, time_out_millis);
  REGISTER_REDIS_PIPELINE_CLUSTER_BY_NAME(adUserRetentionInfo, time_out_millis);
  REGISTER_REDIS_PIPELINE_CLUSTER_BY_NAME(adStypeOpt, time_out_millis);
  REGISTER_REDIS_PIPELINE_CLUSTER_BY_NAME(adCreativeDescBertEmb, time_out_millis);
  REGISTER_REDIS_PIPELINE_CLUSTER_BY_NAME(adNewCreativeLableTest, time_out_millis);

  // RedisClient
};

// redis key prifix
static const std::string kAdCoverVisionFeatureCtr = "acvfc";  // NOLINT
static const std::string kAdTextBertEmbedding = "atbe";  // NOLINT
static const std::string kAdVideoMocoEmbedding = "avme";  // NOLINT
static const std::string kAdDspProgramCreativeSelectModelCreativeImpression = "adpcsmci";  // NOLINT
static const std::string kAdDspProgramCreativeSelectModelCreativeCost = "adpcsmcc";  // NOLINT
static const std::string kAdDspProgramCreativeSelectModelCreativeLivingTime = "adpcsmclt";  // NOLINT
static const std::string kAdDspProgramCreativeUnitUriPrefix = "adpcuu"; // NOLINT
static const std::string kAdDspProgramCreativeCampaignUriPrefix = "adpccu"; // NOLINT
static const std::string kAdDspProgramCreativeAccountUriPrefix = "adpcau"; // NOLINT

}  // namespace creative_server
}  // namespace ks
