#include "teams/ad/ad_creative_server/util/helper/task_version_helper.h"

#include <fstream>

#include "base/time/timestamp.h"
#include "base/file/file_util.h"
#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "absl/strings/str_cat.h"
#include "teams/ad/ad_base/src/hash/hash.h"
#include "teams/ad/ad_creative_server/util/kconf/kconf.h"
#include "teams/ad/ad_creative_server/util/deploy_util/deploy_state.h"

namespace ks {
namespace creative_server {

TaskVersionHelper::TaskVersionHelper() {
  shard_id_ = DeployState::Instance().GetShardId();
  path_ = std::string(kVersionPath) + "_" + std::to_string(shard_id_);
  if (!base::file_util::DirectoryExists(kVersionDir)) {
    base::file_util::CreateDirectory(kVersionDir);
  }
  if (!base::file_util::PathExists(path_)) {
    last_version_timestamp_ = base::GetTimestamp() / kMicroPerSeconds;
  } else {
    base::file_util::ReadFileToString(path_, &version_);
    absl::Time time;
    std::string err;
    absl::ParseTime(kDataFormat, version_, absl::LocalTimeZone(), &time, &err);
    auto seconds = absl::ToInt64Seconds(absl::time_internal::ToUnixDuration(time));
    last_version_timestamp_ = seconds;
    LOG(INFO) << "Load version:" << version_ << ", timestamp:" << last_version_timestamp_;
  }
}

void TaskVersionHelper::Refresh(int64 timestamp) {
  last_version_timestamp_ = timestamp <= 0 ? base::GetTimestamp() / kMicroPerSeconds : timestamp;
  auto time = absl::FromUnixSeconds(last_version_timestamp_);
  version_ = absl::FormatTime(kDataFormat, time, absl::LocalTimeZone());
  hour_version_ = base::GetTimestamp() / (1000L * 1000L * 3600L);    // 小时级别
  LOG(INFO) << "Refresh version:" << version_ << ", hour_version:" << hour_version_;
}

void TaskVersionHelper::Flush() {
  if (last_version_timestamp_ <= 0 || version_.empty()) {
    return;
  }
  LOG(INFO) << "Flush version:" << version_;
  if (!base::file_util::PathExists(path_)) {
    LOG(INFO) << "create file:" << path_;
    std::ofstream file(path_, std::ios::out);
    file.close();
  }
  base::file_util::WriteFile(path_, version_.c_str(), version_.size());
  exec_times_++;
}

uint64_t TaskVersionHelper::GetHourVersionHashWithId(int64 id) {
  if (id == 0) id = base::GetTimestamp();
  std::string str = absl::StrCat(GetCurHourVersion(), "_", id);
  return ks::ad_base::FNVHash()(str);
}

}  // namespace creative_server
}  // namespace ks
