#pragma once

#include <vector>
#include <string>
#include <atomic>
#include <unordered_set>

#include "base/common/basic_types.h"
#include "base/common/logging.h"
#include "teams/ad/ad_creative_server/context_data/context_data.h"
#include "teams/ad/ad_creative_server/util/file_util/file_util.h"
#include "teams/ad/ad_creative_server/util/utility/int_to_word.h"

namespace ks {
namespace creative_server {

struct HdfsDownInfo {
  std::string src;
  std::string dst;
  std::string done;

  HdfsDownInfo() = default;
  HdfsDownInfo(const std::string& source, const std::string& destination, const std::string& done_file) :
        src(source), dst(destination), done(done_file) {}
  void describe(std::ostream& os) {
    os << "[src:" << src << ", dst:" << dst
       << ", done: " << done << "]";
  }
};

inline std::ostream& operator<<(std::ostream& os, HdfsDownInfo& info) {
  info.describe(os);
  return os;
}

class HdfsDownLoadHelper {
 public:
  static HdfsDownLoadHelper* GetInstance() {
    static HdfsDownLoadHelper instance;
    return &instance;
  }

  void DumpHdfsFile(HdfsDownInfo info, std::atomic_bool* ready = nullptr) {
    if (ready) {
      ready->store(false);
    }
    FileUtil::DumpLatestHdfsFile(info.src, info.done, info.dst);
    if (ready) {
      ready->store(true);
    }
    FileUtil::DeleteFile(info.dst, PCKconfUtil::latestSourceDataFileNum());
  }

  void ManageHdfsFile(HdfsDownInfo info) {
    FileUtil::DumpLatestHdfsFile(info.src, info.done, info.dst);
    FileUtil::DeleteFile(info.dst, PCKconfUtil::latestSourceDataFileNum());
  }

 private:
  HdfsDownLoadHelper() {}

  DISALLOW_COPY_AND_ASSIGN(HdfsDownLoadHelper);
};

}  // namespace creative_server
}  // namespace ks
