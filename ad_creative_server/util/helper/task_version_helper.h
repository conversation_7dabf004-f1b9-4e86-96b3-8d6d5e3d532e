#pragma once

#include <string>
#include <vector>
#include <mutex>
#include <unordered_map>
#include <unordered_set>

#include "base/common/basic_types.h"
#include "base/common/logging.h"

namespace ks {
namespace creative_server {

constexpr char kVersionPath[] = "../../data_push/ad_creative_server_version/task_version";
constexpr char kVersionDir[] = "../../data_push/ad_creative_server_version";
constexpr char kDataFormat[] = "%Y-%m-%d_%H:%M:%S";
constexpr int kMicroPerSeconds = 1000 * 1000;

class TaskVersionHelper {
 public:
  using RecordOptResult = std::unordered_map<int32, std::unordered_map<int32, std::vector<int64>>>;
  static TaskVersionHelper* GetInstance() {
    static TaskVersionHelper instance;
    return &instance;
  }

  const int64 GetVersion() const {
    return last_version_timestamp_;
  }

  std::string GetVersionString() const {
    return version_;
  }

  void Refresh(int64 timestamp = 0);

  void Flush();

  void SetWorking(bool working) { working_ = working; }
  const bool IsWorking() const { return working_; }

  int64 GetCurHourVersion() { return hour_version_; }
  uint64_t GetHourVersionHashWithId(int64 id);

 private:
  TaskVersionHelper();
  int64 last_version_timestamp_ {0};
  int64 hour_version_{0};
  std::string version_;
  int32 shard_id_ {0};
  std::string path_;
  bool working_{false};
  int32 exec_times_ {0};

  DISALLOW_COPY_AND_ASSIGN(TaskVersionHelper);
};

}  // namespace creative_server
}  // namespace ks
