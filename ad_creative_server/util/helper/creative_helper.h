#pragma once

#include <vector>
#include <string>
#include <atomic>
#include <unordered_set>
#include <unordered_map>

#include "falcon/counter.h"
#include "base/common/basic_types.h"
#include "base/common/logging.h"
#include "base/common/map_util-inl.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/table_extend_fields.pb.h"
#include "teams/ad/ad_creative_server/common/glags.h"
#include "teams/ad/ad_creative_server/biz/biz.h"
#include "teams/ad/ad_creative_server/util/kconf/kconf.h"
#include "teams/ad/ad_creative_server/context_data/context_data.h"
#include "teams/ad/ad_creative_server/util/bg_task/splash_rtb_gray/rtb_white_list.h"
#include "teams/ad/ad_creative_server/util/stats_collector/select_stats_collector.h"

namespace ks {
namespace creative_server {

using kuaishou::ad::tables::CreativeExtendScore;
using kuaishou::ad::AdEnum;

static inline bool IsZombieCreative(const OptCreativeData& creative) {
  return creative.zombie_status == 1;
}

static inline bool JudgeCreativeChanged(const OptCreativeData& creative) {
  if (PCKconfUtil::enableSendAllCreativeScore()) {
    return true;
  }
  // 如果分数变化
  if (creative.score != creative.score_new) {
    falcon::Inc("creative_changed.score_changed");
    return true;
  }
  // 60 分的表示命中特殊逻辑，如绿色通道，增量等，也需要发送结果
  if (creative.score_new == kFixIndexScore) {
    falcon::Inc("creative_changed.index_score");
    return true;
  }

  if (PCKconfUtil::sendIndexedCreative() && creative.score_new >= kFixIndexScore) {
    falcon::Inc("creative_changed.valid_score");
    return true;
  }

  // 在白名单内，下发结果消息
  auto check_msg_white_list = [&] () -> bool {
    if (PCKconfUtil::msgCreativeWhiteList()->count(creative.id)) { return true; }
    if (PCKconfUtil::msgUnitWhiteList()->count(creative.unit_id)) { return true; }
    if (PCKconfUtil::msgCampaignWhiteList()->count(creative.campaign_id)) { return true; }
    if (PCKconfUtil::msgAccountWhiteList()->count(creative.account_id)) { return true; }
    return false;
  };
  if (check_msg_white_list()) {
    falcon::Inc("creative_changed.in_mgs_white_list");
  }

  // 制作了 rtb 灰投的创意需要下发增量
  if (BizUtil::GetInstance()->IsExternalCirculationUnitBiz() &&
      RtbGrayWhiteList::GetInstance()->IsRtbGray(creative.id)) {
    falcon::Inc("creative_changed.rtb_gray");
    return true;
  }
  return false;
}

static inline void Trace(const OptCreativeData& creative) {
  AdCreativeServerTraceLog trace_log;
  trace_log.set_id(creative.id);
  // 环境信息
  if (BizUtil::GetInstance()->IsDefaultBiz()) {
    trace_log.set_deployment(ServiceDeployment::INTERNAL_DEPLOYMENT);
  } else if (BizUtil::GetInstance()->IsExternalCirculationUnitBiz()) {
    trace_log.set_deployment(ServiceDeployment::EXTERNAL_DEPLOYMENT);
  } else if (BizUtil::GetInstance()->IsSearchBiz()) {
    trace_log.set_deployment(ServiceDeployment::SEARCH_DEPLOYMENT);
  } else if (BizUtil::GetInstance()->IsUniverseDefaultBiz()) {
    trace_log.set_deployment(ServiceDeployment::UNIVERSE_DEPLOYMENT);
  }
  if (BizUtil::GetInstance()->IsProd()) {
    trace_log.set_stage(ServiceStage::PROD);
  } else if (BizUtil::GetInstance()->IsPreonline()) {
    trace_log.set_stage(ServiceStage::PREONLINE);
  } else if (BizUtil::GetInstance()->IsCandidate()) {
    trace_log.set_stage(ServiceStage::CANDIDATE);
  }
  if (ks::infra::KEnv::GetKWSInfo()->GetAZ() == "YZ") {
    trace_log.set_az(ServiceAz::YZ);
  } else if (ks::infra::KEnv::GetKWSInfo()->GetAZ() == "ZW") {
    trace_log.set_az(ServiceAz::ZW);
  }
  // 内外循环
  trace_log.set_circulation_type(creative.is_internal);
  // 僵尸状态
  trace_log.set_zombie_status(IsZombieCreative(creative));
  // 级联状态
  trace_log.set_cascade_status(creative.is_cascade_failed);
  // 新创意冷启状态
  int64_t default_key = PCKconfUtil::adNewCreativeDefaultKey();
  int64_t default_value = PCKconfUtil::adNewCreativeDefaultValue();
  trace_log.set_new_creative_status(creative.IsNewCreative(default_key, default_value));
  // 新建状态， 默认过审核两小时
  trace_log.set_new_create_status(creative.climb);
  // 素材重复状态
  bool is_dup_photo = (creative.dup_photo_id != 0 && creative.photo_id != creative.dup_photo_id);
  trace_log.set_photo_dup_status(is_dup_photo);
  // 优质原生状态
  trace_log.set_good_native_status(creative.photo->native_strict_status);
  // 广告库
  trace_log.set_ad_source(0);
  // 物料类型
  trace_log.set_item_type("");
  if (creative.live_creative_type == AdEnum::LIVE_STREAM_CREATIVE_TYPE ||
      creative.live_creative_type == AdEnum::DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
    trace_log.set_item_type("ITEM_LIVE");
  } else if (creative.live_creative_type == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE ||
            creative.live_creative_type == AdEnum::PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
    trace_log.set_item_type("ITEM_PHOTO_TO_LIVE");
  } else if (creative.live_creative_type == AdEnum::PIC_TO_LIVE_STREAM_CREATIVE_TYPE) {
    trace_log.set_item_type("ITEM_PICTURE_TO_LIVE");
  } else {
    trace_log.set_item_type("ITEM_PHOTO");
  }
  // 一级行业
  trace_log.set_first_industry(FindWithDefault(*PCKconfUtil::industryNameMap(), creative.account->industry_id, "未识别"));   // NOLINT
  // 优化目标
  trace_log.set_ocpx_type(kuaishou::ad::AdActionType_Name(creative.unit->ocpx_action_type));
  // 账户类型
  trace_log.set_account_type(kuaishou::ad::AdEnum::AdDspAccountType_Name(creative.account->account_type));
  // 计划类型
  trace_log.set_campaign_type(kuaishou::ad::AdEnum::CampaignType_Name(creative.campaign->type));
  // 出价类型
  trace_log.set_bid_type(kuaishou::ad::AdEnum::BidType_Name(creative.unit->bid_type));
  // 记录不分实验的结果
  trace_log.set_exp_id(0);
  // 优选状态
  trace_log.set_winning_status(creative.score_new >= kFixIndexScore);
  SelectStatsCollector::Instance().CollectTraceLog(trace_log);
  // 记录分实验结果
  for (int exp = 1; exp <= 7; exp++) {
    trace_log.set_exp_id(exp);
    // 实验未配置
    if (!PCKconfUtil::strategyConfig()->data().IsExpActive(exp)) continue;
    // 检查是否有 opt_info
    auto iter = creative.opt_info.find(exp);
    if (iter == creative.opt_info.end()) {
      // 未被选中
      trace_log.set_winning_status(creative.score_new == kFixIndexScore);
      SelectStatsCollector::Instance().CollectTraceLog(trace_log);
    } else {
      // 被选中
      int win_tag = 1 << (24 + exp - 1);
      trace_log.set_winning_status((creative.score_new & win_tag) == win_tag);
      trace_log.set_selector(iter->second.pool_type);
      trace_log.set_cropper(iter->second.crop_type);
      trace_log.set_block_status(iter->second.is_block);
      SelectStatsCollector::Instance().CollectTraceLog(trace_log);
    }
  }
}

}  // namespace creative_server
}  // namespace ks
