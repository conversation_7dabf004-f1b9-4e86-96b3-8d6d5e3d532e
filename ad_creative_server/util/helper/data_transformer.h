#pragma once

#include <vector>
#include <string>
#include <atomic>
#include <unordered_set>
#include <unordered_map>

#include "falcon/counter.h"
#include "base/common/basic_types.h"
#include "base/common/logging.h"
#include "base/common/map_util-inl.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/table_extend_fields.pb.h"
#include "teams/ad/ad_creative_server/proto/creative_score.pb.h"

namespace ks {
namespace creative_server {

using kuaishou::ad::tables::CreativeExtendScore;
using kuaishou::ad::ad_creative_server::CreativeExtendData;

static inline bool Transform(const CreativeExtendData& in, CreativeExtendScore* out) {
  if (out == nullptr) return false;
  // 内外循环类型
  out->set_circulation_type(in.circulation_type());
  // 新创意标签
  auto* map = out->mutable_new_creative_tag();
  for (auto& tag : in.new_creative_tags()) {
    (*map)[tag.index()] = tag.value();
  }
  return true;
}

static inline bool Transform(const CreativeExtendScore& in, int score, CreativeExtendData* out) {
  if (out == nullptr) return false;
  // 分数
  out->set_creative_score(score);
  // 内外循环类型
  out->set_circulation_type(in.circulation_type());
  return true;
}

}  // namespace creative_server
}  // namespace ks
