#pragma once

#include <vector>
#include <string>
#include <atomic>
#include <unordered_set>

#include "absl/strings/str_cat.h"
#include "absl/strings/str_replace.h"
#include "base/common/basic_types.h"
#include "base/common/logging.h"
#include "teams/ad/ad_creative_server/biz/biz.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_base.pb.h"
#include "teams/ad/ad_creative_server/context_data/creative_data.h"
#include "teams/ad/ad_creative_server/context_data/context_data.h"

namespace ks {
namespace creative_server {

using kuaishou::ad::algorithm::AdCreativeServerCreativeOptInfo;

static void FillAccountExtendInfo(const OptCreativeData &creative, AdCreativeServerCreativeOptInfo* info,
                                  ContextData* context = nullptr) {
  if (creative.account_id == 0) { return; }
  auto* account_ext_info = info->mutable_account_extend_info();
  account_ext_info->set_id(creative.account_id);
  if (creative.account) {
    account_ext_info->set_photo_wake_start_time(creative.account->photo_wake_start_time);
    account_ext_info->set_photo_wake_end_time(creative.account->photo_wake_end_time);
    account_ext_info->set_photo_wake_status(creative.account->photo_wake_status);
  }
}

static void FillCampaignExtendInfo(const OptCreativeData &creative, AdCreativeServerCreativeOptInfo* info,
                                   ContextData* context = nullptr) {
  if (creative.campaign_id == 0) { return; }
  auto* campaign_ext_info = info->mutable_campaign_extend_info();
  campaign_ext_info->set_id(creative.campaign_id);
}

static void FillUnitExtendInfo(const OptCreativeData &creative, AdCreativeServerCreativeOptInfo* info,
                               ContextData* context = nullptr) {
  if (creative.unit_id == 0) { return; }
  auto* unit_ext_info = info->mutable_unit_extend_info();
  unit_ext_info->set_id(creative.unit_id);
}

static void FillPhotoExtendInfo(const OptCreativeData &creative, AdCreativeServerCreativeOptInfo* info,
                               ContextData* context = nullptr) {
  if (creative.photo_id == 0) { return; }
  auto* photo_ext_info = info->mutable_photo_extend_info();
  photo_ext_info->set_is_dup_material(creative.photo->photo_dup_status);
  if (creative.dup_photo_id == 0 || creative.photo_id == creative.dup_photo_id) {
    photo_ext_info->set_is_original_material(true);
  } else {
    photo_ext_info->set_is_original_material(false);
  }
}

static void FillOptExtendInfo(const OptCreativeData &creative, AdCreativeServerCreativeOptInfo* info,
                              ContextData* context = nullptr) {
  FillAccountExtendInfo(creative, info, context);
  FillCampaignExtendInfo(creative, info, context);
  FillUnitExtendInfo(creative, info, context);
  FillPhotoExtendInfo(creative, info, context);
}

}  // namespace creative_server
}  // namespace ks
