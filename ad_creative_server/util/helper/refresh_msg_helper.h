#pragma once

#include <vector>
#include <string>
#include <atomic>
#include <unordered_set>
#include <memory>

#include "base/common/basic_types.h"
#include "base/common/logging.h"
#include "base/time/timestamp.h"
#include "folly/TokenBucket.h"
#include "teams/ad/ad_creative_server/util/kafka_util/kafka_maneger.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_inc.pb.h"
#include "teams/ad/ad_creative_server/context_data/creative_data.h"
#include "teams/ad/ad_creative_server/util/perf_log_util/perf_log_util.h"
#include "teams/ad/ad_creative_server/util/deploy_util/deploy_state.h"
#include "teams/ad/ad_creative_server/context_data/common_type.h"
#include "teams/ad/ad_creative_server/util/bg_task/forward_index/forward_index.h"

namespace ks {
namespace creative_server {

using kuaishou::ad::AdEnum_CreativeSourceType;
using kuaishou::ad::AdInstance;

class RefreshMsgHelper {
 public:
  static RefreshMsgHelper* GetInstance() {
    static RefreshMsgHelper instance;
    return &instance;
  }
  void SendScoreDB(const OptCreativeData& creative, int64_t version, bool limit_qps = true);

  // 发送没有经过优选的创意 opt_info 信息，无后验数据，不填充
  void SendOptInfoCreativeData(const OptCreativeData& creative, const OptMsgType& msg_type);

 private:
  RefreshMsgHelper();
  ~RefreshMsgHelper();

 private:
  KafkaProducer opt_info_kafka_;
  KafkaProducer score_db_notify_;
  KafkaProducer score_message_producer_;
  folly::TokenBucket token_bucket_;

  DISALLOW_COPY_AND_ASSIGN(RefreshMsgHelper);
};

}  // namespace creative_server
}  // namespace ks
