#pragma once

#include <mutex>
#include <vector>
#include <string>

#include "base/common/closure.h"
#include "base/common/logging.h"
#include "base/common/basic_types.h"
#include "base/thread/thread_pool.h"
#include "absl/container/flat_hash_map.h"
#include "absl/container/flat_hash_set.h"
#include "folly/TokenBucket.h"
#include "libcuckoo/cuckoohash_map.hh"
#include "redis_proxy_client/redis_proxy_client.h"
#include "teams/ad/ad_base/src/hash/hash.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_base/src/common/condition_time_wait.h"
#include "teams/ad/ad_creative_server/common/index_types.h"
#include "teams/ad/ad_creative_server/util/kafka_util/kafka_maneger.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_creative_server_stats.pb.h"

namespace ks {
namespace creative_server {

using kuaishou::ad::datalink::AdCreativeScoreResultStats;
using kuaishou::ad::datalink::AdCreativeServerTraceLog;
using kuaishou::ad::datalink::AdCreativeServerTraceLogList;
using kuaishou::ad::datalink::ServiceDeployment;
using kuaishou::ad::datalink::ServiceAz;
using kuaishou::ad::datalink::ServiceStage;

class SelectStatsCollector {
 public:
  static SelectStatsCollector& Instance() {
    static SelectStatsCollector instance;
    return instance;
  }

  void Start();
  void Stop();
  void FlushAndClearSelectStats();
  void CollectorSelectStats(const AdCreativeScoreResultStats& stat);
  void FlushTraceLog();
  void CollectTraceLog(const AdCreativeServerTraceLog& trace);

 private:
  void PerfSelectStats();
  void PerfSelectStatsNew();
  void GetAllSelectStats();
  void PerfTraceStats();
  void GetAllTraceStats();

 private:
  SelectStatsCollector();
  ~SelectStatsCollector() = default;
  int shard_id_{-1};
  infra::RedisClient* redis_client_{nullptr};
  ad_base::ConditionTimeWait cw_;
  thread::ThreadPool pool_;
  std::string select_stats_key_prefix_;
  std::string trace_log_key_prefix_;
  mutable std::mutex select_mtx_;
  cuckoohash_map<std::string, int, ks::ad_base::FNVHash> select_stats_;
  cuckoohash_map<std::string, int, ks::ad_base::FNVHash> select_stats_total_;
  mutable std::mutex trace_mtx_;
  cuckoohash_map<std::string, int, ks::ad_base::FNVHash> trace_log_;
  cuckoohash_map<std::string, int, ks::ad_base::FNVHash> trace_log_total_;
  folly::TokenBucket trace_log_token_bucket_;
  KafkaProducer trace_log_total_producer_;
};

}  // namespace creative_server
}  // namespace ks
