#pragma once

#include <array>
#include <vector>
#include <unordered_map>
#include <string>
#include <atomic>
#include <thread>
#include <mutex>

#include "libcuckoo/cuckoohash_map.hh"
#include "teams/ad/ad_base/src/hash/hash.h"
#include "teams/ad/ad_creative_server/context_data/enums.h"
#include "teams/ad/ad_creative_server/util/perf_log_util/perf_log_util.h"

namespace ks {
namespace creative_server {

static constexpr size_t kExpCnt = 7;
using ExpCreativeCntType = std::array<std::atomic<uint64_t>, kExpCnt + 1>;

class CreativeStatusView {
 public:
  static CreativeStatusView* GetInstance() {
    static CreativeStatusView creative_status_view;
    return &creative_status_view;
  }

  void Start(int shard);
  void Stop();

  bool AddCustomCreative(int64_t creative_id, int64_t score);
  bool AddProgCreative(int64_t creative_id, int64_t score);
  bool AddCreative(int64_t creative_id, int64_t score, const CreativeType& type);

  uint64_t GetProgCount() const {
    return prog_count.load(std::memory_order_relaxed);
  }
  uint64_t GetCustomCount() const {
    return custom_count.load(std::memory_order_relaxed);
  }
  uint64_t GetHistoryProgCount() const {
    return prog_history_count.load(std::memory_order_relaxed);
  }
  uint64_t GetHistoryCustomCount() const {
    return custom_history_count.load(std::memory_order_relaxed);
  }
  void SetExpCreativeCnt(const ExpCreativeCntType& prog, const ExpCreativeCntType& custom);

 private:
  void DailyClear();
  bool Save();
  bool Restore();

  // 创意最新优选状态, 全量 + 增量, 只保存分数 > 49 的 creative_id
  // 每次全量优选前不需要 clear
  cuckoohash_map<int64_t, int64_t, ks::ad_base::FNVHash> prog_creative_status_view;
  cuckoohash_map<int64_t, int64_t, ks::ad_base::FNVHash> custom_creative_status_view;
  std::atomic<uint64_t> prog_count{0};  // 当前的可投程序化创意量
  std::atomic<uint64_t> custom_count{0};  // 当前的可投自定义创意量

  // 曾经被选出来的
  cuckoohash_map<int64_t, int64_t, ks::ad_base::FNVHash> prog_creative_history_view;
  cuckoohash_map<int64_t, int64_t, ks::ad_base::FNVHash> custom_creative_history_view;
  std::atomic<uint64_t> prog_history_count{0};  // 累计的可投程序化创意量
  std::atomic<uint64_t> custom_history_count{0};  // 累计的可投自定义创意量

  std::mutex exp_creative_cnt_mtx_;
  ExpCreativeCntType exp_prog_creative_cnt_;
  ExpCreativeCntType exp_custom_creative_cnt_;
  std::thread* report_thread_{nullptr};
  Dot dot_;
  std::atomic_bool stop_{false};
  int shard_{0};
  std::string version_;
};

}  // namespace creative_server
}  // namespace ks

