#include "teams/ad/ad_creative_server/util/unit_bid_lift_log.h"
#include <fstream>
#include <string>
#include "base/time/timestamp.h"
#include "base/common/logging.h"
#include "base/file/file_util.h"

namespace ks {
namespace creative_server {

static const char kBidLiftLogPath[] = "../../data_push/bid_lift_log/unit_bid_lift_log.bin";
static int64_t kExpireHour = 24;  // 仅保存最近几个小时出价有变更的数据

bool UnitBidLiftLog::UpdateBidLiftTimestamp(int64_t unit_id, int64_t tm_ms) {
  return data_.upsert(
      unit_id,
      [tm_ms](int64_t& origin_tm) {
        if (tm_ms > origin_tm) {
          origin_tm = tm_ms;
        }
      },
      tm_ms);
}

bool UnitBidLiftLog::GetBidLiftTimestamp(int64_t unit_id, int64_t* tm_ms) {
  return data_.find(unit_id, *tm_ms);
}

bool UnitBidLiftLog::Recover() {
  std::ifstream ifs(kBidLiftLogPath, std::ios::in);
  if (!ifs.good()) {
    LOG(WARNING) << "Open file failed: " << kBidLiftLogPath;
    ifs.close();
    return false;
  }
  auto lock_table = data_.lock_table();
  ifs >> lock_table;
  lock_table.unlock();
  ifs.close();
  LOG(INFO) << "Recover succeed, unit cnt: " << data_.size();
  return true;
}

bool UnitBidLiftLog::Save() {
  base::FilePath fp(kBidLiftLogPath);
  base::FilePath dir = fp.DirName();
  if (!base::file_util::DirectoryExists(dir)) {
    base::file_util::CreateDirectory(dir);
  }
  std::ofstream ofs(kBidLiftLogPath, std::ios::out | std::ios::trunc);
  if (!ofs.good()) {
    LOG(ERROR) << "Open file failed: " << kBidLiftLogPath;
    ofs.close();
    return false;
  }
  auto lk_table = data_.lock_table();
  // 保存前清理过期数据
  int64_t min = base::GetTimestamp() / 1000 - kExpireHour * 3600 * 1000;
  size_t expired_cnt = 0;
  for (auto iter = lk_table.begin(); iter != lk_table.end();) {
    LOG(INFO) << "[BidLiftLog] unit_id: " << iter->first << ", last_bid_lift_tm: " << iter->second;
    if (iter->second < min) {
      iter = lk_table.erase(iter);
      expired_cnt++;
    } else {
      iter++;
    }
  }
  ofs << lk_table;
  lk_table.unlock();
  ofs.close();
  LOG(INFO) << "Save UnitBidLiftLog succeed, unit cnt: " << data_.size() << ", expire timestamp: " << min
            << ", expired cnt: " << expired_cnt;
  return true;
}

}  // namespace creative_server
}  // namespace ks
