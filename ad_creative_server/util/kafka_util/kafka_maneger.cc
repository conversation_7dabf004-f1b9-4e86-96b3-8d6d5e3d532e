#include "teams/ad/ad_creative_server/util/kafka_util/kafka_maneger.h"

#include <thread>
#include <memory>

#include "base/time/timestamp.h"
#include "absl/strings/numbers.h"
#include "teams/ad/ad_base/src/common/common.h"
#include "teams/ad/ad_creative_server/util/kconf/kconf.h"
#include "teams/ad/ad_creative_server/context_data/common_type.h"
#include "teams/ad/ad_creative_server/util/perf_log_util/perf_log_util.h"

namespace ks {
namespace creative_server {

KafkaProducer::KafkaProducer(const std::string& topic) : topic_(topic) {
  producer_.SetPartitionCb(&partition_cb_);
  producer_.Init(topic, "");
  ready_ = true;
}

void KafkaProducer::Initialize(const std::string& topic) {
  if (topic.empty()) {
    LOG(WARNING) << "KafkaProducer initialization failed: empty topic!";
  }
  topic_ = topic;
  producer_.Init(topic, "");
  producer_.SetPartitionCb(&partition_cb_);
  ready_ = true;
}

void KafkaProducer::InitializeAfterCb(const std::string& topic) {
  if (topic.empty()) {
    LOG(WARNING) << "KafkaProducer initialization failed: empty topic!";
  }
  topic_ = topic;
  producer_.SetPartitionCb(&partition_cb_);
  producer_.Init(topic, "");
  ready_ = true;
  partitioned_ = true;
}

int KafkaProducer::Produce(const std::string& message, int64 hash) {
  if (message.empty()) {
    return ks::ad_base::AdKafkaStatus::NO_MESSAGE;
  }
  int ret = 0;
  if (partitioned_) {
    ret = producer_.ProduceWithHash(message, hash);
  } else {
    ret = producer_.Produce(message);
  }
  if (ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
    LOG_EVERY_N(INFO, 1000) << "send to kafka failed"
                            << ",topic=" << topic_
                            << ",ret=" << ret;
  }
  msg_cnt_++;
  return ret;
}

// 发送结果到 kafka， 有重试次数限制
void KafkaProducer::SendMessageToKafka(const std::string& message,
                                       KafkaProducer* kafka,
                                       int64 hash) {
  if (kafka == nullptr) {
    return;
  }
  int send_times = 0;
  while (kafka->Produce(message, hash) != ks::ad_base::AdKafkaStatus::SUCCESS) {
    // 发送失败后等待 kKafkaRetryTimeInterval ms 重新发送
    std::this_thread::sleep_for(std::chrono::milliseconds(kKafkaRetryTimeInterval));
    send_times++;
    // LOG(WARNING) << "send message to kafka failed, retry times: " << send_times;
    // 超过重试次数跳过
    if (send_times > kKafkaRetries) {
      LOG(WARNING) << "send kafka message failed!";
      break;
    }
  }
  auto msg_cnt = kafka->GetMsgCnt();
  if ((msg_cnt & kWorkTimesForSleep) == 0) {
    int64 interval = PCKconfUtil::creativeServerKafkaMillisTimeInterval();
    std::this_thread::sleep_for(std::chrono::milliseconds(interval));
  }
}

// 发送结果到 kafka， 有重试次数限制
void KafkaProducer::SendMessageToKafkaWithoutSleep(const std::string& message,
                                                   KafkaProducer* kafka,
                                                   int64 hash) {
  if (kafka == nullptr) {
    return;
  }
  int send_times = 0;
  while (kafka->Produce(message, hash) != ks::ad_base::AdKafkaStatus::SUCCESS) {
    // 发送失败后等待 kKafkaRetryTimeInterval ms 重新发送
    std::this_thread::sleep_for(std::chrono::milliseconds(kKafkaRetryTimeInterval));
    send_times++;
    // LOG(WARNING) << "send message to kafka failed, retry times: " << send_times << ":hash-" << hash;
    // 超过重试次数跳过
    if (send_times > kKafkaRetries) {
      LOG(WARNING) << "send kafka message failed!";
      break;
    }
  }
}

}  // namespace creative_server
}  // namespace ks
