#pragma once

#include <string>

#include "serving_base/jansson/json.h"
#include "absl/strings/substitute.h"
#include "teams/ad/ad_creative_server/util/kconf/kconf.h"
#include "teams/ad/ad_creative_server/util/perf_log_util/perf_log_util.h"

namespace ks {
namespace creative_server {

typedef json_error_t JsonParseErrMsg;
typedef base::Json Json;

// json 解析使用:
// JsonParseErrMsg err;
// Json json(StringToJson(str, &err));
// if (err.line != -1)
//     json 该置配置格式错误
// else
//     [bool] auto value = json.GetBoolean(key, default);
//
//     解析 double 和 int64 使用下面包装的方法
//     [double]
//     auto code = GetDoubleFromJson(&json, key, default, &value)
//       or code = GetDoubleFromJsonNoUseDefault(&json, key, &value);
//     [int64]
//     auto code = GetIntFromJson(&json, key, default, &value)
//       or code = GetIntFromJsonNoUseDefault(&json, key, &value);
//
//     PerfJsonValueStatus(code, key); // 记录解析情况
//
//     if (code != JsonValueCode::OK)
//        value 配置出错
//     else
//        value 解析完成
//
//
//   normal function 用来获取一般配置情况，当不存在是会取传入的默认值
//   no use defaule function 用来获取那些要求必须配置的选项，未配置的会报错
json_t* StringToJson(const std::string& str, JsonParseErrMsg* err) {
  static const char Empty[] = "empty str";
  if (str.empty()) {
    memcpy(err->text, Empty, 10);
    err->line = 1;
    return nullptr;
  }
  return json_loads(str.data(), err);
}

enum class JsonValueCode {
  OK = 0,                         // 正常
  JSON_NULL = 1,                  // 不正确的 json
  EMPTY_KEY = 2,                  // 空 key
  OVERFLOW_UPPER_THRESHOLD = 3,   // 超过阈值上限
  OVERFLOW_LOWER_THRESHOLD = 4,   // 超过阈值下限
  NO_SUCH_KEY_VALUE = 5,          // 不存在此 kv
};

// GetDoubleFromJson 使用默认值
// GetDoubleFromJsonNoUseDefault 不能使用默认值
// 解析过程中会对配置值的上下阈值进行检测
// 阈值设置在 kconf jsonValueThreshold 中
// 只检测配置的值, 未配置的跳过检测

JsonValueCode GetDoubleFromJson(Json* json, const std::string& key, double default_value, double* value) {
  if (json == nullptr) { return JsonValueCode::JSON_NULL; }
  if (key.empty()) { return JsonValueCode::EMPTY_KEY; }
  *value = json->GetNumber(key, default_value);
  auto value_threshold = PCKconfUtil::jsonValueThreshold();
  auto max_value_iter = value_threshold->find(key + "_max");
  if (max_value_iter != value_threshold->end() && *value > (double)max_value_iter->second) {
    return JsonValueCode::OVERFLOW_UPPER_THRESHOLD;
  }
  auto min_value_iter = value_threshold->find(key + "_min");
  if (min_value_iter != value_threshold->end() && *value < (double)min_value_iter->second) {
    return JsonValueCode::OVERFLOW_LOWER_THRESHOLD;
  }
  return JsonValueCode::OK;
}

JsonValueCode GetDoubleFromJsonNoUseDefault(Json* json, const std::string& key, double* value) {
  if (json == nullptr) { return JsonValueCode::JSON_NULL; }
  if (key.empty()) { return JsonValueCode::EMPTY_KEY; }
  auto* kv_json = json->Get(key);
  if (kv_json == nullptr) {
    return JsonValueCode::NO_SUCH_KEY_VALUE;
  }
  return GetDoubleFromJson(json, key, 0, value);
}

JsonValueCode GetIntFromJson(Json* json, const std::string& key, int64 default_value, int64* value) {
  if (json == nullptr) { return JsonValueCode::JSON_NULL; }
  if (key.empty()) { return JsonValueCode::EMPTY_KEY; }
  *value = json->GetInt(key, default_value);
  auto value_threshold = PCKconfUtil::jsonValueThreshold();
  auto max_value_iter = value_threshold->find(key + "_max");
  if (max_value_iter != value_threshold->end() && *value > max_value_iter->second) {
    return JsonValueCode::OVERFLOW_UPPER_THRESHOLD;
  }
  auto min_value_iter = value_threshold->find(key + "_min");
  if (min_value_iter != value_threshold->end() && *value < min_value_iter->second) {
    return JsonValueCode::OVERFLOW_LOWER_THRESHOLD;
  }
  return JsonValueCode::OK;
}

JsonValueCode GetIntFromJsonNoUseDefault(Json* json, const std::string& key, int64* value) {
  if (json == nullptr) { return JsonValueCode::JSON_NULL; }
  if (key.empty()) { return JsonValueCode::EMPTY_KEY; }
  auto* kv_json = json->Get(key);
  if (kv_json == nullptr) {
    return JsonValueCode::NO_SUCH_KEY_VALUE;
  }
  return GetIntFromJson(json, key, 0, value);
}

void PerfJsonValueStatus(const JsonValueCode& code, const std::string& key) {
  std::string extra1 = key;
  std::string extra2 = "";
  switch (code) {
  case JsonValueCode::OK:
    extra2 = "ok";
    break;
  case JsonValueCode::JSON_NULL:
    extra2 = "json_null_ptr";
    break;
  case JsonValueCode::EMPTY_KEY:
    extra2 = "empty_key";
    break;
  case JsonValueCode::OVERFLOW_UPPER_THRESHOLD:
    extra2 = "overflow_upper_threshold";
    break;
  case JsonValueCode::OVERFLOW_LOWER_THRESHOLD:
    extra2 = "overflow_lower_threshold";
    break;
  case JsonValueCode::NO_SUCH_KEY_VALUE:
    extra2 = "no_such_kv";
    break;
  default:
    break;
  }
  if (extra2.empty()) { return; }
  Dotter::GetInstance()->Count(1, "json_value_status", extra1, extra2);
}

}  // namespace creative_server
}  // namespace ks

